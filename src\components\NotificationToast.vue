<template>
  <Transition
    enter-active-class="transform ease-out duration-300 transition"
    enter-from-class="translate-y-2 opacity-0 sm:translate-y-0 sm:translate-x-2"
    enter-to-class="translate-y-0 opacity-100 sm:translate-x-0"
    leave-active-class="transition ease-in duration-100"
    leave-from-class="opacity-100"
    leave-to-class="opacity-0"
  >
    <div
      v-if="visible"
      class="max-w-sm w-full bg-white dark:bg-gray-800 shadow-lg rounded-lg pointer-events-auto ring-1 ring-black ring-opacity-5 overflow-hidden"
    >
      <div class="p-4">
        <div class="flex items-start">
          <div class="flex-shrink-0">
            <div 
              class="w-8 h-8 rounded-full flex items-center justify-center"
              :class="iconClasses"
            >
              <i :class="iconName" class="text-sm"></i>
            </div>
          </div>
          <div class="ml-3 w-0 flex-1 pt-0.5">
            <p class="text-sm font-medium text-gray-900 dark:text-white">
              {{ notification.title }}
            </p>
            <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">
              {{ notification.message }}
            </p>
            <div v-if="notification.actions" class="mt-3 flex space-x-2">
              <button
                v-for="action in notification.actions"
                :key="action.label"
                @click="handleAction(action)"
                class="text-sm font-medium rounded-md px-3 py-1.5 transition-colors"
                :class="action.primary 
                  ? 'bg-primary text-white hover:bg-primary-dark' 
                  : 'bg-gray-100 text-gray-700 hover:bg-gray-200 dark:bg-gray-700 dark:text-gray-300 dark:hover:bg-gray-600'"
              >
                {{ action.label }}
              </button>
            </div>
          </div>
          <div class="ml-4 flex-shrink-0 flex">
            <button
              @click="$emit('close')"
              class="bg-white dark:bg-gray-800 rounded-md inline-flex text-gray-400 hover:text-gray-500 dark:hover:text-gray-300 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary"
            >
              <span class="sr-only">Close</span>
              <i class="fas fa-times text-sm"></i>
            </button>
          </div>
        </div>
      </div>
      
      <!-- Progress bar for auto-dismiss -->
      <div 
        v-if="autoDismiss && timeRemaining > 0"
        class="h-1 bg-gray-200 dark:bg-gray-700"
      >
        <div 
          class="h-full bg-primary transition-all duration-100 ease-linear"
          :style="{ width: `${(timeRemaining / autoDismissTime) * 100}%` }"
        ></div>
      </div>
    </div>
  </Transition>
</template>

<script setup>
import { computed, onMounted, onUnmounted, ref } from 'vue'

const props = defineProps({
  notification: {
    type: Object,
    required: true
  },
  visible: {
    type: Boolean,
    default: true
  },
  autoDismiss: {
    type: Boolean,
    default: true
  },
  autoDismissTime: {
    type: Number,
    default: 5000
  }
})

const emit = defineEmits(['close', 'action'])

const timeRemaining = ref(props.autoDismissTime)
let dismissTimer = null
let progressTimer = null

const iconClasses = computed(() => {
  const baseClasses = 'text-white'
  switch (props.notification.type) {
    case 'success':
      return `${baseClasses} bg-green-500`
    case 'error':
      return `${baseClasses} bg-red-500`
    case 'warning':
      return `${baseClasses} bg-yellow-500`
    case 'info':
      return `${baseClasses} bg-blue-500`
    case 'link_shared':
      return `${baseClasses} bg-blue-500`
    case 'user_joined':
      return `${baseClasses} bg-green-500`
    default:
      return `${baseClasses} bg-gray-500`
  }
})

const iconName = computed(() => {
  switch (props.notification.type) {
    case 'success':
      return 'fas fa-check'
    case 'error':
      return 'fas fa-exclamation-triangle'
    case 'warning':
      return 'fas fa-exclamation'
    case 'info':
      return 'fas fa-info'
    case 'link_shared':
      return 'fas fa-share-alt'
    case 'user_joined':
      return 'fas fa-user-plus'
    default:
      return 'fas fa-bell'
  }
})

const handleAction = (action) => {
  emit('action', action)
  if (action.closeOnClick !== false) {
    emit('close')
  }
}

const startDismissTimer = () => {
  if (!props.autoDismiss) return
  
  dismissTimer = setTimeout(() => {
    emit('close')
  }, props.autoDismissTime)
  
  // Update progress bar
  const updateInterval = 100
  progressTimer = setInterval(() => {
    timeRemaining.value -= updateInterval
    if (timeRemaining.value <= 0) {
      clearInterval(progressTimer)
    }
  }, updateInterval)
}

const stopDismissTimer = () => {
  if (dismissTimer) {
    clearTimeout(dismissTimer)
    dismissTimer = null
  }
  if (progressTimer) {
    clearInterval(progressTimer)
    progressTimer = null
  }
}

onMounted(() => {
  if (props.visible) {
    startDismissTimer()
  }
})

onUnmounted(() => {
  stopDismissTimer()
})

// Pause timer on hover
const pauseTimer = () => {
  stopDismissTimer()
}

const resumeTimer = () => {
  if (timeRemaining.value > 0) {
    startDismissTimer()
  }
}
</script>

<style scoped>
/* Additional styles if needed */
</style>
