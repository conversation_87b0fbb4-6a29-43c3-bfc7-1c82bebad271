!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?t(require("@firebase/app-compat"),require("@firebase/app")):"function"==typeof define&&define.amd?define(["@firebase/app-compat","@firebase/app"],t):t((e="undefined"!=typeof globalThis?globalThis:e||self).firebase,e.firebase.INTERNAL.modularAPIs)}(this,function(Ci,Oi){"use strict";try{!(function(){function e(e){return e&&"object"==typeof e&&"default"in e?e:{default:e}}var i=e(Ci);const t={byteToCharMap_:null,charToByteMap_:null,byteToCharMapWebSafe_:null,charToByteMapWebSafe_:null,ENCODED_VALS_BASE:"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789",get ENCODED_VALS(){return this.ENCODED_VALS_BASE+"+/="},get ENCODED_VALS_WEBSAFE(){return this.ENCODED_VALS_BASE+"-_."},HAS_NATIVE_SUPPORT:"function"==typeof atob,encodeByteArray(r,e){if(!Array.isArray(r))throw Error("encodeByteArray takes an array as a parameter");this.init_();var n=e?this.byteToCharMapWebSafe_:this.byteToCharMap_;const i=[];for(let u=0;u<r.length;u+=3){var s=r[u],a=u+1<r.length,o=a?r[u+1]:0,c=u+2<r.length,l=c?r[u+2]:0;let e=(15&o)<<2|l>>6,t=63&l;c||(t=64,a||(e=64)),i.push(n[s>>2],n[(3&s)<<4|o>>4],n[e],n[t])}return i.join("")},encodeString(e,t){return this.HAS_NATIVE_SUPPORT&&!t?btoa(e):this.encodeByteArray(function(t){const r=[];let n=0;for(let i=0;i<t.length;i++){let e=t.charCodeAt(i);e<128?r[n++]=e:(e<2048?r[n++]=e>>6|192:(55296==(64512&e)&&i+1<t.length&&56320==(64512&t.charCodeAt(i+1))?(e=65536+((1023&e)<<10)+(1023&t.charCodeAt(++i)),r[n++]=e>>18|240,r[n++]=e>>12&63|128):r[n++]=e>>12|224,r[n++]=e>>6&63|128),r[n++]=63&e|128)}return r}(e),t)},decodeString(e,t){return this.HAS_NATIVE_SUPPORT&&!t?atob(e):function(e){const t=[];let r=0,n=0;for(;r<e.length;){var i,s,a=e[r++];a<128?t[n++]=String.fromCharCode(a):191<a&&a<224?(i=e[r++],t[n++]=String.fromCharCode((31&a)<<6|63&i)):239<a&&a<365?(s=((7&a)<<18|(63&e[r++])<<12|(63&e[r++])<<6|63&e[r++])-65536,t[n++]=String.fromCharCode(55296+(s>>10)),t[n++]=String.fromCharCode(56320+(1023&s))):(i=e[r++],s=e[r++],t[n++]=String.fromCharCode((15&a)<<12|(63&i)<<6|63&s))}return t.join("")}(this.decodeStringToByteArray(e,t))},decodeStringToByteArray(e,t){this.init_();var r=t?this.charToByteMapWebSafe_:this.charToByteMap_;const n=[];for(let c=0;c<e.length;){var i=r[e.charAt(c++)],s=c<e.length?r[e.charAt(c)]:0;++c;var a=c<e.length?r[e.charAt(c)]:64;++c;var o=c<e.length?r[e.charAt(c)]:64;if(++c,null==i||null==s||null==a||null==o)throw new l;n.push(i<<2|s>>4),64!==a&&(n.push(s<<4&240|a>>2),64!==o&&n.push(a<<6&192|o))}return n},init_(){if(!this.byteToCharMap_){this.byteToCharMap_={},this.charToByteMap_={},this.byteToCharMapWebSafe_={},this.charToByteMapWebSafe_={};for(let e=0;e<this.ENCODED_VALS.length;e++)this.byteToCharMap_[e]=this.ENCODED_VALS.charAt(e),this.charToByteMap_[this.byteToCharMap_[e]]=e,this.byteToCharMapWebSafe_[e]=this.ENCODED_VALS_WEBSAFE.charAt(e),this.charToByteMapWebSafe_[this.byteToCharMapWebSafe_[e]]=e,e>=this.ENCODED_VALS_BASE.length&&(this.charToByteMap_[this.ENCODED_VALS_WEBSAFE.charAt(e)]=e,this.charToByteMapWebSafe_[this.ENCODED_VALS.charAt(e)]=e)}}};class l extends Error{constructor(){super(...arguments),this.name="DecodeBase64StringError"}}const s=function(e){try{return t.decodeString(e,!0)}catch(e){console.error("base64Decode failed: ",e)}return null};const r=()=>function(){if("undefined"!=typeof self)return self;if("undefined"!=typeof window)return window;if("undefined"!=typeof global)return global;throw new Error("Unable to locate global object.")}().__FIREBASE_DEFAULTS__,n=()=>{if("undefined"!=typeof process&&void 0!==process.env){var e=process.env.__FIREBASE_DEFAULTS__;return e?JSON.parse(e):void 0}},a=()=>{if("undefined"!=typeof document){let e;try{e=document.cookie.match(/__FIREBASE_DEFAULTS__=([^;]+)/)}catch(e){return}var t=e&&s(e[1]);return t&&JSON.parse(t)}},o=()=>{try{return r()||n()||a()}catch(e){return void console.info(`Unable to get __FIREBASE_DEFAULTS__ due to: ${e}`)}};var c,u;function d(){return"undefined"!=typeof navigator&&"string"==typeof navigator.userAgent?navigator.userAgent:""}function h(){var e=null===(e=o())||void 0===e?void 0:e.forceEnvironment;if("node"===e)return!0;if("browser"===e)return!1;try{return"[object process]"===Object.prototype.toString.call(global.process)}catch(e){return!1}}function p(){var e="object"==typeof chrome?chrome.runtime:"object"==typeof browser?browser.runtime:void 0;return"object"==typeof e&&void 0!==e.id}function f(){return"object"==typeof navigator&&"ReactNative"===navigator.product}function m(){const e=d();return 0<=e.indexOf("MSIE ")||0<=e.indexOf("Trident/")}function v(){try{return"object"==typeof indexedDB}catch(e){return!1}}class g extends Error{constructor(e,t,r){super(t),this.code=e,this.customData=r,this.name="FirebaseError",Object.setPrototypeOf(this,g.prototype),Error.captureStackTrace&&Error.captureStackTrace(this,_.prototype.create)}}class _{constructor(e,t,r){this.service=e,this.serviceName=t,this.errors=r}create(e,...t){var n,r=t[0]||{},i=`${this.service}/${e}`,s=this.errors[e],s=s?(n=r,s.replace(y,(e,t)=>{var r=n[t];return null!=r?String(r):`<${t}?>`})):"Error",s=`${this.serviceName}: ${s} (${i}).`;return new g(i,s,r)}}const y=/\{\$([^}]+)}/g;function I(e){const t=[];for(const[r,n]of Object.entries(e))Array.isArray(n)?n.forEach(e=>{t.push(encodeURIComponent(r)+"="+encodeURIComponent(e))}):t.push(encodeURIComponent(r)+"="+encodeURIComponent(n));return t.length?"&"+t.join("&"):""}function w(e){const n={},t=e.replace(/^\?/,"").split("&");return t.forEach(e=>{var t,r;e&&([t,r]=e.split("="),n[decodeURIComponent(t)]=decodeURIComponent(r))}),n}function T(e){var t=e.indexOf("?");if(!t)return"";var r=e.indexOf("#",t);return e.substring(t,0<r?r:void 0)}class b{constructor(e,t){this.observers=[],this.unsubscribes=[],this.observerCount=0,this.task=Promise.resolve(),this.finalized=!1,this.onNoObservers=t,this.task.then(()=>{e(this)}).catch(e=>{this.error(e)})}next(t){this.forEachObserver(e=>{e.next(t)})}error(t){this.forEachObserver(e=>{e.error(t)}),this.close(t)}complete(){this.forEachObserver(e=>{e.complete()}),this.close()}subscribe(e,t,r){let n;if(void 0===e&&void 0===t&&void 0===r)throw new Error("Missing Observer.");n=function(e,t){if("object"!=typeof e||null===e)return!1;for(const r of t)if(r in e&&"function"==typeof e[r])return!0;return!1}(e,["next","error","complete"])?e:{next:e,error:t,complete:r},void 0===n.next&&(n.next=E),void 0===n.error&&(n.error=E),void 0===n.complete&&(n.complete=E);var i=this.unsubscribeOne.bind(this,this.observers.length);return this.finalized&&this.task.then(()=>{try{this.finalError?n.error(this.finalError):n.complete()}catch(e){}}),this.observers.push(n),i}unsubscribeOne(e){void 0!==this.observers&&void 0!==this.observers[e]&&(delete this.observers[e],--this.observerCount,0===this.observerCount&&void 0!==this.onNoObservers&&this.onNoObservers(this))}forEachObserver(t){if(!this.finalized)for(let e=0;e<this.observers.length;e++)this.sendOne(e,t)}sendOne(e,t){this.task.then(()=>{if(void 0!==this.observers&&void 0!==this.observers[e])try{t(this.observers[e])}catch(e){"undefined"!=typeof console&&console.error&&console.error(e)}})}close(e){this.finalized||(this.finalized=!0,void 0!==e&&(this.finalError=e),this.task.then(()=>{this.observers=void 0,this.onNoObservers=void 0}))}}function E(){}function k(e){return e&&e._delegate?e._delegate:e}(u=c=c||{})[u.DEBUG=0]="DEBUG",u[u.VERBOSE=1]="VERBOSE",u[u.INFO=2]="INFO",u[u.WARN=3]="WARN",u[u.ERROR=4]="ERROR",u[u.SILENT=5]="SILENT";const S={debug:c.DEBUG,verbose:c.VERBOSE,info:c.INFO,warn:c.WARN,error:c.ERROR,silent:c.SILENT},A=c.INFO,R={[c.DEBUG]:"log",[c.VERBOSE]:"log",[c.INFO]:"info",[c.WARN]:"warn",[c.ERROR]:"error"},P=(e,t,...r)=>{if(!(t<e.logLevel)){var n=(new Date).toISOString(),i=R[t];if(!i)throw new Error(`Attempted to log a message with an invalid logType (value: ${t})`);console[i](`[${n}]  ${e.name}:`,...r)}};function C(e,t){var r={};for(i in e)Object.prototype.hasOwnProperty.call(e,i)&&t.indexOf(i)<0&&(r[i]=e[i]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var n=0,i=Object.getOwnPropertySymbols(e);n<i.length;n++)t.indexOf(i[n])<0&&Object.prototype.propertyIsEnumerable.call(e,i[n])&&(r[i[n]]=e[i[n]]);return r}class O{constructor(e,t,r){this.name=e,this.instanceFactory=t,this.type=r,this.multipleInstances=!1,this.serviceProps={},this.instantiationMode="LAZY",this.onInstanceCreated=null}setInstantiationMode(e){return this.instantiationMode=e,this}setMultipleInstances(e){return this.multipleInstances=e,this}setServiceProps(e){return this.serviceProps=e,this}setInstanceCreatedCallback(e){return this.onInstanceCreated=e,this}}const N={FACEBOOK:"facebook.com",GITHUB:"github.com",GOOGLE:"google.com",PASSWORD:"password",PHONE:"phone",TWITTER:"twitter.com"},L={EMAIL_SIGNIN:"EMAIL_SIGNIN",PASSWORD_RESET:"PASSWORD_RESET",RECOVER_EMAIL:"RECOVER_EMAIL",REVERT_SECOND_FACTOR_ADDITION:"REVERT_SECOND_FACTOR_ADDITION",VERIFY_AND_CHANGE_EMAIL:"VERIFY_AND_CHANGE_EMAIL",VERIFY_EMAIL:"VERIFY_EMAIL"};function D(){return{"dependent-sdk-initialized-before-auth":"Another Firebase SDK was initialized and is trying to use Auth before Auth is initialized. Please be sure to call `initializeAuth` or `getAuth` before starting any other Firebase SDK."}}function U(){return{"admin-restricted-operation":"This operation is restricted to administrators only.","argument-error":"","app-not-authorized":"This app, identified by the domain where it's hosted, is not authorized to use Firebase Authentication with the provided API key. Review your key configuration in the Google API console.","app-not-installed":"The requested mobile application corresponding to the identifier (Android package name or iOS bundle ID) provided is not installed on this device.","captcha-check-failed":"The reCAPTCHA response token provided is either invalid, expired, already used or the domain associated with it does not match the list of whitelisted domains.","code-expired":"The SMS code has expired. Please re-send the verification code to try again.","cordova-not-ready":"Cordova framework is not ready.","cors-unsupported":"This browser is not supported.","credential-already-in-use":"This credential is already associated with a different user account.","custom-token-mismatch":"The custom token corresponds to a different audience.","requires-recent-login":"This operation is sensitive and requires recent authentication. Log in again before retrying this request.","dependent-sdk-initialized-before-auth":"Another Firebase SDK was initialized and is trying to use Auth before Auth is initialized. Please be sure to call `initializeAuth` or `getAuth` before starting any other Firebase SDK.","dynamic-link-not-activated":"Please activate Dynamic Links in the Firebase Console and agree to the terms and conditions.","email-change-needs-verification":"Multi-factor users must always have a verified email.","email-already-in-use":"The email address is already in use by another account.","emulator-config-failed":'Auth instance has already been used to make a network call. Auth can no longer be configured to use the emulator. Try calling "connectAuthEmulator()" sooner.',"expired-action-code":"The action code has expired.","cancelled-popup-request":"This operation has been cancelled due to another conflicting popup being opened.","internal-error":"An internal AuthError has occurred.","invalid-app-credential":"The phone verification request contains an invalid application verifier. The reCAPTCHA token response is either invalid or expired.","invalid-app-id":"The mobile app identifier is not registered for the current project.","invalid-user-token":"This user's credential isn't valid for this project. This can happen if the user's token has been tampered with, or if the user isn't for the project associated with this API key.","invalid-auth-event":"An internal AuthError has occurred.","invalid-verification-code":"The SMS verification code used to create the phone auth credential is invalid. Please resend the verification code sms and be sure to use the verification code provided by the user.","invalid-continue-uri":"The continue URL provided in the request is invalid.","invalid-cordova-configuration":"The following Cordova plugins must be installed to enable OAuth sign-in: cordova-plugin-buildinfo, cordova-universal-links-plugin, cordova-plugin-browsertab, cordova-plugin-inappbrowser and cordova-plugin-customurlscheme.","invalid-custom-token":"The custom token format is incorrect. Please check the documentation.","invalid-dynamic-link-domain":"The provided dynamic link domain is not configured or authorized for the current project.","invalid-email":"The email address is badly formatted.","invalid-emulator-scheme":"Emulator URL must start with a valid scheme (http:// or https://).","invalid-api-key":"Your API key is invalid, please check you have copied it correctly.","invalid-cert-hash":"The SHA-1 certificate hash provided is invalid.","invalid-credential":"The supplied auth credential is incorrect, malformed or has expired.","invalid-message-payload":"The email template corresponding to this action contains invalid characters in its message. Please fix by going to the Auth email templates section in the Firebase Console.","invalid-multi-factor-session":"The request does not contain a valid proof of first factor successful sign-in.","invalid-oauth-provider":"EmailAuthProvider is not supported for this operation. This operation only supports OAuth providers.","invalid-oauth-client-id":"The OAuth client ID provided is either invalid or does not match the specified API key.","unauthorized-domain":"This domain is not authorized for OAuth operations for your Firebase project. Edit the list of authorized domains from the Firebase console.","invalid-action-code":"The action code is invalid. This can happen if the code is malformed, expired, or has already been used.","wrong-password":"The password is invalid or the user does not have a password.","invalid-persistence-type":"The specified persistence type is invalid. It can only be local, session or none.","invalid-phone-number":"The format of the phone number provided is incorrect. Please enter the phone number in a format that can be parsed into E.164 format. E.164 phone numbers are written in the format [+][country code][subscriber number including area code].","invalid-provider-id":"The specified provider ID is invalid.","invalid-recipient-email":"The email corresponding to this action failed to send as the provided recipient email address is invalid.","invalid-sender":"The email template corresponding to this action contains an invalid sender email or name. Please fix by going to the Auth email templates section in the Firebase Console.","invalid-verification-id":"The verification ID used to create the phone auth credential is invalid.","invalid-tenant-id":"The Auth instance's tenant ID is invalid.","login-blocked":"Login blocked by user-provided method: {$originalMessage}","missing-android-pkg-name":"An Android Package Name must be provided if the Android App is required to be installed.","auth-domain-config-required":"Be sure to include authDomain when calling firebase.initializeApp(), by following the instructions in the Firebase console.","missing-app-credential":"The phone verification request is missing an application verifier assertion. A reCAPTCHA response token needs to be provided.","missing-verification-code":"The phone auth credential was created with an empty SMS verification code.","missing-continue-uri":"A continue URL must be provided in the request.","missing-iframe-start":"An internal AuthError has occurred.","missing-ios-bundle-id":"An iOS Bundle ID must be provided if an App Store ID is provided.","missing-or-invalid-nonce":"The request does not contain a valid nonce. This can occur if the SHA-256 hash of the provided raw nonce does not match the hashed nonce in the ID token payload.","missing-password":"A non-empty password must be provided","missing-multi-factor-info":"No second factor identifier is provided.","missing-multi-factor-session":"The request is missing proof of first factor successful sign-in.","missing-phone-number":"To send verification codes, provide a phone number for the recipient.","missing-verification-id":"The phone auth credential was created with an empty verification ID.","app-deleted":"This instance of FirebaseApp has been deleted.","multi-factor-info-not-found":"The user does not have a second factor matching the identifier provided.","multi-factor-auth-required":"Proof of ownership of a second factor is required to complete sign-in.","account-exists-with-different-credential":"An account already exists with the same email address but different sign-in credentials. Sign in using a provider associated with this email address.","network-request-failed":"A network AuthError (such as timeout, interrupted connection or unreachable host) has occurred.","no-auth-event":"An internal AuthError has occurred.","no-such-provider":"User was not linked to an account with the given provider.","null-user":"A null user object was provided as the argument for an operation which requires a non-null user object.","operation-not-allowed":"The given sign-in provider is disabled for this Firebase project. Enable it in the Firebase console, under the sign-in method tab of the Auth section.","operation-not-supported-in-this-environment":'This operation is not supported in the environment this application is running on. "location.protocol" must be http, https or chrome-extension and web storage must be enabled.',"popup-blocked":"Unable to establish a connection with the popup. It may have been blocked by the browser.","popup-closed-by-user":"The popup has been closed by the user before finalizing the operation.","provider-already-linked":"User can only be linked to one identity for the given provider.","quota-exceeded":"The project's quota for this operation has been exceeded.","redirect-cancelled-by-user":"The redirect operation has been cancelled by the user before finalizing.","redirect-operation-pending":"A redirect sign-in operation is already pending.","rejected-credential":"The request contains malformed or mismatching credentials.","second-factor-already-in-use":"The second factor is already enrolled on this account.","maximum-second-factor-count-exceeded":"The maximum allowed number of second factors on a user has been exceeded.","tenant-id-mismatch":"The provided tenant ID does not match the Auth instance's tenant ID",timeout:"The operation has timed out.","user-token-expired":"The user's credential is no longer valid. The user must sign in again.","too-many-requests":"We have blocked all requests from this device due to unusual activity. Try again later.","unauthorized-continue-uri":"The domain of the continue URL is not whitelisted.  Please whitelist the domain in the Firebase console.","unsupported-first-factor":"Enrolling a second factor or signing in with a multi-factor account requires sign-in with a supported first factor.","unsupported-persistence-type":"The current environment does not support the specified persistence type.","unsupported-tenant-operation":"This operation is not supported in a multi-tenant context.","unverified-email":"The operation requires a verified email.","user-cancelled":"The user did not grant your application the permissions it requested.","user-not-found":"There is no user record corresponding to this identifier. The user may have been deleted.","user-disabled":"The user account has been disabled by an administrator.","user-mismatch":"The supplied credentials do not correspond to the previously signed in user.","user-signed-out":"","weak-password":"The password must be 6 characters long or more.","web-storage-unsupported":"This browser is not supported or 3rd party cookies and data may be disabled.","already-initialized":"initializeAuth() has already been called with different options. To avoid this error, call initializeAuth() with the same options as when it was originally called, or call getAuth() to return the already initialized instance.","missing-recaptcha-token":"The reCAPTCHA token is missing when sending request to the backend.","invalid-recaptcha-token":"The reCAPTCHA token is invalid when sending request to the backend.","invalid-recaptcha-action":"The reCAPTCHA action is invalid when sending request to the backend.","recaptcha-not-enabled":"reCAPTCHA Enterprise integration is not enabled for this project.","missing-client-type":"The reCAPTCHA client type is missing when sending request to the backend.","missing-recaptcha-version":"The reCAPTCHA version is missing when sending request to the backend.","invalid-req-type":"Invalid request parameters.","invalid-recaptcha-version":"The reCAPTCHA version is invalid when sending request to the backend.","unsupported-password-policy-schema-version":"The password policy received from the backend uses a schema version that is not supported by this version of the Firebase SDK.","password-does-not-meet-requirements":"The password does not meet the requirements."}}const M=D,F=new _("auth","Firebase",D()),V=new class{constructor(e){this.name=e,this._logLevel=A,this._logHandler=P,this._userLogHandler=null}get logLevel(){return this._logLevel}set logLevel(e){if(!(e in c))throw new TypeError(`Invalid value "${e}" assigned to \`logLevel\``);this._logLevel=e}setLogLevel(e){this._logLevel="string"==typeof e?S[e]:e}get logHandler(){return this._logHandler}set logHandler(e){if("function"!=typeof e)throw new TypeError("Value assigned to `logHandler` must be a function");this._logHandler=e}get userLogHandler(){return this._userLogHandler}set userLogHandler(e){this._userLogHandler=e}debug(...e){this._userLogHandler&&this._userLogHandler(this,c.DEBUG,...e),this._logHandler(this,c.DEBUG,...e)}log(...e){this._userLogHandler&&this._userLogHandler(this,c.VERBOSE,...e),this._logHandler(this,c.VERBOSE,...e)}info(...e){this._userLogHandler&&this._userLogHandler(this,c.INFO,...e),this._logHandler(this,c.INFO,...e)}warn(...e){this._userLogHandler&&this._userLogHandler(this,c.WARN,...e),this._logHandler(this,c.WARN,...e)}error(...e){this._userLogHandler&&this._userLogHandler(this,c.ERROR,...e),this._logHandler(this,c.ERROR,...e)}}("@firebase/auth");function x(e,...t){V.logLevel<=c.ERROR&&V.error(`Auth (${Oi.SDK_VERSION}): ${e}`,...t)}function j(e,...t){throw B(e,...t)}function H(e,...t){return B(e,...t)}function W(e,t,r){var n=Object.assign(Object.assign({},M()),{[t]:r});const i=new _("auth","Firebase",n);return i.create(t,{appName:e.name})}function q(e){return W(e,"operation-not-supported-in-this-environment","Operations that alter the current user are not supported in conjunction with FirebaseServerApp")}function z(e,t,r){if(!(t instanceof r))throw r.name!==t.constructor.name&&j(e,"argument-error"),W(e,"argument-error",`Type of ${t.constructor.name} does not match expected instance.`+"Did you pass a reference from a different Auth SDK?")}function B(e,...t){if("string"==typeof e)return F.create(e,...t);{var r=t[0];const n=[...t.slice(1)];return n[0]&&(n[0].appName=e.name),e._errorFactory.create(r,...n)}}function G(e,t,...r){if(!e)throw B(t,...r)}function K(e){var t="INTERNAL ASSERTION FAILED: "+e;throw x(t),new Error(t)}function $(e,t){e||K(t)}function J(){var e;return"undefined"!=typeof self&&(null===(e=self.location)||void 0===e?void 0:e.href)||""}function Y(){return"http:"===X()||"https:"===X()}function X(){var e;return"undefined"!=typeof self&&(null===(e=self.location)||void 0===e?void 0:e.protocol)||null}class Q{constructor(e,t){$((this.shortDelay=e)<(this.longDelay=t),"Short delay should be less than long delay!"),this.isMobile="undefined"!=typeof window&&!!(window.cordova||window.phonegap||window.PhoneGap)&&/ios|iphone|ipod|ipad|android|blackberry|iemobile/i.test(d())||f()}get(){return"undefined"!=typeof navigator&&navigator&&"onLine"in navigator&&"boolean"==typeof navigator.onLine&&(Y()||p()||"connection"in navigator)&&!navigator.onLine?Math.min(5e3,this.shortDelay):this.isMobile?this.longDelay:this.shortDelay}}function Z(e,t){$(e.emulator,"Emulator should always be set here");var r=e.emulator["url"];return t?`${r}${t.startsWith("/")?t.slice(1):t}`:r}class ee{static initialize(e,t,r){this.fetchImpl=e,t&&(this.headersImpl=t),r&&(this.responseImpl=r)}static fetch(){return this.fetchImpl||("undefined"!=typeof self&&"fetch"in self?self.fetch:"undefined"!=typeof globalThis&&globalThis.fetch?globalThis.fetch:"undefined"!=typeof fetch?fetch:void K("Could not find fetch implementation, make sure you call FetchProvider.initialize() with an appropriate polyfill"))}static headers(){return this.headersImpl||("undefined"!=typeof self&&"Headers"in self?self.Headers:"undefined"!=typeof globalThis&&globalThis.Headers?globalThis.Headers:"undefined"!=typeof Headers?Headers:void K("Could not find Headers implementation, make sure you call FetchProvider.initialize() with an appropriate polyfill"))}static response(){return this.responseImpl||("undefined"!=typeof self&&"Response"in self?self.Response:"undefined"!=typeof globalThis&&globalThis.Response?globalThis.Response:"undefined"!=typeof Response?Response:void K("Could not find Response implementation, make sure you call FetchProvider.initialize() with an appropriate polyfill"))}}const te={CREDENTIAL_MISMATCH:"custom-token-mismatch",MISSING_CUSTOM_TOKEN:"internal-error",INVALID_IDENTIFIER:"invalid-email",MISSING_CONTINUE_URI:"internal-error",INVALID_PASSWORD:"wrong-password",MISSING_PASSWORD:"missing-password",INVALID_LOGIN_CREDENTIALS:"invalid-credential",EMAIL_EXISTS:"email-already-in-use",PASSWORD_LOGIN_DISABLED:"operation-not-allowed",INVALID_IDP_RESPONSE:"invalid-credential",INVALID_PENDING_TOKEN:"invalid-credential",FEDERATED_USER_ID_ALREADY_LINKED:"credential-already-in-use",MISSING_REQ_TYPE:"internal-error",EMAIL_NOT_FOUND:"user-not-found",RESET_PASSWORD_EXCEED_LIMIT:"too-many-requests",EXPIRED_OOB_CODE:"expired-action-code",INVALID_OOB_CODE:"invalid-action-code",MISSING_OOB_CODE:"internal-error",CREDENTIAL_TOO_OLD_LOGIN_AGAIN:"requires-recent-login",INVALID_ID_TOKEN:"invalid-user-token",TOKEN_EXPIRED:"user-token-expired",USER_NOT_FOUND:"user-token-expired",TOO_MANY_ATTEMPTS_TRY_LATER:"too-many-requests",PASSWORD_DOES_NOT_MEET_REQUIREMENTS:"password-does-not-meet-requirements",INVALID_CODE:"invalid-verification-code",INVALID_SESSION_INFO:"invalid-verification-id",INVALID_TEMPORARY_PROOF:"invalid-credential",MISSING_SESSION_INFO:"missing-verification-id",SESSION_EXPIRED:"code-expired",MISSING_ANDROID_PACKAGE_NAME:"missing-android-pkg-name",UNAUTHORIZED_DOMAIN:"unauthorized-continue-uri",INVALID_OAUTH_CLIENT_ID:"invalid-oauth-client-id",ADMIN_ONLY_OPERATION:"admin-restricted-operation",INVALID_MFA_PENDING_CREDENTIAL:"invalid-multi-factor-session",MFA_ENROLLMENT_NOT_FOUND:"multi-factor-info-not-found",MISSING_MFA_ENROLLMENT_ID:"missing-multi-factor-info",MISSING_MFA_PENDING_CREDENTIAL:"missing-multi-factor-session",SECOND_FACTOR_EXISTS:"second-factor-already-in-use",SECOND_FACTOR_LIMIT_EXCEEDED:"maximum-second-factor-count-exceeded",BLOCKING_FUNCTION_ERROR_RESPONSE:"internal-error",RECAPTCHA_NOT_ENABLED:"recaptcha-not-enabled",MISSING_RECAPTCHA_TOKEN:"missing-recaptcha-token",INVALID_RECAPTCHA_TOKEN:"invalid-recaptcha-token",INVALID_RECAPTCHA_ACTION:"invalid-recaptcha-action",MISSING_CLIENT_TYPE:"missing-client-type",MISSING_RECAPTCHA_VERSION:"missing-recaptcha-version",INVALID_RECAPTCHA_VERSION:"invalid-recaptcha-version",INVALID_REQ_TYPE:"invalid-req-type"},re=new Q(3e4,6e4);function ne(e,t){return e.tenantId&&!t.tenantId?Object.assign(Object.assign({},t),{tenantId:e.tenantId}):t}async function ie(s,a,o,c,e={}){return se(s,e,async()=>{let e={},t={};c&&("GET"===a?t=c:e={body:JSON.stringify(c)});var r=I(Object.assign({key:s.config.apiKey},t)).slice(1);const n=await s._getAdditionalHeaders();n["Content-Type"]="application/json",s.languageCode&&(n["X-Firebase-Locale"]=s.languageCode);const i=Object.assign({method:a,headers:n},e);return"undefined"!=typeof navigator&&"Cloudflare-Workers"===navigator.userAgent||(i.referrerPolicy="no-referrer"),ee.fetch()(oe(s,s.config.apiHost,o,r),i)})}async function se(t,e,r){t._canInitEmulator=!1;var n=Object.assign(Object.assign({},te),e);try{const a=new ce(t),o=await Promise.race([r(),a.promise]);a.clearNetworkTimeout();var i=await o.json();if("needConfirmation"in i)throw le(t,"account-exists-with-different-credential",i);if(o.ok&&!("errorMessage"in i))return i;{const c=o.ok?i.errorMessage:i.error.message,[l,u]=c.split(" : ");if("FEDERATED_USER_ID_ALREADY_LINKED"===l)throw le(t,"credential-already-in-use",i);if("EMAIL_EXISTS"===l)throw le(t,"email-already-in-use",i);if("USER_DISABLED"===l)throw le(t,"user-disabled",i);var s=n[l]||l.toLowerCase().replace(/[_\s]+/g,"-");if(u)throw W(t,s,u);j(t,s)}}catch(e){if(e instanceof g)throw e;j(t,"network-request-failed",{message:String(e)})}}async function ae(e,t,r,n,i={}){var s=await ie(e,t,r,n,i);return"mfaPendingCredential"in s&&j(e,"multi-factor-auth-required",{_serverResponse:s}),s}function oe(e,t,r,n){var i=`${t}${r}?${n}`;return e.config.emulator?Z(e.config,i):`${e.config.apiScheme}://${i}`}class ce{constructor(e){this.auth=e,this.timer=null,this.promise=new Promise((e,t)=>{this.timer=setTimeout(()=>t(H(this.auth,"network-request-failed")),re.get())})}clearNetworkTimeout(){clearTimeout(this.timer)}}function le(e,t,r){const n={appName:e.name};r.email&&(n.email=r.email),r.phoneNumber&&(n.phoneNumber=r.phoneNumber);const i=H(e,t,n);return i.customData._tokenResponse=r,i}function ue(e){return void 0!==e&&void 0!==e.getResponse}function de(e){return void 0!==e&&void 0!==e.enterprise}class he{constructor(e){if(this.siteKey="",this.recaptchaEnforcementState=[],void 0===e.recaptchaKey)throw new Error("recaptchaKey undefined");this.siteKey=e.recaptchaKey.split("/")[3],this.recaptchaEnforcementState=e.recaptchaEnforcementState}getProviderEnforcementState(e){if(!this.recaptchaEnforcementState||0===this.recaptchaEnforcementState.length)return null;for(const t of this.recaptchaEnforcementState)if(t.provider&&t.provider===e)return function(e){switch(e){case"ENFORCE":return"ENFORCE";case"AUDIT":return"AUDIT";case"OFF":return"OFF";default:return"ENFORCEMENT_STATE_UNSPECIFIED"}}(t.enforcementState);return null}isProviderEnabled(e){return"ENFORCE"===this.getProviderEnforcementState(e)||"AUDIT"===this.getProviderEnforcementState(e)}}async function pe(e,t){return ie(e,"POST","/v1/accounts:lookup",t)}function fe(e){if(e)try{const t=new Date(Number(e));if(!isNaN(t.getTime()))return t.toUTCString()}catch(e){}}function me(e){return 1e3*Number(e)}function ve(e){var[t,r,n]=e.split(".");if(void 0===t||void 0===r||void 0===n)return x("JWT malformed, contained fewer than 3 sections"),null;try{var i=s(r);return i?JSON.parse(i):(x("Failed to decode base64 JWT payload"),null)}catch(e){return x("Caught error parsing JWT payload as JSON",null==e?void 0:e.toString()),null}}function ge(e){var t=ve(e);return G(t,"internal-error"),G(void 0!==t.exp,"internal-error"),G(void 0!==t.iat,"internal-error"),Number(t.exp)-Number(t.iat)}async function _e(t,r,e=!1){if(e)return r;try{return r}catch(e){throw e instanceof g&&(r=[e["code"]][0],"auth/user-disabled"===r||"auth/user-token-expired"===r)&&t.auth.currentUser===t&&await t.auth.signOut(),e}}class ye{constructor(e){this.user=e,this.isRunning=!1,this.timerId=null,this.errorBackoff=3e4}_start(){this.isRunning||(this.isRunning=!0,this.schedule())}_stop(){this.isRunning&&(this.isRunning=!1,null!==this.timerId&&clearTimeout(this.timerId))}getInterval(e){if(e){var t=this.errorBackoff;return this.errorBackoff=Math.min(2*this.errorBackoff,96e4),t}this.errorBackoff=3e4;t=(null!==(t=this.user.stsTokenManager.expirationTime)&&void 0!==t?t:0)-Date.now()-3e5;return Math.max(0,t)}schedule(e=!1){var t;this.isRunning&&(t=this.getInterval(e),this.timerId=setTimeout(async()=>{await this.iteration()},t))}async iteration(){try{await this.user.getIdToken(!0)}catch(e){return void("auth/network-request-failed"===(null==e?void 0:e.code)&&this.schedule(!0))}this.schedule()}}class Ie{constructor(e,t){this.createdAt=e,this.lastLoginAt=t,this._initializeTime()}_initializeTime(){this.lastSignInTime=fe(this.lastLoginAt),this.creationTime=fe(this.createdAt)}_copy(e){this.createdAt=e.createdAt,this.lastLoginAt=e.lastLoginAt,this._initializeTime()}toJSON(){return{createdAt:this.createdAt,lastLoginAt:this.lastLoginAt}}}async function we(e){var t=e.auth,r=await e.getIdToken(),n=await _e(e,pe(t,{idToken:r}));G(null==n?void 0:n.users.length,t,"internal-error");var i=n.users[0];e._notifyReloadListener(i);var s,a,t=null!==(r=i.providerUserInfo)&&void 0!==r&&r.length?Te(i.providerUserInfo):[],n=(s=e.providerData,a=t,[...s.filter(t=>!a.some(e=>e.providerId===t.providerId)),...a]),r=e.isAnonymous,t=!(e.email&&i.passwordHash||null!==n&&n.length),t=!!r&&t,t={uid:i.localId,displayName:i.displayName||null,photoURL:i.photoUrl||null,email:i.email||null,emailVerified:i.emailVerified||!1,phoneNumber:i.phoneNumber||null,tenantId:i.tenantId||null,providerData:n,metadata:new Ie(i.createdAt,i.lastLoginAt),isAnonymous:t};Object.assign(e,t)}function Te(e){return e.map(e=>{var t=e["providerId"],r=C(e,["providerId"]);return{providerId:t,uid:r.rawId||"",displayName:r.displayName||null,email:r.email||null,phoneNumber:r.phoneNumber||null,photoURL:r.photoUrl||null}})}class be{constructor(){this.refreshToken=null,this.accessToken=null,this.expirationTime=null}get isExpired(){return!this.expirationTime||Date.now()>this.expirationTime-3e4}updateFromServerResponse(e){G(e.idToken,"internal-error"),G(void 0!==e.idToken,"internal-error"),G(void 0!==e.refreshToken,"internal-error");var t="expiresIn"in e&&void 0!==e.expiresIn?Number(e.expiresIn):ge(e.idToken);this.updateTokensAndExpiration(e.idToken,e.refreshToken,t)}updateFromIdToken(e){G(0!==e.length,"internal-error");var t=ge(e);this.updateTokensAndExpiration(e,null,t)}async getToken(e,t=!1){return t||!this.accessToken||this.isExpired?(G(this.refreshToken,e,"user-token-expired"),this.refreshToken?(await this.refresh(e,this.refreshToken),this.accessToken):null):this.accessToken}clearRefreshToken(){this.refreshToken=null}async refresh(e,t){var i,s,{accessToken:r,refreshToken:n,expiresIn:a}=(s=t,await{accessToken:(a=await se(i=e,{},async()=>{var e=I({grant_type:"refresh_token",refresh_token:s}).slice(1),{tokenApiHost:t,apiKey:r}=i.config,r=oe(i,t,"/v1/token",`key=${r}`);const n=await i._getAdditionalHeaders();return n["Content-Type"]="application/x-www-form-urlencoded",ee.fetch()(r,{method:"POST",headers:n,body:e})})).access_token,expiresIn:a.expires_in,refreshToken:a.refresh_token});this.updateTokensAndExpiration(r,n,Number(a))}updateTokensAndExpiration(e,t,r){this.refreshToken=t||null,this.accessToken=e||null,this.expirationTime=Date.now()+1e3*r}static fromJSON(e,t){var{refreshToken:r,accessToken:n,expirationTime:i}=t;const s=new be;return r&&(G("string"==typeof r,"internal-error",{appName:e}),s.refreshToken=r),n&&(G("string"==typeof n,"internal-error",{appName:e}),s.accessToken=n),i&&(G("number"==typeof i,"internal-error",{appName:e}),s.expirationTime=i),s}toJSON(){return{refreshToken:this.refreshToken,accessToken:this.accessToken,expirationTime:this.expirationTime}}_assign(e){this.accessToken=e.accessToken,this.refreshToken=e.refreshToken,this.expirationTime=e.expirationTime}_clone(){return Object.assign(new be,this.toJSON())}_performRefresh(){return K("not implemented")}}function Ee(e,t){G("string"==typeof e||void 0===e,"internal-error",{appName:t})}class ke{constructor(e){var{uid:t,auth:r,stsTokenManager:n}=e,i=C(e,["uid","auth","stsTokenManager"]);this.providerId="firebase",this.proactiveRefresh=new ye(this),this.reloadUserInfo=null,this.reloadListener=null,this.uid=t,this.auth=r,this.stsTokenManager=n,this.accessToken=n.accessToken,this.displayName=i.displayName||null,this.email=i.email||null,this.emailVerified=i.emailVerified||!1,this.phoneNumber=i.phoneNumber||null,this.photoURL=i.photoURL||null,this.isAnonymous=i.isAnonymous||!1,this.tenantId=i.tenantId||null,this.providerData=i.providerData?[...i.providerData]:[],this.metadata=new Ie(i.createdAt||void 0,i.lastLoginAt||void 0)}async getIdToken(e){var t=await _e(this,this.stsTokenManager.getToken(this.auth,e));return G(t,this.auth,"internal-error"),this.accessToken!==t&&(this.accessToken=t,await this.auth._persistUserIfCurrent(this),this.auth._notifyListenersIfCurrent(this)),t}getIdTokenResult(e){return async function(e,t=!1){const r=k(e);var n=await r.getIdToken(t),i=ve(n);G(i&&i.exp&&i.auth_time&&i.iat,r.auth,"internal-error");var s="object"==typeof i.firebase?i.firebase:void 0,a=null==s?void 0:s.sign_in_provider;return{claims:i,token:n,authTime:fe(me(i.auth_time)),issuedAtTime:fe(me(i.iat)),expirationTime:fe(me(i.exp)),signInProvider:a||null,signInSecondFactor:(null==s?void 0:s.sign_in_second_factor)||null}}(this,e)}reload(){return async function(e){const t=k(e);await we(t),await t.auth._persistUserIfCurrent(t),t.auth._notifyListenersIfCurrent(t)}(this)}_assign(e){this!==e&&(G(this.uid===e.uid,this.auth,"internal-error"),this.displayName=e.displayName,this.photoURL=e.photoURL,this.email=e.email,this.emailVerified=e.emailVerified,this.phoneNumber=e.phoneNumber,this.isAnonymous=e.isAnonymous,this.tenantId=e.tenantId,this.providerData=e.providerData.map(e=>Object.assign({},e)),this.metadata._copy(e.metadata),this.stsTokenManager._assign(e.stsTokenManager))}_clone(e){const t=new ke(Object.assign(Object.assign({},this),{auth:e,stsTokenManager:this.stsTokenManager._clone()}));return t.metadata._copy(this.metadata),t}_onReload(e){G(!this.reloadListener,this.auth,"internal-error"),this.reloadListener=e,this.reloadUserInfo&&(this._notifyReloadListener(this.reloadUserInfo),this.reloadUserInfo=null)}_notifyReloadListener(e){this.reloadListener?this.reloadListener(e):this.reloadUserInfo=e}_startProactiveRefresh(){this.proactiveRefresh._start()}_stopProactiveRefresh(){this.proactiveRefresh._stop()}async _updateTokensIfNecessary(e,t=!1){let r=!1;e.idToken&&e.idToken!==this.stsTokenManager.accessToken&&(this.stsTokenManager.updateFromServerResponse(e),r=!0),t&&await we(this),await this.auth._persistUserIfCurrent(this),r&&this.auth._notifyListenersIfCurrent(this)}async delete(){if(Oi._isFirebaseServerApp(this.auth.app))return Promise.reject(q(this.auth));var e=await this.getIdToken();return await _e(this,async function(e,t){return ie(e,"POST","/v1/accounts:delete",t)}(this.auth,{idToken:e})),this.stsTokenManager.clearRefreshToken(),this.auth.signOut()}toJSON(){return Object.assign(Object.assign({uid:this.uid,email:this.email||void 0,emailVerified:this.emailVerified,displayName:this.displayName||void 0,isAnonymous:this.isAnonymous,photoURL:this.photoURL||void 0,phoneNumber:this.phoneNumber||void 0,tenantId:this.tenantId||void 0,providerData:this.providerData.map(e=>Object.assign({},e)),stsTokenManager:this.stsTokenManager.toJSON(),_redirectEventId:this._redirectEventId},this.metadata.toJSON()),{apiKey:this.auth.config.apiKey,appName:this.auth.name})}get refreshToken(){return this.stsTokenManager.refreshToken||""}static _fromJSON(e,t){var r=null!==(a=t.displayName)&&void 0!==a?a:void 0,n=null!==(m=t.email)&&void 0!==m?m:void 0,i=null!==(o=t.phoneNumber)&&void 0!==o?o:void 0,s=null!==(l=t.photoURL)&&void 0!==l?l:void 0,a=null!==(c=t.tenantId)&&void 0!==c?c:void 0,o=null!==(m=t._redirectEventId)&&void 0!==m?m:void 0,c=null!==(l=t.createdAt)&&void 0!==l?l:void 0,l=null!==(m=t.lastLoginAt)&&void 0!==m?m:void 0;const{uid:u,emailVerified:d,isAnonymous:h,providerData:p,stsTokenManager:f}=t;G(u&&f,e,"internal-error");var m=be.fromJSON(this.name,f);G("string"==typeof u,e,"internal-error"),Ee(r,e.name),Ee(n,e.name),G("boolean"==typeof d,e,"internal-error"),G("boolean"==typeof h,e,"internal-error"),Ee(i,e.name),Ee(s,e.name),Ee(a,e.name),Ee(o,e.name),Ee(c,e.name),Ee(l,e.name);const v=new ke({uid:u,auth:e,email:n,emailVerified:d,displayName:r,isAnonymous:h,photoURL:s,phoneNumber:i,tenantId:a,stsTokenManager:m,createdAt:c,lastLoginAt:l});return p&&Array.isArray(p)&&(v.providerData=p.map(e=>Object.assign({},e))),o&&(v._redirectEventId=o),v}static async _fromIdTokenResponse(e,t,r=!1){const n=new be;n.updateFromServerResponse(t);var i=new ke({uid:t.localId,auth:e,stsTokenManager:n,isAnonymous:r});return await we(i),i}static async _fromGetAccountInfoResponse(e,t,r){var n=t.users[0];G(void 0!==n.localId,"internal-error");var i=void 0!==n.providerUserInfo?Te(n.providerUserInfo):[],s=!(n.email&&n.passwordHash||null!=i&&i.length);const a=new be;a.updateFromIdToken(r);s=new ke({uid:n.localId,auth:e,stsTokenManager:a,isAnonymous:s}),i={uid:n.localId,displayName:n.displayName||null,photoURL:n.photoUrl||null,email:n.email||null,emailVerified:n.emailVerified||!1,phoneNumber:n.phoneNumber||null,tenantId:n.tenantId||null,providerData:i,metadata:new Ie(n.createdAt,n.lastLoginAt),isAnonymous:!(n.email&&n.passwordHash||null!=i&&i.length)};return Object.assign(s,i),s}}const Se=new Map;function Ae(e){$(e instanceof Function,"Expected a class definition");let t=Se.get(e);return t?$(t instanceof e,"Instance stored in cache mismatched with class"):(t=new e,Se.set(e,t)),t}class Re{constructor(){this.type="NONE",this.storage={}}async _isAvailable(){return!0}async _set(e,t){this.storage[e]=t}async _get(e){var t=this.storage[e];return void 0===t?null:t}async _remove(e){delete this.storage[e]}_addListener(e,t){}_removeListener(e,t){}}Re.type="NONE";const Pe=Re;function Ce(e,t,r){return`firebase:${e}:${t}:${r}`}class Oe{constructor(e,t,r){this.persistence=e,this.auth=t,this.userKey=r;var{config:n,name:i}=this.auth;this.fullUserKey=Ce(this.userKey,n.apiKey,i),this.fullPersistenceKey=Ce("persistence",n.apiKey,i),this.boundEventHandler=t._onStorageEvent.bind(t),this.persistence._addListener(this.fullUserKey,this.boundEventHandler)}setCurrentUser(e){return this.persistence._set(this.fullUserKey,e.toJSON())}async getCurrentUser(){var e=await this.persistence._get(this.fullUserKey);return e?ke._fromJSON(this.auth,e):null}removeCurrentUser(){return this.persistence._remove(this.fullUserKey)}savePersistenceForRedirect(){return this.persistence._set(this.fullPersistenceKey,this.persistence.type)}async setPersistence(e){if(this.persistence!==e){var t=await this.getCurrentUser();return await this.removeCurrentUser(),this.persistence=e,t?this.setCurrentUser(t):void 0}}delete(){this.persistence._removeListener(this.fullUserKey,this.boundEventHandler)}static async create(e,t,r="authUser"){if(!t.length)return new Oe(Ae(Pe),e,r);const n=(await Promise.all(t.map(async e=>{if(await e._isAvailable())return e}))).filter(e=>e);let i=n[0]||Ae(Pe);const s=Ce(r,e.config.apiKey,e.name);let a=null;for(const u of t)try{var o=await u._get(s);if(o){var c=ke._fromJSON(e,o);u!==i&&(a=c),i=u;break}}catch(e){}var l=n.filter(e=>e._shouldAllowMigration);return i._shouldAllowMigration&&l.length&&(i=l[0],a&&await i._set(s,a.toJSON()),await Promise.all(t.map(async e=>{if(e!==i)try{await e._remove(s)}catch(e){}}))),new Oe(i,e,r)}}function Ne(e){const t=e.toLowerCase();if(t.includes("opera/")||t.includes("opr/")||t.includes("opios/"))return"Opera";if(Me(t))return"IEMobile";if(t.includes("msie")||t.includes("trident/"))return"IE";if(t.includes("edge/"))return"Edge";if(Le(t))return"Firefox";if(t.includes("silk/"))return"Silk";if(Ve(t))return"Blackberry";if(xe(t))return"Webos";if(De(t))return"Safari";if((t.includes("chrome/")||Ue(t))&&!t.includes("edge/"))return"Chrome";if(Fe(t))return"Android";var r=e.match(/([a-zA-Z\d\.]+)\/[a-zA-Z\d\.]*$/);return 2===(null==r?void 0:r.length)?r[1]:"Other"}function Le(e=d()){return/firefox\//i.test(e)}function De(e=d()){const t=e.toLowerCase();return t.includes("safari/")&&!t.includes("chrome/")&&!t.includes("crios/")&&!t.includes("android")}function Ue(e=d()){return/crios\//i.test(e)}function Me(e=d()){return/iemobile/i.test(e)}function Fe(e=d()){return/android/i.test(e)}function Ve(e=d()){return/blackberry/i.test(e)}function xe(e=d()){return/webos/i.test(e)}function je(e=d()){return/iphone|ipad|ipod/i.test(e)||/macintosh/i.test(e)&&/mobile/i.test(e)}function He(e=d()){return je(e)||Fe(e)||xe(e)||Ve(e)||/windows phone/i.test(e)||Me(e)}function We(e,t=[]){let r;switch(e){case"Browser":r=Ne(d());break;case"Worker":r=`${Ne(d())}-${e}`;break;default:r=e}var n=t.length?t.join(","):"FirebaseCore-web";return`${r}/JsCore/${Oi.SDK_VERSION}/${n}`}class qe{constructor(e){this.auth=e,this.queue=[]}pushCallback(n,e){var t=r=>new Promise((e,t)=>{try{e(n(r))}catch(e){t(e)}});t.onAbort=e,this.queue.push(t);const r=this.queue.length-1;return()=>{this.queue[r]=()=>Promise.resolve()}}async runMiddleware(e){if(this.auth.currentUser!==e){const t=[];try{for(const r of this.queue)await r(e),r.onAbort&&t.push(r.onAbort)}catch(e){t.reverse();for(const n of t)try{n()}catch(e){}throw this.auth._errorFactory.create("login-blocked",{originalMessage:null==e?void 0:e.message})}}}}class ze{constructor(e){var t,r=e.customStrengthOptions;this.customStrengthOptions={},this.customStrengthOptions.minPasswordLength=null!==(t=r.minPasswordLength)&&void 0!==t?t:6,r.maxPasswordLength&&(this.customStrengthOptions.maxPasswordLength=r.maxPasswordLength),void 0!==r.containsLowercaseCharacter&&(this.customStrengthOptions.containsLowercaseLetter=r.containsLowercaseCharacter),void 0!==r.containsUppercaseCharacter&&(this.customStrengthOptions.containsUppercaseLetter=r.containsUppercaseCharacter),void 0!==r.containsNumericCharacter&&(this.customStrengthOptions.containsNumericCharacter=r.containsNumericCharacter),void 0!==r.containsNonAlphanumericCharacter&&(this.customStrengthOptions.containsNonAlphanumericCharacter=r.containsNonAlphanumericCharacter),this.enforcementState=e.enforcementState,"ENFORCEMENT_STATE_UNSPECIFIED"===this.enforcementState&&(this.enforcementState="OFF"),this.allowedNonAlphanumericCharacters=null!==(r=null===(r=e.allowedNonAlphanumericCharacters)||void 0===r?void 0:r.join(""))&&void 0!==r?r:"",this.forceUpgradeOnSignin=null!==(r=e.forceUpgradeOnSignin)&&void 0!==r&&r,this.schemaVersion=e.schemaVersion}validatePassword(e){var t,r,n;const i={isValid:!0,passwordPolicy:this};return this.validatePasswordLengthOptions(e,i),this.validatePasswordCharacterOptions(e,i),i.isValid&&(i.isValid=null===(t=i.meetsMinPasswordLength)||void 0===t||t),i.isValid&&(i.isValid=null===(t=i.meetsMaxPasswordLength)||void 0===t||t),i.isValid&&(i.isValid=null===(r=i.containsLowercaseLetter)||void 0===r||r),i.isValid&&(i.isValid=null===(r=i.containsUppercaseLetter)||void 0===r||r),i.isValid&&(i.isValid=null===(n=i.containsNumericCharacter)||void 0===n||n),i.isValid&&(i.isValid=null===(n=i.containsNonAlphanumericCharacter)||void 0===n||n),i}validatePasswordLengthOptions(e,t){var r=this.customStrengthOptions.minPasswordLength,n=this.customStrengthOptions.maxPasswordLength;r&&(t.meetsMinPasswordLength=e.length>=r),n&&(t.meetsMaxPasswordLength=e.length<=n)}validatePasswordCharacterOptions(e,t){var r;this.updatePasswordCharacterOptionsStatuses(t,!1,!1,!1,!1);for(let n=0;n<e.length;n++)r=e.charAt(n),this.updatePasswordCharacterOptionsStatuses(t,"a"<=r&&r<="z","A"<=r&&r<="Z","0"<=r&&r<="9",this.allowedNonAlphanumericCharacters.includes(r))}updatePasswordCharacterOptionsStatuses(e,t,r,n,i){this.customStrengthOptions.containsLowercaseLetter&&(e.containsLowercaseLetter||(e.containsLowercaseLetter=t)),this.customStrengthOptions.containsUppercaseLetter&&(e.containsUppercaseLetter||(e.containsUppercaseLetter=r)),this.customStrengthOptions.containsNumericCharacter&&(e.containsNumericCharacter||(e.containsNumericCharacter=n)),this.customStrengthOptions.containsNonAlphanumericCharacter&&(e.containsNonAlphanumericCharacter||(e.containsNonAlphanumericCharacter=i))}}class Be{constructor(e,t,r,n){this.app=e,this.heartbeatServiceProvider=t,this.appCheckServiceProvider=r,this.config=n,this.currentUser=null,this.emulatorConfig=null,this.operations=Promise.resolve(),this.authStateSubscription=new Ke(this),this.idTokenSubscription=new Ke(this),this.beforeStateQueue=new qe(this),this.redirectUser=null,this.isProactiveRefreshEnabled=!1,this.EXPECTED_PASSWORD_POLICY_SCHEMA_VERSION=1,this._canInitEmulator=!0,this._isInitialized=!1,this._deleted=!1,this._initializationPromise=null,this._popupRedirectResolver=null,this._errorFactory=F,this._agentRecaptchaConfig=null,this._tenantRecaptchaConfigs={},this._projectPasswordPolicy=null,this._tenantPasswordPolicies={},this.lastNotifiedUid=void 0,this.languageCode=null,this.tenantId=null,this.settings={appVerificationDisabledForTesting:!1},this.frameworks=[],this.name=e.name,this.clientVersion=n.sdkClientVersion}_initializeWithPersistence(t,r){return r&&(this._popupRedirectResolver=Ae(r)),this._initializationPromise=this.queue(async()=>{var e;if(!this._deleted&&(this.persistenceManager=await Oe.create(this,t),!this._deleted)){if(null!==(e=this._popupRedirectResolver)&&void 0!==e&&e._shouldInitProactively)try{await this._popupRedirectResolver._initialize(this)}catch(e){}await this.initializeCurrentUser(r),this.lastNotifiedUid=(null===(e=this.currentUser)||void 0===e?void 0:e.uid)||null,this._deleted||(this._isInitialized=!0)}}),this._initializationPromise}async _onStorageEvent(){if(!this._deleted){var e=await this.assertedPersistence.getCurrentUser();if(this.currentUser||e)return this.currentUser&&e&&this.currentUser.uid===e.uid?(this._currentUser._assign(e),void await this.currentUser.getIdToken()):void await this._updateCurrentUser(e,!0)}}async initializeCurrentUserFromIdToken(e){try{var t=await pe(this,{idToken:e}),r=await ke._fromGetAccountInfoResponse(this,t,e);await this.directlySetCurrentUser(r)}catch(e){console.warn("FirebaseServerApp could not login user with provided authIdToken: ",e),await this.directlySetCurrentUser(null)}}async initializeCurrentUser(e){if(Oi._isFirebaseServerApp(this.app)){const o=this.app.settings.authIdToken;return o?new Promise(e=>{setTimeout(()=>this.initializeCurrentUserFromIdToken(o).then(e,e))}):this.directlySetCurrentUser(null)}var t,r,n,i=await this.assertedPersistence.getCurrentUser();let s=i,a=!1;if(e&&this.config.authDomain&&(await this.getOrInitRedirectPersistenceManager(),t=null===(n=this.redirectUser)||void 0===n?void 0:n._redirectEventId,r=null===s||void 0===s?void 0:s._redirectEventId,n=await this.tryRedirectSignIn(e),t&&t!==r||null==n||!n.user||(s=n.user,a=!0)),!s)return this.directlySetCurrentUser(null);if(s._redirectEventId)return G(this._popupRedirectResolver,this,"argument-error"),await this.getOrInitRedirectPersistenceManager(),this.redirectUser&&this.redirectUser._redirectEventId===s._redirectEventId?this.directlySetCurrentUser(s):this.reloadAndSetCurrentUserOrClear(s);if(a)try{await this.beforeStateQueue.runMiddleware(s)}catch(e){s=i,this._popupRedirectResolver._overrideRedirectResult(this,()=>Promise.reject(e))}return s?this.reloadAndSetCurrentUserOrClear(s):this.directlySetCurrentUser(null)}async tryRedirectSignIn(e){let t=null;try{t=await this._popupRedirectResolver._completeRedirectFn(this,e,!0)}catch(e){await this._setRedirectUser(null)}return t}async reloadAndSetCurrentUserOrClear(e){try{await we(e)}catch(e){if("auth/network-request-failed"!==(null==e?void 0:e.code))return this.directlySetCurrentUser(null)}return this.directlySetCurrentUser(e)}useDeviceLanguage(){this.languageCode=function(){if("undefined"==typeof navigator)return null;var e=navigator;return e.languages&&e.languages[0]||e.language||null}()}async _delete(){this._deleted=!0}async updateCurrentUser(e){if(Oi._isFirebaseServerApp(this.app))return Promise.reject(q(this));const t=e?k(e):null;return t&&G(t.auth.config.apiKey===this.config.apiKey,this,"invalid-user-token"),this._updateCurrentUser(t&&t._clone(this))}async _updateCurrentUser(e,t=!1){if(!this._deleted)return e&&G(this.tenantId===e.tenantId,this,"tenant-id-mismatch"),t||await this.beforeStateQueue.runMiddleware(e),this.queue(async()=>{await this.directlySetCurrentUser(e),this.notifyAuthListeners()})}async signOut(){return Oi._isFirebaseServerApp(this.app)?Promise.reject(q(this)):(await this.beforeStateQueue.runMiddleware(null),(this.redirectPersistenceManager||this._popupRedirectResolver)&&await this._setRedirectUser(null),this._updateCurrentUser(null,!0))}setPersistence(e){return Oi._isFirebaseServerApp(this.app)?Promise.reject(q(this)):this.queue(async()=>{await this.assertedPersistence.setPersistence(Ae(e))})}_getRecaptchaConfig(){return null==this.tenantId?this._agentRecaptchaConfig:this._tenantRecaptchaConfigs[this.tenantId]}async validatePassword(e){this._getPasswordPolicyInternal()||await this._updatePasswordPolicy();const t=this._getPasswordPolicyInternal();return t.schemaVersion!==this.EXPECTED_PASSWORD_POLICY_SCHEMA_VERSION?Promise.reject(this._errorFactory.create("unsupported-password-policy-schema-version",{})):t.validatePassword(e)}_getPasswordPolicyInternal(){return null===this.tenantId?this._projectPasswordPolicy:this._tenantPasswordPolicies[this.tenantId]}async _updatePasswordPolicy(){var e,t=await ie(e=this,"GET","/v2/passwordPolicy",ne(e,{})),t=new ze(t);null===this.tenantId?this._projectPasswordPolicy=t:this._tenantPasswordPolicies[this.tenantId]=t}_getPersistence(){return this.assertedPersistence.persistence.type}_updateErrorMap(e){this._errorFactory=new _("auth","Firebase",e())}onAuthStateChanged(e,t,r){return this.registerStateListener(this.authStateSubscription,e,t,r)}beforeAuthStateChanged(e,t){return this.beforeStateQueue.pushCallback(e,t)}onIdTokenChanged(e,t,r){return this.registerStateListener(this.idTokenSubscription,e,t,r)}authStateReady(){return new Promise((e,t)=>{if(this.currentUser)e();else{const r=this.onAuthStateChanged(()=>{r(),e()},t)}})}async revokeAccessToken(e){if(this.currentUser){const r={providerId:"apple.com",tokenType:"ACCESS_TOKEN",token:e,idToken:await this.currentUser.getIdToken()};null!=this.tenantId&&(r.tenantId=this.tenantId),t=this,e=r,await ie(t,"POST","/v2/accounts:revokeToken",ne(t,e))}var t}toJSON(){var e;return{apiKey:this.config.apiKey,authDomain:this.config.authDomain,appName:this.name,currentUser:null===(e=this._currentUser)||void 0===e?void 0:e.toJSON()}}async _setRedirectUser(e,t){const r=await this.getOrInitRedirectPersistenceManager(t);return null===e?r.removeCurrentUser():r.setCurrentUser(e)}async getOrInitRedirectPersistenceManager(e){var t;return this.redirectPersistenceManager||(G(t=e&&Ae(e)||this._popupRedirectResolver,this,"argument-error"),this.redirectPersistenceManager=await Oe.create(this,[Ae(t._redirectPersistence)],"redirectUser"),this.redirectUser=await this.redirectPersistenceManager.getCurrentUser()),this.redirectPersistenceManager}async _redirectUserForId(e){var t;return this._isInitialized&&await this.queue(async()=>{}),(null===(t=this._currentUser)||void 0===t?void 0:t._redirectEventId)===e?this._currentUser:(null===(t=this.redirectUser)||void 0===t?void 0:t._redirectEventId)===e?this.redirectUser:null}async _persistUserIfCurrent(e){if(e===this.currentUser)return this.queue(async()=>this.directlySetCurrentUser(e))}_notifyListenersIfCurrent(e){e===this.currentUser&&this.notifyAuthListeners()}_key(){return`${this.config.authDomain}:${this.config.apiKey}:${this.name}`}_startProactiveRefresh(){this.isProactiveRefreshEnabled=!0,this.currentUser&&this._currentUser._startProactiveRefresh()}_stopProactiveRefresh(){this.isProactiveRefreshEnabled=!1,this.currentUser&&this._currentUser._stopProactiveRefresh()}get _currentUser(){return this.currentUser}notifyAuthListeners(){var e;this._isInitialized&&(this.idTokenSubscription.next(this.currentUser),e=null!==(e=null===(e=this.currentUser)||void 0===e?void 0:e.uid)&&void 0!==e?e:null,this.lastNotifiedUid!==e&&(this.lastNotifiedUid=e,this.authStateSubscription.next(this.currentUser)))}registerStateListener(e,t,r,n){if(this._deleted)return()=>{};const i="function"==typeof t?t:t.next.bind(t);let s=!1;const a=this._isInitialized?Promise.resolve():this._initializationPromise;if(G(a,this,"internal-error"),a.then(()=>{s||i(this.currentUser)}),"function"==typeof t){const o=e.addObserver(t,r,n);return()=>{s=!0,o()}}{const c=e.addObserver(t);return()=>{s=!0,c()}}}async directlySetCurrentUser(e){this.currentUser&&this.currentUser!==e&&this._currentUser._stopProactiveRefresh(),e&&this.isProactiveRefreshEnabled&&e._startProactiveRefresh(),(this.currentUser=e)?await this.assertedPersistence.setCurrentUser(e):await this.assertedPersistence.removeCurrentUser()}queue(e){return this.operations=this.operations.then(e,e),this.operations}get assertedPersistence(){return G(this.persistenceManager,this,"internal-error"),this.persistenceManager}_logFramework(e){e&&!this.frameworks.includes(e)&&(this.frameworks.push(e),this.frameworks.sort(),this.clientVersion=We(this.config.clientPlatform,this._getFrameworks()))}_getFrameworks(){return this.frameworks}async _getAdditionalHeaders(){const e={"X-Client-Version":this.clientVersion};this.app.options.appId&&(e["X-Firebase-gmpid"]=this.app.options.appId);var t=await(null===(t=this.heartbeatServiceProvider.getImmediate({optional:!0}))||void 0===t?void 0:t.getHeartbeatsHeader());t&&(e["X-Firebase-Client"]=t);t=await this._getAppCheckToken();return t&&(e["X-Firebase-AppCheck"]=t),e}async _getAppCheckToken(){var e,t,r=await(null===(r=this.appCheckServiceProvider.getImmediate({optional:!0}))||void 0===r?void 0:r.getToken());return null!=r&&r.error&&(e=`Error while retrieving App Check token: ${r.error}`,t=[],V.logLevel<=c.WARN&&V.warn(`Auth (${Oi.SDK_VERSION}): ${e}`,...t)),null==r?void 0:r.token}}function Ge(e){return k(e)}class Ke{constructor(e){this.auth=e,this.observer=null,this.addObserver=function(e,t){const r=new b(e,t);return r.subscribe.bind(r)}(e=>this.observer=e)}get next(){return G(this.observer,this.auth,"internal-error"),this.observer.next.bind(this.observer)}}let $e={async loadJS(){throw new Error("Unable to load external scripts")},recaptchaV2Script:"",recaptchaEnterpriseScript:"",gapiScript:""};function Je(e){return $e.loadJS(e)}function Ye(e){return`__${e}${Math.floor(1e6*Math.random())}`}class Xe{constructor(e){this.type="recaptcha-enterprise",this.auth=Ge(e)}async verify(i="verify",e=!1){async function t(i){if(!e){if(null==i.tenantId&&null!=i._agentRecaptchaConfig)return i._agentRecaptchaConfig.siteKey;if(null!=i.tenantId&&void 0!==i._tenantRecaptchaConfigs[i.tenantId])return i._tenantRecaptchaConfigs[i.tenantId].siteKey}return new Promise(async(r,n)=>{!async function(e,t){return ie(e,"GET","/v2/recaptchaConfig",ne(e,t))}(i,{clientType:"CLIENT_TYPE_WEB",version:"RECAPTCHA_ENTERPRISE"}).then(e=>{if(void 0!==e.recaptchaKey){var t=new he(e);return null==i.tenantId?i._agentRecaptchaConfig=t:i._tenantRecaptchaConfigs[i.tenantId]=t,r(t.siteKey)}n(new Error("recaptcha Enterprise site key undefined"))}).catch(e=>{n(e)})})}function s(e,t,r){const n=window.grecaptcha;de(n)?n.enterprise.ready(()=>{n.enterprise.execute(e,{action:i}).then(e=>{t(e)}).catch(()=>{t("NO_RECAPTCHA")})}):r(Error("No reCAPTCHA enterprise script loaded."))}return new Promise((r,n)=>{t(this.auth).then(t=>{if(!e&&de(window.grecaptcha))s(t,r,n);else if("undefined"!=typeof window){let e=$e.recaptchaEnterpriseScript;0!==e.length&&(e+=t),Je(e).then(()=>{s(t,r,n)}).catch(e=>{n(e)})}else n(new Error("RecaptchaVerifier is only supported in browser"))}).catch(e=>{n(e)})})}}async function Qe(e,t,r,n=!1){const i=new Xe(e);let s;try{s=await i.verify(r)}catch(e){s=await i.verify(r,!0)}var a=Object.assign({},t);return n?Object.assign(a,{captchaResp:s}):Object.assign(a,{captchaResponse:s}),Object.assign(a,{clientType:"CLIENT_TYPE_WEB"}),Object.assign(a,{recaptchaVersion:"RECAPTCHA_ENTERPRISE"}),a}async function Ze(r,n,i,s){if(null!==(e=r._getRecaptchaConfig())&&void 0!==e&&e.isProviderEnabled("EMAIL_PASSWORD_PROVIDER")){var e=await Qe(r,n,i,"getOobCode"===i);return s(r,e)}return s(r,n).catch(async e=>{if("auth/missing-recaptcha-token"!==e.code)return Promise.reject(e);console.log(`${i} is protected by reCAPTCHA Enterprise for this project. Automatically triggering the reCAPTCHA flow and restarting the flow.`);var t=await Qe(r,n,i,"getOobCode"===i);return s(r,t)})}function et(e,t,r){const n=Ge(e);G(n._canInitEmulator,n,"emulator-config-failed"),G(/^https?:\/\//.test(t),n,"invalid-emulator-scheme");var i=!(null==r||!r.disableWarnings);const s=tt(t);var{host:a,port:o}=function(e){const t=tt(e),r=/(\/\/)?([^?#/]+)/.exec(e.substr(t.length));if(!r)return{host:"",port:null};const n=r[2].split("@").pop()||"",i=/^(\[[^\]]+\])(:|$)/.exec(n);{if(i){var s=i[1];return{host:s,port:rt(n.substr(s.length+1))}}var[a,s]=n.split(":");return{host:a,port:rt(s)}}}(t);n.config.emulator={url:`${s}//${a}${null===o?"":`:${o}`}/`},n.settings.appVerificationDisabledForTesting=!0,n.emulatorConfig=Object.freeze({host:a,port:o,protocol:s.replace(":",""),options:Object.freeze({disableWarnings:i})}),i||function(){function e(){const e=document.createElement("p"),t=e.style;e.innerText="Running in emulator mode. Do not use with production credentials.",t.position="fixed",t.width="100%",t.backgroundColor="#ffffff",t.border=".1em solid #000000",t.color="#b50000",t.bottom="0px",t.left="0px",t.margin="0px",t.zIndex="10000",t.textAlign="center",e.classList.add("firebase-emulator-warning"),document.body.appendChild(e)}"undefined"!=typeof console&&"function"==typeof console.info&&console.info("WARNING: You are using the Auth Emulator, which is intended for local testing only.  Do not use with production credentials.");"undefined"!=typeof window&&"undefined"!=typeof document&&("loading"===document.readyState?window.addEventListener("DOMContentLoaded",e):e())}()}function tt(e){var t=e.indexOf(":");return t<0?"":e.substr(0,t+1)}function rt(e){if(!e)return null;var t=Number(e);return isNaN(t)?null:t}class nt{constructor(e,t){this.providerId=e,this.signInMethod=t}toJSON(){return K("not implemented")}_getIdTokenResponse(e){return K("not implemented")}_linkToIdToken(e,t){return K("not implemented")}_getReauthenticationResolver(e){return K("not implemented")}}async function it(e,t){return ie(e,"POST","/v1/accounts:resetPassword",ne(e,t))}async function st(e,t){return ie(e,"POST","/v1/accounts:signUp",t)}async function at(e,t){return ae(e,"POST","/v1/accounts:signInWithPassword",ne(e,t))}async function ot(e,t){return ie(e,"POST","/v1/accounts:sendOobCode",ne(e,t))}async function ct(e,t){return ot(e,t)}async function lt(e,t){return ot(e,t)}class ut extends nt{constructor(e,t,r,n=null){super("password",r),this._email=e,this._password=t,this._tenantId=n}static _fromEmailAndPassword(e,t){return new ut(e,t,"password")}static _fromEmailAndCode(e,t,r=null){return new ut(e,t,"emailLink",r)}toJSON(){return{email:this._email,password:this._password,signInMethod:this.signInMethod,tenantId:this._tenantId}}static fromJSON(e){var t="string"==typeof e?JSON.parse(e):e;if(null!=t&&t.email&&null!=t&&t.password){if("password"===t.signInMethod)return this._fromEmailAndPassword(t.email,t.password);if("emailLink"===t.signInMethod)return this._fromEmailAndCode(t.email,t.password,t.tenantId)}return null}async _getIdTokenResponse(e){switch(this.signInMethod){case"password":return Ze(e,{returnSecureToken:!0,email:this._email,password:this._password,clientType:"CLIENT_TYPE_WEB"},"signInWithPassword",at);case"emailLink":return async function(e,t){return ae(e,"POST","/v1/accounts:signInWithEmailLink",ne(e,t))}(e,{email:this._email,oobCode:this._password});default:j(e,"internal-error")}}async _linkToIdToken(e,t){switch(this.signInMethod){case"password":return Ze(e,{idToken:t,returnSecureToken:!0,email:this._email,password:this._password,clientType:"CLIENT_TYPE_WEB"},"signUpPassword",st);case"emailLink":return async function(e,t){return ae(e,"POST","/v1/accounts:signInWithEmailLink",ne(e,t))}(e,{idToken:t,email:this._email,oobCode:this._password});default:j(e,"internal-error")}}_getReauthenticationResolver(e){return this._getIdTokenResponse(e)}}async function dt(e,t){return ae(e,"POST","/v1/accounts:signInWithIdp",ne(e,t))}class ht extends nt{constructor(){super(...arguments),this.pendingToken=null}static _fromParams(e){const t=new ht(e.providerId,e.signInMethod);return e.idToken||e.accessToken?(e.idToken&&(t.idToken=e.idToken),e.accessToken&&(t.accessToken=e.accessToken),e.nonce&&!e.pendingToken&&(t.nonce=e.nonce),e.pendingToken&&(t.pendingToken=e.pendingToken)):e.oauthToken&&e.oauthTokenSecret?(t.accessToken=e.oauthToken,t.secret=e.oauthTokenSecret):j("argument-error"),t}toJSON(){return{idToken:this.idToken,accessToken:this.accessToken,secret:this.secret,nonce:this.nonce,pendingToken:this.pendingToken,providerId:this.providerId,signInMethod:this.signInMethod}}static fromJSON(e){var t="string"==typeof e?JSON.parse(e):e,{providerId:r,signInMethod:n}=t,t=C(t,["providerId","signInMethod"]);if(!r||!n)return null;const i=new ht(r,n);return i.idToken=t.idToken||void 0,i.accessToken=t.accessToken||void 0,i.secret=t.secret,i.nonce=t.nonce,i.pendingToken=t.pendingToken||null,i}_getIdTokenResponse(e){return dt(e,this.buildRequest())}_linkToIdToken(e,t){const r=this.buildRequest();return r.idToken=t,dt(e,r)}_getReauthenticationResolver(e){const t=this.buildRequest();return t.autoCreate=!1,dt(e,t)}buildRequest(){const e={requestUri:"http://localhost",returnSecureToken:!0};if(this.pendingToken)e.pendingToken=this.pendingToken;else{const t={};this.idToken&&(t.id_token=this.idToken),this.accessToken&&(t.access_token=this.accessToken),this.secret&&(t.oauth_token_secret=this.secret),t.providerId=this.providerId,this.nonce&&!this.pendingToken&&(t.nonce=this.nonce),e.postBody=I(t)}return e}}const pt={USER_NOT_FOUND:"user-not-found"};class ft extends nt{constructor(e){super("phone","phone"),this.params=e}static _fromVerification(e,t){return new ft({verificationId:e,verificationCode:t})}static _fromTokenResponse(e,t){return new ft({phoneNumber:e,temporaryProof:t})}_getIdTokenResponse(e){return async function(e,t){return ae(e,"POST","/v1/accounts:signInWithPhoneNumber",ne(e,t))}(e,this._makeVerificationRequest())}_linkToIdToken(e,t){return async function(e,t){var r=await ae(e,"POST","/v1/accounts:signInWithPhoneNumber",ne(e,t));if(r.temporaryProof)throw le(e,"account-exists-with-different-credential",r);return r}(e,Object.assign({idToken:t},this._makeVerificationRequest()))}_getReauthenticationResolver(e){return async function(e,t){return ae(e,"POST","/v1/accounts:signInWithPhoneNumber",ne(e,Object.assign(Object.assign({},t),{operation:"REAUTH"})),pt)}(e,this._makeVerificationRequest())}_makeVerificationRequest(){var{temporaryProof:e,phoneNumber:t,verificationId:r,verificationCode:n}=this.params;return e&&t?{temporaryProof:e,phoneNumber:t}:{sessionInfo:r,code:n}}toJSON(){const e={providerId:this.providerId};return this.params.phoneNumber&&(e.phoneNumber=this.params.phoneNumber),this.params.temporaryProof&&(e.temporaryProof=this.params.temporaryProof),this.params.verificationCode&&(e.verificationCode=this.params.verificationCode),this.params.verificationId&&(e.verificationId=this.params.verificationId),e}static fromJSON(e){var{verificationId:t,verificationCode:r,phoneNumber:n,temporaryProof:i}=e="string"==typeof e?JSON.parse(e):e;return r||t||n||i?new ft({verificationId:t,verificationCode:r,phoneNumber:n,temporaryProof:i}):null}}class mt{constructor(e){var t=w(T(e)),r=null!==(n=t.apiKey)&&void 0!==n?n:null,n=null!==(i=t.oobCode)&&void 0!==i?i:null,i=function(e){switch(e){case"recoverEmail":return"RECOVER_EMAIL";case"resetPassword":return"PASSWORD_RESET";case"signIn":return"EMAIL_SIGNIN";case"verifyEmail":return"VERIFY_EMAIL";case"verifyAndChangeEmail":return"VERIFY_AND_CHANGE_EMAIL";case"revertSecondFactorAddition":return"REVERT_SECOND_FACTOR_ADDITION";default:return null}}(null!==(i=t.mode)&&void 0!==i?i:null);G(r&&n&&i,"argument-error"),this.apiKey=r,this.operation=i,this.code=n,this.continueUrl=null!==(n=t.continueUrl)&&void 0!==n?n:null,this.languageCode=null!==(n=t.languageCode)&&void 0!==n?n:null,this.tenantId=null!==(t=t.tenantId)&&void 0!==t?t:null}static parseLink(e){var t,r,n,t=(t=w(T(e=e)).link,r=t?w(T(t)).deep_link_id:null,((n=w(T(e)).deep_link_id)?w(T(n)).link:null)||n||r||t||e);try{return new mt(t)}catch(e){return null}}}class vt{constructor(){this.providerId=vt.PROVIDER_ID}static credential(e,t){return ut._fromEmailAndPassword(e,t)}static credentialWithLink(e,t){var r=mt.parseLink(t);return G(r,"argument-error"),ut._fromEmailAndCode(e,r.code,r.tenantId)}}vt.PROVIDER_ID="password",vt.EMAIL_PASSWORD_SIGN_IN_METHOD="password",vt.EMAIL_LINK_SIGN_IN_METHOD="emailLink";class gt{constructor(e){this.providerId=e,this.defaultLanguageCode=null,this.customParameters={}}setDefaultLanguage(e){this.defaultLanguageCode=e}setCustomParameters(e){return this.customParameters=e,this}getCustomParameters(){return this.customParameters}}class _t extends gt{constructor(){super(...arguments),this.scopes=[]}addScope(e){return this.scopes.includes(e)||this.scopes.push(e),this}getScopes(){return[...this.scopes]}}class yt extends _t{static credentialFromJSON(e){var t="string"==typeof e?JSON.parse(e):e;return G("providerId"in t&&"signInMethod"in t,"argument-error"),ht._fromParams(t)}credential(e){return this._credential(Object.assign(Object.assign({},e),{nonce:e.rawNonce}))}_credential(e){return G(e.idToken||e.accessToken,"argument-error"),ht._fromParams(Object.assign(Object.assign({},e),{providerId:this.providerId,signInMethod:this.providerId}))}static credentialFromResult(e){return yt.oauthCredentialFromTaggedObject(e)}static credentialFromError(e){return yt.oauthCredentialFromTaggedObject(e.customData||{})}static oauthCredentialFromTaggedObject({_tokenResponse:e}){if(!e)return null;var{oauthIdToken:t,oauthAccessToken:r,oauthTokenSecret:n,pendingToken:i,nonce:s,providerId:a}=e;if(!(r||n||t||i))return null;if(!a)return null;try{return new yt(a)._credential({idToken:t,accessToken:r,nonce:s,pendingToken:i})}catch(e){return null}}}class It extends _t{constructor(){super("facebook.com")}static credential(e){return ht._fromParams({providerId:It.PROVIDER_ID,signInMethod:It.FACEBOOK_SIGN_IN_METHOD,accessToken:e})}static credentialFromResult(e){return It.credentialFromTaggedObject(e)}static credentialFromError(e){return It.credentialFromTaggedObject(e.customData||{})}static credentialFromTaggedObject({_tokenResponse:e}){if(!(e&&"oauthAccessToken"in e))return null;if(!e.oauthAccessToken)return null;try{return It.credential(e.oauthAccessToken)}catch(e){return null}}}It.FACEBOOK_SIGN_IN_METHOD="facebook.com",It.PROVIDER_ID="facebook.com";class wt extends _t{constructor(){super("google.com"),this.addScope("profile")}static credential(e,t){return ht._fromParams({providerId:wt.PROVIDER_ID,signInMethod:wt.GOOGLE_SIGN_IN_METHOD,idToken:e,accessToken:t})}static credentialFromResult(e){return wt.credentialFromTaggedObject(e)}static credentialFromError(e){return wt.credentialFromTaggedObject(e.customData||{})}static credentialFromTaggedObject({_tokenResponse:e}){if(!e)return null;var{oauthIdToken:t,oauthAccessToken:r}=e;if(!t&&!r)return null;try{return wt.credential(t,r)}catch(e){return null}}}wt.GOOGLE_SIGN_IN_METHOD="google.com",wt.PROVIDER_ID="google.com";class Tt extends _t{constructor(){super("github.com")}static credential(e){return ht._fromParams({providerId:Tt.PROVIDER_ID,signInMethod:Tt.GITHUB_SIGN_IN_METHOD,accessToken:e})}static credentialFromResult(e){return Tt.credentialFromTaggedObject(e)}static credentialFromError(e){return Tt.credentialFromTaggedObject(e.customData||{})}static credentialFromTaggedObject({_tokenResponse:e}){if(!(e&&"oauthAccessToken"in e))return null;if(!e.oauthAccessToken)return null;try{return Tt.credential(e.oauthAccessToken)}catch(e){return null}}}Tt.GITHUB_SIGN_IN_METHOD="github.com",Tt.PROVIDER_ID="github.com";class bt extends nt{constructor(e,t){super(e,e),this.pendingToken=t}_getIdTokenResponse(e){return dt(e,this.buildRequest())}_linkToIdToken(e,t){const r=this.buildRequest();return r.idToken=t,dt(e,r)}_getReauthenticationResolver(e){const t=this.buildRequest();return t.autoCreate=!1,dt(e,t)}toJSON(){return{signInMethod:this.signInMethod,providerId:this.providerId,pendingToken:this.pendingToken}}static fromJSON(e){var{providerId:t,signInMethod:r,pendingToken:n}="string"==typeof e?JSON.parse(e):e;return t&&r&&n&&t===r?new bt(t,n):null}static _create(e,t){return new bt(e,t)}buildRequest(){return{requestUri:"http://localhost",returnSecureToken:!0,pendingToken:this.pendingToken}}}class Et extends gt{constructor(e){G(e.startsWith("saml."),"argument-error"),super(e)}static credentialFromResult(e){return Et.samlCredentialFromTaggedObject(e)}static credentialFromError(e){return Et.samlCredentialFromTaggedObject(e.customData||{})}static credentialFromJSON(e){var t=bt.fromJSON(e);return G(t,"argument-error"),t}static samlCredentialFromTaggedObject({_tokenResponse:e}){if(!e)return null;var{pendingToken:t,providerId:r}=e;if(!t||!r)return null;try{return bt._create(r,t)}catch(e){return null}}}class kt extends _t{constructor(){super("twitter.com")}static credential(e,t){return ht._fromParams({providerId:kt.PROVIDER_ID,signInMethod:kt.TWITTER_SIGN_IN_METHOD,oauthToken:e,oauthTokenSecret:t})}static credentialFromResult(e){return kt.credentialFromTaggedObject(e)}static credentialFromError(e){return kt.credentialFromTaggedObject(e.customData||{})}static credentialFromTaggedObject({_tokenResponse:e}){if(!e)return null;var{oauthAccessToken:t,oauthTokenSecret:r}=e;if(!t||!r)return null;try{return kt.credential(t,r)}catch(e){return null}}}async function St(e,t){return ae(e,"POST","/v1/accounts:signUp",ne(e,t))}kt.TWITTER_SIGN_IN_METHOD="twitter.com",kt.PROVIDER_ID="twitter.com";class At{constructor(e){this.user=e.user,this.providerId=e.providerId,this._tokenResponse=e._tokenResponse,this.operationType=e.operationType}static async _fromIdTokenResponse(e,t,r,n=!1){var i=await ke._fromIdTokenResponse(e,r,n),s=Rt(r);return new At({user:i,providerId:s,_tokenResponse:r,operationType:t})}static async _forOperation(e,t,r){await e._updateTokensIfNecessary(r,!0);var n=Rt(r);return new At({user:e,providerId:n,_tokenResponse:r,operationType:t})}}function Rt(e){return e.providerId||("phoneNumber"in e?"phone":null)}class Pt extends g{constructor(e,t,r,n){var i;super(t.code,t.message),this.operationType=r,this.user=n,Object.setPrototypeOf(this,Pt.prototype),this.customData={appName:e.name,tenantId:null!==(i=e.tenantId)&&void 0!==i?i:void 0,_serverResponse:t.customData._serverResponse,operationType:r}}static _fromErrorAndOperation(e,t,r,n){return new Pt(e,t,r,n)}}function Ct(t,r,e,n){const i="reauthenticate"===r?e._getReauthenticationResolver(t):e._getIdTokenResponse(t);return i.catch(e=>{if("auth/multi-factor-auth-required"===e.code)throw Pt._fromErrorAndOperation(t,e,r,n);throw e})}function Ot(e){return new Set(e.map(({providerId:e})=>e).filter(e=>!!e))}async function Nt(e,t){const r=k(e);await Dt(!0,r,t);var n=(e=r.auth,t={idToken:await r.getIdToken(),deleteProvider:[t]},await ie(e,"POST","/v1/accounts:update",t))["providerUserInfo"];const i=Ot(n||[]);return r.providerData=r.providerData.filter(e=>i.has(e.providerId)),i.has("phone")||(r.phoneNumber=null),await r.auth._persistUserIfCurrent(r),r}async function Lt(e,t,r=!1){var n=await _e(e,t._linkToIdToken(e.auth,await e.getIdToken()),r);return At._forOperation(e,"link",n)}async function Dt(e,t,r){await we(t);const n=Ot(t.providerData);var i=!1===e?"provider-already-linked":"no-such-provider";G(n.has(r)===e,t.auth,i)}async function Ut(e,t,r=!1){var n=e["auth"];if(Oi._isFirebaseServerApp(n.app))return Promise.reject(q(n));var i="reauthenticate";try{var s=await _e(e,Ct(n,i,t,e),r);G(s.idToken,n,"internal-error");var a=ve(s.idToken);G(a,n,"internal-error");var o=a["sub"];return G(e.uid===o,n,"user-mismatch"),At._forOperation(e,i,s)}catch(e){throw"auth/user-not-found"===(null==e?void 0:e.code)&&j(n,"user-mismatch"),e}}async function Mt(e,t,r=!1){if(Oi._isFirebaseServerApp(e.app))return Promise.reject(q(e));var n=await Ct(e,"signIn",t),n=await At._fromIdTokenResponse(e,"signIn",n);return r||await e._updateCurrentUser(n.user),n}async function Ft(e,t){return Mt(Ge(e),t)}async function Vt(e,t){var r=k(e);return await Dt(!1,r,t.providerId),Lt(r,t)}async function xt(e,t){return Ut(k(e),t)}async function jt(e,t){if(Oi._isFirebaseServerApp(e.app))return Promise.reject(q(e));const r=Ge(e);var n=await ae(r,"POST","/v1/accounts:signInWithCustomToken",ne(r,{token:t,returnSecureToken:!0})),n=await At._fromIdTokenResponse(r,"signIn",n);return await r._updateCurrentUser(n.user),n}class Ht{constructor(e,t){this.factorId=e,this.uid=t.mfaEnrollmentId,this.enrollmentTime=new Date(t.enrolledAt).toUTCString(),this.displayName=t.displayName}static _fromServerResponse(e,t){return"phoneInfo"in t?Wt._fromServerResponse(e,t):"totpInfo"in t?qt._fromServerResponse(e,t):j(e,"internal-error")}}class Wt extends Ht{constructor(e){super("phone",e),this.phoneNumber=e.phoneInfo}static _fromServerResponse(e,t){return new Wt(t)}}class qt extends Ht{constructor(e){super("totp",e)}static _fromServerResponse(e,t){return new qt(t)}}function zt(e,t,r){var n;G(0<(null===(n=r.url)||void 0===n?void 0:n.length),e,"invalid-continue-uri"),G(void 0===r.dynamicLinkDomain||0<r.dynamicLinkDomain.length,e,"invalid-dynamic-link-domain"),t.continueUrl=r.url,t.dynamicLinkDomain=r.dynamicLinkDomain,t.canHandleCodeInApp=r.handleCodeInApp,r.iOS&&(G(0<r.iOS.bundleId.length,e,"missing-ios-bundle-id"),t.iOSBundleId=r.iOS.bundleId),r.android&&(G(0<r.android.packageName.length,e,"missing-android-pkg-name"),t.androidInstallApp=r.android.installApp,t.androidMinimumVersionCode=r.android.minimumVersion,t.androidPackageName=r.android.packageName)}async function Bt(e){const t=Ge(e);t._getPasswordPolicyInternal()&&await t._updatePasswordPolicy()}async function Gt(e,t){await ie(e=k(e),"POST","/v1/accounts:update",ne(e,{oobCode:t}))}async function Kt(e,t){var r=k(e),n=await it(r,{oobCode:t}),i=n.requestType;switch(G(i,r,"internal-error"),i){case"EMAIL_SIGNIN":break;case"VERIFY_AND_CHANGE_EMAIL":G(n.newEmail,r,"internal-error");break;case"REVERT_SECOND_FACTOR_ADDITION":G(n.mfaInfo,r,"internal-error");default:G(n.email,r,"internal-error")}let s=null;return n.mfaInfo&&(s=Ht._fromServerResponse(Ge(r),n.mfaInfo)),{data:{email:("VERIFY_AND_CHANGE_EMAIL"===n.requestType?n.newEmail:n.email)||null,previousEmail:("VERIFY_AND_CHANGE_EMAIL"===n.requestType?n.email:n.newEmail)||null,multiFactorInfo:s},operation:i}}async function $t(e,t){var r=Y()?J():"http://localhost",r=(await ie(e=k(e),"POST","/v1/accounts:createAuthUri",ne(e,{identifier:t,continueUri:r})))["signinMethods"];return r||[]}async function Jt(e,t){var r=k(e),n={requestType:"VERIFY_EMAIL",idToken:await e.getIdToken()};t&&zt(r.auth,n,t);var n=(await ot(r.auth,n))["email"];n!==e.email&&await e.reload()}async function Yt(e,t,r){var n=k(e),i={requestType:"VERIFY_AND_CHANGE_EMAIL",idToken:await e.getIdToken(),newEmail:t};r&&zt(n.auth,i,r);var i=(await ot(n.auth,i))["email"];i!==e.email&&await e.reload()}async function Xt(e,{displayName:t,photoURL:r}){if(void 0!==t||void 0!==r){const i=k(e);var n=await i.getIdToken(),n=await _e(i,async function(e,t){return ie(e,"POST","/v1/accounts:update",t)}(i.auth,{idToken:n,displayName:t,photoUrl:r,returnSecureToken:!0}));i.displayName=n.displayName||null,i.photoURL=n.photoUrl||null;const s=i.providerData.find(({providerId:e})=>"password"===e);s&&(s.displayName=i.displayName,s.photoURL=i.photoURL),await i._updateTokensIfNecessary(n)}}async function Qt(e,t,r){var n=e["auth"];const i={idToken:await e.getIdToken(),returnSecureToken:!0};t&&(i.email=t),r&&(i.password=r);n=await _e(e,async function(e,t){return ie(e,"POST","/v1/accounts:update",t)}(n,i));await e._updateTokensIfNecessary(n,!0)}class Zt{constructor(e,t,r={}){this.isNewUser=e,this.providerId=t,this.profile=r}}class er extends Zt{constructor(e,t,r,n){super(e,t,r),this.username=n}}class tr extends Zt{constructor(e,t){super(e,"facebook.com",t)}}class rr extends er{constructor(e,t){super(e,"github.com",t,"string"==typeof(null==t?void 0:t.login)?null==t?void 0:t.login:null)}}class nr extends Zt{constructor(e,t){super(e,"google.com",t)}}class ir extends er{constructor(e,t,r){super(e,"twitter.com",t,r)}}function sr(e){var{user:t,_tokenResponse:r}=e;return t.isAnonymous&&!r?{providerId:null,isNewUser:!1,profile:null}:function(e){if(!e)return null;var t=e["providerId"],r=e.rawUserInfo?JSON.parse(e.rawUserInfo):{},n=e.isNewUser||"identitytoolkit#SignupNewUserResponse"===e.kind;if(!t&&null!=e&&e.idToken){var i=null===(i=null===(i=ve(e.idToken))||void 0===i?void 0:i.firebase)||void 0===i?void 0:i.sign_in_provider;if(i){i="anonymous"!==i&&"custom"!==i?i:null;return new Zt(n,i)}}if(!t)return null;switch(t){case"facebook.com":return new tr(n,r);case"github.com":return new rr(n,r);case"google.com":return new nr(n,r);case"twitter.com":return new ir(n,r,e.screenName||null);case"custom":case"anonymous":return new Zt(n,null);default:return new Zt(n,t,r)}}(r)}class ar{constructor(e,t,r){this.type=e,this.credential=t,this.user=r}static _fromIdtoken(e,t){return new ar("enroll",e,t)}static _fromMfaPendingCredential(e){return new ar("signin",e)}toJSON(){return{multiFactorSession:{["enroll"===this.type?"idToken":"pendingCredential"]:this.credential}}}static fromJSON(e){var t;if(null!=e&&e.multiFactorSession){if(null!==(t=e.multiFactorSession)&&void 0!==t&&t.pendingCredential)return ar._fromMfaPendingCredential(e.multiFactorSession.pendingCredential);if(null!==(t=e.multiFactorSession)&&void 0!==t&&t.idToken)return ar._fromIdtoken(e.multiFactorSession.idToken)}return null}}class or{constructor(e,t,r){this.session=e,this.hints=t,this.signInResolver=r}static _fromError(e,i){const s=Ge(e),a=i.customData._serverResponse;var t=(a.mfaInfo||[]).map(e=>Ht._fromServerResponse(s,e));G(a.mfaPendingCredential,s,"internal-error");const o=ar._fromMfaPendingCredential(a.mfaPendingCredential);return new or(o,t,async e=>{var t=await e._process(s,o);delete a.mfaInfo,delete a.mfaPendingCredential;var r=Object.assign(Object.assign({},a),{idToken:t.idToken,refreshToken:t.refreshToken});switch(i.operationType){case"signIn":var n=await At._fromIdTokenResponse(s,i.operationType,r);return await s._updateCurrentUser(n.user),n;case"reauthenticate":return G(i.user,s,"internal-error"),At._forOperation(i.user,i.operationType,r);default:j(s,"internal-error")}})}async resolveSignIn(e){return this.signInResolver(e)}}class cr{constructor(t){this.user=t,this.enrolledFactors=[],t._onReload(e=>{e.mfaInfo&&(this.enrolledFactors=e.mfaInfo.map(e=>Ht._fromServerResponse(t.auth,e)))})}static _fromUser(e){return new cr(e)}async getSession(){return ar._fromIdtoken(await this.user.getIdToken(),this.user)}async enroll(e,t){const r=e;var n=await this.getSession(),n=await _e(this.user,r._process(this.user.auth,n,t));return await this.user._updateTokensIfNecessary(n),this.user.reload()}async unenroll(e){const t="string"==typeof e?e:e.uid;var r,n,i=await this.user.getIdToken();try{var s=await _e(this.user,(r=this.user.auth,n={idToken:i,mfaEnrollmentId:t},ie(r,"POST","/v2/accounts/mfaEnrollment:withdraw",ne(r,n))));this.enrolledFactors=this.enrolledFactors.filter(({uid:e})=>e!==t),await this.user._updateTokensIfNecessary(s),await this.user.reload()}catch(e){throw e}}}const lr=new WeakMap;const ur="__sak";class dr{constructor(e,t){this.storageRetriever=e,this.type=t}_isAvailable(){try{return this.storage?(this.storage.setItem(ur,"1"),this.storage.removeItem(ur),Promise.resolve(!0)):Promise.resolve(!1)}catch(e){return Promise.resolve(!1)}}_set(e,t){return this.storage.setItem(e,JSON.stringify(t)),Promise.resolve()}_get(e){var t=this.storage.getItem(e);return Promise.resolve(t?JSON.parse(t):null)}_remove(e){return this.storage.removeItem(e),Promise.resolve()}get storage(){return this.storageRetriever()}}class hr extends dr{constructor(){super(()=>window.localStorage,"LOCAL"),this.boundEventHandler=(e,t)=>this.onStorageEvent(e,t),this.listeners={},this.localCache={},this.pollTimer=null,this.fallbackToPolling=He(),this._shouldAllowMigration=!0}forAllChangedKeys(e){for(const n of Object.keys(this.listeners)){var t=this.storage.getItem(n),r=this.localCache[n];t!==r&&e(n,r,t)}}onStorageEvent(e,t=!1){if(e.key){const i=e.key;t?this.detachListener():this.stopPolling();var r=()=>{var e=this.storage.getItem(i);!t&&this.localCache[i]===e||this.notifyListeners(i,e)},n=this.storage.getItem(i);m()&&10===document.documentMode&&n!==e.newValue&&e.newValue!==e.oldValue?setTimeout(r,10):r()}else this.forAllChangedKeys((e,t,r)=>{this.notifyListeners(e,r)})}notifyListeners(e,t){this.localCache[e]=t;var r=this.listeners[e];if(r)for(const n of Array.from(r))n(t&&JSON.parse(t))}startPolling(){this.stopPolling(),this.pollTimer=setInterval(()=>{this.forAllChangedKeys((e,t,r)=>{this.onStorageEvent(new StorageEvent("storage",{key:e,oldValue:t,newValue:r}),!0)})},1e3)}stopPolling(){this.pollTimer&&(clearInterval(this.pollTimer),this.pollTimer=null)}attachListener(){window.addEventListener("storage",this.boundEventHandler)}detachListener(){window.removeEventListener("storage",this.boundEventHandler)}_addListener(e,t){0===Object.keys(this.listeners).length&&(this.fallbackToPolling?this.startPolling():this.attachListener()),this.listeners[e]||(this.listeners[e]=new Set,this.localCache[e]=this.storage.getItem(e)),this.listeners[e].add(t)}_removeListener(e,t){this.listeners[e]&&(this.listeners[e].delete(t),0===this.listeners[e].size&&delete this.listeners[e]),0===Object.keys(this.listeners).length&&(this.detachListener(),this.stopPolling())}async _set(e,t){await super._set(e,t),this.localCache[e]=JSON.stringify(t)}async _get(e){var t=await super._get(e);return this.localCache[e]=JSON.stringify(t),t}async _remove(e){await super._remove(e),delete this.localCache[e]}}hr.type="LOCAL";const pr=hr;class fr extends dr{constructor(){super(()=>window.sessionStorage,"SESSION")}_addListener(e,t){}_removeListener(e,t){}}fr.type="SESSION";const mr=fr;class vr{constructor(e){this.eventTarget=e,this.handlersMap={},this.boundEventHandler=this.handleEvent.bind(this)}static _getInstance(t){var e=this.receivers.find(e=>e.isListeningto(t));if(e)return e;e=new vr(t);return this.receivers.push(e),e}isListeningto(e){return this.eventTarget===e}async handleEvent(e){const t=e,{eventId:r,eventType:n,data:i}=t.data;var s=this.handlersMap[n];null!=s&&s.size&&(t.ports[0].postMessage({status:"ack",eventId:r,eventType:n}),s=Array.from(s).map(async e=>e(t.origin,i)),s=await Promise.all(s.map(async e=>{try{return{fulfilled:!0,value:await e}}catch(e){return{fulfilled:!1,reason:e}}})),t.ports[0].postMessage({status:"done",eventId:r,eventType:n,response:s}))}_subscribe(e,t){0===Object.keys(this.handlersMap).length&&this.eventTarget.addEventListener("message",this.boundEventHandler),this.handlersMap[e]||(this.handlersMap[e]=new Set),this.handlersMap[e].add(t)}_unsubscribe(e,t){this.handlersMap[e]&&t&&this.handlersMap[e].delete(t),t&&0!==this.handlersMap[e].size||delete this.handlersMap[e],0===Object.keys(this.handlersMap).length&&this.eventTarget.removeEventListener("message",this.boundEventHandler)}}function gr(e="",t=10){let r="";for(let n=0;n<t;n++)r+=Math.floor(10*Math.random());return e+r}vr.receivers=[];class _r{constructor(e){this.target=e,this.handlers=new Set}removeMessageHandler(e){e.messageChannel&&(e.messageChannel.port1.removeEventListener("message",e.onMessage),e.messageChannel.port1.close()),this.handlers.delete(e)}async _send(e,t,a=50){const o="undefined"!=typeof MessageChannel?new MessageChannel:null;if(!o)throw new Error("connection_unavailable");let c,l;return new Promise((r,n)=>{const i=gr("",20);o.port1.start();const s=setTimeout(()=>{n(new Error("unsupported_event"))},a);l={messageChannel:o,onMessage(e){var t=e;if(t.data.eventId===i)switch(t.data.status){case"ack":clearTimeout(s),c=setTimeout(()=>{n(new Error("timeout"))},3e3);break;case"done":clearTimeout(c),r(t.data.response);break;default:clearTimeout(s),clearTimeout(c),n(new Error("invalid_response"))}}},this.handlers.add(l),o.port1.addEventListener("message",l.onMessage),this.target.postMessage({eventType:e,eventId:i,data:t},[o.port2])}).finally(()=>{l&&this.removeMessageHandler(l)})}}function yr(){return window}function Ir(){return void 0!==yr().WorkerGlobalScope&&"function"==typeof yr().importScripts}const wr="firebaseLocalStorageDb",Tr="firebaseLocalStorage",br="fbase_key";class Er{constructor(e){this.request=e}toPromise(){return new Promise((e,t)=>{this.request.addEventListener("success",()=>{e(this.request.result)}),this.request.addEventListener("error",()=>{t(this.request.error)})})}}function kr(e,t){return e.transaction([Tr],t?"readwrite":"readonly").objectStore(Tr)}function Sr(){const n=indexedDB.open(wr,1);return new Promise((r,t)=>{n.addEventListener("error",()=>{t(n.error)}),n.addEventListener("upgradeneeded",()=>{const e=n.result;try{e.createObjectStore(Tr,{keyPath:br})}catch(e){t(e)}}),n.addEventListener("success",async()=>{const e=n.result;var t;e.objectStoreNames.contains(Tr)?r(e):(e.close(),t=indexedDB.deleteDatabase(wr),await new Er(t).toPromise(),r(await Sr()))})})}async function Ar(e,t,r){var n=kr(e,!0).put({fbase_key:t,value:r});return new Er(n).toPromise()}function Rr(e,t){var r=kr(e,!0).delete(t);return new Er(r).toPromise()}class Pr{constructor(){this.type="LOCAL",this._shouldAllowMigration=!0,this.listeners={},this.localCache={},this.pollTimer=null,this.pendingWrites=0,this.receiver=null,this.sender=null,this.serviceWorkerReceiverAvailable=!1,this.activeServiceWorker=null,this._workerInitializationPromise=this.initializeServiceWorkerMessaging().then(()=>{},()=>{})}async _openDb(){return this.db||(this.db=await Sr(),this.db)}async _withRetries(e){let t=0;for(;;)try{return e(await this._openDb())}catch(e){if(3<t++)throw e;this.db&&(this.db.close(),this.db=void 0)}}async initializeServiceWorkerMessaging(){return Ir()?this.initializeReceiver():this.initializeSender()}async initializeReceiver(){this.receiver=vr._getInstance(Ir()?self:null),this.receiver._subscribe("keyChanged",async(e,t)=>{const r=await this._poll();return{keyProcessed:r.includes(t.key)}}),this.receiver._subscribe("ping",async(e,t)=>["keyChanged"])}async initializeSender(){var e,t,r;this.activeServiceWorker=await async function(){if(null===navigator||void 0===navigator||!navigator.serviceWorker)return null;try{return(await navigator.serviceWorker.ready).active}catch(e){return null}}(),this.activeServiceWorker&&(this.sender=new _r(this.activeServiceWorker),(r=await this.sender._send("ping",{},800))&&null!==(e=r[0])&&void 0!==e&&e.fulfilled&&null!==(t=r[0])&&void 0!==t&&t.value.includes("keyChanged")&&(this.serviceWorkerReceiverAvailable=!0))}async notifyServiceWorker(e){var t;if(this.sender&&this.activeServiceWorker&&((null===(t=null===navigator||void 0===navigator?void 0:navigator.serviceWorker)||void 0===t?void 0:t.controller)||null)===this.activeServiceWorker)try{await this.sender._send("keyChanged",{key:e},this.serviceWorkerReceiverAvailable?800:50)}catch(e){}}async _isAvailable(){try{if(!indexedDB)return!1;var e=await Sr();return await Ar(e,ur,"1"),await Rr(e,ur),!0}catch(e){}return!1}async _withPendingWrite(e){this.pendingWrites++;try{await e()}finally{this.pendingWrites--}}async _set(t,r){return this._withPendingWrite(async()=>(await this._withRetries(e=>Ar(e,t,r)),this.localCache[t]=r,this.notifyServiceWorker(t)))}async _get(t){var e=await this._withRetries(e=>async function(e,t){var r=kr(e,!1).get(t);return void 0===(r=await new Er(r).toPromise())?null:r.value}(e,t));return this.localCache[t]=e}async _remove(t){return this._withPendingWrite(async()=>(await this._withRetries(e=>Rr(e,t)),delete this.localCache[t],this.notifyServiceWorker(t)))}async _poll(){var e=await this._withRetries(e=>{var t=kr(e,!1).getAll();return new Er(t).toPromise()});if(!e)return[];if(0!==this.pendingWrites)return[];const t=[],r=new Set;if(0!==e.length)for(var{fbase_key:n,value:i}of e)r.add(n),JSON.stringify(this.localCache[n])!==JSON.stringify(i)&&(this.notifyListeners(n,i),t.push(n));for(const s of Object.keys(this.localCache))this.localCache[s]&&!r.has(s)&&(this.notifyListeners(s,null),t.push(s));return t}notifyListeners(e,t){this.localCache[e]=t;var r=this.listeners[e];if(r)for(const n of Array.from(r))n(t)}startPolling(){this.stopPolling(),this.pollTimer=setInterval(async()=>this._poll(),800)}stopPolling(){this.pollTimer&&(clearInterval(this.pollTimer),this.pollTimer=null)}_addListener(e,t){0===Object.keys(this.listeners).length&&this.startPolling(),this.listeners[e]||(this.listeners[e]=new Set,this._get(e)),this.listeners[e].add(t)}_removeListener(e,t){this.listeners[e]&&(this.listeners[e].delete(t),0===this.listeners[e].size&&delete this.listeners[e]),0===Object.keys(this.listeners).length&&this.stopPolling()}}Pr.type="LOCAL";const Cr=Pr;class Or{constructor(e){this.auth=e,this.counter=1e12,this._widgets=new Map}render(e,t){var r=this.counter;return this._widgets.set(r,new Nr(e,this.auth.name,t||{})),this.counter++,r}reset(e){var t,r=e||1e12;null===(t=this._widgets.get(r))||void 0===t||t.delete(),this._widgets.delete(r)}getResponse(e){var t;return(null===(t=this._widgets.get(e||1e12))||void 0===t?void 0:t.getResponse())||""}async execute(e){var t;return null===(t=this._widgets.get(e||1e12))||void 0===t||t.execute(),""}}class Nr{constructor(e,t,r){this.params=r,this.timerId=null,this.deleted=!1,this.responseToken=null,this.clickHandler=()=>{this.execute()};var n="string"==typeof e?document.getElementById(e):e;G(n,"argument-error",{appName:t}),this.container=n,this.isVisible="invisible"!==this.params.size,this.isVisible?this.execute():this.container.addEventListener("click",this.clickHandler)}getResponse(){return this.checkIfDeleted(),this.responseToken}delete(){this.checkIfDeleted(),this.deleted=!0,this.timerId&&(clearTimeout(this.timerId),this.timerId=null),this.container.removeEventListener("click",this.clickHandler)}execute(){this.checkIfDeleted(),this.timerId||(this.timerId=window.setTimeout(()=>{this.responseToken=function(e){const t=[],r="1234567890abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ";for(let n=0;n<e;n++)t.push(r.charAt(Math.floor(Math.random()*r.length)));return t.join("")}(50);const{callback:e,"expired-callback":t}=this.params;if(e)try{e(this.responseToken)}catch(e){}this.timerId=window.setTimeout(()=>{if(this.timerId=null,this.responseToken=null,t)try{t()}catch(e){}this.isVisible&&this.execute()},6e4)},500))}checkIfDeleted(){if(this.deleted)throw new Error("reCAPTCHA mock was already deleted!")}}const Lr=Ye("rcb"),Dr=new Q(3e4,6e4);class Ur{constructor(){var e;this.hostLanguage="",this.counter=0,this.librarySeparatelyLoaded=!(null===(e=yr().grecaptcha)||void 0===e||!e.render)}load(s,a=""){var e;return G((e=a).length<=6&&/^\s*[a-zA-Z0-9\-]*\s*$/.test(e),s,"argument-error"),this.shouldResolveImmediately(a)&&ue(yr().grecaptcha)?Promise.resolve(yr().grecaptcha):new Promise((t,r)=>{const i=yr().setTimeout(()=>{r(H(s,"network-request-failed"))},Dr.get());yr()[Lr]=()=>{yr().clearTimeout(i),delete yr()[Lr];const e=yr().grecaptcha;if(e&&ue(e)){const n=e.render;e.render=(e,t)=>{var r=n(e,t);return this.counter++,r},this.hostLanguage=a,t(e)}else r(H(s,"internal-error"))},Je(`${$e.recaptchaV2Script}?${I({onload:Lr,render:"explicit",hl:a})}`).catch(()=>{clearTimeout(i),r(H(s,"internal-error"))})})}clearedOneInstance(){this.counter--}shouldResolveImmediately(e){var t;return!(null===(t=yr().grecaptcha)||void 0===t||!t.render)&&(e===this.hostLanguage||0<this.counter||this.librarySeparatelyLoaded)}}class Mr{async load(e){return new Or(e)}clearedOneInstance(){}}const Fr="recaptcha",Vr={theme:"light",type:"image"};class xr{constructor(e,t,r=Object.assign({},Vr)){this.parameters=r,this.type=Fr,this.destroyed=!1,this.widgetId=null,this.tokenChangeListeners=new Set,this.renderPromise=null,this.recaptcha=null,this.auth=Ge(e),this.isInvisible="invisible"===this.parameters.size,G("undefined"!=typeof document,this.auth,"operation-not-supported-in-this-environment");var n="string"==typeof t?document.getElementById(t):t;G(n,this.auth,"argument-error"),this.container=n,this.parameters.callback=this.makeTokenCallback(this.parameters.callback),this._recaptchaLoader=new(this.auth.settings.appVerificationDisabledForTesting?Mr:Ur),this.validateStartingState()}async verify(){this.assertNotDestroyed();const e=await this.render(),n=this.getAssertedRecaptcha();var t=n.getResponse(e);return t||new Promise(t=>{const r=e=>{e&&(this.tokenChangeListeners.delete(r),t(e))};this.tokenChangeListeners.add(r),this.isInvisible&&n.execute(e)})}render(){try{this.assertNotDestroyed()}catch(e){return Promise.reject(e)}return this.renderPromise||(this.renderPromise=this.makeRenderPromise().catch(e=>{throw this.renderPromise=null,e}),this.renderPromise)}_reset(){this.assertNotDestroyed(),null!==this.widgetId&&this.getAssertedRecaptcha().reset(this.widgetId)}clear(){this.assertNotDestroyed(),this.destroyed=!0,this._recaptchaLoader.clearedOneInstance(),this.isInvisible||this.container.childNodes.forEach(e=>{this.container.removeChild(e)})}validateStartingState(){G(!this.parameters.sitekey,this.auth,"argument-error"),G(this.isInvisible||!this.container.hasChildNodes(),this.auth,"argument-error"),G("undefined"!=typeof document,this.auth,"operation-not-supported-in-this-environment")}makeTokenCallback(r){return t=>{if(this.tokenChangeListeners.forEach(e=>e(t)),"function"==typeof r)r(t);else if("string"==typeof r){const e=yr()[r];"function"==typeof e&&e(t)}}}assertNotDestroyed(){G(!this.destroyed,this.auth,"internal-error")}async makeRenderPromise(){if(await this.init(),!this.widgetId){let e=this.container;var t;this.isInvisible||(t=document.createElement("div"),e.appendChild(t),e=t),this.widgetId=this.getAssertedRecaptcha().render(e,this.parameters)}return this.widgetId}async init(){G(Y()&&!Ir(),this.auth,"internal-error"),await function(){let t=null;return new Promise(e=>{"complete"!==document.readyState?(t=()=>e(),window.addEventListener("load",t)):e()}).catch(e=>{throw t&&window.removeEventListener("load",t),e})}(),this.recaptcha=await this._recaptchaLoader.load(this.auth,this.auth.languageCode||void 0);var e=await((await ie(this.auth,"GET","/v1/recaptchaParams")).recaptchaSiteKey||"");G(e,this.auth,"internal-error"),this.parameters.sitekey=e}getAssertedRecaptcha(){return G(this.recaptcha,this.auth,"internal-error"),this.recaptcha}}class jr{constructor(e,t){this.verificationId=e,this.onConfirmation=t}confirm(e){var t=ft._fromVerification(this.verificationId,e);return this.onConfirmation(t)}}async function Hr(t,r,n){var i,s,a,o,c,l,u=await n.verify();try{G("string"==typeof u,t,"argument-error"),G(n.type===Fr,t,"argument-error");let e;if(e="string"==typeof r?{phoneNumber:r}:r,"session"in e){var d=e.session;if("phoneNumber"in e)return G("enroll"===d.type,t,"internal-error"),(c=t,l={idToken:d.credential,phoneEnrollmentInfo:{phoneNumber:e.phoneNumber,recaptchaToken:u}},await ie(c,"POST","/v2/accounts/mfaEnrollment:start",ne(c,l))).phoneSessionInfo.sessionInfo;G("signin"===d.type,t,"internal-error");var h=(null===(i=e.multiFactorHint)||void 0===i?void 0:i.uid)||e.multiFactorUid;return G(h,t,"missing-multi-factor-info"),(o={mfaPendingCredential:d.credential,mfaEnrollmentId:h,phoneSignInInfo:{recaptchaToken:u}},await ie(t,"POST","/v2/accounts/mfaSignIn:start",ne(t,o))).phoneResponseInfo.sessionInfo}var p=(s=t,a={phoneNumber:e.phoneNumber,recaptchaToken:u},await ie(s,"POST","/v1/accounts:sendVerificationCode",ne(s,a)))["sessionInfo"];return p}finally{n._reset()}}class Wr{constructor(e){this.providerId=Wr.PROVIDER_ID,this.auth=Ge(e)}verifyPhoneNumber(e,t){return Hr(this.auth,e,k(t))}static credential(e,t){return ft._fromVerification(e,t)}static credentialFromResult(e){var t=e;return Wr.credentialFromTaggedObject(t)}static credentialFromError(e){return Wr.credentialFromTaggedObject(e.customData||{})}static credentialFromTaggedObject({_tokenResponse:e}){if(!e)return null;var{phoneNumber:t,temporaryProof:r}=e;return t&&r?ft._fromTokenResponse(t,r):null}}function qr(e,t){return t?Ae(t):(G(e._popupRedirectResolver,e,"argument-error"),e._popupRedirectResolver)}Wr.PROVIDER_ID="phone",Wr.PHONE_SIGN_IN_METHOD="phone";class zr extends nt{constructor(e){super("custom","custom"),this.params=e}_getIdTokenResponse(e){return dt(e,this._buildIdpRequest())}_linkToIdToken(e,t){return dt(e,this._buildIdpRequest(t))}_getReauthenticationResolver(e){return dt(e,this._buildIdpRequest())}_buildIdpRequest(e){const t={requestUri:this.params.requestUri,sessionId:this.params.sessionId,postBody:this.params.postBody,tenantId:this.params.tenantId,pendingToken:this.params.pendingToken,returnSecureToken:!0,returnIdpCredential:!0};return e&&(t.idToken=e),t}}function Br(e){return Mt(e.auth,new zr(e),e.bypassAuthState)}function Gr(e){var{auth:t,user:r}=e;return G(r,t,"internal-error"),Ut(r,new zr(e),e.bypassAuthState)}async function Kr(e){var{auth:t,user:r}=e;return G(r,t,"internal-error"),Lt(r,new zr(e),e.bypassAuthState)}class $r{constructor(e,t,r,n,i=!1){this.auth=e,this.resolver=r,this.user=n,this.bypassAuthState=i,this.pendingPromise=null,this.eventManager=null,this.filter=Array.isArray(t)?t:[t]}execute(){return new Promise(async(e,t)=>{this.pendingPromise={resolve:e,reject:t};try{this.eventManager=await this.resolver._initialize(this.auth),await this.onExecution(),this.eventManager.registerConsumer(this)}catch(e){this.reject(e)}})}async onAuthEvent(e){var{urlResponse:t,sessionId:r,postBody:n,tenantId:i,error:s,type:a}=e;if(s)this.reject(s);else{n={auth:this.auth,requestUri:t,sessionId:r,tenantId:i||void 0,postBody:n||void 0,user:this.user,bypassAuthState:this.bypassAuthState};try{this.resolve(await this.getIdpTask(a)(n))}catch(e){this.reject(e)}}}onError(e){this.reject(e)}getIdpTask(e){switch(e){case"signInViaPopup":case"signInViaRedirect":return Br;case"linkViaPopup":case"linkViaRedirect":return Kr;case"reauthViaPopup":case"reauthViaRedirect":return Gr;default:j(this.auth,"internal-error")}}resolve(e){$(this.pendingPromise,"Pending promise was never set"),this.pendingPromise.resolve(e),this.unregisterAndCleanUp()}reject(e){$(this.pendingPromise,"Pending promise was never set"),this.pendingPromise.reject(e),this.unregisterAndCleanUp()}unregisterAndCleanUp(){this.eventManager&&this.eventManager.unregisterConsumer(this),this.pendingPromise=null,this.cleanUp()}}const Jr=new Q(2e3,1e4);class Yr extends $r{constructor(e,t,r,n,i){super(e,t,n,i),this.provider=r,this.authWindow=null,this.pollId=null,Yr.currentPopupAction&&Yr.currentPopupAction.cancel(),Yr.currentPopupAction=this}async executeNotNull(){var e=await this.execute();return G(e,this.auth,"internal-error"),e}async onExecution(){$(1===this.filter.length,"Popup operations only handle one event");var e=gr();this.authWindow=await this.resolver._openPopup(this.auth,this.provider,this.filter[0],e),this.authWindow.associatedEvent=e,this.resolver._originValidation(this.auth).catch(e=>{this.reject(e)}),this.resolver._isIframeWebStorageSupported(this.auth,e=>{e||this.reject(H(this.auth,"web-storage-unsupported"))}),this.pollUserCancellation()}get eventId(){var e;return(null===(e=this.authWindow)||void 0===e?void 0:e.associatedEvent)||null}cancel(){this.reject(H(this.auth,"cancelled-popup-request"))}cleanUp(){this.authWindow&&this.authWindow.close(),this.pollId&&window.clearTimeout(this.pollId),this.authWindow=null,this.pollId=null,Yr.currentPopupAction=null}pollUserCancellation(){const t=()=>{var e;null!==(e=null===(e=this.authWindow)||void 0===e?void 0:e.window)&&void 0!==e&&e.closed?this.pollId=window.setTimeout(()=>{this.pollId=null,this.reject(H(this.auth,"popup-closed-by-user"))},8e3):this.pollId=window.setTimeout(t,Jr.get())};t()}}Yr.currentPopupAction=null;const Xr="pendingRedirect",Qr=new Map;class Zr extends $r{constructor(e,t,r=!1){super(e,["signInViaRedirect","linkViaRedirect","reauthViaRedirect","unknown"],t,void 0,r),this.eventId=null}async execute(){let t=Qr.get(this.auth._key());if(!t){try{const e=await async function(e,t){const r=nn(t),n=rn(e);if(!await n._isAvailable())return!1;var i="true"===await n._get(r);return await n._remove(r),i}(this.resolver,this.auth)?await super.execute():null;t=()=>Promise.resolve(e)}catch(e){t=()=>Promise.reject(e)}Qr.set(this.auth._key(),t)}return this.bypassAuthState||Qr.set(this.auth._key(),()=>Promise.resolve(null)),t()}async onAuthEvent(e){if("signInViaRedirect"===e.type)return super.onAuthEvent(e);if("unknown"!==e.type){if(e.eventId){var t=await this.auth._redirectUserForId(e.eventId);if(t)return this.user=t,super.onAuthEvent(e);this.resolve(null)}}else this.resolve(null)}async onExecution(){}cleanUp(){}}async function en(e,t){return rn(e)._set(nn(t),"true")}function tn(e,t){Qr.set(e._key(),t)}function rn(e){return Ae(e._redirectPersistence)}function nn(e){return Ce(Xr,e.config.apiKey,e.name)}function sn(e,t,r){return async function(e,t,r){if(Oi._isFirebaseServerApp(e.app))return Promise.reject(q(e));var n=Ge(e);z(e,t,gt),await n._initializationPromise;const i=qr(n,r);return await en(i,n),i._openRedirect(n,t,"signInViaRedirect")}(e,t,r)}function an(e,t,r){return async function(e,t,r){var n=k(e);if(z(n.auth,t,gt),Oi._isFirebaseServerApp(n.auth.app))return Promise.reject(q(n.auth));await n.auth._initializationPromise;const i=qr(n.auth,r);await en(i,n.auth);var s=await ln(n);return i._openRedirect(n.auth,t,"reauthViaRedirect",s)}(e,t,r)}function on(e,t,r){return async function(e,t,r){var n=k(e);z(n.auth,t,gt),await n.auth._initializationPromise;const i=qr(n.auth,r);await Dt(!1,n,t.providerId),await en(i,n.auth);var s=await ln(n);return i._openRedirect(n.auth,t,"linkViaRedirect",s)}(e,t,r)}async function cn(e,t,r=!1){if(Oi._isFirebaseServerApp(e.app))return Promise.reject(q(e));const n=Ge(e);var i=qr(n,t);const s=new Zr(n,i,r),a=await s.execute();return a&&!r&&(delete a.user._redirectEventId,await n._persistUserIfCurrent(a.user),await n._setRedirectUser(null,t)),a}async function ln(e){var t=gr(`${e.uid}:::`);return e._redirectEventId=t,await e.auth._setRedirectUser(e),await e.auth._persistUserIfCurrent(e),t}class un{constructor(e){this.auth=e,this.cachedEventUids=new Set,this.consumers=new Set,this.queuedRedirectEvent=null,this.hasHandledPotentialRedirect=!1,this.lastProcessedEventTime=Date.now()}registerConsumer(e){this.consumers.add(e),this.queuedRedirectEvent&&this.isEventForConsumer(this.queuedRedirectEvent,e)&&(this.sendToConsumer(this.queuedRedirectEvent,e),this.saveEventToCache(this.queuedRedirectEvent),this.queuedRedirectEvent=null)}unregisterConsumer(e){this.consumers.delete(e)}onEvent(t){if(this.hasEventBeenHandled(t))return!1;let r=!1;return this.consumers.forEach(e=>{this.isEventForConsumer(t,e)&&(r=!0,this.sendToConsumer(t,e),this.saveEventToCache(t))}),this.hasHandledPotentialRedirect||!function(e){switch(e.type){case"signInViaRedirect":case"linkViaRedirect":case"reauthViaRedirect":return!0;case"unknown":return hn(e);default:return!1}}(t)||(this.hasHandledPotentialRedirect=!0,r||(this.queuedRedirectEvent=t,r=!0)),r}sendToConsumer(e,t){var r;e.error&&!hn(e)?(r=(null===(r=e.error.code)||void 0===r?void 0:r.split("auth/")[1])||"internal-error",t.onError(H(this.auth,r))):t.onAuthEvent(e)}isEventForConsumer(e,t){var r=null===t.eventId||!!e.eventId&&e.eventId===t.eventId;return t.filter.includes(e.type)&&r}hasEventBeenHandled(e){return 6e5<=Date.now()-this.lastProcessedEventTime&&this.cachedEventUids.clear(),this.cachedEventUids.has(dn(e))}saveEventToCache(e){this.cachedEventUids.add(dn(e)),this.lastProcessedEventTime=Date.now()}}function dn(e){return[e.type,e.eventId,e.sessionId,e.tenantId].filter(e=>e).join("-")}function hn({type:e,error:t}){return"unknown"===e&&"auth/no-auth-event"===(null==t?void 0:t.code)}async function pn(e,t={}){return ie(e,"GET","/v1/projects",t)}const fn=/^\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}$/,mn=/^https?/;async function vn(e){if(!e.config.emulator){var t=(await pn(e))["authorizedDomains"];for(const r of t)try{if(function(e){const t=J(),{protocol:r,hostname:n}=new URL(t);if(e.startsWith("chrome-extension://")){var i=new URL(e);return""===i.hostname&&""===n?"chrome-extension:"===r&&e.replace("chrome-extension://","")===t.replace("chrome-extension://",""):"chrome-extension:"===r&&i.hostname===n}if(!mn.test(r))return!1;if(fn.test(e))return n===e;const s=e.replace(/\./g,"\\."),a=new RegExp("^(.+\\."+s+"|"+s+")$","i");return a.test(n)}(r))return}catch(e){}j(e,"unauthorized-domain")}}const gn=new Q(3e4,6e4);function _n(){const t=yr().___jsl;if(null!==t&&void 0!==t&&t.H)for(const r of Object.keys(t.H))if(t.H[r].r=t.H[r].r||[],t.H[r].L=t.H[r].L||[],t.H[r].r=[...t.H[r].L],t.CP)for(let e=0;e<t.CP.length;e++)t.CP[e]=null}function yn(i){return new Promise((e,t)=>{function r(){_n(),gapi.load("gapi.iframes",{callback:()=>{e(gapi.iframes.getContext())},ontimeout:()=>{_n(),t(H(i,"network-request-failed"))},timeout:gn.get()})}if(null!==(n=null===(n=yr().gapi)||void 0===n?void 0:n.iframes)&&void 0!==n&&n.Iframe)e(gapi.iframes.getContext());else{if(null===(n=yr().gapi)||void 0===n||!n.load){var n=Ye("iframefcb");return yr()[n]=()=>{gapi.load?r():t(H(i,"network-request-failed"))},Je(`${$e.gapiScript}?onload=${n}`).catch(e=>t(e))}r()}}).catch(e=>{throw In=null,e})}let In=null;const wn=new Q(5e3,15e3),Tn="__/auth/iframe",bn="emulator/auth/iframe",En={style:{position:"absolute",top:"-100px",width:"1px",height:"1px"},"aria-hidden":"true",tabindex:"-1"},kn=new Map([["identitytoolkit.googleapis.com","p"],["staging-identitytoolkit.sandbox.googleapis.com","s"],["test-identitytoolkit.sandbox.googleapis.com","t"]]);async function Sn(a){const e=(t=a,In=In||yn(t),await In);var t,r=yr().gapi;return G(r,a,"internal-error"),e.open({where:document.body,url:function(e){var t=e.config;G(t.authDomain,e,"auth-domain-config-required");var r=t.emulator?Z(t,bn):`https://${e.config.authDomain}/${Tn}`;const n={apiKey:t.apiKey,appName:e.name,v:Oi.SDK_VERSION};(t=kn.get(e.config.apiHost))&&(n.eid=t);const i=e._getFrameworks();return i.length&&(n.fw=i.join(",")),`${r}?${I(n).slice(1)}`}(a),messageHandlersFilter:r.iframes.CROSS_ORIGIN_IFRAMES_FILTER,attributes:En,dontclear:!0},s=>new Promise(async(e,t)=>{await s.restyle({setHideOnLeave:!1});const r=H(a,"network-request-failed"),n=yr().setTimeout(()=>{t(r)},wn.get());function i(){yr().clearTimeout(n),e(s)}s.ping(i).then(i,()=>{t(r)})}))}const An={location:"yes",resizable:"yes",statusbar:"yes",toolbar:"no"};class Rn{constructor(e){this.window=e,this.associatedEvent=null}close(){if(this.window)try{this.window.close()}catch(e){}}}function Pn(e,t,r,n=500,i=600){var s=Math.max((window.screen.availHeight-i)/2,0).toString(),a=Math.max((window.screen.availWidth-n)/2,0).toString();let o="";const c=Object.assign(Object.assign({},An),{width:n.toString(),height:i.toString(),top:s,left:a});s=d().toLowerCase();r&&(o=Ue(s)?"_blank":r),Le(s)&&(t=t||"http://localhost",c.scrollbars="yes");var l,a=Object.entries(c).reduce((e,[t,r])=>`${e}${t}=${r},`,"");if([r=d()]=[s],je(r)&&null!==(l=window.navigator)&&void 0!==l&&l.standalone&&"_self"!==o)return function(e,t){const r=document.createElement("a");r.href=e,r.target=t;const n=document.createEvent("MouseEvent");n.initMouseEvent("click",!0,!0,window,1,0,0,0,0,!1,!1,!1,!1,1,null),r.dispatchEvent(n)}(t||"",o),new Rn(null);const u=window.open(t||"",o,a);G(u,e,"popup-blocked");try{u.focus()}catch(e){}return new Rn(u)}const Cn="__/auth/handler",On="emulator/auth/handler",Nn=encodeURIComponent("fac");async function Ln(e,t,r,n,i,s){G(e.config.authDomain,e,"auth-domain-config-required"),G(e.config.apiKey,e,"invalid-api-key");const a={apiKey:e.config.apiKey,appName:e.name,authType:r,redirectUrl:n,v:Oi.SDK_VERSION,eventId:i};if(t instanceof gt){t.setDefaultLanguage(e.languageCode),a.providerId=t.providerId||"",function(e){for(const t in e)if(Object.prototype.hasOwnProperty.call(e,t))return;return 1}(t.getCustomParameters())||(a.customParameters=JSON.stringify(t.getCustomParameters()));for(var[o,c]of Object.entries(s||{}))a[o]=c}if(t instanceof _t){const d=t.getScopes().filter(e=>""!==e);0<d.length&&(a.scopes=d.join(","))}e.tenantId&&(a.tid=e.tenantId);const l=a;for(const h of Object.keys(l))void 0===l[h]&&delete l[h];var u=await e._getAppCheckToken(),u=u?`#${Nn}=${encodeURIComponent(u)}`:"";return`${e=[e["config"]][0],e.emulator?Z(e,On):`https://${e.authDomain}/${Cn}`}?${I(l).slice(1)}${u}`}const Dn="webStorageSupport";const Un=class{constructor(){this.eventManagers={},this.iframes={},this.originValidationPromises={},this._redirectPersistence=mr,this._completeRedirectFn=cn,this._overrideRedirectResult=tn}async _openPopup(e,t,r,n){var i;return $(null===(i=this.eventManagers[e._key()])||void 0===i?void 0:i.manager,"_initialize() not called before _openPopup()"),Pn(e,await Ln(e,t,r,J(),n),gr())}async _openRedirect(e,t,r,n){await this._originValidation(e);var i=await Ln(e,t,r,J(),n);return yr().location.href=i,new Promise(()=>{})}_initialize(e){const t=e._key();if(this.eventManagers[t]){const{manager:n,promise:r}=this.eventManagers[t];return n?Promise.resolve(n):($(r,"If manager is not set, promise should be"),r)}const r=this.initAndGetManager(e);return this.eventManagers[t]={promise:r},r.catch(()=>{delete this.eventManagers[t]}),r}async initAndGetManager(t){const e=await Sn(t),r=new un(t);return e.register("authEvent",e=>{return G(null==e?void 0:e.authEvent,t,"invalid-auth-event"),{status:r.onEvent(e.authEvent)?"ACK":"ERROR"}},gapi.iframes.CROSS_ORIGIN_IFRAMES_FILTER),this.eventManagers[t._key()]={manager:r},this.iframes[t._key()]=e,r}_isIframeWebStorageSupported(r,n){const e=this.iframes[r._key()];e.send(Dn,{type:Dn},e=>{var t=null===(t=null==e?void 0:e[0])||void 0===t?void 0:t[Dn];void 0!==t&&n(!!t),j(r,"internal-error")},gapi.iframes.CROSS_ORIGIN_IFRAMES_FILTER)}_originValidation(e){var t=e._key();return this.originValidationPromises[t]||(this.originValidationPromises[t]=vn(e)),this.originValidationPromises[t]}get _shouldInitProactively(){return He()||De()||je()}};class Mn extends class{constructor(e){this.factorId=e}_process(e,t,r){switch(t.type){case"enroll":return this._finalizeEnroll(e,t.credential,r);case"signin":return this._finalizeSignIn(e,t.credential);default:return K("unexpected MultiFactorSessionType")}}}{constructor(e){super("phone"),this.credential=e}static _fromCredential(e){return new Mn(e)}_finalizeEnroll(e,t,r){return e=e,r={idToken:t,displayName:r,phoneVerificationInfo:this.credential._makeVerificationRequest()},ie(e,"POST","/v2/accounts/mfaEnrollment:finalize",ne(e,r))}_finalizeSignIn(e,t){return e=e,t={mfaPendingCredential:t,phoneVerificationInfo:this.credential._makeVerificationRequest()},ie(e,"POST","/v2/accounts/mfaSignIn:finalize",ne(e,t))}}class Fn{constructor(){}static assertion(e){return Mn._fromCredential(e)}}Fn.FACTOR_ID="phone";var Vn="@firebase/auth";class xn{constructor(e){this.auth=e,this.internalListeners=new Map}getUid(){var e;return this.assertAuthConfigured(),(null===(e=this.auth.currentUser)||void 0===e?void 0:e.uid)||null}async getToken(e){return this.assertAuthConfigured(),await this.auth._initializationPromise,this.auth.currentUser?{accessToken:await this.auth.currentUser.getIdToken(e)}:null}addAuthTokenListener(t){var e;this.assertAuthConfigured(),this.internalListeners.has(t)||(e=this.auth.onIdTokenChanged(e=>{t((null==e?void 0:e.stsTokenManager.accessToken)||null)}),this.internalListeners.set(t,e),this.updateProactiveRefresh())}removeAuthTokenListener(e){this.assertAuthConfigured();const t=this.internalListeners.get(e);t&&(this.internalListeners.delete(e),t(),this.updateProactiveRefresh())}assertAuthConfigured(){G(this.auth._initializationPromise,"dependent-sdk-initialized-before-auth")}updateProactiveRefresh(){0<this.internalListeners.size?this.auth._startProactiveRefresh():this.auth._stopProactiveRefresh()}}var jn,Hn,Wn;function qn(){return window}jn="authIdTokenMaxAge",null===(Hn=o())||void 0===Hn||Hn[`_${jn}`],$e={loadJS(i){return new Promise((e,r)=>{const t=document.createElement("script");var n;t.setAttribute("src",i),t.onload=e,t.onerror=e=>{const t=H("internal-error");t.customData=e,r(t)},t.type="text/javascript",t.charset="UTF-8",(null!==(n=null===(n=document.getElementsByTagName("head"))||void 0===n?void 0:n[0])&&void 0!==n?n:document).appendChild(t)})},gapiScript:"https://apis.google.com/js/api.js",recaptchaV2Script:"https://www.google.com/recaptcha/api.js",recaptchaEnterpriseScript:"https://www.google.com/recaptcha/enterprise.js?render="},Wn="Browser",Oi._registerComponent(new O("auth",(e,{options:t})=>{var r=e.getProvider("app").getImmediate(),n=e.getProvider("heartbeat"),i=e.getProvider("app-check-internal");const{apiKey:s,authDomain:a}=r.options;G(s&&!s.includes(":"),"invalid-api-key",{appName:r.name});var o={apiKey:s,authDomain:a,clientPlatform:Wn,apiHost:"identitytoolkit.googleapis.com",tokenApiHost:"securetoken.googleapis.com",apiScheme:"https",sdkClientVersion:We(Wn)},o=new Be(r,n,i,o);return function(e,t){const r=(null==t?void 0:t.persistence)||[];var n=(Array.isArray(r)?r:[r]).map(Ae);null!=t&&t.errorMap&&e._updateErrorMap(t.errorMap),e._initializeWithPersistence(n,null==t?void 0:t.popupRedirectResolver)}(o,t),o},"PUBLIC").setInstantiationMode("EXPLICIT").setInstanceCreatedCallback((e,t,r)=>{const n=e.getProvider("auth-internal");n.initialize()})),Oi._registerComponent(new O("auth-internal",e=>{var t=Ge(e.getProvider("auth").getImmediate());return e=t,new xn(e)},"PRIVATE").setInstantiationMode("EXPLICIT")),Oi.registerVersion(Vn,"1.7.9",function(e){switch(e){case"Node":return"node";case"ReactNative":return"rn";case"Worker":return"webworker";case"Cordova":return"cordova";case"WebExtension":return"web-extension";default:return}}(Wn)),Oi.registerVersion(Vn,"1.7.9","esm2017");async function zn(e,t,r){var n=qn()["BuildInfo"];$(t.sessionId,"AuthEvent did not contain a session ID");var i=await async function(e){const t=function(e){if($(/[0-9a-zA-Z]+/.test(e),"Can only convert alpha-numeric strings"),"undefined"!=typeof TextEncoder)return(new TextEncoder).encode(e);const t=new ArrayBuffer(e.length),r=new Uint8Array(t);for(let n=0;n<e.length;n++)r[n]=e.charCodeAt(n);return r}(e),r=await crypto.subtle.digest("SHA-256",t),n=Array.from(new Uint8Array(r));return n.map(e=>e.toString(16).padStart(2,"0")).join("")}(t.sessionId);const s={};return je()?s.ibi=n.packageName:Fe()?s.apn=n.packageName:j(e,"operation-not-supported-in-this-environment"),n.displayName&&(s.appDisplayName=n.displayName),s.sessionId=i,Ln(e,r,t.type,void 0,null!==(i=t.eventId)&&void 0!==i?i:void 0,s)}function Bn(n){const i=qn()["cordova"];return new Promise(r=>{i.plugins.browsertab.isAvailable(e=>{let t=null;e?i.plugins.browsertab.openUrl(n):t=i.InAppBrowser.open(n,(e=d(),/(iPad|iPhone|iPod).*OS 7_\d/i.test(e)||/(iPad|iPhone|iPod).*OS 8_\d/i.test(e)?"_blank":"_system"),"location=yes"),r(t)})})}const Gn=20;class Kn extends un{constructor(){super(...arguments),this.passiveListeners=new Set,this.initPromise=new Promise(e=>{this.resolveInitialized=e})}addPassiveListener(e){this.passiveListeners.add(e)}removePassiveListener(e){this.passiveListeners.delete(e)}resetRedirect(){this.queuedRedirectEvent=null,this.hasHandledPotentialRedirect=!1}onEvent(t){return this.resolveInitialized(),this.passiveListeners.forEach(e=>e(t)),super.onEvent(t)}async initialized(){await this.initPromise}}function $n(e,t,r=null){return{type:t,eventId:r,urlResponse:null,sessionId:function(){const e=[],t="1234567890abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ";for(let n=0;n<Gn;n++){var r=Math.floor(Math.random()*t.length);e.push(t.charAt(r))}return e.join("")}(),postBody:null,tenantId:e.tenantId,error:H(e,"no-auth-event")}}async function Jn(e){var t=await Xn()._get(Qn(e));return t&&await Xn()._remove(Qn(e)),t}function Yn(e,t){var r,n,i;const s=(r=Zn(t=t),a=r.link?decodeURIComponent(r.link):void 0,n=Zn(a).link,i=r.deep_link_id?decodeURIComponent(r.deep_link_id):void 0,(r=Zn(i).link)||i||n||a||t);if(s.includes("/__/auth/callback")){var a=Zn(s),a=a.firebaseError?function(e){try{return JSON.parse(e)}catch(e){return null}}(decodeURIComponent(a.firebaseError)):null,a=null===(a=null===(a=null==a?void 0:a.code)||void 0===a?void 0:a.split("auth/"))||void 0===a?void 0:a[1],a=a?H(a):null;return a?{type:e.type,eventId:e.eventId,tenantId:e.tenantId,error:a,urlResponse:null,sessionId:null,postBody:null}:{type:e.type,eventId:e.eventId,tenantId:e.tenantId,sessionId:e.sessionId,urlResponse:s,postBody:null}}return null}function Xn(){return Ae(pr)}function Qn(e){return Ce("authEvent",e.config.apiKey,e.name)}function Zn(e){if(null==e||!e.includes("?"))return{};const[,...t]=e.split("?");return w(t.join("?"))}const ei=class{constructor(){this._redirectPersistence=mr,this._shouldInitProactively=!0,this.eventManagers=new Map,this.originValidationPromises={},this._completeRedirectFn=cn,this._overrideRedirectResult=tn}async _initialize(e){var t=e._key();let r=this.eventManagers.get(t);return r||(r=new Kn(e),this.eventManagers.set(t,r),this.attachCallbackListeners(e,r)),r}_openPopup(e){j(e,"operation-not-supported-in-this-environment")}async _openRedirect(e,t,r,n){var i,s;i=e,o=qn(),G("function"==typeof(null===(s=null==o?void 0:o.universalLinks)||void 0===s?void 0:s.subscribe),i,"invalid-cordova-configuration",{missingPlugin:"cordova-universal-links-plugin-fix"}),G(void 0!==(null===(s=null==o?void 0:o.BuildInfo)||void 0===s?void 0:s.packageName),i,"invalid-cordova-configuration",{missingPlugin:"cordova-plugin-buildInfo"}),G("function"==typeof(null===(s=null===(s=null===(s=null==o?void 0:o.cordova)||void 0===s?void 0:s.plugins)||void 0===s?void 0:s.browsertab)||void 0===s?void 0:s.openUrl),i,"invalid-cordova-configuration",{missingPlugin:"cordova-plugin-browsertab"}),G("function"==typeof(null===(s=null===(s=null===(s=null==o?void 0:o.cordova)||void 0===s?void 0:s.plugins)||void 0===s?void 0:s.browsertab)||void 0===s?void 0:s.isAvailable),i,"invalid-cordova-configuration",{missingPlugin:"cordova-plugin-browsertab"}),G("function"==typeof(null===(o=null===(o=null==o?void 0:o.cordova)||void 0===o?void 0:o.InAppBrowser)||void 0===o?void 0:o.open),i,"invalid-cordova-configuration",{missingPlugin:"cordova-plugin-inappbrowser"});const a=await this._initialize(e);await a.initialized(),a.resetRedirect(),Qr.clear(),await this._originValidation(e);var o=$n(e,r,n);r=e,n=o,await Xn()._set(Qn(r),n);o=await Bn(await zn(e,o,t));return async function(a,o,c){const l=qn()["cordova"];let u=()=>{};try{await new Promise((r,e)=>{let t=null;function n(){var e;r();const t=null===(e=l.plugins.browsertab)||void 0===e?void 0:e.close;"function"==typeof t&&t(),"function"==typeof(null==c?void 0:c.close)&&c.close()}function i(){t=t||window.setTimeout(()=>{e(H(a,"redirect-cancelled-by-user"))},2e3)}function s(){"visible"===(null===document||void 0===document?void 0:document.visibilityState)&&i()}o.addPassiveListener(n),document.addEventListener("resume",i,!1),Fe()&&document.addEventListener("visibilitychange",s,!1),u=()=>{o.removePassiveListener(n),document.removeEventListener("resume",i,!1),document.removeEventListener("visibilitychange",s,!1),t&&window.clearTimeout(t)}})}finally{u()}}(e,a,o)}_isIframeWebStorageSupported(e,t){throw new Error("Method not implemented.")}_originValidation(e){var t=e._key();return this.originValidationPromises[t]||(this.originValidationPromises[t]=async function(e){var t=qn()["BuildInfo"];const r={};je()?r.iosBundleId=t.packageName:Fe()?r.androidPackageName=t.packageName:j(e,"operation-not-supported-in-this-environment"),await pn(e,r)}(e)),this.originValidationPromises[t]}attachCallbackListeners(n,i){const{universalLinks:e,handleOpenURL:t,BuildInfo:r}=qn(),s=setTimeout(async()=>{await Jn(n),i.onEvent(ti())},500),a=async e=>{clearTimeout(s);var t=await Jn(n);let r=null;t&&null!=e&&e.url&&(r=Yn(t,e.url)),i.onEvent(r||ti())};void 0!==e&&"function"==typeof e.subscribe&&e.subscribe(null,a);const o=t,c=`${r.packageName.toLowerCase()}://`;qn().handleOpenURL=async e=>{if(e.toLowerCase().startsWith(c)&&a({url:e}),"function"==typeof o)try{o(e)}catch(e){console.error(e)}}}};function ti(){return{type:"unknown",eventId:null,sessionId:null,urlResponse:null,postBody:null,tenantId:null,error:H("no-auth-event")}}var ri;function ni(){var e;return(null===(e=null===self||void 0===self?void 0:self.location)||void 0===e?void 0:e.protocol)||null}function ii(e=d()){return!("file:"!==ni()&&"ionic:"!==ni()&&"capacitor:"!==ni()||!e.toLowerCase().match(/iphone|ipad|ipod|android/))}function si(e=d()){return m()&&11===(null===document||void 0===document?void 0:document.documentMode)||([e=d()]=[e],/Edge\/\d+/.test(e))}function ai(){try{const t=self.localStorage;var e=gr();if(t)return t.setItem(e,"1"),t.removeItem(e),!si()||v()}catch(e){return oi()&&v()}return!1}function oi(){return"undefined"!=typeof global&&"WorkerGlobalScope"in global&&"importScripts"in global}function ci(){return("http:"===ni()||"https:"===ni()||p()||ii())&&!(f()||h())&&ai()&&!oi()}function li(){return ii()&&"undefined"!=typeof document}const ui={LOCAL:"local",NONE:"none",SESSION:"session"},di=G,hi="persistence";async function pi(e){await e._initializationPromise;const t=fi();var r=Ce(hi,e.config.apiKey,e.name);t&&t.setItem(r,e._getPersistence())}function fi(){var e;try{return(null===(e="undefined"!=typeof window?window:null)?void 0:e.sessionStorage)||null}catch(e){return null}}const mi=G;class vi{constructor(){this.browserResolver=Ae(Un),this.cordovaResolver=Ae(ei),this.underlyingResolver=null,this._redirectPersistence=mr,this._completeRedirectFn=cn,this._overrideRedirectResult=tn}async _initialize(e){return await this.selectUnderlyingResolver(),this.assertedUnderlyingResolver._initialize(e)}async _openPopup(e,t,r,n){return await this.selectUnderlyingResolver(),this.assertedUnderlyingResolver._openPopup(e,t,r,n)}async _openRedirect(e,t,r,n){return await this.selectUnderlyingResolver(),this.assertedUnderlyingResolver._openRedirect(e,t,r,n)}_isIframeWebStorageSupported(e,t){this.assertedUnderlyingResolver._isIframeWebStorageSupported(e,t)}_originValidation(e){return this.assertedUnderlyingResolver._originValidation(e)}get _shouldInitProactively(){return li()||this.browserResolver._shouldInitProactively}get assertedUnderlyingResolver(){return mi(this.underlyingResolver,"internal-error"),this.underlyingResolver}async selectUnderlyingResolver(){var e;this.underlyingResolver||(e=await(!!li()&&new Promise(e=>{const t=setTimeout(()=>{e(!1)},1e3);document.addEventListener("deviceready",()=>{clearTimeout(t),e(!0)})})),this.underlyingResolver=e?this.cordovaResolver:this.browserResolver)}}function gi(e){return e.unwrap()}function _i(e,t){var r,n,i,s=null===(n=t.customData)||void 0===n?void 0:n._tokenResponse;if("auth/multi-factor-auth-required"===(null==t?void 0:t.code)){const o=t;o.resolver=new Ti(e,(r=t,i=k(e),G((a=r).customData.operationType,i,"argument-error"),G(null===(n=a.customData._serverResponse)||void 0===n?void 0:n.mfaPendingCredential,i,"argument-error"),or._fromError(i,a)))}else if(s){var a=yi(t);const c=t;a&&(c.credential=a,c.tenantId=s.tenantId||void 0,c.email=s.email||void 0,c.phoneNumber=s.phoneNumber||void 0)}}function yi(e){var t=(e instanceof g?e.customData:e)["_tokenResponse"];if(!t)return null;if(!(e instanceof g)&&"temporaryProof"in t&&"phoneNumber"in t)return Wr.credentialFromResult(e);const r=t.providerId;if(!r||r===N.PASSWORD)return null;let n;switch(r){case N.GOOGLE:n=wt;break;case N.FACEBOOK:n=It;break;case N.GITHUB:n=Tt;break;case N.TWITTER:n=kt;break;default:var{oauthIdToken:i,oauthAccessToken:s,oauthTokenSecret:a,pendingToken:o,nonce:c}=t;return s||a||i||o?o?r.startsWith("saml.")?bt._create(r,o):ht._fromParams({providerId:r,signInMethod:r,pendingToken:o,idToken:i,accessToken:s}):new yt(r).credential({idToken:i,accessToken:s,rawNonce:c}):null}return e instanceof g?n.credentialFromError(e):n.credentialFromResult(e)}function Ii(t,e){return e.catch(e=>{throw e instanceof g&&_i(t,e),e}).then(e=>{var t=e.operationType,r=e.user;return{operationType:t,credential:yi(e),additionalUserInfo:sr(e),user:bi.getOrCreate(r)}})}async function wi(t,e){const r=await e;return{verificationId:r.verificationId,confirm:e=>Ii(t,r.confirm(e))}}class Ti{constructor(e,t){this.resolver=t,this.auth=e.wrapped()}get session(){return this.resolver.session}get hints(){return this.resolver.hints}resolveSignIn(e){return Ii(gi(this.auth),this.resolver.resolveSignIn(e))}}class bi{constructor(e){var t;this._delegate=e,this.multiFactor=(t=k(e),lr.has(t)||lr.set(t,cr._fromUser(t)),lr.get(t))}static getOrCreate(e){return bi.USER_MAP.has(e)||bi.USER_MAP.set(e,new bi(e)),bi.USER_MAP.get(e)}delete(){return this._delegate.delete()}reload(){return this._delegate.reload()}toJSON(){return this._delegate.toJSON()}getIdTokenResult(e){return this._delegate.getIdTokenResult(e)}getIdToken(e){return this._delegate.getIdToken(e)}linkAndRetrieveDataWithCredential(e){return this.linkWithCredential(e)}async linkWithCredential(e){return Ii(this.auth,Vt(this._delegate,e))}async linkWithPhoneNumber(e,t){return wi(this.auth,async function(e,t,r){const n=k(e);await Dt(!1,n,"phone");var i=await Hr(n.auth,t,k(r));return new jr(i,e=>Vt(n,e))}(this._delegate,e,t))}async linkWithPopup(e){return Ii(this.auth,async function(e,t,r){var n=k(e);z(n.auth,t,gt);var i=qr(n.auth,r);const s=new Yr(n.auth,"linkViaPopup",t,i,n);return s.executeNotNull()}(this._delegate,e,vi))}async linkWithRedirect(e){return await pi(Ge(this.auth)),on(this._delegate,e,vi)}reauthenticateAndRetrieveDataWithCredential(e){return this.reauthenticateWithCredential(e)}async reauthenticateWithCredential(e){return Ii(this.auth,xt(this._delegate,e))}reauthenticateWithPhoneNumber(e,t){return wi(this.auth,async function(e,t,r){const n=k(e);if(Oi._isFirebaseServerApp(n.auth.app))return Promise.reject(q(n.auth));var i=await Hr(n.auth,t,k(r));return new jr(i,e=>xt(n,e))}(this._delegate,e,t))}reauthenticateWithPopup(e){return Ii(this.auth,async function(e,t,r){var n=k(e);if(Oi._isFirebaseServerApp(n.auth.app))return Promise.reject(H(n.auth,"operation-not-supported-in-this-environment"));z(n.auth,t,gt);var i=qr(n.auth,r);const s=new Yr(n.auth,"reauthViaPopup",t,i,n);return s.executeNotNull()}(this._delegate,e,vi))}async reauthenticateWithRedirect(e){return await pi(Ge(this.auth)),an(this._delegate,e,vi)}sendEmailVerification(e){return Jt(this._delegate,e)}async unlink(e){return await Nt(this._delegate,e),this}updateEmail(e){return t=this._delegate,e=e,r=k(t),Oi._isFirebaseServerApp(r.auth.app)?Promise.reject(q(r.auth)):Qt(r,e,null);var t,r}updatePassword(e){return Qt(k(this._delegate),null,e)}updatePhoneNumber(e){return async function(e,t){var r=k(e);if(Oi._isFirebaseServerApp(r.auth.app))return Promise.reject(q(r.auth));await Lt(r,t)}(this._delegate,e)}updateProfile(e){return Xt(this._delegate,e)}verifyBeforeUpdateEmail(e,t){return Yt(this._delegate,e,t)}get emailVerified(){return this._delegate.emailVerified}get isAnonymous(){return this._delegate.isAnonymous}get metadata(){return this._delegate.metadata}get phoneNumber(){return this._delegate.phoneNumber}get providerData(){return this._delegate.providerData}get refreshToken(){return this._delegate.refreshToken}get tenantId(){return this._delegate.tenantId}get displayName(){return this._delegate.displayName}get email(){return this._delegate.email}get photoURL(){return this._delegate.photoURL}get providerId(){return this._delegate.providerId}get uid(){return this._delegate.uid}get auth(){return this._delegate.auth}}bi.USER_MAP=new WeakMap;const Ei=G;class ki{constructor(e,t){if(this.app=e,t.isInitialized())return this._delegate=t.getImmediate(),void this.linkUnderlyingAuth();var r=e.options["apiKey"];Ei(r,"invalid-api-key",{appName:e.name}),Ei(r,"invalid-api-key",{appName:e.name});var n="undefined"!=typeof window?vi:void 0;this._delegate=t.initialize({options:{persistence:function(e,t){const r=function(e,t){const r=fi();if(!r)return[];var n=Ce(hi,e,t);switch(r.getItem(n)){case ui.NONE:return[Pe];case ui.LOCAL:return[Cr,mr];case ui.SESSION:return[mr];default:return[]}}(e,t);"undefined"==typeof self||r.includes(Cr)||r.push(Cr);if("undefined"!=typeof window)for(const n of[pr,mr])r.includes(n)||r.push(n);r.includes(Pe)||r.push(Pe);return r}(r,e.name),popupRedirectResolver:n}}),this._delegate._updateErrorMap(U),this.linkUnderlyingAuth()}get emulatorConfig(){return this._delegate.emulatorConfig}get currentUser(){return this._delegate.currentUser?bi.getOrCreate(this._delegate.currentUser):null}get languageCode(){return this._delegate.languageCode}set languageCode(e){this._delegate.languageCode=e}get settings(){return this._delegate.settings}get tenantId(){return this._delegate.tenantId}set tenantId(e){this._delegate.tenantId=e}useDeviceLanguage(){this._delegate.useDeviceLanguage()}signOut(){return this._delegate.signOut()}useEmulator(e,t){et(this._delegate,e,t)}applyActionCode(e){return Gt(this._delegate,e)}checkActionCode(e){return Kt(this._delegate,e)}confirmPasswordReset(e,t){return async function(t,e,r){await it(k(t),{oobCode:e,newPassword:r}).catch(async e=>{throw"auth/password-does-not-meet-requirements"===e.code&&Bt(t),e})}(this._delegate,e,t)}async createUserWithEmailAndPassword(e,t){return Ii(this._delegate,async function(t,e,r){if(Oi._isFirebaseServerApp(t.app))return Promise.reject(q(t));const n=Ge(t),i=Ze(n,{returnSecureToken:!0,email:e,password:r,clientType:"CLIENT_TYPE_WEB"},"signUpPassword",St);var s=await i.catch(e=>{throw"auth/password-does-not-meet-requirements"===e.code&&Bt(t),e}),s=await At._fromIdTokenResponse(n,"signIn",s);return await n._updateCurrentUser(s.user),s}(this._delegate,e,t))}fetchProvidersForEmail(e){return this.fetchSignInMethodsForEmail(e)}fetchSignInMethodsForEmail(e){return $t(this._delegate,e)}isSignInWithEmailLink(e){return this._delegate,e=e,"EMAIL_SIGNIN"===(null==(t=mt.parseLink(e))?void 0:t.operation);var t}async getRedirectResult(){Ei(ci(),this._delegate,"operation-not-supported-in-this-environment");var e,t,r=(e=this._delegate,t=vi,await Ge(e)._initializationPromise,await cn(e,t,!1));return r?Ii(this._delegate,Promise.resolve(r)):{credential:null,user:null}}addFrameworkForLogging(e){Ge(this._delegate)._logFramework(e)}onAuthStateChanged(e,t,r){var{next:n,error:i,complete:s}=Si(e,t,r);return this._delegate.onAuthStateChanged(n,i,s)}onIdTokenChanged(e,t,r){var{next:n,error:i,complete:s}=Si(e,t,r);return this._delegate.onIdTokenChanged(n,i,s)}sendSignInLinkToEmail(e,t){return async function(e,t,r){const n=Ge(e);var i={requestType:"EMAIL_SIGNIN",email:t,clientType:"CLIENT_TYPE_WEB"};t=i,G((r=r).handleCodeInApp,n,"argument-error"),r&&zt(n,t,r),await Ze(n,i,"getOobCode",lt)}(this._delegate,e,t)}sendPasswordResetEmail(e,t){return async function(e,t,r){var n=Ge(e),i={requestType:"PASSWORD_RESET",email:t,clientType:"CLIENT_TYPE_WEB"};r&&zt(n,i,r),await Ze(n,i,"getOobCode",ct)}(this._delegate,e,t||void 0)}async setPersistence(e){var t,r;t=this._delegate,r=e,di(Object.values(ui).includes(r),t,"invalid-persistence-type"),f()?di(r!==ui.SESSION,t,"unsupported-persistence-type"):h()?di(r===ui.NONE,t,"unsupported-persistence-type"):oi()?di(r===ui.NONE||r===ui.LOCAL&&v(),t,"unsupported-persistence-type"):di(r===ui.NONE||ai(),t,"unsupported-persistence-type");let n;switch(e){case ui.SESSION:n=mr;break;case ui.LOCAL:var i=await Ae(Cr)._isAvailable();n=i?Cr:pr;break;case ui.NONE:n=Pe;break;default:return j("argument-error",{appName:this._delegate.name})}return this._delegate.setPersistence(n)}signInAndRetrieveDataWithCredential(e){return this.signInWithCredential(e)}signInAnonymously(){return Ii(this._delegate,async function(e){if(Oi._isFirebaseServerApp(e.app))return Promise.reject(q(e));const t=Ge(e);if(await t._initializationPromise,null!==(r=t.currentUser)&&void 0!==r&&r.isAnonymous)return new At({user:t.currentUser,providerId:null,operationType:"signIn"});var r=await St(t,{returnSecureToken:!0}),r=await At._fromIdTokenResponse(t,"signIn",r,!0);return await t._updateCurrentUser(r.user),r}(this._delegate))}signInWithCredential(e){return Ii(this._delegate,Ft(this._delegate,e))}signInWithCustomToken(e){return Ii(this._delegate,jt(this._delegate,e))}signInWithEmailAndPassword(e,t){return Ii(this._delegate,(r=this._delegate,e=e,t=t,Oi._isFirebaseServerApp(r.app)?Promise.reject(q(r)):Ft(k(r),vt.credential(e,t)).catch(async e=>{throw"auth/password-does-not-meet-requirements"===e.code&&Bt(r),e})));var r}signInWithEmailLink(e,t){return Ii(this._delegate,async function(e,t,r){if(Oi._isFirebaseServerApp(e.app))return Promise.reject(q(e));var n=k(e),i=vt.credentialWithLink(t,r||J());return G(i._tenantId===(n.tenantId||null),n,"tenant-id-mismatch"),Ft(n,i)}(this._delegate,e,t))}signInWithPhoneNumber(e,t){return wi(this._delegate,async function(e,t,r){if(Oi._isFirebaseServerApp(e.app))return Promise.reject(q(e));const n=Ge(e);var i=await Hr(n,t,k(r));return new jr(i,e=>Ft(n,e))}(this._delegate,e,t))}async signInWithPopup(e){return Ei(ci(),this._delegate,"operation-not-supported-in-this-environment"),Ii(this._delegate,async function(e,t,r){if(Oi._isFirebaseServerApp(e.app))return Promise.reject(H(e,"operation-not-supported-in-this-environment"));var n=Ge(e);z(e,t,gt);var i=qr(n,r);const s=new Yr(n,"signInViaPopup",t,i);return s.executeNotNull()}(this._delegate,e,vi))}async signInWithRedirect(e){return Ei(ci(),this._delegate,"operation-not-supported-in-this-environment"),await pi(this._delegate),sn(this._delegate,e,vi)}updateCurrentUser(e){return this._delegate.updateCurrentUser(e)}verifyPasswordResetCode(e){return async function(e,t){var r=(await Kt(k(e),t))["data"];return r.email}(this._delegate,e)}unwrap(){return this._delegate}_delete(){return this._delegate._delete()}linkUnderlyingAuth(){this._delegate.wrapped=()=>this}}function Si(e,t,r){let n=e;"function"!=typeof e&&({next:n,error:t,complete:r}=e);const i=n;return{next:e=>i(e&&bi.getOrCreate(e)),error:t,complete:r}}ki.Persistence=ui;class Ai{constructor(){this.providerId="phone",this._delegate=new Wr(gi(i.default.auth()))}static credential(e,t){return Wr.credential(e,t)}verifyPhoneNumber(e,t){return this._delegate.verifyPhoneNumber(e,t)}unwrap(){return this._delegate}}Ai.PHONE_SIGN_IN_METHOD=Wr.PHONE_SIGN_IN_METHOD,Ai.PROVIDER_ID=Wr.PROVIDER_ID;const Ri=G;class Pi{constructor(e,t,r=i.default.app()){var n;Ri(null===(n=r.options)||void 0===n?void 0:n.apiKey,"invalid-api-key",{appName:r.name}),this._delegate=new xr(r.auth(),e,t),this.type=this._delegate.type}clear(){this._delegate.clear()}render(){return this._delegate.render()}verify(){return this._delegate.verify()}}(ri=i.default).INTERNAL.registerComponent(new O("auth-compat",e=>{var t=e.getProvider("app-compat").getImmediate(),r=e.getProvider("auth");return new ki(t,r)},"PUBLIC").setServiceProps({ActionCodeInfo:{Operation:{EMAIL_SIGNIN:L.EMAIL_SIGNIN,PASSWORD_RESET:L.PASSWORD_RESET,RECOVER_EMAIL:L.RECOVER_EMAIL,REVERT_SECOND_FACTOR_ADDITION:L.REVERT_SECOND_FACTOR_ADDITION,VERIFY_AND_CHANGE_EMAIL:L.VERIFY_AND_CHANGE_EMAIL,VERIFY_EMAIL:L.VERIFY_EMAIL}},EmailAuthProvider:vt,FacebookAuthProvider:It,GithubAuthProvider:Tt,GoogleAuthProvider:wt,OAuthProvider:yt,SAMLAuthProvider:Et,PhoneAuthProvider:Ai,PhoneMultiFactorGenerator:Fn,RecaptchaVerifier:Pi,TwitterAuthProvider:kt,Auth:ki,AuthCredential:nt,Error:g}).setInstantiationMode("LAZY").setMultipleInstances(!1)),ri.registerVersion("@firebase/auth-compat","0.5.14")}).apply(this,arguments)}catch(e){throw console.error(e),new Error("Cannot instantiate firebase-auth-compat.js - be sure to load firebase-app.js first.")}});
//# sourceMappingURL=firebase-auth-compat.js.map
