import { ref, reactive } from 'vue'

// Global notification state
const notifications = ref([])
let notificationId = 0

export function useNotifications() {
  const addNotification = (notification) => {
    const id = ++notificationId
    const newNotification = {
      id,
      ...notification,
      timestamp: new Date()
    }
    
    notifications.value.push(newNotification)
    
    // Auto-remove after specified time or default
    const autoRemoveTime = notification.autoRemoveTime || 5000
    if (notification.autoDismiss !== false) {
      setTimeout(() => {
        removeNotification(id)
      }, autoRemoveTime)
    }
    
    return id
  }
  
  const removeNotification = (id) => {
    const index = notifications.value.findIndex(n => n.id === id)
    if (index > -1) {
      notifications.value.splice(index, 1)
    }
  }
  
  const clearAllNotifications = () => {
    notifications.value = []
  }
  
  // Convenience methods for different notification types
  const showSuccess = (title, message, options = {}) => {
    return addNotification({
      type: 'success',
      title,
      message,
      ...options
    })
  }
  
  const showError = (title, message, options = {}) => {
    return addNotification({
      type: 'error',
      title,
      message,
      autoDismiss: false, // Errors should be manually dismissed
      ...options
    })
  }
  
  const showWarning = (title, message, options = {}) => {
    return addNotification({
      type: 'warning',
      title,
      message,
      ...options
    })
  }
  
  const showInfo = (title, message, options = {}) => {
    return addNotification({
      type: 'info',
      title,
      message,
      ...options
    })
  }
  
  // Specific methods for link sharing notifications
  const showLinkShared = (sharedBy, method, options = {}) => {
    return addNotification({
      type: 'link_shared',
      title: 'Meeting Link Shared',
      message: `${sharedBy} shared the meeting link${method ? ` via ${method}` : ''}`,
      ...options
    })
  }
  
  const showUserJoinedViaLink = (userName, sharedBy, options = {}) => {
    return addNotification({
      type: 'user_joined',
      title: 'New Participant Joined',
      message: `${userName} joined via link shared by ${sharedBy}`,
      ...options
    })
  }
  
  const showInvitePrompt = (participantCount, options = {}) => {
    return addNotification({
      type: 'info',
      title: 'Invite Participants',
      message: participantCount === 1 
        ? 'You\'re alone in the meeting. Invite others to join!'
        : 'Share the meeting link to invite more participants',
      actions: [
        {
          label: 'Share Link',
          primary: true,
          action: 'show_invite'
        },
        {
          label: 'Dismiss',
          action: 'dismiss'
        }
      ],
      autoDismiss: false,
      ...options
    })
  }
  
  const showLinkSharingPolicyChanged = (newPolicy, changedBy, options = {}) => {
    const policyText = newPolicy === 'all' ? 'All participants' : 'Host only'
    return addNotification({
      type: 'info',
      title: 'Link Sharing Policy Updated',
      message: `${changedBy} changed link sharing to: ${policyText}`,
      ...options
    })
  }
  
  return {
    notifications,
    addNotification,
    removeNotification,
    clearAllNotifications,
    showSuccess,
    showError,
    showWarning,
    showInfo,
    showLinkShared,
    showUserJoinedViaLink,
    showInvitePrompt,
    showLinkSharingPolicyChanged
  }
}
