{"name": "@firebase/analytics", "version": "0.10.8", "description": "A analytics package for new firebase packages", "author": "Firebase <<EMAIL>> (https://firebase.google.com/)", "main": "dist/index.cjs.js", "browser": "dist/esm/index.esm2017.js", "module": "dist/esm/index.esm2017.js", "esm5": "dist/esm/index.esm.js", "exports": {".": {"types": "./dist/analytics-public.d.ts", "require": "./dist/index.cjs.js", "esm5": "./dist/esm/index.esm.js", "default": "./dist/esm/index.esm2017.js"}, "./package.json": "./package.json"}, "files": ["dist"], "scripts": {"lint": "eslint -c .eslintrc.js '**/*.ts' --ignore-path '../../.gitignore'", "lint:fix": "eslint --fix -c .eslintrc.js '**/*.ts' --ignore-path '../../.gitignore'", "build": "rollup -c && yarn api-report", "build:release": "yarn build && yarn typings:public", "build:deps": "lerna run --scope @firebase/analytics --include-dependencies build", "dev": "rollup -c -w", "test": "run-p --npm-path npm lint test:all", "test:all": "run-p --npm-path npm test:browser test:integration", "test:ci": "node ../../scripts/run_tests_in_ci.js -s test:all", "test:browser": "karma start --nocache", "test:integration": "karma start ./karma.integration.conf.js --nocache", "trusted-type-check": "tsec -p tsconfig.json --noEmit", "api-report": "api-extractor run --local --verbose", "doc": "api-documenter markdown --input temp --output docs", "build:doc": "yarn build && yarn doc", "typings:public": "node ../../scripts/build/use_typings.js ./dist/analytics-public.d.ts"}, "peerDependencies": {"@firebase/app": "0.x"}, "dependencies": {"@firebase/installations": "0.6.9", "@firebase/logger": "0.4.2", "@firebase/util": "1.10.0", "@firebase/component": "0.6.9", "tslib": "^2.1.0"}, "license": "Apache-2.0", "devDependencies": {"@firebase/app": "0.10.11", "rollup": "2.79.1", "@rollup/plugin-commonjs": "21.1.0", "@rollup/plugin-json": "4.1.0", "@rollup/plugin-node-resolve": "13.3.0", "rollup-plugin-typescript2": "0.31.2", "typescript": "4.7.4"}, "repository": {"directory": "packages/analytics", "type": "git", "url": "git+https://github.com/firebase/firebase-js-sdk.git"}, "bugs": {"url": "https://github.com/firebase/firebase-js-sdk/issues"}, "typings": "./dist/analytics-public.d.ts", "nyc": {"extension": [".ts"], "reportDir": "./coverage/node"}}