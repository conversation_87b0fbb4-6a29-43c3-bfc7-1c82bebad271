import{d as V,a as U,b as A,u as D,o as J,c as O,r as v,e as z,f as H,g as u,h as s,w as x,v as M,i as N,F as j,j as C,k as q,l as W,m as g,t as w,n as Y,p as G}from"./index-DX9maXWO.js";import{n as T,c as Q,a as X}from"./meetings-CV11DH_e.js";import{u as F}from"./extensionsStore-PEOhYxsJ.js";const $={recording:!1,whiteboard:!1,breakoutRooms:!1,chat:!0,reactions:!0,virtualBackground:!1,audibleImpairedSystem:!1},Z=V("meetingConfig",{state:()=>({currentMeetingId:null,meetingSettings:{name:"TheMeet Meeting",requireApproval:!1,hostUserId:null},activeFeatures:{...$},activeExtensions:[],linkSharingPolicy:"host",joinTracking:[],isLoading:!1,error:null,_firebaseUnsubscribe:null}),getters:{isFeatureActive:t=>a=>!!t.activeFeatures[a],getExtensionConfig:t=>a=>{const i=t.activeExtensions.find(o=>o.id===a);return i?i.config:null},getAllActiveExtensions:t=>t.activeExtensions,getMeetingName:t=>t.meetingSettings.name,isHost:t=>a=>t.meetingSettings.hostUserId===a,getLinkSharingPolicy:t=>t.linkSharingPolicy,getJoinTracking:t=>t.joinTracking},actions:{initializeNewMeeting(t,a){this.meetingSettings={...this.meetingSettings,...t,hostUserId:a}},async loadMeetingConfiguration(t,a){this._firebaseUnsubscribe&&(this._firebaseUnsubscribe(),this._firebaseUnsubscribe=null),this.currentMeetingId=t,this.isLoading=!0,this.error=null;const i=U(A,"meetings",t);this._firebaseUnsubscribe=J(i,o=>{if(o.exists()){const r=o.data();this.meetingSettings={name:r.meetingName||"TheMeet Meeting",requireApproval:r.requireApproval||!1,hostUserId:r.createdBy},this.activeFeatures={...$,...r.features||{}};const c=F();this.activeExtensions=(r.extensions||[]).map(l=>{const m=c.getExtensionById(l.id);return m?{...m,config:l.config||{}}:{id:l.id,name:l.name||"Unknown Extension",config:l.config||{},manifestNotFound:!0}}),this.isLoading=!1}else this.error="Meeting not found or you do not have access.",this.isLoading=!1,console.error(`Meeting with ID ${t} not found.`)},o=>{this.error=`Error loading meeting: ${o.message}`,this.isLoading=!1,console.error("Firebase snapshot error:",o)})},async _updateFirebaseMeeting(t){if(!this.currentMeetingId){console.error("No currentMeetingId set, cannot update Firebase."),this.error="Cannot update: No active meeting.";return}try{const a=U(A,"meetings",this.currentMeetingId);await D(a,t)}catch(a){console.error("Error updating Firebase meeting:",a),this.error=`Failed to update meeting setting: ${a.message}`}},async toggleFeature(t){if(typeof this.activeFeatures[t]>"u"){console.warn(`Feature ${t} is not defined in activeFeatures state.`);return}const a=!this.activeFeatures[t];this.activeFeatures[t]=a,await this._updateFirebaseMeeting({[`features.${t}`]:a})},async addExtensionToMeeting(t){const i=F().getExtensionById(t);if(!i){this.error=`Extension manifest for ${t} not found.`,console.error(this.error);return}if(this.activeExtensions.some(c=>c.id===t)){console.warn(`Extension ${t} is already active.`);return}const o={...i,config:i.defaultConfig||{}};this.activeExtensions.push(o);const r=this.activeExtensions.map(c=>({id:c.id,name:c.name,config:c.config||{}}));await this._updateFirebaseMeeting({extensions:r})},async removeExtensionFromMeeting(t){this.activeExtensions=this.activeExtensions.filter(i=>i.id!==t);const a=this.activeExtensions.map(i=>({id:i.id,name:i.name,config:i.config||{}}));await this._updateFirebaseMeeting({extensions:a})},async updateExtensionMeetingConfig(t,a){const i=this.activeExtensions.findIndex(r=>r.id===t);if(i===-1){this.error=`Cannot update config: Extension ${t} not active.`,console.error(this.error);return}this.activeExtensions[i].config={...this.activeExtensions[i].config,...a};const o=this.activeExtensions.map(r=>({id:r.id,name:r.name,config:r.config||{}}));await this._updateFirebaseMeeting({extensions:o})},setMeetingHost(t){this.meetingSettings.hostUserId=t},async updateLinkSharingPolicy(t){t!=="host"&&t!=="all"||(this.linkSharingPolicy=t,await this._updateFirebaseMeeting({linkSharingPolicy:t}))},async logJoinEvent(t,a,i){const o={sharedBy:t,joinedBy:a,joinedName:i,joinedAt:Date.now()};this.joinTracking.push(o),await this._updateFirebaseMeeting({joinTracking:this.joinTracking})},resetMeetingConfig(){this._firebaseUnsubscribe&&(this._firebaseUnsubscribe(),this._firebaseUnsubscribe=null),this.currentMeetingId=null,this.meetingSettings={name:"TheMeet Meeting",requireApproval:!1,hostUserId:null},this.activeFeatures={recording:!1,whiteboard:!1,breakoutRooms:!1,chat:!0,reactions:!0,virtualBackground:!1,audibleImpairedSystem:!1},this.activeExtensions=[],this.isLoading=!1,this.error=null}}}),K={class:"w-full px-2 sm:px-4 py-6 max-w-3xl mx-auto flex flex-col gap-8"},ee={class:"bg-white dark:bg-gray-800 rounded-lg shadow p-4 sm:p-6 mb-8 flex flex-col gap-4"},te={class:"mb-4"},se={class:"mb-4"},ie={class:"mb-4"},ne={class:"flex items-center"},ae={class:"mb-6"},oe={class:"grid grid-cols-1 sm:grid-cols-2 gap-2"},re=["id","onUpdate:modelValue"],le=["for"],de={class:"mb-6"},ue={key:0,class:"text-center py-4"},ge={key:1,class:"text-center py-4"},ce={key:2,class:"grid grid-cols-1 sm:grid-cols-2 gap-2"},me=["onClick"],fe={class:"font-semibold text-gray-800 dark:text-white"},ve={class:"text-xs text-gray-500 dark:text-gray-400"},pe={class:"text-sm text-gray-600 dark:text-gray-300"},xe=["disabled"],he={key:0},be={key:1},ye={class:"bg-white dark:bg-gray-800 rounded-lg shadow p-4 sm:p-6 flex flex-col gap-4"},ke={class:"mb-4"},Ee={class:"mb-4"},Me=["disabled"],we={key:0},Se={key:1},Ue={__name:"Home",setup(t){const a=W(),i=Z(),o=F(),r=O(),c=v(i.meetingSettings.name||"My Awesome Meeting"),l=v(localStorage.getItem("userName")||""),m=v(""),S=v(i.meetingSettings.requireApproval||!1),p=v({...i.activeFeatures}),h=v([]),y=v(!1),b=v(!1),_=z(()=>o.getMarketplaceExtensions);H(async()=>{var d;o.extensions.length===0&&!o.isLoading&&await o.fetchExtensions(),!l.value&&((d=r.user)!=null&&d.name)&&(l.value=r.user.name),I.value.forEach(e=>{typeof p.value[e.key]>"u"&&(p.value[e.key]=i.activeFeatures[e.key]===void 0?!1:i.activeFeatures[e.key])})});const L=d=>{const e=h.value.indexOf(d);e>-1?h.value.splice(e,1):h.value.push(d)},B=async()=>{var d;if(!l.value.trim()){alert("Please enter your name.");return}try{y.value=!0;let e=((d=r.user)==null?void 0:d.id)||localStorage.getItem("userId");e||(e=T(),localStorage.setItem("userId",e)),localStorage.setItem("userName",l.value);const n={name:c.value||"TheMeet Meeting",requireApproval:S.value};i.initializeNewMeeting(n,e),Object.keys(p.value).forEach(f=>{Object.prototype.hasOwnProperty.call(i.activeFeatures,f),i.activeFeatures[f]=p.value[f]});const k=h.value.map(f=>{const E=o.getExtensionById(f);return E?{id:E.id,name:E.name,config:E.defaultConfig||{}}:null}).filter(f=>f!==null);i.activeExtensions=k;const R=await Q(e,i.meetingSettings.name,i.meetingSettings.requireApproval,i.activeFeatures,i.activeExtensions);a.push(`/meeting/${R}`)}catch(e){console.error("Error creating meeting:",e),alert(`Failed to create meeting: ${e.message}. Please try again.`)}finally{y.value=!1}},P=async()=>{var d;if(!(!m.value.trim()||!l.value.trim()))try{if(b.value=!0,!await X(m.value)){alert("Meeting not found or has ended."),b.value=!1;return}let n=((d=r.user)==null?void 0:d.id)||localStorage.getItem("userId");n||(n=T(),localStorage.setItem("userId",n)),localStorage.setItem("userName",l.value),a.push(`/meeting/${m.value}`)}catch(e){console.error("Error joining meeting:",e),alert("Failed to join meeting. Please try again.")}finally{b.value=!1}},I=v([{key:"recording",label:"Enable Recording"},{key:"whiteboard",label:"Enable Whiteboard"},{key:"breakoutRooms",label:"Enable Breakout Rooms"},{key:"virtualBackground",label:"Enable Virtual Backgrounds"},{key:"audibleImpairedSystem",label:"Enable Audible Impaired System"},{key:"reactions",label:"Enable Reactions"}]);return(d,e)=>(g(),u("div",K,[e[16]||(e[16]=s("div",{class:"text-center mb-8"},[s("h1",{class:"text-4xl font-bold text-gray-900 dark:text-white mb-4"},"Welcome to TheMeet"),s("p",{class:"text-xl text-gray-600 dark:text-gray-300"}," A modern web app for hosting seamless video meetings ")],-1)),s("div",ee,[e[12]||(e[12]=s("h2",{class:"text-2xl font-semibold text-gray-900 dark:text-white mb-4"},"Start a New Meeting",-1)),s("div",te,[e[5]||(e[5]=s("label",{for:"meetingName",class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"}," Meeting Name (optional) ",-1)),x(s("input",{id:"meetingName","onUpdate:modelValue":e[0]||(e[0]=n=>c.value=n),type:"text",placeholder:"My Awesome Meeting",class:"input"},null,512),[[M,c.value]])]),s("div",se,[e[6]||(e[6]=s("label",{for:"userName",class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"}," Your Name ",-1)),x(s("input",{id:"userName","onUpdate:modelValue":e[1]||(e[1]=n=>l.value=n),type:"text",placeholder:"John Doe",class:"input",required:""},null,512),[[M,l.value]])]),s("div",ie,[s("div",ne,[x(s("input",{id:"requireApproval","onUpdate:modelValue":e[2]||(e[2]=n=>S.value=n),type:"checkbox",class:"h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded"},null,512),[[N,S.value]]),e[7]||(e[7]=s("label",{for:"requireApproval",class:"ml-2 block text-sm text-gray-700 dark:text-gray-300"}," Require approval for participants to join ",-1))])]),s("div",ae,[e[8]||(e[8]=s("h3",{class:"text-lg font-medium text-gray-900 dark:text-white mb-2"},"Core Features",-1)),s("div",oe,[(g(!0),u(j,null,C(I.value,n=>(g(),u("div",{key:n.key,class:"flex items-center"},[x(s("input",{id:`feature-${n.key}`,"onUpdate:modelValue":k=>p.value[n.key]=k,type:"checkbox",class:"h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded"},null,8,re),[[N,p.value[n.key]]]),s("label",{for:`feature-${n.key}`,class:"ml-2 block text-sm text-gray-700 dark:text-gray-300"},w(n.label),9,le)]))),128))])]),s("div",de,[e[11]||(e[11]=s("h3",{class:"text-lg font-medium text-gray-900 dark:text-white mb-2"},"Select Extensions (Optional)",-1)),q(o).isLoading&&_.value.length===0?(g(),u("div",ue,e[9]||(e[9]=[s("p",{class:"text-gray-500 dark:text-gray-400"},"Loading extensions...",-1)]))):!q(o).isLoading&&_.value.length===0?(g(),u("div",ge,e[10]||(e[10]=[s("p",{class:"text-gray-500 dark:text-gray-400"},"No extensions currently available.",-1)]))):(g(),u("div",ce,[(g(!0),u(j,null,C(_.value,n=>(g(),u("div",{key:n.id,onClick:k=>L(n.id),class:Y(["extension-card",{selected:h.value.includes(n.id)}])},[s("h4",fe,[G(w(n.name)+" ",1),s("span",ve,"v"+w(n.version),1)]),s("p",pe,w(n.description),1)],10,me))),128))]))]),s("button",{onClick:B,class:"w-full py-3 px-4 rounded bg-blue-600 text-white font-semibold text-lg shadow hover:bg-blue-700 transition disabled:opacity-60 disabled:cursor-not-allowed",disabled:!l.value.trim()||y.value},[y.value?(g(),u("span",he,"Creating...")):(g(),u("span",be,"Start Meeting Now"))],8,xe)]),s("div",ye,[e[15]||(e[15]=s("h2",{class:"text-2xl font-semibold text-gray-900 dark:text-white mb-4"},"Join a Meeting",-1)),s("div",ke,[e[13]||(e[13]=s("label",{for:"meetingId",class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"}," Meeting ID ",-1)),x(s("input",{id:"meetingId","onUpdate:modelValue":e[3]||(e[3]=n=>m.value=n),type:"text",placeholder:"Enter meeting ID",class:"input",required:""},null,512),[[M,m.value]])]),s("div",Ee,[e[14]||(e[14]=s("label",{for:"joinUserName",class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"}," Your Name ",-1)),x(s("input",{id:"joinUserName","onUpdate:modelValue":e[4]||(e[4]=n=>l.value=n),type:"text",placeholder:"John Doe",class:"input w-full py-2 pl-10 text-sm text-gray-700 dark:text-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-gray-600 focus:border-transparent",required:""},null,512),[[M,l.value]])]),s("button",{onClick:P,class:"w-full py-3 px-4 rounded bg-gray-700 text-white font-semibold text-lg shadow hover:bg-gray-800 transition disabled:opacity-60 disabled:cursor-not-allowed",disabled:!m.value.trim()||!l.value.trim()||b.value},[b.value?(g(),u("span",we,"Joining...")):(g(),u("span",Se,"Join Meeting"))],8,Me)])]))}};export{Ue as default};
