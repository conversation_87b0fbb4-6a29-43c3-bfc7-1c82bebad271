import{_ as x,u as y,r as m,o as b,M as h,a as i,b as s,d as v,t as n,w as u,g,G as w,m as l,n as f}from"./index-2cGdSAEs.js";import{u as k}from"./extensionsStore-DVoXHZtn.js";const E={class:"analytics-container"},D={key:0,class:"loading-message"},V={key:1,class:"error-message"},S={key:2,class:"analytics-content"},A={class:"analytics-header"},B={class:"stats-grid"},N={class:"stat-card total-downloads"},C={class:"stat-value"},I={class:"stat-card last-updated"},L={class:"stat-value-small"},T={class:"stat-card version-info"},U={class:"stat-value-small"},M={class:"actions-footer"},P={__name:"AnalyticsView",setup($){const _=h(),d=y(),r=k(),t=m(null),c=m(!0);b(async()=>{var o;c.value=!0;const e=_.params.id;r.extensions.length===0&&await r.fetchExtensions();const a=r.getExtensionById(e);a&&d.user&&a.dev_metadata.author_id===d.user.id?t.value=a:(t.value=null,console.warn(`User ${(o=d.user)==null?void 0:o.id} not authorized for extension ${e} or extension not found.`)),c.value=!1});function p(e){if(!e)return"N/A";const a={year:"numeric",month:"short",day:"numeric"};return new Date(e).toLocaleDateString(void 0,a)}return(e,a)=>{const o=g("router-link");return l(),i("div",E,[c.value?(l(),i("div",D,"Loading analytics data...")):t.value?(l(),i("div",S,[s("div",A,[s("h1",null,'Analytics for "'+n(t.value.name)+'"',1),v(o,{to:{name:"Dashboard"},class:"back-button"},{default:u(()=>a[0]||(a[0]=[s("i",{class:"fas fa-arrow-left"},null,-1),f(" Back to Dashboard ")])),_:1,__:[0]})]),s("div",B,[s("div",N,[a[1]||(a[1]=s("div",{class:"stat-icon"},[s("i",{class:"fas fa-users"})],-1)),s("div",C,n(t.value.downloads),1),a[2]||(a[2]=s("div",{class:"stat-label"},"Total Installs",-1))]),s("div",I,[a[3]||(a[3]=s("div",{class:"stat-icon"},[s("i",{class:"fas fa-clock"})],-1)),s("div",L,n(p(t.value.dev_metadata.updated_at)),1),a[4]||(a[4]=s("div",{class:"stat-label"},"Last Updated",-1))]),s("div",T,[a[5]||(a[5]=s("div",{class:"stat-icon"},[s("i",{class:"fas fa-code-branch"})],-1)),s("div",U,"v"+n(t.value.version),1),a[6]||(a[6]=s("div",{class:"stat-label"},"Current Version",-1))]),a[7]||(a[7]=w('<div class="stat-card chart-placeholder" data-v-07148a07><div class="stat-icon" data-v-07148a07><i class="fas fa-chart-bar" data-v-07148a07></i></div><div class="stat-value-small" data-v-07148a07>Downloads Over Time</div><div class="stat-label" data-v-07148a07>(Chart coming soon)</div></div><div class="stat-card chart-placeholder" data-v-07148a07><div class="stat-icon" data-v-07148a07><i class="fas fa-map-marked-alt" data-v-07148a07></i></div><div class="stat-value-small" data-v-07148a07>Usage Heatmap</div><div class="stat-label" data-v-07148a07>(Feature coming soon)</div></div>',2))]),s("div",M,[v(o,{to:{name:"ExtensionEditor",params:{id:t.value.id}},class:"action-btn edit-btn"},{default:u(()=>a[8]||(a[8]=[s("i",{class:"fas fa-edit"},null,-1),f(" Edit Extension ")])),_:1,__:[8]},8,["to"]),v(o,{to:{name:"ExtensionDetail",params:{id:t.value.id}},class:"action-btn view-btn"},{default:u(()=>a[9]||(a[9]=[s("i",{class:"fas fa-eye"},null,-1),f(" View Public Page ")])),_:1,__:[9]},8,["to"])])])):(l(),i("div",V," Extension not found or you do not have permission to view its analytics. "))])}}},G=x(P,[["__scopeId","data-v-07148a07"]]);export{G as default};
