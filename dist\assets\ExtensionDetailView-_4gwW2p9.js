import{_ as I,r as c,I as B,f as N,J as S,g as a,s as v,k as _,p as o,x,H as w,A as L,h as e,t as l,F as p,j as m,l as V,m as n,q as C}from"./index-DX9maXWO.js";import{u as D}from"./extensionsStore-PEOhYxsJ.js";import{B as P}from"./BlockRenderer-DmC888_9.js";const j={class:"extension-detail-container"},F={key:0,class:"loading-message"},J={key:1,class:"not-found-message"},M={key:2,class:"extension-content"},R={class:"header"},U=["src","alt"],$={class:"version-tag-large"},A={class:"author"},O={class:"description-full"},q={class:"tags-detail"},H={class:"actions-bar"},T={class:"downloads-detail"},z={class:"details-layout"},G={class:"preview-section"},K={class:"preview-box"},Q={key:1},W={class:"event-debugger-preview"},X={key:0},Y={key:1},Z={class:"json-view-section"},ee={class:"json-code-block"},se={__name:"ExtensionDetailView",setup(te){const f=B();V();const i=D(),h=c(f.params.id),s=c(null),r=c([]);async function k(){i.extensions.length===0&&await i.fetchExtensions(),s.value=i.getExtensionById(h.value)}N(k),S(()=>f.params.id,d=>{d&&(h.value=d,k(),r.value=[])});async function y(){if(!s.value)return;if(console.log(`Installing extension: ${s.value.name}`),await i.incrementDownloadCount(s.value.id)){const t=i.getExtensionById(s.value.id);t&&(s.value={...t}),alert(`${s.value.name} installed successfully! (Simulated)`)}else alert(`Failed to install ${s.value.name}. (Simulated)`)}function E(d){r.value.unshift({eventName:d.eventName,blockId:d.blockId}),r.value.length>5&&r.value.pop()}return(d,t)=>{const g=L("router-link");return n(),a("div",j,[_(i).isLoading?(n(),a("div",F,"Loading extension details...")):v("",!0),!_(i).isLoading&&!s.value?(n(),a("div",J,[t[1]||(t[1]=o(" Extension not found. ")),x(g,{to:{name:"Marketplace"}},{default:w(()=>t[0]||(t[0]=[o("Back to Marketplace")])),_:1,__:[0]})])):v("",!0),s.value&&!_(i).isLoading?(n(),a("div",M,[e("div",R,[e("img",{src:s.value.dev_metadata.author_pic||"https://via.placeholder.com/50",alt:s.value.dev_metadata.author_name,class:"dev-avatar-large"},null,8,U),e("div",null,[e("h1",null,[o(l(s.value.name)+" ",1),e("span",$,"v"+l(s.value.version),1)]),e("p",A,[t[2]||(t[2]=o(" By ")),x(g,{to:{name:"UserProfile",params:{username:s.value.dev_metadata.author_id}}},{default:w(()=>[o(l(s.value.dev_metadata.author_name||"Unknown Developer"),1)]),_:1},8,["to"])])])]),e("p",O,l(s.value.description),1),e("div",q,[(n(!0),a(p,null,m(s.value.tags,u=>(n(),a("span",{key:u,class:"tag"},l(u),1))),128))]),e("div",H,[e("button",{onClick:y,class:"install-button"},t[3]||(t[3]=[e("i",{class:"fas fa-download"},null,-1),o(" Install Extension ")])),e("p",T,[t[4]||(t[4]=e("i",{class:"fas fa-users"},null,-1)),o(" "+l(s.value.downloads)+" Installs ",1)])]),e("div",z,[e("div",G,[t[6]||(t[6]=e("h3",null,[e("i",{class:"fas fa-eye"}),o(" Live Preview")],-1)),e("div",K,[s.value.ui_blocks&&s.value.ui_blocks.length>0?(n(!0),a(p,{key:0},m(s.value.ui_blocks,u=>(n(),C(P,{key:u.id,block:u,onBlockEvent:E},null,8,["block"]))),128)):(n(),a("p",Q,"No UI preview available for this extension."))]),e("div",W,[t[5]||(t[5]=e("h4",null,"Preview Events:",-1)),r.value.length>0?(n(),a("ul",X,[(n(!0),a(p,null,m(r.value,(u,b)=>(n(),a("li",{key:b},l(u.eventName)+" on '"+l(u.blockId)+"' ",1))),128))])):(n(),a("p",Y,"Interact with the preview to see events here."))])]),e("div",Z,[t[7]||(t[7]=e("h3",null,[e("i",{class:"fas fa-code"}),o(" Extension JSON")],-1)),e("pre",ee,l(JSON.stringify(s.value,null,2)),1)])])])):v("",!0)])}}},le=I(se,[["__scopeId","data-v-36600090"]]);export{le as default};
