import{u as m}from"./extensionsStore-C2g9n1A4.js";import{o as p,a as r,b as e,f as i,h as n,t as l,F as x,k as g,g as b,m as o,n as d,d as f,w as y}from"./index-oz5Qducj.js";const _={class:"p-4 sm:p-8 max-w-6xl mx-auto"},h={key:0,class:"text-center text-lg py-8 text-gray-500"},k={key:1,class:"text-center text-lg py-8 text-red-500"},v={key:2,class:"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6"},w=["src","alt"],E={class:"flex flex-col flex-1 px-5 pb-5 pt-2"},M={class:"text-lg font-semibold text-gray-900 dark:text-white mb-1"},V={class:"ml-2 px-2 py-0.5 rounded bg-gray-100 dark:bg-gray-700 text-xs text-gray-600 dark:text-gray-300 align-middle"},N={class:"text-gray-600 dark:text-gray-300 text-sm mb-2 flex-1"},D={class:"flex flex-wrap gap-2 mb-3"},L={class:"flex justify-between items-center border-t border-gray-100 dark:border-gray-700 pt-3 mt-auto"},B={class:"text-xs text-gray-500 flex items-center"},C={key:0,class:"col-span-full text-center text-gray-400 py-8"},q={__name:"MarketplaceView",setup(S){const t=m();return p(()=>{t.extensions.length===0&&t.fetchExtensions()}),(j,a)=>{const u=b("router-link");return o(),r("div",_,[a[2]||(a[2]=e("h1",{class:"text-3xl sm:text-4xl font-bold mb-2 text-center text-gray-800 dark:text-white"},"Extension Marketplace",-1)),a[3]||(a[3]=e("p",{class:"mb-6 text-center text-gray-600 dark:text-gray-300 text-base sm:text-lg"},"Discover and install extensions built by TheMeet community.",-1)),n(t).isLoading?(o(),r("div",h,"Loading extensions...")):i("",!0),n(t).error?(o(),r("div",k,"Error fetching extensions: "+l(n(t).error),1)):i("",!0),!n(t).isLoading&&!n(t).error?(o(),r("div",v,[(o(!0),r(x,null,g(n(t).getMarketplaceExtensions,s=>(o(),r("div",{key:s.id,class:"bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-100 dark:border-gray-700 flex flex-col transition-transform duration-150 hover:scale-[1.02] hover:shadow-lg focus-within:scale-[1.02] focus-within:shadow-lg"},[e("img",{src:s.dev_metadata.author_pic||"https://via.placeholder.com/40",alt:s.dev_metadata.author_name,class:"w-12 h-12 rounded-full mt-5 ml-5 mb-0 object-cover border border-gray-200 dark:border-gray-600"},null,8,w),e("div",E,[e("h3",M,[d(l(s.name)+" ",1),e("span",V,"v"+l(s.version),1)]),e("p",N,l(s.description),1),e("div",D,[(o(!0),r(x,null,g(s.tags,c=>(o(),r("span",{key:c,class:"bg-blue-50 dark:bg-blue-900 text-blue-600 dark:text-blue-200 px-2 py-0.5 rounded-full text-xs"},l(c),1))),128))]),e("div",L,[e("span",B,[a[0]||(a[0]=e("i",{class:"fas fa-download mr-1"},null,-1)),d(l(s.downloads)+" downloads",1)]),f(u,{to:{name:"ExtensionDetail",params:{id:s.id}},class:"inline-block bg-blue-600 hover:bg-blue-700 text-white text-xs font-medium px-4 py-2 rounded transition-colors duration-150 focus:outline-none focus:ring-2 focus:ring-blue-400 focus:ring-offset-2"},{default:y(()=>a[1]||(a[1]=[d("View Details")])),_:2,__:[1]},1032,["to"])])])]))),128)),n(t).getMarketplaceExtensions.length===0?(o(),r("div",C," No extensions found in the marketplace yet. ")):i("",!0)])):i("",!0)])}}};export{q as default};
