<template>
  <div v-if="visible" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6 max-w-lg w-full mx-4">
      <div class="flex items-center justify-between mb-4">
        <h2 class="text-xl font-semibold text-gray-900 dark:text-white">
          <i class="fas fa-share-alt mr-2 text-primary"></i>
          Invite Participants
        </h2>
        <button @click="$emit('close')" class="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300">
          <i class="fas fa-times text-lg"></i>
        </button>
      </div>

      <div class="space-y-4">
        <!-- Meeting Link Section -->
        <div>
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            <i class="fas fa-link mr-1"></i>
            Meeting Link
          </label>
          <div class="flex items-center">
            <input
              :value="inviteLink"
              readonly
              class="flex-1 p-3 border rounded-l-lg bg-gray-50 dark:bg-gray-700 text-gray-900 dark:text-white text-sm font-mono"
              id="invite-link-input"
            />
            <button
              @click="copyLink"
              class="px-4 py-3 bg-primary text-white rounded-r-lg hover:bg-primary-dark transition-colors flex items-center"
              :class="{ 'bg-green-500 hover:bg-green-600': copySuccess }"
            >
              <i :class="copySuccess ? 'fas fa-check' : 'fas fa-copy'" class="mr-1"></i>
              {{ copySuccess ? 'Copied!' : 'Copy' }}
            </button>
          </div>
        </div>

        <!-- Quick Share Options -->
        <div>
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            <i class="fas fa-share mr-1"></i>
            Quick Share
          </label>
          <div class="grid grid-cols-2 gap-2">
            <button
              @click="shareViaEmail"
              class="flex items-center justify-center p-3 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
            >
              <i class="fas fa-envelope mr-2 text-blue-500"></i>
              <span class="text-sm">Email</span>
            </button>
            <button
              @click="shareViaWhatsApp"
              class="flex items-center justify-center p-3 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
            >
              <i class="fab fa-whatsapp mr-2 text-green-500"></i>
              <span class="text-sm">WhatsApp</span>
            </button>
          </div>
        </div>

        <!-- Meeting Info -->
        <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-3">
          <h3 class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Meeting Information</h3>
          <div class="space-y-1 text-xs text-gray-600 dark:text-gray-400">
            <div><strong>Meeting ID:</strong> {{ meetingId }}</div>
            <div><strong>Host:</strong> {{ hostName }}</div>
            <div v-if="meetingName"><strong>Title:</strong> {{ meetingName }}</div>
          </div>
        </div>

        <!-- Security Notice -->
        <div class="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-3">
          <div class="flex items-start">
            <i class="fas fa-shield-alt text-blue-500 mr-2 mt-0.5"></i>
            <div class="text-xs text-blue-700 dark:text-blue-300">
              <strong>Security Notice:</strong> External users joining via this link will be prompted to enter their name before joining the meeting.
              {{ linkSharingPolicy === 'host' ? 'Only you can share this link.' : 'All participants can share this link.' }}
            </div>
          </div>
        </div>
      </div>

      <div class="flex justify-between items-center mt-6">
        <button
          @click="trackLinkShare"
          class="text-sm text-primary hover:text-primary-dark flex items-center"
        >
          <i class="fas fa-chart-line mr-1"></i>
          Mark as Shared
        </button>
        <div class="flex space-x-2">
          <button
            @click="$emit('close')"
            class="px-4 py-2 bg-gray-200 dark:bg-gray-700 text-gray-800 dark:text-gray-200 rounded-lg hover:bg-gray-300 dark:hover:bg-gray-600 transition-colors"
          >
            Close
          </button>
        </div>
      </div>
    </div>
  </div>
</template>
<script setup>
import { ref, watch, computed } from 'vue';

const props = defineProps({
  visible: Boolean,
  inviteLink: String,
  meetingId: String,
  meetingName: String,
  hostName: String,
  linkSharingPolicy: {
    type: String,
    default: 'host'
  }
});

const emit = defineEmits(['close', 'linkShared']);

const copySuccess = ref(false);

const shareMessage = computed(() => {
  const meetingTitle = props.meetingName || `Meeting ${props.meetingId}`;
  return `Join me in "${meetingTitle}" on TheMeet!\n\nMeeting Link: ${props.inviteLink}\nMeeting ID: ${props.meetingId}\n\nHosted by: ${props.hostName}`;
});

function copyLink() {
  navigator.clipboard.writeText(props.inviteLink).then(() => {
    copySuccess.value = true;
    setTimeout(() => (copySuccess.value = false), 2000);
    trackLinkShare('copy');
  }).catch(err => {
    console.error('Failed to copy link:', err);
    // Fallback for older browsers
    const textArea = document.createElement('textarea');
    textArea.value = props.inviteLink;
    document.body.appendChild(textArea);
    textArea.select();
    document.execCommand('copy');
    document.body.removeChild(textArea);
    copySuccess.value = true;
    setTimeout(() => (copySuccess.value = false), 2000);
    trackLinkShare('copy');
  });
}

function shareViaEmail() {
  const subject = encodeURIComponent(`Join me in "${props.meetingName || 'TheMeet Meeting'}"`);
  const body = encodeURIComponent(shareMessage.value);
  window.open(`mailto:?subject=${subject}&body=${body}`);
  trackLinkShare('email');
}

function shareViaWhatsApp() {
  const text = encodeURIComponent(shareMessage.value);
  window.open(`https://wa.me/?text=${text}`);
  trackLinkShare('whatsapp');
}

function trackLinkShare(method = 'manual') {
  emit('linkShared', {
    method,
    timestamp: new Date().toISOString(),
    link: props.inviteLink
  });
}

watch(() => props.visible, v => {
  if (!v) {
    copySuccess.value = false;
  }
});
</script>
