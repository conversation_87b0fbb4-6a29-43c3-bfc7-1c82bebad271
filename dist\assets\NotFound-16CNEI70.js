import{_ as r,g as s,h as e,x as a,H as n,A as l,m as i,p as d}from"./index-DX9maXWO.js";const m={},x={class:"min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900 py-12 px-4 sm:px-6 lg:px-8"},c={class:"max-w-md w-full space-y-8 text-center"};function p(g,t){const o=l("router-link");return i(),s("div",x,[e("div",c,[t[1]||(t[1]=e("div",null,[e("h1",{class:"text-6xl font-extrabold text-primary dark:text-primary-light"},"404"),e("h2",{class:"mt-6 text-3xl font-bold text-gray-900 dark:text-white"},"Page Not Found"),e("p",{class:"mt-2 text-sm text-gray-600 dark:text-gray-400"}," The page you're looking for doesn't exist or has been moved. ")],-1)),e("div",null,[a(o,{to:"/",class:"btn btn-primary inline-flex items-center"},{default:n(()=>t[0]||(t[0]=[e("svg",{xmlns:"http://www.w3.org/2000/svg",class:"h-5 w-5 mr-2",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6"})],-1),d(" Go Home ")])),_:1,__:[0]})])])])}const u=r(m,[["render",p]]);export{u as default};
