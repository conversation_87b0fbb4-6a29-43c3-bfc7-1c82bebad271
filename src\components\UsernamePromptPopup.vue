<template>
  <div v-if="visible" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6 max-w-md w-full mx-4">
      <div class="text-center mb-6">
        <div class="w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-4">
          <i class="fas fa-user-plus text-2xl text-primary"></i>
        </div>
        <h2 class="text-xl font-semibold text-gray-900 dark:text-white mb-2">Join Meeting</h2>
        <p class="text-sm text-gray-600 dark:text-gray-400">
          You're joining via a shared link. Please enter your name to continue.
        </p>
      </div>

      <!-- Meeting Info -->
      <div v-if="meetingInfo" class="bg-gray-50 dark:bg-gray-700 rounded-lg p-3 mb-4">
        <div class="text-center">
          <h3 class="font-medium text-gray-900 dark:text-white">{{ meetingInfo.name || 'TheMeet Meeting' }}</h3>
          <p class="text-xs text-gray-600 dark:text-gray-400 mt-1">
            Hosted by {{ meetingInfo.hostName || 'Host' }}
          </p>
          <div class="flex items-center justify-center mt-2 text-xs text-gray-500 dark:text-gray-400">
            <i class="fas fa-users mr-1"></i>
            {{ meetingInfo.participantCount || 0 }} participant{{ (meetingInfo.participantCount || 0) !== 1 ? 's' : '' }}
          </div>
        </div>
      </div>

      <form @submit.prevent="submitUsername" class="space-y-4">
        <div>
          <label for="username" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Your Name <span class="text-red-500">*</span>
          </label>
          <input
            id="username"
            v-model="username"
            type="text"
            class="w-full p-3 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:ring-2 focus:ring-primary focus:border-transparent"
            placeholder="Enter your full name"
            maxlength="50"
            required
            autocomplete="name"
            :disabled="isJoining"
          />
          <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">
            This name will be visible to other participants
          </p>
        </div>

        <!-- Security Notice -->
        <div class="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-3">
          <div class="flex items-start">
            <i class="fas fa-shield-alt text-blue-500 mr-2 mt-0.5 text-sm"></i>
            <div class="text-xs text-blue-700 dark:text-blue-300">
              <strong>Secure Meeting:</strong> Your join will be tracked for security purposes.
              The host can see who shared the link that you used to join.
            </div>
          </div>
        </div>

        <div class="flex justify-end space-x-3 pt-2">
          <button
            type="button"
            @click="$emit('cancel')"
            class="px-4 py-2 bg-gray-200 dark:bg-gray-700 text-gray-800 dark:text-gray-200 rounded-lg hover:bg-gray-300 dark:hover:bg-gray-600 transition-colors"
            :disabled="isJoining"
          >
            Cancel
          </button>
          <button
            type="submit"
            class="px-6 py-2 bg-primary text-white rounded-lg hover:bg-primary-dark transition-colors flex items-center"
            :disabled="!username.trim() || isJoining"
          >
            <i v-if="isJoining" class="fas fa-spinner fa-spin mr-2"></i>
            <i v-else class="fas fa-sign-in-alt mr-2"></i>
            {{ isJoining ? 'Joining...' : 'Join Meeting' }}
          </button>
        </div>
      </form>
    </div>
  </div>
</template>
<script setup>
import { ref, watch, onMounted } from 'vue';

const props = defineProps({
  visible: Boolean,
  meetingInfo: {
    type: Object,
    default: () => ({})
  },
  sharedBy: String,
  joinViaLink: Boolean
});

const emit = defineEmits(['submit', 'cancel']);

const username = ref('');
const isJoining = ref(false);

function submitUsername() {
  if (username.value.trim() && !isJoining.value) {
    isJoining.value = true;

    // Emit with additional context for tracking
    emit('submit', {
      username: username.value.trim(),
      joinMethod: 'shared_link',
      sharedBy: props.sharedBy,
      timestamp: new Date().toISOString()
    });

    // Reset after a delay to allow for processing
    setTimeout(() => {
      username.value = '';
      isJoining.value = false;
    }, 1000);
  }
}

// Auto-focus on username input when popup becomes visible
watch(() => props.visible, (newVal) => {
  if (!newVal) {
    username.value = '';
    isJoining.value = false;
  } else {
    // Focus the input after the popup is rendered
    setTimeout(() => {
      const input = document.getElementById('username');
      if (input) input.focus();
    }, 100);
  }
});

// Pre-fill username if available in localStorage but allow editing
onMounted(() => {
  const savedName = localStorage.getItem('userName');
  if (savedName && !username.value) {
    username.value = savedName;
  }
});
</script>
