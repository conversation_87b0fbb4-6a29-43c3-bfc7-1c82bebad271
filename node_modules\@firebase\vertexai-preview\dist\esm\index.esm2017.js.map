{"version": 3, "file": "index.esm2017.js", "sources": ["../../src/constants.ts", "../../src/service.ts", "../../src/errors.ts", "../../src/requests/request.ts", "../../src/types/enums.ts", "../../src/types/requests.ts", "../../src/requests/response-helpers.ts", "../../src/requests/stream-reader.ts", "../../src/methods/generate-content.ts", "../../src/requests/request-helpers.ts", "../../src/methods/chat-session-helpers.ts", "../../src/methods/chat-session.ts", "../../src/methods/count-tokens.ts", "../../src/models/generative-model.ts", "../../src/api.ts", "../../src/index.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2024 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { version } from '../package.json';\n\nexport const VERTEX_TYPE = 'vertexAI';\n\nexport const DEFAULT_LOCATION = 'us-central1';\n\nexport const DEFAULT_BASE_URL = 'https://firebaseml.googleapis.com';\n\nexport const DEFAULT_API_VERSION = 'v2beta';\n\nexport const PACKAGE_VERSION = version;\n\nexport const LANGUAGE_TAG = 'gl-js';\n", "/**\n * @license\n * Copyright 2024 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { FirebaseApp, _FirebaseService } from '@firebase/app';\nimport { VertexAI, VertexAIOptions } from './public-types';\nimport {\n  AppCheckInternalComponentName,\n  FirebaseAppCheckInternal\n} from '@firebase/app-check-interop-types';\nimport { Provider } from '@firebase/component';\nimport {\n  FirebaseAuthInternal,\n  FirebaseAuthInternalName\n} from '@firebase/auth-interop-types';\nimport { DEFAULT_LOCATION } from './constants';\n\nexport class VertexAIService implements VertexAI, _FirebaseService {\n  auth: FirebaseAuthInternal | null;\n  appCheck: FirebaseAppCheckInternal | null;\n  location: string;\n\n  constructor(\n    public app: FirebaseApp,\n    authProvider?: Provider<FirebaseAuthInternalName>,\n    appCheckProvider?: Provider<AppCheckInternalComponentName>,\n    public options?: VertexAIOptions\n  ) {\n    const appCheck = appCheckProvider?.getImmediate({ optional: true });\n    const auth = authProvider?.getImmediate({ optional: true });\n    this.auth = auth || null;\n    this.appCheck = appCheck || null;\n    this.location = this.options?.location || DEFAULT_LOCATION;\n  }\n\n  _delete(): Promise<void> {\n    return Promise.resolve();\n  }\n}\n", "/**\n * @license\n * Copyright 2024 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { FirebaseError } from '@firebase/util';\nimport { VertexAIErrorCode, CustomErrorData } from './types';\nimport { VERTEX_TYPE } from './constants';\n\n/**\n * Error class for the Vertex AI in Firebase SDK.\n *\n * @public\n */\nexport class VertexAIError extends FirebaseError {\n  /**\n   * Constructs a new instance of the `VertexAIError` class.\n   *\n   * @param code - The error code from {@link VertexAIErrorCode}.\n   * @param message - A human-readable message describing the error.\n   * @param customErrorData - Optional error data.\n   */\n  constructor(\n    readonly code: VertexAIErrorCode,\n    readonly message: string,\n    readonly customErrorData?: CustomErrorData\n  ) {\n    // Match error format used by FirebaseError from ErrorFactory\n    const service = VERTEX_TYPE;\n    const serviceName = 'VertexAI';\n    const fullCode = `${service}/${code}`;\n    const fullMessage = `${serviceName}: ${message} (${fullCode}).`;\n    super(fullCode, fullMessage);\n\n    // FirebaseError initializes a stack trace, but it assumes the error is created from the error\n    // factory. Since we break this assumption, we set the stack trace to be originating from this\n    // constructor.\n    // This is only supported in V8.\n    if (Error.captureStackTrace) {\n      // Allows us to initialize the stack trace without including the constructor itself at the\n      // top level of the stack trace.\n      Error.captureStackTrace(this, VertexAIError);\n    }\n\n    // Allows instanceof VertexAIError in ES5/ES6\n    // https://github.com/Microsoft/TypeScript-wiki/blob/master/Breaking-Changes.md#extending-built-ins-like-error-array-and-map-may-no-longer-work\n    Object.setPrototypeOf(this, VertexAIError.prototype);\n\n    // Since Error is an interface, we don't inherit toString and so we define it ourselves.\n    this.toString = () => fullMessage;\n  }\n}\n", "/**\n * @license\n * Copyright 2024 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { RequestOptions, VertexAIErrorCode } from '../types';\nimport { VertexAIError } from '../errors';\nimport { ApiSettings } from '../types/internal';\nimport {\n  DEFAULT_API_VERSION,\n  DEFAULT_BASE_URL,\n  LANGUAGE_TAG,\n  PACKAGE_VERSION\n} from '../constants';\n\nexport enum Task {\n  GENERATE_CONTENT = 'generateContent',\n  STREAM_GENERATE_CONTENT = 'streamGenerateContent',\n  COUNT_TOKENS = 'countTokens'\n}\n\nexport class RequestUrl {\n  constructor(\n    public model: string,\n    public task: Task,\n    public apiSettings: ApiSettings,\n    public stream: boolean,\n    public requestOptions?: RequestOptions\n  ) {}\n  toString(): string {\n    // TODO: allow user-set option if that feature becomes available\n    const apiVersion = DEFAULT_API_VERSION;\n    const baseUrl = this.requestOptions?.baseUrl || DEFAULT_BASE_URL;\n    let url = `${baseUrl}/${apiVersion}`;\n    url += `/projects/${this.apiSettings.project}`;\n    url += `/locations/${this.apiSettings.location}`;\n    url += `/${this.model}`;\n    url += `:${this.task}`;\n    if (this.stream) {\n      url += '?alt=sse';\n    }\n    return url;\n  }\n\n  /**\n   * If the model needs to be passed to the backend, it needs to\n   * include project and location path.\n   */\n  get fullModelString(): string {\n    let modelString = `projects/${this.apiSettings.project}`;\n    modelString += `/locations/${this.apiSettings.location}`;\n    modelString += `/${this.model}`;\n    return modelString;\n  }\n}\n\n/**\n * Log language and \"fire/version\" to x-goog-api-client\n */\nfunction getClientHeaders(): string {\n  const loggingTags = [];\n  loggingTags.push(`${LANGUAGE_TAG}/${PACKAGE_VERSION}`);\n  loggingTags.push(`fire/${PACKAGE_VERSION}`);\n  return loggingTags.join(' ');\n}\n\nexport async function getHeaders(url: RequestUrl): Promise<Headers> {\n  const headers = new Headers();\n  headers.append('Content-Type', 'application/json');\n  headers.append('x-goog-api-client', getClientHeaders());\n  headers.append('x-goog-api-key', url.apiSettings.apiKey);\n  if (url.apiSettings.getAppCheckToken) {\n    const appCheckToken = await url.apiSettings.getAppCheckToken();\n    if (appCheckToken && !appCheckToken.error) {\n      headers.append('X-Firebase-AppCheck', appCheckToken.token);\n    }\n  }\n\n  if (url.apiSettings.getAuthToken) {\n    const authToken = await url.apiSettings.getAuthToken();\n    if (authToken) {\n      headers.append('Authorization', `Firebase ${authToken.accessToken}`);\n    }\n  }\n\n  return headers;\n}\n\nexport async function constructRequest(\n  model: string,\n  task: Task,\n  apiSettings: ApiSettings,\n  stream: boolean,\n  body: string,\n  requestOptions?: RequestOptions\n): Promise<{ url: string; fetchOptions: RequestInit }> {\n  const url = new RequestUrl(model, task, apiSettings, stream, requestOptions);\n  return {\n    url: url.toString(),\n    fetchOptions: {\n      ...buildFetchOptions(requestOptions),\n      method: 'POST',\n      headers: await getHeaders(url),\n      body\n    }\n  };\n}\n\nexport async function makeRequest(\n  model: string,\n  task: Task,\n  apiSettings: ApiSettings,\n  stream: boolean,\n  body: string,\n  requestOptions?: RequestOptions\n): Promise<Response> {\n  const url = new RequestUrl(model, task, apiSettings, stream, requestOptions);\n  let response;\n  try {\n    const request = await constructRequest(\n      model,\n      task,\n      apiSettings,\n      stream,\n      body,\n      requestOptions\n    );\n    response = await fetch(request.url, request.fetchOptions);\n    if (!response.ok) {\n      let message = '';\n      let errorDetails;\n      try {\n        const json = await response.json();\n        message = json.error.message;\n        if (json.error.details) {\n          message += ` ${JSON.stringify(json.error.details)}`;\n          errorDetails = json.error.details;\n        }\n      } catch (e) {\n        // ignored\n      }\n      throw new VertexAIError(\n        VertexAIErrorCode.FETCH_ERROR,\n        `Error fetching from ${url}: [${response.status} ${response.statusText}] ${message}`,\n        {\n          status: response.status,\n          statusText: response.statusText,\n          errorDetails\n        }\n      );\n    }\n  } catch (e) {\n    let err = e as Error;\n    if (\n      (e as VertexAIError).code !== VertexAIErrorCode.FETCH_ERROR &&\n      e instanceof Error\n    ) {\n      err = new VertexAIError(\n        VertexAIErrorCode.ERROR,\n        `Error fetching from ${url.toString()}: ${e.message}`\n      );\n      err.stack = e.stack;\n    }\n\n    throw err;\n  }\n  return response;\n}\n\n/**\n * Generates the request options to be passed to the fetch API.\n * @param requestOptions - The user-defined request options.\n * @returns The generated request options.\n */\nfunction buildFetchOptions(requestOptions?: RequestOptions): RequestInit {\n  const fetchOptions = {} as RequestInit;\n  if (requestOptions?.timeout && requestOptions?.timeout >= 0) {\n    const abortController = new AbortController();\n    const signal = abortController.signal;\n    setTimeout(() => abortController.abort(), requestOptions.timeout);\n    fetchOptions.signal = signal;\n  }\n  return fetchOptions;\n}\n", "/**\n * @license\n * Copyright 2024 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n/**\n * Role is the producer of the content.\n * @public\n */\nexport type Role = (typeof POSSIBLE_ROLES)[number];\n\n/**\n * Possible roles.\n * @public\n */\nexport const POSSIBLE_ROLES = ['user', 'model', 'function', 'system'] as const;\n\n/**\n * Harm categories that would cause prompts or candidates to be blocked.\n * @public\n */\nexport enum HarmCategory {\n  HARM_CATEGORY_UNSPECIFIED = 'HARM_CATEGORY_UNSPECIFIED',\n  HARM_CATEGORY_HATE_SPEECH = 'HARM_CATEGORY_HATE_SPEECH',\n  HARM_CATEGORY_SEXUALLY_EXPLICIT = 'HARM_CATEGORY_SEXUALLY_EXPLICIT',\n  HARM_CATEGORY_HARASSMENT = 'HARM_CATEGORY_HARASSMENT',\n  HARM_CATEGORY_DANGEROUS_CONTENT = 'HARM_CATEGORY_DANGEROUS_CONTENT'\n}\n\n/**\n * Threshold above which a prompt or candidate will be blocked.\n * @public\n */\nexport enum HarmBlockThreshold {\n  // Threshold is unspecified.\n  HARM_BLOCK_THRESHOLD_UNSPECIFIED = 'HARM_BLOCK_THRESHOLD_UNSPECIFIED',\n  // Content with NEGLIGIBLE will be allowed.\n  BLOCK_LOW_AND_ABOVE = 'BLOCK_LOW_AND_ABOVE',\n  // Content with NEGLIGIBLE and LOW will be allowed.\n  BLOCK_MEDIUM_AND_ABOVE = 'BLOCK_MEDIUM_AND_ABOVE',\n  // Content with NEGLIGIBLE, LOW, and MEDIUM will be allowed.\n  BLOCK_ONLY_HIGH = 'BLOCK_ONLY_HIGH',\n  // All content will be allowed.\n  BLOCK_NONE = 'BLOCK_NONE'\n}\n\n/**\n * @public\n */\nexport enum HarmBlockMethod {\n  // The harm block method is unspecified.\n  HARM_BLOCK_METHOD_UNSPECIFIED = 'HARM_BLOCK_METHOD_UNSPECIFIED',\n  // The harm block method uses both probability and severity scores.\n  SEVERITY = 'SEVERITY',\n  // The harm block method uses the probability score.\n  PROBABILITY = 'PROBABILITY'\n}\n\n/**\n * Probability that a prompt or candidate matches a harm category.\n * @public\n */\nexport enum HarmProbability {\n  // Probability is unspecified.\n  HARM_PROBABILITY_UNSPECIFIED = 'HARM_PROBABILITY_UNSPECIFIED',\n  // Content has a negligible chance of being unsafe.\n  NEGLIGIBLE = 'NEGLIGIBLE',\n  // Content has a low chance of being unsafe.\n  LOW = 'LOW',\n  // Content has a medium chance of being unsafe.\n  MEDIUM = 'MEDIUM',\n  // Content has a high chance of being unsafe.\n  HIGH = 'HIGH'\n}\n\n/**\n * Harm severity levels.\n * @public\n */\nexport enum HarmSeverity {\n  // Harm severity unspecified.\n  HARM_SEVERITY_UNSPECIFIED = 'HARM_SEVERITY_UNSPECIFIED',\n  // Negligible level of harm severity.\n  HARM_SEVERITY_NEGLIGIBLE = 'HARM_SEVERITY_NEGLIGIBLE',\n  // Low level of harm severity.\n  HARM_SEVERITY_LOW = 'HARM_SEVERITY_LOW',\n  // Medium level of harm severity.\n  HARM_SEVERITY_MEDIUM = 'HARM_SEVERITY_MEDIUM',\n  // High level of harm severity.\n  HARM_SEVERITY_HIGH = 'HARM_SEVERITY_HIGH'\n}\n\n/**\n * Reason that a prompt was blocked.\n * @public\n */\nexport enum BlockReason {\n  // A blocked reason was not specified.\n  BLOCKED_REASON_UNSPECIFIED = 'BLOCKED_REASON_UNSPECIFIED',\n  // Content was blocked by safety settings.\n  SAFETY = 'SAFETY',\n  // Content was blocked, but the reason is uncategorized.\n  OTHER = 'OTHER'\n}\n\n/**\n * Reason that a candidate finished.\n * @public\n */\nexport enum FinishReason {\n  // Default value. This value is unused.\n  FINISH_REASON_UNSPECIFIED = 'FINISH_REASON_UNSPECIFIED',\n  // Natural stop point of the model or provided stop sequence.\n  STOP = 'STOP',\n  // The maximum number of tokens as specified in the request was reached.\n  MAX_TOKENS = 'MAX_TOKENS',\n  // The candidate content was flagged for safety reasons.\n  SAFETY = 'SAFETY',\n  // The candidate content was flagged for recitation reasons.\n  RECITATION = 'RECITATION',\n  // Unknown reason.\n  OTHER = 'OTHER'\n}\n\n/**\n * @public\n */\nexport enum FunctionCallingMode {\n  // Unspecified function calling mode. This value should not be used.\n  MODE_UNSPECIFIED = 'MODE_UNSPECIFIED',\n  // Default model behavior, model decides to predict either a function call\n  // or a natural language response.\n  AUTO = 'AUTO',\n  // Model is constrained to always predicting a function call only.\n  // If \"allowed_function_names\" is set, the predicted function call will be\n  // limited to any one of \"allowed_function_names\", else the predicted\n  // function call will be any one of the provided \"function_declarations\".\n  ANY = 'ANY',\n  // Model will not predict any function call. Model behavior is same as when\n  // not passing any function declarations.\n  NONE = 'NONE'\n}\n", "/**\n * @license\n * Copyright 2024 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { Content, Part } from './content';\nimport {\n  FunctionCallingMode,\n  HarmBlockMethod,\n  HarmBlockThreshold,\n  HarmCategory\n} from './enums';\n\n/**\n * Base parameters for a number of methods.\n * @public\n */\nexport interface BaseParams {\n  safetySettings?: SafetySetting[];\n  generationConfig?: GenerationConfig;\n}\n\n/**\n * Params passed to {@link getGenerativeModel}.\n * @public\n */\nexport interface ModelParams extends BaseParams {\n  model: string;\n  tools?: Tool[];\n  toolConfig?: ToolConfig;\n  systemInstruction?: string | Part | Content;\n}\n\n/**\n * Request sent through {@link GenerativeModel.generateContent}\n * @public\n */\nexport interface GenerateContentRequest extends BaseParams {\n  contents: Content[];\n  tools?: Tool[];\n  toolConfig?: ToolConfig;\n  systemInstruction?: string | Part | Content;\n}\n\n/**\n * Safety setting that can be sent as part of request parameters.\n * @public\n */\nexport interface SafetySetting {\n  category: HarmCategory;\n  threshold: HarmBlockThreshold;\n  method: HarmBlockMethod;\n}\n\n/**\n * Config options for content-related requests\n * @public\n */\nexport interface GenerationConfig {\n  candidateCount?: number;\n  stopSequences?: string[];\n  maxOutputTokens?: number;\n  temperature?: number;\n  topP?: number;\n  topK?: number;\n  presencePenalty?: number;\n  frequencyPenalty?: number;\n  /**\n   * Output response mimetype of the generated candidate text.\n   * Supported mimetypes are `text/plain` (default, text output) and `application/json`\n   * (JSON response in the candidates).\n   * The model needs to be prompted to output the appropriate response type,\n   * otherwise the behavior is undefined.\n   * This is a preview feature.\n   */\n  responseMimeType?: string;\n}\n\n/**\n * Params for {@link GenerativeModel.startChat}.\n * @public\n */\nexport interface StartChatParams extends BaseParams {\n  history?: Content[];\n  tools?: Tool[];\n  toolConfig?: ToolConfig;\n  systemInstruction?: string | Part | Content;\n}\n\n/**\n * Params for calling {@link GenerativeModel.countTokens}\n * @public\n */\nexport interface CountTokensRequest {\n  contents: Content[];\n}\n\n/**\n * Params passed to {@link getGenerativeModel}.\n * @public\n */\nexport interface RequestOptions {\n  /**\n   * Request timeout in milliseconds.\n   */\n  timeout?: number;\n  /**\n   * Base url for endpoint. Defaults to https://firebaseml.googleapis.com\n   */\n  baseUrl?: string;\n}\n\n/**\n * Defines a tool that model can call to access external knowledge.\n * @public\n */\nexport declare type Tool = FunctionDeclarationsTool;\n\n/**\n * Structured representation of a function declaration as defined by the\n * {@link https://spec.openapis.org/oas/v3.0.3 | OpenAPI 3.0 specification}.\n * Included\n * in this declaration are the function name and parameters. This\n * `FunctionDeclaration` is a representation of a block of code that can be used\n * as a Tool by the model and executed by the client.\n * @public\n */\nexport declare interface FunctionDeclaration {\n  /**\n   * The name of the function to call. Must start with a letter or an\n   * underscore. Must be a-z, A-Z, 0-9, or contain underscores and dashes, with\n   * a max length of 64.\n   */\n  name: string;\n  /**\n   * Optional. Description and purpose of the function. Model uses it to decide\n   * how and whether to call the function.\n   */\n  description?: string;\n  /**\n   * Optional. Describes the parameters to this function in JSON Schema Object\n   * format. Reflects the Open API 3.03 Parameter Object. Parameter names are\n   * case-sensitive. For a function with no parameters, this can be left unset.\n   */\n  parameters?: FunctionDeclarationSchema;\n}\n\n/**\n * A `FunctionDeclarationsTool` is a piece of code that enables the system to\n * interact with external systems to perform an action, or set of actions,\n * outside of knowledge and scope of the model.\n * @public\n */\nexport declare interface FunctionDeclarationsTool {\n  /**\n   * Optional. One or more function declarations\n   * to be passed to the model along with the current user query. Model may\n   * decide to call a subset of these functions by populating\n   * {@link FunctionCall} in the response. User should\n   * provide a {@link FunctionResponse} for each\n   * function call in the next turn. Based on the function responses, the model will\n   * generate the final response back to the user. Maximum 64 function\n   * declarations can be provided.\n   */\n  functionDeclarations?: FunctionDeclaration[];\n}\n\n/**\n * Contains the list of OpenAPI data types\n * as defined by https://swagger.io/docs/specification/data-models/data-types/\n * @public\n */\nexport enum FunctionDeclarationSchemaType {\n  /** String type. */\n  STRING = 'STRING',\n  /** Number type. */\n  NUMBER = 'NUMBER',\n  /** Integer type. */\n  INTEGER = 'INTEGER',\n  /** Boolean type. */\n  BOOLEAN = 'BOOLEAN',\n  /** Array type. */\n  ARRAY = 'ARRAY',\n  /** Object type. */\n  OBJECT = 'OBJECT'\n}\n\n/**\n * Schema for parameters passed to {@link FunctionDeclaration.parameters}.\n * @public\n */\nexport interface FunctionDeclarationSchema {\n  /** The type of the parameter. */\n  type: FunctionDeclarationSchemaType;\n  /** The format of the parameter. */\n  properties: { [k: string]: FunctionDeclarationSchemaProperty };\n  /** Optional. Description of the parameter. */\n  description?: string;\n  /** Optional. Array of required parameters. */\n  required?: string[];\n}\n\n/**\n * Schema is used to define the format of input/output data.\n * Represents a select subset of an OpenAPI 3.0 schema object.\n * More fields may be added in the future as needed.\n * @public\n */\nexport interface FunctionDeclarationSchemaProperty {\n  /**\n   * Optional. The type of the property. {@link\n   * FunctionDeclarationSchemaType}.\n   */\n  type?: FunctionDeclarationSchemaType;\n  /** Optional. The format of the property. */\n  format?: string;\n  /** Optional. The description of the property. */\n  description?: string;\n  /** Optional. Whether the property is nullable. */\n  nullable?: boolean;\n  /** Optional. The items of the property. {@link FunctionDeclarationSchema} */\n  items?: FunctionDeclarationSchema;\n  /** Optional. The enum of the property. */\n  enum?: string[];\n  /** Optional. Map of {@link FunctionDeclarationSchema}. */\n  properties?: { [k: string]: FunctionDeclarationSchema };\n  /** Optional. Array of required property. */\n  required?: string[];\n  /** Optional. The example of the property. */\n  example?: unknown;\n}\n\n/**\n * Tool config. This config is shared for all tools provided in the request.\n * @public\n */\nexport interface ToolConfig {\n  functionCallingConfig: FunctionCallingConfig;\n}\n\n/**\n * @public\n */\nexport interface FunctionCallingConfig {\n  mode?: FunctionCallingMode;\n  allowedFunctionNames?: string[];\n}\n", "/**\n * @license\n * Copyright 2024 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {\n  EnhancedGenerateContentResponse,\n  FinishReason,\n  FunctionCall,\n  GenerateContentCandidate,\n  GenerateContentResponse,\n  VertexAIErrorCode\n} from '../types';\nimport { VertexAIError } from '../errors';\n\n/**\n * Adds convenience helper methods to a response object, including stream\n * chunks (as long as each chunk is a complete GenerateContentResponse JSON).\n */\nexport function addHelpers(\n  response: GenerateContentResponse\n): EnhancedGenerateContentResponse {\n  (response as EnhancedGenerateContentResponse).text = () => {\n    if (response.candidates && response.candidates.length > 0) {\n      if (response.candidates.length > 1) {\n        console.warn(\n          `This response had ${response.candidates.length} ` +\n            `candidates. Returning text from the first candidate only. ` +\n            `Access response.candidates directly to use the other candidates.`\n        );\n      }\n      if (hadBadFinishReason(response.candidates[0])) {\n        throw new VertexAIError(\n          VertexAIErrorCode.RESPONSE_ERROR,\n          `Response error: ${formatBlockErrorMessage(\n            response\n          )}. Response body stored in error.response`,\n          {\n            response\n          }\n        );\n      }\n      return getText(response);\n    } else if (response.promptFeedback) {\n      throw new VertexAIError(\n        VertexAIErrorCode.RESPONSE_ERROR,\n        `Text not available. ${formatBlockErrorMessage(response)}`,\n        {\n          response\n        }\n      );\n    }\n    return '';\n  };\n  (response as EnhancedGenerateContentResponse).functionCalls = () => {\n    if (response.candidates && response.candidates.length > 0) {\n      if (response.candidates.length > 1) {\n        console.warn(\n          `This response had ${response.candidates.length} ` +\n            `candidates. Returning function calls from the first candidate only. ` +\n            `Access response.candidates directly to use the other candidates.`\n        );\n      }\n      if (hadBadFinishReason(response.candidates[0])) {\n        throw new VertexAIError(\n          VertexAIErrorCode.RESPONSE_ERROR,\n          `Response error: ${formatBlockErrorMessage(\n            response\n          )}. Response body stored in error.response`,\n          {\n            response\n          }\n        );\n      }\n      return getFunctionCalls(response);\n    } else if (response.promptFeedback) {\n      throw new VertexAIError(\n        VertexAIErrorCode.RESPONSE_ERROR,\n        `Function call not available. ${formatBlockErrorMessage(response)}`,\n        {\n          response\n        }\n      );\n    }\n    return undefined;\n  };\n  return response as EnhancedGenerateContentResponse;\n}\n\n/**\n * Returns all text found in all parts of first candidate.\n */\nexport function getText(response: GenerateContentResponse): string {\n  const textStrings = [];\n  if (response.candidates?.[0].content?.parts) {\n    for (const part of response.candidates?.[0].content?.parts) {\n      if (part.text) {\n        textStrings.push(part.text);\n      }\n    }\n  }\n  if (textStrings.length > 0) {\n    return textStrings.join('');\n  } else {\n    return '';\n  }\n}\n\n/**\n * Returns {@link FunctionCall}s associated with first candidate.\n */\nexport function getFunctionCalls(\n  response: GenerateContentResponse\n): FunctionCall[] | undefined {\n  const functionCalls: FunctionCall[] = [];\n  if (response.candidates?.[0].content?.parts) {\n    for (const part of response.candidates?.[0].content?.parts) {\n      if (part.functionCall) {\n        functionCalls.push(part.functionCall);\n      }\n    }\n  }\n  if (functionCalls.length > 0) {\n    return functionCalls;\n  } else {\n    return undefined;\n  }\n}\n\nconst badFinishReasons = [FinishReason.RECITATION, FinishReason.SAFETY];\n\nfunction hadBadFinishReason(candidate: GenerateContentCandidate): boolean {\n  return (\n    !!candidate.finishReason &&\n    badFinishReasons.includes(candidate.finishReason)\n  );\n}\n\nexport function formatBlockErrorMessage(\n  response: GenerateContentResponse\n): string {\n  let message = '';\n  if (\n    (!response.candidates || response.candidates.length === 0) &&\n    response.promptFeedback\n  ) {\n    message += 'Response was blocked';\n    if (response.promptFeedback?.blockReason) {\n      message += ` due to ${response.promptFeedback.blockReason}`;\n    }\n    if (response.promptFeedback?.blockReasonMessage) {\n      message += `: ${response.promptFeedback.blockReasonMessage}`;\n    }\n  } else if (response.candidates?.[0]) {\n    const firstCandidate = response.candidates[0];\n    if (hadBadFinishReason(firstCandidate)) {\n      message += `Candidate was blocked due to ${firstCandidate.finishReason}`;\n      if (firstCandidate.finishMessage) {\n        message += `: ${firstCandidate.finishMessage}`;\n      }\n    }\n  }\n  return message;\n}\n", "/**\n * @license\n * Copyright 2024 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {\n  EnhancedGenerateContentResponse,\n  GenerateContentCandidate,\n  GenerateContentResponse,\n  GenerateContentStreamResult,\n  Part,\n  VertexAIErrorCode\n} from '../types';\nimport { VertexAIError } from '../errors';\nimport { addHelpers } from './response-helpers';\n\nconst responseLineRE = /^data\\: (.*)(?:\\n\\n|\\r\\r|\\r\\n\\r\\n)/;\n\n/**\n * Process a response.body stream from the backend and return an\n * iterator that provides one complete GenerateContentResponse at a time\n * and a promise that resolves with a single aggregated\n * GenerateContentResponse.\n *\n * @param response - Response from a fetch call\n */\nexport function processStream(response: Response): GenerateContentStreamResult {\n  const inputStream = response.body!.pipeThrough(\n    new TextDecoderStream('utf8', { fatal: true })\n  );\n  const responseStream =\n    getResponseStream<GenerateContentResponse>(inputStream);\n  const [stream1, stream2] = responseStream.tee();\n  return {\n    stream: generateResponseSequence(stream1),\n    response: getResponsePromise(stream2)\n  };\n}\n\nasync function getResponsePromise(\n  stream: ReadableStream<GenerateContentResponse>\n): Promise<EnhancedGenerateContentResponse> {\n  const allResponses: GenerateContentResponse[] = [];\n  const reader = stream.getReader();\n  while (true) {\n    const { done, value } = await reader.read();\n    if (done) {\n      return addHelpers(aggregateResponses(allResponses));\n    }\n    allResponses.push(value);\n  }\n}\n\nasync function* generateResponseSequence(\n  stream: ReadableStream<GenerateContentResponse>\n): AsyncGenerator<EnhancedGenerateContentResponse> {\n  const reader = stream.getReader();\n  while (true) {\n    const { value, done } = await reader.read();\n    if (done) {\n      break;\n    }\n    yield addHelpers(value);\n  }\n}\n\n/**\n * Reads a raw stream from the fetch response and join incomplete\n * chunks, returning a new stream that provides a single complete\n * GenerateContentResponse in each iteration.\n */\nexport function getResponseStream<T>(\n  inputStream: ReadableStream<string>\n): ReadableStream<T> {\n  const reader = inputStream.getReader();\n  const stream = new ReadableStream<T>({\n    start(controller) {\n      let currentText = '';\n      return pump();\n      function pump(): Promise<(() => Promise<void>) | undefined> {\n        return reader.read().then(({ value, done }) => {\n          if (done) {\n            if (currentText.trim()) {\n              controller.error(\n                new VertexAIError(\n                  VertexAIErrorCode.PARSE_FAILED,\n                  'Failed to parse stream'\n                )\n              );\n              return;\n            }\n            controller.close();\n            return;\n          }\n\n          currentText += value;\n          let match = currentText.match(responseLineRE);\n          let parsedResponse: T;\n          while (match) {\n            try {\n              parsedResponse = JSON.parse(match[1]);\n            } catch (e) {\n              controller.error(\n                new VertexAIError(\n                  VertexAIErrorCode.PARSE_FAILED,\n                  `Error parsing JSON response: \"${match[1]}`\n                )\n              );\n              return;\n            }\n            controller.enqueue(parsedResponse);\n            currentText = currentText.substring(match[0].length);\n            match = currentText.match(responseLineRE);\n          }\n          return pump();\n        });\n      }\n    }\n  });\n  return stream;\n}\n\n/**\n * Aggregates an array of `GenerateContentResponse`s into a single\n * GenerateContentResponse.\n */\nexport function aggregateResponses(\n  responses: GenerateContentResponse[]\n): GenerateContentResponse {\n  const lastResponse = responses[responses.length - 1];\n  const aggregatedResponse: GenerateContentResponse = {\n    promptFeedback: lastResponse?.promptFeedback\n  };\n  for (const response of responses) {\n    if (response.candidates) {\n      for (const candidate of response.candidates) {\n        const i = candidate.index;\n        if (!aggregatedResponse.candidates) {\n          aggregatedResponse.candidates = [];\n        }\n        if (!aggregatedResponse.candidates[i]) {\n          aggregatedResponse.candidates[i] = {\n            index: candidate.index\n          } as GenerateContentCandidate;\n        }\n        // Keep overwriting, the last one will be final\n        aggregatedResponse.candidates[i].citationMetadata =\n          candidate.citationMetadata;\n        aggregatedResponse.candidates[i].finishReason = candidate.finishReason;\n        aggregatedResponse.candidates[i].finishMessage =\n          candidate.finishMessage;\n        aggregatedResponse.candidates[i].safetyRatings =\n          candidate.safetyRatings;\n\n        /**\n         * Candidates should always have content and parts, but this handles\n         * possible malformed responses.\n         */\n        if (candidate.content && candidate.content.parts) {\n          if (!aggregatedResponse.candidates[i].content) {\n            aggregatedResponse.candidates[i].content = {\n              role: candidate.content.role || 'user',\n              parts: []\n            };\n          }\n          const newPart: Partial<Part> = {};\n          for (const part of candidate.content.parts) {\n            if (part.text) {\n              newPart.text = part.text;\n            }\n            if (part.functionCall) {\n              newPart.functionCall = part.functionCall;\n            }\n            if (Object.keys(newPart).length === 0) {\n              newPart.text = '';\n            }\n            aggregatedResponse.candidates[i].content.parts.push(\n              newPart as Part\n            );\n          }\n        }\n      }\n    }\n  }\n  return aggregatedResponse;\n}\n", "/**\n * @license\n * Copyright 2024 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {\n  GenerateContentRequest,\n  GenerateContentResponse,\n  GenerateContentResult,\n  GenerateContentStreamResult,\n  RequestOptions\n} from '../types';\nimport { Task, makeRequest } from '../requests/request';\nimport { addHelpers } from '../requests/response-helpers';\nimport { processStream } from '../requests/stream-reader';\nimport { ApiSettings } from '../types/internal';\n\nexport async function generateContentStream(\n  apiSettings: ApiSettings,\n  model: string,\n  params: GenerateContentRequest,\n  requestOptions?: RequestOptions\n): Promise<GenerateContentStreamResult> {\n  const response = await makeRequest(\n    model,\n    Task.STREAM_GENERATE_CONTENT,\n    apiSettings,\n    /* stream */ true,\n    JSON.stringify(params),\n    requestOptions\n  );\n  return processStream(response);\n}\n\nexport async function generateContent(\n  apiSettings: ApiSettings,\n  model: string,\n  params: GenerateContentRequest,\n  requestOptions?: RequestOptions\n): Promise<GenerateContentResult> {\n  const response = await makeRequest(\n    model,\n    Task.GENERATE_CONTENT,\n    apiSettings,\n    /* stream */ false,\n    JSON.stringify(params),\n    requestOptions\n  );\n  const responseJson: GenerateContentResponse = await response.json();\n  const enhancedResponse = addHelpers(responseJson);\n  return {\n    response: enhancedResponse\n  };\n}\n", "/**\n * @license\n * Copyright 2024 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {\n  Content,\n  GenerateContentRequest,\n  Part,\n  VertexAIErrorCode\n} from '../types';\nimport { VertexAIError } from '../errors';\n\nexport function formatSystemInstruction(\n  input?: string | Part | Content\n): Content | undefined {\n  // null or undefined\n  if (input == null) {\n    return undefined;\n  } else if (typeof input === 'string') {\n    return { role: 'system', parts: [{ text: input }] } as Content;\n  } else if ((input as Part).text) {\n    return { role: 'system', parts: [input as Part] };\n  } else if ((input as Content).parts) {\n    if (!(input as Content).role) {\n      return { role: 'system', parts: (input as Content).parts };\n    } else {\n      return input as Content;\n    }\n  }\n}\n\nexport function formatNewContent(\n  request: string | Array<string | Part>\n): Content {\n  let newParts: Part[] = [];\n  if (typeof request === 'string') {\n    newParts = [{ text: request }];\n  } else {\n    for (const partOrString of request) {\n      if (typeof partOrString === 'string') {\n        newParts.push({ text: partOrString });\n      } else {\n        newParts.push(partOrString);\n      }\n    }\n  }\n  return assignRoleToPartsAndValidateSendMessageRequest(newParts);\n}\n\n/**\n * When multiple Part types (i.e. FunctionResponsePart and TextPart) are\n * passed in a single Part array, we may need to assign different roles to each\n * part. Currently only FunctionResponsePart requires a role other than 'user'.\n * @private\n * @param parts Array of parts to pass to the model\n * @returns Array of content items\n */\nfunction assignRoleToPartsAndValidateSendMessageRequest(\n  parts: Part[]\n): Content {\n  const userContent: Content = { role: 'user', parts: [] };\n  const functionContent: Content = { role: 'function', parts: [] };\n  let hasUserContent = false;\n  let hasFunctionContent = false;\n  for (const part of parts) {\n    if ('functionResponse' in part) {\n      functionContent.parts.push(part);\n      hasFunctionContent = true;\n    } else {\n      userContent.parts.push(part);\n      hasUserContent = true;\n    }\n  }\n\n  if (hasUserContent && hasFunctionContent) {\n    throw new VertexAIError(\n      VertexAIErrorCode.INVALID_CONTENT,\n      'Within a single message, FunctionResponse cannot be mixed with other type of Part in the request for sending chat message.'\n    );\n  }\n\n  if (!hasUserContent && !hasFunctionContent) {\n    throw new VertexAIError(\n      VertexAIErrorCode.INVALID_CONTENT,\n      'No Content is provided for sending chat message.'\n    );\n  }\n\n  if (hasUserContent) {\n    return userContent;\n  }\n\n  return functionContent;\n}\n\nexport function formatGenerateContentInput(\n  params: GenerateContentRequest | string | Array<string | Part>\n): GenerateContentRequest {\n  let formattedRequest: GenerateContentRequest;\n  if ((params as GenerateContentRequest).contents) {\n    formattedRequest = params as GenerateContentRequest;\n  } else {\n    // Array or string\n    const content = formatNewContent(params as string | Array<string | Part>);\n    formattedRequest = { contents: [content] };\n  }\n  if ((params as GenerateContentRequest).systemInstruction) {\n    formattedRequest.systemInstruction = formatSystemInstruction(\n      (params as GenerateContentRequest).systemInstruction\n    );\n  }\n  return formattedRequest;\n}\n", "/**\n * @license\n * Copyright 2024 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {\n  Content,\n  POSSIBLE_ROLES,\n  Part,\n  Role,\n  VertexAIErrorCode\n} from '../types';\nimport { VertexAIError } from '../errors';\n\n// https://ai.google.dev/api/rest/v1beta/Content#part\n\nconst VALID_PART_FIELDS: Array<keyof Part> = [\n  'text',\n  'inlineData',\n  'functionCall',\n  'functionResponse'\n];\n\nconst VALID_PARTS_PER_ROLE: { [key in Role]: Array<keyof Part> } = {\n  user: ['text', 'inlineData'],\n  function: ['functionResponse'],\n  model: ['text', 'functionCall'],\n  // System instructions shouldn't be in history anyway.\n  system: ['text']\n};\n\nconst VALID_PREVIOUS_CONTENT_ROLES: { [key in Role]: Role[] } = {\n  user: ['model'],\n  function: ['model'],\n  model: ['user', 'function'],\n  // System instructions shouldn't be in history.\n  system: []\n};\n\nexport function validateChatHistory(history: Content[]): void {\n  let prevContent: Content | null = null;\n  for (const currContent of history) {\n    const { role, parts } = currContent;\n    if (!prevContent && role !== 'user') {\n      throw new VertexAIError(\n        VertexAIErrorCode.INVALID_CONTENT,\n        `First Content should be with role 'user', got ${role}`\n      );\n    }\n    if (!POSSIBLE_ROLES.includes(role)) {\n      throw new VertexAIError(\n        VertexAIErrorCode.INVALID_CONTENT,\n        `Each item should include role field. Got ${role} but valid roles are: ${JSON.stringify(\n          POSSIBLE_ROLES\n        )}`\n      );\n    }\n\n    if (!Array.isArray(parts)) {\n      throw new VertexAIError(\n        VertexAIErrorCode.INVALID_CONTENT,\n        `Content should have 'parts' but property with an array of Parts`\n      );\n    }\n\n    if (parts.length === 0) {\n      throw new VertexAIError(\n        VertexAIErrorCode.INVALID_CONTENT,\n        `Each Content should have at least one part`\n      );\n    }\n\n    const countFields: Record<keyof Part, number> = {\n      text: 0,\n      inlineData: 0,\n      functionCall: 0,\n      functionResponse: 0\n    };\n\n    for (const part of parts) {\n      for (const key of VALID_PART_FIELDS) {\n        if (key in part) {\n          countFields[key] += 1;\n        }\n      }\n    }\n    const validParts = VALID_PARTS_PER_ROLE[role];\n    for (const key of VALID_PART_FIELDS) {\n      if (!validParts.includes(key) && countFields[key] > 0) {\n        throw new VertexAIError(\n          VertexAIErrorCode.INVALID_CONTENT,\n          `Content with role '${role}' can't contain '${key}' part`\n        );\n      }\n    }\n\n    if (prevContent) {\n      const validPreviousContentRoles = VALID_PREVIOUS_CONTENT_ROLES[role];\n      if (!validPreviousContentRoles.includes(prevContent.role)) {\n        throw new VertexAIError(\n          VertexAIErrorCode.INVALID_CONTENT,\n          `Content with role '${role} can't follow '${\n            prevContent.role\n          }'. Valid previous roles: ${JSON.stringify(\n            VALID_PREVIOUS_CONTENT_ROLES\n          )}`\n        );\n      }\n    }\n    prevContent = currContent;\n  }\n}\n", "/**\n * @license\n * Copyright 2024 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {\n  Content,\n  GenerateContentRequest,\n  GenerateContentResult,\n  GenerateContentStreamResult,\n  Part,\n  RequestOptions,\n  StartChatParams\n} from '../types';\nimport { formatNewContent } from '../requests/request-helpers';\nimport { formatBlockErrorMessage } from '../requests/response-helpers';\nimport { validateChatHistory } from './chat-session-helpers';\nimport { generateContent, generateContentStream } from './generate-content';\nimport { ApiSettings } from '../types/internal';\n\n/**\n * Do not log a message for this error.\n */\nconst SILENT_ERROR = 'SILENT_ERROR';\n\n/**\n * ChatSession class that enables sending chat messages and stores\n * history of sent and received messages so far.\n *\n * @public\n */\nexport class ChatSession {\n  private _apiSettings: ApiSettings;\n  private _history: Content[] = [];\n  private _sendPromise: Promise<void> = Promise.resolve();\n\n  constructor(\n    apiSettings: ApiSettings,\n    public model: string,\n    public params?: StartChatParams,\n    public requestOptions?: RequestOptions\n  ) {\n    this._apiSettings = apiSettings;\n    if (params?.history) {\n      validateChatHistory(params.history);\n      this._history = params.history;\n    }\n  }\n\n  /**\n   * Gets the chat history so far. Blocked prompts are not added to history.\n   * Neither blocked candidates nor the prompts that generated them are added\n   * to history.\n   */\n  async getHistory(): Promise<Content[]> {\n    await this._sendPromise;\n    return this._history;\n  }\n\n  /**\n   * Sends a chat message and receives a non-streaming\n   * {@link GenerateContentResult}\n   */\n  async sendMessage(\n    request: string | Array<string | Part>\n  ): Promise<GenerateContentResult> {\n    await this._sendPromise;\n    const newContent = formatNewContent(request);\n    const generateContentRequest: GenerateContentRequest = {\n      safetySettings: this.params?.safetySettings,\n      generationConfig: this.params?.generationConfig,\n      tools: this.params?.tools,\n      toolConfig: this.params?.toolConfig,\n      systemInstruction: this.params?.systemInstruction,\n      contents: [...this._history, newContent]\n    };\n    let finalResult = {} as GenerateContentResult;\n    // Add onto the chain.\n    this._sendPromise = this._sendPromise\n      .then(() =>\n        generateContent(\n          this._apiSettings,\n          this.model,\n          generateContentRequest,\n          this.requestOptions\n        )\n      )\n      .then(result => {\n        if (\n          result.response.candidates &&\n          result.response.candidates.length > 0\n        ) {\n          this._history.push(newContent);\n          const responseContent: Content = {\n            parts: result.response.candidates?.[0].content.parts || [],\n            // Response seems to come back without a role set.\n            role: result.response.candidates?.[0].content.role || 'model'\n          };\n          this._history.push(responseContent);\n        } else {\n          const blockErrorMessage = formatBlockErrorMessage(result.response);\n          if (blockErrorMessage) {\n            console.warn(\n              `sendMessage() was unsuccessful. ${blockErrorMessage}. Inspect response object for details.`\n            );\n          }\n        }\n        finalResult = result;\n      });\n    await this._sendPromise;\n    return finalResult;\n  }\n\n  /**\n   * Sends a chat message and receives the response as a\n   * {@link GenerateContentStreamResult} containing an iterable stream\n   * and a response promise.\n   */\n  async sendMessageStream(\n    request: string | Array<string | Part>\n  ): Promise<GenerateContentStreamResult> {\n    await this._sendPromise;\n    const newContent = formatNewContent(request);\n    const generateContentRequest: GenerateContentRequest = {\n      safetySettings: this.params?.safetySettings,\n      generationConfig: this.params?.generationConfig,\n      tools: this.params?.tools,\n      toolConfig: this.params?.toolConfig,\n      systemInstruction: this.params?.systemInstruction,\n      contents: [...this._history, newContent]\n    };\n    const streamPromise = generateContentStream(\n      this._apiSettings,\n      this.model,\n      generateContentRequest,\n      this.requestOptions\n    );\n\n    // Add onto the chain.\n    this._sendPromise = this._sendPromise\n      .then(() => streamPromise)\n      // This must be handled to avoid unhandled rejection, but jump\n      // to the final catch block with a label to not log this error.\n      .catch(_ignored => {\n        throw new Error(SILENT_ERROR);\n      })\n      .then(streamResult => streamResult.response)\n      .then(response => {\n        if (response.candidates && response.candidates.length > 0) {\n          this._history.push(newContent);\n          const responseContent = { ...response.candidates[0].content };\n          // Response seems to come back without a role set.\n          if (!responseContent.role) {\n            responseContent.role = 'model';\n          }\n          this._history.push(responseContent);\n        } else {\n          const blockErrorMessage = formatBlockErrorMessage(response);\n          if (blockErrorMessage) {\n            console.warn(\n              `sendMessageStream() was unsuccessful. ${blockErrorMessage}. Inspect response object for details.`\n            );\n          }\n        }\n      })\n      .catch(e => {\n        // Errors in streamPromise are already catchable by the user as\n        // streamPromise is returned.\n        // Avoid duplicating the error message in logs.\n        if (e.message !== SILENT_ERROR) {\n          // Users do not have access to _sendPromise to catch errors\n          // downstream from streamPromise, so they should not throw.\n          console.error(e);\n        }\n      });\n    return streamPromise;\n  }\n}\n", "/**\n * @license\n * Copyright 2024 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {\n  CountTokensRequest,\n  CountTokensResponse,\n  RequestOptions\n} from '../types';\nimport { Task, makeRequest } from '../requests/request';\nimport { ApiSettings } from '../types/internal';\n\nexport async function countTokens(\n  apiSettings: ApiSettings,\n  model: string,\n  params: CountTokensRequest,\n  requestOptions?: RequestOptions\n): Promise<CountTokensResponse> {\n  const response = await makeRequest(\n    model,\n    Task.COUNT_TOKENS,\n    apiSettings,\n    false,\n    JSON.stringify(params),\n    requestOptions\n  );\n  return response.json();\n}\n", "/**\n * @license\n * Copyright 2024 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {\n  generateContent,\n  generateContentStream\n} from '../methods/generate-content';\nimport {\n  Content,\n  CountTokensRequest,\n  CountTokensResponse,\n  GenerateContentRequest,\n  GenerateContentResult,\n  GenerateContentStreamResult,\n  GenerationConfig,\n  ModelParams,\n  Part,\n  RequestOptions,\n  SafetySetting,\n  StartChatParams,\n  Tool,\n  ToolConfig,\n  VertexAIErrorCode\n} from '../types';\nimport { VertexAIError } from '../errors';\nimport { ChatSession } from '../methods/chat-session';\nimport { countTokens } from '../methods/count-tokens';\nimport {\n  formatGenerateContentInput,\n  formatSystemInstruction\n} from '../requests/request-helpers';\nimport { VertexAI } from '../public-types';\nimport { ApiSettings } from '../types/internal';\nimport { VertexAIService } from '../service';\n\n/**\n * Class for generative model APIs.\n * @public\n */\nexport class GenerativeModel {\n  private _apiSettings: ApiSettings;\n  model: string;\n  generationConfig: GenerationConfig;\n  safetySettings: SafetySetting[];\n  requestOptions?: RequestOptions;\n  tools?: Tool[];\n  toolConfig?: ToolConfig;\n  systemInstruction?: Content;\n\n  constructor(\n    vertexAI: VertexAI,\n    modelParams: ModelParams,\n    requestOptions?: RequestOptions\n  ) {\n    if (!vertexAI.app?.options?.apiKey) {\n      throw new VertexAIError(\n        VertexAIErrorCode.NO_API_KEY,\n        `The \"apiKey\" field is empty in the local Firebase config. Firebase VertexAI requires this field to contain a valid API key.`\n      );\n    } else if (!vertexAI.app?.options?.projectId) {\n      throw new VertexAIError(\n        VertexAIErrorCode.NO_PROJECT_ID,\n        `The \"projectId\" field is empty in the local Firebase config. Firebase VertexAI requires this field to contain a valid project ID.`\n      );\n    } else {\n      this._apiSettings = {\n        apiKey: vertexAI.app.options.apiKey,\n        project: vertexAI.app.options.projectId,\n        location: vertexAI.location\n      };\n      if ((vertexAI as VertexAIService).appCheck) {\n        this._apiSettings.getAppCheckToken = () =>\n          (vertexAI as VertexAIService).appCheck!.getToken();\n      }\n\n      if ((vertexAI as VertexAIService).auth) {\n        this._apiSettings.getAuthToken = () =>\n          (vertexAI as VertexAIService).auth!.getToken();\n      }\n    }\n    if (modelParams.model.includes('/')) {\n      if (modelParams.model.startsWith('models/')) {\n        // Add \"publishers/google\" if the user is only passing in 'models/model-name'.\n        this.model = `publishers/google/${modelParams.model}`;\n      } else {\n        // Any other custom format (e.g. tuned models) must be passed in correctly.\n        this.model = modelParams.model;\n      }\n    } else {\n      // If path is not included, assume it's a non-tuned model.\n      this.model = `publishers/google/models/${modelParams.model}`;\n    }\n    this.generationConfig = modelParams.generationConfig || {};\n    this.safetySettings = modelParams.safetySettings || [];\n    this.tools = modelParams.tools;\n    this.toolConfig = modelParams.toolConfig;\n    this.systemInstruction = formatSystemInstruction(\n      modelParams.systemInstruction\n    );\n    this.requestOptions = requestOptions || {};\n  }\n\n  /**\n   * Makes a single non-streaming call to the model\n   * and returns an object containing a single {@link GenerateContentResponse}.\n   */\n  async generateContent(\n    request: GenerateContentRequest | string | Array<string | Part>\n  ): Promise<GenerateContentResult> {\n    const formattedParams = formatGenerateContentInput(request);\n    return generateContent(\n      this._apiSettings,\n      this.model,\n      {\n        generationConfig: this.generationConfig,\n        safetySettings: this.safetySettings,\n        tools: this.tools,\n        toolConfig: this.toolConfig,\n        systemInstruction: this.systemInstruction,\n        ...formattedParams\n      },\n      this.requestOptions\n    );\n  }\n\n  /**\n   * Makes a single streaming call to the model\n   * and returns an object containing an iterable stream that iterates\n   * over all chunks in the streaming response as well as\n   * a promise that returns the final aggregated response.\n   */\n  async generateContentStream(\n    request: GenerateContentRequest | string | Array<string | Part>\n  ): Promise<GenerateContentStreamResult> {\n    const formattedParams = formatGenerateContentInput(request);\n    return generateContentStream(\n      this._apiSettings,\n      this.model,\n      {\n        generationConfig: this.generationConfig,\n        safetySettings: this.safetySettings,\n        tools: this.tools,\n        toolConfig: this.toolConfig,\n        systemInstruction: this.systemInstruction,\n        ...formattedParams\n      },\n      this.requestOptions\n    );\n  }\n\n  /**\n   * Gets a new {@link ChatSession} instance which can be used for\n   * multi-turn chats.\n   */\n  startChat(startChatParams?: StartChatParams): ChatSession {\n    return new ChatSession(\n      this._apiSettings,\n      this.model,\n      {\n        tools: this.tools,\n        toolConfig: this.toolConfig,\n        systemInstruction: this.systemInstruction,\n        ...startChatParams\n      },\n      this.requestOptions\n    );\n  }\n\n  /**\n   * Counts the tokens in the provided request.\n   */\n  async countTokens(\n    request: CountTokensRequest | string | Array<string | Part>\n  ): Promise<CountTokensResponse> {\n    const formattedParams = formatGenerateContentInput(request);\n    return countTokens(this._apiSettings, this.model, formattedParams);\n  }\n}\n", "/**\n * @license\n * Copyright 2024 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { FirebaseApp, getApp, _getProvider } from '@firebase/app';\nimport { Provider } from '@firebase/component';\nimport { getModularInstance } from '@firebase/util';\nimport { DEFAULT_LOCATION, VERTEX_TYPE } from './constants';\nimport { VertexAIService } from './service';\nimport { VertexAI, VertexAIOptions } from './public-types';\nimport { ModelParams, RequestOptions, VertexAIErrorCode } from './types';\nimport { VertexAIError } from './errors';\nimport { GenerativeModel } from './models/generative-model';\n\nexport { ChatSession } from './methods/chat-session';\n\nexport { GenerativeModel };\n\nexport { VertexAIError };\n\ndeclare module '@firebase/component' {\n  interface NameServiceMapping {\n    [VERTEX_TYPE]: VertexAIService;\n  }\n}\n\n/**\n * Returns a {@link VertexAI} instance for the given app.\n *\n * @public\n *\n * @param app - The {@link @firebase/app#FirebaseApp} to use.\n */\nexport function getVertexAI(\n  app: FirebaseApp = getApp(),\n  options?: VertexAIOptions\n): VertexAI {\n  app = getModularInstance(app);\n  // Dependencies\n  const vertexProvider: Provider<'vertexAI'> = _getProvider(app, VERTEX_TYPE);\n\n  return vertexProvider.getImmediate({\n    identifier: options?.location || DEFAULT_LOCATION\n  });\n}\n\n/**\n * Returns a {@link GenerativeModel} class with methods for inference\n * and other functionality.\n *\n * @public\n */\nexport function getGenerativeModel(\n  vertexAI: VertexAI,\n  modelParams: ModelParams,\n  requestOptions?: RequestOptions\n): GenerativeModel {\n  if (!modelParams.model) {\n    throw new VertexAIError(\n      VertexAIErrorCode.NO_MODEL,\n      `Must provide a model name. Example: getGenerativeModel({ model: 'my-model-name' })`\n    );\n  }\n  return new GenerativeModel(vertexAI, modelParams, requestOptions);\n}\n", "/**\n * The Vertex AI in Firebase Web SDK.\n *\n * @packageDocumentation\n */\n\n/**\n * @license\n * Copyright 2024 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { registerVersion, _registerComponent } from '@firebase/app';\nimport { VertexAIService } from './service';\nimport { VERTEX_TYPE } from './constants';\nimport { Component, ComponentType } from '@firebase/component';\nimport { name, version } from '../package.json';\n\ndeclare global {\n  interface Window {\n    [key: string]: unknown;\n  }\n}\n\nfunction registerVertex(): void {\n  _registerComponent(\n    new Component(\n      VERTEX_TYPE,\n      (container, { instanceIdentifier: location }) => {\n        // getImmediate for FirebaseApp will always succeed\n        const app = container.getProvider('app').getImmediate();\n        const auth = container.getProvider('auth-internal');\n        const appCheckProvider = container.getProvider('app-check-internal');\n        return new VertexAIService(app, auth, appCheckProvider, { location });\n      },\n      ComponentType.PUBLIC\n    ).setMultipleInstances(true)\n  );\n\n  registerVersion(name, version);\n  // BUILD_TARGET will be replaced by values like esm5, esm2017, cjs5, etc during the compilation\n  registerVersion(name, version, '__BUILD_TARGET__');\n}\n\nregisterVertex();\n\nexport * from './api';\nexport * from './public-types';\n"], "names": [], "mappings": ";;;;;;;;AAAA;;;;;;;;;;;;;;;AAeG;AAII,MAAM,WAAW,GAAG,UAAU,CAAC;AAE/B,MAAM,gBAAgB,GAAG,aAAa,CAAC;AAEvC,MAAM,gBAAgB,GAAG,mCAAmC,CAAC;AAE7D,MAAM,mBAAmB,GAAG,QAAQ,CAAC;AAErC,MAAM,eAAe,GAAG,OAAO,CAAC;AAEhC,MAAM,YAAY,GAAG,OAAO;;AC7BnC;;;;;;;;;;;;;;;AAeG;MAeU,eAAe,CAAA;AAK1B,IAAA,WAAA,CACS,GAAgB,EACvB,YAAiD,EACjD,gBAA0D,EACnD,OAAyB,EAAA;;QAHzB,IAAG,CAAA,GAAA,GAAH,GAAG,CAAa;QAGhB,IAAO,CAAA,OAAA,GAAP,OAAO,CAAkB;AAEhC,QAAA,MAAM,QAAQ,GAAG,gBAAgB,KAAhB,IAAA,IAAA,gBAAgB,uBAAhB,gBAAgB,CAAE,YAAY,CAAC,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC,CAAC;AACpE,QAAA,MAAM,IAAI,GAAG,YAAY,KAAZ,IAAA,IAAA,YAAY,uBAAZ,YAAY,CAAE,YAAY,CAAC,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC,CAAC;AAC5D,QAAA,IAAI,CAAC,IAAI,GAAG,IAAI,IAAI,IAAI,CAAC;AACzB,QAAA,IAAI,CAAC,QAAQ,GAAG,QAAQ,IAAI,IAAI,CAAC;AACjC,QAAA,IAAI,CAAC,QAAQ,GAAG,CAAA,CAAA,EAAA,GAAA,IAAI,CAAC,OAAO,MAAE,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,QAAQ,KAAI,gBAAgB,CAAC;KAC5D;IAED,OAAO,GAAA;AACL,QAAA,OAAO,OAAO,CAAC,OAAO,EAAE,CAAC;KAC1B;AACF;;ACnDD;;;;;;;;;;;;;;;AAeG;AAMH;;;;AAIG;AACG,MAAO,aAAc,SAAQ,aAAa,CAAA;AAC9C;;;;;;AAMG;AACH,IAAA,WAAA,CACW,IAAuB,EACvB,OAAe,EACf,eAAiC,EAAA;;QAG1C,MAAM,OAAO,GAAG,WAAW,CAAC;QAC5B,MAAM,WAAW,GAAG,UAAU,CAAC;AAC/B,QAAA,MAAM,QAAQ,GAAG,CAAA,EAAG,OAAO,CAAI,CAAA,EAAA,IAAI,EAAE,CAAC;QACtC,MAAM,WAAW,GAAG,CAAG,EAAA,WAAW,KAAK,OAAO,CAAA,EAAA,EAAK,QAAQ,CAAA,EAAA,CAAI,CAAC;AAChE,QAAA,KAAK,CAAC,QAAQ,EAAE,WAAW,CAAC,CAAC;QATpB,IAAI,CAAA,IAAA,GAAJ,IAAI,CAAmB;QACvB,IAAO,CAAA,OAAA,GAAP,OAAO,CAAQ;QACf,IAAe,CAAA,eAAA,GAAf,eAAe,CAAkB;;;;;QAa1C,IAAI,KAAK,CAAC,iBAAiB,EAAE;;;AAG3B,YAAA,KAAK,CAAC,iBAAiB,CAAC,IAAI,EAAE,aAAa,CAAC,CAAC;AAC9C,SAAA;;;QAID,MAAM,CAAC,cAAc,CAAC,IAAI,EAAE,aAAa,CAAC,SAAS,CAAC,CAAC;;AAGrD,QAAA,IAAI,CAAC,QAAQ,GAAG,MAAM,WAAW,CAAC;KACnC;AACF;;AC/DD;;;;;;;;;;;;;;;AAeG;AAYH,IAAY,IAIX,CAAA;AAJD,CAAA,UAAY,IAAI,EAAA;AACd,IAAA,IAAA,CAAA,kBAAA,CAAA,GAAA,iBAAoC,CAAA;AACpC,IAAA,IAAA,CAAA,yBAAA,CAAA,GAAA,uBAAiD,CAAA;AACjD,IAAA,IAAA,CAAA,cAAA,CAAA,GAAA,aAA4B,CAAA;AAC9B,CAAC,EAJW,IAAI,KAAJ,IAAI,GAIf,EAAA,CAAA,CAAA,CAAA;MAEY,UAAU,CAAA;IACrB,WACS,CAAA,KAAa,EACb,IAAU,EACV,WAAwB,EACxB,MAAe,EACf,cAA+B,EAAA;QAJ/B,IAAK,CAAA,KAAA,GAAL,KAAK,CAAQ;QACb,IAAI,CAAA,IAAA,GAAJ,IAAI,CAAM;QACV,IAAW,CAAA,WAAA,GAAX,WAAW,CAAa;QACxB,IAAM,CAAA,MAAA,GAAN,MAAM,CAAS;QACf,IAAc,CAAA,cAAA,GAAd,cAAc,CAAiB;KACpC;IACJ,QAAQ,GAAA;;;QAEN,MAAM,UAAU,GAAG,mBAAmB,CAAC;QACvC,MAAM,OAAO,GAAG,CAAA,CAAA,EAAA,GAAA,IAAI,CAAC,cAAc,MAAE,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,OAAO,KAAI,gBAAgB,CAAC;AACjE,QAAA,IAAI,GAAG,GAAG,CAAA,EAAG,OAAO,CAAI,CAAA,EAAA,UAAU,EAAE,CAAC;QACrC,GAAG,IAAI,aAAa,IAAI,CAAC,WAAW,CAAC,OAAO,EAAE,CAAC;QAC/C,GAAG,IAAI,cAAc,IAAI,CAAC,WAAW,CAAC,QAAQ,EAAE,CAAC;AACjD,QAAA,GAAG,IAAI,CAAI,CAAA,EAAA,IAAI,CAAC,KAAK,EAAE,CAAC;AACxB,QAAA,GAAG,IAAI,CAAI,CAAA,EAAA,IAAI,CAAC,IAAI,EAAE,CAAC;QACvB,IAAI,IAAI,CAAC,MAAM,EAAE;YACf,GAAG,IAAI,UAAU,CAAC;AACnB,SAAA;AACD,QAAA,OAAO,GAAG,CAAC;KACZ;AAED;;;AAGG;AACH,IAAA,IAAI,eAAe,GAAA;QACjB,IAAI,WAAW,GAAG,CAAY,SAAA,EAAA,IAAI,CAAC,WAAW,CAAC,OAAO,CAAA,CAAE,CAAC;QACzD,WAAW,IAAI,cAAc,IAAI,CAAC,WAAW,CAAC,QAAQ,EAAE,CAAC;AACzD,QAAA,WAAW,IAAI,CAAI,CAAA,EAAA,IAAI,CAAC,KAAK,EAAE,CAAC;AAChC,QAAA,OAAO,WAAW,CAAC;KACpB;AACF,CAAA;AAED;;AAEG;AACH,SAAS,gBAAgB,GAAA;IACvB,MAAM,WAAW,GAAG,EAAE,CAAC;IACvB,WAAW,CAAC,IAAI,CAAC,CAAA,EAAG,YAAY,CAAI,CAAA,EAAA,eAAe,CAAE,CAAA,CAAC,CAAC;AACvD,IAAA,WAAW,CAAC,IAAI,CAAC,QAAQ,eAAe,CAAA,CAAE,CAAC,CAAC;AAC5C,IAAA,OAAO,WAAW,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;AAC/B,CAAC;AAEM,eAAe,UAAU,CAAC,GAAe,EAAA;AAC9C,IAAA,MAAM,OAAO,GAAG,IAAI,OAAO,EAAE,CAAC;AAC9B,IAAA,OAAO,CAAC,MAAM,CAAC,cAAc,EAAE,kBAAkB,CAAC,CAAC;IACnD,OAAO,CAAC,MAAM,CAAC,mBAAmB,EAAE,gBAAgB,EAAE,CAAC,CAAC;IACxD,OAAO,CAAC,MAAM,CAAC,gBAAgB,EAAE,GAAG,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC;AACzD,IAAA,IAAI,GAAG,CAAC,WAAW,CAAC,gBAAgB,EAAE;QACpC,MAAM,aAAa,GAAG,MAAM,GAAG,CAAC,WAAW,CAAC,gBAAgB,EAAE,CAAC;AAC/D,QAAA,IAAI,aAAa,IAAI,CAAC,aAAa,CAAC,KAAK,EAAE;YACzC,OAAO,CAAC,MAAM,CAAC,qBAAqB,EAAE,aAAa,CAAC,KAAK,CAAC,CAAC;AAC5D,SAAA;AACF,KAAA;AAED,IAAA,IAAI,GAAG,CAAC,WAAW,CAAC,YAAY,EAAE;QAChC,MAAM,SAAS,GAAG,MAAM,GAAG,CAAC,WAAW,CAAC,YAAY,EAAE,CAAC;AACvD,QAAA,IAAI,SAAS,EAAE;YACb,OAAO,CAAC,MAAM,CAAC,eAAe,EAAE,CAAY,SAAA,EAAA,SAAS,CAAC,WAAW,CAAE,CAAA,CAAC,CAAC;AACtE,SAAA;AACF,KAAA;AAED,IAAA,OAAO,OAAO,CAAC;AACjB,CAAC;AAEM,eAAe,gBAAgB,CACpC,KAAa,EACb,IAAU,EACV,WAAwB,EACxB,MAAe,EACf,IAAY,EACZ,cAA+B,EAAA;AAE/B,IAAA,MAAM,GAAG,GAAG,IAAI,UAAU,CAAC,KAAK,EAAE,IAAI,EAAE,WAAW,EAAE,MAAM,EAAE,cAAc,CAAC,CAAC;IAC7E,OAAO;AACL,QAAA,GAAG,EAAE,GAAG,CAAC,QAAQ,EAAE;AACnB,QAAA,YAAY,kCACP,iBAAiB,CAAC,cAAc,CAAC,CAAA,EAAA,EACpC,MAAM,EAAE,MAAM,EACd,OAAO,EAAE,MAAM,UAAU,CAAC,GAAG,CAAC,EAC9B,IAAI,EACL,CAAA;KACF,CAAC;AACJ,CAAC;AAEM,eAAe,WAAW,CAC/B,KAAa,EACb,IAAU,EACV,WAAwB,EACxB,MAAe,EACf,IAAY,EACZ,cAA+B,EAAA;AAE/B,IAAA,MAAM,GAAG,GAAG,IAAI,UAAU,CAAC,KAAK,EAAE,IAAI,EAAE,WAAW,EAAE,MAAM,EAAE,cAAc,CAAC,CAAC;AAC7E,IAAA,IAAI,QAAQ,CAAC;IACb,IAAI;AACF,QAAA,MAAM,OAAO,GAAG,MAAM,gBAAgB,CACpC,KAAK,EACL,IAAI,EACJ,WAAW,EACX,MAAM,EACN,IAAI,EACJ,cAAc,CACf,CAAC;AACF,QAAA,QAAQ,GAAG,MAAM,KAAK,CAAC,OAAO,CAAC,GAAG,EAAE,OAAO,CAAC,YAAY,CAAC,CAAC;AAC1D,QAAA,IAAI,CAAC,QAAQ,CAAC,EAAE,EAAE;YAChB,IAAI,OAAO,GAAG,EAAE,CAAC;AACjB,YAAA,IAAI,YAAY,CAAC;YACjB,IAAI;AACF,gBAAA,MAAM,IAAI,GAAG,MAAM,QAAQ,CAAC,IAAI,EAAE,CAAC;AACnC,gBAAA,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC;AAC7B,gBAAA,IAAI,IAAI,CAAC,KAAK,CAAC,OAAO,EAAE;AACtB,oBAAA,OAAO,IAAI,CAAA,CAAA,EAAI,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,EAAE,CAAC;AACpD,oBAAA,YAAY,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC;AACnC,iBAAA;AACF,aAAA;AAAC,YAAA,OAAO,CAAC,EAAE;;AAEX,aAAA;AACD,YAAA,MAAM,IAAI,aAAa,CAAA,aAAA,sCAErB,CAAuB,oBAAA,EAAA,GAAG,MAAM,QAAQ,CAAC,MAAM,CAAA,CAAA,EAAI,QAAQ,CAAC,UAAU,CAAK,EAAA,EAAA,OAAO,EAAE,EACpF;gBACE,MAAM,EAAE,QAAQ,CAAC,MAAM;gBACvB,UAAU,EAAE,QAAQ,CAAC,UAAU;gBAC/B,YAAY;AACb,aAAA,CACF,CAAC;AACH,SAAA;AACF,KAAA;AAAC,IAAA,OAAO,CAAC,EAAE;QACV,IAAI,GAAG,GAAG,CAAU,CAAC;QACrB,IACG,CAAmB,CAAC,IAAI,KAAkC,aAAA;YAC3D,CAAC,YAAY,KAAK,EAClB;AACA,YAAA,GAAG,GAAG,IAAI,aAAa,CAAA,OAAA,gCAErB,uBAAuB,GAAG,CAAC,QAAQ,EAAE,KAAK,CAAC,CAAC,OAAO,CAAA,CAAE,CACtD,CAAC;AACF,YAAA,GAAG,CAAC,KAAK,GAAG,CAAC,CAAC,KAAK,CAAC;AACrB,SAAA;AAED,QAAA,MAAM,GAAG,CAAC;AACX,KAAA;AACD,IAAA,OAAO,QAAQ,CAAC;AAClB,CAAC;AAED;;;;AAIG;AACH,SAAS,iBAAiB,CAAC,cAA+B,EAAA;IACxD,MAAM,YAAY,GAAG,EAAiB,CAAC;AACvC,IAAA,IAAI,CAAA,cAAc,KAAA,IAAA,IAAd,cAAc,KAAd,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,cAAc,CAAE,OAAO,KAAI,CAAA,cAAc,KAAA,IAAA,IAAd,cAAc,KAAd,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,cAAc,CAAE,OAAO,KAAI,CAAC,EAAE;AAC3D,QAAA,MAAM,eAAe,GAAG,IAAI,eAAe,EAAE,CAAC;AAC9C,QAAA,MAAM,MAAM,GAAG,eAAe,CAAC,MAAM,CAAC;AACtC,QAAA,UAAU,CAAC,MAAM,eAAe,CAAC,KAAK,EAAE,EAAE,cAAc,CAAC,OAAO,CAAC,CAAC;AAClE,QAAA,YAAY,CAAC,MAAM,GAAG,MAAM,CAAC;AAC9B,KAAA;AACD,IAAA,OAAO,YAAY,CAAC;AACtB;;ACnMA;;;;;;;;;;;;;;;AAeG;AAQH;;;AAGG;AACI,MAAM,cAAc,GAAG,CAAC,MAAM,EAAE,OAAO,EAAE,UAAU,EAAE,QAAQ,EAAW;AAE/E;;;AAGG;IACS,aAMX;AAND,CAAA,UAAY,YAAY,EAAA;AACtB,IAAA,YAAA,CAAA,2BAAA,CAAA,GAAA,2BAAuD,CAAA;AACvD,IAAA,YAAA,CAAA,2BAAA,CAAA,GAAA,2BAAuD,CAAA;AACvD,IAAA,YAAA,CAAA,iCAAA,CAAA,GAAA,iCAAmE,CAAA;AACnE,IAAA,YAAA,CAAA,0BAAA,CAAA,GAAA,0BAAqD,CAAA;AACrD,IAAA,YAAA,CAAA,iCAAA,CAAA,GAAA,iCAAmE,CAAA;AACrE,CAAC,EANW,YAAY,KAAZ,YAAY,GAMvB,EAAA,CAAA,CAAA,CAAA;AAED;;;AAGG;IACS,mBAWX;AAXD,CAAA,UAAY,kBAAkB,EAAA;;AAE5B,IAAA,kBAAA,CAAA,kCAAA,CAAA,GAAA,kCAAqE,CAAA;;AAErE,IAAA,kBAAA,CAAA,qBAAA,CAAA,GAAA,qBAA2C,CAAA;;AAE3C,IAAA,kBAAA,CAAA,wBAAA,CAAA,GAAA,wBAAiD,CAAA;;AAEjD,IAAA,kBAAA,CAAA,iBAAA,CAAA,GAAA,iBAAmC,CAAA;;AAEnC,IAAA,kBAAA,CAAA,YAAA,CAAA,GAAA,YAAyB,CAAA;AAC3B,CAAC,EAXW,kBAAkB,KAAlB,kBAAkB,GAW7B,EAAA,CAAA,CAAA,CAAA;AAED;;AAEG;IACS,gBAOX;AAPD,CAAA,UAAY,eAAe,EAAA;;AAEzB,IAAA,eAAA,CAAA,+BAAA,CAAA,GAAA,+BAA+D,CAAA;;AAE/D,IAAA,eAAA,CAAA,UAAA,CAAA,GAAA,UAAqB,CAAA;;AAErB,IAAA,eAAA,CAAA,aAAA,CAAA,GAAA,aAA2B,CAAA;AAC7B,CAAC,EAPW,eAAe,KAAf,eAAe,GAO1B,EAAA,CAAA,CAAA,CAAA;AAED;;;AAGG;IACS,gBAWX;AAXD,CAAA,UAAY,eAAe,EAAA;;AAEzB,IAAA,eAAA,CAAA,8BAAA,CAAA,GAAA,8BAA6D,CAAA;;AAE7D,IAAA,eAAA,CAAA,YAAA,CAAA,GAAA,YAAyB,CAAA;;AAEzB,IAAA,eAAA,CAAA,KAAA,CAAA,GAAA,KAAW,CAAA;;AAEX,IAAA,eAAA,CAAA,QAAA,CAAA,GAAA,QAAiB,CAAA;;AAEjB,IAAA,eAAA,CAAA,MAAA,CAAA,GAAA,MAAa,CAAA;AACf,CAAC,EAXW,eAAe,KAAf,eAAe,GAW1B,EAAA,CAAA,CAAA,CAAA;AAED;;;AAGG;IACS,aAWX;AAXD,CAAA,UAAY,YAAY,EAAA;;AAEtB,IAAA,YAAA,CAAA,2BAAA,CAAA,GAAA,2BAAuD,CAAA;;AAEvD,IAAA,YAAA,CAAA,0BAAA,CAAA,GAAA,0BAAqD,CAAA;;AAErD,IAAA,YAAA,CAAA,mBAAA,CAAA,GAAA,mBAAuC,CAAA;;AAEvC,IAAA,YAAA,CAAA,sBAAA,CAAA,GAAA,sBAA6C,CAAA;;AAE7C,IAAA,YAAA,CAAA,oBAAA,CAAA,GAAA,oBAAyC,CAAA;AAC3C,CAAC,EAXW,YAAY,KAAZ,YAAY,GAWvB,EAAA,CAAA,CAAA,CAAA;AAED;;;AAGG;IACS,YAOX;AAPD,CAAA,UAAY,WAAW,EAAA;;AAErB,IAAA,WAAA,CAAA,4BAAA,CAAA,GAAA,4BAAyD,CAAA;;AAEzD,IAAA,WAAA,CAAA,QAAA,CAAA,GAAA,QAAiB,CAAA;;AAEjB,IAAA,WAAA,CAAA,OAAA,CAAA,GAAA,OAAe,CAAA;AACjB,CAAC,EAPW,WAAW,KAAX,WAAW,GAOtB,EAAA,CAAA,CAAA,CAAA;AAED;;;AAGG;IACS,aAaX;AAbD,CAAA,UAAY,YAAY,EAAA;;AAEtB,IAAA,YAAA,CAAA,2BAAA,CAAA,GAAA,2BAAuD,CAAA;;AAEvD,IAAA,YAAA,CAAA,MAAA,CAAA,GAAA,MAAa,CAAA;;AAEb,IAAA,YAAA,CAAA,YAAA,CAAA,GAAA,YAAyB,CAAA;;AAEzB,IAAA,YAAA,CAAA,QAAA,CAAA,GAAA,QAAiB,CAAA;;AAEjB,IAAA,YAAA,CAAA,YAAA,CAAA,GAAA,YAAyB,CAAA;;AAEzB,IAAA,YAAA,CAAA,OAAA,CAAA,GAAA,OAAe,CAAA;AACjB,CAAC,EAbW,YAAY,KAAZ,YAAY,GAavB,EAAA,CAAA,CAAA,CAAA;AAED;;AAEG;IACS,oBAcX;AAdD,CAAA,UAAY,mBAAmB,EAAA;;AAE7B,IAAA,mBAAA,CAAA,kBAAA,CAAA,GAAA,kBAAqC,CAAA;;;AAGrC,IAAA,mBAAA,CAAA,MAAA,CAAA,GAAA,MAAa,CAAA;;;;;AAKb,IAAA,mBAAA,CAAA,KAAA,CAAA,GAAA,KAAW,CAAA;;;AAGX,IAAA,mBAAA,CAAA,MAAA,CAAA,GAAA,MAAa,CAAA;AACf,CAAC,EAdW,mBAAmB,KAAnB,mBAAmB,GAc9B,EAAA,CAAA,CAAA;;ACzJD;;;;;;;;;;;;;;;AAeG;AAoKH;;;;AAIG;IACS,8BAaX;AAbD,CAAA,UAAY,6BAA6B,EAAA;;AAEvC,IAAA,6BAAA,CAAA,QAAA,CAAA,GAAA,QAAiB,CAAA;;AAEjB,IAAA,6BAAA,CAAA,QAAA,CAAA,GAAA,QAAiB,CAAA;;AAEjB,IAAA,6BAAA,CAAA,SAAA,CAAA,GAAA,SAAmB,CAAA;;AAEnB,IAAA,6BAAA,CAAA,SAAA,CAAA,GAAA,SAAmB,CAAA;;AAEnB,IAAA,6BAAA,CAAA,OAAA,CAAA,GAAA,OAAe,CAAA;;AAEf,IAAA,6BAAA,CAAA,QAAA,CAAA,GAAA,QAAiB,CAAA;AACnB,CAAC,EAbW,6BAA6B,KAA7B,6BAA6B,GAaxC,EAAA,CAAA,CAAA;;ACrMD;;;;;;;;;;;;;;;AAeG;AAYH;;;AAGG;AACG,SAAU,UAAU,CACxB,QAAiC,EAAA;AAEhC,IAAA,QAA4C,CAAC,IAAI,GAAG,MAAK;QACxD,IAAI,QAAQ,CAAC,UAAU,IAAI,QAAQ,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC,EAAE;AACzD,YAAA,IAAI,QAAQ,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC,EAAE;gBAClC,OAAO,CAAC,IAAI,CACV,CAAA,kBAAA,EAAqB,QAAQ,CAAC,UAAU,CAAC,MAAM,CAAG,CAAA,CAAA;oBAChD,CAA4D,0DAAA,CAAA;AAC5D,oBAAA,CAAA,gEAAA,CAAkE,CACrE,CAAC;AACH,aAAA;YACD,IAAI,kBAAkB,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,EAAE;gBAC9C,MAAM,IAAI,aAAa,CAErB,gBAAA,yCAAA,CAAA,gBAAA,EAAmB,uBAAuB,CACxC,QAAQ,CACT,CAAA,wCAAA,CAA0C,EAC3C;oBACE,QAAQ;AACT,iBAAA,CACF,CAAC;AACH,aAAA;AACD,YAAA,OAAO,OAAO,CAAC,QAAQ,CAAC,CAAC;AAC1B,SAAA;aAAM,IAAI,QAAQ,CAAC,cAAc,EAAE;YAClC,MAAM,IAAI,aAAa,CAErB,gBAAA,yCAAA,CAAA,oBAAA,EAAuB,uBAAuB,CAAC,QAAQ,CAAC,CAAA,CAAE,EAC1D;gBACE,QAAQ;AACT,aAAA,CACF,CAAC;AACH,SAAA;AACD,QAAA,OAAO,EAAE,CAAC;AACZ,KAAC,CAAC;AACD,IAAA,QAA4C,CAAC,aAAa,GAAG,MAAK;QACjE,IAAI,QAAQ,CAAC,UAAU,IAAI,QAAQ,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC,EAAE;AACzD,YAAA,IAAI,QAAQ,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC,EAAE;gBAClC,OAAO,CAAC,IAAI,CACV,CAAA,kBAAA,EAAqB,QAAQ,CAAC,UAAU,CAAC,MAAM,CAAG,CAAA,CAAA;oBAChD,CAAsE,oEAAA,CAAA;AACtE,oBAAA,CAAA,gEAAA,CAAkE,CACrE,CAAC;AACH,aAAA;YACD,IAAI,kBAAkB,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,EAAE;gBAC9C,MAAM,IAAI,aAAa,CAErB,gBAAA,yCAAA,CAAA,gBAAA,EAAmB,uBAAuB,CACxC,QAAQ,CACT,CAAA,wCAAA,CAA0C,EAC3C;oBACE,QAAQ;AACT,iBAAA,CACF,CAAC;AACH,aAAA;AACD,YAAA,OAAO,gBAAgB,CAAC,QAAQ,CAAC,CAAC;AACnC,SAAA;aAAM,IAAI,QAAQ,CAAC,cAAc,EAAE;YAClC,MAAM,IAAI,aAAa,CAErB,gBAAA,yCAAA,CAAA,6BAAA,EAAgC,uBAAuB,CAAC,QAAQ,CAAC,CAAA,CAAE,EACnE;gBACE,QAAQ;AACT,aAAA,CACF,CAAC;AACH,SAAA;AACD,QAAA,OAAO,SAAS,CAAC;AACnB,KAAC,CAAC;AACF,IAAA,OAAO,QAA2C,CAAC;AACrD,CAAC;AAED;;AAEG;AACG,SAAU,OAAO,CAAC,QAAiC,EAAA;;IACvD,MAAM,WAAW,GAAG,EAAE,CAAC;IACvB,IAAI,CAAA,EAAA,GAAA,CAAA,EAAA,GAAA,QAAQ,CAAC,UAAU,MAAG,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,CAAC,CAAE,CAAA,OAAO,MAAE,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,KAAK,EAAE;AAC3C,QAAA,KAAK,MAAM,IAAI,IAAI,CAAA,EAAA,GAAA,MAAA,QAAQ,CAAC,UAAU,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAG,CAAC,CAAA,CAAE,OAAO,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAE,KAAK,EAAE;YAC1D,IAAI,IAAI,CAAC,IAAI,EAAE;AACb,gBAAA,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AAC7B,aAAA;AACF,SAAA;AACF,KAAA;AACD,IAAA,IAAI,WAAW,CAAC,MAAM,GAAG,CAAC,EAAE;AAC1B,QAAA,OAAO,WAAW,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;AAC7B,KAAA;AAAM,SAAA;AACL,QAAA,OAAO,EAAE,CAAC;AACX,KAAA;AACH,CAAC;AAED;;AAEG;AACG,SAAU,gBAAgB,CAC9B,QAAiC,EAAA;;IAEjC,MAAM,aAAa,GAAmB,EAAE,CAAC;IACzC,IAAI,CAAA,EAAA,GAAA,CAAA,EAAA,GAAA,QAAQ,CAAC,UAAU,MAAG,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,CAAC,CAAE,CAAA,OAAO,MAAE,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,KAAK,EAAE;AAC3C,QAAA,KAAK,MAAM,IAAI,IAAI,CAAA,EAAA,GAAA,MAAA,QAAQ,CAAC,UAAU,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAG,CAAC,CAAA,CAAE,OAAO,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAE,KAAK,EAAE;YAC1D,IAAI,IAAI,CAAC,YAAY,EAAE;AACrB,gBAAA,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;AACvC,aAAA;AACF,SAAA;AACF,KAAA;AACD,IAAA,IAAI,aAAa,CAAC,MAAM,GAAG,CAAC,EAAE;AAC5B,QAAA,OAAO,aAAa,CAAC;AACtB,KAAA;AAAM,SAAA;AACL,QAAA,OAAO,SAAS,CAAC;AAClB,KAAA;AACH,CAAC;AAED,MAAM,gBAAgB,GAAG,CAAC,YAAY,CAAC,UAAU,EAAE,YAAY,CAAC,MAAM,CAAC,CAAC;AAExE,SAAS,kBAAkB,CAAC,SAAmC,EAAA;AAC7D,IAAA,QACE,CAAC,CAAC,SAAS,CAAC,YAAY;QACxB,gBAAgB,CAAC,QAAQ,CAAC,SAAS,CAAC,YAAY,CAAC,EACjD;AACJ,CAAC;AAEK,SAAU,uBAAuB,CACrC,QAAiC,EAAA;;IAEjC,IAAI,OAAO,GAAG,EAAE,CAAC;AACjB,IAAA,IACE,CAAC,CAAC,QAAQ,CAAC,UAAU,IAAI,QAAQ,CAAC,UAAU,CAAC,MAAM,KAAK,CAAC;QACzD,QAAQ,CAAC,cAAc,EACvB;QACA,OAAO,IAAI,sBAAsB,CAAC;AAClC,QAAA,IAAI,MAAA,QAAQ,CAAC,cAAc,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAE,WAAW,EAAE;YACxC,OAAO,IAAI,WAAW,QAAQ,CAAC,cAAc,CAAC,WAAW,EAAE,CAAC;AAC7D,SAAA;AACD,QAAA,IAAI,MAAA,QAAQ,CAAC,cAAc,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAE,kBAAkB,EAAE;YAC/C,OAAO,IAAI,KAAK,QAAQ,CAAC,cAAc,CAAC,kBAAkB,EAAE,CAAC;AAC9D,SAAA;AACF,KAAA;AAAM,SAAA,IAAI,MAAA,QAAQ,CAAC,UAAU,MAAG,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,CAAC,CAAC,EAAE;QACnC,MAAM,cAAc,GAAG,QAAQ,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;AAC9C,QAAA,IAAI,kBAAkB,CAAC,cAAc,CAAC,EAAE;AACtC,YAAA,OAAO,IAAI,CAAgC,6BAAA,EAAA,cAAc,CAAC,YAAY,EAAE,CAAC;YACzE,IAAI,cAAc,CAAC,aAAa,EAAE;AAChC,gBAAA,OAAO,IAAI,CAAK,EAAA,EAAA,cAAc,CAAC,aAAa,EAAE,CAAC;AAChD,aAAA;AACF,SAAA;AACF,KAAA;AACD,IAAA,OAAO,OAAO,CAAC;AACjB;;AC/KA;;;;;;;;;;;;;;;AAeG;AAaH,MAAM,cAAc,GAAG,oCAAoC,CAAC;AAE5D;;;;;;;AAOG;AACG,SAAU,aAAa,CAAC,QAAkB,EAAA;IAC9C,MAAM,WAAW,GAAG,QAAQ,CAAC,IAAK,CAAC,WAAW,CAC5C,IAAI,iBAAiB,CAAC,MAAM,EAAE,EAAE,KAAK,EAAE,IAAI,EAAE,CAAC,CAC/C,CAAC;AACF,IAAA,MAAM,cAAc,GAClB,iBAAiB,CAA0B,WAAW,CAAC,CAAC;IAC1D,MAAM,CAAC,OAAO,EAAE,OAAO,CAAC,GAAG,cAAc,CAAC,GAAG,EAAE,CAAC;IAChD,OAAO;AACL,QAAA,MAAM,EAAE,wBAAwB,CAAC,OAAO,CAAC;AACzC,QAAA,QAAQ,EAAE,kBAAkB,CAAC,OAAO,CAAC;KACtC,CAAC;AACJ,CAAC;AAED,eAAe,kBAAkB,CAC/B,MAA+C,EAAA;IAE/C,MAAM,YAAY,GAA8B,EAAE,CAAC;AACnD,IAAA,MAAM,MAAM,GAAG,MAAM,CAAC,SAAS,EAAE,CAAC;AAClC,IAAA,OAAO,IAAI,EAAE;QACX,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,MAAM,CAAC,IAAI,EAAE,CAAC;AAC5C,QAAA,IAAI,IAAI,EAAE;AACR,YAAA,OAAO,UAAU,CAAC,kBAAkB,CAAC,YAAY,CAAC,CAAC,CAAC;AACrD,SAAA;AACD,QAAA,YAAY,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;AAC1B,KAAA;AACH,CAAC;AAED,SAAgB,wBAAwB,CACtC,MAA+C,EAAA;;AAE/C,QAAA,MAAM,MAAM,GAAG,MAAM,CAAC,SAAS,EAAE,CAAC;AAClC,QAAA,OAAO,IAAI,EAAE;AACX,YAAA,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,GAAG,MAAM,OAAA,CAAA,MAAM,CAAC,IAAI,EAAE,CAAA,CAAC;AAC5C,YAAA,IAAI,IAAI,EAAE;gBACR,MAAM;AACP,aAAA;AACD,YAAA,MAAA,MAAA,OAAA,CAAM,UAAU,CAAC,KAAK,CAAC,CAAA,CAAC;AACzB,SAAA;KACF,CAAA,CAAA;AAAA,CAAA;AAED;;;;AAIG;AACG,SAAU,iBAAiB,CAC/B,WAAmC,EAAA;AAEnC,IAAA,MAAM,MAAM,GAAG,WAAW,CAAC,SAAS,EAAE,CAAC;AACvC,IAAA,MAAM,MAAM,GAAG,IAAI,cAAc,CAAI;AACnC,QAAA,KAAK,CAAC,UAAU,EAAA;YACd,IAAI,WAAW,GAAG,EAAE,CAAC;YACrB,OAAO,IAAI,EAAE,CAAC;AACd,YAAA,SAAS,IAAI,GAAA;AACX,gBAAA,OAAO,MAAM,CAAC,IAAI,EAAE,CAAC,IAAI,CAAC,CAAC,EAAE,KAAK,EAAE,IAAI,EAAE,KAAI;AAC5C,oBAAA,IAAI,IAAI,EAAE;AACR,wBAAA,IAAI,WAAW,CAAC,IAAI,EAAE,EAAE;4BACtB,UAAU,CAAC,KAAK,CACd,IAAI,aAAa,CAEf,cAAA,uCAAA,wBAAwB,CACzB,CACF,CAAC;4BACF,OAAO;AACR,yBAAA;wBACD,UAAU,CAAC,KAAK,EAAE,CAAC;wBACnB,OAAO;AACR,qBAAA;oBAED,WAAW,IAAI,KAAK,CAAC;oBACrB,IAAI,KAAK,GAAG,WAAW,CAAC,KAAK,CAAC,cAAc,CAAC,CAAC;AAC9C,oBAAA,IAAI,cAAiB,CAAC;AACtB,oBAAA,OAAO,KAAK,EAAE;wBACZ,IAAI;4BACF,cAAc,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;AACvC,yBAAA;AAAC,wBAAA,OAAO,CAAC,EAAE;AACV,4BAAA,UAAU,CAAC,KAAK,CACd,IAAI,aAAa,CAEf,cAAA,uCAAA,CAAA,8BAAA,EAAiC,KAAK,CAAC,CAAC,CAAC,CAAE,CAAA,CAC5C,CACF,CAAC;4BACF,OAAO;AACR,yBAAA;AACD,wBAAA,UAAU,CAAC,OAAO,CAAC,cAAc,CAAC,CAAC;AACnC,wBAAA,WAAW,GAAG,WAAW,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC;AACrD,wBAAA,KAAK,GAAG,WAAW,CAAC,KAAK,CAAC,cAAc,CAAC,CAAC;AAC3C,qBAAA;oBACD,OAAO,IAAI,EAAE,CAAC;AAChB,iBAAC,CAAC,CAAC;aACJ;SACF;AACF,KAAA,CAAC,CAAC;AACH,IAAA,OAAO,MAAM,CAAC;AAChB,CAAC;AAED;;;AAGG;AACG,SAAU,kBAAkB,CAChC,SAAoC,EAAA;IAEpC,MAAM,YAAY,GAAG,SAAS,CAAC,SAAS,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;AACrD,IAAA,MAAM,kBAAkB,GAA4B;AAClD,QAAA,cAAc,EAAE,YAAY,KAAA,IAAA,IAAZ,YAAY,KAAZ,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,YAAY,CAAE,cAAc;KAC7C,CAAC;AACF,IAAA,KAAK,MAAM,QAAQ,IAAI,SAAS,EAAE;QAChC,IAAI,QAAQ,CAAC,UAAU,EAAE;AACvB,YAAA,KAAK,MAAM,SAAS,IAAI,QAAQ,CAAC,UAAU,EAAE;AAC3C,gBAAA,MAAM,CAAC,GAAG,SAAS,CAAC,KAAK,CAAC;AAC1B,gBAAA,IAAI,CAAC,kBAAkB,CAAC,UAAU,EAAE;AAClC,oBAAA,kBAAkB,CAAC,UAAU,GAAG,EAAE,CAAC;AACpC,iBAAA;AACD,gBAAA,IAAI,CAAC,kBAAkB,CAAC,UAAU,CAAC,CAAC,CAAC,EAAE;AACrC,oBAAA,kBAAkB,CAAC,UAAU,CAAC,CAAC,CAAC,GAAG;wBACjC,KAAK,EAAE,SAAS,CAAC,KAAK;qBACK,CAAC;AAC/B,iBAAA;;AAED,gBAAA,kBAAkB,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,gBAAgB;oBAC/C,SAAS,CAAC,gBAAgB,CAAC;gBAC7B,kBAAkB,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,YAAY,GAAG,SAAS,CAAC,YAAY,CAAC;AACvE,gBAAA,kBAAkB,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,aAAa;oBAC5C,SAAS,CAAC,aAAa,CAAC;AAC1B,gBAAA,kBAAkB,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,aAAa;oBAC5C,SAAS,CAAC,aAAa,CAAC;AAE1B;;;AAGG;gBACH,IAAI,SAAS,CAAC,OAAO,IAAI,SAAS,CAAC,OAAO,CAAC,KAAK,EAAE;oBAChD,IAAI,CAAC,kBAAkB,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,OAAO,EAAE;AAC7C,wBAAA,kBAAkB,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,OAAO,GAAG;AACzC,4BAAA,IAAI,EAAE,SAAS,CAAC,OAAO,CAAC,IAAI,IAAI,MAAM;AACtC,4BAAA,KAAK,EAAE,EAAE;yBACV,CAAC;AACH,qBAAA;oBACD,MAAM,OAAO,GAAkB,EAAE,CAAC;oBAClC,KAAK,MAAM,IAAI,IAAI,SAAS,CAAC,OAAO,CAAC,KAAK,EAAE;wBAC1C,IAAI,IAAI,CAAC,IAAI,EAAE;AACb,4BAAA,OAAO,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC;AAC1B,yBAAA;wBACD,IAAI,IAAI,CAAC,YAAY,EAAE;AACrB,4BAAA,OAAO,CAAC,YAAY,GAAG,IAAI,CAAC,YAAY,CAAC;AAC1C,yBAAA;wBACD,IAAI,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,MAAM,KAAK,CAAC,EAAE;AACrC,4BAAA,OAAO,CAAC,IAAI,GAAG,EAAE,CAAC;AACnB,yBAAA;AACD,wBAAA,kBAAkB,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,KAAK,CAAC,IAAI,CACjD,OAAe,CAChB,CAAC;AACH,qBAAA;AACF,iBAAA;AACF,aAAA;AACF,SAAA;AACF,KAAA;AACD,IAAA,OAAO,kBAAkB,CAAC;AAC5B;;ACrMA;;;;;;;;;;;;;;;AAeG;AAcI,eAAe,qBAAqB,CACzC,WAAwB,EACxB,KAAa,EACb,MAA8B,EAC9B,cAA+B,EAAA;IAE/B,MAAM,QAAQ,GAAG,MAAM,WAAW,CAChC,KAAK,EACL,IAAI,CAAC,uBAAuB,EAC5B,WAAW;AACX,iBAAa,IAAI,EACjB,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,EACtB,cAAc,CACf,CAAC;AACF,IAAA,OAAO,aAAa,CAAC,QAAQ,CAAC,CAAC;AACjC,CAAC;AAEM,eAAe,eAAe,CACnC,WAAwB,EACxB,KAAa,EACb,MAA8B,EAC9B,cAA+B,EAAA;IAE/B,MAAM,QAAQ,GAAG,MAAM,WAAW,CAChC,KAAK,EACL,IAAI,CAAC,gBAAgB,EACrB,WAAW;AACX,iBAAa,KAAK,EAClB,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,EACtB,cAAc,CACf,CAAC;AACF,IAAA,MAAM,YAAY,GAA4B,MAAM,QAAQ,CAAC,IAAI,EAAE,CAAC;AACpE,IAAA,MAAM,gBAAgB,GAAG,UAAU,CAAC,YAAY,CAAC,CAAC;IAClD,OAAO;AACL,QAAA,QAAQ,EAAE,gBAAgB;KAC3B,CAAC;AACJ;;ACjEA;;;;;;;;;;;;;;;AAeG;AAUG,SAAU,uBAAuB,CACrC,KAA+B,EAAA;;IAG/B,IAAI,KAAK,IAAI,IAAI,EAAE;AACjB,QAAA,OAAO,SAAS,CAAC;AAClB,KAAA;AAAM,SAAA,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE;AACpC,QAAA,OAAO,EAAE,IAAI,EAAE,QAAQ,EAAE,KAAK,EAAE,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE,CAAC,EAAa,CAAC;AAChE,KAAA;SAAM,IAAK,KAAc,CAAC,IAAI,EAAE;QAC/B,OAAO,EAAE,IAAI,EAAE,QAAQ,EAAE,KAAK,EAAE,CAAC,KAAa,CAAC,EAAE,CAAC;AACnD,KAAA;SAAM,IAAK,KAAiB,CAAC,KAAK,EAAE;AACnC,QAAA,IAAI,CAAE,KAAiB,CAAC,IAAI,EAAE;YAC5B,OAAO,EAAE,IAAI,EAAE,QAAQ,EAAE,KAAK,EAAG,KAAiB,CAAC,KAAK,EAAE,CAAC;AAC5D,SAAA;AAAM,aAAA;AACL,YAAA,OAAO,KAAgB,CAAC;AACzB,SAAA;AACF,KAAA;AACH,CAAC;AAEK,SAAU,gBAAgB,CAC9B,OAAsC,EAAA;IAEtC,IAAI,QAAQ,GAAW,EAAE,CAAC;AAC1B,IAAA,IAAI,OAAO,OAAO,KAAK,QAAQ,EAAE;QAC/B,QAAQ,GAAG,CAAC,EAAE,IAAI,EAAE,OAAO,EAAE,CAAC,CAAC;AAChC,KAAA;AAAM,SAAA;AACL,QAAA,KAAK,MAAM,YAAY,IAAI,OAAO,EAAE;AAClC,YAAA,IAAI,OAAO,YAAY,KAAK,QAAQ,EAAE;gBACpC,QAAQ,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,YAAY,EAAE,CAAC,CAAC;AACvC,aAAA;AAAM,iBAAA;AACL,gBAAA,QAAQ,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;AAC7B,aAAA;AACF,SAAA;AACF,KAAA;AACD,IAAA,OAAO,8CAA8C,CAAC,QAAQ,CAAC,CAAC;AAClE,CAAC;AAED;;;;;;;AAOG;AACH,SAAS,8CAA8C,CACrD,KAAa,EAAA;IAEb,MAAM,WAAW,GAAY,EAAE,IAAI,EAAE,MAAM,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC;IACzD,MAAM,eAAe,GAAY,EAAE,IAAI,EAAE,UAAU,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC;IACjE,IAAI,cAAc,GAAG,KAAK,CAAC;IAC3B,IAAI,kBAAkB,GAAG,KAAK,CAAC;AAC/B,IAAA,KAAK,MAAM,IAAI,IAAI,KAAK,EAAE;QACxB,IAAI,kBAAkB,IAAI,IAAI,EAAE;AAC9B,YAAA,eAAe,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACjC,kBAAkB,GAAG,IAAI,CAAC;AAC3B,SAAA;AAAM,aAAA;AACL,YAAA,WAAW,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC7B,cAAc,GAAG,IAAI,CAAC;AACvB,SAAA;AACF,KAAA;IAED,IAAI,cAAc,IAAI,kBAAkB,EAAE;AACxC,QAAA,MAAM,IAAI,aAAa,CAErB,iBAAA,0CAAA,4HAA4H,CAC7H,CAAC;AACH,KAAA;AAED,IAAA,IAAI,CAAC,cAAc,IAAI,CAAC,kBAAkB,EAAE;AAC1C,QAAA,MAAM,IAAI,aAAa,CAErB,iBAAA,0CAAA,kDAAkD,CACnD,CAAC;AACH,KAAA;AAED,IAAA,IAAI,cAAc,EAAE;AAClB,QAAA,OAAO,WAAW,CAAC;AACpB,KAAA;AAED,IAAA,OAAO,eAAe,CAAC;AACzB,CAAC;AAEK,SAAU,0BAA0B,CACxC,MAA8D,EAAA;AAE9D,IAAA,IAAI,gBAAwC,CAAC;IAC7C,IAAK,MAAiC,CAAC,QAAQ,EAAE;QAC/C,gBAAgB,GAAG,MAAgC,CAAC;AACrD,KAAA;AAAM,SAAA;;AAEL,QAAA,MAAM,OAAO,GAAG,gBAAgB,CAAC,MAAuC,CAAC,CAAC;QAC1E,gBAAgB,GAAG,EAAE,QAAQ,EAAE,CAAC,OAAO,CAAC,EAAE,CAAC;AAC5C,KAAA;IACD,IAAK,MAAiC,CAAC,iBAAiB,EAAE;QACxD,gBAAgB,CAAC,iBAAiB,GAAG,uBAAuB,CACzD,MAAiC,CAAC,iBAAiB,CACrD,CAAC;AACH,KAAA;AACD,IAAA,OAAO,gBAAgB,CAAC;AAC1B;;AC7HA;;;;;;;;;;;;;;;AAeG;AAWH;AAEA,MAAM,iBAAiB,GAAsB;IAC3C,MAAM;IACN,YAAY;IACZ,cAAc;IACd,kBAAkB;CACnB,CAAC;AAEF,MAAM,oBAAoB,GAAyC;AACjE,IAAA,IAAI,EAAE,CAAC,MAAM,EAAE,YAAY,CAAC;IAC5B,QAAQ,EAAE,CAAC,kBAAkB,CAAC;AAC9B,IAAA,KAAK,EAAE,CAAC,MAAM,EAAE,cAAc,CAAC;;IAE/B,MAAM,EAAE,CAAC,MAAM,CAAC;CACjB,CAAC;AAEF,MAAM,4BAA4B,GAA8B;IAC9D,IAAI,EAAE,CAAC,OAAO,CAAC;IACf,QAAQ,EAAE,CAAC,OAAO,CAAC;AACnB,IAAA,KAAK,EAAE,CAAC,MAAM,EAAE,UAAU,CAAC;;AAE3B,IAAA,MAAM,EAAE,EAAE;CACX,CAAC;AAEI,SAAU,mBAAmB,CAAC,OAAkB,EAAA;IACpD,IAAI,WAAW,GAAmB,IAAI,CAAC;AACvC,IAAA,KAAK,MAAM,WAAW,IAAI,OAAO,EAAE;AACjC,QAAA,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,WAAW,CAAC;AACpC,QAAA,IAAI,CAAC,WAAW,IAAI,IAAI,KAAK,MAAM,EAAE;AACnC,YAAA,MAAM,IAAI,aAAa,CAAA,iBAAA,0CAErB,iDAAiD,IAAI,CAAA,CAAE,CACxD,CAAC;AACH,SAAA;AACD,QAAA,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE;AAClC,YAAA,MAAM,IAAI,aAAa,CAErB,iBAAA,0CAAA,CAAA,yCAAA,EAA4C,IAAI,CAAyB,sBAAA,EAAA,IAAI,CAAC,SAAS,CACrF,cAAc,CACf,CAAA,CAAE,CACJ,CAAC;AACH,SAAA;AAED,QAAA,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE;AACzB,YAAA,MAAM,IAAI,aAAa,CAErB,iBAAA,0CAAA,CAAA,+DAAA,CAAiE,CAClE,CAAC;AACH,SAAA;AAED,QAAA,IAAI,KAAK,CAAC,MAAM,KAAK,CAAC,EAAE;AACtB,YAAA,MAAM,IAAI,aAAa,CAErB,iBAAA,0CAAA,CAAA,0CAAA,CAA4C,CAC7C,CAAC;AACH,SAAA;AAED,QAAA,MAAM,WAAW,GAA+B;AAC9C,YAAA,IAAI,EAAE,CAAC;AACP,YAAA,UAAU,EAAE,CAAC;AACb,YAAA,YAAY,EAAE,CAAC;AACf,YAAA,gBAAgB,EAAE,CAAC;SACpB,CAAC;AAEF,QAAA,KAAK,MAAM,IAAI,IAAI,KAAK,EAAE;AACxB,YAAA,KAAK,MAAM,GAAG,IAAI,iBAAiB,EAAE;gBACnC,IAAI,GAAG,IAAI,IAAI,EAAE;AACf,oBAAA,WAAW,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;AACvB,iBAAA;AACF,aAAA;AACF,SAAA;AACD,QAAA,MAAM,UAAU,GAAG,oBAAoB,CAAC,IAAI,CAAC,CAAC;AAC9C,QAAA,KAAK,MAAM,GAAG,IAAI,iBAAiB,EAAE;AACnC,YAAA,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,GAAG,CAAC,IAAI,WAAW,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE;gBACrD,MAAM,IAAI,aAAa,CAErB,iBAAA,0CAAA,CAAA,mBAAA,EAAsB,IAAI,CAAoB,iBAAA,EAAA,GAAG,CAAQ,MAAA,CAAA,CAC1D,CAAC;AACH,aAAA;AACF,SAAA;AAED,QAAA,IAAI,WAAW,EAAE;AACf,YAAA,MAAM,yBAAyB,GAAG,4BAA4B,CAAC,IAAI,CAAC,CAAC;YACrE,IAAI,CAAC,yBAAyB,CAAC,QAAQ,CAAC,WAAW,CAAC,IAAI,CAAC,EAAE;AACzD,gBAAA,MAAM,IAAI,aAAa,CAAA,iBAAA,0CAErB,sBAAsB,IAAI,CAAA,eAAA,EACxB,WAAW,CAAC,IACd,CAA4B,yBAAA,EAAA,IAAI,CAAC,SAAS,CACxC,4BAA4B,CAC7B,CAAA,CAAE,CACJ,CAAC;AACH,aAAA;AACF,SAAA;QACD,WAAW,GAAG,WAAW,CAAC;AAC3B,KAAA;AACH;;AC3HA;;;;;;;;;;;;;;;AAeG;AAiBH;;AAEG;AACH,MAAM,YAAY,GAAG,cAAc,CAAC;AAEpC;;;;;AAKG;MACU,WAAW,CAAA;AAKtB,IAAA,WAAA,CACE,WAAwB,EACjB,KAAa,EACb,MAAwB,EACxB,cAA+B,EAAA;QAF/B,IAAK,CAAA,KAAA,GAAL,KAAK,CAAQ;QACb,IAAM,CAAA,MAAA,GAAN,MAAM,CAAkB;QACxB,IAAc,CAAA,cAAA,GAAd,cAAc,CAAiB;QAPhC,IAAQ,CAAA,QAAA,GAAc,EAAE,CAAC;AACzB,QAAA,IAAA,CAAA,YAAY,GAAkB,OAAO,CAAC,OAAO,EAAE,CAAC;AAQtD,QAAA,IAAI,CAAC,YAAY,GAAG,WAAW,CAAC;AAChC,QAAA,IAAI,MAAM,KAAN,IAAA,IAAA,MAAM,uBAAN,MAAM,CAAE,OAAO,EAAE;AACnB,YAAA,mBAAmB,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;AACpC,YAAA,IAAI,CAAC,QAAQ,GAAG,MAAM,CAAC,OAAO,CAAC;AAChC,SAAA;KACF;AAED;;;;AAIG;AACH,IAAA,MAAM,UAAU,GAAA;QACd,MAAM,IAAI,CAAC,YAAY,CAAC;QACxB,OAAO,IAAI,CAAC,QAAQ,CAAC;KACtB;AAED;;;AAGG;IACH,MAAM,WAAW,CACf,OAAsC,EAAA;;QAEtC,MAAM,IAAI,CAAC,YAAY,CAAC;AACxB,QAAA,MAAM,UAAU,GAAG,gBAAgB,CAAC,OAAO,CAAC,CAAC;AAC7C,QAAA,MAAM,sBAAsB,GAA2B;AACrD,YAAA,cAAc,EAAE,CAAA,EAAA,GAAA,IAAI,CAAC,MAAM,0CAAE,cAAc;AAC3C,YAAA,gBAAgB,EAAE,CAAA,EAAA,GAAA,IAAI,CAAC,MAAM,0CAAE,gBAAgB;AAC/C,YAAA,KAAK,EAAE,CAAA,EAAA,GAAA,IAAI,CAAC,MAAM,0CAAE,KAAK;AACzB,YAAA,UAAU,EAAE,CAAA,EAAA,GAAA,IAAI,CAAC,MAAM,0CAAE,UAAU;AACnC,YAAA,iBAAiB,EAAE,CAAA,EAAA,GAAA,IAAI,CAAC,MAAM,0CAAE,iBAAiB;YACjD,QAAQ,EAAE,CAAC,GAAG,IAAI,CAAC,QAAQ,EAAE,UAAU,CAAC;SACzC,CAAC;QACF,IAAI,WAAW,GAAG,EAA2B,CAAC;;AAE9C,QAAA,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,YAAY;aAClC,IAAI,CAAC,MACJ,eAAe,CACb,IAAI,CAAC,YAAY,EACjB,IAAI,CAAC,KAAK,EACV,sBAAsB,EACtB,IAAI,CAAC,cAAc,CACpB,CACF;aACA,IAAI,CAAC,MAAM,IAAG;;AACb,YAAA,IACE,MAAM,CAAC,QAAQ,CAAC,UAAU;gBAC1B,MAAM,CAAC,QAAQ,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC,EACrC;AACA,gBAAA,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;AAC/B,gBAAA,MAAM,eAAe,GAAY;AAC/B,oBAAA,KAAK,EAAE,CAAA,CAAA,EAAA,GAAA,MAAM,CAAC,QAAQ,CAAC,UAAU,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAG,CAAC,CAAE,CAAA,OAAO,CAAC,KAAK,KAAI,EAAE;;AAE1D,oBAAA,IAAI,EAAE,CAAA,CAAA,EAAA,GAAA,MAAM,CAAC,QAAQ,CAAC,UAAU,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAG,CAAC,CAAE,CAAA,OAAO,CAAC,IAAI,KAAI,OAAO;iBAC9D,CAAC;AACF,gBAAA,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;AACrC,aAAA;AAAM,iBAAA;gBACL,MAAM,iBAAiB,GAAG,uBAAuB,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;AACnE,gBAAA,IAAI,iBAAiB,EAAE;AACrB,oBAAA,OAAO,CAAC,IAAI,CACV,mCAAmC,iBAAiB,CAAA,sCAAA,CAAwC,CAC7F,CAAC;AACH,iBAAA;AACF,aAAA;YACD,WAAW,GAAG,MAAM,CAAC;AACvB,SAAC,CAAC,CAAC;QACL,MAAM,IAAI,CAAC,YAAY,CAAC;AACxB,QAAA,OAAO,WAAW,CAAC;KACpB;AAED;;;;AAIG;IACH,MAAM,iBAAiB,CACrB,OAAsC,EAAA;;QAEtC,MAAM,IAAI,CAAC,YAAY,CAAC;AACxB,QAAA,MAAM,UAAU,GAAG,gBAAgB,CAAC,OAAO,CAAC,CAAC;AAC7C,QAAA,MAAM,sBAAsB,GAA2B;AACrD,YAAA,cAAc,EAAE,CAAA,EAAA,GAAA,IAAI,CAAC,MAAM,0CAAE,cAAc;AAC3C,YAAA,gBAAgB,EAAE,CAAA,EAAA,GAAA,IAAI,CAAC,MAAM,0CAAE,gBAAgB;AAC/C,YAAA,KAAK,EAAE,CAAA,EAAA,GAAA,IAAI,CAAC,MAAM,0CAAE,KAAK;AACzB,YAAA,UAAU,EAAE,CAAA,EAAA,GAAA,IAAI,CAAC,MAAM,0CAAE,UAAU;AACnC,YAAA,iBAAiB,EAAE,CAAA,EAAA,GAAA,IAAI,CAAC,MAAM,0CAAE,iBAAiB;YACjD,QAAQ,EAAE,CAAC,GAAG,IAAI,CAAC,QAAQ,EAAE,UAAU,CAAC;SACzC,CAAC;AACF,QAAA,MAAM,aAAa,GAAG,qBAAqB,CACzC,IAAI,CAAC,YAAY,EACjB,IAAI,CAAC,KAAK,EACV,sBAAsB,EACtB,IAAI,CAAC,cAAc,CACpB,CAAC;;AAGF,QAAA,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,YAAY;AAClC,aAAA,IAAI,CAAC,MAAM,aAAa,CAAC;;;aAGzB,KAAK,CAAC,QAAQ,IAAG;AAChB,YAAA,MAAM,IAAI,KAAK,CAAC,YAAY,CAAC,CAAC;AAChC,SAAC,CAAC;aACD,IAAI,CAAC,YAAY,IAAI,YAAY,CAAC,QAAQ,CAAC;aAC3C,IAAI,CAAC,QAAQ,IAAG;YACf,IAAI,QAAQ,CAAC,UAAU,IAAI,QAAQ,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC,EAAE;AACzD,gBAAA,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;gBAC/B,MAAM,eAAe,GAAQ,MAAA,CAAA,MAAA,CAAA,EAAA,EAAA,QAAQ,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,OAAO,CAAE,CAAC;;AAE9D,gBAAA,IAAI,CAAC,eAAe,CAAC,IAAI,EAAE;AACzB,oBAAA,eAAe,CAAC,IAAI,GAAG,OAAO,CAAC;AAChC,iBAAA;AACD,gBAAA,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;AACrC,aAAA;AAAM,iBAAA;AACL,gBAAA,MAAM,iBAAiB,GAAG,uBAAuB,CAAC,QAAQ,CAAC,CAAC;AAC5D,gBAAA,IAAI,iBAAiB,EAAE;AACrB,oBAAA,OAAO,CAAC,IAAI,CACV,yCAAyC,iBAAiB,CAAA,sCAAA,CAAwC,CACnG,CAAC;AACH,iBAAA;AACF,aAAA;AACH,SAAC,CAAC;aACD,KAAK,CAAC,CAAC,IAAG;;;;AAIT,YAAA,IAAI,CAAC,CAAC,OAAO,KAAK,YAAY,EAAE;;;AAG9B,gBAAA,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;AAClB,aAAA;AACH,SAAC,CAAC,CAAC;AACL,QAAA,OAAO,aAAa,CAAC;KACtB;AACF;;AC7LD;;;;;;;;;;;;;;;AAeG;AAUI,eAAe,WAAW,CAC/B,WAAwB,EACxB,KAAa,EACb,MAA0B,EAC1B,cAA+B,EAAA;IAE/B,MAAM,QAAQ,GAAG,MAAM,WAAW,CAChC,KAAK,EACL,IAAI,CAAC,YAAY,EACjB,WAAW,EACX,KAAK,EACL,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,EACtB,cAAc,CACf,CAAC;AACF,IAAA,OAAO,QAAQ,CAAC,IAAI,EAAE,CAAC;AACzB;;ACxCA;;;;;;;;;;;;;;;AAeG;AAkCH;;;AAGG;MACU,eAAe,CAAA;AAU1B,IAAA,WAAA,CACE,QAAkB,EAClB,WAAwB,EACxB,cAA+B,EAAA;;AAE/B,QAAA,IAAI,EAAC,CAAA,EAAA,GAAA,CAAA,EAAA,GAAA,QAAQ,CAAC,GAAG,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAE,OAAO,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAE,MAAM,CAAA,EAAE;AAClC,YAAA,MAAM,IAAI,aAAa,CAErB,YAAA,qCAAA,CAAA,2HAAA,CAA6H,CAC9H,CAAC;AACH,SAAA;AAAM,aAAA,IAAI,EAAC,CAAA,EAAA,GAAA,CAAA,EAAA,GAAA,QAAQ,CAAC,GAAG,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAE,OAAO,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAE,SAAS,CAAA,EAAE;AAC5C,YAAA,MAAM,IAAI,aAAa,CAErB,eAAA,wCAAA,CAAA,iIAAA,CAAmI,CACpI,CAAC;AACH,SAAA;AAAM,aAAA;YACL,IAAI,CAAC,YAAY,GAAG;AAClB,gBAAA,MAAM,EAAE,QAAQ,CAAC,GAAG,CAAC,OAAO,CAAC,MAAM;AACnC,gBAAA,OAAO,EAAE,QAAQ,CAAC,GAAG,CAAC,OAAO,CAAC,SAAS;gBACvC,QAAQ,EAAE,QAAQ,CAAC,QAAQ;aAC5B,CAAC;YACF,IAAK,QAA4B,CAAC,QAAQ,EAAE;AAC1C,gBAAA,IAAI,CAAC,YAAY,CAAC,gBAAgB,GAAG,MAClC,QAA4B,CAAC,QAAS,CAAC,QAAQ,EAAE,CAAC;AACtD,aAAA;YAED,IAAK,QAA4B,CAAC,IAAI,EAAE;AACtC,gBAAA,IAAI,CAAC,YAAY,CAAC,YAAY,GAAG,MAC9B,QAA4B,CAAC,IAAK,CAAC,QAAQ,EAAE,CAAC;AAClD,aAAA;AACF,SAAA;QACD,IAAI,WAAW,CAAC,KAAK,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE;YACnC,IAAI,WAAW,CAAC,KAAK,CAAC,UAAU,CAAC,SAAS,CAAC,EAAE;;gBAE3C,IAAI,CAAC,KAAK,GAAG,CAAA,kBAAA,EAAqB,WAAW,CAAC,KAAK,EAAE,CAAC;AACvD,aAAA;AAAM,iBAAA;;AAEL,gBAAA,IAAI,CAAC,KAAK,GAAG,WAAW,CAAC,KAAK,CAAC;AAChC,aAAA;AACF,SAAA;AAAM,aAAA;;YAEL,IAAI,CAAC,KAAK,GAAG,CAAA,yBAAA,EAA4B,WAAW,CAAC,KAAK,EAAE,CAAC;AAC9D,SAAA;QACD,IAAI,CAAC,gBAAgB,GAAG,WAAW,CAAC,gBAAgB,IAAI,EAAE,CAAC;QAC3D,IAAI,CAAC,cAAc,GAAG,WAAW,CAAC,cAAc,IAAI,EAAE,CAAC;AACvD,QAAA,IAAI,CAAC,KAAK,GAAG,WAAW,CAAC,KAAK,CAAC;AAC/B,QAAA,IAAI,CAAC,UAAU,GAAG,WAAW,CAAC,UAAU,CAAC;QACzC,IAAI,CAAC,iBAAiB,GAAG,uBAAuB,CAC9C,WAAW,CAAC,iBAAiB,CAC9B,CAAC;AACF,QAAA,IAAI,CAAC,cAAc,GAAG,cAAc,IAAI,EAAE,CAAC;KAC5C;AAED;;;AAGG;IACH,MAAM,eAAe,CACnB,OAA+D,EAAA;AAE/D,QAAA,MAAM,eAAe,GAAG,0BAA0B,CAAC,OAAO,CAAC,CAAC;QAC5D,OAAO,eAAe,CACpB,IAAI,CAAC,YAAY,EACjB,IAAI,CAAC,KAAK,EAAA,MAAA,CAAA,MAAA,CAAA,EAER,gBAAgB,EAAE,IAAI,CAAC,gBAAgB,EACvC,cAAc,EAAE,IAAI,CAAC,cAAc,EACnC,KAAK,EAAE,IAAI,CAAC,KAAK,EACjB,UAAU,EAAE,IAAI,CAAC,UAAU,EAC3B,iBAAiB,EAAE,IAAI,CAAC,iBAAiB,EACtC,EAAA,eAAe,GAEpB,IAAI,CAAC,cAAc,CACpB,CAAC;KACH;AAED;;;;;AAKG;IACH,MAAM,qBAAqB,CACzB,OAA+D,EAAA;AAE/D,QAAA,MAAM,eAAe,GAAG,0BAA0B,CAAC,OAAO,CAAC,CAAC;QAC5D,OAAO,qBAAqB,CAC1B,IAAI,CAAC,YAAY,EACjB,IAAI,CAAC,KAAK,EAAA,MAAA,CAAA,MAAA,CAAA,EAER,gBAAgB,EAAE,IAAI,CAAC,gBAAgB,EACvC,cAAc,EAAE,IAAI,CAAC,cAAc,EACnC,KAAK,EAAE,IAAI,CAAC,KAAK,EACjB,UAAU,EAAE,IAAI,CAAC,UAAU,EAC3B,iBAAiB,EAAE,IAAI,CAAC,iBAAiB,EACtC,EAAA,eAAe,GAEpB,IAAI,CAAC,cAAc,CACpB,CAAC;KACH;AAED;;;AAGG;AACH,IAAA,SAAS,CAAC,eAAiC,EAAA;AACzC,QAAA,OAAO,IAAI,WAAW,CACpB,IAAI,CAAC,YAAY,EACjB,IAAI,CAAC,KAAK,EAER,MAAA,CAAA,MAAA,CAAA,EAAA,KAAK,EAAE,IAAI,CAAC,KAAK,EACjB,UAAU,EAAE,IAAI,CAAC,UAAU,EAC3B,iBAAiB,EAAE,IAAI,CAAC,iBAAiB,EAAA,EACtC,eAAe,CAEpB,EAAA,IAAI,CAAC,cAAc,CACpB,CAAC;KACH;AAED;;AAEG;IACH,MAAM,WAAW,CACf,OAA2D,EAAA;AAE3D,QAAA,MAAM,eAAe,GAAG,0BAA0B,CAAC,OAAO,CAAC,CAAC;AAC5D,QAAA,OAAO,WAAW,CAAC,IAAI,CAAC,YAAY,EAAE,IAAI,CAAC,KAAK,EAAE,eAAe,CAAC,CAAC;KACpE;AACF;;AC/LD;;;;;;;;;;;;;;;AAeG;AAwBH;;;;;;AAMG;SACa,WAAW,CACzB,MAAmB,MAAM,EAAE,EAC3B,OAAyB,EAAA;AAEzB,IAAA,GAAG,GAAG,kBAAkB,CAAC,GAAG,CAAC,CAAC;;IAE9B,MAAM,cAAc,GAAyB,YAAY,CAAC,GAAG,EAAE,WAAW,CAAC,CAAC;IAE5E,OAAO,cAAc,CAAC,YAAY,CAAC;QACjC,UAAU,EAAE,CAAA,OAAO,KAAP,IAAA,IAAA,OAAO,uBAAP,OAAO,CAAE,QAAQ,KAAI,gBAAgB;AAClD,KAAA,CAAC,CAAC;AACL,CAAC;AAED;;;;;AAKG;SACa,kBAAkB,CAChC,QAAkB,EAClB,WAAwB,EACxB,cAA+B,EAAA;AAE/B,IAAA,IAAI,CAAC,WAAW,CAAC,KAAK,EAAE;AACtB,QAAA,MAAM,IAAI,aAAa,CAErB,UAAA,mCAAA,CAAA,kFAAA,CAAoF,CACrF,CAAC;AACH,KAAA;IACD,OAAO,IAAI,eAAe,CAAC,QAAQ,EAAE,WAAW,EAAE,cAAc,CAAC,CAAC;AACpE;;AC7EA;;;;AAIG;AA+BH,SAAS,cAAc,GAAA;AACrB,IAAA,kBAAkB,CAChB,IAAI,SAAS,CACX,WAAW,EACX,CAAC,SAAS,EAAE,EAAE,kBAAkB,EAAE,QAAQ,EAAE,KAAI;;QAE9C,MAAM,GAAG,GAAG,SAAS,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC,YAAY,EAAE,CAAC;QACxD,MAAM,IAAI,GAAG,SAAS,CAAC,WAAW,CAAC,eAAe,CAAC,CAAC;QACpD,MAAM,gBAAgB,GAAG,SAAS,CAAC,WAAW,CAAC,oBAAoB,CAAC,CAAC;AACrE,QAAA,OAAO,IAAI,eAAe,CAAC,GAAG,EAAE,IAAI,EAAE,gBAAgB,EAAE,EAAE,QAAQ,EAAE,CAAC,CAAC;AACxE,KAAC,sCAEF,CAAC,oBAAoB,CAAC,IAAI,CAAC,CAC7B,CAAC;AAEF,IAAA,eAAe,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;;AAE/B,IAAA,eAAe,CAAC,IAAI,EAAE,OAAO,EAAE,SAAkB,CAAC,CAAC;AACrD,CAAC;AAED,cAAc,EAAE;;;;"}