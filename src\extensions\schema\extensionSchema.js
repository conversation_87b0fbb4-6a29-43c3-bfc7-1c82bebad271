export const extensionSchema = {
  type: 'object',
  properties: {
    name: { type: 'string', minLength: 3, maxLength: 100 },
    version: { type: 'string', pattern: '^[0-9]+\\.[0-9]+\\.[0-9]+$' }, // Semantic versioning e.g., 1.0.0
    description: { type: 'string', maxLength: 500 },
    permissions: {
      type: 'array',
      items: { type: 'string', enum: ['api_access', 'read_user_data'] }, // Example permissions
      uniqueItems: true,
      default: [],
    },
    tags: {
      type: 'array',
      items: { type: 'string', maxLength: 20 },
      uniqueItems: true,
      default: [],
    },
    dev_metadata: {
      type: 'object',
      properties: {
        author_id: { type: 'string' },
        created_at: { type: 'string', format: 'date-time' },
        updated_at: { type: 'string', format: 'date-time' },
      },
      required: ['author_id'],
      additionalProperties: false,
    },
    ui_blocks: {
      type: 'array',
      items: {
        type: 'object',
        properties: {
          id: { type: 'string', description: 'Unique identifier for this block' },
          type: { type: 'string', enum: ['label', 'button', 'input', 'image_display', 'container'] },
          content: { type: 'string', description: 'Text content for label, button, etc.' }, // Used by label, button
          placeholder: { type: 'string', description: 'Placeholder for input fields' }, // Used by input
          src: { type: 'string', format: 'url', description: 'URL for image_display' }, // Used by image_display
          altText: { type: 'string', description: 'Alt text for image_display' }, // Used by image_display
          children: { $ref: '#/properties/ui_blocks/items/properties/children' }, // Recursive definition for container blocks
          styles: {
            type: 'object',
            additionalProperties: { type: 'string' },
            description: 'CSS-like styles, e.g., {"color": "red", "fontSize": "12px"}'
          },
          events: {
            type: 'object',
            properties: {
              onClick: { type: 'string', description: 'Name of the function to call on click' },
              onChange: { type: 'string', description: 'Name of the function to call on change (for inputs)' },
            },
            additionalProperties: false,
          },
        },
        required: ['id', 'type'],
        // Conditional requirements based on type could be added here if AJV supports `if/then/else` well enough
        // For simplicity now, some properties are optional and their relevance depends on 'type'.
      },
      default: [],
    },
    logic: {
      type: 'object',
      properties: {
        functions: {
          type: 'object',
          additionalProperties: { // Each property is a function name
            type: 'object',
            properties: {
              actions: {
                type: 'array',
                items: {
                  type: 'object',
                  properties: {
                    actionType: { type: 'string', enum: ['api_call', 'update_state', 'log_message', 'navigate'] },
                    target: { type: 'string', description: 'e.g., API endpoint, state variable name, or route path' },
                    payload: { type: 'object', additionalProperties: true, description: 'Data for the action' },
                  },
                  required: ['actionType', 'target'],
                },
              },
            },
            required: ['actions'],
          },
        },
        event_mappings: { // Example: map a button click to a function
          type: 'object',
          additionalProperties: { type: 'string' } // e.g. "myButton.onClick": "myFunctionName"
        },
        initial_state: {
            type: 'object',
            additionalProperties: true,
            description: 'Initial values for the extension state variables'
        }
      },
      default: { functions: {}, event_mappings: {}, initial_state: {} },
    },
    // downloads field will be managed by the backend, not defined by the developer.
  },
  required: ['name', 'version', 'description', 'ui_blocks', 'logic'],
  additionalProperties: false, // Disallow properties not defined in the schema
};

// Self-referential part for nested children in ui_blocks (for containers)
// This is a common way to handle recursion in JSON schema.
// Note: AJV needs to be configured to handle recursive refs if they are more complex.
// For simple cases like this, it often works out of the box.
extensionSchema.properties.ui_blocks.items.properties.children = {
    type: 'array',
    items: { $ref: '#/properties/ui_blocks/items' }, // Points to the ui_block definition itself
    default: []
};
