import{_ as v,u as b,c as y,o as h,a as n,b as e,f as m,d as c,w as _,g as w,h as l,t as a,n as o,F as k,k as E,l as x,m as i,p as D}from"./index-oz5Qducj.js";import{u as V}from"./extensionsStore-C2g9n1A4.js";const C={class:"dashboard-container"},L={class:"dashboard-header"},N={key:0},S={key:1},A={key:2,class:"loading-message"},P={key:3,class:"error-message"},B={key:4,class:"no-extensions-message"},F={key:5,class:"extensions-list"},M={class:"item-header"},z={class:"version-tag"},G={class:"description-snippet"},I={class:"item-details"},R={class:"item-actions"},T={__name:"DashboardView",setup(U){const d=b(),r=V();x();const p=y(()=>d.user&&d.user.id?r.getUserExtensions(d.user.id):[]);h(async()=>{r.extensions.length===0&&await r.fetchExtensions()});function g(f){if(!f)return"N/A";const s={year:"numeric",month:"long",day:"numeric",hour:"2-digit",minute:"2-digit"};return new Date(f).toLocaleDateString(void 0,s)}return(f,s)=>{const u=w("router-link");return i(),n("div",C,[e("div",L,[s[1]||(s[1]=e("h1",null,"Developer Dashboard",-1)),c(u,{to:{name:"ExtensionCreate"},class:"create-new-button"},{default:_(()=>s[0]||(s[0]=[e("i",{class:"fas fa-plus-circle"},null,-1),o(" Create New Extension ")])),_:1,__:[0]})]),l(d).user?(i(),n("p",N,"Welcome, "+a(l(d).user.name)+"! Manage your extensions here.",1)):(i(),n("p",S,"Loading user information...")),l(r).isLoading?(i(),n("div",A,"Loading your extensions...")):m("",!0),l(r).error?(i(),n("div",P,"Error: "+a(l(r).error),1)):m("",!0),p.value.length===0&&!l(r).isLoading?(i(),n("div",B,[s[3]||(s[3]=o(" You haven't created any extensions yet. ")),c(u,{to:{name:"ExtensionCreate"}},{default:_(()=>s[2]||(s[2]=[o("Get started by creating one!")])),_:1,__:[2]})])):m("",!0),p.value.length>0?(i(),n("div",F,[(i(!0),n(k,null,E(p.value,t=>(i(),n("div",{key:t.id,class:"extension-item"},[e("div",M,[e("h3",null,[o(a(t.name)+" ",1),e("span",z,"v"+a(t.version),1)]),e("span",{class:D(["status-tag",t.is_public?"public":"private"])},a(t.is_public?"Public":"Private"),3)]),e("p",G,a(t.description.substring(0,100))+a(t.description.length>100?"...":""),1),e("div",I,[e("p",null,[s[4]||(s[4]=e("i",{class:"fas fa-download"},null,-1)),o(" "+a(t.downloads)+" downloads",1)]),e("p",null,[s[5]||(s[5]=e("i",{class:"fas fa-clock"},null,-1)),o(" Last updated: "+a(g(t.dev_metadata.updated_at)),1)])]),e("div",R,[c(u,{to:{name:"ExtensionEditor",params:{id:t.id}},class:"action-btn edit-btn"},{default:_(()=>s[6]||(s[6]=[e("i",{class:"fas fa-edit"},null,-1),o(" Edit ")])),_:2,__:[6]},1032,["to"]),c(u,{to:{name:"Analytics",params:{id:t.id}},class:"action-btn analytics-btn"},{default:_(()=>s[7]||(s[7]=[e("i",{class:"fas fa-chart-line"},null,-1),o(" View Analytics ")])),_:2,__:[7]},1032,["to"]),c(u,{to:{name:"ExtensionDetail",params:{id:t.id}},class:"action-btn view-btn"},{default:_(()=>s[8]||(s[8]=[e("i",{class:"fas fa-eye"},null,-1),o(" View Public Page ")])),_:2,__:[8]},1032,["to"])])]))),128))])):m("",!0)])}}},j=v(T,[["__scopeId","data-v-5c0b42c9"]]);export{j as default};
