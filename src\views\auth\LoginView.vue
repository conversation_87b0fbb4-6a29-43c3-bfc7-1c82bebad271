<template>
  <div class="login-container">
    <h1>Developer Login</h1>
    <p>Please log in to access developer tools and extension management.</p>
    <div class="text-gray-600 dark:text-gray-300 mt-6">
      Developer login is only available with real Google or Email/Password authentication.
    </div>
  </div>
</template>

<script setup>
import { useRouter } from 'vue-router';
import { useAuthStore } from '../../store/authStore';

const router = useRouter();
const authStore = useAuthStore();

function performDevLogin() {
  authStore.loginAsDev();
  console.log('Simulating developer login...');
  navigateToNextRoute();
}

function performUserLogin() {
  authStore.loginAsUser();
  console.log('Simulating regular user login...');
  navigateToNextRoute();
}

function navigateToNextRoute() {
  const intendedRoute = authStore.clearIntendedRoute();
  if (intendedRoute) {
    router.push(intendedRoute);
  } else {
    // Default redirection after login if no intended route
    if (authStore.isDev) {
      router.push({ name: 'Dashboard' });
    } else {
      router.push({ name: 'Home' }); // Or marketplace for regular users
    }
  }
}
</script>

<style scoped>
.login-container {
  max-width: 400px;
  margin: 50px auto;
  padding: 20px;
  border: 1px solid #ccc;
  border-radius: 8px;
  text-align: center;
}
h1 {
  font-size: 1.8em;
  margin-bottom: 1em;
}
.login-button {
  display: block;
  width: 100%;
  padding: 10px;
  margin-bottom: 10px;
  background-color: #007bff;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 1em;
}
.login-button:hover {
  background-color: #0056b3;
}
.login-button.secondary {
  background-color: #6c757d;
}
.login-button.secondary:hover {
  background-color: #545b62;
}
.user-status {
  margin-top: 15px;
  font-style: italic;
  color: #333;
}
</style>
