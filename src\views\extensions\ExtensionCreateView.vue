<template>
  <div class="extension-create-page">
    <ExtensionBuilderLayout />
  </div>
</template>

<script setup>
import ExtensionBuilderLayout from '../../components/extensions/ExtensionBuilderLayout.vue';

// Logic specific to creating a new extension can be added here later.
// For example, initializing with a default JSON template.
</script>

<style scoped>
.extension-create-page {
  padding: 20px; /* Add some padding around the builder if needed */
}
</style>
