<template>
  <div class="recording-controls">
    <button
      v-if="!isRecording"
      @click="startRecording"
      class="btn flex items-center space-x-2"
      :class="{ 'btn-primary': !isRecording, 'btn-danger': isRecording }"
      :disabled="isLoading"
    >
      <span v-if="isLoading">Starting...</span>
      <template v-else>
        <i class="fas fa-record-vinyl mr-2"></i>
        <span>Start Recording</span>
      </template>
    </button>

    <button
      v-else
      @click="stopRecording"
      class="btn btn-danger flex items-center space-x-2"
      :disabled="isLoading"
    >
      <span v-if="isLoading">Stopping...</span>
      <template v-else>
        <i class="fas fa-stop-circle mr-2"></i>
        <span>Stop Recording</span>
      </template>
    </button>

    <div v-if="isRecording" class="recording-indicator flex items-center mt-2">
      <span class="recording-dot mr-2"></span>
      <span class="text-sm text-red-600 dark:text-red-400">Recording: {{ formattedDuration }}</span>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted, watch, defineProps, defineEmits } from 'vue'
import { doc, onSnapshot, collection, query, orderBy, getDocs } from 'firebase/firestore'
import { db } from '../firebase/config'
import { startRecording as startRec, stopRecording as stopRec } from '../utils/meetingFeatures'

const props = defineProps({
  meetingId: {
    type: String,
    required: true
  },
  userId: {
    type: String,
    required: true
  }
})

// State
const isRecording = ref(false)
const isLoading = ref(false)
const recordingStartTime = ref(null)
const recordingDuration = ref(0)
const recordingInterval = ref(null)
const recordingData = ref({
  audio: null,
  video: null,
  transcripts: []
})
let recordingUnsubscribe = null

// Computed properties
const formattedDuration = computed(() => {
  const totalSeconds = Math.floor(recordingDuration.value / 1000)
  const minutes = Math.floor(totalSeconds / 60)
  const seconds = totalSeconds % 60

  return `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`
})

// Listen for recording status
onMounted(() => {
  const recordingRef = doc(db, 'meetings', props.meetingId, 'recordings', 'current')

  recordingUnsubscribe = onSnapshot(recordingRef, (doc) => {
    if (doc.exists() && doc.data().active) {
      isRecording.value = true
      recordingStartTime.value = doc.data().startedAt?.toDate() || new Date()

      // Start the timer
      startTimer()
    } else {
      isRecording.value = false
      stopTimer()
    }
  })
})

// Clean up
onUnmounted(() => {
  if (recordingUnsubscribe) {
    recordingUnsubscribe()
  }

  stopTimer()
})

// Start the recording
const startRecording = async () => {
  if (isLoading.value || isRecording.value) return

  try {
    isLoading.value = true
    
    // Get screen capture stream
    const screenStream = await navigator.mediaDevices.getDisplayMedia({
      video: { mediaSource: 'screen' },
      audio: true
    })

    // Get system audio
    const audioStream = await navigator.mediaDevices.getUserMedia({
      audio: true
    })

    // Combine streams
    const combinedStream = new MediaStream([
      ...screenStream.getVideoTracks(),
      ...audioStream.getAudioTracks()
    ])

    // Start recording with combined stream
    await startRec(props.meetingId, props.userId, combinedStream)
  } catch (error) {
    console.error('Error starting recording:', error)
  } finally {
    isLoading.value = false
  }
}

// Stop the recording
const stopRecording = async () => {
  if (isLoading.value || !isRecording.value) return

  try {
    isLoading.value = true
    const recordingData = await stopRec(props.meetingId, props.userId)
    
    // Download the recording
    if (recordingData) {
      const blob = new Blob([recordingData], { type: 'video/webm' })
      const url = URL.createObjectURL(blob)
      const link = document.createElement('a')
      link.href = url
      link.download = `meeting-recording-${props.meetingId}.webm`
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
    }
  } catch (error) {
    console.error('Error stopping recording:', error)
  } finally {
    isLoading.value = false
  }
}

// Collect all recording data (audio, video, transcripts)
const collectRecordingData = async () => {
  try {
    // In a real implementation, this would collect actual audio/video data
    // For this demo, we'll simulate it with placeholder data
    recordingData.value.audio = "Simulated audio data"
    recordingData.value.video = "Simulated video data"

    // Get transcripts from Firestore
    const transcriptsRef = collection(db, 'meetings', props.meetingId, 'transcripts')
    const transcriptsQuery = query(transcriptsRef, orderBy('timestamp', 'asc'))
    const snapshot = await getDocs(transcriptsQuery)

    const transcripts = []
    snapshot.forEach(doc => {
      transcripts.push(doc.data())
    })

    recordingData.value.transcripts = transcripts
  } catch (error) {
    console.error('Error collecting recording data:', error)
  }
}

// Download the recording as a text file (for demo purposes)
const downloadRecording = () => {
  try {
    // Format the transcript data with the requested format
    let content = "MEETING RECORDING\n\n"
    content += `Date: ${new Date().toLocaleDateString()}\n`
    content += `Duration: ${formattedDuration.value}\n\n`
    content += "TRANSCRIPT:\n\n"

    // Format transcripts with the requested format
    recordingData.value.transcripts.forEach(transcript => {
      const timestamp = transcript.timestamp?.toDate ?
        transcript.timestamp.toDate().toLocaleTimeString() :
        new Date().toLocaleTimeString()

      content += `${transcript.userName}: ${transcript.text}\n\n`

      // Add translated version (simulated for demo)
      if (transcript.language && transcript.language !== 'en-US') {
        content += `[Translated to English]: ${transcript.text}\n\n`
      }
    })

    // Create a download link
    const blob = new Blob([content], { type: 'text/plain' })
    const url = URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = `meeting-recording-${props.meetingId}.txt`
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
  } catch (error) {
    console.error('Error downloading recording:', error)
  }
}

// Start the timer
const startTimer = () => {
  // Clear any existing interval
  stopTimer()

  // Update duration immediately
  updateDuration()

  // Start interval to update duration every second
  recordingInterval.value = setInterval(updateDuration, 1000)
}

// Stop the timer
const stopTimer = () => {
  if (recordingInterval.value) {
    clearInterval(recordingInterval.value)
    recordingInterval.value = null
  }
}

// Update the recording duration
const updateDuration = () => {
  if (!recordingStartTime.value) return

  recordingDuration.value = Date.now() - recordingStartTime.value.getTime()
}
</script>

<style scoped>
.recording-dot {
  width: 10px;
  height: 10px;
  background-color: #ef4444;
  border-radius: 50%;
  animation: pulse 1.5s infinite;
}

@keyframes pulse {
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
  100% {
    opacity: 1;
  }
}
</style>
