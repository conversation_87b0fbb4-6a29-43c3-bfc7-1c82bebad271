<template>
  <div class="min-h-screen bg-gray-50 dark:bg-gray-900 transition-colors duration-300">
    <NavBar />
    <Breadcrumb />

    <main>
      <router-view />
    </main>

    <footer class="bg-white dark:bg-gray-800 shadow mt-auto py-4">
      <div class="container mx-auto px-4 text-center text-gray-600 dark:text-gray-400">
        <p>&copy; {{ new Date().getFullYear() }} TheMeet. All rights reserved.</p>
      </div>
    </footer>
  </div>
</template>

<script setup>
import NavBar from './components/NavBar.vue';
import Breadcrumb from './components/Breadcrumb.vue';
import { ref, onMounted, watch } from 'vue'

// Dark mode state
const isDarkMode = ref(false)

// Toggle dark mode
const toggleDarkMode = () => {
  isDarkMode.value = !isDarkMode.value
  localStorage.setItem('darkMode', isDarkMode.value)
  updateTheme()
}

// Update theme based on dark mode state
const updateTheme = () => {
  if (isDarkMode.value) {
    document.documentElement.classList.add('dark')
  } else {
    document.documentElement.classList.remove('dark')
  }
}

// Initialize dark mode from local storage or system preference
onMounted(() => {
  const savedMode = localStorage.getItem('darkMode')

  if (savedMode !== null) {
    isDarkMode.value = savedMode === 'true'
  } else {
    // Check system preference
    isDarkMode.value = window.matchMedia('(prefers-color-scheme: dark)').matches
  }

  updateTheme()

  // Listen for system preference changes
  window.matchMedia('(prefers-color-scheme: dark)').addEventListener('change', e => {
    if (localStorage.getItem('darkMode') === null) {
      isDarkMode.value = e.matches
      updateTheme()
    }
  })
})
</script>

<style>
@import '../public/css/tailwind.css';
</style>
