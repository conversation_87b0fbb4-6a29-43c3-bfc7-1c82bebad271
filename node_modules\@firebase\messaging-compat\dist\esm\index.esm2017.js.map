{"version": 3, "file": "index.esm2017.js", "sources": ["../../src/messaging-compat.ts", "../../src/registerMessagingCompat.ts", "../../src/index.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {\n  FirebaseApp as AppCompat,\n  _FirebaseService\n} from '@firebase/app-compat';\nimport {\n  Messaging,\n  MessagePayload,\n  deleteToken,\n  getToken,\n  onMessage\n} from '@firebase/messaging';\nimport {\n  areCookiesEnabled,\n  isIndexedDBAvailable,\n  NextFn,\n  Observer,\n  Unsubscribe\n} from '@firebase/util';\n\nimport { onBackgroundMessage } from '@firebase/messaging/sw';\n\nexport interface MessagingCompat {\n  getToken(options?: {\n    vapidKey?: string;\n    serviceWorkerRegistration?: ServiceWorkerRegistration;\n  }): Promise<string>;\n\n  deleteToken(): Promise<boolean>;\n\n  onMessage(\n    nextOrObserver: NextFn<MessagePayload> | Observer<MessagePayload>\n  ): Unsubscribe;\n\n  onBackgroundMessage(\n    nextOrObserver: NextFn<MessagePayload> | Observer<MessagePayload>\n  ): Unsubscribe;\n}\n\nexport function isSupported(): boolean {\n  if (self && 'ServiceWorkerGlobalScope' in self) {\n    // Running in ServiceWorker context\n    return isSwSupported();\n  } else {\n    // Assume we are in the window context.\n    return isWindowSupported();\n  }\n}\n\n/**\n * Checks to see if the required APIs exist.\n * Unlike the modular version, it does not check if IndexedDB.open() is allowed\n * in order to keep isSupported() synchronous and maintain v8 compatibility.\n */\nfunction isWindowSupported(): boolean {\n  return (\n    typeof window !== 'undefined' &&\n    isIndexedDBAvailable() &&\n    areCookiesEnabled() &&\n    'serviceWorker' in navigator &&\n    'PushManager' in window &&\n    'Notification' in window &&\n    'fetch' in window &&\n    ServiceWorkerRegistration.prototype.hasOwnProperty('showNotification') &&\n    PushSubscription.prototype.hasOwnProperty('getKey')\n  );\n}\n\n/**\n * Checks to see if the required APIs exist within SW Context.\n */\nfunction isSwSupported(): boolean {\n  return (\n    isIndexedDBAvailable() &&\n    'PushManager' in self &&\n    'Notification' in self &&\n    ServiceWorkerRegistration.prototype.hasOwnProperty('showNotification') &&\n    PushSubscription.prototype.hasOwnProperty('getKey')\n  );\n}\n\nexport class MessagingCompatImpl implements MessagingCompat, _FirebaseService {\n  constructor(readonly app: AppCompat, readonly _delegate: Messaging) {\n    this.app = app;\n    this._delegate = _delegate;\n  }\n\n  async getToken(options?: {\n    vapidKey?: string;\n    serviceWorkerRegistration?: ServiceWorkerRegistration;\n  }): Promise<string> {\n    return getToken(this._delegate, options);\n  }\n\n  async deleteToken(): Promise<boolean> {\n    return deleteToken(this._delegate);\n  }\n\n  onMessage(\n    nextOrObserver: NextFn<MessagePayload> | Observer<MessagePayload>\n  ): Unsubscribe {\n    return onMessage(this._delegate, nextOrObserver);\n  }\n\n  onBackgroundMessage(\n    nextOrObserver: NextFn<MessagePayload> | Observer<MessagePayload>\n  ): Unsubscribe {\n    return onBackgroundMessage(this._delegate, nextOrObserver);\n  }\n}\n", "/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {\n  Component,\n  ComponentContainer,\n  ComponentType,\n  InstanceFactory\n} from '@firebase/component';\nimport { MessagingCompatImpl, isSupported } from './messaging-compat';\nimport firebase, { _FirebaseNamespace } from '@firebase/app-compat';\n\ndeclare module '@firebase/component' {\n  interface NameServiceMapping {\n    'messaging-compat': MessagingCompatImpl;\n  }\n}\n\nconst messagingCompatFactory: InstanceFactory<'messaging-compat'> = (\n  container: ComponentContainer\n) => {\n  if (self && 'ServiceWorkerGlobalScope' in self) {\n    // in sw\n    return new MessagingCompatImpl(\n      container.getProvider('app-compat').getImmediate(),\n      container.getProvider('messaging-sw').getImmediate()\n    );\n  } else {\n    // in window\n    return new MessagingCompatImpl(\n      container.getProvider('app-compat').getImmediate(),\n      container.getProvider('messaging').getImmediate()\n    );\n  }\n};\n\nconst NAMESPACE_EXPORTS = {\n  isSupported\n};\n\nexport function registerMessagingCompat(): void {\n  (firebase as _FirebaseNamespace).INTERNAL.registerComponent(\n    new Component(\n      'messaging-compat',\n      messagingCompatFactory,\n      ComponentType.PUBLIC\n    ).setServiceProps(NAMESPACE_EXPORTS)\n  );\n}\n", "/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { name, version } from '../package.json';\n\nimport firebase from '@firebase/app-compat';\nimport { registerMessagingCompat } from './registerMessagingCompat';\nimport { MessagingCompat } from './messaging-compat';\n\nregisterMessagingCompat();\nfirebase.registerVersion(name, version);\n\n/**\n * Define extension behavior of `registerMessaging`\n */\ndeclare module '@firebase/app-compat' {\n  interface FirebaseNamespace {\n    messaging: {\n      (app?: FirebaseApp): MessagingCompat;\n      isSupported(): boolean;\n    };\n  }\n  interface FirebaseApp {\n    messaging(): MessagingCompat;\n  }\n}\n"], "names": [], "mappings": ";;;;;;;;;AAAA;;;;;;;;;;;;;;;AAeG;SAwCa,WAAW,GAAA;AACzB,IAAA,IAAI,IAAI,IAAI,0BAA0B,IAAI,IAAI,EAAE;;QAE9C,OAAO,aAAa,EAAE,CAAC;AACxB,KAAA;AAAM,SAAA;;QAEL,OAAO,iBAAiB,EAAE,CAAC;AAC5B,KAAA;AACH,CAAC;AAED;;;;AAIG;AACH,SAAS,iBAAiB,GAAA;AACxB,IAAA,QACE,OAAO,MAAM,KAAK,WAAW;AAC7B,QAAA,oBAAoB,EAAE;AACtB,QAAA,iBAAiB,EAAE;AACnB,QAAA,eAAe,IAAI,SAAS;AAC5B,QAAA,aAAa,IAAI,MAAM;AACvB,QAAA,cAAc,IAAI,MAAM;AACxB,QAAA,OAAO,IAAI,MAAM;AACjB,QAAA,yBAAyB,CAAC,SAAS,CAAC,cAAc,CAAC,kBAAkB,CAAC;QACtE,gBAAgB,CAAC,SAAS,CAAC,cAAc,CAAC,QAAQ,CAAC,EACnD;AACJ,CAAC;AAED;;AAEG;AACH,SAAS,aAAa,GAAA;IACpB,QACE,oBAAoB,EAAE;AACtB,QAAA,aAAa,IAAI,IAAI;AACrB,QAAA,cAAc,IAAI,IAAI;AACtB,QAAA,yBAAyB,CAAC,SAAS,CAAC,cAAc,CAAC,kBAAkB,CAAC;QACtE,gBAAgB,CAAC,SAAS,CAAC,cAAc,CAAC,QAAQ,CAAC,EACnD;AACJ,CAAC;MAEY,mBAAmB,CAAA;IAC9B,WAAqB,CAAA,GAAc,EAAW,SAAoB,EAAA;QAA7C,IAAG,CAAA,GAAA,GAAH,GAAG,CAAW;QAAW,IAAS,CAAA,SAAA,GAAT,SAAS,CAAW;AAChE,QAAA,IAAI,CAAC,GAAG,GAAG,GAAG,CAAC;AACf,QAAA,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;KAC5B;IAED,MAAM,QAAQ,CAAC,OAGd,EAAA;QACC,OAAO,QAAQ,CAAC,IAAI,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC;KAC1C;AAED,IAAA,MAAM,WAAW,GAAA;AACf,QAAA,OAAO,WAAW,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;KACpC;AAED,IAAA,SAAS,CACP,cAAiE,EAAA;QAEjE,OAAO,SAAS,CAAC,IAAI,CAAC,SAAS,EAAE,cAAc,CAAC,CAAC;KAClD;AAED,IAAA,mBAAmB,CACjB,cAAiE,EAAA;QAEjE,OAAO,mBAAmB,CAAC,IAAI,CAAC,SAAS,EAAE,cAAc,CAAC,CAAC;KAC5D;AACF;;AC7HD;;;;;;;;;;;;;;;AAeG;AAiBH,MAAM,sBAAsB,GAAwC,CAClE,SAA6B,KAC3B;AACF,IAAA,IAAI,IAAI,IAAI,0BAA0B,IAAI,IAAI,EAAE;;QAE9C,OAAO,IAAI,mBAAmB,CAC5B,SAAS,CAAC,WAAW,CAAC,YAAY,CAAC,CAAC,YAAY,EAAE,EAClD,SAAS,CAAC,WAAW,CAAC,cAAc,CAAC,CAAC,YAAY,EAAE,CACrD,CAAC;AACH,KAAA;AAAM,SAAA;;QAEL,OAAO,IAAI,mBAAmB,CAC5B,SAAS,CAAC,WAAW,CAAC,YAAY,CAAC,CAAC,YAAY,EAAE,EAClD,SAAS,CAAC,WAAW,CAAC,WAAW,CAAC,CAAC,YAAY,EAAE,CAClD,CAAC;AACH,KAAA;AACH,CAAC,CAAC;AAEF,MAAM,iBAAiB,GAAG;IACxB,WAAW;CACZ,CAAC;SAEc,uBAAuB,GAAA;AACpC,IAAA,QAA+B,CAAC,QAAQ,CAAC,iBAAiB,CACzD,IAAI,SAAS,CACX,kBAAkB,EAClB,sBAAsB,sCAEvB,CAAC,eAAe,CAAC,iBAAiB,CAAC,CACrC,CAAC;AACJ;;AC9DA;;;;;;;;;;;;;;;AAeG;AAQH,uBAAuB,EAAE,CAAC;AAC1B,QAAQ,CAAC,eAAe,CAAC,IAAI,EAAE,OAAO,CAAC"}