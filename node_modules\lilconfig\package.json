{"name": "lilconfig", "version": "3.1.3", "description": "A zero-dependency alternative to cosmiconfig", "main": "src/index.js", "types": "src/index.d.ts", "scripts": {"test": "NODE_OPTIONS=--experimental-vm-modules ./node_modules/.bin/jest --coverage", "lint": "biome ci ./src", "types": "tsc"}, "keywords": ["cosmiconfig", "config", "configuration", "search"], "files": ["src/index.*"], "repository": {"type": "git", "url": "https://github.com/antonk52/lilconfig"}, "bugs": "https://github.com/antonk52/lilconfig/issues", "author": "antonk52", "license": "MIT", "devDependencies": {"@biomejs/biome": "^1.6.0", "@types/jest": "^29.5.12", "@types/node": "^14.18.63", "@types/webpack-env": "^1.18.5", "cosmiconfig": "^8.3.6", "jest": "^29.7.0", "typescript": "^5.3.3", "uvu": "^0.5.6"}, "funding": "https://github.com/sponsors/antonk52", "engines": {"node": ">=14"}}