<template>
  <div class="bg-white dark:bg-gray-800 shadow-lg border-l border-gray-200 dark:border-gray-700 w-80 flex flex-col">
    <!-- Header -->
    <div class="p-4 border-b border-gray-200 dark:border-gray-700 flex justify-between items-center">
      <h2 class="font-semibold text-gray-900 dark:text-white flex items-center">
        <i class="fas fa-share-alt mr-2 text-primary"></i>
        Link Activity
      </h2>
      <button @click="$emit('close')" class="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200">
        <i class="fas fa-times"></i>
      </button>
    </div>

    <!-- Controls -->
    <div class="p-4 border-b border-gray-200 dark:border-gray-700 space-y-3">
      <!-- Link Sharing Policy -->
      <div>
        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
          Who can share the meeting link?
        </label>
        <select 
          :value="linkSharingPolicy" 
          @change="$emit('updatePolicy', $event.target.value)"
          class="w-full p-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white text-sm"
        >
          <option value="host">Host Only</option>
          <option value="all">All Participants</option>
        </select>
      </div>

      <!-- Quick Share Button -->
      <button 
        @click="$emit('showInvite')"
        class="w-full btn btn-primary flex items-center justify-center"
      >
        <i class="fas fa-share mr-2"></i>
        Share Meeting Link
      </button>
    </div>

    <!-- Activity Feed -->
    <div class="flex-1 overflow-y-auto">
      <div class="p-4">
        <h3 class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-3 flex items-center">
          <i class="fas fa-history mr-2"></i>
          Recent Activity
        </h3>

        <div v-if="activities.length === 0" class="text-center py-8">
          <i class="fas fa-share-alt text-4xl text-gray-300 dark:text-gray-600 mb-3"></i>
          <p class="text-sm text-gray-500 dark:text-gray-400">No link sharing activity yet</p>
        </div>

        <div v-else class="space-y-3">
          <div 
            v-for="activity in sortedActivities" 
            :key="activity.id"
            class="bg-gray-50 dark:bg-gray-700 rounded-lg p-3 border border-gray-200 dark:border-gray-600"
          >
            <!-- Link Shared Activity -->
            <div v-if="activity.type === 'link_shared'" class="flex items-start space-x-3">
              <div class="w-8 h-8 bg-blue-100 dark:bg-blue-900 rounded-full flex items-center justify-center flex-shrink-0">
                <i class="fas fa-share text-blue-600 dark:text-blue-400 text-sm"></i>
              </div>
              <div class="flex-1 min-w-0">
                <p class="text-sm text-gray-900 dark:text-white">
                  <strong>{{ getParticipantName(activity.sharedBy) }}</strong> shared the meeting link
                </p>
                <div class="flex items-center mt-1 text-xs text-gray-500 dark:text-gray-400">
                  <i class="fas fa-clock mr-1"></i>
                  {{ formatTime(activity.timestamp) }}
                  <span v-if="activity.method" class="ml-2 px-2 py-0.5 bg-gray-200 dark:bg-gray-600 rounded text-xs">
                    {{ activity.method }}
                  </span>
                </div>
              </div>
            </div>

            <!-- User Joined Activity -->
            <div v-else-if="activity.type === 'user_joined'" class="flex items-start space-x-3">
              <div class="w-8 h-8 bg-green-100 dark:bg-green-900 rounded-full flex items-center justify-center flex-shrink-0">
                <i class="fas fa-user-plus text-green-600 dark:text-green-400 text-sm"></i>
              </div>
              <div class="flex-1 min-w-0">
                <p class="text-sm text-gray-900 dark:text-white">
                  <strong>{{ activity.joinedName }}</strong> joined via shared link
                </p>
                <div class="flex items-center mt-1 text-xs text-gray-500 dark:text-gray-400">
                  <i class="fas fa-clock mr-1"></i>
                  {{ formatTime(activity.timestamp) }}
                  <span v-if="activity.sharedBy" class="ml-2 text-xs">
                    (Link shared by {{ getParticipantName(activity.sharedBy) }})
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Statistics -->
    <div class="p-4 border-t border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-700">
      <h3 class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Statistics</h3>
      <div class="grid grid-cols-2 gap-4 text-center">
        <div>
          <div class="text-lg font-semibold text-primary">{{ linkShareCount }}</div>
          <div class="text-xs text-gray-500 dark:text-gray-400">Links Shared</div>
        </div>
        <div>
          <div class="text-lg font-semibold text-green-600">{{ joinViaLinkCount }}</div>
          <div class="text-xs text-gray-500 dark:text-gray-400">Joined via Link</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'

const props = defineProps({
  activities: {
    type: Array,
    default: () => []
  },
  linkSharingPolicy: {
    type: String,
    default: 'host'
  },
  participants: {
    type: Array,
    default: () => []
  }
})

const emit = defineEmits(['close', 'updatePolicy', 'showInvite'])

const sortedActivities = computed(() => {
  return [...props.activities].sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp))
})

const linkShareCount = computed(() => {
  return props.activities.filter(a => a.type === 'link_shared').length
})

const joinViaLinkCount = computed(() => {
  return props.activities.filter(a => a.type === 'user_joined').length
})

function getParticipantName(participantId) {
  const participant = props.participants.find(p => p.id === participantId || p.userId === participantId)
  return participant?.userName || 'Unknown User'
}

function formatTime(timestamp) {
  if (!timestamp) return ''
  const date = timestamp.toDate ? timestamp.toDate() : new Date(timestamp)
  return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })
}
</script>
