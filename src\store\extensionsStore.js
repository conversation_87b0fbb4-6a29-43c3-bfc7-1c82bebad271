import { defineStore } from 'pinia';
import { useAuthStore } from './authStore'; // To get current developer's ID

// Firestore imports
import { db } from '../firebase/config';
import { collection, getDocs, doc, setDoc, updateDoc, addDoc, deleteDoc } from 'firebase/firestore';

// Helper to generate unique IDs for new extensions (if needed for local logic)
const generateUniqueId = () => `ext_${Date.now().toString().slice(-6)}${Math.floor(Math.random() * 1000).toString().padStart(3, '0')}`;

export const useExtensionsStore = defineStore('extensions', {
  state: () => ({
    // Initialize with a deep copy of dummy data to prevent direct mutation issues
    extensions: [], // Will be loaded from Firestore
    isLoading: false,
    error: null,
  }),
  getters: {
    getMarketplaceExtensions: (state) => {
      return state.extensions.filter(ext => ext.is_public);
    },
    getExtensionById: (state) => (id) => {
      return state.extensions.find(ext => ext.id === id);
    },
    getUserExtensions: (state) => (userId) => {
      if (!userId) return [];
      return state.extensions.filter(ext => ext.dev_metadata?.author_id === userId);
    }
  },
  actions: {
    async fetchExtensions() {
      this.isLoading = true;
      this.error = null;
      try {
        // Fetch all extensions from Firestore 'extensions' collection
        const extensionsSnapshot = await getDocs(collection(db, 'extensions'));
        this.extensions = extensionsSnapshot.docs.map(doc => ({ id: doc.id, ...doc.data() }));
        console.log('Loaded extensions from Firestore:', this.extensions.length);
      } catch (e) {
        this.error = e.message;
        this.extensions = []; // Fallback to empty on error
      } finally {
        this.isLoading = false;
      }
    },

    async incrementDownloadCount(extensionId) {
      const extension = this.extensions.find(ext => ext.id === extensionId);
      if (extension) {
        await new Promise(resolve => setTimeout(resolve, 50)); // Simulate API delay
        extension.downloads = (extension.downloads || 0) + 1;
        console.log(`Download count for ${extensionId} incremented to ${extension.downloads}`);
        return true;
      }
      console.error(`Extension ID ${extensionId} not found for download count increment.`);
      return false;
    },

    async deleteExtension(extensionId) {
      this.isLoading = true;
      this.error = null;
      try {
        await deleteDoc(doc(db, 'extensions', extensionId));
        this.extensions = this.extensions.filter(ext => ext.id !== extensionId);
        this.isLoading = false;
        return true;
      } catch (e) {
        this.error = e.message;
        this.isLoading = false;
        return false;
      }
    },

    async saveExtension(extensionDataToSave) {
      this.isLoading = true;
      const authStore = useAuthStore();
      if (!authStore.user) {
        this.error = "User not authenticated. Cannot save extension.";
        this.isLoading = false;
        return null;
      }
      try {
        const now = new Date().toISOString();
        let docRef;
        let isUpdate = false;
        let extensionId = extensionDataToSave.id;
        let devMetadata = {
          ...(extensionDataToSave.dev_metadata || {}),
          author_id: authStore.user.id,
          author_name: authStore.user.name || 'Unknown Developer',
          author_pic: authStore.user.picture || `https://i.pravatar.cc/40?u=${authStore.user.id}`,
          updated_at: now,
        };
        if (extensionId) {
          // Update existing extension
          docRef = doc(db, 'extensions', extensionId);
          await updateDoc(docRef, {
            ...extensionDataToSave,
            dev_metadata: devMetadata,
            updated_at: now
          });
          isUpdate = true;
        } else {
          // Create new extension
          devMetadata.created_at = now;
          const newDoc = await addDoc(collection(db, 'extensions'), {
            ...extensionDataToSave,
            dev_metadata: devMetadata,
            downloads: 0,
            is_public: extensionDataToSave.is_public === undefined ? false : extensionDataToSave.is_public,
            created_at: now,
            updated_at: now
          });
          extensionId = newDoc.id;
          docRef = newDoc;
        }
        // Fetch the fresh extension from Firestore
        const freshSnap = await getDocs(collection(db, 'extensions'));
        this.extensions = freshSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
        const savedExtension = this.extensions.find(e => e.id === extensionId) || null;
        this.isLoading = false;
        return savedExtension;
      } catch (e) {
        this.error = e.message;
        this.isLoading = false;
        return null;
      }
    }
  },
});
