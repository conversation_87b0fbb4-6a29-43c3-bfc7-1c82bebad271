{"version": 3, "file": "index.esm.js", "sources": ["../../app/index.ts", "../../index.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport firebase from '@firebase/app-compat';\nimport { name, version } from '../../package.json';\n\nfirebase.registerVersion(name, version, 'app-compat');\n\nexport default firebase;\n", "/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nconsole.warn(`\nIt looks like you're using the development build of the Firebase JS SDK.\nWhen deploying Firebase apps to production, it is advisable to only import\nthe individual SDK components you intend to use.\n\nFor the module builds, these are available in the following manner\n(replace <PACKAGE> with the name of a component - i.e. auth, database, etc):\n\nCommonJS Modules:\nconst firebase = require('firebase/app');\nrequire('firebase/<PACKAGE>');\n\nES Modules:\nimport firebase from 'firebase/app';\nimport 'firebase/<PACKAGE>';\n\nTypeScript:\nimport firebase from 'firebase/app';\nimport 'firebase/<PACKAGE>';\n`);\n\nimport firebase from './app';\nimport { name, version } from '../package.json';\n\nimport './analytics';\nimport './app-check';\nimport './auth';\nimport './database';\nimport './firestore';\nimport './functions';\nimport './messaging';\nimport './storage';\nimport './performance';\nimport './remote-config';\n\nfirebase.registerVersion(name, version, 'compat');\n\nexport default firebase;\n"], "names": ["name", "version"], "mappings": ";;;;;;;;;;;;;;;;AAAA;;;;;;;;;;;;;;;AAeG;AAKH,QAAQ,CAAC,eAAe,CAACA,MAAI,EAAEC,SAAO,EAAE,YAAY,CAAC;;;;;ACpBrD;;;;;;;;;;;;;;;AAeG;AAEH,OAAO,CAAC,IAAI,CAAC,smBAmBZ,CAAC,CAAC;AAgBH,QAAQ,CAAC,eAAe,CAAC,IAAI,EAAE,OAAO,EAAE,QAAQ,CAAC"}