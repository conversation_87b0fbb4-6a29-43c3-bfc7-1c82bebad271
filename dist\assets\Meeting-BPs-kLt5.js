import{g as i,q as M,s as n,k as b,h as o,t as a,x as y,p as S,w as P,y as V,z as U,F as f,j as C,A as g,B as J,v as B,m as s,C as W,n as q}from"./index-DX9maXWO.js";const D={key:2,class:"h-screen flex items-center justify-center bg-gray-100 dark:bg-gray-900"},O={class:"text-center"},H={class:"mt-4 text-lg font-medium text-gray-700 dark:text-gray-300"},z={key:3,class:"h-screen flex items-center justify-center bg-gray-100 dark:bg-gray-900"},Y={class:"text-center p-6 bg-white dark:bg-gray-800 rounded-lg shadow-xl"},G={class:"text-gray-600 dark:text-gray-300 mb-6"},K={key:4,class:"h-screen flex flex-col bg-gray-100 dark:bg-gray-900"},Q={class:"sticky top-0 z-30 bg-white dark:bg-gray-800 shadow-md px-2 sm:px-4 py-2 flex flex-col sm:flex-row justify-between items-center gap-2 border-b border-gray-200 dark:border-gray-700"},X={class:"flex items-center"},Z={class:"text-xl font-semibold text-gray-900 dark:text-white"},_={class:"ml-4 px-2 py-1 bg-gray-100 dark:bg-gray-700 rounded text-sm text-gray-600 dark:text-gray-300"},ee={key:0,class:"ml-4 flex items-center gap-2"},te={key:0,class:"w-full mt-2"},oe={class:"text-xs max-h-24 overflow-y-auto"},re={class:"font-bold"},se={class:"font-bold"},ie={class:"flex items-center"},ne={class:"text-sm text-gray-600 dark:text-gray-300 mr-2"},ae={class:"flex-1 flex overflow-hidden"},le={class:"flex-1 p-2 sm:p-4 overflow-auto"},de={class:"grid grid-cols-1 sm:grid-cols-2 xl:grid-cols-3 gap-2 sm:gap-4"},ge={class:"relative bg-black rounded-lg overflow-hidden shadow-lg aspect-video"},ue={ref:"localVideo",muted:!0,autoplay:"",playsinline:"",class:"w-full h-full object-cover"},me={class:"absolute bottom-2 left-2 bg-black bg-opacity-50 text-white px-2 py-1 rounded text-sm"},be={key:1,class:"w-full h-full flex items-center justify-center bg-gray-800"},ve={class:"absolute bottom-2 left-2 bg-black bg-opacity-50 text-white px-2 py-1 rounded text-sm flex items-center"},ye={key:0,class:"ml-2 text-red-500"},fe={key:2,class:"absolute top-2 right-2"},pe=["onClick"],ce={key:0,class:"mt-4"},ke={key:0},he={class:"font-semibold"},Se={class:"text-sm text-red-500"},Ce={key:0,class:"w-80 bg-white dark:bg-gray-800 shadow-lg flex flex-col border-l border-gray-200 dark:border-gray-700"},we={class:"p-3 border-b border-gray-200 dark:border-gray-700 flex justify-between items-center"},Ie={class:"flex-1 p-3 overflow-y-auto"},Re={class:"font-semibold mb-1 text-gray-900 dark:text-white"},xe={class:"text-sm text-gray-600 dark:text-gray-300 mb-2"},Me={class:"flex space-x-2"},Pe=["onClick"],je=["onClick"],Fe={key:1,class:"w-80 bg-white dark:bg-gray-800 shadow-lg flex flex-col border-l border-gray-200 dark:border-gray-700"},Ae={class:"p-3 border-b border-gray-200 dark:border-gray-700 flex justify-between items-center"},Te={class:"flex-1"},Be={key:2,class:"w-80 bg-white dark:bg-gray-800 shadow-lg flex flex-col border-l border-gray-200 dark:border-gray-700"},Ee={class:"p-3 border-b border-gray-200 dark:border-gray-700 flex justify-between items-center"},Le={class:"flex-1"},qe={key:3,class:"w-80 bg-white dark:bg-gray-800 shadow-lg flex flex-col border-l border-gray-200 dark:border-gray-700"},Ne={class:"p-3 border-b border-gray-200 dark:border-gray-700 flex justify-between items-center"},$e={class:"flex-1"},Ve={key:4,class:"w-80 bg-white dark:bg-gray-800 shadow-lg flex flex-col border-l border-gray-200 dark:border-gray-700"},Ue={class:"p-3 border-b border-gray-200 dark:border-gray-700 flex justify-between items-center"},Je={class:"flex-1 p-3 overflow-y-auto",ref:"chatMessagesContainer"},We={class:"flex items-start"},De={class:"font-semibold mb-1"},Oe={class:"p-3 border-t border-gray-200 dark:border-gray-700"},He=["disabled"],ze={key:1,class:"border-t border-gray-200 dark:border-gray-700"},Ye={key:2,class:"border-t border-gray-200 dark:border-gray-700 p-2"},Ge={key:3,class:"fixed bottom-24 left-1/2 transform -translate-x-1/2 z-10"},Ke={key:5,class:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50"},Qe={class:"bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6 max-w-md w-full"},Xe={class:"flex justify-end space-x-2"},Ze={key:6,class:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50"},_e={class:"bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6 max-w-md w-full"},et={class:"mb-4 text-gray-700 dark:text-gray-300"},tt={class:"font-semibold"},ot={class:"flex justify-end space-x-2"},it={__name:"Meeting",setup(rt){const j=computed(()=>meetingStore.participants.length===1&&(meetingConfigStore.isHost(userId.value)||meetingConfigStore.getLinkSharingPolicy==="all")),E=computed(()=>!authStore.user&&!localStorage.getItem("userName")&&route.query.joinViaLink),N=computed(()=>`${window.location.origin}/meeting/${meetingId.value}`),F=computed(()=>meetingConfigStore.getLinkSharingPolicy),L=computed(()=>meetingConfigStore.getJoinTracking);function $(e){meetingConfigStore.updateLinkSharingPolicy(e);const t=computed(()=>meetingConfigStore.isHost(userId.value));computed(()=>meetingConfigStore.activeFeatures),computed(()=>meetingConfigStore.getAllActiveExtensions);const w=ref(null);ref({});const u=ref(null);ref(!1);const A=ref(!1);ref("");const p=ref(null),I=ref([]);ref(!1),ref(null),ref(""),ref(!1),ref("");let c=null,k=null,h=null;onMounted(async()=>{if(!userId.value){alert("User ID not found. Please ensure you are logged in or have provided a name."),router.push("/");return}localStorage.getItem("userId")||localStorage.setItem("userId",userId.value),userName.value&&!localStorage.getItem("userName")&&localStorage.setItem("userName",userName.value);try{if(meetingStore.setMeetingId(meetingId.value),await meetingConfigStore.loadMeetingConfiguration(meetingId.value,userId.value),meetingConfigStore.error){alert(`Error loading meeting: ${meetingConfigStore.error}`),router.push("/");return}u.value=new WebRTCHandler(meetingId.value,userId.value,(m,x)=>meetingStore.addRemoteStream(m,x),m=>meetingStore.removeRemoteStream(m),T,r);const d=await u.value.initLocalStream();meetingStore.setLocalStream(d),w.value&&meetingStore.localStream&&(w.value.srcObject=meetingStore.localStream),await u.value.joinMeeting(),meetingStore.setConnectionStatus("connected"),k=listenForMessages(meetingId.value,m=>{meetingStore.messages=m,l()}),h=listenForParticipants(meetingId.value,m=>{meetingStore.participants=m}),t.value&&v(),window.addEventListener("beforeunload",R)}catch(d){console.error("Error initializing meeting:",d),meetingStore.setConnectionStatus("disconnected"),alert(`Failed to join the meeting: ${d.message}. Please try again.`),router.push("/")}});const R=()=>{u.value&&u.value.leaveMeeting()};onBeforeUnmount(()=>{window.removeEventListener("beforeunload",R),u.value&&u.value.leaveMeeting(),meetingStore.localStream&&meetingStore.localStream.getTracks().forEach(d=>d.stop()),meetingStore.reset(),meetingConfigStore.resetMeetingConfig(),k&&k(),h&&h(),c&&c()});const T=d=>{console.log("Participant joined (WebRTC):",d)},r=d=>{console.log("Participant left (WebRTC):",d)},l=()=>{nextTick(()=>{p.value&&(p.value.scrollTop=p.value.scrollHeight)})};watch(()=>meetingStore.messages,l,{deep:!0,immediate:!0});const v=()=>{const d=collection(db,"meetings",meetingId.value,"joinRequests");c=onSnapshot(d,m=>{I.value=m.docs.map(x=>({id:x.id,...x.data()})),I.value.length>0&&A.value})};computed(()=>meetingConfigStore.isFeatureActive("recording")),computed(()=>meetingConfigStore.isFeatureActive("whiteboard"))}return(e,t)=>{const w=g("MeetingInvitePopup"),u=g("UsernamePromptPopup"),A=g("BreakoutRooms"),p=g("VirtualBackgroundSelector"),I=g("Whiteboard"),c=g("ReactionDisplay"),k=g("AudibleImpairedSystem"),h=g("RecordingControls"),R=g("ReactionSelector"),T=g("MeetingControls");return s(),i(f,null,[b(j)?(s(),M(w,{key:0,visible:b(j),inviteLink:b(N),onClose:t[0]||(t[0]=r=>j.value=!1)},null,8,["visible","inviteLink"])):n("",!0),b(E)?(s(),M(u,{key:1,visible:b(E),onSubmit:e.onUsernameSubmit,onCancel:e.onUsernameCancel},null,8,["visible","onSubmit","onCancel"])):n("",!0),e.meetingConfigStore.isLoading||e.meetingStore.connectionStatus==="connecting"?(s(),i("div",D,[o("div",O,[t[27]||(t[27]=o("svg",{class:"mx-auto h-12 w-12 text-primary animate-spin",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24"},[o("circle",{class:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor","stroke-width":"4"}),o("path",{class:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})],-1)),o("p",H,a(e.meetingStore.connectionStatus==="connecting"?"Connecting to meeting...":"Loading meeting details..."),1)])])):e.meetingConfigStore.error?(s(),i("div",z,[o("div",Y,[t[28]||(t[28]=o("i",{class:"fas fa-exclamation-triangle text-4xl text-red-500 mb-4"},null,-1)),t[29]||(t[29]=o("h2",{class:"text-2xl font-semibold text-gray-900 dark:text-white mb-2"},"Error Loading Meeting",-1)),o("p",G,a(e.meetingConfigStore.error),1),o("button",{onClick:t[1]||(t[1]=r=>e.router.push("/")),class:"btn btn-primary"},"Go to Homepage")])])):(s(),i("div",K,[o("div",Q,[o("div",X,[o("h1",Z,a(e.meetingName),1),o("div",_,[S(a(e.meetingId)+" ",1),o("button",{onClick:t[2]||(t[2]=(...r)=>e.copyMeetingId&&e.copyMeetingId(...r)),class:"ml-1 text-primary hover:text-primary-dark",title:"Copy meeting ID"},t[30]||(t[30]=[o("i",{class:"fas fa-copy"},null,-1)]))]),e.isAdmin?(s(),i("div",ee,[t[32]||(t[32]=o("label",{class:"text-xs text-gray-600 dark:text-gray-300"},"Who can share invite?",-1)),P(o("select",{"onUpdate:modelValue":t[3]||(t[3]=r=>U(F)?F.value=r:null),onChange:$,class:"text-xs p-1 rounded border bg-gray-50 dark:bg-gray-700"},t[31]||(t[31]=[o("option",{value:"host"},"Host Only",-1),o("option",{value:"all"},"All Members",-1)]),544),[[V,b(F)]])])):n("",!0)]),e.isAdmin&&b(L).length?(s(),i("div",te,[t[34]||(t[34]=o("div",{class:"text-xs text-gray-600 dark:text-gray-300 font-semibold mb-1"},"Join Link Activity",-1)),o("ul",oe,[(s(!0),i(f,null,C(b(L),(r,l)=>(s(),i("li",{key:l,class:"mb-1"},[o("span",re,a(e.getParticipantName(r.sharedBy)),1),t[33]||(t[33]=S(" shared link → ")),o("span",se,a(r.joinedName),1),S(" joined ("+a(e.formatTime(r.joinedAt))+") ",1)]))),128))])])):n("",!0),o("div",ie,[o("span",ne,a(e.meetingStore.participants.length)+" participant"+a(e.meetingStore.participants.length!==1?"s":""),1),o("button",{onClick:t[4]||(t[4]=(...r)=>e.showLeaveMeetingDialog&&e.showLeaveMeetingDialog(...r)),class:"btn btn-outline text-red-600 hover:bg-red-50 dark:hover:bg-red-900 dark:text-red-400"}," Leave ")])]),o("div",ae,[o("div",le,[o("div",de,[o("div",ge,[o("video",ue,null,512),o("div",me," You"+a(e.meetingStore.isMicMuted?" (muted)":""),1)]),(s(!0),i(f,null,C(e.meetingStore.remoteStreams,(r,l)=>(s(),i("div",{key:l,class:"relative bg-black rounded-lg overflow-hidden shadow-lg aspect-video"},[e.isParticipantVideoOff(l)?(s(),i("div",be,t[35]||(t[35]=[o("div",{class:"w-24 h-24 bg-white rounded-full flex items-center justify-center"},[o("i",{class:"fas fa-user text-4xl text-black"})],-1)]))):(s(),i("video",{key:0,ref_for:!0,ref:v=>{v&&(e.remoteVideoRefs[l]=v),v&&r&&(v.srcObject=r)},autoplay:"",playsinline:"",class:"w-full h-full object-cover"},null,512)),o("div",ve,[o("span",null,a(e.getParticipantName(l)),1),e.isParticipantMuted(l)?(s(),i("span",ye,t[36]||(t[36]=[o("i",{class:"fas fa-microphone-slash"},null,-1)]))):n("",!0)]),e.isAdmin?(s(),i("div",fe,[o("button",{onClick:v=>e.showRemoveParticipantDialog(l),class:"bg-red-600 text-white p-1 rounded-full hover:bg-red-700",title:"Remove participant"},t[37]||(t[37]=[o("i",{class:"fas fa-user-times"},null,-1)]),8,pe)])):n("",!0)]))),128))]),e.activeMeetingExtensions.length>0?(s(),i("div",ce,[t[38]||(t[38]=o("h3",{class:"text-lg font-semibold text-gray-800 dark:text-white mb-2"},"Active Extensions",-1)),(s(!0),i(f,null,C(e.activeMeetingExtensions,r=>(s(),i("div",{key:r.id,class:"p-2 border rounded mb-2 bg-gray-50 dark:bg-gray-800"},[(s(),M(W(e.resolveExtensionComponent(r.entryPoint||r.id)),{config:r.config,meetingId:e.meetingId,userId:e.userId,isAdmin:e.isAdmin,meetingContext:{meetingId:e.meetingId,userId:e.userId,userName:e.userName.value,meetingConfigStore:e.meetingConfigStore,meetingStore:e.meetingStore}},null,8,["config","meetingId","userId","isAdmin","meetingContext"])),e.resolveExtensionComponent(r.entryPoint||r.id)?n("",!0):(s(),i("div",ke,[o("p",he,a(r.name),1),o("p",Se,"Cannot load UI for this extension (entry point: "+a(r.entryPoint||"not defined")+").",1)]))]))),128))])):n("",!0)]),e.isAdmin&&e.showJoinRequestsPanel&&e.joinRequests.length>0?(s(),i("div",Ce,[o("div",we,[t[40]||(t[40]=o("h2",{class:"font-semibold text-gray-900 dark:text-white"},"Join Requests",-1)),o("button",{onClick:t[5]||(t[5]=(...r)=>e.toggleJoinRequestsPanel&&e.toggleJoinRequestsPanel(...r)),class:"text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"},t[39]||(t[39]=[o("i",{class:"fas fa-times"},null,-1)]))]),o("div",Ie,[(s(!0),i(f,null,C(e.joinRequests,r=>(s(),i("div",{key:r.id,class:"mb-4 p-3 bg-gray-100 dark:bg-gray-700 rounded-lg"},[o("div",Re,a(r.userName),1),o("div",xe,"Requested at "+a(e.formatTime(r.timestamp)),1),o("div",Me,[o("button",{onClick:l=>e.approveJoinRequest(r.id),class:"btn btn-primary text-sm py-1 px-3"},"Approve",8,Pe),o("button",{onClick:l=>e.denyJoinRequest(r.id),class:"btn btn-outline text-sm py-1 px-3"},"Deny",8,je)])]))),128))])])):n("",!0),e.activeCoreFeatures.breakoutRooms?(s(),i("div",Fe,[o("div",Ae,[t[42]||(t[42]=o("h2",{class:"font-semibold text-gray-900 dark:text-white"},"Breakout Rooms",-1)),o("button",{onClick:t[6]||(t[6]=r=>e.meetingConfigStore.toggleFeature("breakoutRooms")),class:"text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"},t[41]||(t[41]=[o("i",{class:"fas fa-times"},null,-1)]))]),o("div",Te,[y(A,{meetingId:e.meetingId,userId:e.userId,isHost:e.isAdmin,participants:e.meetingStore.participants},null,8,["meetingId","userId","isHost","participants"])])])):n("",!0),e.activeCoreFeatures.virtualBackground?(s(),i("div",Be,[o("div",Ee,[t[44]||(t[44]=o("h2",{class:"font-semibold text-gray-900 dark:text-white"},"Virtual Background",-1)),o("button",{onClick:t[7]||(t[7]=r=>e.meetingConfigStore.toggleFeature("virtualBackground")),class:"text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"},t[43]||(t[43]=[o("i",{class:"fas fa-times"},null,-1)]))]),o("div",Le,[y(p,{stream:e.meetingStore.localStream,currentBackground:"none",onApply:e.applyVirtualBackground,onCancel:t[8]||(t[8]=r=>e.meetingConfigStore.toggleFeature("virtualBackground"))},null,8,["stream","onApply"])])])):n("",!0),e.activeCoreFeatures.whiteboard?(s(),i("div",qe,[o("div",Ne,[t[46]||(t[46]=o("h2",{class:"font-semibold text-gray-900 dark:text-white"},"Whiteboard",-1)),o("button",{onClick:t[9]||(t[9]=(...r)=>e.toggleWhiteboardFeature&&e.toggleWhiteboardFeature(...r)),class:"text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"},t[45]||(t[45]=[o("i",{class:"fas fa-times"},null,-1)]))]),o("div",$e,[y(I,{meetingId:e.meetingId,userId:e.userId,isHost:e.isAdmin},null,8,["meetingId","userId","isHost"])])])):n("",!0),e.isChatOpen&&e.activeCoreFeatures.chat?(s(),i("div",Ve,[o("div",Ue,[t[48]||(t[48]=o("h2",{class:"font-semibold text-gray-900 dark:text-white"},"Chat",-1)),o("button",{onClick:t[10]||(t[10]=r=>e.isChatOpen=!1),class:"text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"},t[47]||(t[47]=[o("i",{class:"fas fa-times"},null,-1)]))]),o("div",Je,[(s(!0),i(f,null,C(e.meetingStore.messages,r=>(s(),i("div",{key:r.id,class:"mb-3"},[o("div",We,[o("div",{class:q(["flex-1 bg-gray-100 dark:bg-gray-700 rounded-lg px-3 py-2 text-sm",{"bg-primary-light text-white":r.userId===e.userId,"ml-auto":r.userId===e.userId}])},[o("div",De,a(r.userId===e.userId?"You":r.userName),1),o("div",null,a(r.message),1)],2)]),o("div",{class:q(["text-xs text-gray-500 dark:text-gray-400 mt-1",{"text-right":r.userId===e.userId}])},a(e.formatTime(r.timestamp)),3)]))),128))],512),o("div",Oe,[o("form",{onSubmit:t[12]||(t[12]=J((...r)=>e.sendChatMessage&&e.sendChatMessage(...r),["prevent"])),class:"flex"},[P(o("input",{"onUpdate:modelValue":t[11]||(t[11]=r=>e.chatMessage=r),type:"text",placeholder:"Type a message...",class:"input flex-1 mr-2"},null,512),[[B,e.chatMessage]]),o("button",{type:"submit",class:"btn btn-primary",disabled:!e.chatMessage.trim()},"Send",8,He)],32)])])):n("",!0)]),e.activeCoreFeatures.reactions?(s(),M(c,{key:0,meetingId:e.meetingId},null,8,["meetingId"])):n("",!0),e.activeCoreFeatures.audibleImpairedSystem?(s(),i("div",ze,[y(k,{meetingId:e.meetingId,currentUserId:e.userId,currentUserName:e.userName.value,localStream:e.meetingStore.localStream},null,8,["meetingId","currentUserId","currentUserName","localStream"])])):n("",!0),e.activeCoreFeatures.recording?(s(),i("div",Ye,[y(h,{meetingId:e.meetingId,userId:e.userId,stream:e.meetingStore.localStream,onRecordingStarted:t[13]||(t[13]=r=>e.meetingStore.isRecording=!0),onRecordingStopped:t[14]||(t[14]=r=>e.meetingStore.isRecording=!1)},null,8,["meetingId","userId","stream"])])):n("",!0),e.activeCoreFeatures.reactions?(s(),i("div",Ge,[y(R,{meetingId:e.meetingId,userId:e.userId,userName:e.userName.value},null,8,["meetingId","userId","userName"])])):n("",!0),y(T,{isMicMuted:e.meetingStore.isMicMuted,isCameraOff:e.meetingStore.isCameraOff,isScreenSharing:e.meetingStore.isScreenSharing,isChatOpen:e.isChatOpen,isChatEnabled:e.activeCoreFeatures.chat,isAISEnabled:e.activeCoreFeatures.audibleImpairedSystem,isReactionsEnabled:e.activeCoreFeatures.reactions,isBreakoutRoomsEnabled:e.activeCoreFeatures.breakoutRooms,isVirtualBackgroundEnabled:e.activeCoreFeatures.virtualBackground,isWhiteboardEnabled:e.isWhiteboardFeatureEnabled,isRecordingEnabled:e.isRecordingFeatureEnabled,isRecordingActive:e.meetingStore.isRecording,hasJoinRequests:e.isAdmin&&e.joinRequests.length>0,showJoinRequestsPanel:e.showJoinRequestsPanel,joinRequestsCount:e.joinRequests.length,isAdmin:e.isAdmin,onToggleMic:e.toggleMic,onToggleCamera:e.toggleCamera,onToggleScreen:e.toggleScreenSharing,onToggleChat:t[15]||(t[15]=r=>e.isChatOpen=!e.isChatOpen),onToggleAis:t[16]||(t[16]=r=>e.meetingConfigStore.toggleFeature("audibleImpairedSystem")),onToggleReactions:t[17]||(t[17]=r=>e.meetingConfigStore.toggleFeature("reactions")),onToggleBreakoutRooms:t[18]||(t[18]=r=>e.meetingConfigStore.toggleFeature("breakoutRooms")),onToggleVirtualBackground:t[19]||(t[19]=r=>e.meetingConfigStore.toggleFeature("virtualBackground")),onToggleWhiteboard:e.toggleWhiteboardFeature,onToggleRecording:t[20]||(t[20]=r=>e.meetingConfigStore.toggleFeature("recording")),onToggleJoinRequests:e.toggleJoinRequestsPanel,onLeaveMeeting:e.showLeaveMeetingDialog},null,8,["isMicMuted","isCameraOff","isScreenSharing","isChatOpen","isChatEnabled","isAISEnabled","isReactionsEnabled","isBreakoutRoomsEnabled","isVirtualBackgroundEnabled","isWhiteboardEnabled","isRecordingEnabled","isRecordingActive","hasJoinRequests","showJoinRequestsPanel","joinRequestsCount","isAdmin","onToggleMic","onToggleCamera","onToggleScreen","onToggleWhiteboard","onToggleJoinRequests","onLeaveMeeting"])])),e.showLeaveDialog?(s(),i("div",Ke,[o("div",Qe,[t[49]||(t[49]=o("h2",{class:"text-xl font-semibold mb-4 text-gray-900 dark:text-white"},"Leave Meeting",-1)),t[50]||(t[50]=o("p",{class:"mb-4 text-gray-700 dark:text-gray-300"},"Please provide a reason for leaving the meeting:",-1)),P(o("textarea",{"onUpdate:modelValue":t[21]||(t[21]=r=>e.leaveReason=r),class:"w-full p-2 border border-gray-300 dark:border-gray-600 rounded mb-4 bg-white dark:bg-gray-700 text-gray-900 dark:text-white",placeholder:"Reason for leaving (optional)",rows:"3"},null,512),[[B,e.leaveReason]]),o("div",Xe,[o("button",{onClick:t[22]||(t[22]=r=>e.showLeaveDialog=!1),class:"px-4 py-2 bg-gray-200 dark:bg-gray-700 text-gray-800 dark:text-gray-200 rounded hover:bg-gray-300 dark:hover:bg-gray-600"},"Cancel"),o("button",{onClick:t[23]||(t[23]=(...r)=>e.leaveMeetingWithReason&&e.leaveMeetingWithReason(...r)),class:"px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700"},"Leave Meeting")])])])):n("",!0),e.showRemoveDialog?(s(),i("div",Ze,[o("div",_e,[t[53]||(t[53]=o("h2",{class:"text-xl font-semibold mb-4 text-gray-900 dark:text-white"},"Remove Participant",-1)),o("p",et,[t[51]||(t[51]=S(" You are about to remove ")),o("span",tt,a(e.participantToRemove?e.getParticipantName(e.participantToRemove):""),1),t[52]||(t[52]=S(" from the meeting. "))]),t[54]||(t[54]=o("p",{class:"mb-4 text-gray-700 dark:text-gray-300"},"Please provide a reason (visible only to the host):",-1)),P(o("textarea",{"onUpdate:modelValue":t[24]||(t[24]=r=>e.removeReason=r),class:"w-full p-2 border border-gray-300 dark:border-gray-600 rounded mb-4 bg-white dark:bg-gray-700 text-gray-900 dark:text-white",placeholder:"Reason for removal (optional)",rows:"3"},null,512),[[B,e.removeReason]]),o("div",ot,[o("button",{onClick:t[25]||(t[25]=r=>e.showRemoveDialog=!1),class:"px-4 py-2 bg-gray-200 dark:bg-gray-700 text-gray-800 dark:text-gray-200 rounded hover:bg-gray-300 dark:hover:bg-gray-600"},"Cancel"),o("button",{onClick:t[26]||(t[26]=(...r)=>e.removeParticipantWithReason&&e.removeParticipantWithReason(...r)),class:"px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700"},"Remove")])])])):n("",!0)],64)}}};export{it as default};
