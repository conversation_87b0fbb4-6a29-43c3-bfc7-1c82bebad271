import{_isFirebaseServerApp as e,SDK_VERSION as t,_getProvider,_registerComponent as r,registerVersion as n,getApp as i}from"https://www.gstatic.com/firebasejs/10.14.1/firebase-app.js";var extendStatics=function(e,t){return extendStatics=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var r in t)Object.prototype.hasOwnProperty.call(t,r)&&(e[r]=t[r])},extendStatics(e,t)};function __extends(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Class extends value "+String(t)+" is not a constructor or null");function __(){this.constructor=e}extendStatics(e,t),e.prototype=null===t?Object.create(t):(__.prototype=t.prototype,new __)}var __assign=function(){return __assign=Object.assign||function __assign(e){for(var t,r=1,n=arguments.length;r<n;r++)for(var i in t=arguments[r])Object.prototype.hasOwnProperty.call(t,i)&&(e[i]=t[i]);return e},__assign.apply(this,arguments)};function __rest(e,t){var r={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&t.indexOf(n)<0&&(r[n]=e[n]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var i=0;for(n=Object.getOwnPropertySymbols(e);i<n.length;i++)t.indexOf(n[i])<0&&Object.prototype.propertyIsEnumerable.call(e,n[i])&&(r[n[i]]=e[n[i]])}return r}function __awaiter(e,t,r,n){return new(r||(r=Promise))((function(i,o){function fulfilled(e){try{step(n.next(e))}catch(e){o(e)}}function rejected(e){try{step(n.throw(e))}catch(e){o(e)}}function step(e){e.done?i(e.value):function adopt(e){return e instanceof r?e:new r((function(t){t(e)}))}(e.value).then(fulfilled,rejected)}step((n=n.apply(e,t||[])).next())}))}function __generator(e,t){var r,n,i,o,s={label:0,sent:function(){if(1&i[0])throw i[1];return i[1]},trys:[],ops:[]};return o={next:verb(0),throw:verb(1),return:verb(2)},"function"==typeof Symbol&&(o[Symbol.iterator]=function(){return this}),o;function verb(o){return function(a){return function step(o){if(r)throw new TypeError("Generator is already executing.");for(;s;)try{if(r=1,n&&(i=2&o[0]?n.return:o[0]?n.throw||((i=n.return)&&i.call(n),0):n.next)&&!(i=i.call(n,o[1])).done)return i;switch(n=0,i&&(o=[2&o[0],i.value]),o[0]){case 0:case 1:i=o;break;case 4:return s.label++,{value:o[1],done:!1};case 5:s.label++,n=o[1],o=[0];continue;case 7:o=s.ops.pop(),s.trys.pop();continue;default:if(!(i=s.trys,(i=i.length>0&&i[i.length-1])||6!==o[0]&&2!==o[0])){s=0;continue}if(3===o[0]&&(!i||o[1]>i[0]&&o[1]<i[3])){s.label=o[1];break}if(6===o[0]&&s.label<i[1]){s.label=i[1],i=o;break}if(i&&s.label<i[2]){s.label=i[2],s.ops.push(o);break}i[2]&&s.ops.pop(),s.trys.pop();continue}o=t.call(e,s)}catch(e){o=[6,e],n=0}finally{r=i=0}if(5&o[0])throw o[1];return{value:o[0]?o[1]:void 0,done:!0}}([o,a])}}}function __spreadArray(e,t,r){if(r||2===arguments.length)for(var n,i=0,o=t.length;i<o;i++)!n&&i in t||(n||(n=Array.prototype.slice.call(t,0,i)),n[i]=t[i]);return e.concat(n||Array.prototype.slice.call(t))}const o={byteToCharMap_:null,charToByteMap_:null,byteToCharMapWebSafe_:null,charToByteMapWebSafe_:null,ENCODED_VALS_BASE:"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789",get ENCODED_VALS(){return this.ENCODED_VALS_BASE+"+/="},get ENCODED_VALS_WEBSAFE(){return this.ENCODED_VALS_BASE+"-_."},HAS_NATIVE_SUPPORT:"function"==typeof atob,encodeByteArray(e,t){if(!Array.isArray(e))throw Error("encodeByteArray takes an array as a parameter");this.init_();const r=t?this.byteToCharMapWebSafe_:this.byteToCharMap_,n=[];for(let t=0;t<e.length;t+=3){const i=e[t],o=t+1<e.length,s=o?e[t+1]:0,a=t+2<e.length,u=a?e[t+2]:0,c=i>>2,d=(3&i)<<4|s>>4;let l=(15&s)<<2|u>>6,h=63&u;a||(h=64,o||(l=64)),n.push(r[c],r[d],r[l],r[h])}return n.join("")},encodeString(e,t){return this.HAS_NATIVE_SUPPORT&&!t?btoa(e):this.encodeByteArray(function(e){const t=[];let r=0;for(let n=0;n<e.length;n++){let i=e.charCodeAt(n);i<128?t[r++]=i:i<2048?(t[r++]=i>>6|192,t[r++]=63&i|128):55296==(64512&i)&&n+1<e.length&&56320==(64512&e.charCodeAt(n+1))?(i=65536+((1023&i)<<10)+(1023&e.charCodeAt(++n)),t[r++]=i>>18|240,t[r++]=i>>12&63|128,t[r++]=i>>6&63|128,t[r++]=63&i|128):(t[r++]=i>>12|224,t[r++]=i>>6&63|128,t[r++]=63&i|128)}return t}(e),t)},decodeString(e,t){return this.HAS_NATIVE_SUPPORT&&!t?atob(e):function(e){const t=[];let r=0,n=0;for(;r<e.length;){const i=e[r++];if(i<128)t[n++]=String.fromCharCode(i);else if(i>191&&i<224){const o=e[r++];t[n++]=String.fromCharCode((31&i)<<6|63&o)}else if(i>239&&i<365){const o=((7&i)<<18|(63&e[r++])<<12|(63&e[r++])<<6|63&e[r++])-65536;t[n++]=String.fromCharCode(55296+(o>>10)),t[n++]=String.fromCharCode(56320+(1023&o))}else{const o=e[r++],s=e[r++];t[n++]=String.fromCharCode((15&i)<<12|(63&o)<<6|63&s)}}return t.join("")}(this.decodeStringToByteArray(e,t))},decodeStringToByteArray(e,t){this.init_();const r=t?this.charToByteMapWebSafe_:this.charToByteMap_,n=[];for(let t=0;t<e.length;){const i=r[e.charAt(t++)],o=t<e.length?r[e.charAt(t)]:0;++t;const s=t<e.length?r[e.charAt(t)]:64;++t;const a=t<e.length?r[e.charAt(t)]:64;if(++t,null==i||null==o||null==s||null==a)throw new DecodeBase64StringError;const u=i<<2|o>>4;if(n.push(u),64!==s){const e=o<<4&240|s>>2;if(n.push(e),64!==a){const e=s<<6&192|a;n.push(e)}}}return n},init_(){if(!this.byteToCharMap_){this.byteToCharMap_={},this.charToByteMap_={},this.byteToCharMapWebSafe_={},this.charToByteMapWebSafe_={};for(let e=0;e<this.ENCODED_VALS.length;e++)this.byteToCharMap_[e]=this.ENCODED_VALS.charAt(e),this.charToByteMap_[this.byteToCharMap_[e]]=e,this.byteToCharMapWebSafe_[e]=this.ENCODED_VALS_WEBSAFE.charAt(e),this.charToByteMapWebSafe_[this.byteToCharMapWebSafe_[e]]=e,e>=this.ENCODED_VALS_BASE.length&&(this.charToByteMap_[this.ENCODED_VALS_WEBSAFE.charAt(e)]=e,this.charToByteMapWebSafe_[this.ENCODED_VALS.charAt(e)]=e)}}};class DecodeBase64StringError extends Error{constructor(){super(...arguments),this.name="DecodeBase64StringError"}}function getUA(){return"undefined"!=typeof navigator&&"string"==typeof navigator.userAgent?navigator.userAgent:""}class FirebaseError extends Error{constructor(e,t,r){super(t),this.code=e,this.customData=r,this.name="FirebaseError",Object.setPrototypeOf(this,FirebaseError.prototype),Error.captureStackTrace&&Error.captureStackTrace(this,ErrorFactory.prototype.create)}}class ErrorFactory{constructor(e,t,r){this.service=e,this.serviceName=t,this.errors=r}create(e,...t){const r=t[0]||{},n=`${this.service}/${e}`,i=this.errors[e],o=i?function replaceTemplate(e,t){return e.replace(s,((e,r)=>{const n=t[r];return null!=n?String(n):`<${r}?>`}))}(i,r):"Error",a=`${this.serviceName}: ${o} (${n}).`;return new FirebaseError(n,a,r)}}const s=/\{\$([^}]+)}/g;function deepEqual(e,t){if(e===t)return!0;const r=Object.keys(e),n=Object.keys(t);for(const i of r){if(!n.includes(i))return!1;const r=e[i],o=t[i];if(isObject(r)&&isObject(o)){if(!deepEqual(r,o))return!1}else if(r!==o)return!1}for(const e of n)if(!r.includes(e))return!1;return!0}function isObject(e){return null!==e&&"object"==typeof e}function querystring(e){const t=[];for(const[r,n]of Object.entries(e))Array.isArray(n)?n.forEach((e=>{t.push(encodeURIComponent(r)+"="+encodeURIComponent(e))})):t.push(encodeURIComponent(r)+"="+encodeURIComponent(n));return t.length?"&"+t.join("&"):""}function querystringDecode(e){const t={};return e.replace(/^\?/,"").split("&").forEach((e=>{if(e){const[r,n]=e.split("=");t[decodeURIComponent(r)]=decodeURIComponent(n)}})),t}function extractQuerystring(e){const t=e.indexOf("?");if(!t)return"";const r=e.indexOf("#",t);return e.substring(t,r>0?r:void 0)}class ObserverProxy{constructor(e,t){this.observers=[],this.unsubscribes=[],this.observerCount=0,this.task=Promise.resolve(),this.finalized=!1,this.onNoObservers=t,this.task.then((()=>{e(this)})).catch((e=>{this.error(e)}))}next(e){this.forEachObserver((t=>{t.next(e)}))}error(e){this.forEachObserver((t=>{t.error(e)})),this.close(e)}complete(){this.forEachObserver((e=>{e.complete()})),this.close()}subscribe(e,t,r){let n;if(void 0===e&&void 0===t&&void 0===r)throw new Error("Missing Observer.");n=function implementsAnyMethods(e,t){if("object"!=typeof e||null===e)return!1;for(const r of t)if(r in e&&"function"==typeof e[r])return!0;return!1}(e,["next","error","complete"])?e:{next:e,error:t,complete:r},void 0===n.next&&(n.next=noop),void 0===n.error&&(n.error=noop),void 0===n.complete&&(n.complete=noop);const i=this.unsubscribeOne.bind(this,this.observers.length);return this.finalized&&this.task.then((()=>{try{this.finalError?n.error(this.finalError):n.complete()}catch(e){}})),this.observers.push(n),i}unsubscribeOne(e){void 0!==this.observers&&void 0!==this.observers[e]&&(delete this.observers[e],this.observerCount-=1,0===this.observerCount&&void 0!==this.onNoObservers&&this.onNoObservers(this))}forEachObserver(e){if(!this.finalized)for(let t=0;t<this.observers.length;t++)this.sendOne(t,e)}sendOne(e,t){this.task.then((()=>{if(void 0!==this.observers&&void 0!==this.observers[e])try{t(this.observers[e])}catch(e){"undefined"!=typeof console&&console.error&&console.error(e)}}))}close(e){this.finalized||(this.finalized=!0,void 0!==e&&(this.finalError=e),this.task.then((()=>{this.observers=void 0,this.onNoObservers=void 0})))}}function noop(){}function getModularInstance(e){return e&&e._delegate?e._delegate:e}class Component{constructor(e,t,r){this.name=e,this.instanceFactory=t,this.type=r,this.multipleInstances=!1,this.serviceProps={},this.instantiationMode="LAZY",this.onInstanceCreated=null}setInstantiationMode(e){return this.instantiationMode=e,this}setMultipleInstances(e){return this.multipleInstances=e,this}setServiceProps(e){return this.serviceProps=e,this}setInstanceCreatedCallback(e){return this.onInstanceCreated=e,this}}var a;!function(e){e[e.DEBUG=0]="DEBUG",e[e.VERBOSE=1]="VERBOSE",e[e.INFO=2]="INFO",e[e.WARN=3]="WARN",e[e.ERROR=4]="ERROR",e[e.SILENT=5]="SILENT"}(a||(a={}));const u={debug:a.DEBUG,verbose:a.VERBOSE,info:a.INFO,warn:a.WARN,error:a.ERROR,silent:a.SILENT},c=a.INFO,d={[a.DEBUG]:"log",[a.VERBOSE]:"log",[a.INFO]:"info",[a.WARN]:"warn",[a.ERROR]:"error"},defaultLogHandler=(e,t,...r)=>{if(t<e.logLevel)return;const n=(new Date).toISOString(),i=d[t];if(!i)throw new Error(`Attempted to log a message with an invalid logType (value: ${t})`);console[i](`[${n}]  ${e.name}:`,...r)};function _allSettled(e){var t=this;return Promise.all(e.map((function(e){return __awaiter(t,void 0,void 0,(function(){return __generator(this,(function(t){switch(t.label){case 0:return t.trys.push([0,2,,3]),[4,e];case 1:return[2,{fulfilled:!0,value:t.sent()}];case 2:return[2,{fulfilled:!1,reason:t.sent()}];case 3:return[2]}}))}))})))}var l=function(){function Receiver(e){this.eventTarget=e,this.handlersMap={},this.boundEventHandler=this.handleEvent.bind(this)}return Receiver._getInstance=function(e){var t=this.receivers.find((function(t){return t.isListeningto(e)}));if(t)return t;var r=new Receiver(e);return this.receivers.push(r),r},Receiver.prototype.isListeningto=function(e){return this.eventTarget===e},Receiver.prototype.handleEvent=function(e){return __awaiter(this,void 0,void 0,(function(){var t,r,n,i,o,s,a,u=this;return __generator(this,(function(c){switch(c.label){case 0:return r=(t=e).data,n=r.eventId,i=r.eventType,o=r.data,(null==(s=this.handlersMap[i])?void 0:s.size)?(t.ports[0].postMessage({status:"ack",eventId:n,eventType:i}),[4,_allSettled(Array.from(s).map((function(e){return __awaiter(u,void 0,void 0,(function(){return __generator(this,(function(r){return[2,e(t.origin,o)]}))}))})))]):[2];case 1:return a=c.sent(),t.ports[0].postMessage({status:"done",eventId:n,eventType:i,response:a}),[2]}}))}))},Receiver.prototype._subscribe=function(e,t){0===Object.keys(this.handlersMap).length&&this.eventTarget.addEventListener("message",this.boundEventHandler),this.handlersMap[e]||(this.handlersMap[e]=new Set),this.handlersMap[e].add(t)},Receiver.prototype._unsubscribe=function(e,t){this.handlersMap[e]&&t&&this.handlersMap[e].delete(t),t&&0!==this.handlersMap[e].size||delete this.handlersMap[e],0===Object.keys(this.handlersMap).length&&this.eventTarget.removeEventListener("message",this.boundEventHandler)},Receiver.receivers=[],Receiver}();function _generateEventId(e,t){void 0===e&&(e=""),void 0===t&&(t=10);for(var r="",n=0;n<t;n++)r+=Math.floor(10*Math.random());return e+r}var h=function(){function Sender(e){this.target=e,this.handlers=new Set}return Sender.prototype.removeMessageHandler=function(e){e.messageChannel&&(e.messageChannel.port1.removeEventListener("message",e.onMessage),e.messageChannel.port1.close()),this.handlers.delete(e)},Sender.prototype._send=function(e,t,r){return void 0===r&&(r=50),__awaiter(this,void 0,void 0,(function(){var n,i,o,s=this;return __generator(this,(function(a){if(!(n="undefined"!=typeof MessageChannel?new MessageChannel:null))throw new Error("connection_unavailable");return[2,new Promise((function(a,u){var c=_generateEventId("",20);n.port1.start();var d=setTimeout((function(){u(new Error("unsupported_event"))}),r);o={messageChannel:n,onMessage:function(e){var t=e;if(t.data.eventId===c)switch(t.data.status){case"ack":clearTimeout(d),i=setTimeout((function(){u(new Error("timeout"))}),3e3);break;case"done":clearTimeout(i),a(t.data.response);break;default:clearTimeout(d),clearTimeout(i),u(new Error("invalid_response"))}}},s.handlers.add(o),n.port1.addEventListener("message",o.onMessage),s.target.postMessage({eventType:e,eventId:c,data:t},[n.port2])})).finally((function(){o&&s.removeMessageHandler(o)}))]}))}))},Sender}();function _window(){return window}function _isWorker(){return void 0!==_window().WorkerGlobalScope&&"function"==typeof _window().importScripts}function _getActiveServiceWorker(){return __awaiter(this,void 0,void 0,(function(){return __generator(this,(function(e){switch(e.label){case 0:if(!(null===navigator||void 0===navigator?void 0:navigator.serviceWorker))return[2,null];e.label=1;case 1:return e.trys.push([1,3,,4]),[4,navigator.serviceWorker.ready];case 2:return[2,e.sent().active];case 3:return e.sent(),[2,null];case 4:return[2]}}))}))}var p="firebaseLocalStorageDb",f=function(){function DBPromise(e){this.request=e}return DBPromise.prototype.toPromise=function(){var e=this;return new Promise((function(t,r){e.request.addEventListener("success",(function(){t(e.request.result)})),e.request.addEventListener("error",(function(){r(e.request.error)}))}))},DBPromise}();function getObjectStore(e,t){return e.transaction(["firebaseLocalStorage"],t?"readwrite":"readonly").objectStore("firebaseLocalStorage")}function _deleteDatabase(){var e=indexedDB.deleteDatabase(p);return new f(e).toPromise()}function _openDatabase(){var e=this,t=indexedDB.open(p,1);return new Promise((function(r,n){t.addEventListener("error",(function(){n(t.error)})),t.addEventListener("upgradeneeded",(function(){var e=t.result;try{e.createObjectStore("firebaseLocalStorage",{keyPath:"fbase_key"})}catch(e){n(e)}})),t.addEventListener("success",(function(){return __awaiter(e,void 0,void 0,(function(){var e,n;return __generator(this,(function(i){switch(i.label){case 0:return(e=t.result).objectStoreNames.contains("firebaseLocalStorage")?[3,3]:(e.close(),[4,_deleteDatabase()]);case 1:return i.sent(),n=r,[4,_openDatabase()];case 2:return n.apply(void 0,[i.sent()]),[3,4];case 3:r(e),i.label=4;case 4:return[2]}}))}))}))}))}function _putObject(e,t,r){return __awaiter(this,void 0,void 0,(function(){var n,i;return __generator(this,(function(o){return n=getObjectStore(e,!0).put(((i={}).fbase_key=t,i.value=r,i)),[2,new f(n).toPromise()]}))}))}function _deleteObject(e,t){var r=getObjectStore(e,!0).delete(t);return new f(r).toPromise()}var _=function(){function IndexedDBLocalPersistence(){this.type="LOCAL",this._shouldAllowMigration=!0,this.listeners={},this.localCache={},this.pollTimer=null,this.pendingWrites=0,this.receiver=null,this.sender=null,this.serviceWorkerReceiverAvailable=!1,this.activeServiceWorker=null,this._workerInitializationPromise=this.initializeServiceWorkerMessaging().then((function(){}),(function(){}))}return IndexedDBLocalPersistence.prototype._openDb=function(){return __awaiter(this,void 0,void 0,(function(){var e;return __generator(this,(function(t){switch(t.label){case 0:return this.db?[2,this.db]:(e=this,[4,_openDatabase()]);case 1:return e.db=t.sent(),[2,this.db]}}))}))},IndexedDBLocalPersistence.prototype._withRetries=function(e){return __awaiter(this,void 0,void 0,(function(){var t,r,n;return __generator(this,(function(i){switch(i.label){case 0:t=0,i.label=1;case 1:i.label=2;case 2:return i.trys.push([2,5,,6]),[4,this._openDb()];case 3:return r=i.sent(),[4,e(r)];case 4:return[2,i.sent()];case 5:if(n=i.sent(),t++>3)throw n;return this.db&&(this.db.close(),this.db=void 0),[3,6];case 6:return[3,1];case 7:return[2]}}))}))},IndexedDBLocalPersistence.prototype.initializeServiceWorkerMessaging=function(){return __awaiter(this,void 0,void 0,(function(){return __generator(this,(function(e){return[2,_isWorker()?this.initializeReceiver():this.initializeSender()]}))}))},IndexedDBLocalPersistence.prototype.initializeReceiver=function(){return __awaiter(this,void 0,void 0,(function(){var e=this;return __generator(this,(function(t){return this.receiver=l._getInstance(function _getWorkerGlobalScope(){return _isWorker()?self:null}()),this.receiver._subscribe("keyChanged",(function(t,r){return __awaiter(e,void 0,void 0,(function(){return __generator(this,(function(e){switch(e.label){case 0:return[4,this._poll()];case 1:return[2,{keyProcessed:e.sent().includes(r.key)}]}}))}))})),this.receiver._subscribe("ping",(function(t,r){return __awaiter(e,void 0,void 0,(function(){return __generator(this,(function(e){return[2,["keyChanged"]]}))}))})),[2]}))}))},IndexedDBLocalPersistence.prototype.initializeSender=function(){var e,t;return __awaiter(this,void 0,void 0,(function(){var r,n;return __generator(this,(function(i){switch(i.label){case 0:return r=this,[4,_getActiveServiceWorker()];case 1:return r.activeServiceWorker=i.sent(),this.activeServiceWorker?(this.sender=new h(this.activeServiceWorker),[4,this.sender._send("ping",{},800)]):[2];case 2:return(n=i.sent())?((null===(e=n[0])||void 0===e?void 0:e.fulfilled)&&(null===(t=n[0])||void 0===t?void 0:t.value.includes("keyChanged"))&&(this.serviceWorkerReceiverAvailable=!0),[2]):[2]}}))}))},IndexedDBLocalPersistence.prototype.notifyServiceWorker=function(e){return __awaiter(this,void 0,void 0,(function(){return __generator(this,(function(t){switch(t.label){case 0:if(!this.sender||!this.activeServiceWorker||function _getServiceWorkerController(){var e;return(null===(e=null===navigator||void 0===navigator?void 0:navigator.serviceWorker)||void 0===e?void 0:e.controller)||null}()!==this.activeServiceWorker)return[2];t.label=1;case 1:return t.trys.push([1,3,,4]),[4,this.sender._send("keyChanged",{key:e},this.serviceWorkerReceiverAvailable?800:50)];case 2:case 3:return t.sent(),[3,4];case 4:return[2]}}))}))},IndexedDBLocalPersistence.prototype._isAvailable=function(){return __awaiter(this,void 0,void 0,(function(){var e;return __generator(this,(function(t){switch(t.label){case 0:return t.trys.push([0,4,,5]),indexedDB?[4,_openDatabase()]:[2,!1];case 1:return[4,_putObject(e=t.sent(),"__sak","1")];case 2:return t.sent(),[4,_deleteObject(e,"__sak")];case 3:return t.sent(),[2,!0];case 4:return t.sent(),[3,5];case 5:return[2,!1]}}))}))},IndexedDBLocalPersistence.prototype._withPendingWrite=function(e){return __awaiter(this,void 0,void 0,(function(){return __generator(this,(function(t){switch(t.label){case 0:this.pendingWrites++,t.label=1;case 1:return t.trys.push([1,,3,4]),[4,e()];case 2:return t.sent(),[3,4];case 3:return this.pendingWrites--,[7];case 4:return[2]}}))}))},IndexedDBLocalPersistence.prototype._set=function(e,t){return __awaiter(this,void 0,void 0,(function(){var r=this;return __generator(this,(function(n){return[2,this._withPendingWrite((function(){return __awaiter(r,void 0,void 0,(function(){return __generator(this,(function(r){switch(r.label){case 0:return[4,this._withRetries((function(r){return _putObject(r,e,t)}))];case 1:return r.sent(),this.localCache[e]=t,[2,this.notifyServiceWorker(e)]}}))}))}))]}))}))},IndexedDBLocalPersistence.prototype._get=function(e){return __awaiter(this,void 0,void 0,(function(){var t;return __generator(this,(function(r){switch(r.label){case 0:return[4,this._withRetries((function(t){return function getObject(e,t){return __awaiter(this,void 0,void 0,(function(){var r,n;return __generator(this,(function(i){switch(i.label){case 0:return r=getObjectStore(e,!1).get(t),[4,new f(r).toPromise()];case 1:return[2,void 0===(n=i.sent())?null:n.value]}}))}))}(t,e)}))];case 1:return t=r.sent(),this.localCache[e]=t,[2,t]}}))}))},IndexedDBLocalPersistence.prototype._remove=function(e){return __awaiter(this,void 0,void 0,(function(){var t=this;return __generator(this,(function(r){return[2,this._withPendingWrite((function(){return __awaiter(t,void 0,void 0,(function(){return __generator(this,(function(t){switch(t.label){case 0:return[4,this._withRetries((function(t){return _deleteObject(t,e)}))];case 1:return t.sent(),delete this.localCache[e],[2,this.notifyServiceWorker(e)]}}))}))}))]}))}))},IndexedDBLocalPersistence.prototype._poll=function(){return __awaiter(this,void 0,void 0,(function(){var e,t,r,n,i,o,s,a,u,c,d;return __generator(this,(function(l){switch(l.label){case 0:return[4,this._withRetries((function(e){var t=getObjectStore(e,!1).getAll();return new f(t).toPromise()}))];case 1:if(!(e=l.sent()))return[2,[]];if(0!==this.pendingWrites)return[2,[]];if(t=[],r=new Set,0!==e.length)for(n=0,i=e;n<i.length;n++)o=i[n],s=o.fbase_key,a=o.value,r.add(s),JSON.stringify(this.localCache[s])!==JSON.stringify(a)&&(this.notifyListeners(s,a),t.push(s));for(u=0,c=Object.keys(this.localCache);u<c.length;u++)d=c[u],this.localCache[d]&&!r.has(d)&&(this.notifyListeners(d,null),t.push(d));return[2,t]}}))}))},IndexedDBLocalPersistence.prototype.notifyListeners=function(e,t){this.localCache[e]=t;var r=this.listeners[e];if(r)for(var n=0,i=Array.from(r);n<i.length;n++){(0,i[n])(t)}},IndexedDBLocalPersistence.prototype.startPolling=function(){var e=this;this.stopPolling(),this.pollTimer=setInterval((function(){return __awaiter(e,void 0,void 0,(function(){return __generator(this,(function(e){return[2,this._poll()]}))}))}),800)},IndexedDBLocalPersistence.prototype.stopPolling=function(){this.pollTimer&&(clearInterval(this.pollTimer),this.pollTimer=null)},IndexedDBLocalPersistence.prototype._addListener=function(e,t){0===Object.keys(this.listeners).length&&this.startPolling(),this.listeners[e]||(this.listeners[e]=new Set,this._get(e)),this.listeners[e].add(t)},IndexedDBLocalPersistence.prototype._removeListener=function(e,t){this.listeners[e]&&(this.listeners[e].delete(t),0===this.listeners[e].size&&delete this.listeners[e]),0===Object.keys(this.listeners).length&&this.stopPolling()},IndexedDBLocalPersistence.type="LOCAL",IndexedDBLocalPersistence}(),v=_;function _prodErrorMap(){var e;return(e={})["dependent-sdk-initialized-before-auth"]="Another Firebase SDK was initialized and is trying to use Auth before Auth is initialized. Please be sure to call `initializeAuth` or `getAuth` before starting any other Firebase SDK.",e}var g=function _debugErrorMap(){var e;return(e={})["admin-restricted-operation"]="This operation is restricted to administrators only.",e["argument-error"]="",e["app-not-authorized"]="This app, identified by the domain where it's hosted, is not authorized to use Firebase Authentication with the provided API key. Review your key configuration in the Google API console.",e["app-not-installed"]="The requested mobile application corresponding to the identifier (Android package name or iOS bundle ID) provided is not installed on this device.",e["captcha-check-failed"]="The reCAPTCHA response token provided is either invalid, expired, already used or the domain associated with it does not match the list of whitelisted domains.",e["code-expired"]="The SMS code has expired. Please re-send the verification code to try again.",e["cordova-not-ready"]="Cordova framework is not ready.",e["cors-unsupported"]="This browser is not supported.",e["credential-already-in-use"]="This credential is already associated with a different user account.",e["custom-token-mismatch"]="The custom token corresponds to a different audience.",e["requires-recent-login"]="This operation is sensitive and requires recent authentication. Log in again before retrying this request.",e["dependent-sdk-initialized-before-auth"]="Another Firebase SDK was initialized and is trying to use Auth before Auth is initialized. Please be sure to call `initializeAuth` or `getAuth` before starting any other Firebase SDK.",e["dynamic-link-not-activated"]="Please activate Dynamic Links in the Firebase Console and agree to the terms and conditions.",e["email-change-needs-verification"]="Multi-factor users must always have a verified email.",e["email-already-in-use"]="The email address is already in use by another account.",e["emulator-config-failed"]='Auth instance has already been used to make a network call. Auth can no longer be configured to use the emulator. Try calling "connectAuthEmulator()" sooner.',e["expired-action-code"]="The action code has expired.",e["cancelled-popup-request"]="This operation has been cancelled due to another conflicting popup being opened.",e["internal-error"]="An internal AuthError has occurred.",e["invalid-app-credential"]="The phone verification request contains an invalid application verifier. The reCAPTCHA token response is either invalid or expired.",e["invalid-app-id"]="The mobile app identifier is not registered for the current project.",e["invalid-user-token"]="This user's credential isn't valid for this project. This can happen if the user's token has been tampered with, or if the user isn't for the project associated with this API key.",e["invalid-auth-event"]="An internal AuthError has occurred.",e["invalid-verification-code"]="The SMS verification code used to create the phone auth credential is invalid. Please resend the verification code sms and be sure to use the verification code provided by the user.",e["invalid-continue-uri"]="The continue URL provided in the request is invalid.",e["invalid-cordova-configuration"]="The following Cordova plugins must be installed to enable OAuth sign-in: cordova-plugin-buildinfo, cordova-universal-links-plugin, cordova-plugin-browsertab, cordova-plugin-inappbrowser and cordova-plugin-customurlscheme.",e["invalid-custom-token"]="The custom token format is incorrect. Please check the documentation.",e["invalid-dynamic-link-domain"]="The provided dynamic link domain is not configured or authorized for the current project.",e["invalid-email"]="The email address is badly formatted.",e["invalid-emulator-scheme"]="Emulator URL must start with a valid scheme (http:// or https://).",e["invalid-api-key"]="Your API key is invalid, please check you have copied it correctly.",e["invalid-cert-hash"]="The SHA-1 certificate hash provided is invalid.",e["invalid-credential"]="The supplied auth credential is incorrect, malformed or has expired.",e["invalid-message-payload"]="The email template corresponding to this action contains invalid characters in its message. Please fix by going to the Auth email templates section in the Firebase Console.",e["invalid-multi-factor-session"]="The request does not contain a valid proof of first factor successful sign-in.",e["invalid-oauth-provider"]="EmailAuthProvider is not supported for this operation. This operation only supports OAuth providers.",e["invalid-oauth-client-id"]="The OAuth client ID provided is either invalid or does not match the specified API key.",e["unauthorized-domain"]="This domain is not authorized for OAuth operations for your Firebase project. Edit the list of authorized domains from the Firebase console.",e["invalid-action-code"]="The action code is invalid. This can happen if the code is malformed, expired, or has already been used.",e["wrong-password"]="The password is invalid or the user does not have a password.",e["invalid-persistence-type"]="The specified persistence type is invalid. It can only be local, session or none.",e["invalid-phone-number"]="The format of the phone number provided is incorrect. Please enter the phone number in a format that can be parsed into E.164 format. E.164 phone numbers are written in the format [+][country code][subscriber number including area code].",e["invalid-provider-id"]="The specified provider ID is invalid.",e["invalid-recipient-email"]="The email corresponding to this action failed to send as the provided recipient email address is invalid.",e["invalid-sender"]="The email template corresponding to this action contains an invalid sender email or name. Please fix by going to the Auth email templates section in the Firebase Console.",e["invalid-verification-id"]="The verification ID used to create the phone auth credential is invalid.",e["invalid-tenant-id"]="The Auth instance's tenant ID is invalid.",e["login-blocked"]="Login blocked by user-provided method: {$originalMessage}",e["missing-android-pkg-name"]="An Android Package Name must be provided if the Android App is required to be installed.",e["auth-domain-config-required"]="Be sure to include authDomain when calling firebase.initializeApp(), by following the instructions in the Firebase console.",e["missing-app-credential"]="The phone verification request is missing an application verifier assertion. A reCAPTCHA response token needs to be provided.",e["missing-verification-code"]="The phone auth credential was created with an empty SMS verification code.",e["missing-continue-uri"]="A continue URL must be provided in the request.",e["missing-iframe-start"]="An internal AuthError has occurred.",e["missing-ios-bundle-id"]="An iOS Bundle ID must be provided if an App Store ID is provided.",e["missing-or-invalid-nonce"]="The request does not contain a valid nonce. This can occur if the SHA-256 hash of the provided raw nonce does not match the hashed nonce in the ID token payload.",e["missing-password"]="A non-empty password must be provided",e["missing-multi-factor-info"]="No second factor identifier is provided.",e["missing-multi-factor-session"]="The request is missing proof of first factor successful sign-in.",e["missing-phone-number"]="To send verification codes, provide a phone number for the recipient.",e["missing-verification-id"]="The phone auth credential was created with an empty verification ID.",e["app-deleted"]="This instance of FirebaseApp has been deleted.",e["multi-factor-info-not-found"]="The user does not have a second factor matching the identifier provided.",e["multi-factor-auth-required"]="Proof of ownership of a second factor is required to complete sign-in.",e["account-exists-with-different-credential"]="An account already exists with the same email address but different sign-in credentials. Sign in using a provider associated with this email address.",e["network-request-failed"]="A network AuthError (such as timeout, interrupted connection or unreachable host) has occurred.",e["no-auth-event"]="An internal AuthError has occurred.",e["no-such-provider"]="User was not linked to an account with the given provider.",e["null-user"]="A null user object was provided as the argument for an operation which requires a non-null user object.",e["operation-not-allowed"]="The given sign-in provider is disabled for this Firebase project. Enable it in the Firebase console, under the sign-in method tab of the Auth section.",e["operation-not-supported-in-this-environment"]='This operation is not supported in the environment this application is running on. "location.protocol" must be http, https or chrome-extension and web storage must be enabled.',e["popup-blocked"]="Unable to establish a connection with the popup. It may have been blocked by the browser.",e["popup-closed-by-user"]="The popup has been closed by the user before finalizing the operation.",e["provider-already-linked"]="User can only be linked to one identity for the given provider.",e["quota-exceeded"]="The project's quota for this operation has been exceeded.",e["redirect-cancelled-by-user"]="The redirect operation has been cancelled by the user before finalizing.",e["redirect-operation-pending"]="A redirect sign-in operation is already pending.",e["rejected-credential"]="The request contains malformed or mismatching credentials.",e["second-factor-already-in-use"]="The second factor is already enrolled on this account.",e["maximum-second-factor-count-exceeded"]="The maximum allowed number of second factors on a user has been exceeded.",e["tenant-id-mismatch"]="The provided tenant ID does not match the Auth instance's tenant ID",e.timeout="The operation has timed out.",e["user-token-expired"]="The user's credential is no longer valid. The user must sign in again.",e["too-many-requests"]="We have blocked all requests from this device due to unusual activity. Try again later.",e["unauthorized-continue-uri"]="The domain of the continue URL is not whitelisted.  Please whitelist the domain in the Firebase console.",e["unsupported-first-factor"]="Enrolling a second factor or signing in with a multi-factor account requires sign-in with a supported first factor.",e["unsupported-persistence-type"]="The current environment does not support the specified persistence type.",e["unsupported-tenant-operation"]="This operation is not supported in a multi-tenant context.",e["unverified-email"]="The operation requires a verified email.",e["user-cancelled"]="The user did not grant your application the permissions it requested.",e["user-not-found"]="There is no user record corresponding to this identifier. The user may have been deleted.",e["user-disabled"]="The user account has been disabled by an administrator.",e["user-mismatch"]="The supplied credentials do not correspond to the previously signed in user.",e["user-signed-out"]="",e["weak-password"]="The password must be 6 characters long or more.",e["web-storage-unsupported"]="This browser is not supported or 3rd party cookies and data may be disabled.",e["already-initialized"]="initializeAuth() has already been called with different options. To avoid this error, call initializeAuth() with the same options as when it was originally called, or call getAuth() to return the already initialized instance.",e["missing-recaptcha-token"]="The reCAPTCHA token is missing when sending request to the backend.",e["invalid-recaptcha-token"]="The reCAPTCHA token is invalid when sending request to the backend.",e["invalid-recaptcha-action"]="The reCAPTCHA action is invalid when sending request to the backend.",e["recaptcha-not-enabled"]="reCAPTCHA Enterprise integration is not enabled for this project.",e["missing-client-type"]="The reCAPTCHA client type is missing when sending request to the backend.",e["missing-recaptcha-version"]="The reCAPTCHA version is missing when sending request to the backend.",e["invalid-req-type"]="Invalid request parameters.",e["invalid-recaptcha-version"]="The reCAPTCHA version is invalid when sending request to the backend.",e["unsupported-password-policy-schema-version"]="The password policy received from the backend uses a schema version that is not supported by this version of the Firebase SDK.",e["password-does-not-meet-requirements"]="The password does not meet the requirements.",e},m=_prodErrorMap,I=new ErrorFactory("auth","Firebase",_prodErrorMap()),A={ADMIN_ONLY_OPERATION:"auth/admin-restricted-operation",ARGUMENT_ERROR:"auth/argument-error",APP_NOT_AUTHORIZED:"auth/app-not-authorized",APP_NOT_INSTALLED:"auth/app-not-installed",CAPTCHA_CHECK_FAILED:"auth/captcha-check-failed",CODE_EXPIRED:"auth/code-expired",CORDOVA_NOT_READY:"auth/cordova-not-ready",CORS_UNSUPPORTED:"auth/cors-unsupported",CREDENTIAL_ALREADY_IN_USE:"auth/credential-already-in-use",CREDENTIAL_MISMATCH:"auth/custom-token-mismatch",CREDENTIAL_TOO_OLD_LOGIN_AGAIN:"auth/requires-recent-login",DEPENDENT_SDK_INIT_BEFORE_AUTH:"auth/dependent-sdk-initialized-before-auth",DYNAMIC_LINK_NOT_ACTIVATED:"auth/dynamic-link-not-activated",EMAIL_CHANGE_NEEDS_VERIFICATION:"auth/email-change-needs-verification",EMAIL_EXISTS:"auth/email-already-in-use",EMULATOR_CONFIG_FAILED:"auth/emulator-config-failed",EXPIRED_OOB_CODE:"auth/expired-action-code",EXPIRED_POPUP_REQUEST:"auth/cancelled-popup-request",INTERNAL_ERROR:"auth/internal-error",INVALID_API_KEY:"auth/invalid-api-key",INVALID_APP_CREDENTIAL:"auth/invalid-app-credential",INVALID_APP_ID:"auth/invalid-app-id",INVALID_AUTH:"auth/invalid-user-token",INVALID_AUTH_EVENT:"auth/invalid-auth-event",INVALID_CERT_HASH:"auth/invalid-cert-hash",INVALID_CODE:"auth/invalid-verification-code",INVALID_CONTINUE_URI:"auth/invalid-continue-uri",INVALID_CORDOVA_CONFIGURATION:"auth/invalid-cordova-configuration",INVALID_CUSTOM_TOKEN:"auth/invalid-custom-token",INVALID_DYNAMIC_LINK_DOMAIN:"auth/invalid-dynamic-link-domain",INVALID_EMAIL:"auth/invalid-email",INVALID_EMULATOR_SCHEME:"auth/invalid-emulator-scheme",INVALID_IDP_RESPONSE:"auth/invalid-credential",INVALID_LOGIN_CREDENTIALS:"auth/invalid-credential",INVALID_MESSAGE_PAYLOAD:"auth/invalid-message-payload",INVALID_MFA_SESSION:"auth/invalid-multi-factor-session",INVALID_OAUTH_CLIENT_ID:"auth/invalid-oauth-client-id",INVALID_OAUTH_PROVIDER:"auth/invalid-oauth-provider",INVALID_OOB_CODE:"auth/invalid-action-code",INVALID_ORIGIN:"auth/unauthorized-domain",INVALID_PASSWORD:"auth/wrong-password",INVALID_PERSISTENCE:"auth/invalid-persistence-type",INVALID_PHONE_NUMBER:"auth/invalid-phone-number",INVALID_PROVIDER_ID:"auth/invalid-provider-id",INVALID_RECIPIENT_EMAIL:"auth/invalid-recipient-email",INVALID_SENDER:"auth/invalid-sender",INVALID_SESSION_INFO:"auth/invalid-verification-id",INVALID_TENANT_ID:"auth/invalid-tenant-id",MFA_INFO_NOT_FOUND:"auth/multi-factor-info-not-found",MFA_REQUIRED:"auth/multi-factor-auth-required",MISSING_ANDROID_PACKAGE_NAME:"auth/missing-android-pkg-name",MISSING_APP_CREDENTIAL:"auth/missing-app-credential",MISSING_AUTH_DOMAIN:"auth/auth-domain-config-required",MISSING_CODE:"auth/missing-verification-code",MISSING_CONTINUE_URI:"auth/missing-continue-uri",MISSING_IFRAME_START:"auth/missing-iframe-start",MISSING_IOS_BUNDLE_ID:"auth/missing-ios-bundle-id",MISSING_OR_INVALID_NONCE:"auth/missing-or-invalid-nonce",MISSING_MFA_INFO:"auth/missing-multi-factor-info",MISSING_MFA_SESSION:"auth/missing-multi-factor-session",MISSING_PHONE_NUMBER:"auth/missing-phone-number",MISSING_SESSION_INFO:"auth/missing-verification-id",MODULE_DESTROYED:"auth/app-deleted",NEED_CONFIRMATION:"auth/account-exists-with-different-credential",NETWORK_REQUEST_FAILED:"auth/network-request-failed",NULL_USER:"auth/null-user",NO_AUTH_EVENT:"auth/no-auth-event",NO_SUCH_PROVIDER:"auth/no-such-provider",OPERATION_NOT_ALLOWED:"auth/operation-not-allowed",OPERATION_NOT_SUPPORTED:"auth/operation-not-supported-in-this-environment",POPUP_BLOCKED:"auth/popup-blocked",POPUP_CLOSED_BY_USER:"auth/popup-closed-by-user",PROVIDER_ALREADY_LINKED:"auth/provider-already-linked",QUOTA_EXCEEDED:"auth/quota-exceeded",REDIRECT_CANCELLED_BY_USER:"auth/redirect-cancelled-by-user",REDIRECT_OPERATION_PENDING:"auth/redirect-operation-pending",REJECTED_CREDENTIAL:"auth/rejected-credential",SECOND_FACTOR_ALREADY_ENROLLED:"auth/second-factor-already-in-use",SECOND_FACTOR_LIMIT_EXCEEDED:"auth/maximum-second-factor-count-exceeded",TENANT_ID_MISMATCH:"auth/tenant-id-mismatch",TIMEOUT:"auth/timeout",TOKEN_EXPIRED:"auth/user-token-expired",TOO_MANY_ATTEMPTS_TRY_LATER:"auth/too-many-requests",UNAUTHORIZED_DOMAIN:"auth/unauthorized-continue-uri",UNSUPPORTED_FIRST_FACTOR:"auth/unsupported-first-factor",UNSUPPORTED_PERSISTENCE:"auth/unsupported-persistence-type",UNSUPPORTED_TENANT_OPERATION:"auth/unsupported-tenant-operation",UNVERIFIED_EMAIL:"auth/unverified-email",USER_CANCELLED:"auth/user-cancelled",USER_DELETED:"auth/user-not-found",USER_DISABLED:"auth/user-disabled",USER_MISMATCH:"auth/user-mismatch",USER_SIGNED_OUT:"auth/user-signed-out",WEAK_PASSWORD:"auth/weak-password",WEB_STORAGE_UNSUPPORTED:"auth/web-storage-unsupported",ALREADY_INITIALIZED:"auth/already-initialized",RECAPTCHA_NOT_ENABLED:"auth/recaptcha-not-enabled",MISSING_RECAPTCHA_TOKEN:"auth/missing-recaptcha-token",INVALID_RECAPTCHA_TOKEN:"auth/invalid-recaptcha-token",INVALID_RECAPTCHA_ACTION:"auth/invalid-recaptcha-action",MISSING_CLIENT_TYPE:"auth/missing-client-type",MISSING_RECAPTCHA_VERSION:"auth/missing-recaptcha-version",INVALID_RECAPTCHA_VERSION:"auth/invalid-recaptcha-version",INVALID_REQ_TYPE:"auth/invalid-req-type"},y=new class Logger{constructor(e){this.name=e,this._logLevel=c,this._logHandler=defaultLogHandler,this._userLogHandler=null}get logLevel(){return this._logLevel}set logLevel(e){if(!(e in a))throw new TypeError(`Invalid value "${e}" assigned to \`logLevel\``);this._logLevel=e}setLogLevel(e){this._logLevel="string"==typeof e?u[e]:e}get logHandler(){return this._logHandler}set logHandler(e){if("function"!=typeof e)throw new TypeError("Value assigned to `logHandler` must be a function");this._logHandler=e}get userLogHandler(){return this._userLogHandler}set userLogHandler(e){this._userLogHandler=e}debug(...e){this._userLogHandler&&this._userLogHandler(this,a.DEBUG,...e),this._logHandler(this,a.DEBUG,...e)}log(...e){this._userLogHandler&&this._userLogHandler(this,a.VERBOSE,...e),this._logHandler(this,a.VERBOSE,...e)}info(...e){this._userLogHandler&&this._userLogHandler(this,a.INFO,...e),this._logHandler(this,a.INFO,...e)}warn(...e){this._userLogHandler&&this._userLogHandler(this,a.WARN,...e),this._logHandler(this,a.WARN,...e)}error(...e){this._userLogHandler&&this._userLogHandler(this,a.ERROR,...e),this._logHandler(this,a.ERROR,...e)}}("@firebase/auth");function _logError(e){for(var r=[],n=1;n<arguments.length;n++)r[n-1]=arguments[n];y.logLevel<=a.ERROR&&y.error.apply(y,__spreadArray(["Auth (".concat(t,"): ").concat(e)],r,!1))}function _fail(e){for(var t=[],r=1;r<arguments.length;r++)t[r-1]=arguments[r];throw createErrorInternal.apply(void 0,__spreadArray([e],t,!1))}function _createError(e){for(var t=[],r=1;r<arguments.length;r++)t[r-1]=arguments[r];return createErrorInternal.apply(void 0,__spreadArray([e],t,!1))}function _errorWithCustomMessage(e,t,r){var n,i=__assign(__assign({},m()),((n={})[t]=r,n));return new ErrorFactory("auth","Firebase",i).create(t,{appName:e.name})}function _serverAppCurrentUserOperationNotSupportedError(e){return _errorWithCustomMessage(e,"operation-not-supported-in-this-environment","Operations that alter the current user are not supported in conjunction with FirebaseServerApp")}function _assertInstanceOf(e,t,r){if(!(t instanceof r))throw r.name!==t.constructor.name&&_fail(e,"argument-error"),_errorWithCustomMessage(e,"argument-error","Type of ".concat(t.constructor.name," does not match expected instance.")+"Did you pass a reference from a different Auth SDK?")}function createErrorInternal(e){for(var t,r=[],n=1;n<arguments.length;n++)r[n-1]=arguments[n];if("string"!=typeof e){var i=r[0],o=__spreadArray([],r.slice(1),!0);return o[0]&&(o[0].appName=e.name),(t=e._errorFactory).create.apply(t,__spreadArray([i],o,!1))}return I.create.apply(I,__spreadArray([e],r,!1))}function _assert(e,t){for(var r=[],n=2;n<arguments.length;n++)r[n-2]=arguments[n];if(!e)throw createErrorInternal.apply(void 0,__spreadArray([t],r,!1))}function debugFail(e){var t="INTERNAL ASSERTION FAILED: "+e;throw _logError(t),new Error(t)}function debugAssert(e,t){e||debugFail(t)}function _getCurrentUrl(){var e;return"undefined"!=typeof self&&(null===(e=self.location)||void 0===e?void 0:e.href)||""}function _isHttpOrHttps(){return"http:"===_getCurrentScheme()||"https:"===_getCurrentScheme()}function _getCurrentScheme(){var e;return"undefined"!=typeof self&&(null===(e=self.location)||void 0===e?void 0:e.protocol)||null}function _isOnline(){return!("undefined"!=typeof navigator&&navigator&&"onLine"in navigator&&"boolean"==typeof navigator.onLine&&(_isHttpOrHttps()||function isBrowserExtension(){const e="object"==typeof chrome?chrome.runtime:"object"==typeof browser?browser.runtime:void 0;return"object"==typeof e&&void 0!==e.id}()||"connection"in navigator))||navigator.onLine}var E=function(){function Delay(e,t){this.shortDelay=e,this.longDelay=t,debugAssert(t>e,"Short delay should be less than long delay!"),this.isMobile=function isMobileCordova(){return"undefined"!=typeof window&&!!(window.cordova||window.phonegap||window.PhoneGap)&&/ios|iphone|ipod|ipad|android|blackberry|iemobile/i.test(getUA())}()||function isReactNative(){return"object"==typeof navigator&&"ReactNative"===navigator.product}()}return Delay.prototype.get=function(){return _isOnline()?this.isMobile?this.longDelay:this.shortDelay:Math.min(5e3,this.shortDelay)},Delay}();function _emulatorUrl(e,t){debugAssert(e.emulator,"Emulator should always be set here");var r=e.emulator.url;return t?"".concat(r).concat(t.startsWith("/")?t.slice(1):t):r}var w,T=function(){function FetchProvider(){}return FetchProvider.initialize=function(e,t,r){this.fetchImpl=e,t&&(this.headersImpl=t),r&&(this.responseImpl=r)},FetchProvider.fetch=function(){return this.fetchImpl?this.fetchImpl:"undefined"!=typeof self&&"fetch"in self?self.fetch:"undefined"!=typeof globalThis&&globalThis.fetch?globalThis.fetch:"undefined"!=typeof fetch?fetch:void debugFail("Could not find fetch implementation, make sure you call FetchProvider.initialize() with an appropriate polyfill")},FetchProvider.headers=function(){return this.headersImpl?this.headersImpl:"undefined"!=typeof self&&"Headers"in self?self.Headers:"undefined"!=typeof globalThis&&globalThis.Headers?globalThis.Headers:"undefined"!=typeof Headers?Headers:void debugFail("Could not find Headers implementation, make sure you call FetchProvider.initialize() with an appropriate polyfill")},FetchProvider.response=function(){return this.responseImpl?this.responseImpl:"undefined"!=typeof self&&"Response"in self?self.Response:"undefined"!=typeof globalThis&&globalThis.Response?globalThis.Response:"undefined"!=typeof Response?Response:void debugFail("Could not find Response implementation, make sure you call FetchProvider.initialize() with an appropriate polyfill")},FetchProvider}(),b=((w={}).CREDENTIAL_MISMATCH="custom-token-mismatch",w.MISSING_CUSTOM_TOKEN="internal-error",w.INVALID_IDENTIFIER="invalid-email",w.MISSING_CONTINUE_URI="internal-error",w.INVALID_PASSWORD="wrong-password",w.MISSING_PASSWORD="missing-password",w.INVALID_LOGIN_CREDENTIALS="invalid-credential",w.EMAIL_EXISTS="email-already-in-use",w.PASSWORD_LOGIN_DISABLED="operation-not-allowed",w.INVALID_IDP_RESPONSE="invalid-credential",w.INVALID_PENDING_TOKEN="invalid-credential",w.FEDERATED_USER_ID_ALREADY_LINKED="credential-already-in-use",w.MISSING_REQ_TYPE="internal-error",w.EMAIL_NOT_FOUND="user-not-found",w.RESET_PASSWORD_EXCEED_LIMIT="too-many-requests",w.EXPIRED_OOB_CODE="expired-action-code",w.INVALID_OOB_CODE="invalid-action-code",w.MISSING_OOB_CODE="internal-error",w.CREDENTIAL_TOO_OLD_LOGIN_AGAIN="requires-recent-login",w.INVALID_ID_TOKEN="invalid-user-token",w.TOKEN_EXPIRED="user-token-expired",w.USER_NOT_FOUND="user-token-expired",w.TOO_MANY_ATTEMPTS_TRY_LATER="too-many-requests",w.PASSWORD_DOES_NOT_MEET_REQUIREMENTS="password-does-not-meet-requirements",w.INVALID_CODE="invalid-verification-code",w.INVALID_SESSION_INFO="invalid-verification-id",w.INVALID_TEMPORARY_PROOF="invalid-credential",w.MISSING_SESSION_INFO="missing-verification-id",w.SESSION_EXPIRED="code-expired",w.MISSING_ANDROID_PACKAGE_NAME="missing-android-pkg-name",w.UNAUTHORIZED_DOMAIN="unauthorized-continue-uri",w.INVALID_OAUTH_CLIENT_ID="invalid-oauth-client-id",w.ADMIN_ONLY_OPERATION="admin-restricted-operation",w.INVALID_MFA_PENDING_CREDENTIAL="invalid-multi-factor-session",w.MFA_ENROLLMENT_NOT_FOUND="multi-factor-info-not-found",w.MISSING_MFA_ENROLLMENT_ID="missing-multi-factor-info",w.MISSING_MFA_PENDING_CREDENTIAL="missing-multi-factor-session",w.SECOND_FACTOR_EXISTS="second-factor-already-in-use",w.SECOND_FACTOR_LIMIT_EXCEEDED="maximum-second-factor-count-exceeded",w.BLOCKING_FUNCTION_ERROR_RESPONSE="internal-error",w.RECAPTCHA_NOT_ENABLED="recaptcha-not-enabled",w.MISSING_RECAPTCHA_TOKEN="missing-recaptcha-token",w.INVALID_RECAPTCHA_TOKEN="invalid-recaptcha-token",w.INVALID_RECAPTCHA_ACTION="invalid-recaptcha-action",w.MISSING_CLIENT_TYPE="missing-client-type",w.MISSING_RECAPTCHA_VERSION="missing-recaptcha-version",w.INVALID_RECAPTCHA_VERSION="invalid-recaptcha-version",w.INVALID_REQ_TYPE="invalid-req-type",w),P=new E(3e4,6e4);function _addTidIfNecessary(e,t){return e.tenantId&&!t.tenantId?__assign(__assign({},t),{tenantId:e.tenantId}):t}function _performApiRequest(e,t,r,n,i){return void 0===i&&(i={}),__awaiter(this,void 0,void 0,(function(){var o=this;return __generator(this,(function(s){return[2,_performFetchWithErrorHandling(e,i,(function(){return __awaiter(o,void 0,void 0,(function(){var i,o,s,a,u;return __generator(this,(function(c){switch(c.label){case 0:return i={},o={},n&&("GET"===t?o=n:i={body:JSON.stringify(n)}),s=querystring(__assign({key:e.config.apiKey},o)).slice(1),[4,e._getAdditionalHeaders()];case 1:return(a=c.sent())["Content-Type"]="application/json",e.languageCode&&(a["X-Firebase-Locale"]=e.languageCode),u=__assign({method:t,headers:a},i),function isCloudflareWorker(){return"undefined"!=typeof navigator&&"Cloudflare-Workers"===navigator.userAgent}()||(u.referrerPolicy="no-referrer"),[2,T.fetch()(_getFinalTarget(e,e.config.apiHost,r,s),u)]}}))}))}))]}))}))}function _performFetchWithErrorHandling(e,t,r){return __awaiter(this,void 0,void 0,(function(){var n,i,o,s,a,u,c,d,l,h;return __generator(this,(function(p){switch(p.label){case 0:e._canInitEmulator=!1,n=__assign(__assign({},b),t),p.label=1;case 1:return p.trys.push([1,4,,5]),i=new S(e),[4,Promise.race([r(),i.promise])];case 2:return o=p.sent(),i.clearNetworkTimeout(),[4,o.json()];case 3:if("needConfirmation"in(s=p.sent()))throw _makeTaggedError(e,"account-exists-with-different-credential",s);if(o.ok&&!("errorMessage"in s))return[2,s];if(a=o.ok?s.errorMessage:s.error.message,u=a.split(" : "),c=u[0],d=u[1],"FEDERATED_USER_ID_ALREADY_LINKED"===c)throw _makeTaggedError(e,"credential-already-in-use",s);if("EMAIL_EXISTS"===c)throw _makeTaggedError(e,"email-already-in-use",s);if("USER_DISABLED"===c)throw _makeTaggedError(e,"user-disabled",s);if(l=n[c]||c.toLowerCase().replace(/[_\s]+/g,"-"),d)throw _errorWithCustomMessage(e,l,d);return _fail(e,l),[3,5];case 4:if((h=p.sent())instanceof FirebaseError)throw h;return _fail(e,"network-request-failed",{message:String(h)}),[3,5];case 5:return[2]}}))}))}function _performSignInRequest(e,t,r,n,i){return void 0===i&&(i={}),__awaiter(this,void 0,void 0,(function(){var o;return __generator(this,(function(s){switch(s.label){case 0:return[4,_performApiRequest(e,t,r,n,i)];case 1:return"mfaPendingCredential"in(o=s.sent())&&_fail(e,"multi-factor-auth-required",{_serverResponse:o}),[2,o]}}))}))}function _getFinalTarget(e,t,r,n){var i="".concat(t).concat(r,"?").concat(n);return e.config.emulator?_emulatorUrl(e.config,i):"".concat(e.config.apiScheme,"://").concat(i)}function _parseEnforcementState(e){switch(e){case"ENFORCE":return"ENFORCE";case"AUDIT":return"AUDIT";case"OFF":return"OFF";default:return"ENFORCEMENT_STATE_UNSPECIFIED"}}var S=function(){function NetworkTimeout(e){var t=this;this.auth=e,this.timer=null,this.promise=new Promise((function(e,r){t.timer=setTimeout((function(){return r(_createError(t.auth,"network-request-failed"))}),P.get())}))}return NetworkTimeout.prototype.clearNetworkTimeout=function(){clearTimeout(this.timer)},NetworkTimeout}();function _makeTaggedError(e,t,r){var n={appName:e.name};r.email&&(n.email=r.email),r.phoneNumber&&(n.phoneNumber=r.phoneNumber);var i=_createError(e,t,n);return i.customData._tokenResponse=r,i}function isEnterprise(e){return void 0!==e&&void 0!==e.enterprise}var R=function(){function RecaptchaConfig(e){if(this.siteKey="",this.recaptchaEnforcementState=[],void 0===e.recaptchaKey)throw new Error("recaptchaKey undefined");this.siteKey=e.recaptchaKey.split("/")[3],this.recaptchaEnforcementState=e.recaptchaEnforcementState}return RecaptchaConfig.prototype.getProviderEnforcementState=function(e){if(!this.recaptchaEnforcementState||0===this.recaptchaEnforcementState.length)return null;for(var t=0,r=this.recaptchaEnforcementState;t<r.length;t++){var n=r[t];if(n.provider&&n.provider===e)return _parseEnforcementState(n.enforcementState)}return null},RecaptchaConfig.prototype.isProviderEnabled=function(e){return"ENFORCE"===this.getProviderEnforcementState(e)||"AUDIT"===this.getProviderEnforcementState(e)},RecaptchaConfig}();function getRecaptchaConfig(e,t){return __awaiter(this,void 0,void 0,(function(){return __generator(this,(function(r){return[2,_performApiRequest(e,"GET","/v2/recaptchaConfig",_addTidIfNecessary(e,t))]}))}))}function deleteAccount(e,t){return __awaiter(this,void 0,void 0,(function(){return __generator(this,(function(r){return[2,_performApiRequest(e,"POST","/v1/accounts:delete",t)]}))}))}function deleteLinkedAccounts(e,t){return __awaiter(this,void 0,void 0,(function(){return __generator(this,(function(r){return[2,_performApiRequest(e,"POST","/v1/accounts:update",t)]}))}))}function getAccountInfo(e,t){return __awaiter(this,void 0,void 0,(function(){return __generator(this,(function(r){return[2,_performApiRequest(e,"POST","/v1/accounts:lookup",t)]}))}))}function utcTimestampToDateString(e){if(e)try{var t=new Date(Number(e));if(!isNaN(t.getTime()))return t.toUTCString()}catch(e){}}function getIdToken(e,t){return void 0===t&&(t=!1),getModularInstance(e).getIdToken(t)}function getIdTokenResult(e,t){return void 0===t&&(t=!1),__awaiter(this,void 0,void 0,(function(){var r,n,i,o,s;return __generator(this,(function(a){switch(a.label){case 0:return[4,(r=getModularInstance(e)).getIdToken(t)];case 1:return n=a.sent(),_assert((i=_parseToken(n))&&i.exp&&i.auth_time&&i.iat,r.auth,"internal-error"),o="object"==typeof i.firebase?i.firebase:void 0,s=null==o?void 0:o.sign_in_provider,[2,{claims:i,token:n,authTime:utcTimestampToDateString(secondsStringToMilliseconds(i.auth_time)),issuedAtTime:utcTimestampToDateString(secondsStringToMilliseconds(i.iat)),expirationTime:utcTimestampToDateString(secondsStringToMilliseconds(i.exp)),signInProvider:s||null,signInSecondFactor:(null==o?void 0:o.sign_in_second_factor)||null}]}}))}))}function secondsStringToMilliseconds(e){return 1e3*Number(e)}function _parseToken(e){var t=e.split("."),r=t[0],n=t[1],i=t[2];if(void 0===r||void 0===n||void 0===i)return _logError("JWT malformed, contained fewer than 3 sections"),null;try{var s=function(e){try{return o.decodeString(e,!0)}catch(e){console.error("base64Decode failed: ",e)}return null}(n);return s?JSON.parse(s):(_logError("Failed to decode base64 JWT payload"),null)}catch(e){return _logError("Caught error parsing JWT payload as JSON",null==e?void 0:e.toString()),null}}function _tokenExpiresIn(e){var t=_parseToken(e);return _assert(t,"internal-error"),_assert(void 0!==t.exp,"internal-error"),_assert(void 0!==t.iat,"internal-error"),Number(t.exp)-Number(t.iat)}function _logoutIfInvalidated(e,t,r){return void 0===r&&(r=!1),__awaiter(this,void 0,void 0,(function(){var n;return __generator(this,(function(i){switch(i.label){case 0:if(r)return[2,t];i.label=1;case 1:return i.trys.push([1,3,,6]),[4,t];case 2:return[2,i.sent()];case 3:return(n=i.sent())instanceof FirebaseError&&function isUserInvalidated(e){var t=e.code;return t==="auth/".concat("user-disabled")||t==="auth/".concat("user-token-expired")}(n)?e.auth.currentUser!==e?[3,5]:[4,e.auth.signOut()]:[3,5];case 4:i.sent(),i.label=5;case 5:throw n;case 6:return[2]}}))}))}var C=function(){function ProactiveRefresh(e){this.user=e,this.isRunning=!1,this.timerId=null,this.errorBackoff=3e4}return ProactiveRefresh.prototype._start=function(){this.isRunning||(this.isRunning=!0,this.schedule())},ProactiveRefresh.prototype._stop=function(){this.isRunning&&(this.isRunning=!1,null!==this.timerId&&clearTimeout(this.timerId))},ProactiveRefresh.prototype.getInterval=function(e){var t;if(e){var r=this.errorBackoff;return this.errorBackoff=Math.min(2*this.errorBackoff,96e4),r}this.errorBackoff=3e4;r=(null!==(t=this.user.stsTokenManager.expirationTime)&&void 0!==t?t:0)-Date.now()-3e5;return Math.max(0,r)},ProactiveRefresh.prototype.schedule=function(e){var t=this;if(void 0===e&&(e=!1),this.isRunning){var r=this.getInterval(e);this.timerId=setTimeout((function(){return __awaiter(t,void 0,void 0,(function(){return __generator(this,(function(e){switch(e.label){case 0:return[4,this.iteration()];case 1:return e.sent(),[2]}}))}))}),r)}},ProactiveRefresh.prototype.iteration=function(){return __awaiter(this,void 0,void 0,(function(){var e;return __generator(this,(function(t){switch(t.label){case 0:return t.trys.push([0,2,,3]),[4,this.user.getIdToken(!0)];case 1:return t.sent(),[3,3];case 2:return(null==(e=t.sent())?void 0:e.code)==="auth/".concat("network-request-failed")&&this.schedule(!0),[2];case 3:return this.schedule(),[2]}}))}))},ProactiveRefresh}(),k=function(){function UserMetadata(e,t){this.createdAt=e,this.lastLoginAt=t,this._initializeTime()}return UserMetadata.prototype._initializeTime=function(){this.lastSignInTime=utcTimestampToDateString(this.lastLoginAt),this.creationTime=utcTimestampToDateString(this.createdAt)},UserMetadata.prototype._copy=function(e){this.createdAt=e.createdAt,this.lastLoginAt=e.lastLoginAt,this._initializeTime()},UserMetadata.prototype.toJSON=function(){return{createdAt:this.createdAt,lastLoginAt:this.lastLoginAt}},UserMetadata}();function _reloadWithoutSaving(e){var t;return __awaiter(this,void 0,void 0,(function(){var r,n,i,o,s,a,u,c,d,l;return __generator(this,(function(h){switch(h.label){case 0:return r=e.auth,[4,e.getIdToken()];case 1:return n=h.sent(),[4,_logoutIfInvalidated(e,getAccountInfo(r,{idToken:n}))];case 2:return _assert(null==(i=h.sent())?void 0:i.users.length,r,"internal-error"),o=i.users[0],e._notifyReloadListener(o),s=(null===(t=o.providerUserInfo)||void 0===t?void 0:t.length)?extractProviderData(o.providerUserInfo):[],a=function mergeProviderData(e,t){return __spreadArray(__spreadArray([],e.filter((function(e){return!t.some((function(t){return t.providerId===e.providerId}))})),!0),t,!0)}(e.providerData,s),u=e.isAnonymous,c=!(e.email&&o.passwordHash||(null==a?void 0:a.length)),d=!!u&&c,l={uid:o.localId,displayName:o.displayName||null,photoURL:o.photoUrl||null,email:o.email||null,emailVerified:o.emailVerified||!1,phoneNumber:o.phoneNumber||null,tenantId:o.tenantId||null,providerData:a,metadata:new k(o.createdAt,o.lastLoginAt),isAnonymous:d},Object.assign(e,l),[2]}}))}))}function reload(e){return __awaiter(this,void 0,void 0,(function(){var t;return __generator(this,(function(r){switch(r.label){case 0:return[4,_reloadWithoutSaving(t=getModularInstance(e))];case 1:return r.sent(),[4,t.auth._persistUserIfCurrent(t)];case 2:return r.sent(),t.auth._notifyListenersIfCurrent(t),[2]}}))}))}function extractProviderData(e){return e.map((function(e){var t=e.providerId,r=__rest(e,["providerId"]);return{providerId:t,uid:r.rawId||"",displayName:r.displayName||null,email:r.email||null,phoneNumber:r.phoneNumber||null,photoURL:r.photoUrl||null}}))}function requestStsToken(e,t){return __awaiter(this,void 0,void 0,(function(){var r,n=this;return __generator(this,(function(i){switch(i.label){case 0:return[4,_performFetchWithErrorHandling(e,{},(function(){return __awaiter(n,void 0,void 0,(function(){var r,n,i,o,s,a;return __generator(this,(function(u){switch(u.label){case 0:return r=querystring({grant_type:"refresh_token",refresh_token:t}).slice(1),n=e.config,i=n.tokenApiHost,o=n.apiKey,s=_getFinalTarget(e,i,"/v1/token","key=".concat(o)),[4,e._getAdditionalHeaders()];case 1:return(a=u.sent())["Content-Type"]="application/x-www-form-urlencoded",[2,T.fetch()(s,{method:"POST",headers:a,body:r})]}}))}))}))];case 1:return[2,{accessToken:(r=i.sent()).access_token,expiresIn:r.expires_in,refreshToken:r.refresh_token}]}}))}))}function revokeToken(e,t){return __awaiter(this,void 0,void 0,(function(){return __generator(this,(function(r){return[2,_performApiRequest(e,"POST","/v2/accounts:revokeToken",_addTidIfNecessary(e,t))]}))}))}var O=function(){function StsTokenManager(){this.refreshToken=null,this.accessToken=null,this.expirationTime=null}return Object.defineProperty(StsTokenManager.prototype,"isExpired",{get:function(){return!this.expirationTime||Date.now()>this.expirationTime-3e4},enumerable:!1,configurable:!0}),StsTokenManager.prototype.updateFromServerResponse=function(e){_assert(e.idToken,"internal-error"),_assert(void 0!==e.idToken,"internal-error"),_assert(void 0!==e.refreshToken,"internal-error");var t="expiresIn"in e&&void 0!==e.expiresIn?Number(e.expiresIn):_tokenExpiresIn(e.idToken);this.updateTokensAndExpiration(e.idToken,e.refreshToken,t)},StsTokenManager.prototype.updateFromIdToken=function(e){_assert(0!==e.length,"internal-error");var t=_tokenExpiresIn(e);this.updateTokensAndExpiration(e,null,t)},StsTokenManager.prototype.getToken=function(e,t){return void 0===t&&(t=!1),__awaiter(this,void 0,void 0,(function(){return __generator(this,(function(r){switch(r.label){case 0:return t||!this.accessToken||this.isExpired?(_assert(this.refreshToken,e,"user-token-expired"),this.refreshToken?[4,this.refresh(e,this.refreshToken)]:[3,2]):[2,this.accessToken];case 1:return r.sent(),[2,this.accessToken];case 2:return[2,null]}}))}))},StsTokenManager.prototype.clearRefreshToken=function(){this.refreshToken=null},StsTokenManager.prototype.refresh=function(e,t){return __awaiter(this,void 0,void 0,(function(){var r,n,i,o;return __generator(this,(function(s){switch(s.label){case 0:return[4,requestStsToken(e,t)];case 1:return r=s.sent(),n=r.accessToken,i=r.refreshToken,o=r.expiresIn,this.updateTokensAndExpiration(n,i,Number(o)),[2]}}))}))},StsTokenManager.prototype.updateTokensAndExpiration=function(e,t,r){this.refreshToken=t||null,this.accessToken=e||null,this.expirationTime=Date.now()+1e3*r},StsTokenManager.fromJSON=function(e,t){var r=t.refreshToken,n=t.accessToken,i=t.expirationTime,o=new StsTokenManager;return r&&(_assert("string"==typeof r,"internal-error",{appName:e}),o.refreshToken=r),n&&(_assert("string"==typeof n,"internal-error",{appName:e}),o.accessToken=n),i&&(_assert("number"==typeof i,"internal-error",{appName:e}),o.expirationTime=i),o},StsTokenManager.prototype.toJSON=function(){return{refreshToken:this.refreshToken,accessToken:this.accessToken,expirationTime:this.expirationTime}},StsTokenManager.prototype._assign=function(e){this.accessToken=e.accessToken,this.refreshToken=e.refreshToken,this.expirationTime=e.expirationTime},StsTokenManager.prototype._clone=function(){return Object.assign(new StsTokenManager,this.toJSON())},StsTokenManager.prototype._performRefresh=function(){return debugFail("not implemented")},StsTokenManager}();function assertStringOrUndefined(e,t){_assert("string"==typeof e||void 0===e,"internal-error",{appName:t})}var N=function(){function UserImpl(e){var t=e.uid,r=e.auth,n=e.stsTokenManager,i=__rest(e,["uid","auth","stsTokenManager"]);this.providerId="firebase",this.proactiveRefresh=new C(this),this.reloadUserInfo=null,this.reloadListener=null,this.uid=t,this.auth=r,this.stsTokenManager=n,this.accessToken=n.accessToken,this.displayName=i.displayName||null,this.email=i.email||null,this.emailVerified=i.emailVerified||!1,this.phoneNumber=i.phoneNumber||null,this.photoURL=i.photoURL||null,this.isAnonymous=i.isAnonymous||!1,this.tenantId=i.tenantId||null,this.providerData=i.providerData?__spreadArray([],i.providerData,!0):[],this.metadata=new k(i.createdAt||void 0,i.lastLoginAt||void 0)}return UserImpl.prototype.getIdToken=function(e){return __awaiter(this,void 0,void 0,(function(){var t;return __generator(this,(function(r){switch(r.label){case 0:return[4,_logoutIfInvalidated(this,this.stsTokenManager.getToken(this.auth,e))];case 1:return _assert(t=r.sent(),this.auth,"internal-error"),this.accessToken===t?[3,3]:(this.accessToken=t,[4,this.auth._persistUserIfCurrent(this)]);case 2:r.sent(),this.auth._notifyListenersIfCurrent(this),r.label=3;case 3:return[2,t]}}))}))},UserImpl.prototype.getIdTokenResult=function(e){return getIdTokenResult(this,e)},UserImpl.prototype.reload=function(){return reload(this)},UserImpl.prototype._assign=function(e){this!==e&&(_assert(this.uid===e.uid,this.auth,"internal-error"),this.displayName=e.displayName,this.photoURL=e.photoURL,this.email=e.email,this.emailVerified=e.emailVerified,this.phoneNumber=e.phoneNumber,this.isAnonymous=e.isAnonymous,this.tenantId=e.tenantId,this.providerData=e.providerData.map((function(e){return __assign({},e)})),this.metadata._copy(e.metadata),this.stsTokenManager._assign(e.stsTokenManager))},UserImpl.prototype._clone=function(e){var t=new UserImpl(__assign(__assign({},this),{auth:e,stsTokenManager:this.stsTokenManager._clone()}));return t.metadata._copy(this.metadata),t},UserImpl.prototype._onReload=function(e){_assert(!this.reloadListener,this.auth,"internal-error"),this.reloadListener=e,this.reloadUserInfo&&(this._notifyReloadListener(this.reloadUserInfo),this.reloadUserInfo=null)},UserImpl.prototype._notifyReloadListener=function(e){this.reloadListener?this.reloadListener(e):this.reloadUserInfo=e},UserImpl.prototype._startProactiveRefresh=function(){this.proactiveRefresh._start()},UserImpl.prototype._stopProactiveRefresh=function(){this.proactiveRefresh._stop()},UserImpl.prototype._updateTokensIfNecessary=function(e,t){return void 0===t&&(t=!1),__awaiter(this,void 0,void 0,(function(){var r;return __generator(this,(function(n){switch(n.label){case 0:return r=!1,e.idToken&&e.idToken!==this.stsTokenManager.accessToken&&(this.stsTokenManager.updateFromServerResponse(e),r=!0),t?[4,_reloadWithoutSaving(this)]:[3,2];case 1:n.sent(),n.label=2;case 2:return[4,this.auth._persistUserIfCurrent(this)];case 3:return n.sent(),r&&this.auth._notifyListenersIfCurrent(this),[2]}}))}))},UserImpl.prototype.delete=function(){return __awaiter(this,void 0,void 0,(function(){var t;return __generator(this,(function(r){switch(r.label){case 0:return e(this.auth.app)?[2,Promise.reject(_serverAppCurrentUserOperationNotSupportedError(this.auth))]:[4,this.getIdToken()];case 1:return t=r.sent(),[4,_logoutIfInvalidated(this,deleteAccount(this.auth,{idToken:t}))];case 2:return r.sent(),this.stsTokenManager.clearRefreshToken(),[2,this.auth.signOut()]}}))}))},UserImpl.prototype.toJSON=function(){return __assign(__assign({uid:this.uid,email:this.email||void 0,emailVerified:this.emailVerified,displayName:this.displayName||void 0,isAnonymous:this.isAnonymous,photoURL:this.photoURL||void 0,phoneNumber:this.phoneNumber||void 0,tenantId:this.tenantId||void 0,providerData:this.providerData.map((function(e){return __assign({},e)})),stsTokenManager:this.stsTokenManager.toJSON(),_redirectEventId:this._redirectEventId},this.metadata.toJSON()),{apiKey:this.auth.config.apiKey,appName:this.auth.name})},Object.defineProperty(UserImpl.prototype,"refreshToken",{get:function(){return this.stsTokenManager.refreshToken||""},enumerable:!1,configurable:!0}),UserImpl._fromJSON=function(e,t){var r,n,i,o,s,a,u,c,d=null!==(r=t.displayName)&&void 0!==r?r:void 0,l=null!==(n=t.email)&&void 0!==n?n:void 0,h=null!==(i=t.phoneNumber)&&void 0!==i?i:void 0,p=null!==(o=t.photoURL)&&void 0!==o?o:void 0,f=null!==(s=t.tenantId)&&void 0!==s?s:void 0,_=null!==(a=t._redirectEventId)&&void 0!==a?a:void 0,v=null!==(u=t.createdAt)&&void 0!==u?u:void 0,g=null!==(c=t.lastLoginAt)&&void 0!==c?c:void 0,m=t.uid,I=t.emailVerified,A=t.isAnonymous,y=t.providerData,E=t.stsTokenManager;_assert(m&&E,e,"internal-error");var w=O.fromJSON(this.name,E);_assert("string"==typeof m,e,"internal-error"),assertStringOrUndefined(d,e.name),assertStringOrUndefined(l,e.name),_assert("boolean"==typeof I,e,"internal-error"),_assert("boolean"==typeof A,e,"internal-error"),assertStringOrUndefined(h,e.name),assertStringOrUndefined(p,e.name),assertStringOrUndefined(f,e.name),assertStringOrUndefined(_,e.name),assertStringOrUndefined(v,e.name),assertStringOrUndefined(g,e.name);var T=new UserImpl({uid:m,auth:e,email:l,emailVerified:I,displayName:d,isAnonymous:A,photoURL:p,phoneNumber:h,tenantId:f,stsTokenManager:w,createdAt:v,lastLoginAt:g});return y&&Array.isArray(y)&&(T.providerData=y.map((function(e){return __assign({},e)}))),_&&(T._redirectEventId=_),T},UserImpl._fromIdTokenResponse=function(e,t,r){return void 0===r&&(r=!1),__awaiter(this,void 0,void 0,(function(){var n,i;return __generator(this,(function(o){switch(o.label){case 0:return(n=new O).updateFromServerResponse(t),[4,_reloadWithoutSaving(i=new UserImpl({uid:t.localId,auth:e,stsTokenManager:n,isAnonymous:r}))];case 1:return o.sent(),[2,i]}}))}))},UserImpl._fromGetAccountInfoResponse=function(e,t,r){return __awaiter(this,void 0,void 0,(function(){var n,i,o,s,a,u;return __generator(this,(function(c){return _assert(void 0!==(n=t.users[0]).localId,"internal-error"),i=void 0!==n.providerUserInfo?extractProviderData(n.providerUserInfo):[],o=!(n.email&&n.passwordHash||(null==i?void 0:i.length)),(s=new O).updateFromIdToken(r),a=new UserImpl({uid:n.localId,auth:e,stsTokenManager:s,isAnonymous:o}),u={uid:n.localId,displayName:n.displayName||null,photoURL:n.photoUrl||null,email:n.email||null,emailVerified:n.emailVerified||!1,phoneNumber:n.phoneNumber||null,tenantId:n.tenantId||null,providerData:i,metadata:new k(n.createdAt,n.lastLoginAt),isAnonymous:!(n.email&&n.passwordHash||(null==i?void 0:i.length))},Object.assign(a,u),[2,a]}))}))},UserImpl}(),L=new Map;function _getInstance(e){debugAssert(e instanceof Function,"Expected a class definition");var t=L.get(e);return t?(debugAssert(t instanceof e,"Instance stored in cache mismatched with class"),t):(t=new e,L.set(e,t),t)}var M=function(){function InMemoryPersistence(){this.type="NONE",this.storage={}}return InMemoryPersistence.prototype._isAvailable=function(){return __awaiter(this,void 0,void 0,(function(){return __generator(this,(function(e){return[2,!0]}))}))},InMemoryPersistence.prototype._set=function(e,t){return __awaiter(this,void 0,void 0,(function(){return __generator(this,(function(r){return this.storage[e]=t,[2]}))}))},InMemoryPersistence.prototype._get=function(e){return __awaiter(this,void 0,void 0,(function(){var t;return __generator(this,(function(r){return[2,void 0===(t=this.storage[e])?null:t]}))}))},InMemoryPersistence.prototype._remove=function(e){return __awaiter(this,void 0,void 0,(function(){return __generator(this,(function(t){return delete this.storage[e],[2]}))}))},InMemoryPersistence.prototype._addListener=function(e,t){},InMemoryPersistence.prototype._removeListener=function(e,t){},InMemoryPersistence.type="NONE",InMemoryPersistence}();function _persistenceKeyName(e,t,r){return"".concat("firebase",":").concat(e,":").concat(t,":").concat(r)}var D=function(){function PersistenceUserManager(e,t,r){this.persistence=e,this.auth=t,this.userKey=r;var n=this.auth,i=n.config,o=n.name;this.fullUserKey=_persistenceKeyName(this.userKey,i.apiKey,o),this.fullPersistenceKey=_persistenceKeyName("persistence",i.apiKey,o),this.boundEventHandler=t._onStorageEvent.bind(t),this.persistence._addListener(this.fullUserKey,this.boundEventHandler)}return PersistenceUserManager.prototype.setCurrentUser=function(e){return this.persistence._set(this.fullUserKey,e.toJSON())},PersistenceUserManager.prototype.getCurrentUser=function(){return __awaiter(this,void 0,void 0,(function(){var e;return __generator(this,(function(t){switch(t.label){case 0:return[4,this.persistence._get(this.fullUserKey)];case 1:return[2,(e=t.sent())?N._fromJSON(this.auth,e):null]}}))}))},PersistenceUserManager.prototype.removeCurrentUser=function(){return this.persistence._remove(this.fullUserKey)},PersistenceUserManager.prototype.savePersistenceForRedirect=function(){return this.persistence._set(this.fullPersistenceKey,this.persistence.type)},PersistenceUserManager.prototype.setPersistence=function(e){return __awaiter(this,void 0,void 0,(function(){var t;return __generator(this,(function(r){switch(r.label){case 0:return this.persistence===e?[2]:[4,this.getCurrentUser()];case 1:return t=r.sent(),[4,this.removeCurrentUser()];case 2:return r.sent(),this.persistence=e,t?[2,this.setCurrentUser(t)]:[2]}}))}))},PersistenceUserManager.prototype.delete=function(){this.persistence._removeListener(this.fullUserKey,this.boundEventHandler)},PersistenceUserManager.create=function(e,t,r){return void 0===r&&(r="authUser"),__awaiter(this,void 0,void 0,(function(){var n,i,o,s,a,u,c,d,l,h,p=this;return __generator(this,(function(f){switch(f.label){case 0:return t.length?[4,Promise.all(t.map((function(e){return __awaiter(p,void 0,void 0,(function(){return __generator(this,(function(t){switch(t.label){case 0:return[4,e._isAvailable()];case 1:return t.sent()?[2,e]:[2,void 0]}}))}))})))]:[2,new PersistenceUserManager(_getInstance(M),e,r)];case 1:n=f.sent().filter((function(e){return e})),i=n[0]||_getInstance(M),o=_persistenceKeyName(r,e.config.apiKey,e.name),s=null,a=0,u=t,f.label=2;case 2:if(!(a<u.length))return[3,7];c=u[a],f.label=3;case 3:return f.trys.push([3,5,,6]),[4,c._get(o)];case 4:return(d=f.sent())?(l=N._fromJSON(e,d),c!==i&&(s=l),i=c,[3,7]):[3,6];case 5:return f.sent(),[3,6];case 6:return a++,[3,2];case 7:return h=n.filter((function(e){return e._shouldAllowMigration})),i._shouldAllowMigration&&h.length?(i=h[0],s?[4,i._set(o,s.toJSON())]:[3,9]):[2,new PersistenceUserManager(i,e,r)];case 8:f.sent(),f.label=9;case 9:return[4,Promise.all(t.map((function(e){return __awaiter(p,void 0,void 0,(function(){return __generator(this,(function(t){switch(t.label){case 0:if(e===i)return[3,4];t.label=1;case 1:return t.trys.push([1,3,,4]),[4,e._remove(o)];case 2:case 3:return t.sent(),[3,4];case 4:return[2]}}))}))})))];case 10:return f.sent(),[2,new PersistenceUserManager(i,e,r)]}}))}))},PersistenceUserManager}();function _getBrowserName(e){var t=e.toLowerCase();if(t.includes("opera/")||t.includes("opr/")||t.includes("opios/"))return"Opera";if(_isIEMobile(t))return"IEMobile";if(t.includes("msie")||t.includes("trident/"))return"IE";if(t.includes("edge/"))return"Edge";if(function _isFirefox(e){void 0===e&&(e=getUA());return/firefox\//i.test(e)}(t))return"Firefox";if(t.includes("silk/"))return"Silk";if(_isBlackBerry(t))return"Blackberry";if(_isWebOS(t))return"Webos";if(function _isSafari(e){void 0===e&&(e=getUA());var t=e.toLowerCase();return t.includes("safari/")&&!t.includes("chrome/")&&!t.includes("crios/")&&!t.includes("android")}(t))return"Safari";if((t.includes("chrome/")||function _isChromeIOS(e){void 0===e&&(e=getUA());return/crios\//i.test(e)}(t))&&!t.includes("edge/"))return"Chrome";if(_isAndroid(t))return"Android";var r=e.match(/([a-zA-Z\d\.]+)\/[a-zA-Z\d\.]*$/);return 2===(null==r?void 0:r.length)?r[1]:"Other"}function _isIEMobile(e){return void 0===e&&(e=getUA()),/iemobile/i.test(e)}function _isAndroid(e){return void 0===e&&(e=getUA()),/android/i.test(e)}function _isBlackBerry(e){return void 0===e&&(e=getUA()),/blackberry/i.test(e)}function _isWebOS(e){return void 0===e&&(e=getUA()),/webos/i.test(e)}function _isIOS(e){return void 0===e&&(e=getUA()),/iphone|ipad|ipod/i.test(e)||/macintosh/i.test(e)&&/mobile/i.test(e)}function _isIE10(){return function isIE(){const e=getUA();return e.indexOf("MSIE ")>=0||e.indexOf("Trident/")>=0}()&&10===document.documentMode}function _getClientVersion(e,r){var n;switch(void 0===r&&(r=[]),e){case"Browser":n=_getBrowserName(getUA());break;case"Worker":n="".concat(_getBrowserName(getUA()),"-").concat(e);break;default:n=e}var i=r.length?r.join(","):"FirebaseCore-web";return"".concat(n,"/").concat("JsCore","/").concat(t,"/").concat(i)}var U=function(){function AuthMiddlewareQueue(e){this.auth=e,this.queue=[]}return AuthMiddlewareQueue.prototype.pushCallback=function(e,t){var r=this,wrappedCallback=function(t){return new Promise((function(r,n){try{r(e(t))}catch(e){n(e)}}))};wrappedCallback.onAbort=t,this.queue.push(wrappedCallback);var n=this.queue.length-1;return function(){r.queue[n]=function(){return Promise.resolve()}}},AuthMiddlewareQueue.prototype.runMiddleware=function(e){return __awaiter(this,void 0,void 0,(function(){var t,r,n,i,o,s,a,u;return __generator(this,(function(c){switch(c.label){case 0:if(this.auth.currentUser===e)return[2];t=[],c.label=1;case 1:c.trys.push([1,6,,7]),r=0,n=this.queue,c.label=2;case 2:return r<n.length?[4,(i=n[r])(e)]:[3,5];case 3:c.sent(),i.onAbort&&t.push(i.onAbort),c.label=4;case 4:return r++,[3,2];case 5:return[3,7];case 6:for(o=c.sent(),t.reverse(),s=0,a=t;s<a.length;s++){u=a[s];try{u()}catch(e){}}throw this.auth._errorFactory.create("login-blocked",{originalMessage:null==o?void 0:o.message});case 7:return[2]}}))}))},AuthMiddlewareQueue}();function _getPasswordPolicy(e,t){return void 0===t&&(t={}),__awaiter(this,void 0,void 0,(function(){return __generator(this,(function(r){return[2,_performApiRequest(e,"GET","/v2/passwordPolicy",_addTidIfNecessary(e,t))]}))}))}var F=function(){function PasswordPolicyImpl(e){var t,r,n,i,o=e.customStrengthOptions;this.customStrengthOptions={},this.customStrengthOptions.minPasswordLength=null!==(t=o.minPasswordLength)&&void 0!==t?t:6,o.maxPasswordLength&&(this.customStrengthOptions.maxPasswordLength=o.maxPasswordLength),void 0!==o.containsLowercaseCharacter&&(this.customStrengthOptions.containsLowercaseLetter=o.containsLowercaseCharacter),void 0!==o.containsUppercaseCharacter&&(this.customStrengthOptions.containsUppercaseLetter=o.containsUppercaseCharacter),void 0!==o.containsNumericCharacter&&(this.customStrengthOptions.containsNumericCharacter=o.containsNumericCharacter),void 0!==o.containsNonAlphanumericCharacter&&(this.customStrengthOptions.containsNonAlphanumericCharacter=o.containsNonAlphanumericCharacter),this.enforcementState=e.enforcementState,"ENFORCEMENT_STATE_UNSPECIFIED"===this.enforcementState&&(this.enforcementState="OFF"),this.allowedNonAlphanumericCharacters=null!==(n=null===(r=e.allowedNonAlphanumericCharacters)||void 0===r?void 0:r.join(""))&&void 0!==n?n:"",this.forceUpgradeOnSignin=null!==(i=e.forceUpgradeOnSignin)&&void 0!==i&&i,this.schemaVersion=e.schemaVersion}return PasswordPolicyImpl.prototype.validatePassword=function(e){var t,r,n,i,o,s,a={isValid:!0,passwordPolicy:this};return this.validatePasswordLengthOptions(e,a),this.validatePasswordCharacterOptions(e,a),a.isValid&&(a.isValid=null===(t=a.meetsMinPasswordLength)||void 0===t||t),a.isValid&&(a.isValid=null===(r=a.meetsMaxPasswordLength)||void 0===r||r),a.isValid&&(a.isValid=null===(n=a.containsLowercaseLetter)||void 0===n||n),a.isValid&&(a.isValid=null===(i=a.containsUppercaseLetter)||void 0===i||i),a.isValid&&(a.isValid=null===(o=a.containsNumericCharacter)||void 0===o||o),a.isValid&&(a.isValid=null===(s=a.containsNonAlphanumericCharacter)||void 0===s||s),a},PasswordPolicyImpl.prototype.validatePasswordLengthOptions=function(e,t){var r=this.customStrengthOptions.minPasswordLength,n=this.customStrengthOptions.maxPasswordLength;r&&(t.meetsMinPasswordLength=e.length>=r),n&&(t.meetsMaxPasswordLength=e.length<=n)},PasswordPolicyImpl.prototype.validatePasswordCharacterOptions=function(e,t){var r;this.updatePasswordCharacterOptionsStatuses(t,!1,!1,!1,!1);for(var n=0;n<e.length;n++)r=e.charAt(n),this.updatePasswordCharacterOptionsStatuses(t,r>="a"&&r<="z",r>="A"&&r<="Z",r>="0"&&r<="9",this.allowedNonAlphanumericCharacters.includes(r))},PasswordPolicyImpl.prototype.updatePasswordCharacterOptionsStatuses=function(e,t,r,n,i){this.customStrengthOptions.containsLowercaseLetter&&(e.containsLowercaseLetter||(e.containsLowercaseLetter=t)),this.customStrengthOptions.containsUppercaseLetter&&(e.containsUppercaseLetter||(e.containsUppercaseLetter=r)),this.customStrengthOptions.containsNumericCharacter&&(e.containsNumericCharacter||(e.containsNumericCharacter=n)),this.customStrengthOptions.containsNonAlphanumericCharacter&&(e.containsNonAlphanumericCharacter||(e.containsNonAlphanumericCharacter=i))},PasswordPolicyImpl}(),V=function(){function AuthImpl(e,t,r,n){this.app=e,this.heartbeatServiceProvider=t,this.appCheckServiceProvider=r,this.config=n,this.currentUser=null,this.emulatorConfig=null,this.operations=Promise.resolve(),this.authStateSubscription=new x(this),this.idTokenSubscription=new x(this),this.beforeStateQueue=new U(this),this.redirectUser=null,this.isProactiveRefreshEnabled=!1,this.EXPECTED_PASSWORD_POLICY_SCHEMA_VERSION=1,this._canInitEmulator=!0,this._isInitialized=!1,this._deleted=!1,this._initializationPromise=null,this._popupRedirectResolver=null,this._errorFactory=I,this._agentRecaptchaConfig=null,this._tenantRecaptchaConfigs={},this._projectPasswordPolicy=null,this._tenantPasswordPolicies={},this.lastNotifiedUid=void 0,this.languageCode=null,this.tenantId=null,this.settings={appVerificationDisabledForTesting:!1},this.frameworks=[],this.name=e.name,this.clientVersion=n.sdkClientVersion}return AuthImpl.prototype._initializeWithPersistence=function(e,t){var r=this;return t&&(this._popupRedirectResolver=_getInstance(t)),this._initializationPromise=this.queue((function(){return __awaiter(r,void 0,void 0,(function(){var r,n,i;return __generator(this,(function(o){switch(o.label){case 0:return this._deleted?[2]:(r=this,[4,D.create(this,e)]);case 1:if(r.persistenceManager=o.sent(),this._deleted)return[2];if(!(null===(n=this._popupRedirectResolver)||void 0===n?void 0:n._shouldInitProactively))return[3,5];o.label=2;case 2:return o.trys.push([2,4,,5]),[4,this._popupRedirectResolver._initialize(this)];case 3:case 4:return o.sent(),[3,5];case 5:return[4,this.initializeCurrentUser(t)];case 6:return o.sent(),this.lastNotifiedUid=(null===(i=this.currentUser)||void 0===i?void 0:i.uid)||null,this._deleted?[2]:(this._isInitialized=!0,[2])}}))}))})),this._initializationPromise},AuthImpl.prototype._onStorageEvent=function(){return __awaiter(this,void 0,void 0,(function(){var e;return __generator(this,(function(t){switch(t.label){case 0:return this._deleted?[2]:[4,this.assertedPersistence.getCurrentUser()];case 1:return e=t.sent(),this.currentUser||e?this.currentUser&&e&&this.currentUser.uid===e.uid?(this._currentUser._assign(e),[4,this.currentUser.getIdToken()]):[3,3]:[2];case 2:case 4:return t.sent(),[2];case 3:return[4,this._updateCurrentUser(e,!0)]}}))}))},AuthImpl.prototype.initializeCurrentUserFromIdToken=function(e){return __awaiter(this,void 0,void 0,(function(){var t,r,n;return __generator(this,(function(i){switch(i.label){case 0:return i.trys.push([0,4,,6]),[4,getAccountInfo(this,{idToken:e})];case 1:return t=i.sent(),[4,N._fromGetAccountInfoResponse(this,t,e)];case 2:return r=i.sent(),[4,this.directlySetCurrentUser(r)];case 3:case 5:return i.sent(),[3,6];case 4:return n=i.sent(),console.warn("FirebaseServerApp could not login user with provided authIdToken: ",n),[4,this.directlySetCurrentUser(null)];case 6:return[2]}}))}))},AuthImpl.prototype.initializeCurrentUser=function(t){var r;return __awaiter(this,void 0,void 0,(function(){var n,i,o,s,a,u,c,d,l=this;return __generator(this,(function(h){switch(h.label){case 0:return e(this.app)?(n=this.app.settings.authIdToken)?[2,new Promise((function(e){setTimeout((function(){return l.initializeCurrentUserFromIdToken(n).then(e,e)}))}))]:[2,this.directlySetCurrentUser(null)]:[4,this.assertedPersistence.getCurrentUser()];case 1:return i=h.sent(),o=i,s=!1,t&&this.config.authDomain?[4,this.getOrInitRedirectPersistenceManager()]:[3,4];case 2:return h.sent(),a=null===(r=this.redirectUser)||void 0===r?void 0:r._redirectEventId,u=null==o?void 0:o._redirectEventId,[4,this.tryRedirectSignIn(t)];case 3:c=h.sent(),a&&a!==u||!(null==c?void 0:c.user)||(o=c.user,s=!0),h.label=4;case 4:if(!o)return[2,this.directlySetCurrentUser(null)];if(o._redirectEventId)return[3,9];if(!s)return[3,8];h.label=5;case 5:return h.trys.push([5,7,,8]),[4,this.beforeStateQueue.runMiddleware(o)];case 6:return h.sent(),[3,8];case 7:return d=h.sent(),o=i,this._popupRedirectResolver._overrideRedirectResult(this,(function(){return Promise.reject(d)})),[3,8];case 8:return o?[2,this.reloadAndSetCurrentUserOrClear(o)]:[2,this.directlySetCurrentUser(null)];case 9:return _assert(this._popupRedirectResolver,this,"argument-error"),[4,this.getOrInitRedirectPersistenceManager()];case 10:return h.sent(),this.redirectUser&&this.redirectUser._redirectEventId===o._redirectEventId?[2,this.directlySetCurrentUser(o)]:[2,this.reloadAndSetCurrentUserOrClear(o)]}}))}))},AuthImpl.prototype.tryRedirectSignIn=function(e){return __awaiter(this,void 0,void 0,(function(){var t;return __generator(this,(function(r){switch(r.label){case 0:t=null,r.label=1;case 1:return r.trys.push([1,3,,5]),[4,this._popupRedirectResolver._completeRedirectFn(this,e,!0)];case 2:return t=r.sent(),[3,5];case 3:return r.sent(),[4,this._setRedirectUser(null)];case 4:return r.sent(),[3,5];case 5:return[2,t]}}))}))},AuthImpl.prototype.reloadAndSetCurrentUserOrClear=function(e){return __awaiter(this,void 0,void 0,(function(){var t;return __generator(this,(function(r){switch(r.label){case 0:return r.trys.push([0,2,,3]),[4,_reloadWithoutSaving(e)];case 1:return r.sent(),[3,3];case 2:return(null==(t=r.sent())?void 0:t.code)!=="auth/".concat("network-request-failed")?[2,this.directlySetCurrentUser(null)]:[3,3];case 3:return[2,this.directlySetCurrentUser(e)]}}))}))},AuthImpl.prototype.useDeviceLanguage=function(){this.languageCode=function _getUserLanguage(){if("undefined"==typeof navigator)return null;var e=navigator;return e.languages&&e.languages[0]||e.language||null}()},AuthImpl.prototype._delete=function(){return __awaiter(this,void 0,void 0,(function(){return __generator(this,(function(e){return this._deleted=!0,[2]}))}))},AuthImpl.prototype.updateCurrentUser=function(t){return __awaiter(this,void 0,void 0,(function(){var r;return __generator(this,(function(n){return e(this.app)?[2,Promise.reject(_serverAppCurrentUserOperationNotSupportedError(this))]:((r=t?getModularInstance(t):null)&&_assert(r.auth.config.apiKey===this.config.apiKey,this,"invalid-user-token"),[2,this._updateCurrentUser(r&&r._clone(this))])}))}))},AuthImpl.prototype._updateCurrentUser=function(e,t){return void 0===t&&(t=!1),__awaiter(this,void 0,void 0,(function(){var r=this;return __generator(this,(function(n){switch(n.label){case 0:return this._deleted?[2]:(e&&_assert(this.tenantId===e.tenantId,this,"tenant-id-mismatch"),t?[3,2]:[4,this.beforeStateQueue.runMiddleware(e)]);case 1:n.sent(),n.label=2;case 2:return[2,this.queue((function(){return __awaiter(r,void 0,void 0,(function(){return __generator(this,(function(t){switch(t.label){case 0:return[4,this.directlySetCurrentUser(e)];case 1:return t.sent(),this.notifyAuthListeners(),[2]}}))}))}))]}}))}))},AuthImpl.prototype.signOut=function(){return __awaiter(this,void 0,void 0,(function(){return __generator(this,(function(t){switch(t.label){case 0:return e(this.app)?[2,Promise.reject(_serverAppCurrentUserOperationNotSupportedError(this))]:[4,this.beforeStateQueue.runMiddleware(null)];case 1:return t.sent(),this.redirectPersistenceManager||this._popupRedirectResolver?[4,this._setRedirectUser(null)]:[3,3];case 2:t.sent(),t.label=3;case 3:return[2,this._updateCurrentUser(null,!0)]}}))}))},AuthImpl.prototype.setPersistence=function(t){var r=this;return e(this.app)?Promise.reject(_serverAppCurrentUserOperationNotSupportedError(this)):this.queue((function(){return __awaiter(r,void 0,void 0,(function(){return __generator(this,(function(e){switch(e.label){case 0:return[4,this.assertedPersistence.setPersistence(_getInstance(t))];case 1:return e.sent(),[2]}}))}))}))},AuthImpl.prototype._getRecaptchaConfig=function(){return null==this.tenantId?this._agentRecaptchaConfig:this._tenantRecaptchaConfigs[this.tenantId]},AuthImpl.prototype.validatePassword=function(e){return __awaiter(this,void 0,void 0,(function(){var t;return __generator(this,(function(r){switch(r.label){case 0:return this._getPasswordPolicyInternal()?[3,2]:[4,this._updatePasswordPolicy()];case 1:r.sent(),r.label=2;case 2:return(t=this._getPasswordPolicyInternal()).schemaVersion!==this.EXPECTED_PASSWORD_POLICY_SCHEMA_VERSION?[2,Promise.reject(this._errorFactory.create("unsupported-password-policy-schema-version",{}))]:[2,t.validatePassword(e)]}}))}))},AuthImpl.prototype._getPasswordPolicyInternal=function(){return null===this.tenantId?this._projectPasswordPolicy:this._tenantPasswordPolicies[this.tenantId]},AuthImpl.prototype._updatePasswordPolicy=function(){return __awaiter(this,void 0,void 0,(function(){var e,t;return __generator(this,(function(r){switch(r.label){case 0:return[4,_getPasswordPolicy(this)];case 1:return e=r.sent(),t=new F(e),null===this.tenantId?this._projectPasswordPolicy=t:this._tenantPasswordPolicies[this.tenantId]=t,[2]}}))}))},AuthImpl.prototype._getPersistence=function(){return this.assertedPersistence.persistence.type},AuthImpl.prototype._updateErrorMap=function(e){this._errorFactory=new ErrorFactory("auth","Firebase",e())},AuthImpl.prototype.onAuthStateChanged=function(e,t,r){return this.registerStateListener(this.authStateSubscription,e,t,r)},AuthImpl.prototype.beforeAuthStateChanged=function(e,t){return this.beforeStateQueue.pushCallback(e,t)},AuthImpl.prototype.onIdTokenChanged=function(e,t,r){return this.registerStateListener(this.idTokenSubscription,e,t,r)},AuthImpl.prototype.authStateReady=function(){var e=this;return new Promise((function(t,r){if(e.currentUser)t();else var n=e.onAuthStateChanged((function(){n(),t()}),r)}))},AuthImpl.prototype.revokeAccessToken=function(e){return __awaiter(this,void 0,void 0,(function(){var t,r;return __generator(this,(function(n){switch(n.label){case 0:return this.currentUser?[4,this.currentUser.getIdToken()]:[3,3];case 1:return t=n.sent(),r={providerId:"apple.com",tokenType:"ACCESS_TOKEN",token:e,idToken:t},null!=this.tenantId&&(r.tenantId=this.tenantId),[4,revokeToken(this,r)];case 2:n.sent(),n.label=3;case 3:return[2]}}))}))},AuthImpl.prototype.toJSON=function(){var e;return{apiKey:this.config.apiKey,authDomain:this.config.authDomain,appName:this.name,currentUser:null===(e=this._currentUser)||void 0===e?void 0:e.toJSON()}},AuthImpl.prototype._setRedirectUser=function(e,t){return __awaiter(this,void 0,void 0,(function(){var r;return __generator(this,(function(n){switch(n.label){case 0:return[4,this.getOrInitRedirectPersistenceManager(t)];case 1:return r=n.sent(),[2,null===e?r.removeCurrentUser():r.setCurrentUser(e)]}}))}))},AuthImpl.prototype.getOrInitRedirectPersistenceManager=function(e){return __awaiter(this,void 0,void 0,(function(){var t,r,n;return __generator(this,(function(i){switch(i.label){case 0:return this.redirectPersistenceManager?[3,3]:(_assert(t=e&&_getInstance(e)||this._popupRedirectResolver,this,"argument-error"),r=this,[4,D.create(this,[_getInstance(t._redirectPersistence)],"redirectUser")]);case 1:return r.redirectPersistenceManager=i.sent(),n=this,[4,this.redirectPersistenceManager.getCurrentUser()];case 2:n.redirectUser=i.sent(),i.label=3;case 3:return[2,this.redirectPersistenceManager]}}))}))},AuthImpl.prototype._redirectUserForId=function(e){var t,r;return __awaiter(this,void 0,void 0,(function(){var n=this;return __generator(this,(function(i){switch(i.label){case 0:return this._isInitialized?[4,this.queue((function(){return __awaiter(n,void 0,void 0,(function(){return __generator(this,(function(e){return[2]}))}))}))]:[3,2];case 1:i.sent(),i.label=2;case 2:return(null===(t=this._currentUser)||void 0===t?void 0:t._redirectEventId)===e?[2,this._currentUser]:(null===(r=this.redirectUser)||void 0===r?void 0:r._redirectEventId)===e?[2,this.redirectUser]:[2,null]}}))}))},AuthImpl.prototype._persistUserIfCurrent=function(e){return __awaiter(this,void 0,void 0,(function(){var t=this;return __generator(this,(function(r){return e===this.currentUser?[2,this.queue((function(){return __awaiter(t,void 0,void 0,(function(){return __generator(this,(function(t){return[2,this.directlySetCurrentUser(e)]}))}))}))]:[2]}))}))},AuthImpl.prototype._notifyListenersIfCurrent=function(e){e===this.currentUser&&this.notifyAuthListeners()},AuthImpl.prototype._key=function(){return"".concat(this.config.authDomain,":").concat(this.config.apiKey,":").concat(this.name)},AuthImpl.prototype._startProactiveRefresh=function(){this.isProactiveRefreshEnabled=!0,this.currentUser&&this._currentUser._startProactiveRefresh()},AuthImpl.prototype._stopProactiveRefresh=function(){this.isProactiveRefreshEnabled=!1,this.currentUser&&this._currentUser._stopProactiveRefresh()},Object.defineProperty(AuthImpl.prototype,"_currentUser",{get:function(){return this.currentUser},enumerable:!1,configurable:!0}),AuthImpl.prototype.notifyAuthListeners=function(){var e,t;if(this._isInitialized){this.idTokenSubscription.next(this.currentUser);var r=null!==(t=null===(e=this.currentUser)||void 0===e?void 0:e.uid)&&void 0!==t?t:null;this.lastNotifiedUid!==r&&(this.lastNotifiedUid=r,this.authStateSubscription.next(this.currentUser))}},AuthImpl.prototype.registerStateListener=function(e,t,r,n){var i=this;if(this._deleted)return function(){};var o="function"==typeof t?t:t.next.bind(t),s=!1,a=this._isInitialized?Promise.resolve():this._initializationPromise;if(_assert(a,this,"internal-error"),a.then((function(){s||o(i.currentUser)})),"function"==typeof t){var u=e.addObserver(t,r,n);return function(){s=!0,u()}}var c=e.addObserver(t);return function(){s=!0,c()}},AuthImpl.prototype.directlySetCurrentUser=function(e){return __awaiter(this,void 0,void 0,(function(){return __generator(this,(function(t){switch(t.label){case 0:return this.currentUser&&this.currentUser!==e&&this._currentUser._stopProactiveRefresh(),e&&this.isProactiveRefreshEnabled&&e._startProactiveRefresh(),this.currentUser=e,e?[4,this.assertedPersistence.setCurrentUser(e)]:[3,2];case 1:return t.sent(),[3,4];case 2:return[4,this.assertedPersistence.removeCurrentUser()];case 3:t.sent(),t.label=4;case 4:return[2]}}))}))},AuthImpl.prototype.queue=function(e){return this.operations=this.operations.then(e,e),this.operations},Object.defineProperty(AuthImpl.prototype,"assertedPersistence",{get:function(){return _assert(this.persistenceManager,this,"internal-error"),this.persistenceManager},enumerable:!1,configurable:!0}),AuthImpl.prototype._logFramework=function(e){e&&!this.frameworks.includes(e)&&(this.frameworks.push(e),this.frameworks.sort(),this.clientVersion=_getClientVersion(this.config.clientPlatform,this._getFrameworks()))},AuthImpl.prototype._getFrameworks=function(){return this.frameworks},AuthImpl.prototype._getAdditionalHeaders=function(){var e;return __awaiter(this,void 0,void 0,(function(){var t,r,n,i;return __generator(this,(function(o){switch(o.label){case 0:return(i={})["X-Client-Version"]=this.clientVersion,t=i,this.app.options.appId&&(t["X-Firebase-gmpid"]=this.app.options.appId),[4,null===(e=this.heartbeatServiceProvider.getImmediate({optional:!0}))||void 0===e?void 0:e.getHeartbeatsHeader()];case 1:return(r=o.sent())&&(t["X-Firebase-Client"]=r),[4,this._getAppCheckToken()];case 2:return(n=o.sent())&&(t["X-Firebase-AppCheck"]=n),[2,t]}}))}))},AuthImpl.prototype._getAppCheckToken=function(){var e;return __awaiter(this,void 0,void 0,(function(){var r;return __generator(this,(function(n){switch(n.label){case 0:return[4,null===(e=this.appCheckServiceProvider.getImmediate({optional:!0}))||void 0===e?void 0:e.getToken()];case 1:return(null==(r=n.sent())?void 0:r.error)&&function _logWarn(e){for(var r=[],n=1;n<arguments.length;n++)r[n-1]=arguments[n];y.logLevel<=a.WARN&&y.warn.apply(y,__spreadArray(["Auth (".concat(t,"): ").concat(e)],r,!1))}("Error while retrieving App Check token: ".concat(r.error)),[2,null==r?void 0:r.token]}}))}))},AuthImpl}();function _castAuth(e){return getModularInstance(e)}var x=function(){function Subscription(e){var t=this;this.auth=e,this.observer=null,this.addObserver=function createSubscribe(e,t){const r=new ObserverProxy(e,t);return r.subscribe.bind(r)}((function(e){return t.observer=e}))}return Object.defineProperty(Subscription.prototype,"next",{get:function(){return _assert(this.observer,this.auth,"internal-error"),this.observer.next.bind(this.observer)},enumerable:!1,configurable:!0}),Subscription}(),W={loadJS:function(){return __awaiter(this,void 0,void 0,(function(){return __generator(this,(function(e){throw new Error("Unable to load external scripts")}))}))},recaptchaV2Script:"",recaptchaEnterpriseScript:"",gapiScript:""};var B=function(){function RecaptchaEnterpriseVerifier(e){this.type="recaptcha-enterprise",this.auth=_castAuth(e)}return RecaptchaEnterpriseVerifier.prototype.verify=function(e,t){return void 0===e&&(e="verify"),void 0===t&&(t=!1),__awaiter(this,void 0,void 0,(function(){function retrieveRecaptchaToken(t,r,n){var i=window.grecaptcha;isEnterprise(i)?i.enterprise.ready((function(){i.enterprise.execute(t,{action:e}).then((function(e){r(e)})).catch((function(){r("NO_RECAPTCHA")}))})):n(Error("No reCAPTCHA enterprise script loaded."))}var r=this;return __generator(this,(function(e){return[2,new Promise((function(e,n){(function retrieveSiteKey(e){return __awaiter(this,void 0,void 0,(function(){var r=this;return __generator(this,(function(n){if(!t){if(null==e.tenantId&&null!=e._agentRecaptchaConfig)return[2,e._agentRecaptchaConfig.siteKey];if(null!=e.tenantId&&void 0!==e._tenantRecaptchaConfigs[e.tenantId])return[2,e._tenantRecaptchaConfigs[e.tenantId].siteKey]}return[2,new Promise((function(t,n){return __awaiter(r,void 0,void 0,(function(){return __generator(this,(function(r){return getRecaptchaConfig(e,{clientType:"CLIENT_TYPE_WEB",version:"RECAPTCHA_ENTERPRISE"}).then((function(r){if(void 0!==r.recaptchaKey){var i=new R(r);return null==e.tenantId?e._agentRecaptchaConfig=i:e._tenantRecaptchaConfigs[e.tenantId]=i,t(i.siteKey)}n(new Error("recaptcha Enterprise site key undefined"))})).catch((function(e){n(e)})),[2]}))}))}))]}))}))})(r.auth).then((function(r){if(!t&&isEnterprise(window.grecaptcha))retrieveRecaptchaToken(r,e,n);else{if("undefined"==typeof window)return void n(new Error("RecaptchaVerifier is only supported in browser"));var i=function _recaptchaEnterpriseScriptUrl(){return W.recaptchaEnterpriseScript}();0!==i.length&&(i+=r),function _loadJS(e){return W.loadJS(e)}(i).then((function(){retrieveRecaptchaToken(r,e,n)})).catch((function(e){n(e)}))}})).catch((function(e){n(e)}))}))]}))}))},RecaptchaEnterpriseVerifier}();function injectRecaptchaFields(e,t,r,n){return void 0===n&&(n=!1),__awaiter(this,void 0,void 0,(function(){var i,o,s;return __generator(this,(function(a){switch(a.label){case 0:i=new B(e),a.label=1;case 1:return a.trys.push([1,3,,5]),[4,i.verify(r)];case 2:return o=a.sent(),[3,5];case 3:return a.sent(),[4,i.verify(r,!0)];case 4:return o=a.sent(),[3,5];case 5:return s=__assign({},t),n?Object.assign(s,{captchaResp:o}):Object.assign(s,{captchaResponse:o}),Object.assign(s,{clientType:"CLIENT_TYPE_WEB"}),Object.assign(s,{recaptchaVersion:"RECAPTCHA_ENTERPRISE"}),[2,s]}}))}))}function handleRecaptchaFlow(e,t,r,n){var i;return __awaiter(this,void 0,void 0,(function(){var o,s=this;return __generator(this,(function(a){switch(a.label){case 0:return(null===(i=e._getRecaptchaConfig())||void 0===i?void 0:i.isProviderEnabled("EMAIL_PASSWORD_PROVIDER"))?[4,injectRecaptchaFields(e,t,r,"getOobCode"===r)]:[3,2];case 1:return o=a.sent(),[2,n(e,o)];case 2:return[2,n(e,t).catch((function(i){return __awaiter(s,void 0,void 0,(function(){var o;return __generator(this,(function(s){switch(s.label){case 0:return i.code!=="auth/".concat("missing-recaptcha-token")?[3,2]:(console.log("".concat(r," is protected by reCAPTCHA Enterprise for this project. Automatically triggering the reCAPTCHA flow and restarting the flow.")),[4,injectRecaptchaFields(e,t,r,"getOobCode"===r)]);case 1:return o=s.sent(),[2,n(e,o)];case 2:return[2,Promise.reject(i)]}}))}))}))]}}))}))}function initializeAuth(e,t){var r=_getProvider(e,"auth");if(r.isInitialized()){var n=r.getImmediate();if(deepEqual(r.getOptions(),null!=t?t:{}))return n;_fail(n,"already-initialized")}return r.initialize({options:t})}function connectAuthEmulator(e,t,r){var n=_castAuth(e);_assert(n._canInitEmulator,n,"emulator-config-failed"),_assert(/^https?:\/\//.test(t),n,"invalid-emulator-scheme");var i=!!(null==r?void 0:r.disableWarnings),o=extractProtocol(t),s=function extractHostAndPort(e){var t=extractProtocol(e),r=/(\/\/)?([^?#/]+)/.exec(e.substr(t.length));if(!r)return{host:"",port:null};var n=r[2].split("@").pop()||"",i=/^(\[[^\]]+\])(:|$)/.exec(n);if(i)return{host:o=i[1],port:parsePort(n.substr(o.length+1))};var o,s=n.split(":");return{host:o=s[0],port:parsePort(s[1])}}(t),a=s.host,u=s.port,c=null===u?"":":".concat(u);n.config.emulator={url:"".concat(o,"//").concat(a).concat(c,"/")},n.settings.appVerificationDisabledForTesting=!0,n.emulatorConfig=Object.freeze({host:a,port:u,protocol:o.replace(":",""),options:Object.freeze({disableWarnings:i})}),i||function emitEmulatorWarning(){function attachBanner(){var e=document.createElement("p"),t=e.style;e.innerText="Running in emulator mode. Do not use with production credentials.",t.position="fixed",t.width="100%",t.backgroundColor="#ffffff",t.border=".1em solid #000000",t.color="#b50000",t.bottom="0px",t.left="0px",t.margin="0px",t.zIndex="10000",t.textAlign="center",e.classList.add("firebase-emulator-warning"),document.body.appendChild(e)}"undefined"!=typeof console&&"function"==typeof console.info&&console.info("WARNING: You are using the Auth Emulator, which is intended for local testing only.  Do not use with production credentials.");"undefined"!=typeof window&&"undefined"!=typeof document&&("loading"===document.readyState?window.addEventListener("DOMContentLoaded",attachBanner):attachBanner())}()}function extractProtocol(e){var t=e.indexOf(":");return t<0?"":e.substr(0,t+1)}function parsePort(e){if(!e)return null;var t=Number(e);return isNaN(t)?null:t}var H=function(){function AuthCredential(e,t){this.providerId=e,this.signInMethod=t}return AuthCredential.prototype.toJSON=function(){return debugFail("not implemented")},AuthCredential.prototype._getIdTokenResponse=function(e){return debugFail("not implemented")},AuthCredential.prototype._linkToIdToken=function(e,t){return debugFail("not implemented")},AuthCredential.prototype._getReauthenticationResolver=function(e){return debugFail("not implemented")},AuthCredential}();function resetPassword(e,t){return __awaiter(this,void 0,void 0,(function(){return __generator(this,(function(r){return[2,_performApiRequest(e,"POST","/v1/accounts:resetPassword",_addTidIfNecessary(e,t))]}))}))}function updateEmailPassword(e,t){return __awaiter(this,void 0,void 0,(function(){return __generator(this,(function(r){return[2,_performApiRequest(e,"POST","/v1/accounts:update",t)]}))}))}function linkEmailPassword(e,t){return __awaiter(this,void 0,void 0,(function(){return __generator(this,(function(r){return[2,_performApiRequest(e,"POST","/v1/accounts:signUp",t)]}))}))}function applyActionCode$1(e,t){return __awaiter(this,void 0,void 0,(function(){return __generator(this,(function(r){return[2,_performApiRequest(e,"POST","/v1/accounts:update",_addTidIfNecessary(e,t))]}))}))}function signInWithPassword(e,t){return __awaiter(this,void 0,void 0,(function(){return __generator(this,(function(r){return[2,_performSignInRequest(e,"POST","/v1/accounts:signInWithPassword",_addTidIfNecessary(e,t))]}))}))}function sendOobCode(e,t){return __awaiter(this,void 0,void 0,(function(){return __generator(this,(function(r){return[2,_performApiRequest(e,"POST","/v1/accounts:sendOobCode",_addTidIfNecessary(e,t))]}))}))}function sendEmailVerification$1(e,t){return __awaiter(this,void 0,void 0,(function(){return __generator(this,(function(r){return[2,sendOobCode(e,t)]}))}))}function sendPasswordResetEmail$1(e,t){return __awaiter(this,void 0,void 0,(function(){return __generator(this,(function(r){return[2,sendOobCode(e,t)]}))}))}function sendSignInLinkToEmail$1(e,t){return __awaiter(this,void 0,void 0,(function(){return __generator(this,(function(r){return[2,sendOobCode(e,t)]}))}))}function verifyAndChangeEmail(e,t){return __awaiter(this,void 0,void 0,(function(){return __generator(this,(function(r){return[2,sendOobCode(e,t)]}))}))}function signInWithEmailLink$1(e,t){return __awaiter(this,void 0,void 0,(function(){return __generator(this,(function(r){return[2,_performSignInRequest(e,"POST","/v1/accounts:signInWithEmailLink",_addTidIfNecessary(e,t))]}))}))}function signInWithEmailLinkForLinking(e,t){return __awaiter(this,void 0,void 0,(function(){return __generator(this,(function(r){return[2,_performSignInRequest(e,"POST","/v1/accounts:signInWithEmailLink",_addTidIfNecessary(e,t))]}))}))}var q=function(e){function EmailAuthCredential(t,r,n,i){void 0===i&&(i=null);var o=e.call(this,"password",n)||this;return o._email=t,o._password=r,o._tenantId=i,o}return __extends(EmailAuthCredential,e),EmailAuthCredential._fromEmailAndPassword=function(e,t){return new EmailAuthCredential(e,t,"password")},EmailAuthCredential._fromEmailAndCode=function(e,t,r){return void 0===r&&(r=null),new EmailAuthCredential(e,t,"emailLink",r)},EmailAuthCredential.prototype.toJSON=function(){return{email:this._email,password:this._password,signInMethod:this.signInMethod,tenantId:this._tenantId}},EmailAuthCredential.fromJSON=function(e){var t="string"==typeof e?JSON.parse(e):e;if((null==t?void 0:t.email)&&(null==t?void 0:t.password)){if("password"===t.signInMethod)return this._fromEmailAndPassword(t.email,t.password);if("emailLink"===t.signInMethod)return this._fromEmailAndCode(t.email,t.password,t.tenantId)}return null},EmailAuthCredential.prototype._getIdTokenResponse=function(e){return __awaiter(this,void 0,void 0,(function(){var t;return __generator(this,(function(r){switch(this.signInMethod){case"password":return t={returnSecureToken:!0,email:this._email,password:this._password,clientType:"CLIENT_TYPE_WEB"},[2,handleRecaptchaFlow(e,t,"signInWithPassword",signInWithPassword)];case"emailLink":return[2,signInWithEmailLink$1(e,{email:this._email,oobCode:this._password})];default:_fail(e,"internal-error")}return[2]}))}))},EmailAuthCredential.prototype._linkToIdToken=function(e,t){return __awaiter(this,void 0,void 0,(function(){var r;return __generator(this,(function(n){switch(this.signInMethod){case"password":return r={idToken:t,returnSecureToken:!0,email:this._email,password:this._password,clientType:"CLIENT_TYPE_WEB"},[2,handleRecaptchaFlow(e,r,"signUpPassword",linkEmailPassword)];case"emailLink":return[2,signInWithEmailLinkForLinking(e,{idToken:t,email:this._email,oobCode:this._password})];default:_fail(e,"internal-error")}return[2]}))}))},EmailAuthCredential.prototype._getReauthenticationResolver=function(e){return this._getIdTokenResponse(e)},EmailAuthCredential}(H);function signInWithIdp(e,t){return __awaiter(this,void 0,void 0,(function(){return __generator(this,(function(r){return[2,_performSignInRequest(e,"POST","/v1/accounts:signInWithIdp",_addTidIfNecessary(e,t))]}))}))}var j,G=function(e){function OAuthCredential(){var t=null!==e&&e.apply(this,arguments)||this;return t.pendingToken=null,t}return __extends(OAuthCredential,e),OAuthCredential._fromParams=function(e){var t=new OAuthCredential(e.providerId,e.signInMethod);return e.idToken||e.accessToken?(e.idToken&&(t.idToken=e.idToken),e.accessToken&&(t.accessToken=e.accessToken),e.nonce&&!e.pendingToken&&(t.nonce=e.nonce),e.pendingToken&&(t.pendingToken=e.pendingToken)):e.oauthToken&&e.oauthTokenSecret?(t.accessToken=e.oauthToken,t.secret=e.oauthTokenSecret):_fail("argument-error"),t},OAuthCredential.prototype.toJSON=function(){return{idToken:this.idToken,accessToken:this.accessToken,secret:this.secret,nonce:this.nonce,pendingToken:this.pendingToken,providerId:this.providerId,signInMethod:this.signInMethod}},OAuthCredential.fromJSON=function(e){var t="string"==typeof e?JSON.parse(e):e,r=t.providerId,n=t.signInMethod,i=__rest(t,["providerId","signInMethod"]);if(!r||!n)return null;var o=new OAuthCredential(r,n);return o.idToken=i.idToken||void 0,o.accessToken=i.accessToken||void 0,o.secret=i.secret,o.nonce=i.nonce,o.pendingToken=i.pendingToken||null,o},OAuthCredential.prototype._getIdTokenResponse=function(e){return signInWithIdp(e,this.buildRequest())},OAuthCredential.prototype._linkToIdToken=function(e,t){var r=this.buildRequest();return r.idToken=t,signInWithIdp(e,r)},OAuthCredential.prototype._getReauthenticationResolver=function(e){var t=this.buildRequest();return t.autoCreate=!1,signInWithIdp(e,t)},OAuthCredential.prototype.buildRequest=function(){var e={requestUri:"http://localhost",returnSecureToken:!0};if(this.pendingToken)e.pendingToken=this.pendingToken;else{var t={};this.idToken&&(t.id_token=this.idToken),this.accessToken&&(t.access_token=this.accessToken),this.secret&&(t.oauth_token_secret=this.secret),t.providerId=this.providerId,this.nonce&&!this.pendingToken&&(t.nonce=this.nonce),e.postBody=querystring(t)}return e},OAuthCredential}(H);var z=((j={}).USER_NOT_FOUND="user-not-found",j);var K=function(e){function PhoneAuthCredential(t){var r=e.call(this,"phone","phone")||this;return r.params=t,r}return __extends(PhoneAuthCredential,e),PhoneAuthCredential._fromVerification=function(e,t){return new PhoneAuthCredential({verificationId:e,verificationCode:t})},PhoneAuthCredential._fromTokenResponse=function(e,t){return new PhoneAuthCredential({phoneNumber:e,temporaryProof:t})},PhoneAuthCredential.prototype._getIdTokenResponse=function(e){return function signInWithPhoneNumber(e,t){return __awaiter(this,void 0,void 0,(function(){return __generator(this,(function(r){return[2,_performSignInRequest(e,"POST","/v1/accounts:signInWithPhoneNumber",_addTidIfNecessary(e,t))]}))}))}(e,this._makeVerificationRequest())},PhoneAuthCredential.prototype._linkToIdToken=function(e,t){return function linkWithPhoneNumber(e,t){return __awaiter(this,void 0,void 0,(function(){var r;return __generator(this,(function(n){switch(n.label){case 0:return[4,_performSignInRequest(e,"POST","/v1/accounts:signInWithPhoneNumber",_addTidIfNecessary(e,t))];case 1:if((r=n.sent()).temporaryProof)throw _makeTaggedError(e,"account-exists-with-different-credential",r);return[2,r]}}))}))}(e,__assign({idToken:t},this._makeVerificationRequest()))},PhoneAuthCredential.prototype._getReauthenticationResolver=function(e){return function verifyPhoneNumberForExisting(e,t){return __awaiter(this,void 0,void 0,(function(){var r;return __generator(this,(function(n){return r=__assign(__assign({},t),{operation:"REAUTH"}),[2,_performSignInRequest(e,"POST","/v1/accounts:signInWithPhoneNumber",_addTidIfNecessary(e,r),z)]}))}))}(e,this._makeVerificationRequest())},PhoneAuthCredential.prototype._makeVerificationRequest=function(){var e=this.params,t=e.temporaryProof,r=e.phoneNumber,n=e.verificationId,i=e.verificationCode;return t&&r?{temporaryProof:t,phoneNumber:r}:{sessionInfo:n,code:i}},PhoneAuthCredential.prototype.toJSON=function(){var e={providerId:this.providerId};return this.params.phoneNumber&&(e.phoneNumber=this.params.phoneNumber),this.params.temporaryProof&&(e.temporaryProof=this.params.temporaryProof),this.params.verificationCode&&(e.verificationCode=this.params.verificationCode),this.params.verificationId&&(e.verificationId=this.params.verificationId),e},PhoneAuthCredential.fromJSON=function(e){"string"==typeof e&&(e=JSON.parse(e));var t=e,r=t.verificationId,n=t.verificationCode,i=t.phoneNumber,o=t.temporaryProof;return n||r||i||o?new PhoneAuthCredential({verificationId:r,verificationCode:n,phoneNumber:i,temporaryProof:o}):null},PhoneAuthCredential}(H);var J=function(){function ActionCodeURL(e){var t,r,n,i,o,s,a=querystringDecode(extractQuerystring(e)),u=null!==(t=a.apiKey)&&void 0!==t?t:null,c=null!==(r=a.oobCode)&&void 0!==r?r:null,d=function parseMode(e){switch(e){case"recoverEmail":return"RECOVER_EMAIL";case"resetPassword":return"PASSWORD_RESET";case"signIn":return"EMAIL_SIGNIN";case"verifyEmail":return"VERIFY_EMAIL";case"verifyAndChangeEmail":return"VERIFY_AND_CHANGE_EMAIL";case"revertSecondFactorAddition":return"REVERT_SECOND_FACTOR_ADDITION";default:return null}}(null!==(n=a.mode)&&void 0!==n?n:null);_assert(u&&c&&d,"argument-error"),this.apiKey=u,this.operation=d,this.code=c,this.continueUrl=null!==(i=a.continueUrl)&&void 0!==i?i:null,this.languageCode=null!==(o=a.languageCode)&&void 0!==o?o:null,this.tenantId=null!==(s=a.tenantId)&&void 0!==s?s:null}return ActionCodeURL.parseLink=function(e){var t=function parseDeepLink(e){var t=querystringDecode(extractQuerystring(e)).link,r=t?querystringDecode(extractQuerystring(t)).deep_link_id:null,n=querystringDecode(extractQuerystring(e)).deep_link_id;return(n?querystringDecode(extractQuerystring(n)).link:null)||n||r||t||e}(e);try{return new ActionCodeURL(t)}catch(e){return null}},ActionCodeURL}();function parseActionCodeURL(e){return J.parseLink(e)}var Y=function(){function EmailAuthProvider(){this.providerId=EmailAuthProvider.PROVIDER_ID}return EmailAuthProvider.credential=function(e,t){return q._fromEmailAndPassword(e,t)},EmailAuthProvider.credentialWithLink=function(e,t){var r=J.parseLink(t);return _assert(r,"argument-error"),q._fromEmailAndCode(e,r.code,r.tenantId)},EmailAuthProvider.PROVIDER_ID="password",EmailAuthProvider.EMAIL_PASSWORD_SIGN_IN_METHOD="password",EmailAuthProvider.EMAIL_LINK_SIGN_IN_METHOD="emailLink",EmailAuthProvider}(),$=function(){function FederatedAuthProvider(e){this.providerId=e,this.defaultLanguageCode=null,this.customParameters={}}return FederatedAuthProvider.prototype.setDefaultLanguage=function(e){this.defaultLanguageCode=e},FederatedAuthProvider.prototype.setCustomParameters=function(e){return this.customParameters=e,this},FederatedAuthProvider.prototype.getCustomParameters=function(){return this.customParameters},FederatedAuthProvider}(),X=function(e){function BaseOAuthProvider(){var t=null!==e&&e.apply(this,arguments)||this;return t.scopes=[],t}return __extends(BaseOAuthProvider,e),BaseOAuthProvider.prototype.addScope=function(e){return this.scopes.includes(e)||this.scopes.push(e),this},BaseOAuthProvider.prototype.getScopes=function(){return __spreadArray([],this.scopes,!0)},BaseOAuthProvider}($),Q=function(e){function OAuthProvider(){return null!==e&&e.apply(this,arguments)||this}return __extends(OAuthProvider,e),OAuthProvider.credentialFromJSON=function(e){var t="string"==typeof e?JSON.parse(e):e;return _assert("providerId"in t&&"signInMethod"in t,"argument-error"),G._fromParams(t)},OAuthProvider.prototype.credential=function(e){return this._credential(__assign(__assign({},e),{nonce:e.rawNonce}))},OAuthProvider.prototype._credential=function(e){return _assert(e.idToken||e.accessToken,"argument-error"),G._fromParams(__assign(__assign({},e),{providerId:this.providerId,signInMethod:this.providerId}))},OAuthProvider.credentialFromResult=function(e){return OAuthProvider.oauthCredentialFromTaggedObject(e)},OAuthProvider.credentialFromError=function(e){return OAuthProvider.oauthCredentialFromTaggedObject(e.customData||{})},OAuthProvider.oauthCredentialFromTaggedObject=function(e){var t=e._tokenResponse;if(!t)return null;var r=t,n=r.oauthIdToken,i=r.oauthAccessToken,o=r.oauthTokenSecret,s=r.pendingToken,a=r.nonce,u=r.providerId;if(!(i||o||n||s))return null;if(!u)return null;try{return new OAuthProvider(u)._credential({idToken:n,accessToken:i,nonce:a,pendingToken:s})}catch(e){return null}},OAuthProvider}(X),Z=function(e){function FacebookAuthProvider(){return e.call(this,"facebook.com")||this}return __extends(FacebookAuthProvider,e),FacebookAuthProvider.credential=function(e){return G._fromParams({providerId:FacebookAuthProvider.PROVIDER_ID,signInMethod:FacebookAuthProvider.FACEBOOK_SIGN_IN_METHOD,accessToken:e})},FacebookAuthProvider.credentialFromResult=function(e){return FacebookAuthProvider.credentialFromTaggedObject(e)},FacebookAuthProvider.credentialFromError=function(e){return FacebookAuthProvider.credentialFromTaggedObject(e.customData||{})},FacebookAuthProvider.credentialFromTaggedObject=function(e){var t=e._tokenResponse;if(!t||!("oauthAccessToken"in t))return null;if(!t.oauthAccessToken)return null;try{return FacebookAuthProvider.credential(t.oauthAccessToken)}catch(e){return null}},FacebookAuthProvider.FACEBOOK_SIGN_IN_METHOD="facebook.com",FacebookAuthProvider.PROVIDER_ID="facebook.com",FacebookAuthProvider}(X),ee=function(e){function GoogleAuthProvider(){var t=e.call(this,"google.com")||this;return t.addScope("profile"),t}return __extends(GoogleAuthProvider,e),GoogleAuthProvider.credential=function(e,t){return G._fromParams({providerId:GoogleAuthProvider.PROVIDER_ID,signInMethod:GoogleAuthProvider.GOOGLE_SIGN_IN_METHOD,idToken:e,accessToken:t})},GoogleAuthProvider.credentialFromResult=function(e){return GoogleAuthProvider.credentialFromTaggedObject(e)},GoogleAuthProvider.credentialFromError=function(e){return GoogleAuthProvider.credentialFromTaggedObject(e.customData||{})},GoogleAuthProvider.credentialFromTaggedObject=function(e){var t=e._tokenResponse;if(!t)return null;var r=t,n=r.oauthIdToken,i=r.oauthAccessToken;if(!n&&!i)return null;try{return GoogleAuthProvider.credential(n,i)}catch(e){return null}},GoogleAuthProvider.GOOGLE_SIGN_IN_METHOD="google.com",GoogleAuthProvider.PROVIDER_ID="google.com",GoogleAuthProvider}(X),te=function(e){function GithubAuthProvider(){return e.call(this,"github.com")||this}return __extends(GithubAuthProvider,e),GithubAuthProvider.credential=function(e){return G._fromParams({providerId:GithubAuthProvider.PROVIDER_ID,signInMethod:GithubAuthProvider.GITHUB_SIGN_IN_METHOD,accessToken:e})},GithubAuthProvider.credentialFromResult=function(e){return GithubAuthProvider.credentialFromTaggedObject(e)},GithubAuthProvider.credentialFromError=function(e){return GithubAuthProvider.credentialFromTaggedObject(e.customData||{})},GithubAuthProvider.credentialFromTaggedObject=function(e){var t=e._tokenResponse;if(!t||!("oauthAccessToken"in t))return null;if(!t.oauthAccessToken)return null;try{return GithubAuthProvider.credential(t.oauthAccessToken)}catch(e){return null}},GithubAuthProvider.GITHUB_SIGN_IN_METHOD="github.com",GithubAuthProvider.PROVIDER_ID="github.com",GithubAuthProvider}(X),re=function(e){function SAMLAuthCredential(t,r){var n=e.call(this,t,t)||this;return n.pendingToken=r,n}return __extends(SAMLAuthCredential,e),SAMLAuthCredential.prototype._getIdTokenResponse=function(e){return signInWithIdp(e,this.buildRequest())},SAMLAuthCredential.prototype._linkToIdToken=function(e,t){var r=this.buildRequest();return r.idToken=t,signInWithIdp(e,r)},SAMLAuthCredential.prototype._getReauthenticationResolver=function(e){var t=this.buildRequest();return t.autoCreate=!1,signInWithIdp(e,t)},SAMLAuthCredential.prototype.toJSON=function(){return{signInMethod:this.signInMethod,providerId:this.providerId,pendingToken:this.pendingToken}},SAMLAuthCredential.fromJSON=function(e){var t="string"==typeof e?JSON.parse(e):e,r=t.providerId,n=t.signInMethod,i=t.pendingToken;return r&&n&&i&&r===n?new SAMLAuthCredential(r,i):null},SAMLAuthCredential._create=function(e,t){return new SAMLAuthCredential(e,t)},SAMLAuthCredential.prototype.buildRequest=function(){return{requestUri:"http://localhost",returnSecureToken:!0,pendingToken:this.pendingToken}},SAMLAuthCredential}(H),ne=function(e){function SAMLAuthProvider(t){return _assert(t.startsWith("saml."),"argument-error"),e.call(this,t)||this}return __extends(SAMLAuthProvider,e),SAMLAuthProvider.credentialFromResult=function(e){return SAMLAuthProvider.samlCredentialFromTaggedObject(e)},SAMLAuthProvider.credentialFromError=function(e){return SAMLAuthProvider.samlCredentialFromTaggedObject(e.customData||{})},SAMLAuthProvider.credentialFromJSON=function(e){var t=re.fromJSON(e);return _assert(t,"argument-error"),t},SAMLAuthProvider.samlCredentialFromTaggedObject=function(e){var t=e._tokenResponse;if(!t)return null;var r=t,n=r.pendingToken,i=r.providerId;if(!n||!i)return null;try{return re._create(i,n)}catch(e){return null}},SAMLAuthProvider}($),ie=function(e){function TwitterAuthProvider(){return e.call(this,"twitter.com")||this}return __extends(TwitterAuthProvider,e),TwitterAuthProvider.credential=function(e,t){return G._fromParams({providerId:TwitterAuthProvider.PROVIDER_ID,signInMethod:TwitterAuthProvider.TWITTER_SIGN_IN_METHOD,oauthToken:e,oauthTokenSecret:t})},TwitterAuthProvider.credentialFromResult=function(e){return TwitterAuthProvider.credentialFromTaggedObject(e)},TwitterAuthProvider.credentialFromError=function(e){return TwitterAuthProvider.credentialFromTaggedObject(e.customData||{})},TwitterAuthProvider.credentialFromTaggedObject=function(e){var t=e._tokenResponse;if(!t)return null;var r=t,n=r.oauthAccessToken,i=r.oauthTokenSecret;if(!n||!i)return null;try{return TwitterAuthProvider.credential(n,i)}catch(e){return null}},TwitterAuthProvider.TWITTER_SIGN_IN_METHOD="twitter.com",TwitterAuthProvider.PROVIDER_ID="twitter.com",TwitterAuthProvider}(X);function signUp(e,t){return __awaiter(this,void 0,void 0,(function(){return __generator(this,(function(r){return[2,_performSignInRequest(e,"POST","/v1/accounts:signUp",_addTidIfNecessary(e,t))]}))}))}var oe=function(){function UserCredentialImpl(e){this.user=e.user,this.providerId=e.providerId,this._tokenResponse=e._tokenResponse,this.operationType=e.operationType}return UserCredentialImpl._fromIdTokenResponse=function(e,t,r,n){return void 0===n&&(n=!1),__awaiter(this,void 0,void 0,(function(){var i,o;return __generator(this,(function(s){switch(s.label){case 0:return[4,N._fromIdTokenResponse(e,r,n)];case 1:return i=s.sent(),o=providerIdForResponse(r),[2,new UserCredentialImpl({user:i,providerId:o,_tokenResponse:r,operationType:t})]}}))}))},UserCredentialImpl._forOperation=function(e,t,r){return __awaiter(this,void 0,void 0,(function(){var n;return __generator(this,(function(i){switch(i.label){case 0:return[4,e._updateTokensIfNecessary(r,!0)];case 1:return i.sent(),n=providerIdForResponse(r),[2,new UserCredentialImpl({user:e,providerId:n,_tokenResponse:r,operationType:t})]}}))}))},UserCredentialImpl}();function providerIdForResponse(e){return e.providerId?e.providerId:"phoneNumber"in e?"phone":null}function signInAnonymously(t){var r;return __awaiter(this,void 0,void 0,(function(){var n,i,o;return __generator(this,(function(s){switch(s.label){case 0:return e(t.app)?[2,Promise.reject(_serverAppCurrentUserOperationNotSupportedError(t))]:[4,(n=_castAuth(t))._initializationPromise];case 1:return s.sent(),(null===(r=n.currentUser)||void 0===r?void 0:r.isAnonymous)?[2,new oe({user:n.currentUser,providerId:null,operationType:"signIn"})]:[4,signUp(n,{returnSecureToken:!0})];case 2:return i=s.sent(),[4,oe._fromIdTokenResponse(n,"signIn",i,!0)];case 3:return o=s.sent(),[4,n._updateCurrentUser(o.user)];case 4:return s.sent(),[2,o]}}))}))}var se=function(e){function MultiFactorError(t,r,n,i){var o,s=this;return(s=e.call(this,r.code,r.message)||this).operationType=n,s.user=i,Object.setPrototypeOf(s,MultiFactorError.prototype),s.customData={appName:t.name,tenantId:null!==(o=t.tenantId)&&void 0!==o?o:void 0,_serverResponse:r.customData._serverResponse,operationType:n},s}return __extends(MultiFactorError,e),MultiFactorError._fromErrorAndOperation=function(e,t,r,n){return new MultiFactorError(e,t,r,n)},MultiFactorError}(FirebaseError);function _processCredentialSavingMfaContextIfNecessary(e,t,r,n){return("reauthenticate"===t?r._getReauthenticationResolver(e):r._getIdTokenResponse(e)).catch((function(r){if(r.code==="auth/".concat("multi-factor-auth-required"))throw se._fromErrorAndOperation(e,r,t,n);throw r}))}function providerDataAsNames(e){return new Set(e.map((function(e){return e.providerId})).filter((function(e){return!!e})))}function unlink(e,t){return __awaiter(this,void 0,void 0,(function(){var r,n,i,o,s,a;return __generator(this,(function(u){switch(u.label){case 0:return[4,_assertLinkedStatus(!0,r=getModularInstance(e),t)];case 1:return u.sent(),i=deleteLinkedAccounts,o=[r.auth],a={},[4,r.getIdToken()];case 2:return[4,i.apply(void 0,o.concat([(a.idToken=u.sent(),a.deleteProvider=[t],a)]))];case 3:return n=u.sent().providerUserInfo,s=providerDataAsNames(n||[]),r.providerData=r.providerData.filter((function(e){return s.has(e.providerId)})),s.has("phone")||(r.phoneNumber=null),[4,r.auth._persistUserIfCurrent(r)];case 4:return u.sent(),[2,r]}}))}))}function _link$1(e,t,r){return void 0===r&&(r=!1),__awaiter(this,void 0,void 0,(function(){var n,i,o,s,a,u;return __generator(this,(function(c){switch(c.label){case 0:return i=_logoutIfInvalidated,o=[e],a=(s=t)._linkToIdToken,u=[e.auth],[4,e.getIdToken()];case 1:return[4,i.apply(void 0,o.concat([a.apply(s,u.concat([c.sent()])),r]))];case 2:return n=c.sent(),[2,oe._forOperation(e,"link",n)]}}))}))}function _assertLinkedStatus(e,t,r){return __awaiter(this,void 0,void 0,(function(){var n,i;return __generator(this,(function(o){switch(o.label){case 0:return[4,_reloadWithoutSaving(t)];case 1:return o.sent(),n=providerDataAsNames(t.providerData),i=!1===e?"provider-already-linked":"no-such-provider",_assert(n.has(r)===e,t.auth,i),[2]}}))}))}function _reauthenticate(t,r,n){return void 0===n&&(n=!1),__awaiter(this,void 0,void 0,(function(){var i,o,s,a,u,c;return __generator(this,(function(d){switch(d.label){case 0:if(i=t.auth,e(i.app))return[2,Promise.reject(_serverAppCurrentUserOperationNotSupportedError(i))];o="reauthenticate",d.label=1;case 1:return d.trys.push([1,3,,4]),[4,_logoutIfInvalidated(t,_processCredentialSavingMfaContextIfNecessary(i,o,r,t),n)];case 2:return _assert((s=d.sent()).idToken,i,"internal-error"),_assert(a=_parseToken(s.idToken),i,"internal-error"),u=a.sub,_assert(t.uid===u,i,"user-mismatch"),[2,oe._forOperation(t,o,s)];case 3:throw(null==(c=d.sent())?void 0:c.code)==="auth/".concat("user-not-found")&&_fail(i,"user-mismatch"),c;case 4:return[2]}}))}))}function _signInWithCredential(t,r,n){return void 0===n&&(n=!1),__awaiter(this,void 0,void 0,(function(){var i,o,s;return __generator(this,(function(a){switch(a.label){case 0:return e(t.app)?[2,Promise.reject(_serverAppCurrentUserOperationNotSupportedError(t))]:[4,_processCredentialSavingMfaContextIfNecessary(t,i="signIn",r)];case 1:return o=a.sent(),[4,oe._fromIdTokenResponse(t,i,o)];case 2:return s=a.sent(),n?[3,4]:[4,t._updateCurrentUser(s.user)];case 3:a.sent(),a.label=4;case 4:return[2,s]}}))}))}function signInWithCredential(e,t){return __awaiter(this,void 0,void 0,(function(){return __generator(this,(function(r){return[2,_signInWithCredential(_castAuth(e),t)]}))}))}function linkWithCredential(e,t){return __awaiter(this,void 0,void 0,(function(){var r;return __generator(this,(function(n){switch(n.label){case 0:return[4,_assertLinkedStatus(!1,r=getModularInstance(e),t.providerId)];case 1:return n.sent(),[2,_link$1(r,t)]}}))}))}function reauthenticateWithCredential(e,t){return __awaiter(this,void 0,void 0,(function(){return __generator(this,(function(r){return[2,_reauthenticate(getModularInstance(e),t)]}))}))}function signInWithCustomToken$1(e,t){return __awaiter(this,void 0,void 0,(function(){return __generator(this,(function(r){return[2,_performSignInRequest(e,"POST","/v1/accounts:signInWithCustomToken",_addTidIfNecessary(e,t))]}))}))}function signInWithCustomToken(t,r){return __awaiter(this,void 0,void 0,(function(){var n,i,o;return __generator(this,(function(s){switch(s.label){case 0:return e(t.app)?[2,Promise.reject(_serverAppCurrentUserOperationNotSupportedError(t))]:[4,signInWithCustomToken$1(n=_castAuth(t),{token:r,returnSecureToken:!0})];case 1:return i=s.sent(),[4,oe._fromIdTokenResponse(n,"signIn",i)];case 2:return o=s.sent(),[4,n._updateCurrentUser(o.user)];case 3:return s.sent(),[2,o]}}))}))}var ae=function(){function MultiFactorInfoImpl(e,t){this.factorId=e,this.uid=t.mfaEnrollmentId,this.enrollmentTime=new Date(t.enrolledAt).toUTCString(),this.displayName=t.displayName}return MultiFactorInfoImpl._fromServerResponse=function(e,t){return"phoneInfo"in t?ue._fromServerResponse(e,t):"totpInfo"in t?ce._fromServerResponse(e,t):_fail(e,"internal-error")},MultiFactorInfoImpl}(),ue=function(e){function PhoneMultiFactorInfoImpl(t){var r=e.call(this,"phone",t)||this;return r.phoneNumber=t.phoneInfo,r}return __extends(PhoneMultiFactorInfoImpl,e),PhoneMultiFactorInfoImpl._fromServerResponse=function(e,t){return new PhoneMultiFactorInfoImpl(t)},PhoneMultiFactorInfoImpl}(ae),ce=function(e){function TotpMultiFactorInfoImpl(t){return e.call(this,"totp",t)||this}return __extends(TotpMultiFactorInfoImpl,e),TotpMultiFactorInfoImpl._fromServerResponse=function(e,t){return new TotpMultiFactorInfoImpl(t)},TotpMultiFactorInfoImpl}(ae);function _setActionCodeSettingsOnRequest(e,t,r){var n;_assert((null===(n=r.url)||void 0===n?void 0:n.length)>0,e,"invalid-continue-uri"),_assert(void 0===r.dynamicLinkDomain||r.dynamicLinkDomain.length>0,e,"invalid-dynamic-link-domain"),t.continueUrl=r.url,t.dynamicLinkDomain=r.dynamicLinkDomain,t.canHandleCodeInApp=r.handleCodeInApp,r.iOS&&(_assert(r.iOS.bundleId.length>0,e,"missing-ios-bundle-id"),t.iOSBundleId=r.iOS.bundleId),r.android&&(_assert(r.android.packageName.length>0,e,"missing-android-pkg-name"),t.androidInstallApp=r.android.installApp,t.androidMinimumVersionCode=r.android.minimumVersion,t.androidPackageName=r.android.packageName)}function recachePasswordPolicy(e){return __awaiter(this,void 0,void 0,(function(){var t;return __generator(this,(function(r){switch(r.label){case 0:return(t=_castAuth(e))._getPasswordPolicyInternal()?[4,t._updatePasswordPolicy()]:[3,2];case 1:r.sent(),r.label=2;case 2:return[2]}}))}))}function sendPasswordResetEmail(e,t,r){return __awaiter(this,void 0,void 0,(function(){var n,i;return __generator(this,(function(o){switch(o.label){case 0:return n=_castAuth(e),i={requestType:"PASSWORD_RESET",email:t,clientType:"CLIENT_TYPE_WEB"},r&&_setActionCodeSettingsOnRequest(n,i,r),[4,handleRecaptchaFlow(n,i,"getOobCode",sendPasswordResetEmail$1)];case 1:return o.sent(),[2]}}))}))}function confirmPasswordReset(e,t,r){return __awaiter(this,void 0,void 0,(function(){var n=this;return __generator(this,(function(i){switch(i.label){case 0:return[4,resetPassword(getModularInstance(e),{oobCode:t,newPassword:r}).catch((function(t){return __awaiter(n,void 0,void 0,(function(){return __generator(this,(function(r){throw t.code==="auth/".concat("password-does-not-meet-requirements")&&recachePasswordPolicy(e),t}))}))}))];case 1:return i.sent(),[2]}}))}))}function applyActionCode(e,t){return __awaiter(this,void 0,void 0,(function(){return __generator(this,(function(r){switch(r.label){case 0:return[4,applyActionCode$1(getModularInstance(e),{oobCode:t})];case 1:return r.sent(),[2]}}))}))}function checkActionCode(e,t){return __awaiter(this,void 0,void 0,(function(){var r,n,i,o;return __generator(this,(function(s){switch(s.label){case 0:return[4,resetPassword(r=getModularInstance(e),{oobCode:t})];case 1:switch(n=s.sent(),_assert(i=n.requestType,r,"internal-error"),i){case"EMAIL_SIGNIN":break;case"VERIFY_AND_CHANGE_EMAIL":_assert(n.newEmail,r,"internal-error");break;case"REVERT_SECOND_FACTOR_ADDITION":_assert(n.mfaInfo,r,"internal-error");default:_assert(n.email,r,"internal-error")}return o=null,n.mfaInfo&&(o=ae._fromServerResponse(_castAuth(r),n.mfaInfo)),[2,{data:{email:("VERIFY_AND_CHANGE_EMAIL"===n.requestType?n.newEmail:n.email)||null,previousEmail:("VERIFY_AND_CHANGE_EMAIL"===n.requestType?n.email:n.newEmail)||null,multiFactorInfo:o},operation:i}]}}))}))}function verifyPasswordResetCode(e,t){return __awaiter(this,void 0,void 0,(function(){return __generator(this,(function(r){switch(r.label){case 0:return[4,checkActionCode(getModularInstance(e),t)];case 1:return[2,r.sent().data.email]}}))}))}function createUserWithEmailAndPassword(t,r,n){return __awaiter(this,void 0,void 0,(function(){var i,o,s;return __generator(this,(function(a){switch(a.label){case 0:return e(t.app)?[2,Promise.reject(_serverAppCurrentUserOperationNotSupportedError(t))]:(i=_castAuth(t),[4,handleRecaptchaFlow(i,{returnSecureToken:!0,email:r,password:n,clientType:"CLIENT_TYPE_WEB"},"signUpPassword",signUp).catch((function(e){throw e.code==="auth/".concat("password-does-not-meet-requirements")&&recachePasswordPolicy(t),e}))]);case 1:return o=a.sent(),[4,oe._fromIdTokenResponse(i,"signIn",o)];case 2:return s=a.sent(),[4,i._updateCurrentUser(s.user)];case 3:return a.sent(),[2,s]}}))}))}function signInWithEmailAndPassword(t,r,n){var i=this;return e(t.app)?Promise.reject(_serverAppCurrentUserOperationNotSupportedError(t)):signInWithCredential(getModularInstance(t),Y.credential(r,n)).catch((function(e){return __awaiter(i,void 0,void 0,(function(){return __generator(this,(function(r){throw e.code==="auth/".concat("password-does-not-meet-requirements")&&recachePasswordPolicy(t),e}))}))}))}function sendSignInLinkToEmail(e,t,r){return __awaiter(this,void 0,void 0,(function(){var n,i;return __generator(this,(function(o){switch(o.label){case 0:return n=_castAuth(e),function setActionCodeSettings(e,t){_assert(t.handleCodeInApp,n,"argument-error"),t&&_setActionCodeSettingsOnRequest(n,e,t)}(i={requestType:"EMAIL_SIGNIN",email:t,clientType:"CLIENT_TYPE_WEB"},r),[4,handleRecaptchaFlow(n,i,"getOobCode",sendSignInLinkToEmail$1)];case 1:return o.sent(),[2]}}))}))}function isSignInWithEmailLink(e,t){var r=J.parseLink(t);return"EMAIL_SIGNIN"===(null==r?void 0:r.operation)}function signInWithEmailLink(t,r,n){return __awaiter(this,void 0,void 0,(function(){var i,o;return __generator(this,(function(s){return e(t.app)?[2,Promise.reject(_serverAppCurrentUserOperationNotSupportedError(t))]:(i=getModularInstance(t),_assert((o=Y.credentialWithLink(r,n||_getCurrentUrl()))._tenantId===(i.tenantId||null),i,"tenant-id-mismatch"),[2,signInWithCredential(i,o)])}))}))}function createAuthUri(e,t){return __awaiter(this,void 0,void 0,(function(){return __generator(this,(function(r){return[2,_performApiRequest(e,"POST","/v1/accounts:createAuthUri",_addTidIfNecessary(e,t))]}))}))}function fetchSignInMethodsForEmail(e,t){return __awaiter(this,void 0,void 0,(function(){var r,n;return __generator(this,(function(i){switch(i.label){case 0:return r=_isHttpOrHttps()?_getCurrentUrl():"http://localhost",n={identifier:t,continueUri:r},[4,createAuthUri(getModularInstance(e),n)];case 1:return[2,i.sent().signinMethods||[]]}}))}))}function sendEmailVerification(e,t){return __awaiter(this,void 0,void 0,(function(){var r,n,i;return __generator(this,(function(o){switch(o.label){case 0:return r=getModularInstance(e),[4,e.getIdToken()];case 1:return n=o.sent(),i={requestType:"VERIFY_EMAIL",idToken:n},t&&_setActionCodeSettingsOnRequest(r.auth,i,t),[4,sendEmailVerification$1(r.auth,i)];case 2:return o.sent().email===e.email?[3,4]:[4,e.reload()];case 3:o.sent(),o.label=4;case 4:return[2]}}))}))}function verifyBeforeUpdateEmail(e,t,r){return __awaiter(this,void 0,void 0,(function(){var n,i,o;return __generator(this,(function(s){switch(s.label){case 0:return n=getModularInstance(e),[4,e.getIdToken()];case 1:return i=s.sent(),o={requestType:"VERIFY_AND_CHANGE_EMAIL",idToken:i,newEmail:t},r&&_setActionCodeSettingsOnRequest(n.auth,o,r),[4,verifyAndChangeEmail(n.auth,o)];case 2:return s.sent().email===e.email?[3,4]:[4,e.reload()];case 3:s.sent(),s.label=4;case 4:return[2]}}))}))}function updateProfile$1(e,t){return __awaiter(this,void 0,void 0,(function(){return __generator(this,(function(r){return[2,_performApiRequest(e,"POST","/v1/accounts:update",t)]}))}))}function updateProfile(e,t){var r=t.displayName,n=t.photoURL;return __awaiter(this,void 0,void 0,(function(){var t,i,o,s,a;return __generator(this,(function(u){switch(u.label){case 0:return void 0===r&&void 0===n?[2]:[4,(t=getModularInstance(e)).getIdToken()];case 1:return i=u.sent(),o={idToken:i,displayName:r,photoUrl:n,returnSecureToken:!0},[4,_logoutIfInvalidated(t,updateProfile$1(t.auth,o))];case 2:return s=u.sent(),t.displayName=s.displayName||null,t.photoURL=s.photoUrl||null,a=t.providerData.find((function(e){return"password"===e.providerId})),a&&(a.displayName=t.displayName,a.photoURL=t.photoURL),[4,t._updateTokensIfNecessary(s)];case 3:return u.sent(),[2]}}))}))}function updateEmail(t,r){var n=getModularInstance(t);return e(n.auth.app)?Promise.reject(_serverAppCurrentUserOperationNotSupportedError(n.auth)):updateEmailOrPassword(n,r,null)}function updatePassword(e,t){return updateEmailOrPassword(getModularInstance(e),null,t)}function updateEmailOrPassword(e,t,r){return __awaiter(this,void 0,void 0,(function(){var n,i,o,s;return __generator(this,(function(a){switch(a.label){case 0:return n=e.auth,[4,e.getIdToken()];case 1:return i=a.sent(),o={idToken:i,returnSecureToken:!0},t&&(o.email=t),r&&(o.password=r),[4,_logoutIfInvalidated(e,updateEmailPassword(n,o))];case 2:return s=a.sent(),[4,e._updateTokensIfNecessary(s,!0)];case 3:return a.sent(),[2]}}))}))}var de=function de(e,t,r){void 0===r&&(r={}),this.isNewUser=e,this.providerId=t,this.profile=r},le=function(e){function FederatedAdditionalUserInfoWithUsername(t,r,n,i){var o=e.call(this,t,r,n)||this;return o.username=i,o}return __extends(FederatedAdditionalUserInfoWithUsername,e),FederatedAdditionalUserInfoWithUsername}(de),he=function(e){function FacebookAdditionalUserInfo(t,r){return e.call(this,t,"facebook.com",r)||this}return __extends(FacebookAdditionalUserInfo,e),FacebookAdditionalUserInfo}(de),pe=function(e){function GithubAdditionalUserInfo(t,r){return e.call(this,t,"github.com",r,"string"==typeof(null==r?void 0:r.login)?null==r?void 0:r.login:null)||this}return __extends(GithubAdditionalUserInfo,e),GithubAdditionalUserInfo}(le),fe=function(e){function GoogleAdditionalUserInfo(t,r){return e.call(this,t,"google.com",r)||this}return __extends(GoogleAdditionalUserInfo,e),GoogleAdditionalUserInfo}(de),_e=function(e){function TwitterAdditionalUserInfo(t,r,n){return e.call(this,t,"twitter.com",r,n)||this}return __extends(TwitterAdditionalUserInfo,e),TwitterAdditionalUserInfo}(le);function getAdditionalUserInfo(e){var t=e,r=t.user,n=t._tokenResponse;return r.isAnonymous&&!n?{providerId:null,isNewUser:!1,profile:null}:function _fromIdTokenResponse(e){var t,r;if(!e)return null;var n=e.providerId,i=e.rawUserInfo?JSON.parse(e.rawUserInfo):{},o=e.isNewUser||"identitytoolkit#SignupNewUserResponse"===e.kind;if(!n&&(null==e?void 0:e.idToken)){var s=null===(r=null===(t=_parseToken(e.idToken))||void 0===t?void 0:t.firebase)||void 0===r?void 0:r.sign_in_provider;if(s)return new de(o,"anonymous"!==s&&"custom"!==s?s:null)}if(!n)return null;switch(n){case"facebook.com":return new he(o,i);case"github.com":return new pe(o,i);case"google.com":return new fe(o,i);case"twitter.com":return new _e(o,i,e.screenName||null);case"custom":case"anonymous":return new de(o,null);default:return new de(o,n,i)}}(n)}function setPersistence(e,t){return getModularInstance(e).setPersistence(t)}function initializeRecaptchaConfig(e){return function _initializeRecaptchaConfig(e){return __awaiter(this,void 0,void 0,(function(){var t,r,n;return __generator(this,(function(i){switch(i.label){case 0:return[4,getRecaptchaConfig(t=_castAuth(e),{clientType:"CLIENT_TYPE_WEB",version:"RECAPTCHA_ENTERPRISE"})];case 1:return r=i.sent(),n=new R(r),null==t.tenantId?t._agentRecaptchaConfig=n:t._tenantRecaptchaConfigs[t.tenantId]=n,n.isProviderEnabled("EMAIL_PASSWORD_PROVIDER")&&new B(t).verify(),[2]}}))}))}(e)}function validatePassword(e,t){return __awaiter(this,void 0,void 0,(function(){return __generator(this,(function(r){return[2,_castAuth(e).validatePassword(t)]}))}))}function onIdTokenChanged(e,t,r,n){return getModularInstance(e).onIdTokenChanged(t,r,n)}function beforeAuthStateChanged(e,t,r){return getModularInstance(e).beforeAuthStateChanged(t,r)}function onAuthStateChanged(e,t,r,n){return getModularInstance(e).onAuthStateChanged(t,r,n)}function useDeviceLanguage(e){getModularInstance(e).useDeviceLanguage()}function updateCurrentUser(e,t){return getModularInstance(e).updateCurrentUser(t)}function signOut(e){return getModularInstance(e).signOut()}function revokeAccessToken(e,t){return _castAuth(e).revokeAccessToken(t)}function deleteUser(e){return __awaiter(this,void 0,void 0,(function(){return __generator(this,(function(t){return[2,getModularInstance(e).delete()]}))}))}var ve=function(){function MultiFactorSessionImpl(e,t,r){this.type=e,this.credential=t,this.user=r}return MultiFactorSessionImpl._fromIdtoken=function(e,t){return new MultiFactorSessionImpl("enroll",e,t)},MultiFactorSessionImpl._fromMfaPendingCredential=function(e){return new MultiFactorSessionImpl("signin",e)},MultiFactorSessionImpl.prototype.toJSON=function(){var e;return{multiFactorSession:(e={},e["enroll"===this.type?"idToken":"pendingCredential"]=this.credential,e)}},MultiFactorSessionImpl.fromJSON=function(e){var t,r;if(null==e?void 0:e.multiFactorSession){if(null===(t=e.multiFactorSession)||void 0===t?void 0:t.pendingCredential)return MultiFactorSessionImpl._fromMfaPendingCredential(e.multiFactorSession.pendingCredential);if(null===(r=e.multiFactorSession)||void 0===r?void 0:r.idToken)return MultiFactorSessionImpl._fromIdtoken(e.multiFactorSession.idToken)}return null},MultiFactorSessionImpl}(),ge=function(){function MultiFactorResolverImpl(e,t,r){this.session=e,this.hints=t,this.signInResolver=r}return MultiFactorResolverImpl._fromError=function(e,t){var r=this,n=_castAuth(e),i=t.customData._serverResponse,o=(i.mfaInfo||[]).map((function(e){return ae._fromServerResponse(n,e)}));_assert(i.mfaPendingCredential,n,"internal-error");var s=ve._fromMfaPendingCredential(i.mfaPendingCredential);return new MultiFactorResolverImpl(s,o,(function(e){return __awaiter(r,void 0,void 0,(function(){var r,o,a;return __generator(this,(function(u){switch(u.label){case 0:return[4,e._process(n,s)];case 1:switch(r=u.sent(),delete i.mfaInfo,delete i.mfaPendingCredential,o=__assign(__assign({},i),{idToken:r.idToken,refreshToken:r.refreshToken}),t.operationType){case"signIn":return[3,2];case"reauthenticate":return[3,5]}return[3,6];case 2:return[4,oe._fromIdTokenResponse(n,t.operationType,o)];case 3:return a=u.sent(),[4,n._updateCurrentUser(a.user)];case 4:return u.sent(),[2,a];case 5:return _assert(t.user,n,"internal-error"),[2,oe._forOperation(t.user,t.operationType,o)];case 6:_fail(n,"internal-error"),u.label=7;case 7:return[2]}}))}))}))},MultiFactorResolverImpl.prototype.resolveSignIn=function(e){return __awaiter(this,void 0,void 0,(function(){var t;return __generator(this,(function(r){return t=e,[2,this.signInResolver(t)]}))}))},MultiFactorResolverImpl}();function getMultiFactorResolver(e,t){var r,n=getModularInstance(e),i=t;return _assert(t.customData.operationType,n,"argument-error"),_assert(null===(r=i.customData._serverResponse)||void 0===r?void 0:r.mfaPendingCredential,n,"argument-error"),ge._fromError(n,i)}var me=function(){function MultiFactorUserImpl(e){var t=this;this.user=e,this.enrolledFactors=[],e._onReload((function(r){r.mfaInfo&&(t.enrolledFactors=r.mfaInfo.map((function(t){return ae._fromServerResponse(e.auth,t)})))}))}return MultiFactorUserImpl._fromUser=function(e){return new MultiFactorUserImpl(e)},MultiFactorUserImpl.prototype.getSession=function(){return __awaiter(this,void 0,void 0,(function(){var e,t;return __generator(this,(function(r){switch(r.label){case 0:return t=(e=ve)._fromIdtoken,[4,this.user.getIdToken()];case 1:return[2,t.apply(e,[r.sent(),this.user])]}}))}))},MultiFactorUserImpl.prototype.enroll=function(e,t){return __awaiter(this,void 0,void 0,(function(){var r,n,i;return __generator(this,(function(o){switch(o.label){case 0:return r=e,[4,this.getSession()];case 1:return n=o.sent(),[4,_logoutIfInvalidated(this.user,r._process(this.user.auth,n,t))];case 2:return i=o.sent(),[4,this.user._updateTokensIfNecessary(i)];case 3:return o.sent(),[2,this.user.reload()]}}))}))},MultiFactorUserImpl.prototype.unenroll=function(e){return __awaiter(this,void 0,void 0,(function(){var t,r,n;return __generator(this,(function(i){switch(i.label){case 0:return t="string"==typeof e?e:e.uid,[4,this.user.getIdToken()];case 1:r=i.sent(),i.label=2;case 2:return i.trys.push([2,6,,7]),[4,_logoutIfInvalidated(this.user,(o=this.user.auth,s={idToken:r,mfaEnrollmentId:t},_performApiRequest(o,"POST","/v2/accounts/mfaEnrollment:withdraw",_addTidIfNecessary(o,s))))];case 3:return n=i.sent(),this.enrolledFactors=this.enrolledFactors.filter((function(e){return e.uid!==t})),[4,this.user._updateTokensIfNecessary(n)];case 4:return i.sent(),[4,this.user.reload()];case 5:return i.sent(),[3,7];case 6:throw i.sent();case 7:return[2]}var o,s}))}))},MultiFactorUserImpl}(),Ie=new WeakMap;function multiFactor(e){var t=getModularInstance(e);return Ie.has(t)||Ie.set(t,me._fromUser(t)),Ie.get(t)}var Ae="@firebase/auth",ye=function(){function AuthInterop(e){this.auth=e,this.internalListeners=new Map}return AuthInterop.prototype.getUid=function(){var e;return this.assertAuthConfigured(),(null===(e=this.auth.currentUser)||void 0===e?void 0:e.uid)||null},AuthInterop.prototype.getToken=function(e){return __awaiter(this,void 0,void 0,(function(){return __generator(this,(function(t){switch(t.label){case 0:return this.assertAuthConfigured(),[4,this.auth._initializationPromise];case 1:return t.sent(),this.auth.currentUser?[4,this.auth.currentUser.getIdToken(e)]:[2,null];case 2:return[2,{accessToken:t.sent()}]}}))}))},AuthInterop.prototype.addAuthTokenListener=function(e){if(this.assertAuthConfigured(),!this.internalListeners.has(e)){var t=this.auth.onIdTokenChanged((function(t){e((null==t?void 0:t.stsTokenManager.accessToken)||null)}));this.internalListeners.set(e,t),this.updateProactiveRefresh()}},AuthInterop.prototype.removeAuthTokenListener=function(e){this.assertAuthConfigured();var t=this.internalListeners.get(e);t&&(this.internalListeners.delete(e),t(),this.updateProactiveRefresh())},AuthInterop.prototype.assertAuthConfigured=function(){_assert(this.auth._initializationPromise,"dependent-sdk-initialized-before-auth")},AuthInterop.prototype.updateProactiveRefresh=function(){this.internalListeners.size>0?this.auth._startProactiveRefresh():this.auth._stopProactiveRefresh()},AuthInterop}();var Ee={PHONE:"phone",TOTP:"totp"},we={FACEBOOK:"facebook.com",GITHUB:"github.com",GOOGLE:"google.com",PASSWORD:"password",PHONE:"phone",TWITTER:"twitter.com"},Te={EMAIL_LINK:"emailLink",EMAIL_PASSWORD:"password",FACEBOOK:"facebook.com",GITHUB:"github.com",GOOGLE:"google.com",PHONE:"phone",TWITTER:"twitter.com"},be={LINK:"link",REAUTHENTICATE:"reauthenticate",SIGN_IN:"signIn"},Pe={EMAIL_SIGNIN:"EMAIL_SIGNIN",PASSWORD_RESET:"PASSWORD_RESET",RECOVER_EMAIL:"RECOVER_EMAIL",REVERT_SECOND_FACTOR_ADDITION:"REVERT_SECOND_FACTOR_ADDITION",VERIFY_AND_CHANGE_EMAIL:"VERIFY_AND_CHANGE_EMAIL",VERIFY_EMAIL:"VERIFY_EMAIL"},Se=function(){function BrowserPersistenceClass(e,t){this.storageRetriever=e,this.type=t}return BrowserPersistenceClass.prototype._isAvailable=function(){try{return this.storage?(this.storage.setItem("__sak","1"),this.storage.removeItem("__sak"),Promise.resolve(!0)):Promise.resolve(!1)}catch(e){return Promise.resolve(!1)}},BrowserPersistenceClass.prototype._set=function(e,t){return this.storage.setItem(e,JSON.stringify(t)),Promise.resolve()},BrowserPersistenceClass.prototype._get=function(e){var t=this.storage.getItem(e);return Promise.resolve(t?JSON.parse(t):null)},BrowserPersistenceClass.prototype._remove=function(e){return this.storage.removeItem(e),Promise.resolve()},Object.defineProperty(BrowserPersistenceClass.prototype,"storage",{get:function(){return this.storageRetriever()},enumerable:!1,configurable:!0}),BrowserPersistenceClass}(),Re=function(e){function BrowserLocalPersistence(){var t=e.call(this,(function(){return window.localStorage}),"LOCAL")||this;return t.boundEventHandler=function(e,r){return t.onStorageEvent(e,r)},t.listeners={},t.localCache={},t.pollTimer=null,t.fallbackToPolling=function _isMobileBrowser(e){return void 0===e&&(e=getUA()),_isIOS(e)||_isAndroid(e)||_isWebOS(e)||_isBlackBerry(e)||/windows phone/i.test(e)||_isIEMobile(e)}(),t._shouldAllowMigration=!0,t}return __extends(BrowserLocalPersistence,e),BrowserLocalPersistence.prototype.forAllChangedKeys=function(e){for(var t=0,r=Object.keys(this.listeners);t<r.length;t++){var n=r[t],i=this.storage.getItem(n),o=this.localCache[n];i!==o&&e(n,o,i)}},BrowserLocalPersistence.prototype.onStorageEvent=function(e,t){var r=this;if(void 0===t&&(t=!1),e.key){var n=e.key;t?this.detachListener():this.stopPolling();var triggerListeners=function(){var e=r.storage.getItem(n);(t||r.localCache[n]!==e)&&r.notifyListeners(n,e)},i=this.storage.getItem(n);_isIE10()&&i!==e.newValue&&e.newValue!==e.oldValue?setTimeout(triggerListeners,10):triggerListeners()}else this.forAllChangedKeys((function(e,t,n){r.notifyListeners(e,n)}))},BrowserLocalPersistence.prototype.notifyListeners=function(e,t){this.localCache[e]=t;var r=this.listeners[e];if(r)for(var n=0,i=Array.from(r);n<i.length;n++){(0,i[n])(t?JSON.parse(t):t)}},BrowserLocalPersistence.prototype.startPolling=function(){var e=this;this.stopPolling(),this.pollTimer=setInterval((function(){e.forAllChangedKeys((function(t,r,n){e.onStorageEvent(new StorageEvent("storage",{key:t,oldValue:r,newValue:n}),!0)}))}),1e3)},BrowserLocalPersistence.prototype.stopPolling=function(){this.pollTimer&&(clearInterval(this.pollTimer),this.pollTimer=null)},BrowserLocalPersistence.prototype.attachListener=function(){window.addEventListener("storage",this.boundEventHandler)},BrowserLocalPersistence.prototype.detachListener=function(){window.removeEventListener("storage",this.boundEventHandler)},BrowserLocalPersistence.prototype._addListener=function(e,t){0===Object.keys(this.listeners).length&&(this.fallbackToPolling?this.startPolling():this.attachListener()),this.listeners[e]||(this.listeners[e]=new Set,this.localCache[e]=this.storage.getItem(e)),this.listeners[e].add(t)},BrowserLocalPersistence.prototype._removeListener=function(e,t){this.listeners[e]&&(this.listeners[e].delete(t),0===this.listeners[e].size&&delete this.listeners[e]),0===Object.keys(this.listeners).length&&(this.detachListener(),this.stopPolling())},BrowserLocalPersistence.prototype._set=function(t,r){return __awaiter(this,void 0,void 0,(function(){return __generator(this,(function(n){switch(n.label){case 0:return[4,e.prototype._set.call(this,t,r)];case 1:return n.sent(),this.localCache[t]=JSON.stringify(r),[2]}}))}))},BrowserLocalPersistence.prototype._get=function(t){return __awaiter(this,void 0,void 0,(function(){var r;return __generator(this,(function(n){switch(n.label){case 0:return[4,e.prototype._get.call(this,t)];case 1:return r=n.sent(),this.localCache[t]=JSON.stringify(r),[2,r]}}))}))},BrowserLocalPersistence.prototype._remove=function(t){return __awaiter(this,void 0,void 0,(function(){return __generator(this,(function(r){switch(r.label){case 0:return[4,e.prototype._remove.call(this,t)];case 1:return r.sent(),delete this.localCache[t],[2]}}))}))},BrowserLocalPersistence.type="LOCAL",BrowserLocalPersistence}(Se),Ce=Re,ke=function(e){function BrowserSessionPersistence(){return e.call(this,(function(){return window.sessionStorage}),"SESSION")||this}return __extends(BrowserSessionPersistence,e),BrowserSessionPersistence.prototype._addListener=function(e,t){},BrowserSessionPersistence.prototype._removeListener=function(e,t){},BrowserSessionPersistence.type="SESSION",BrowserSessionPersistence}(Se);function _withDefaultResolver(e,t){return t?_getInstance(t):(_assert(e._popupRedirectResolver,e,"argument-error"),e._popupRedirectResolver)}var Oe=function(e){function IdpCredential(t){var r=e.call(this,"custom","custom")||this;return r.params=t,r}return __extends(IdpCredential,e),IdpCredential.prototype._getIdTokenResponse=function(e){return signInWithIdp(e,this._buildIdpRequest())},IdpCredential.prototype._linkToIdToken=function(e,t){return signInWithIdp(e,this._buildIdpRequest(t))},IdpCredential.prototype._getReauthenticationResolver=function(e){return signInWithIdp(e,this._buildIdpRequest())},IdpCredential.prototype._buildIdpRequest=function(e){var t={requestUri:this.params.requestUri,sessionId:this.params.sessionId,postBody:this.params.postBody,tenantId:this.params.tenantId,pendingToken:this.params.pendingToken,returnSecureToken:!0,returnIdpCredential:!0};return e&&(t.idToken=e),t},IdpCredential}(H);function _signIn(e){return _signInWithCredential(e.auth,new Oe(e),e.bypassAuthState)}function _reauth(e){var t=e.auth,r=e.user;return _assert(r,t,"internal-error"),_reauthenticate(r,new Oe(e),e.bypassAuthState)}function _link(e){return __awaiter(this,void 0,void 0,(function(){var t,r;return __generator(this,(function(n){return t=e.auth,_assert(r=e.user,t,"internal-error"),[2,_link$1(r,new Oe(e),e.bypassAuthState)]}))}))}var Ne=function(){function AbstractPopupRedirectOperation(e,t,r,n,i){void 0===i&&(i=!1),this.auth=e,this.resolver=r,this.user=n,this.bypassAuthState=i,this.pendingPromise=null,this.eventManager=null,this.filter=Array.isArray(t)?t:[t]}return AbstractPopupRedirectOperation.prototype.execute=function(){var e=this;return new Promise((function(t,r){return __awaiter(e,void 0,void 0,(function(){var e,n;return __generator(this,(function(i){switch(i.label){case 0:this.pendingPromise={resolve:t,reject:r},i.label=1;case 1:return i.trys.push([1,4,,5]),e=this,[4,this.resolver._initialize(this.auth)];case 2:return e.eventManager=i.sent(),[4,this.onExecution()];case 3:return i.sent(),this.eventManager.registerConsumer(this),[3,5];case 4:return n=i.sent(),this.reject(n),[3,5];case 5:return[2]}}))}))}))},AbstractPopupRedirectOperation.prototype.onAuthEvent=function(e){return __awaiter(this,void 0,void 0,(function(){var t,r,n,i,o,s,a,u,c;return __generator(this,(function(d){switch(d.label){case 0:if(t=e.urlResponse,r=e.sessionId,n=e.postBody,i=e.tenantId,o=e.error,s=e.type,o)return this.reject(o),[2];a={auth:this.auth,requestUri:t,sessionId:r,tenantId:i||void 0,postBody:n||void 0,user:this.user,bypassAuthState:this.bypassAuthState},d.label=1;case 1:return d.trys.push([1,3,,4]),u=this.resolve,[4,this.getIdpTask(s)(a)];case 2:return u.apply(this,[d.sent()]),[3,4];case 3:return c=d.sent(),this.reject(c),[3,4];case 4:return[2]}}))}))},AbstractPopupRedirectOperation.prototype.onError=function(e){this.reject(e)},AbstractPopupRedirectOperation.prototype.getIdpTask=function(e){switch(e){case"signInViaPopup":case"signInViaRedirect":return _signIn;case"linkViaPopup":case"linkViaRedirect":return _link;case"reauthViaPopup":case"reauthViaRedirect":return _reauth;default:_fail(this.auth,"internal-error")}},AbstractPopupRedirectOperation.prototype.resolve=function(e){debugAssert(this.pendingPromise,"Pending promise was never set"),this.pendingPromise.resolve(e),this.unregisterAndCleanUp()},AbstractPopupRedirectOperation.prototype.reject=function(e){debugAssert(this.pendingPromise,"Pending promise was never set"),this.pendingPromise.reject(e),this.unregisterAndCleanUp()},AbstractPopupRedirectOperation.prototype.unregisterAndCleanUp=function(){this.eventManager&&this.eventManager.unregisterConsumer(this),this.pendingPromise=null,this.cleanUp()},AbstractPopupRedirectOperation}(),Le=new Map,Me=function(e){function RedirectAction(t,r,n){void 0===n&&(n=!1);var i=e.call(this,t,["signInViaRedirect","linkViaRedirect","reauthViaRedirect","unknown"],r,void 0,n)||this;return i.eventId=null,i}return __extends(RedirectAction,e),RedirectAction.prototype.execute=function(){return __awaiter(this,void 0,void 0,(function(){var t,r,n,i;return __generator(this,(function(o){switch(o.label){case 0:if(t=Le.get(this.auth._key()))return[3,8];o.label=1;case 1:return o.trys.push([1,6,,7]),[4,_getAndClearPendingRedirectStatus(this.resolver,this.auth)];case 2:return o.sent()?[4,e.prototype.execute.call(this)]:[3,4];case 3:return n=o.sent(),[3,5];case 4:n=null,o.label=5;case 5:return r=n,t=function(){return Promise.resolve(r)},[3,7];case 6:return i=o.sent(),t=function(){return Promise.reject(i)},[3,7];case 7:Le.set(this.auth._key(),t),o.label=8;case 8:return this.bypassAuthState||Le.set(this.auth._key(),(function(){return Promise.resolve(null)})),[2,t()]}}))}))},RedirectAction.prototype.onAuthEvent=function(t){return __awaiter(this,void 0,void 0,(function(){var r;return __generator(this,(function(n){switch(n.label){case 0:return"signInViaRedirect"===t.type?[2,e.prototype.onAuthEvent.call(this,t)]:"unknown"===t.type?(this.resolve(null),[2]):t.eventId?[4,this.auth._redirectUserForId(t.eventId)]:[3,2];case 1:if(r=n.sent())return this.user=r,[2,e.prototype.onAuthEvent.call(this,t)];this.resolve(null),n.label=2;case 2:return[2]}}))}))},RedirectAction.prototype.onExecution=function(){return __awaiter(this,void 0,void 0,(function(){return __generator(this,(function(e){return[2]}))}))},RedirectAction.prototype.cleanUp=function(){},RedirectAction}(Ne);function _getAndClearPendingRedirectStatus(e,t){return __awaiter(this,void 0,void 0,(function(){var r,n,i;return __generator(this,(function(o){switch(o.label){case 0:return r=pendingRedirectKey(t),[4,(n=resolverPersistence(e))._isAvailable()];case 1:return o.sent()?[4,n._get(r)]:[2,!1];case 2:return i="true"===o.sent(),[4,n._remove(r)];case 3:return o.sent(),[2,i]}}))}))}function _setPendingRedirectStatus(e,t){return __awaiter(this,void 0,void 0,(function(){return __generator(this,(function(r){return[2,resolverPersistence(e)._set(pendingRedirectKey(t),"true")]}))}))}function _overrideRedirectResult(e,t){Le.set(e._key(),t)}function resolverPersistence(e){return _getInstance(e._redirectPersistence)}function pendingRedirectKey(e){return _persistenceKeyName("pendingRedirect",e.config.apiKey,e.name)}function getRedirectResult(e,t){return __awaiter(this,void 0,void 0,(function(){return __generator(this,(function(r){switch(r.label){case 0:return[4,_castAuth(e)._initializationPromise];case 1:return r.sent(),[2,_getRedirectResult(e,t,!1)]}}))}))}function _getRedirectResult(t,r,n){return void 0===n&&(n=!1),__awaiter(this,void 0,void 0,(function(){var i,o,s;return __generator(this,(function(a){switch(a.label){case 0:return e(t.app)?[2,Promise.reject(_serverAppCurrentUserOperationNotSupportedError(t))]:(i=_castAuth(t),o=_withDefaultResolver(i,r),[4,new Me(i,o,n).execute()]);case 1:return!(s=a.sent())||n?[3,4]:(delete s.user._redirectEventId,[4,i._persistUserIfCurrent(s.user)]);case 2:return a.sent(),[4,i._setRedirectUser(null,r)];case 3:a.sent(),a.label=4;case 4:return[2,s]}}))}))}function prepareUserForRedirect(e){return __awaiter(this,void 0,void 0,(function(){var t;return __generator(this,(function(r){switch(r.label){case 0:return t=_generateEventId("".concat(e.uid,":::")),e._redirectEventId=t,[4,e.auth._setRedirectUser(e)];case 1:return r.sent(),[4,e.auth._persistUserIfCurrent(e)];case 2:return r.sent(),[2,t]}}))}))}var De=encodeURIComponent("fac");function _getRedirectUrl(e,r,n,i,o,s){return __awaiter(this,void 0,void 0,(function(){var a,u,c,d,l,h,p,f,_,v,g,m;return __generator(this,(function(I){switch(I.label){case 0:if(_assert(e.config.authDomain,e,"auth-domain-config-required"),_assert(e.config.apiKey,e,"invalid-api-key"),a={apiKey:e.config.apiKey,appName:e.name,authType:n,redirectUrl:i,v:t,eventId:o},r instanceof $)for(r.setDefaultLanguage(e.languageCode),a.providerId=r.providerId||"",function isEmpty(e){for(const t in e)if(Object.prototype.hasOwnProperty.call(e,t))return!1;return!0}(r.getCustomParameters())||(a.customParameters=JSON.stringify(r.getCustomParameters())),u=0,c=Object.entries(s||{});u<c.length;u++)d=c[u],v=d[0],l=d[1],a[v]=l;for(r instanceof X&&(h=r.getScopes().filter((function(e){return""!==e}))).length>0&&(a.scopes=h.join(",")),e.tenantId&&(a.tid=e.tenantId),p=a,f=0,_=Object.keys(p);f<_.length;f++)v=_[f],void 0===p[v]&&delete p[v];return[4,e._getAppCheckToken()];case 1:return g=I.sent(),m=g?"#".concat(De,"=").concat(encodeURIComponent(g)):"",[2,"".concat(getHandlerBase(e),"?").concat(querystring(p).slice(1)).concat(m)]}}))}))}function getHandlerBase(e){var t=e.config;return t.emulator?_emulatorUrl(t,"emulator/auth/handler"):"https://".concat(t.authDomain,"/").concat("__/auth/handler")}function _cordovaWindow(){return window}function _getProjectConfig(e,t){return void 0===t&&(t={}),__awaiter(this,void 0,void 0,(function(){return __generator(this,(function(r){return[2,_performApiRequest(e,"GET","/v1/projects",t)]}))}))}function _generateHandlerUrl(e,t,r){var n;return __awaiter(this,void 0,void 0,(function(){var i,o,s;return __generator(this,(function(a){switch(a.label){case 0:return i=_cordovaWindow().BuildInfo,debugAssert(t.sessionId,"AuthEvent did not contain a session ID"),[4,computeSha256(t.sessionId)];case 1:return o=a.sent(),s={},_isIOS()?s.ibi=i.packageName:_isAndroid()?s.apn=i.packageName:_fail(e,"operation-not-supported-in-this-environment"),i.displayName&&(s.appDisplayName=i.displayName),s.sessionId=o,[2,_getRedirectUrl(e,r,t.type,void 0,null!==(n=t.eventId)&&void 0!==n?n:void 0,s)]}}))}))}function _performRedirect(e){var t=_cordovaWindow().cordova;return new Promise((function(r){t.plugins.browsertab.isAvailable((function(n){var i=null;n?t.plugins.browsertab.openUrl(e):i=t.InAppBrowser.open(e,function _isIOS7Or8(e){return void 0===e&&(e=getUA()),/(iPad|iPhone|iPod).*OS 7_\d/i.test(e)||/(iPad|iPhone|iPod).*OS 8_\d/i.test(e)}()?"_blank":"_system","location=yes"),r(i)}))}))}function _waitForAppResume(e,t,r){return __awaiter(this,void 0,void 0,(function(){var n,i;return __generator(this,(function(o){switch(o.label){case 0:n=_cordovaWindow().cordova,i=function(){},o.label=1;case 1:return o.trys.push([1,,3,4]),[4,new Promise((function(o,s){var a=null;function authEventSeen(){var e;o();var t=null===(e=n.plugins.browsertab)||void 0===e?void 0:e.close;"function"==typeof t&&t(),"function"==typeof(null==r?void 0:r.close)&&r.close()}function resumed(){a||(a=window.setTimeout((function(){s(_createError(e,"redirect-cancelled-by-user"))}),2e3))}function visibilityChanged(){"visible"===(null===document||void 0===document?void 0:document.visibilityState)&&resumed()}t.addPassiveListener(authEventSeen),document.addEventListener("resume",resumed,!1),_isAndroid()&&document.addEventListener("visibilitychange",visibilityChanged,!1),i=function(){t.removePassiveListener(authEventSeen),document.removeEventListener("resume",resumed,!1),document.removeEventListener("visibilitychange",visibilityChanged,!1),a&&window.clearTimeout(a)}}))];case 2:return o.sent(),[3,4];case 3:return i(),[7];case 4:return[2]}}))}))}function computeSha256(e){return __awaiter(this,void 0,void 0,(function(){var t,r;return __generator(this,(function(n){switch(n.label){case 0:return t=function stringToArrayBuffer(e){if(debugAssert(/[0-9a-zA-Z]+/.test(e),"Can only convert alpha-numeric strings"),"undefined"!=typeof TextEncoder)return(new TextEncoder).encode(e);for(var t=new ArrayBuffer(e.length),r=new Uint8Array(t),n=0;n<e.length;n++)r[n]=e.charCodeAt(n);return r}(e),[4,crypto.subtle.digest("SHA-256",t)];case 1:return r=n.sent(),[2,Array.from(new Uint8Array(r)).map((function(e){return e.toString(16).padStart(2,"0")})).join("")]}}))}))}var Ue=function(){function AuthEventManager(e){this.auth=e,this.cachedEventUids=new Set,this.consumers=new Set,this.queuedRedirectEvent=null,this.hasHandledPotentialRedirect=!1,this.lastProcessedEventTime=Date.now()}return AuthEventManager.prototype.registerConsumer=function(e){this.consumers.add(e),this.queuedRedirectEvent&&this.isEventForConsumer(this.queuedRedirectEvent,e)&&(this.sendToConsumer(this.queuedRedirectEvent,e),this.saveEventToCache(this.queuedRedirectEvent),this.queuedRedirectEvent=null)},AuthEventManager.prototype.unregisterConsumer=function(e){this.consumers.delete(e)},AuthEventManager.prototype.onEvent=function(e){var t=this;if(this.hasEventBeenHandled(e))return!1;var r=!1;return this.consumers.forEach((function(n){t.isEventForConsumer(e,n)&&(r=!0,t.sendToConsumer(e,n),t.saveEventToCache(e))})),this.hasHandledPotentialRedirect||!function isRedirectEvent(e){switch(e.type){case"signInViaRedirect":case"linkViaRedirect":case"reauthViaRedirect":return!0;case"unknown":return isNullRedirectEvent(e);default:return!1}}(e)||(this.hasHandledPotentialRedirect=!0,r||(this.queuedRedirectEvent=e,r=!0)),r},AuthEventManager.prototype.sendToConsumer=function(e,t){var r;if(e.error&&!isNullRedirectEvent(e)){var n=(null===(r=e.error.code)||void 0===r?void 0:r.split("auth/")[1])||"internal-error";t.onError(_createError(this.auth,n))}else t.onAuthEvent(e)},AuthEventManager.prototype.isEventForConsumer=function(e,t){var r=null===t.eventId||!!e.eventId&&e.eventId===t.eventId;return t.filter.includes(e.type)&&r},AuthEventManager.prototype.hasEventBeenHandled=function(e){return Date.now()-this.lastProcessedEventTime>=6e5&&this.cachedEventUids.clear(),this.cachedEventUids.has(eventUid(e))},AuthEventManager.prototype.saveEventToCache=function(e){this.cachedEventUids.add(eventUid(e)),this.lastProcessedEventTime=Date.now()},AuthEventManager}();function eventUid(e){return[e.type,e.eventId,e.sessionId,e.tenantId].filter((function(e){return e})).join("-")}function isNullRedirectEvent(e){var t=e.type,r=e.error;return"unknown"===t&&(null==r?void 0:r.code)==="auth/".concat("no-auth-event")}var Fe=function(e){function CordovaAuthEventManager(){var t=null!==e&&e.apply(this,arguments)||this;return t.passiveListeners=new Set,t.initPromise=new Promise((function(e){t.resolveInitialized=e})),t}return __extends(CordovaAuthEventManager,e),CordovaAuthEventManager.prototype.addPassiveListener=function(e){this.passiveListeners.add(e)},CordovaAuthEventManager.prototype.removePassiveListener=function(e){this.passiveListeners.delete(e)},CordovaAuthEventManager.prototype.resetRedirect=function(){this.queuedRedirectEvent=null,this.hasHandledPotentialRedirect=!1},CordovaAuthEventManager.prototype.onEvent=function(t){return this.resolveInitialized(),this.passiveListeners.forEach((function(e){return e(t)})),e.prototype.onEvent.call(this,t)},CordovaAuthEventManager.prototype.initialized=function(){return __awaiter(this,void 0,void 0,(function(){return __generator(this,(function(e){switch(e.label){case 0:return[4,this.initPromise];case 1:return e.sent(),[2]}}))}))},CordovaAuthEventManager}(Ue);function _savePartialEvent(e,t){return storage()._set(persistenceKey(e),t)}function _getAndRemoveEvent(e){return __awaiter(this,void 0,void 0,(function(){var t;return __generator(this,(function(r){switch(r.label){case 0:return[4,storage()._get(persistenceKey(e))];case 1:return(t=r.sent())?[4,storage()._remove(persistenceKey(e))]:[3,3];case 2:r.sent(),r.label=3;case 3:return[2,t]}}))}))}function _eventFromPartialAndUrl(e,t){var r,n,i=function _getDeepLinkFromCallback(e){var t=searchParamsOrEmpty(e),r=t.link?decodeURIComponent(t.link):void 0,n=searchParamsOrEmpty(r).link,i=t.deep_link_id?decodeURIComponent(t.deep_link_id):void 0;return searchParamsOrEmpty(i).link||i||n||r||e}(t);if(i.includes("/__/auth/callback")){var o=searchParamsOrEmpty(i),s=o.firebaseError?function parseJsonOrNull(e){try{return JSON.parse(e)}catch(e){return null}}(decodeURIComponent(o.firebaseError)):null,a=null===(n=null===(r=null==s?void 0:s.code)||void 0===r?void 0:r.split("auth/"))||void 0===n?void 0:n[1],u=a?_createError(a):null;return u?{type:e.type,eventId:e.eventId,tenantId:e.tenantId,error:u,urlResponse:null,sessionId:null,postBody:null}:{type:e.type,eventId:e.eventId,tenantId:e.tenantId,sessionId:e.sessionId,urlResponse:i,postBody:null}}return null}function generateSessionId(){for(var e=[],t="1234567890abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ",r=0;r<20;r++){var n=Math.floor(Math.random()*t.length);e.push(t.charAt(n))}return e.join("")}function storage(){return _getInstance(Ce)}function persistenceKey(e){return _persistenceKeyName("authEvent",e.config.apiKey,e.name)}function searchParamsOrEmpty(e){if(!(null==e?void 0:e.includes("?")))return{};var t=e.split("?");return t[0],querystringDecode(t.slice(1).join("?"))}var Ve=function(){function CordovaPopupRedirectResolver(){this._redirectPersistence=ke,this._shouldInitProactively=!0,this.eventManagers=new Map,this.originValidationPromises={},this._completeRedirectFn=_getRedirectResult,this._overrideRedirectResult=_overrideRedirectResult}return CordovaPopupRedirectResolver.prototype._initialize=function(e){return __awaiter(this,void 0,void 0,(function(){var t,r;return __generator(this,(function(n){return t=e._key(),(r=this.eventManagers.get(t))||(r=new Fe(e),this.eventManagers.set(t,r),this.attachCallbackListeners(e,r)),[2,r]}))}))},CordovaPopupRedirectResolver.prototype._openPopup=function(e){_fail(e,"operation-not-supported-in-this-environment")},CordovaPopupRedirectResolver.prototype._openRedirect=function(e,t,r,n){return __awaiter(this,void 0,void 0,(function(){var i,o,s;return __generator(this,(function(a){switch(a.label){case 0:return function _checkCordovaConfiguration(e){var t,r,n,i,o,s,a,u,c,d,l=_cordovaWindow();_assert("function"==typeof(null===(t=null==l?void 0:l.universalLinks)||void 0===t?void 0:t.subscribe),e,"invalid-cordova-configuration",{missingPlugin:"cordova-universal-links-plugin-fix"}),_assert(void 0!==(null===(r=null==l?void 0:l.BuildInfo)||void 0===r?void 0:r.packageName),e,"invalid-cordova-configuration",{missingPlugin:"cordova-plugin-buildInfo"}),_assert("function"==typeof(null===(o=null===(i=null===(n=null==l?void 0:l.cordova)||void 0===n?void 0:n.plugins)||void 0===i?void 0:i.browsertab)||void 0===o?void 0:o.openUrl),e,"invalid-cordova-configuration",{missingPlugin:"cordova-plugin-browsertab"}),_assert("function"==typeof(null===(u=null===(a=null===(s=null==l?void 0:l.cordova)||void 0===s?void 0:s.plugins)||void 0===a?void 0:a.browsertab)||void 0===u?void 0:u.isAvailable),e,"invalid-cordova-configuration",{missingPlugin:"cordova-plugin-browsertab"}),_assert("function"==typeof(null===(d=null===(c=null==l?void 0:l.cordova)||void 0===c?void 0:c.InAppBrowser)||void 0===d?void 0:d.open),e,"invalid-cordova-configuration",{missingPlugin:"cordova-plugin-inappbrowser"})}(e),[4,this._initialize(e)];case 1:return[4,(i=a.sent()).initialized()];case 2:return a.sent(),i.resetRedirect(),function _clearRedirectOutcomes(){Le.clear()}(),[4,this._originValidation(e)];case 3:return a.sent(),o=function _generateNewEvent(e,t,r){return void 0===r&&(r=null),{type:t,eventId:r,urlResponse:null,sessionId:generateSessionId(),postBody:null,tenantId:e.tenantId,error:_createError(e,"no-auth-event")}}(e,r,n),[4,_savePartialEvent(e,o)];case 4:return a.sent(),[4,_generateHandlerUrl(e,o,t)];case 5:return[4,_performRedirect(a.sent())];case 6:return s=a.sent(),[2,_waitForAppResume(e,i,s)]}}))}))},CordovaPopupRedirectResolver.prototype._isIframeWebStorageSupported=function(e,t){throw new Error("Method not implemented.")},CordovaPopupRedirectResolver.prototype._originValidation=function(e){var t=e._key();return this.originValidationPromises[t]||(this.originValidationPromises[t]=function _validateOrigin(e){return __awaiter(this,void 0,void 0,(function(){var t,r;return __generator(this,(function(n){switch(n.label){case 0:return t=_cordovaWindow().BuildInfo,r={},_isIOS()?r.iosBundleId=t.packageName:_isAndroid()?r.androidPackageName=t.packageName:_fail(e,"operation-not-supported-in-this-environment"),[4,_getProjectConfig(e,r)];case 1:return n.sent(),[2]}}))}))}(e)),this.originValidationPromises[t]},CordovaPopupRedirectResolver.prototype.attachCallbackListeners=function(e,t){var r=this,n=_cordovaWindow(),i=n.universalLinks,o=n.handleOpenURL,s=n.BuildInfo,a=setTimeout((function(){return __awaiter(r,void 0,void 0,(function(){return __generator(this,(function(r){switch(r.label){case 0:return[4,_getAndRemoveEvent(e)];case 1:return r.sent(),t.onEvent(generateNoEvent()),[2]}}))}))}),500),universalLinksCb=function(n){return __awaiter(r,void 0,void 0,(function(){var r,i;return __generator(this,(function(o){switch(o.label){case 0:return clearTimeout(a),[4,_getAndRemoveEvent(e)];case 1:return r=o.sent(),i=null,r&&(null==n?void 0:n.url)&&(i=_eventFromPartialAndUrl(r,n.url)),t.onEvent(i||generateNoEvent()),[2]}}))}))};void 0!==i&&"function"==typeof i.subscribe&&i.subscribe(null,universalLinksCb);var u=o,c="".concat(s.packageName.toLowerCase(),"://");_cordovaWindow().handleOpenURL=function(e){return __awaiter(r,void 0,void 0,(function(){return __generator(this,(function(t){if(e.toLowerCase().startsWith(c)&&universalLinksCb({url:e}),"function"==typeof u)try{u(e)}catch(e){console.error(e)}return[2]}))}))}},CordovaPopupRedirectResolver}(),xe=Ve;function generateNoEvent(){return{type:"unknown",eventId:null,sessionId:null,urlResponse:null,postBody:null,tenantId:null,error:_createError("no-auth-event")}}function signInWithRedirect(t,r,n){return function _signInWithRedirect(t,r,n){return __awaiter(this,void 0,void 0,(function(){var i,o;return __generator(this,(function(s){switch(s.label){case 0:return e(t.app)?[2,Promise.reject(_serverAppCurrentUserOperationNotSupportedError(t))]:(i=_castAuth(t),_assertInstanceOf(t,r,$),[4,i._initializationPromise]);case 1:return s.sent(),[4,_setPendingRedirectStatus(o=_withDefaultResolver(i,n),i)];case 2:return s.sent(),[2,o._openRedirect(i,r,"signInViaRedirect")]}}))}))}(t,r,n)}function reauthenticateWithRedirect(t,r,n){return function _reauthenticateWithRedirect(t,r,n){return __awaiter(this,void 0,void 0,(function(){var i,o,s;return __generator(this,(function(a){switch(a.label){case 0:return _assertInstanceOf((i=getModularInstance(t)).auth,r,$),e(i.auth.app)?[2,Promise.reject(_serverAppCurrentUserOperationNotSupportedError(i.auth))]:[4,i.auth._initializationPromise];case 1:return a.sent(),[4,_setPendingRedirectStatus(o=_withDefaultResolver(i.auth,n),i.auth)];case 2:return a.sent(),[4,prepareUserForRedirect(i)];case 3:return s=a.sent(),[2,o._openRedirect(i.auth,r,"reauthViaRedirect",s)]}}))}))}(t,r,n)}function linkWithRedirect(e,t,r){return function _linkWithRedirect(e,t,r){return __awaiter(this,void 0,void 0,(function(){var n,i,o;return __generator(this,(function(s){switch(s.label){case 0:return _assertInstanceOf((n=getModularInstance(e)).auth,t,$),[4,n.auth._initializationPromise];case 1:return s.sent(),i=_withDefaultResolver(n.auth,r),[4,_assertLinkedStatus(!1,n,t.providerId)];case 2:return s.sent(),[4,_setPendingRedirectStatus(i,n.auth)];case 3:return s.sent(),[4,prepareUserForRedirect(n)];case 4:return o=s.sent(),[2,i._openRedirect(n.auth,t,"linkViaRedirect",o)]}}))}))}(e,t,r)}function getAuth(e){void 0===e&&(e=i());var t=_getProvider(e,"auth");return t.isInitialized()?t.getImmediate():initializeAuth(e,{persistence:v,popupRedirectResolver:xe})}!function registerAuth(e){r(new Component("auth",(function(t,r){var n=r.options,i=t.getProvider("app").getImmediate(),o=t.getProvider("heartbeat"),s=t.getProvider("app-check-internal"),a=i.options,u=a.apiKey,c=a.authDomain;_assert(u&&!u.includes(":"),"invalid-api-key",{appName:i.name});var d={apiKey:u,authDomain:c,clientPlatform:e,apiHost:"identitytoolkit.googleapis.com",tokenApiHost:"securetoken.googleapis.com",apiScheme:"https",sdkClientVersion:_getClientVersion(e)},l=new V(i,o,s,d);return function _initializeAuthInstance(e,t){var r=(null==t?void 0:t.persistence)||[],n=(Array.isArray(r)?r:[r]).map(_getInstance);(null==t?void 0:t.errorMap)&&e._updateErrorMap(t.errorMap),e._initializeWithPersistence(n,null==t?void 0:t.popupRedirectResolver)}(l,n),l}),"PUBLIC").setInstantiationMode("EXPLICIT").setInstanceCreatedCallback((function(e,t,r){e.getProvider("auth-internal").initialize()}))),r(new Component("auth-internal",(function(e){return function(e){return new ye(e)}(_castAuth(e.getProvider("auth").getImmediate()))}),"PRIVATE").setInstantiationMode("EXPLICIT")),n(Ae,"1.7.9",function getVersionForPlatform(e){switch(e){case"Node":return"node";case"ReactNative":return"rn";case"Worker":return"webworker";case"Cordova":return"cordova";case"WebExtension":return"web-extension";default:return}}(e)),n(Ae,"1.7.9","esm5")}("Cordova");export{Pe as ActionCodeOperation,J as ActionCodeURL,H as AuthCredential,A as AuthErrorCodes,q as EmailAuthCredential,Y as EmailAuthProvider,Z as FacebookAuthProvider,Ee as FactorId,te as GithubAuthProvider,ee as GoogleAuthProvider,G as OAuthCredential,Q as OAuthProvider,be as OperationType,K as PhoneAuthCredential,we as ProviderId,ne as SAMLAuthProvider,Te as SignInMethod,ie as TwitterAuthProvider,applyActionCode,beforeAuthStateChanged,Ce as browserLocalPersistence,ke as browserSessionPersistence,checkActionCode,confirmPasswordReset,connectAuthEmulator,xe as cordovaPopupRedirectResolver,createUserWithEmailAndPassword,g as debugErrorMap,deleteUser,fetchSignInMethodsForEmail,getAdditionalUserInfo,getAuth,getIdToken,getIdTokenResult,getMultiFactorResolver,getRedirectResult,M as inMemoryPersistence,v as indexedDBLocalPersistence,initializeAuth,initializeRecaptchaConfig,isSignInWithEmailLink,linkWithCredential,linkWithRedirect,multiFactor,onAuthStateChanged,onIdTokenChanged,parseActionCodeURL,m as prodErrorMap,reauthenticateWithCredential,reauthenticateWithRedirect,reload,revokeAccessToken,sendEmailVerification,sendPasswordResetEmail,sendSignInLinkToEmail,setPersistence,signInAnonymously,signInWithCredential,signInWithCustomToken,signInWithEmailAndPassword,signInWithEmailLink,signInWithRedirect,signOut,unlink,updateCurrentUser,updateEmail,updatePassword,updateProfile,useDeviceLanguage,validatePassword,verifyBeforeUpdateEmail,verifyPasswordResetCode};

//# sourceMappingURL=firebase-auth-cordova.js.map
