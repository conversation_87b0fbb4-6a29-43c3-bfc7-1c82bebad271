import{D as g,a as o,b as s,E as m,G as l}from"./index-DX9maXWO.js";let p=(r=21)=>crypto.getRandomValues(new Uint8Array(r)).reduce((e,t)=>(t&=63,t<36?e+=t.toString(36):t<62?e+=(t-26).toString(36).toUpperCase():t>62?e+="-":e+="_",e),"");const u=async(r,e="",t=!1,c={},i=[])=>{try{const a=p(10);return console.log(`Creating meeting with requireApproval: ${t}`),console.log("Initial features:",c),console.log("Initial extensions:",i),await g(o(s,"meetings",a),{createdBy:r,createdAt:m(),meetingName:e||`Meeting ${a}`,active:!0,requiresApproval:t,features:c,extensions:i.map(n=>({id:n.id,name:n.name,config:n.config||{}}))}),await g(o(s,"meetings",a,"participants",r),{userId:r,userName:localStorage.getItem("userName")||"Host",joined:m(),isAdmin:!0}),a}catch(a){throw console.error("Error creating meeting:",a),a}},d=async r=>{try{const e=await l(o(s,"meetings",r));return e.exists()&&e.data().active}catch(e){return console.error("Error checking meeting:",e),!1}};export{d as a,u as c,p as n};
