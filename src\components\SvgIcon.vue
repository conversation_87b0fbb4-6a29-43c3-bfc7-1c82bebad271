<template>
  <svg
    :class="iconClasses"
    :style="iconStyles"
    :aria-label="computedAriaLabel"
    :role="role"
    fill="currentColor"
    viewBox="0 0 24 24"
    xmlns="http://www.w3.org/2000/svg"
    v-bind="$attrs"
  >
    <path :d="iconPath" />
  </svg>
</template>

<script setup>
import { computed } from 'vue'

const props = defineProps({
  name: {
    type: String,
    required: true,
    validator: (value) => [
      'camera-off',
      'camera-on',
      'microphone-off',
      'microphone-on',
      'user-avatar',
      'broadcasting',
      'screen-share',
      'chat'
    ].includes(value)
  },
  size: {
    type: [String, Number],
    default: '6'
  },
  color: {
    type: String,
    default: 'currentColor'
  },
  ariaLabel: {
    type: String,
    default: null
  },
  role: {
    type: String,
    default: 'img'
  }
})

// Icon path definitions
const iconPaths = {
  'camera-off': 'M3.707 2.293a1 1 0 0 0-1.414 1.414l18 18a1 1 0 0 0 1.414-1.414l-18-18zM21 6.5l-4 4v-1.5a2 2 0 0 0-2-2H9.414l-2-2H15a4 4 0 0 1 4 4v1.5l2-2v8.5a1 1 0 0 1-1.707.707L21 6.5zM3 7a1 1 0 0 1 1-1h.586l2 2H4v8a2 2 0 0 0 2 2h8a2 2 0 0 0 2-2v-.586l2 2H6a4 4 0 0 1-4-4V7z',
  'camera-on': 'M4 6a2 2 0 0 1 2-2h8a2 2 0 0 1 2 2v1.5l4-4v13l-4-4V18a2 2 0 0 1-2 2H6a2 2 0 0 1-2-2V6z',
  'microphone-off': 'M3.707 2.293a1 1 0 0 0-1.414 1.414l18 18a1 1 0 0 0 1.414-1.414l-18-18zM12 2a3 3 0 0 1 3 3v6c0 .386-.074.752-.121 1.121l-1.758-1.758A1 1 0 0 0 13 10V5a1 1 0 0 0-2 0v3.586l-2-2V5a3 3 0 0 1 3-3zM5.586 9H5a1 1 0 0 0-1 1v1a8 8 0 0 0 8 8 8.001 8.001 0 0 0 6.929-4.071l1.414 1.414A9.969 9.969 0 0 1 12 21a10 10 0 0 1-10-10v-1a1 1 0 0 1 1-1h1.586l2 2z',
  'microphone-on': 'M12 2a3 3 0 0 1 3 3v6a3 3 0 0 1-6 0V5a3 3 0 0 1 3-3zM19 10v1a7 7 0 0 1-14 0v-1a1 1 0 0 1 2 0v1a5 5 0 0 0 10 0v-1a1 1 0 0 1 2 0zM12 18.95a1 1 0 0 1 1 1V22a1 1 0 0 1-2 0v-2.05a1 1 0 0 1 1-1z',
  'screen-share': 'M20 3H4c-1.11 0-2 .89-2 2v11c0 1.11.89 2 2 2h4v2h8v-2h4c1.11 0 2-.89 2-2V5c0-1.11-.89-2-2-2zm0 13H4V5h16v11zM6.5 7.5v1L10 6l-3.5-2.5v1H5v3h1.5zM17.5 16.5v-1L14 18l3.5 2.5v-1H19v-3h-1.5z',
  'chat': 'M20 2H4c-1.1 0-2 .9-2 2v12c0 1.1.9 2 2 2h4v3c0 .6.4 1 1 1 .2 0 .5-.1.7-.3L14.4 18H20c1.1 0 2-.9 2-2V4c0-1.1-.9-2-2-2zM8 10a1 1 0 1 1 0-2 1 1 0 0 1 0 2zM12 10a1 1 0 1 1 0-2 1 1 0 0 1 0 2zM16 10a1 1 0 1 1 0-2 1 1 0 0 1 0 2z'
}

// Get the icon path based on name
const iconPath = computed(() => {
  return iconPaths[props.name] || ''
})

// Computed classes for styling
const iconClasses = computed(() => {
  const sizeClass = typeof props.size === 'string' && props.size.includes('w-')
    ? props.size
    : `w-${props.size} h-${props.size}`

  return [
    sizeClass,
    'inline-block',
    'flex-shrink-0'
  ]
})

// Computed styles
const iconStyles = computed(() => ({
  color: props.color
}))

// Computed aria-label
const computedAriaLabel = computed(() => {
  if (props.ariaLabel) return props.ariaLabel

  // Generate default aria-label based on icon name
  const labelMap = {
    'camera-off': 'Camera is off',
    'camera-on': 'Camera is on',
    'microphone-off': 'Microphone is muted',
    'microphone-on': 'Microphone is active',
    'user-avatar': 'User avatar',
    'broadcasting': 'Broadcasting',
    'screen-share': 'Screen sharing',
    'chat': 'Chat'
  }

  return labelMap[props.name] || props.name
})
</script>

<style scoped>
/* Ensure SVG inherits color properly */
svg {
  fill: currentColor;
  color: inherit;
}
</style>
