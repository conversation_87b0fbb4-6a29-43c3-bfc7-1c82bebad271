import { createApp } from 'vue'
import { createPinia } from 'pinia'
import App from './App.vue'
import router from './router'
import '../public/css/tailwind.css'

// Add Font Awesome CSS
const fontAwesomeLink = document.createElement('link')
fontAwesomeLink.rel = 'stylesheet'
fontAwesomeLink.href = 'https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css'
fontAwesomeLink.integrity = 'sha512-iecdLmaskl7CVkqkXNQ/ZH/XLlvWZOJyj7Yy7tcenmpD1ypASozpmT/E0iPtmFIB46ZmdtAc9eNBvH0H/ZpiBw=='
fontAwesomeLink.crossOrigin = 'anonymous'
fontAwesomeLink.referrerPolicy = 'no-referrer'
document.head.appendChild(fontAwesomeLink)

// Initialize the app
const app = createApp(App)

// Use plugins
app.use(createPinia())
app.use(router)

// Mount the app
app.mount('#app')
