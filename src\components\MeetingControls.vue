<template>
  <div class="bg-white dark:bg-gray-800 shadow-lg px-6 py-4 flex justify-center items-center space-x-6">
    <!-- Microphone Control -->
    <button
      @click="$emit('toggle-mic')"
      class="p-4 rounded-full transition-colors duration-200"
      :class="isMicMuted ? 'bg-red-500 text-white hover:bg-red-600' : 'bg-gray-200 text-gray-700 hover:bg-gray-300'"
      :title="isMicMuted ? 'Unmute microphone' : 'Mute microphone'"
      :aria-label="isMicMuted ? 'Unmute microphone' : 'Mute microphone'"
    >
      <i :class="isMicMuted ? 'fas fa-microphone-slash' : 'fas fa-microphone'" class="text-lg"></i>
    </button>

    <!-- Camera Control -->
    <button
      @click="$emit('toggle-camera')"
      class="p-4 rounded-full transition-colors duration-200"
      :class="isCameraOff ? 'bg-red-500 text-white hover:bg-red-600' : 'bg-gray-200 text-gray-700 hover:bg-gray-300'"
      :title="isCameraOff ? 'Turn on camera' : 'Turn off camera'"
      :aria-label="isCameraOff ? 'Turn on camera' : 'Turn off camera'"
    >
      <i :class="isCameraOff ? 'fas fa-video-slash' : 'fas fa-video'" class="text-lg"></i>
    </button>

    <!-- Screen Share Control -->
    <button
      @click="$emit('toggle-screen')"
      class="p-4 rounded-full transition-colors duration-200"
      :class="isScreenSharing ? 'bg-green-500 text-white hover:bg-green-600' : 'bg-gray-200 text-gray-700 hover:bg-gray-300'"
      :title="isScreenSharing ? 'Stop sharing screen' : 'Share screen'"
      :aria-label="isScreenSharing ? 'Stop sharing screen' : 'Share screen'"
    >
      <i class="fas fa-desktop text-lg"></i>
    </button>

    <!-- Chat Control -->
    <button
      @click="$emit('toggle-chat')"
      class="p-4 rounded-full transition-colors duration-200"
      :class="isChatOpen ? 'bg-blue-500 text-white hover:bg-blue-600' : 'bg-gray-200 text-gray-700 hover:bg-gray-300'"
      :title="isChatOpen ? 'Close chat' : 'Open chat'"
      :aria-label="isChatOpen ? 'Close chat' : 'Open chat'"
    >
      <i class="fas fa-comments text-lg"></i>
    </button>

    <!-- End Call Button -->
    <button
      @click="$emit('leave-meeting')"
      class="p-4 rounded-full bg-red-500 text-white hover:bg-red-600 transition-colors duration-200"
      title="Leave meeting"
      aria-label="Leave meeting"
    >
      <i class="fas fa-phone-slash text-lg"></i>
    </button>
  </div>
</template>

<script setup>
defineProps({
  isMicMuted: {
    type: Boolean,
    default: false
  },
  isCameraOff: {
    type: Boolean,
    default: false
  },
  isScreenSharing: {
    type: Boolean,
    default: false
  },
  isChatOpen: {
    type: Boolean,
    default: false
  }
})

defineEmits([
  'toggle-mic',
  'toggle-camera',
  'toggle-screen',
  'toggle-chat',
  'leave-meeting'
])
</script>
