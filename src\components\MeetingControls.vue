<template>
  <div class="bg-white dark:bg-gray-800 shadow-lg px-4 py-3 flex justify-center items-center space-x-4">
    <button
      @click="$emit('toggle-mic')"
      class="p-3 rounded-full"
      :class="isMicMuted ? 'bg-red-100 text-red-600 dark:bg-red-900 dark:text-red-300' : 'bg-gray-100 text-gray-700 dark:bg-gray-700 dark:text-gray-300'"
      :title="isMicMuted ? 'Unmute microphone' : 'Mute microphone'"
    >
      <i :class="isMicMuted ? 'fas fa-microphone-slash' : 'fas fa-microphone'"></i>
    </button>

    <button
      @click="$emit('toggle-camera')"
      class="p-3 rounded-full"
      :class="isCameraOff ? 'bg-red-100 text-red-600 dark:bg-red-900 dark:text-red-300' : 'bg-gray-100 text-gray-700 dark:bg-gray-700 dark:text-gray-300'"
      :title="isCameraOff ? 'Turn on camera' : 'Turn off camera'"
    >
      <i :class="isCameraOff ? 'fas fa-video-slash' : 'fas fa-video'"></i>
    </button>

    <button
      @click="$emit('toggle-screen')"
      class="p-3 rounded-full"
      :class="isScreenSharing ? 'bg-green-100 text-green-600 dark:bg-green-900 dark:text-green-300' : 'bg-gray-100 text-gray-700 dark:bg-gray-700 dark:text-gray-300'"
      :title="isScreenSharing ? 'Stop sharing screen' : 'Share screen'"
    >
      <i class="fas fa-desktop"></i>
    </button>

    <button
      @click="$emit('toggle-chat')"
      class="p-3 rounded-full"
      :class="isChatOpen ? 'bg-primary-light text-white' : 'bg-gray-100 text-gray-700 dark:bg-gray-700 dark:text-gray-300'"
      :title="isChatOpen ? 'Close chat' : 'Open chat'"
    >
      <i class="fas fa-comments"></i>
    </button>

    <!-- AIS toggle -->
    <button
      @click="$emit('toggle-ais')"
      class="p-3 rounded-full"
      :class="isAISEnabled ? 'bg-purple-100 text-purple-600 dark:bg-purple-900 dark:text-purple-300' : 'bg-gray-100 text-gray-700 dark:bg-gray-700 dark:text-gray-300'"
      :title="isAISEnabled ? 'Disable transcription' : 'Enable transcription'"
    >
      <i class="fas fa-closed-captioning"></i>
    </button>

    <!-- Reactions toggle -->
    <button
      @click="$emit('toggle-reactions')"
      class="p-3 rounded-full"
      :class="isReactionsEnabled ? 'bg-yellow-100 text-yellow-600 dark:bg-yellow-900 dark:text-yellow-300' : 'bg-gray-100 text-gray-700 dark:bg-gray-700 dark:text-gray-300'"
      title="Toggle reactions"
    >
      <i class="fas fa-smile"></i>
    </button>

    <!-- Breakout Rooms (Admin only) -->
    <button
      v-if="isAdmin"
      @click="$emit('toggle-breakout-rooms')"
      class="p-3 rounded-full"
      :class="isBreakoutRoomsEnabled ? 'bg-blue-100 text-blue-600 dark:bg-blue-900 dark:text-blue-300' : 'bg-gray-100 text-gray-700 dark:bg-gray-700 dark:text-gray-300'"
      title="Toggle breakout rooms"
    >
      <i class="fas fa-th-large"></i>
    </button>

    <!-- Virtual Background -->
    <button
      @click="$emit('toggle-virtual-background')"
      class="p-3 rounded-full"
      :class="isVirtualBackgroundEnabled ? 'bg-indigo-100 text-indigo-600 dark:bg-indigo-900 dark:text-indigo-300' : 'bg-gray-100 text-gray-700 dark:bg-gray-700 dark:text-gray-300'"
      title="Toggle virtual background"
    >
      <i class="fas fa-image"></i>
    </button>

    <!-- Whiteboard -->
    <button
      @click="$emit('toggle-whiteboard')"
      class="p-3 rounded-full"
      :class="isWhiteboardEnabled ? 'bg-pink-100 text-pink-600 dark:bg-pink-900 dark:text-pink-300' : 'bg-gray-100 text-gray-700 dark:bg-gray-700 dark:text-gray-300'"
      title="Toggle whiteboard"
    >
      <i class="fas fa-edit"></i>
    </button>

    <!-- Recording -->
    <button
      @click="$emit('toggle-recording')"
      class="p-3 rounded-full"
      :class="isRecordingEnabled ? (isRecording ? 'bg-red-100 text-red-600 dark:bg-red-900 dark:text-red-300 animate-pulse' : 'bg-green-100 text-green-600 dark:bg-green-900 dark:text-green-300') : 'bg-gray-100 text-gray-700 dark:bg-gray-700 dark:text-gray-300'"
      :title="isRecordingEnabled ? (isRecording ? 'Recording in progress' : 'Stop recording') : 'Start recording'"
    >
      <i :class="isRecording ? 'fas fa-stop-circle' : 'fas fa-record-vinyl'"></i>
    </button>

    <!-- Join Requests Button (Admin only) -->
    <button
      v-if="hasJoinRequests"
      @click="$emit('toggle-join-requests')"
      class="p-3 rounded-full relative"
      :class="showJoinRequestsPanel ? 'bg-primary-light text-white' : 'bg-gray-100 text-gray-700 dark:bg-gray-700 dark:text-gray-300'"
      title="Manage join requests"
    >
      <i class="fas fa-user-plus"></i>
      <span class="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center">
        {{ joinRequestsCount }}
      </span>
    </button>

    <!-- Invite Button -->
    <button
      v-if="canShareLink"
      @click="$emit('show-invite')"
      class="p-3 rounded-full bg-blue-100 text-blue-600 dark:bg-blue-900 dark:text-blue-300 hover:bg-blue-200 dark:hover:bg-blue-800 transition-colors"
      title="Invite participants"
    >
      <i class="fas fa-user-plus"></i>
    </button>

    <!-- Link Sharing Management Button (Host only) -->
    <button
      v-if="isAdmin"
      @click="$emit('show-link-sharing')"
      class="p-3 rounded-full relative"
      :class="linkSharingPolicy === 'all' ? 'bg-green-100 text-green-600 dark:bg-green-900 dark:text-green-300' : 'bg-orange-100 text-orange-600 dark:bg-orange-900 dark:text-orange-300'"
      :title="linkSharingPolicy === 'all' ? 'Link sharing: All members' : 'Link sharing: Host only'"
    >
      <i class="fas fa-share-alt"></i>
      <span class="absolute -bottom-1 -right-1 text-xs">
        <i v-if="linkSharingPolicy === 'all'" class="fas fa-users text-green-500"></i>
        <i v-else class="fas fa-crown text-orange-500"></i>
      </span>
    </button>

    <button
      @click="$emit('leave-meeting')"
      class="p-3 rounded-full bg-red-100 text-red-600 dark:bg-red-900 dark:text-red-300"
      title="Leave meeting"
    >
      <i class="fas fa-sign-out-alt"></i>
    </button>
  </div>
</template>

<script setup>
import { defineProps, defineEmits } from 'vue'

defineProps({
  isMicMuted: {
    type: Boolean,
    default: false
  },
  isCameraOff: {
    type: Boolean,
    default: false
  },
  isScreenSharing: {
    type: Boolean,
    default: false
  },
  isChatOpen: {
    type: Boolean,
    default: false
  },
  isAISEnabled: {
    type: Boolean,
    default: false
  },
  isReactionsEnabled: {
    type: Boolean,
    default: false
  },
  isBreakoutRoomsEnabled: {
    type: Boolean,
    default: false
  },
  isVirtualBackgroundEnabled: {
    type: Boolean,
    default: false
  },
  isWhiteboardEnabled: {
    type: Boolean,
    default: false
  },
  isRecordingEnabled: {
    type: Boolean,
    default: false
  },
  isRecording: {
    type: Boolean,
    default: false
  },
  hasJoinRequests: {
    type: Boolean,
    default: false
  },
  showJoinRequestsPanel: {
    type: Boolean,
    default: false
  },
  joinRequestsCount: {
    type: Number,
    default: 0
  },
  isAdmin: {
    type: Boolean,
    default: false
  },
  canShareLink: {
    type: Boolean,
    default: false
  },
  linkSharingPolicy: {
    type: String,
    default: 'host'
  }
})

defineEmits([
  'toggle-mic',
  'toggle-camera',
  'toggle-screen',
  'toggle-chat',
  'toggle-ais',
  'toggle-reactions',
  'toggle-breakout-rooms',
  'toggle-virtual-background',
  'toggle-whiteboard',
  'toggle-recording',
  'toggle-join-requests',
  'show-invite',
  'show-link-sharing',
  'leave-meeting'
])
</script>
