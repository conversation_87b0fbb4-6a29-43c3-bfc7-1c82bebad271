import{q as x,u as g,x as l,y as o,R as p,W as m,A as d,D as u,C as w}from"./index-DwK_bd6V.js";const L=x("extensions",{state:()=>({extensions:[],isLoading:!1,error:null}),getters:{getMarketplaceExtensions:t=>t.extensions.filter(e=>e.is_public),getExtensionById:t=>e=>t.extensions.find(s=>s.id===e),getUserExtensions:t=>e=>e?t.extensions.filter(s=>{var i;return((i=s.dev_metadata)==null?void 0:i.author_id)===e}):[]},actions:{async fetchExtensions(){this.isLoading=!0,this.error=null;try{const t=await u(d(o,"extensions"));this.extensions=t.docs.map(e=>({id:e.id,...e.data()})),console.log("Loaded extensions from Firestore:",this.extensions.length)}catch(t){this.error=t.message,this.extensions=[]}finally{this.isLoading=!1}},async incrementDownloadCount(t){const e=this.extensions.find(s=>s.id===t);return e?(await new Promise(s=>setTimeout(s,50)),e.downloads=(e.downloads||0)+1,console.log(`Download count for ${t} incremented to ${e.downloads}`),!0):(console.error(`Extension ID ${t} not found for download count increment.`),!1)},async deleteExtension(t){this.isLoading=!0,this.error=null;try{return await w(l(o,"extensions",t)),this.extensions=this.extensions.filter(e=>e.id!==t),this.isLoading=!1,!0}catch(e){return this.error=e.message,this.isLoading=!1,!1}},async saveExtension(t){this.isLoading=!0;const e=g();if(!e.user)return this.error="User not authenticated. Cannot save extension.",this.isLoading=!1,null;try{const s=new Date().toISOString();let i,c=!1,a=t.id,r={...t.dev_metadata||{},author_id:e.user.id,author_name:e.user.name||"Unknown Developer",author_pic:e.user.picture||`https://i.pravatar.cc/40?u=${e.user.id}`,updated_at:s};if(a)i=l(o,"extensions",a),await p(i,{...t,dev_metadata:r,updated_at:s}),c=!0;else{r.created_at=s;const n=await m(d(o,"extensions"),{...t,dev_metadata:r,downloads:0,is_public:t.is_public===void 0?!1:t.is_public,created_at:s,updated_at:s});a=n.id,i=n}const h=await u(d(o,"extensions"));this.extensions=h.docs.map(n=>({id:n.id,...n.data()}));const f=this.extensions.find(n=>n.id===a)||null;return this.isLoading=!1,f}catch(s){return this.error=s.message,this.isLoading=!1,null}}}});export{L as u};
