import { createRouter, createWebHistory } from 'vue-router'
import { useAuthStore } from '../store/authStore' // Import the auth store

const routes = [
  {
    path: '/',
    name: 'Home',
    component: () => import('../views/Home.vue')
  },
  {
    path: '/meeting/:id',
    name: 'Meeting',
    component: () => import('../views/Meeting.vue')
  },
  {
    path: '/join',
    name: 'Join',
    component: () => import('../views/Join.vue')
  },
  // Extension System Routes
  {
    path: '/dashboard',
    name: 'Dashboard',
    component: () => import('../views/extensions/DashboardView.vue'),
    meta: { requiresDev: true } // Dashboard is typically for devs
  },
  {
    path: '/extensions',
    name: 'ExtensionsList',
    component: () => import('../views/extensions/ExtensionsListView.vue') // Accessible to all for viewing
  },
  {
    path: '/extensions/:id',
    name: 'ExtensionDetail',
    component: () => import('../views/extensions/ExtensionDetailView.vue'),
    props: true // Accessible to all for viewing
  },
  {
    path: '/extensions/create',
    name: 'ExtensionCreate',
    component: () => import('../views/extensions/ExtensionCreateView.vue'),
    meta: { requiresDev: true }
  },
  {
    path: '/extensions/editor/:id',
    name: 'ExtensionEditor',
    component: () => import('../views/extensions/ExtensionEditorView.vue'),
    props: true,
    meta: { requiresDev: true }
  },
  {
    path: '/marketplace',
    name: 'Marketplace',
    component: () => import('../views/extensions/MarketplaceView.vue') // Accessible to all
  },
  {
    path: '/profile/:username',
    name: 'UserProfile',
    component: () => import('../views/extensions/UserProfileView.vue'),
    props: true // Accessible to all
  },
  {
    path: '/analytics/:id',
    name: 'Analytics',
    component: () => import('../views/extensions/AnalyticsView.vue'),
    props: true,
    meta: { requiresDev: true }
  },
  {
    path: '/auth/login',
    name: 'Login',
    component: () => import('../views/auth/LoginView.vue')
  },
  // End Extension System Routes
  {
    path: '/:pathMatch(.*)*',
    name: 'NotFound',
    component: () => import('../views/NotFound.vue')
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes
})

// Navigation Guard
router.beforeEach((to, from, next) => {
  const authStore = useAuthStore();

  // Check if the route requires developer access
  if (to.meta.requiresDev) {
    if (authStore.isDev) {
      // User is a dev, allow access
      next();
    } else {
      // User is not a dev, redirect to login
      authStore.setIntendedRoute(to.fullPath); // Store the intended route
      next({ name: 'Login' });
    }
  } else {
    // Route does not require dev access, allow everyone
    next();
  }
});

export default router
