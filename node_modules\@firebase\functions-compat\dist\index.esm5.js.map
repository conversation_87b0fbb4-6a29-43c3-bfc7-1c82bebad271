{"version": 3, "file": "index.esm5.js", "sources": ["../src/service.ts", "../src/register.ts", "../src/index.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { FirebaseFunctions, HttpsCallable } from '@firebase/functions-types';\nimport {\n  httpsCallable as httpsCallableExp,\n  httpsCallableFromURL as httpsCallableFromURLExp,\n  connectFunctionsEmulator as useFunctionsEmulatorExp,\n  HttpsCallableOptions,\n  Functions as FunctionsServiceExp\n} from '@firebase/functions';\nimport { FirebaseApp, _FirebaseService } from '@firebase/app-compat';\nimport { FirebaseError } from '@firebase/util';\n\nexport class FunctionsService implements FirebaseFunctions, _FirebaseService {\n  /**\n   * For testing.\n   * @internal\n   */\n  _region: string;\n  /**\n   * For testing.\n   * @internal\n   */\n  _customDomain: string | null;\n\n  constructor(\n    public app: FirebaseApp,\n    readonly _delegate: FunctionsServiceExp\n  ) {\n    this._region = this._delegate.region;\n    this._customDomain = this._delegate.customDomain;\n  }\n  httpsCallable(name: string, options?: HttpsCallableOptions): HttpsCallable {\n    return httpsCallableExp(this._delegate, name, options);\n  }\n  httpsCallableFromURL(\n    url: string,\n    options?: HttpsCallableOptions\n  ): HttpsCallable {\n    return httpsCallableFromURLExp(this._delegate, url, options);\n  }\n  /**\n   * Deprecated in pre-modularized repo, does not exist in modularized\n   * functions package, need to convert to \"host\" and \"port\" args that\n   * `useFunctionsEmulatorExp` takes.\n   * @deprecated\n   */\n  useFunctionsEmulator(origin: string): void {\n    const match = origin.match('[a-zA-Z]+://([a-zA-Z0-9.-]+)(?::([0-9]+))?');\n    if (match == null) {\n      throw new FirebaseError(\n        'functions',\n        'No origin provided to useFunctionsEmulator()'\n      );\n    }\n    if (match[2] == null) {\n      throw new FirebaseError(\n        'functions',\n        'Port missing in origin provided to useFunctionsEmulator()'\n      );\n    }\n    return useFunctionsEmulatorExp(this._delegate, match[1], Number(match[2]));\n  }\n  useEmulator(host: string, port: number): void {\n    return useFunctionsEmulatorExp(this._delegate, host, port);\n  }\n}\n", "/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport firebase, { _FirebaseNamespace } from '@firebase/app-compat';\nimport { FunctionsService } from './service';\nimport {\n  Component,\n  ComponentType,\n  InstanceFactory,\n  ComponentContainer,\n  InstanceFactoryOptions\n} from '@firebase/component';\n\nconst DEFAULT_REGION = 'us-central1';\n\nconst factory: InstanceFactory<'functions-compat'> = (\n  container: ComponentContainer,\n  { instanceIdentifier: regionOrCustomDomain }: InstanceFactoryOptions\n) => {\n  // Dependencies\n  const app = container.getProvider('app-compat').getImmediate();\n  const functionsServiceExp = container.getProvider('functions').getImmediate({\n    identifier: regionOrCustomDomain ?? DEFAULT_REGION\n  });\n\n  return new FunctionsService(app, functionsServiceExp);\n};\n\nexport function registerFunctions(): void {\n  const namespaceExports = {\n    Functions: FunctionsService\n  };\n  (firebase as _FirebaseNamespace).INTERNAL.registerComponent(\n    new Component('functions-compat', factory, ComponentType.PUBLIC)\n      .setServiceProps(namespaceExports)\n      .setMultipleInstances(true)\n  );\n}\n", "/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport firebase from '@firebase/app-compat';\nimport { name, version } from '../package.json';\nimport { registerFunctions } from './register';\nimport * as types from '@firebase/functions-types';\n\nregisterFunctions();\nfirebase.registerVersion(name, version);\n\ndeclare module '@firebase/app-compat' {\n  interface FirebaseNamespace {\n    functions: {\n      (app?: FirebaseApp): types.FirebaseFunctions;\n      Functions: typeof types.FirebaseFunctions;\n    };\n  }\n  interface FirebaseApp {\n    functions(regionOrCustomDomain?: string): types.FirebaseFunctions;\n  }\n}\n"], "names": ["httpsCallableExp", "httpsCallableFromURLExp", "useFunctionsEmulatorExp"], "mappings": ";;;;;;;;AAAA;;;;;;;;;;;;;;;AAeG;AAaH,IAAA,gBAAA,kBAAA,YAAA;IAYE,SACS,gBAAA,CAAA,GAAgB,EACd,SAA8B,EAAA;QADhC,IAAG,CAAA,GAAA,GAAH,GAAG,CAAa;QACd,IAAS,CAAA,SAAA,GAAT,SAAS,CAAqB;QAEvC,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC;QACrC,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,SAAS,CAAC,YAAY,CAAC;KAClD;AACD,IAAA,gBAAA,CAAA,SAAA,CAAA,aAAa,GAAb,UAAc,IAAY,EAAE,OAA8B,EAAA;QACxD,OAAOA,aAAgB,CAAC,IAAI,CAAC,SAAS,EAAE,IAAI,EAAE,OAAO,CAAC,CAAC;KACxD,CAAA;AACD,IAAA,gBAAA,CAAA,SAAA,CAAA,oBAAoB,GAApB,UACE,GAAW,EACX,OAA8B,EAAA;QAE9B,OAAOC,oBAAuB,CAAC,IAAI,CAAC,SAAS,EAAE,GAAG,EAAE,OAAO,CAAC,CAAC;KAC9D,CAAA;AACD;;;;;AAKG;IACH,gBAAoB,CAAA,SAAA,CAAA,oBAAA,GAApB,UAAqB,MAAc,EAAA;QACjC,IAAM,KAAK,GAAG,MAAM,CAAC,KAAK,CAAC,4CAA4C,CAAC,CAAC;QACzE,IAAI,KAAK,IAAI,IAAI,EAAE;AACjB,YAAA,MAAM,IAAI,aAAa,CACrB,WAAW,EACX,8CAA8C,CAC/C,CAAC;AACH,SAAA;AACD,QAAA,IAAI,KAAK,CAAC,CAAC,CAAC,IAAI,IAAI,EAAE;AACpB,YAAA,MAAM,IAAI,aAAa,CACrB,WAAW,EACX,2DAA2D,CAC5D,CAAC;AACH,SAAA;AACD,QAAA,OAAOC,wBAAuB,CAAC,IAAI,CAAC,SAAS,EAAE,KAAK,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;KAC5E,CAAA;AACD,IAAA,gBAAA,CAAA,SAAA,CAAA,WAAW,GAAX,UAAY,IAAY,EAAE,IAAY,EAAA;QACpC,OAAOA,wBAAuB,CAAC,IAAI,CAAC,SAAS,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;KAC5D,CAAA;IACH,OAAC,gBAAA,CAAA;AAAD,CAAC,EAAA,CAAA;;ACjFD;;;;;;;;;;;;;;;AAeG;AAYH,IAAM,cAAc,GAAG,aAAa,CAAC;AAErC,IAAM,OAAO,GAAwC,UACnD,SAA6B,EAC7B,EAAoE,EAAA;AAA9C,IAAA,IAAA,oBAAoB,GAAA,EAAA,CAAA,kBAAA,CAAA;;IAG1C,IAAM,GAAG,GAAG,SAAS,CAAC,WAAW,CAAC,YAAY,CAAC,CAAC,YAAY,EAAE,CAAC;IAC/D,IAAM,mBAAmB,GAAG,SAAS,CAAC,WAAW,CAAC,WAAW,CAAC,CAAC,YAAY,CAAC;AAC1E,QAAA,UAAU,EAAE,oBAAoB,KAAA,IAAA,IAApB,oBAAoB,KAApB,KAAA,CAAA,GAAA,oBAAoB,GAAI,cAAc;AACnD,KAAA,CAAC,CAAC;AAEH,IAAA,OAAO,IAAI,gBAAgB,CAAC,GAAG,EAAE,mBAAmB,CAAC,CAAC;AACxD,CAAC,CAAC;SAEc,iBAAiB,GAAA;AAC/B,IAAA,IAAM,gBAAgB,GAAG;AACvB,QAAA,SAAS,EAAE,gBAAgB;KAC5B,CAAC;IACD,QAA+B,CAAC,QAAQ,CAAC,iBAAiB,CACzD,IAAI,SAAS,CAAC,kBAAkB,EAAE,OAAO,EAAuB,QAAA,4BAAA;SAC7D,eAAe,CAAC,gBAAgB,CAAC;AACjC,SAAA,oBAAoB,CAAC,IAAI,CAAC,CAC9B,CAAC;AACJ;;ACnDA;;;;;;;;;;;;;;;AAeG;AAOH,iBAAiB,EAAE,CAAC;AACpB,QAAQ,CAAC,eAAe,CAAC,IAAI,EAAE,OAAO,CAAC"}