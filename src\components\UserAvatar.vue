<template>
  <div class="relative flex items-center justify-center" :class="containerClasses">
    <!-- Main avatar -->
    <div class="relative z-10">
      <svg 
        :class="avatarClasses"
        fill="currentColor" 
        viewBox="0 0 24 24"
        :aria-label="ariaLabel"
        role="img"
      >
        <circle cx="12" cy="8" r="3"/>
        <path d="M12 12c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z"/>
      </svg>
    </div>
    
    <!-- Broadcasting waves animation (only when broadcasting) -->
    <div v-if="isBroadcasting" class="absolute inset-0 flex items-center justify-center">
      <div 
        v-for="(wave, index) in waves" 
        :key="index"
        class="absolute border-2 border-current rounded-full animate-ping"
        :class="wave.classes"
        :style="wave.style"
      ></div>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'

const props = defineProps({
  size: {
    type: String,
    default: 'md',
    validator: (value) => ['sm', 'md', 'lg', 'xl'].includes(value)
  },
  isBroadcasting: {
    type: Boolean,
    default: false
  },
  userName: {
    type: String,
    default: 'User'
  },
  color: {
    type: String,
    default: 'text-white'
  }
})

// Size configurations
const sizeConfig = {
  sm: {
    container: 'w-12 h-12',
    avatar: 'w-8 h-8',
    waves: [
      { size: 'w-16 h-16', opacity: 'opacity-30', delay: '0s' },
      { size: 'w-20 h-20', opacity: 'opacity-20', delay: '0.5s' },
      { size: 'w-24 h-24', opacity: 'opacity-10', delay: '1s' }
    ]
  },
  md: {
    container: 'w-16 h-16',
    avatar: 'w-12 h-12',
    waves: [
      { size: 'w-20 h-20', opacity: 'opacity-30', delay: '0s' },
      { size: 'w-24 h-24', opacity: 'opacity-20', delay: '0.5s' },
      { size: 'w-28 h-28', opacity: 'opacity-10', delay: '1s' }
    ]
  },
  lg: {
    container: 'w-24 h-24',
    avatar: 'w-16 h-16',
    waves: [
      { size: 'w-32 h-32', opacity: 'opacity-30', delay: '0s' },
      { size: 'w-40 h-40', opacity: 'opacity-20', delay: '0.5s' },
      { size: 'w-48 h-48', opacity: 'opacity-10', delay: '1s' }
    ]
  },
  xl: {
    container: 'w-32 h-32',
    avatar: 'w-24 h-24',
    waves: [
      { size: 'w-40 h-40', opacity: 'opacity-30', delay: '0s' },
      { size: 'w-48 h-48', opacity: 'opacity-20', delay: '0.5s' },
      { size: 'w-56 h-56', opacity: 'opacity-10', delay: '1s' }
    ]
  }
}

// Computed classes and styles
const containerClasses = computed(() => [
  sizeConfig[props.size].container,
  props.color
])

const avatarClasses = computed(() => [
  sizeConfig[props.size].avatar
])

const waves = computed(() => {
  if (!props.isBroadcasting) return []
  
  return sizeConfig[props.size].waves.map(wave => ({
    classes: [wave.size, wave.opacity],
    style: {
      animationDelay: wave.delay,
      animationDuration: '2s'
    }
  }))
})

const ariaLabel = computed(() => {
  const baseLabel = `${props.userName} avatar`
  return props.isBroadcasting ? `${baseLabel} - broadcasting` : baseLabel
})
</script>

<style scoped>
/* Custom animation for broadcasting waves */
@keyframes broadcasting-pulse {
  0% {
    transform: scale(0.8);
    opacity: 1;
  }
  100% {
    transform: scale(1.2);
    opacity: 0;
  }
}

.animate-ping {
  animation: broadcasting-pulse 2s cubic-bezier(0, 0, 0.2, 1) infinite;
}
</style>
