import{_ as a,u as t,a as s,b as e,l as n,m as l}from"./index-2cGdSAEs.js";const r={class:"login-container"},i={__name:"LoginView",setup(c){return n(),t(),(_,o)=>(l(),s("div",r,o[0]||(o[0]=[e("h1",null,"Developer Login",-1),e("p",null,"Please log in to access developer tools and extension management.",-1),e("div",{class:"text-gray-600 dark:text-gray-300 mt-6"}," Developer login is only available with real Google or Email/Password authentication. ",-1)])))}},u=a(i,[["__scopeId","data-v-89757fe4"]]);export{u as default};
