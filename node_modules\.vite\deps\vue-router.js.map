{"version": 3, "sources": ["../../vue-router/dist/vue-router.mjs"], "sourcesContent": ["/*!\r\n  * vue-router v4.5.1\r\n  * (c) 2025 <PERSON>\r\n  * @license MIT\r\n  */\r\nimport { getCurrentInstance, inject, onUnmounted, onDeactivated, onActivated, computed, unref, watchEffect, defineComponent, reactive, h, provide, ref, watch, shallowRef, shallowReactive, nextTick } from 'vue';\r\nimport { setupDevtoolsPlugin } from '@vue/devtools-api';\r\n\r\nconst isBrowser = typeof document !== 'undefined';\r\n\r\n/**\r\n * Allows differentiating lazy components from functional components and vue-class-component\r\n * @internal\r\n *\r\n * @param component\r\n */\r\nfunction isRouteComponent(component) {\r\n    return (typeof component === 'object' ||\r\n        'displayName' in component ||\r\n        'props' in component ||\r\n        '__vccOpts' in component);\r\n}\r\nfunction isESModule(obj) {\r\n    return (obj.__esModule ||\r\n        obj[Symbol.toStringTag] === 'Module' ||\r\n        // support CF with dynamic imports that do not\r\n        // add the Module string tag\r\n        (obj.default && isRouteComponent(obj.default)));\r\n}\r\nconst assign = Object.assign;\r\nfunction applyToParams(fn, params) {\r\n    const newParams = {};\r\n    for (const key in params) {\r\n        const value = params[key];\r\n        newParams[key] = isArray(value)\r\n            ? value.map(fn)\r\n            : fn(value);\r\n    }\r\n    return newParams;\r\n}\r\nconst noop = () => { };\r\n/**\r\n * Typesafe alternative to Array.isArray\r\n * https://github.com/microsoft/TypeScript/pull/48228\r\n */\r\nconst isArray = Array.isArray;\r\n\r\nfunction warn(msg) {\r\n    // avoid using ...args as it breaks in older Edge builds\r\n    const args = Array.from(arguments).slice(1);\r\n    console.warn.apply(console, ['[Vue Router warn]: ' + msg].concat(args));\r\n}\r\n\r\n/**\r\n * Encoding Rules (␣ = Space)\r\n * - Path: ␣ \" < > # ? { }\r\n * - Query: ␣ \" < > # & =\r\n * - Hash: ␣ \" < > `\r\n *\r\n * On top of that, the RFC3986 (https://tools.ietf.org/html/rfc3986#section-2.2)\r\n * defines some extra characters to be encoded. Most browsers do not encode them\r\n * in encodeURI https://github.com/whatwg/url/issues/369, so it may be safer to\r\n * also encode `!'()*`. Leaving un-encoded only ASCII alphanumeric(`a-zA-Z0-9`)\r\n * plus `-._~`. This extra safety should be applied to query by patching the\r\n * string returned by encodeURIComponent encodeURI also encodes `[\\]^`. `\\`\r\n * should be encoded to avoid ambiguity. Browsers (IE, FF, C) transform a `\\`\r\n * into a `/` if directly typed in. The _backtick_ (`````) should also be\r\n * encoded everywhere because some browsers like FF encode it when directly\r\n * written while others don't. Safari and IE don't encode ``\"<>{}``` in hash.\r\n */\r\n// const EXTRA_RESERVED_RE = /[!'()*]/g\r\n// const encodeReservedReplacer = (c: string) => '%' + c.charCodeAt(0).toString(16)\r\nconst HASH_RE = /#/g; // %23\r\nconst AMPERSAND_RE = /&/g; // %26\r\nconst SLASH_RE = /\\//g; // %2F\r\nconst EQUAL_RE = /=/g; // %3D\r\nconst IM_RE = /\\?/g; // %3F\r\nconst PLUS_RE = /\\+/g; // %2B\r\n/**\r\n * NOTE: It's not clear to me if we should encode the + symbol in queries, it\r\n * seems to be less flexible than not doing so and I can't find out the legacy\r\n * systems requiring this for regular requests like text/html. In the standard,\r\n * the encoding of the plus character is only mentioned for\r\n * application/x-www-form-urlencoded\r\n * (https://url.spec.whatwg.org/#urlencoded-parsing) and most browsers seems lo\r\n * leave the plus character as is in queries. To be more flexible, we allow the\r\n * plus character on the query, but it can also be manually encoded by the user.\r\n *\r\n * Resources:\r\n * - https://url.spec.whatwg.org/#urlencoded-parsing\r\n * - https://stackoverflow.com/questions/1634271/url-encoding-the-space-character-or-20\r\n */\r\nconst ENC_BRACKET_OPEN_RE = /%5B/g; // [\r\nconst ENC_BRACKET_CLOSE_RE = /%5D/g; // ]\r\nconst ENC_CARET_RE = /%5E/g; // ^\r\nconst ENC_BACKTICK_RE = /%60/g; // `\r\nconst ENC_CURLY_OPEN_RE = /%7B/g; // {\r\nconst ENC_PIPE_RE = /%7C/g; // |\r\nconst ENC_CURLY_CLOSE_RE = /%7D/g; // }\r\nconst ENC_SPACE_RE = /%20/g; // }\r\n/**\r\n * Encode characters that need to be encoded on the path, search and hash\r\n * sections of the URL.\r\n *\r\n * @internal\r\n * @param text - string to encode\r\n * @returns encoded string\r\n */\r\nfunction commonEncode(text) {\r\n    return encodeURI('' + text)\r\n        .replace(ENC_PIPE_RE, '|')\r\n        .replace(ENC_BRACKET_OPEN_RE, '[')\r\n        .replace(ENC_BRACKET_CLOSE_RE, ']');\r\n}\r\n/**\r\n * Encode characters that need to be encoded on the hash section of the URL.\r\n *\r\n * @param text - string to encode\r\n * @returns encoded string\r\n */\r\nfunction encodeHash(text) {\r\n    return commonEncode(text)\r\n        .replace(ENC_CURLY_OPEN_RE, '{')\r\n        .replace(ENC_CURLY_CLOSE_RE, '}')\r\n        .replace(ENC_CARET_RE, '^');\r\n}\r\n/**\r\n * Encode characters that need to be encoded query values on the query\r\n * section of the URL.\r\n *\r\n * @param text - string to encode\r\n * @returns encoded string\r\n */\r\nfunction encodeQueryValue(text) {\r\n    return (commonEncode(text)\r\n        // Encode the space as +, encode the + to differentiate it from the space\r\n        .replace(PLUS_RE, '%2B')\r\n        .replace(ENC_SPACE_RE, '+')\r\n        .replace(HASH_RE, '%23')\r\n        .replace(AMPERSAND_RE, '%26')\r\n        .replace(ENC_BACKTICK_RE, '`')\r\n        .replace(ENC_CURLY_OPEN_RE, '{')\r\n        .replace(ENC_CURLY_CLOSE_RE, '}')\r\n        .replace(ENC_CARET_RE, '^'));\r\n}\r\n/**\r\n * Like `encodeQueryValue` but also encodes the `=` character.\r\n *\r\n * @param text - string to encode\r\n */\r\nfunction encodeQueryKey(text) {\r\n    return encodeQueryValue(text).replace(EQUAL_RE, '%3D');\r\n}\r\n/**\r\n * Encode characters that need to be encoded on the path section of the URL.\r\n *\r\n * @param text - string to encode\r\n * @returns encoded string\r\n */\r\nfunction encodePath(text) {\r\n    return commonEncode(text).replace(HASH_RE, '%23').replace(IM_RE, '%3F');\r\n}\r\n/**\r\n * Encode characters that need to be encoded on the path section of the URL as a\r\n * param. This function encodes everything {@link encodePath} does plus the\r\n * slash (`/`) character. If `text` is `null` or `undefined`, returns an empty\r\n * string instead.\r\n *\r\n * @param text - string to encode\r\n * @returns encoded string\r\n */\r\nfunction encodeParam(text) {\r\n    return text == null ? '' : encodePath(text).replace(SLASH_RE, '%2F');\r\n}\r\n/**\r\n * Decode text using `decodeURIComponent`. Returns the original text if it\r\n * fails.\r\n *\r\n * @param text - string to decode\r\n * @returns decoded string\r\n */\r\nfunction decode(text) {\r\n    try {\r\n        return decodeURIComponent('' + text);\r\n    }\r\n    catch (err) {\r\n        (process.env.NODE_ENV !== 'production') && warn(`Error decoding \"${text}\". Using original value`);\r\n    }\r\n    return '' + text;\r\n}\r\n\r\nconst TRAILING_SLASH_RE = /\\/$/;\r\nconst removeTrailingSlash = (path) => path.replace(TRAILING_SLASH_RE, '');\r\n/**\r\n * Transforms a URI into a normalized history location\r\n *\r\n * @param parseQuery\r\n * @param location - URI to normalize\r\n * @param currentLocation - current absolute location. Allows resolving relative\r\n * paths. Must start with `/`. Defaults to `/`\r\n * @returns a normalized history location\r\n */\r\nfunction parseURL(parseQuery, location, currentLocation = '/') {\r\n    let path, query = {}, searchString = '', hash = '';\r\n    // Could use URL and URLSearchParams but IE 11 doesn't support it\r\n    // TODO: move to new URL()\r\n    const hashPos = location.indexOf('#');\r\n    let searchPos = location.indexOf('?');\r\n    // the hash appears before the search, so it's not part of the search string\r\n    if (hashPos < searchPos && hashPos >= 0) {\r\n        searchPos = -1;\r\n    }\r\n    if (searchPos > -1) {\r\n        path = location.slice(0, searchPos);\r\n        searchString = location.slice(searchPos + 1, hashPos > -1 ? hashPos : location.length);\r\n        query = parseQuery(searchString);\r\n    }\r\n    if (hashPos > -1) {\r\n        path = path || location.slice(0, hashPos);\r\n        // keep the # character\r\n        hash = location.slice(hashPos, location.length);\r\n    }\r\n    // no search and no query\r\n    path = resolveRelativePath(path != null ? path : location, currentLocation);\r\n    // empty path means a relative query or hash `?foo=f`, `#thing`\r\n    return {\r\n        fullPath: path + (searchString && '?') + searchString + hash,\r\n        path,\r\n        query,\r\n        hash: decode(hash),\r\n    };\r\n}\r\n/**\r\n * Stringifies a URL object\r\n *\r\n * @param stringifyQuery\r\n * @param location\r\n */\r\nfunction stringifyURL(stringifyQuery, location) {\r\n    const query = location.query ? stringifyQuery(location.query) : '';\r\n    return location.path + (query && '?') + query + (location.hash || '');\r\n}\r\n/**\r\n * Strips off the base from the beginning of a location.pathname in a non-case-sensitive way.\r\n *\r\n * @param pathname - location.pathname\r\n * @param base - base to strip off\r\n */\r\nfunction stripBase(pathname, base) {\r\n    // no base or base is not found at the beginning\r\n    if (!base || !pathname.toLowerCase().startsWith(base.toLowerCase()))\r\n        return pathname;\r\n    return pathname.slice(base.length) || '/';\r\n}\r\n/**\r\n * Checks if two RouteLocation are equal. This means that both locations are\r\n * pointing towards the same {@link RouteRecord} and that all `params`, `query`\r\n * parameters and `hash` are the same\r\n *\r\n * @param stringifyQuery - A function that takes a query object of type LocationQueryRaw and returns a string representation of it.\r\n * @param a - first {@link RouteLocation}\r\n * @param b - second {@link RouteLocation}\r\n */\r\nfunction isSameRouteLocation(stringifyQuery, a, b) {\r\n    const aLastIndex = a.matched.length - 1;\r\n    const bLastIndex = b.matched.length - 1;\r\n    return (aLastIndex > -1 &&\r\n        aLastIndex === bLastIndex &&\r\n        isSameRouteRecord(a.matched[aLastIndex], b.matched[bLastIndex]) &&\r\n        isSameRouteLocationParams(a.params, b.params) &&\r\n        stringifyQuery(a.query) === stringifyQuery(b.query) &&\r\n        a.hash === b.hash);\r\n}\r\n/**\r\n * Check if two `RouteRecords` are equal. Takes into account aliases: they are\r\n * considered equal to the `RouteRecord` they are aliasing.\r\n *\r\n * @param a - first {@link RouteRecord}\r\n * @param b - second {@link RouteRecord}\r\n */\r\nfunction isSameRouteRecord(a, b) {\r\n    // since the original record has an undefined value for aliasOf\r\n    // but all aliases point to the original record, this will always compare\r\n    // the original record\r\n    return (a.aliasOf || a) === (b.aliasOf || b);\r\n}\r\nfunction isSameRouteLocationParams(a, b) {\r\n    if (Object.keys(a).length !== Object.keys(b).length)\r\n        return false;\r\n    for (const key in a) {\r\n        if (!isSameRouteLocationParamsValue(a[key], b[key]))\r\n            return false;\r\n    }\r\n    return true;\r\n}\r\nfunction isSameRouteLocationParamsValue(a, b) {\r\n    return isArray(a)\r\n        ? isEquivalentArray(a, b)\r\n        : isArray(b)\r\n            ? isEquivalentArray(b, a)\r\n            : a === b;\r\n}\r\n/**\r\n * Check if two arrays are the same or if an array with one single entry is the\r\n * same as another primitive value. Used to check query and parameters\r\n *\r\n * @param a - array of values\r\n * @param b - array of values or a single value\r\n */\r\nfunction isEquivalentArray(a, b) {\r\n    return isArray(b)\r\n        ? a.length === b.length && a.every((value, i) => value === b[i])\r\n        : a.length === 1 && a[0] === b;\r\n}\r\n/**\r\n * Resolves a relative path that starts with `.`.\r\n *\r\n * @param to - path location we are resolving\r\n * @param from - currentLocation.path, should start with `/`\r\n */\r\nfunction resolveRelativePath(to, from) {\r\n    if (to.startsWith('/'))\r\n        return to;\r\n    if ((process.env.NODE_ENV !== 'production') && !from.startsWith('/')) {\r\n        warn(`Cannot resolve a relative location without an absolute path. Trying to resolve \"${to}\" from \"${from}\". It should look like \"/${from}\".`);\r\n        return to;\r\n    }\r\n    if (!to)\r\n        return from;\r\n    const fromSegments = from.split('/');\r\n    const toSegments = to.split('/');\r\n    const lastToSegment = toSegments[toSegments.length - 1];\r\n    // make . and ./ the same (../ === .., ../../ === ../..)\r\n    // this is the same behavior as new URL()\r\n    if (lastToSegment === '..' || lastToSegment === '.') {\r\n        toSegments.push('');\r\n    }\r\n    let position = fromSegments.length - 1;\r\n    let toPosition;\r\n    let segment;\r\n    for (toPosition = 0; toPosition < toSegments.length; toPosition++) {\r\n        segment = toSegments[toPosition];\r\n        // we stay on the same position\r\n        if (segment === '.')\r\n            continue;\r\n        // go up in the from array\r\n        if (segment === '..') {\r\n            // we can't go below zero, but we still need to increment toPosition\r\n            if (position > 1)\r\n                position--;\r\n            // continue\r\n        }\r\n        // we reached a non-relative path, we stop here\r\n        else\r\n            break;\r\n    }\r\n    return (fromSegments.slice(0, position).join('/') +\r\n        '/' +\r\n        toSegments.slice(toPosition).join('/'));\r\n}\r\n/**\r\n * Initial route location where the router is. Can be used in navigation guards\r\n * to differentiate the initial navigation.\r\n *\r\n * @example\r\n * ```js\r\n * import { START_LOCATION } from 'vue-router'\r\n *\r\n * router.beforeEach((to, from) => {\r\n *   if (from === START_LOCATION) {\r\n *     // initial navigation\r\n *   }\r\n * })\r\n * ```\r\n */\r\nconst START_LOCATION_NORMALIZED = {\r\n    path: '/',\r\n    // TODO: could we use a symbol in the future?\r\n    name: undefined,\r\n    params: {},\r\n    query: {},\r\n    hash: '',\r\n    fullPath: '/',\r\n    matched: [],\r\n    meta: {},\r\n    redirectedFrom: undefined,\r\n};\r\n\r\nvar NavigationType;\r\n(function (NavigationType) {\r\n    NavigationType[\"pop\"] = \"pop\";\r\n    NavigationType[\"push\"] = \"push\";\r\n})(NavigationType || (NavigationType = {}));\r\nvar NavigationDirection;\r\n(function (NavigationDirection) {\r\n    NavigationDirection[\"back\"] = \"back\";\r\n    NavigationDirection[\"forward\"] = \"forward\";\r\n    NavigationDirection[\"unknown\"] = \"\";\r\n})(NavigationDirection || (NavigationDirection = {}));\r\n/**\r\n * Starting location for Histories\r\n */\r\nconst START = '';\r\n// Generic utils\r\n/**\r\n * Normalizes a base by removing any trailing slash and reading the base tag if\r\n * present.\r\n *\r\n * @param base - base to normalize\r\n */\r\nfunction normalizeBase(base) {\r\n    if (!base) {\r\n        if (isBrowser) {\r\n            // respect <base> tag\r\n            const baseEl = document.querySelector('base');\r\n            base = (baseEl && baseEl.getAttribute('href')) || '/';\r\n            // strip full URL origin\r\n            base = base.replace(/^\\w+:\\/\\/[^\\/]+/, '');\r\n        }\r\n        else {\r\n            base = '/';\r\n        }\r\n    }\r\n    // ensure leading slash when it was removed by the regex above avoid leading\r\n    // slash with hash because the file could be read from the disk like file://\r\n    // and the leading slash would cause problems\r\n    if (base[0] !== '/' && base[0] !== '#')\r\n        base = '/' + base;\r\n    // remove the trailing slash so all other method can just do `base + fullPath`\r\n    // to build an href\r\n    return removeTrailingSlash(base);\r\n}\r\n// remove any character before the hash\r\nconst BEFORE_HASH_RE = /^[^#]+#/;\r\nfunction createHref(base, location) {\r\n    return base.replace(BEFORE_HASH_RE, '#') + location;\r\n}\r\n\r\nfunction getElementPosition(el, offset) {\r\n    const docRect = document.documentElement.getBoundingClientRect();\r\n    const elRect = el.getBoundingClientRect();\r\n    return {\r\n        behavior: offset.behavior,\r\n        left: elRect.left - docRect.left - (offset.left || 0),\r\n        top: elRect.top - docRect.top - (offset.top || 0),\r\n    };\r\n}\r\nconst computeScrollPosition = () => ({\r\n    left: window.scrollX,\r\n    top: window.scrollY,\r\n});\r\nfunction scrollToPosition(position) {\r\n    let scrollToOptions;\r\n    if ('el' in position) {\r\n        const positionEl = position.el;\r\n        const isIdSelector = typeof positionEl === 'string' && positionEl.startsWith('#');\r\n        /**\r\n         * `id`s can accept pretty much any characters, including CSS combinators\r\n         * like `>` or `~`. It's still possible to retrieve elements using\r\n         * `document.getElementById('~')` but it needs to be escaped when using\r\n         * `document.querySelector('#\\\\~')` for it to be valid. The only\r\n         * requirements for `id`s are them to be unique on the page and to not be\r\n         * empty (`id=\"\"`). Because of that, when passing an id selector, it should\r\n         * be properly escaped for it to work with `querySelector`. We could check\r\n         * for the id selector to be simple (no CSS combinators `+ >~`) but that\r\n         * would make things inconsistent since they are valid characters for an\r\n         * `id` but would need to be escaped when using `querySelector`, breaking\r\n         * their usage and ending up in no selector returned. Selectors need to be\r\n         * escaped:\r\n         *\r\n         * - `#1-thing` becomes `#\\31 -thing`\r\n         * - `#with~symbols` becomes `#with\\\\~symbols`\r\n         *\r\n         * - More information about  the topic can be found at\r\n         *   https://mathiasbynens.be/notes/html5-id-class.\r\n         * - Practical example: https://mathiasbynens.be/demo/html5-id\r\n         */\r\n        if ((process.env.NODE_ENV !== 'production') && typeof position.el === 'string') {\r\n            if (!isIdSelector || !document.getElementById(position.el.slice(1))) {\r\n                try {\r\n                    const foundEl = document.querySelector(position.el);\r\n                    if (isIdSelector && foundEl) {\r\n                        warn(`The selector \"${position.el}\" should be passed as \"el: document.querySelector('${position.el}')\" because it starts with \"#\".`);\r\n                        // return to avoid other warnings\r\n                        return;\r\n                    }\r\n                }\r\n                catch (err) {\r\n                    warn(`The selector \"${position.el}\" is invalid. If you are using an id selector, make sure to escape it. You can find more information about escaping characters in selectors at https://mathiasbynens.be/notes/css-escapes or use CSS.escape (https://developer.mozilla.org/en-US/docs/Web/API/CSS/escape).`);\r\n                    // return to avoid other warnings\r\n                    return;\r\n                }\r\n            }\r\n        }\r\n        const el = typeof positionEl === 'string'\r\n            ? isIdSelector\r\n                ? document.getElementById(positionEl.slice(1))\r\n                : document.querySelector(positionEl)\r\n            : positionEl;\r\n        if (!el) {\r\n            (process.env.NODE_ENV !== 'production') &&\r\n                warn(`Couldn't find element using selector \"${position.el}\" returned by scrollBehavior.`);\r\n            return;\r\n        }\r\n        scrollToOptions = getElementPosition(el, position);\r\n    }\r\n    else {\r\n        scrollToOptions = position;\r\n    }\r\n    if ('scrollBehavior' in document.documentElement.style)\r\n        window.scrollTo(scrollToOptions);\r\n    else {\r\n        window.scrollTo(scrollToOptions.left != null ? scrollToOptions.left : window.scrollX, scrollToOptions.top != null ? scrollToOptions.top : window.scrollY);\r\n    }\r\n}\r\nfunction getScrollKey(path, delta) {\r\n    const position = history.state ? history.state.position - delta : -1;\r\n    return position + path;\r\n}\r\nconst scrollPositions = new Map();\r\nfunction saveScrollPosition(key, scrollPosition) {\r\n    scrollPositions.set(key, scrollPosition);\r\n}\r\nfunction getSavedScrollPosition(key) {\r\n    const scroll = scrollPositions.get(key);\r\n    // consume it so it's not used again\r\n    scrollPositions.delete(key);\r\n    return scroll;\r\n}\r\n// TODO: RFC about how to save scroll position\r\n/**\r\n * ScrollBehavior instance used by the router to compute and restore the scroll\r\n * position when navigating.\r\n */\r\n// export interface ScrollHandler<ScrollPositionEntry extends HistoryStateValue, ScrollPosition extends ScrollPositionEntry> {\r\n//   // returns a scroll position that can be saved in history\r\n//   compute(): ScrollPositionEntry\r\n//   // can take an extended ScrollPositionEntry\r\n//   scroll(position: ScrollPosition): void\r\n// }\r\n// export const scrollHandler: ScrollHandler<ScrollPosition> = {\r\n//   compute: computeScroll,\r\n//   scroll: scrollToPosition,\r\n// }\r\n\r\nlet createBaseLocation = () => location.protocol + '//' + location.host;\r\n/**\r\n * Creates a normalized history location from a window.location object\r\n * @param base - The base path\r\n * @param location - The window.location object\r\n */\r\nfunction createCurrentLocation(base, location) {\r\n    const { pathname, search, hash } = location;\r\n    // allows hash bases like #, /#, #/, #!, #!/, /#!/, or even /folder#end\r\n    const hashPos = base.indexOf('#');\r\n    if (hashPos > -1) {\r\n        let slicePos = hash.includes(base.slice(hashPos))\r\n            ? base.slice(hashPos).length\r\n            : 1;\r\n        let pathFromHash = hash.slice(slicePos);\r\n        // prepend the starting slash to hash so the url starts with /#\r\n        if (pathFromHash[0] !== '/')\r\n            pathFromHash = '/' + pathFromHash;\r\n        return stripBase(pathFromHash, '');\r\n    }\r\n    const path = stripBase(pathname, base);\r\n    return path + search + hash;\r\n}\r\nfunction useHistoryListeners(base, historyState, currentLocation, replace) {\r\n    let listeners = [];\r\n    let teardowns = [];\r\n    // TODO: should it be a stack? a Dict. Check if the popstate listener\r\n    // can trigger twice\r\n    let pauseState = null;\r\n    const popStateHandler = ({ state, }) => {\r\n        const to = createCurrentLocation(base, location);\r\n        const from = currentLocation.value;\r\n        const fromState = historyState.value;\r\n        let delta = 0;\r\n        if (state) {\r\n            currentLocation.value = to;\r\n            historyState.value = state;\r\n            // ignore the popstate and reset the pauseState\r\n            if (pauseState && pauseState === from) {\r\n                pauseState = null;\r\n                return;\r\n            }\r\n            delta = fromState ? state.position - fromState.position : 0;\r\n        }\r\n        else {\r\n            replace(to);\r\n        }\r\n        // Here we could also revert the navigation by calling history.go(-delta)\r\n        // this listener will have to be adapted to not trigger again and to wait for the url\r\n        // to be updated before triggering the listeners. Some kind of validation function would also\r\n        // need to be passed to the listeners so the navigation can be accepted\r\n        // call all listeners\r\n        listeners.forEach(listener => {\r\n            listener(currentLocation.value, from, {\r\n                delta,\r\n                type: NavigationType.pop,\r\n                direction: delta\r\n                    ? delta > 0\r\n                        ? NavigationDirection.forward\r\n                        : NavigationDirection.back\r\n                    : NavigationDirection.unknown,\r\n            });\r\n        });\r\n    };\r\n    function pauseListeners() {\r\n        pauseState = currentLocation.value;\r\n    }\r\n    function listen(callback) {\r\n        // set up the listener and prepare teardown callbacks\r\n        listeners.push(callback);\r\n        const teardown = () => {\r\n            const index = listeners.indexOf(callback);\r\n            if (index > -1)\r\n                listeners.splice(index, 1);\r\n        };\r\n        teardowns.push(teardown);\r\n        return teardown;\r\n    }\r\n    function beforeUnloadListener() {\r\n        const { history } = window;\r\n        if (!history.state)\r\n            return;\r\n        history.replaceState(assign({}, history.state, { scroll: computeScrollPosition() }), '');\r\n    }\r\n    function destroy() {\r\n        for (const teardown of teardowns)\r\n            teardown();\r\n        teardowns = [];\r\n        window.removeEventListener('popstate', popStateHandler);\r\n        window.removeEventListener('beforeunload', beforeUnloadListener);\r\n    }\r\n    // set up the listeners and prepare teardown callbacks\r\n    window.addEventListener('popstate', popStateHandler);\r\n    // TODO: could we use 'pagehide' or 'visibilitychange' instead?\r\n    // https://developer.chrome.com/blog/page-lifecycle-api/\r\n    window.addEventListener('beforeunload', beforeUnloadListener, {\r\n        passive: true,\r\n    });\r\n    return {\r\n        pauseListeners,\r\n        listen,\r\n        destroy,\r\n    };\r\n}\r\n/**\r\n * Creates a state object\r\n */\r\nfunction buildState(back, current, forward, replaced = false, computeScroll = false) {\r\n    return {\r\n        back,\r\n        current,\r\n        forward,\r\n        replaced,\r\n        position: window.history.length,\r\n        scroll: computeScroll ? computeScrollPosition() : null,\r\n    };\r\n}\r\nfunction useHistoryStateNavigation(base) {\r\n    const { history, location } = window;\r\n    // private variables\r\n    const currentLocation = {\r\n        value: createCurrentLocation(base, location),\r\n    };\r\n    const historyState = { value: history.state };\r\n    // build current history entry as this is a fresh navigation\r\n    if (!historyState.value) {\r\n        changeLocation(currentLocation.value, {\r\n            back: null,\r\n            current: currentLocation.value,\r\n            forward: null,\r\n            // the length is off by one, we need to decrease it\r\n            position: history.length - 1,\r\n            replaced: true,\r\n            // don't add a scroll as the user may have an anchor, and we want\r\n            // scrollBehavior to be triggered without a saved position\r\n            scroll: null,\r\n        }, true);\r\n    }\r\n    function changeLocation(to, state, replace) {\r\n        /**\r\n         * if a base tag is provided, and we are on a normal domain, we have to\r\n         * respect the provided `base` attribute because pushState() will use it and\r\n         * potentially erase anything before the `#` like at\r\n         * https://github.com/vuejs/router/issues/685 where a base of\r\n         * `/folder/#` but a base of `/` would erase the `/folder/` section. If\r\n         * there is no host, the `<base>` tag makes no sense and if there isn't a\r\n         * base tag we can just use everything after the `#`.\r\n         */\r\n        const hashIndex = base.indexOf('#');\r\n        const url = hashIndex > -1\r\n            ? (location.host && document.querySelector('base')\r\n                ? base\r\n                : base.slice(hashIndex)) + to\r\n            : createBaseLocation() + base + to;\r\n        try {\r\n            // BROWSER QUIRK\r\n            // NOTE: Safari throws a SecurityError when calling this function 100 times in 30 seconds\r\n            history[replace ? 'replaceState' : 'pushState'](state, '', url);\r\n            historyState.value = state;\r\n        }\r\n        catch (err) {\r\n            if ((process.env.NODE_ENV !== 'production')) {\r\n                warn('Error with push/replace State', err);\r\n            }\r\n            else {\r\n                console.error(err);\r\n            }\r\n            // Force the navigation, this also resets the call count\r\n            location[replace ? 'replace' : 'assign'](url);\r\n        }\r\n    }\r\n    function replace(to, data) {\r\n        const state = assign({}, history.state, buildState(historyState.value.back, \r\n        // keep back and forward entries but override current position\r\n        to, historyState.value.forward, true), data, { position: historyState.value.position });\r\n        changeLocation(to, state, true);\r\n        currentLocation.value = to;\r\n    }\r\n    function push(to, data) {\r\n        // Add to current entry the information of where we are going\r\n        // as well as saving the current position\r\n        const currentState = assign({}, \r\n        // use current history state to gracefully handle a wrong call to\r\n        // history.replaceState\r\n        // https://github.com/vuejs/router/issues/366\r\n        historyState.value, history.state, {\r\n            forward: to,\r\n            scroll: computeScrollPosition(),\r\n        });\r\n        if ((process.env.NODE_ENV !== 'production') && !history.state) {\r\n            warn(`history.state seems to have been manually replaced without preserving the necessary values. Make sure to preserve existing history state if you are manually calling history.replaceState:\\n\\n` +\r\n                `history.replaceState(history.state, '', url)\\n\\n` +\r\n                `You can find more information at https://router.vuejs.org/guide/migration/#Usage-of-history-state`);\r\n        }\r\n        changeLocation(currentState.current, currentState, true);\r\n        const state = assign({}, buildState(currentLocation.value, to, null), { position: currentState.position + 1 }, data);\r\n        changeLocation(to, state, false);\r\n        currentLocation.value = to;\r\n    }\r\n    return {\r\n        location: currentLocation,\r\n        state: historyState,\r\n        push,\r\n        replace,\r\n    };\r\n}\r\n/**\r\n * Creates an HTML5 history. Most common history for single page applications.\r\n *\r\n * @param base -\r\n */\r\nfunction createWebHistory(base) {\r\n    base = normalizeBase(base);\r\n    const historyNavigation = useHistoryStateNavigation(base);\r\n    const historyListeners = useHistoryListeners(base, historyNavigation.state, historyNavigation.location, historyNavigation.replace);\r\n    function go(delta, triggerListeners = true) {\r\n        if (!triggerListeners)\r\n            historyListeners.pauseListeners();\r\n        history.go(delta);\r\n    }\r\n    const routerHistory = assign({\r\n        // it's overridden right after\r\n        location: '',\r\n        base,\r\n        go,\r\n        createHref: createHref.bind(null, base),\r\n    }, historyNavigation, historyListeners);\r\n    Object.defineProperty(routerHistory, 'location', {\r\n        enumerable: true,\r\n        get: () => historyNavigation.location.value,\r\n    });\r\n    Object.defineProperty(routerHistory, 'state', {\r\n        enumerable: true,\r\n        get: () => historyNavigation.state.value,\r\n    });\r\n    return routerHistory;\r\n}\r\n\r\n/**\r\n * Creates an in-memory based history. The main purpose of this history is to handle SSR. It starts in a special location that is nowhere.\r\n * It's up to the user to replace that location with the starter location by either calling `router.push` or `router.replace`.\r\n *\r\n * @param base - Base applied to all urls, defaults to '/'\r\n * @returns a history object that can be passed to the router constructor\r\n */\r\nfunction createMemoryHistory(base = '') {\r\n    let listeners = [];\r\n    let queue = [[START, {}]];\r\n    let position = 0;\r\n    base = normalizeBase(base);\r\n    function setLocation(location, state = {}) {\r\n        position++;\r\n        if (position !== queue.length) {\r\n            // we are in the middle, we remove everything from here in the queue\r\n            queue.splice(position);\r\n        }\r\n        queue.push([location, state]);\r\n    }\r\n    function triggerListeners(to, from, { direction, delta }) {\r\n        const info = {\r\n            direction,\r\n            delta,\r\n            type: NavigationType.pop,\r\n        };\r\n        for (const callback of listeners) {\r\n            callback(to, from, info);\r\n        }\r\n    }\r\n    const routerHistory = {\r\n        // rewritten by Object.defineProperty\r\n        location: START,\r\n        // rewritten by Object.defineProperty\r\n        state: {},\r\n        base,\r\n        createHref: createHref.bind(null, base),\r\n        replace(to, state) {\r\n            // remove current entry and decrement position\r\n            queue.splice(position--, 1);\r\n            setLocation(to, state);\r\n        },\r\n        push(to, state) {\r\n            setLocation(to, state);\r\n        },\r\n        listen(callback) {\r\n            listeners.push(callback);\r\n            return () => {\r\n                const index = listeners.indexOf(callback);\r\n                if (index > -1)\r\n                    listeners.splice(index, 1);\r\n            };\r\n        },\r\n        destroy() {\r\n            listeners = [];\r\n            queue = [[START, {}]];\r\n            position = 0;\r\n        },\r\n        go(delta, shouldTrigger = true) {\r\n            const from = this.location;\r\n            const direction = \r\n            // we are considering delta === 0 going forward, but in abstract mode\r\n            // using 0 for the delta doesn't make sense like it does in html5 where\r\n            // it reloads the page\r\n            delta < 0 ? NavigationDirection.back : NavigationDirection.forward;\r\n            position = Math.max(0, Math.min(position + delta, queue.length - 1));\r\n            if (shouldTrigger) {\r\n                triggerListeners(this.location, from, {\r\n                    direction,\r\n                    delta,\r\n                });\r\n            }\r\n        },\r\n    };\r\n    Object.defineProperty(routerHistory, 'location', {\r\n        enumerable: true,\r\n        get: () => queue[position][0],\r\n    });\r\n    Object.defineProperty(routerHistory, 'state', {\r\n        enumerable: true,\r\n        get: () => queue[position][1],\r\n    });\r\n    return routerHistory;\r\n}\r\n\r\n/**\r\n * Creates a hash history. Useful for web applications with no host (e.g. `file://`) or when configuring a server to\r\n * handle any URL is not possible.\r\n *\r\n * @param base - optional base to provide. Defaults to `location.pathname + location.search` If there is a `<base>` tag\r\n * in the `head`, its value will be ignored in favor of this parameter **but note it affects all the history.pushState()\r\n * calls**, meaning that if you use a `<base>` tag, it's `href` value **has to match this parameter** (ignoring anything\r\n * after the `#`).\r\n *\r\n * @example\r\n * ```js\r\n * // at https://example.com/folder\r\n * createWebHashHistory() // gives a url of `https://example.com/folder#`\r\n * createWebHashHistory('/folder/') // gives a url of `https://example.com/folder/#`\r\n * // if the `#` is provided in the base, it won't be added by `createWebHashHistory`\r\n * createWebHashHistory('/folder/#/app/') // gives a url of `https://example.com/folder/#/app/`\r\n * // you should avoid doing this because it changes the original url and breaks copying urls\r\n * createWebHashHistory('/other-folder/') // gives a url of `https://example.com/other-folder/#`\r\n *\r\n * // at file:///usr/etc/folder/index.html\r\n * // for locations with no `host`, the base is ignored\r\n * createWebHashHistory('/iAmIgnored') // gives a url of `file:///usr/etc/folder/index.html#`\r\n * ```\r\n */\r\nfunction createWebHashHistory(base) {\r\n    // Make sure this implementation is fine in terms of encoding, specially for IE11\r\n    // for `file://`, directly use the pathname and ignore the base\r\n    // location.pathname contains an initial `/` even at the root: `https://example.com`\r\n    base = location.host ? base || location.pathname + location.search : '';\r\n    // allow the user to provide a `#` in the middle: `/base/#/app`\r\n    if (!base.includes('#'))\r\n        base += '#';\r\n    if ((process.env.NODE_ENV !== 'production') && !base.endsWith('#/') && !base.endsWith('#')) {\r\n        warn(`A hash base must end with a \"#\":\\n\"${base}\" should be \"${base.replace(/#.*$/, '#')}\".`);\r\n    }\r\n    return createWebHistory(base);\r\n}\r\n\r\nfunction isRouteLocation(route) {\r\n    return typeof route === 'string' || (route && typeof route === 'object');\r\n}\r\nfunction isRouteName(name) {\r\n    return typeof name === 'string' || typeof name === 'symbol';\r\n}\r\n\r\nconst NavigationFailureSymbol = Symbol((process.env.NODE_ENV !== 'production') ? 'navigation failure' : '');\r\n/**\r\n * Enumeration with all possible types for navigation failures. Can be passed to\r\n * {@link isNavigationFailure} to check for specific failures.\r\n */\r\nvar NavigationFailureType;\r\n(function (NavigationFailureType) {\r\n    /**\r\n     * An aborted navigation is a navigation that failed because a navigation\r\n     * guard returned `false` or called `next(false)`\r\n     */\r\n    NavigationFailureType[NavigationFailureType[\"aborted\"] = 4] = \"aborted\";\r\n    /**\r\n     * A cancelled navigation is a navigation that failed because a more recent\r\n     * navigation finished started (not necessarily finished).\r\n     */\r\n    NavigationFailureType[NavigationFailureType[\"cancelled\"] = 8] = \"cancelled\";\r\n    /**\r\n     * A duplicated navigation is a navigation that failed because it was\r\n     * initiated while already being at the exact same location.\r\n     */\r\n    NavigationFailureType[NavigationFailureType[\"duplicated\"] = 16] = \"duplicated\";\r\n})(NavigationFailureType || (NavigationFailureType = {}));\r\n// DEV only debug messages\r\nconst ErrorTypeMessages = {\r\n    [1 /* ErrorTypes.MATCHER_NOT_FOUND */]({ location, currentLocation }) {\r\n        return `No match for\\n ${JSON.stringify(location)}${currentLocation\r\n            ? '\\nwhile being at\\n' + JSON.stringify(currentLocation)\r\n            : ''}`;\r\n    },\r\n    [2 /* ErrorTypes.NAVIGATION_GUARD_REDIRECT */]({ from, to, }) {\r\n        return `Redirected from \"${from.fullPath}\" to \"${stringifyRoute(to)}\" via a navigation guard.`;\r\n    },\r\n    [4 /* ErrorTypes.NAVIGATION_ABORTED */]({ from, to }) {\r\n        return `Navigation aborted from \"${from.fullPath}\" to \"${to.fullPath}\" via a navigation guard.`;\r\n    },\r\n    [8 /* ErrorTypes.NAVIGATION_CANCELLED */]({ from, to }) {\r\n        return `Navigation cancelled from \"${from.fullPath}\" to \"${to.fullPath}\" with a new navigation.`;\r\n    },\r\n    [16 /* ErrorTypes.NAVIGATION_DUPLICATED */]({ from, to }) {\r\n        return `Avoided redundant navigation to current location: \"${from.fullPath}\".`;\r\n    },\r\n};\r\n/**\r\n * Creates a typed NavigationFailure object.\r\n * @internal\r\n * @param type - NavigationFailureType\r\n * @param params - { from, to }\r\n */\r\nfunction createRouterError(type, params) {\r\n    // keep full error messages in cjs versions\r\n    if ((process.env.NODE_ENV !== 'production') || !true) {\r\n        return assign(new Error(ErrorTypeMessages[type](params)), {\r\n            type,\r\n            [NavigationFailureSymbol]: true,\r\n        }, params);\r\n    }\r\n    else {\r\n        return assign(new Error(), {\r\n            type,\r\n            [NavigationFailureSymbol]: true,\r\n        }, params);\r\n    }\r\n}\r\nfunction isNavigationFailure(error, type) {\r\n    return (error instanceof Error &&\r\n        NavigationFailureSymbol in error &&\r\n        (type == null || !!(error.type & type)));\r\n}\r\nconst propertiesToLog = ['params', 'query', 'hash'];\r\nfunction stringifyRoute(to) {\r\n    if (typeof to === 'string')\r\n        return to;\r\n    if (to.path != null)\r\n        return to.path;\r\n    const location = {};\r\n    for (const key of propertiesToLog) {\r\n        if (key in to)\r\n            location[key] = to[key];\r\n    }\r\n    return JSON.stringify(location, null, 2);\r\n}\r\n\r\n// default pattern for a param: non-greedy everything but /\r\nconst BASE_PARAM_PATTERN = '[^/]+?';\r\nconst BASE_PATH_PARSER_OPTIONS = {\r\n    sensitive: false,\r\n    strict: false,\r\n    start: true,\r\n    end: true,\r\n};\r\n// Special Regex characters that must be escaped in static tokens\r\nconst REGEX_CHARS_RE = /[.+*?^${}()[\\]/\\\\]/g;\r\n/**\r\n * Creates a path parser from an array of Segments (a segment is an array of Tokens)\r\n *\r\n * @param segments - array of segments returned by tokenizePath\r\n * @param extraOptions - optional options for the regexp\r\n * @returns a PathParser\r\n */\r\nfunction tokensToParser(segments, extraOptions) {\r\n    const options = assign({}, BASE_PATH_PARSER_OPTIONS, extraOptions);\r\n    // the amount of scores is the same as the length of segments except for the root segment \"/\"\r\n    const score = [];\r\n    // the regexp as a string\r\n    let pattern = options.start ? '^' : '';\r\n    // extracted keys\r\n    const keys = [];\r\n    for (const segment of segments) {\r\n        // the root segment needs special treatment\r\n        const segmentScores = segment.length ? [] : [90 /* PathScore.Root */];\r\n        // allow trailing slash\r\n        if (options.strict && !segment.length)\r\n            pattern += '/';\r\n        for (let tokenIndex = 0; tokenIndex < segment.length; tokenIndex++) {\r\n            const token = segment[tokenIndex];\r\n            // resets the score if we are inside a sub-segment /:a-other-:b\r\n            let subSegmentScore = 40 /* PathScore.Segment */ +\r\n                (options.sensitive ? 0.25 /* PathScore.BonusCaseSensitive */ : 0);\r\n            if (token.type === 0 /* TokenType.Static */) {\r\n                // prepend the slash if we are starting a new segment\r\n                if (!tokenIndex)\r\n                    pattern += '/';\r\n                pattern += token.value.replace(REGEX_CHARS_RE, '\\\\$&');\r\n                subSegmentScore += 40 /* PathScore.Static */;\r\n            }\r\n            else if (token.type === 1 /* TokenType.Param */) {\r\n                const { value, repeatable, optional, regexp } = token;\r\n                keys.push({\r\n                    name: value,\r\n                    repeatable,\r\n                    optional,\r\n                });\r\n                const re = regexp ? regexp : BASE_PARAM_PATTERN;\r\n                // the user provided a custom regexp /:id(\\\\d+)\r\n                if (re !== BASE_PARAM_PATTERN) {\r\n                    subSegmentScore += 10 /* PathScore.BonusCustomRegExp */;\r\n                    // make sure the regexp is valid before using it\r\n                    try {\r\n                        new RegExp(`(${re})`);\r\n                    }\r\n                    catch (err) {\r\n                        throw new Error(`Invalid custom RegExp for param \"${value}\" (${re}): ` +\r\n                            err.message);\r\n                    }\r\n                }\r\n                // when we repeat we must take care of the repeating leading slash\r\n                let subPattern = repeatable ? `((?:${re})(?:/(?:${re}))*)` : `(${re})`;\r\n                // prepend the slash if we are starting a new segment\r\n                if (!tokenIndex)\r\n                    subPattern =\r\n                        // avoid an optional / if there are more segments e.g. /:p?-static\r\n                        // or /:p?-:p2\r\n                        optional && segment.length < 2\r\n                            ? `(?:/${subPattern})`\r\n                            : '/' + subPattern;\r\n                if (optional)\r\n                    subPattern += '?';\r\n                pattern += subPattern;\r\n                subSegmentScore += 20 /* PathScore.Dynamic */;\r\n                if (optional)\r\n                    subSegmentScore += -8 /* PathScore.BonusOptional */;\r\n                if (repeatable)\r\n                    subSegmentScore += -20 /* PathScore.BonusRepeatable */;\r\n                if (re === '.*')\r\n                    subSegmentScore += -50 /* PathScore.BonusWildcard */;\r\n            }\r\n            segmentScores.push(subSegmentScore);\r\n        }\r\n        // an empty array like /home/<USER>\n        // if (!segment.length) pattern += '/'\r\n        score.push(segmentScores);\r\n    }\r\n    // only apply the strict bonus to the last score\r\n    if (options.strict && options.end) {\r\n        const i = score.length - 1;\r\n        score[i][score[i].length - 1] += 0.7000000000000001 /* PathScore.BonusStrict */;\r\n    }\r\n    // TODO: dev only warn double trailing slash\r\n    if (!options.strict)\r\n        pattern += '/?';\r\n    if (options.end)\r\n        pattern += '$';\r\n    // allow paths like /dynamic to only match dynamic or dynamic/... but not dynamic_something_else\r\n    else if (options.strict && !pattern.endsWith('/'))\r\n        pattern += '(?:/|$)';\r\n    const re = new RegExp(pattern, options.sensitive ? '' : 'i');\r\n    function parse(path) {\r\n        const match = path.match(re);\r\n        const params = {};\r\n        if (!match)\r\n            return null;\r\n        for (let i = 1; i < match.length; i++) {\r\n            const value = match[i] || '';\r\n            const key = keys[i - 1];\r\n            params[key.name] = value && key.repeatable ? value.split('/') : value;\r\n        }\r\n        return params;\r\n    }\r\n    function stringify(params) {\r\n        let path = '';\r\n        // for optional parameters to allow to be empty\r\n        let avoidDuplicatedSlash = false;\r\n        for (const segment of segments) {\r\n            if (!avoidDuplicatedSlash || !path.endsWith('/'))\r\n                path += '/';\r\n            avoidDuplicatedSlash = false;\r\n            for (const token of segment) {\r\n                if (token.type === 0 /* TokenType.Static */) {\r\n                    path += token.value;\r\n                }\r\n                else if (token.type === 1 /* TokenType.Param */) {\r\n                    const { value, repeatable, optional } = token;\r\n                    const param = value in params ? params[value] : '';\r\n                    if (isArray(param) && !repeatable) {\r\n                        throw new Error(`Provided param \"${value}\" is an array but it is not repeatable (* or + modifiers)`);\r\n                    }\r\n                    const text = isArray(param)\r\n                        ? param.join('/')\r\n                        : param;\r\n                    if (!text) {\r\n                        if (optional) {\r\n                            // if we have more than one optional param like /:a?-static we don't need to care about the optional param\r\n                            if (segment.length < 2) {\r\n                                // remove the last slash as we could be at the end\r\n                                if (path.endsWith('/'))\r\n                                    path = path.slice(0, -1);\r\n                                // do not append a slash on the next iteration\r\n                                else\r\n                                    avoidDuplicatedSlash = true;\r\n                            }\r\n                        }\r\n                        else\r\n                            throw new Error(`Missing required param \"${value}\"`);\r\n                    }\r\n                    path += text;\r\n                }\r\n            }\r\n        }\r\n        // avoid empty path when we have multiple optional params\r\n        return path || '/';\r\n    }\r\n    return {\r\n        re,\r\n        score,\r\n        keys,\r\n        parse,\r\n        stringify,\r\n    };\r\n}\r\n/**\r\n * Compares an array of numbers as used in PathParser.score and returns a\r\n * number. This function can be used to `sort` an array\r\n *\r\n * @param a - first array of numbers\r\n * @param b - second array of numbers\r\n * @returns 0 if both are equal, < 0 if a should be sorted first, > 0 if b\r\n * should be sorted first\r\n */\r\nfunction compareScoreArray(a, b) {\r\n    let i = 0;\r\n    while (i < a.length && i < b.length) {\r\n        const diff = b[i] - a[i];\r\n        // only keep going if diff === 0\r\n        if (diff)\r\n            return diff;\r\n        i++;\r\n    }\r\n    // if the last subsegment was Static, the shorter segments should be sorted first\r\n    // otherwise sort the longest segment first\r\n    if (a.length < b.length) {\r\n        return a.length === 1 && a[0] === 40 /* PathScore.Static */ + 40 /* PathScore.Segment */\r\n            ? -1\r\n            : 1;\r\n    }\r\n    else if (a.length > b.length) {\r\n        return b.length === 1 && b[0] === 40 /* PathScore.Static */ + 40 /* PathScore.Segment */\r\n            ? 1\r\n            : -1;\r\n    }\r\n    return 0;\r\n}\r\n/**\r\n * Compare function that can be used with `sort` to sort an array of PathParser\r\n *\r\n * @param a - first PathParser\r\n * @param b - second PathParser\r\n * @returns 0 if both are equal, < 0 if a should be sorted first, > 0 if b\r\n */\r\nfunction comparePathParserScore(a, b) {\r\n    let i = 0;\r\n    const aScore = a.score;\r\n    const bScore = b.score;\r\n    while (i < aScore.length && i < bScore.length) {\r\n        const comp = compareScoreArray(aScore[i], bScore[i]);\r\n        // do not return if both are equal\r\n        if (comp)\r\n            return comp;\r\n        i++;\r\n    }\r\n    if (Math.abs(bScore.length - aScore.length) === 1) {\r\n        if (isLastScoreNegative(aScore))\r\n            return 1;\r\n        if (isLastScoreNegative(bScore))\r\n            return -1;\r\n    }\r\n    // if a and b share the same score entries but b has more, sort b first\r\n    return bScore.length - aScore.length;\r\n    // this is the ternary version\r\n    // return aScore.length < bScore.length\r\n    //   ? 1\r\n    //   : aScore.length > bScore.length\r\n    //   ? -1\r\n    //   : 0\r\n}\r\n/**\r\n * This allows detecting splats at the end of a path: /home/<USER>\n *\r\n * @param score - score to check\r\n * @returns true if the last entry is negative\r\n */\r\nfunction isLastScoreNegative(score) {\r\n    const last = score[score.length - 1];\r\n    return score.length > 0 && last[last.length - 1] < 0;\r\n}\r\n\r\nconst ROOT_TOKEN = {\r\n    type: 0 /* TokenType.Static */,\r\n    value: '',\r\n};\r\nconst VALID_PARAM_RE = /[a-zA-Z0-9_]/;\r\n// After some profiling, the cache seems to be unnecessary because tokenizePath\r\n// (the slowest part of adding a route) is very fast\r\n// const tokenCache = new Map<string, Token[][]>()\r\nfunction tokenizePath(path) {\r\n    if (!path)\r\n        return [[]];\r\n    if (path === '/')\r\n        return [[ROOT_TOKEN]];\r\n    if (!path.startsWith('/')) {\r\n        throw new Error((process.env.NODE_ENV !== 'production')\r\n            ? `Route paths should start with a \"/\": \"${path}\" should be \"/${path}\".`\r\n            : `Invalid path \"${path}\"`);\r\n    }\r\n    // if (tokenCache.has(path)) return tokenCache.get(path)!\r\n    function crash(message) {\r\n        throw new Error(`ERR (${state})/\"${buffer}\": ${message}`);\r\n    }\r\n    let state = 0 /* TokenizerState.Static */;\r\n    let previousState = state;\r\n    const tokens = [];\r\n    // the segment will always be valid because we get into the initial state\r\n    // with the leading /\r\n    let segment;\r\n    function finalizeSegment() {\r\n        if (segment)\r\n            tokens.push(segment);\r\n        segment = [];\r\n    }\r\n    // index on the path\r\n    let i = 0;\r\n    // char at index\r\n    let char;\r\n    // buffer of the value read\r\n    let buffer = '';\r\n    // custom regexp for a param\r\n    let customRe = '';\r\n    function consumeBuffer() {\r\n        if (!buffer)\r\n            return;\r\n        if (state === 0 /* TokenizerState.Static */) {\r\n            segment.push({\r\n                type: 0 /* TokenType.Static */,\r\n                value: buffer,\r\n            });\r\n        }\r\n        else if (state === 1 /* TokenizerState.Param */ ||\r\n            state === 2 /* TokenizerState.ParamRegExp */ ||\r\n            state === 3 /* TokenizerState.ParamRegExpEnd */) {\r\n            if (segment.length > 1 && (char === '*' || char === '+'))\r\n                crash(`A repeatable param (${buffer}) must be alone in its segment. eg: '/:ids+.`);\r\n            segment.push({\r\n                type: 1 /* TokenType.Param */,\r\n                value: buffer,\r\n                regexp: customRe,\r\n                repeatable: char === '*' || char === '+',\r\n                optional: char === '*' || char === '?',\r\n            });\r\n        }\r\n        else {\r\n            crash('Invalid state to consume buffer');\r\n        }\r\n        buffer = '';\r\n    }\r\n    function addCharToBuffer() {\r\n        buffer += char;\r\n    }\r\n    while (i < path.length) {\r\n        char = path[i++];\r\n        if (char === '\\\\' && state !== 2 /* TokenizerState.ParamRegExp */) {\r\n            previousState = state;\r\n            state = 4 /* TokenizerState.EscapeNext */;\r\n            continue;\r\n        }\r\n        switch (state) {\r\n            case 0 /* TokenizerState.Static */:\r\n                if (char === '/') {\r\n                    if (buffer) {\r\n                        consumeBuffer();\r\n                    }\r\n                    finalizeSegment();\r\n                }\r\n                else if (char === ':') {\r\n                    consumeBuffer();\r\n                    state = 1 /* TokenizerState.Param */;\r\n                }\r\n                else {\r\n                    addCharToBuffer();\r\n                }\r\n                break;\r\n            case 4 /* TokenizerState.EscapeNext */:\r\n                addCharToBuffer();\r\n                state = previousState;\r\n                break;\r\n            case 1 /* TokenizerState.Param */:\r\n                if (char === '(') {\r\n                    state = 2 /* TokenizerState.ParamRegExp */;\r\n                }\r\n                else if (VALID_PARAM_RE.test(char)) {\r\n                    addCharToBuffer();\r\n                }\r\n                else {\r\n                    consumeBuffer();\r\n                    state = 0 /* TokenizerState.Static */;\r\n                    // go back one character if we were not modifying\r\n                    if (char !== '*' && char !== '?' && char !== '+')\r\n                        i--;\r\n                }\r\n                break;\r\n            case 2 /* TokenizerState.ParamRegExp */:\r\n                // TODO: is it worth handling nested regexp? like :p(?:prefix_([^/]+)_suffix)\r\n                // it already works by escaping the closing )\r\n                // https://paths.esm.dev/?p=AAMeJbiAwQEcDKbAoAAkP60PG2R6QAvgNaA6AFACM2ABuQBB#\r\n                // is this really something people need since you can also write\r\n                // /prefix_:p()_suffix\r\n                if (char === ')') {\r\n                    // handle the escaped )\r\n                    if (customRe[customRe.length - 1] == '\\\\')\r\n                        customRe = customRe.slice(0, -1) + char;\r\n                    else\r\n                        state = 3 /* TokenizerState.ParamRegExpEnd */;\r\n                }\r\n                else {\r\n                    customRe += char;\r\n                }\r\n                break;\r\n            case 3 /* TokenizerState.ParamRegExpEnd */:\r\n                // same as finalizing a param\r\n                consumeBuffer();\r\n                state = 0 /* TokenizerState.Static */;\r\n                // go back one character if we were not modifying\r\n                if (char !== '*' && char !== '?' && char !== '+')\r\n                    i--;\r\n                customRe = '';\r\n                break;\r\n            default:\r\n                crash('Unknown state');\r\n                break;\r\n        }\r\n    }\r\n    if (state === 2 /* TokenizerState.ParamRegExp */)\r\n        crash(`Unfinished custom RegExp for param \"${buffer}\"`);\r\n    consumeBuffer();\r\n    finalizeSegment();\r\n    // tokenCache.set(path, tokens)\r\n    return tokens;\r\n}\r\n\r\nfunction createRouteRecordMatcher(record, parent, options) {\r\n    const parser = tokensToParser(tokenizePath(record.path), options);\r\n    // warn against params with the same name\r\n    if ((process.env.NODE_ENV !== 'production')) {\r\n        const existingKeys = new Set();\r\n        for (const key of parser.keys) {\r\n            if (existingKeys.has(key.name))\r\n                warn(`Found duplicated params with name \"${key.name}\" for path \"${record.path}\". Only the last one will be available on \"$route.params\".`);\r\n            existingKeys.add(key.name);\r\n        }\r\n    }\r\n    const matcher = assign(parser, {\r\n        record,\r\n        parent,\r\n        // these needs to be populated by the parent\r\n        children: [],\r\n        alias: [],\r\n    });\r\n    if (parent) {\r\n        // both are aliases or both are not aliases\r\n        // we don't want to mix them because the order is used when\r\n        // passing originalRecord in Matcher.addRoute\r\n        if (!matcher.record.aliasOf === !parent.record.aliasOf)\r\n            parent.children.push(matcher);\r\n    }\r\n    return matcher;\r\n}\r\n\r\n/**\r\n * Creates a Router Matcher.\r\n *\r\n * @internal\r\n * @param routes - array of initial routes\r\n * @param globalOptions - global route options\r\n */\r\nfunction createRouterMatcher(routes, globalOptions) {\r\n    // normalized ordered array of matchers\r\n    const matchers = [];\r\n    const matcherMap = new Map();\r\n    globalOptions = mergeOptions({ strict: false, end: true, sensitive: false }, globalOptions);\r\n    function getRecordMatcher(name) {\r\n        return matcherMap.get(name);\r\n    }\r\n    function addRoute(record, parent, originalRecord) {\r\n        // used later on to remove by name\r\n        const isRootAdd = !originalRecord;\r\n        const mainNormalizedRecord = normalizeRouteRecord(record);\r\n        if ((process.env.NODE_ENV !== 'production')) {\r\n            checkChildMissingNameWithEmptyPath(mainNormalizedRecord, parent);\r\n        }\r\n        // we might be the child of an alias\r\n        mainNormalizedRecord.aliasOf = originalRecord && originalRecord.record;\r\n        const options = mergeOptions(globalOptions, record);\r\n        // generate an array of records to correctly handle aliases\r\n        const normalizedRecords = [mainNormalizedRecord];\r\n        if ('alias' in record) {\r\n            const aliases = typeof record.alias === 'string' ? [record.alias] : record.alias;\r\n            for (const alias of aliases) {\r\n                normalizedRecords.push(\r\n                // we need to normalize again to ensure the `mods` property\r\n                // being non enumerable\r\n                normalizeRouteRecord(assign({}, mainNormalizedRecord, {\r\n                    // this allows us to hold a copy of the `components` option\r\n                    // so that async components cache is hold on the original record\r\n                    components: originalRecord\r\n                        ? originalRecord.record.components\r\n                        : mainNormalizedRecord.components,\r\n                    path: alias,\r\n                    // we might be the child of an alias\r\n                    aliasOf: originalRecord\r\n                        ? originalRecord.record\r\n                        : mainNormalizedRecord,\r\n                    // the aliases are always of the same kind as the original since they\r\n                    // are defined on the same record\r\n                })));\r\n            }\r\n        }\r\n        let matcher;\r\n        let originalMatcher;\r\n        for (const normalizedRecord of normalizedRecords) {\r\n            const { path } = normalizedRecord;\r\n            // Build up the path for nested routes if the child isn't an absolute\r\n            // route. Only add the / delimiter if the child path isn't empty and if the\r\n            // parent path doesn't have a trailing slash\r\n            if (parent && path[0] !== '/') {\r\n                const parentPath = parent.record.path;\r\n                const connectingSlash = parentPath[parentPath.length - 1] === '/' ? '' : '/';\r\n                normalizedRecord.path =\r\n                    parent.record.path + (path && connectingSlash + path);\r\n            }\r\n            if ((process.env.NODE_ENV !== 'production') && normalizedRecord.path === '*') {\r\n                throw new Error('Catch all routes (\"*\") must now be defined using a param with a custom regexp.\\n' +\r\n                    'See more at https://router.vuejs.org/guide/migration/#Removed-star-or-catch-all-routes.');\r\n            }\r\n            // create the object beforehand, so it can be passed to children\r\n            matcher = createRouteRecordMatcher(normalizedRecord, parent, options);\r\n            if ((process.env.NODE_ENV !== 'production') && parent && path[0] === '/')\r\n                checkMissingParamsInAbsolutePath(matcher, parent);\r\n            // if we are an alias we must tell the original record that we exist,\r\n            // so we can be removed\r\n            if (originalRecord) {\r\n                originalRecord.alias.push(matcher);\r\n                if ((process.env.NODE_ENV !== 'production')) {\r\n                    checkSameParams(originalRecord, matcher);\r\n                }\r\n            }\r\n            else {\r\n                // otherwise, the first record is the original and others are aliases\r\n                originalMatcher = originalMatcher || matcher;\r\n                if (originalMatcher !== matcher)\r\n                    originalMatcher.alias.push(matcher);\r\n                // remove the route if named and only for the top record (avoid in nested calls)\r\n                // this works because the original record is the first one\r\n                if (isRootAdd && record.name && !isAliasRecord(matcher)) {\r\n                    if ((process.env.NODE_ENV !== 'production')) {\r\n                        checkSameNameAsAncestor(record, parent);\r\n                    }\r\n                    removeRoute(record.name);\r\n                }\r\n            }\r\n            // Avoid adding a record that doesn't display anything. This allows passing through records without a component to\r\n            // not be reached and pass through the catch all route\r\n            if (isMatchable(matcher)) {\r\n                insertMatcher(matcher);\r\n            }\r\n            if (mainNormalizedRecord.children) {\r\n                const children = mainNormalizedRecord.children;\r\n                for (let i = 0; i < children.length; i++) {\r\n                    addRoute(children[i], matcher, originalRecord && originalRecord.children[i]);\r\n                }\r\n            }\r\n            // if there was no original record, then the first one was not an alias and all\r\n            // other aliases (if any) need to reference this record when adding children\r\n            originalRecord = originalRecord || matcher;\r\n            // TODO: add normalized records for more flexibility\r\n            // if (parent && isAliasRecord(originalRecord)) {\r\n            //   parent.children.push(originalRecord)\r\n            // }\r\n        }\r\n        return originalMatcher\r\n            ? () => {\r\n                // since other matchers are aliases, they should be removed by the original matcher\r\n                removeRoute(originalMatcher);\r\n            }\r\n            : noop;\r\n    }\r\n    function removeRoute(matcherRef) {\r\n        if (isRouteName(matcherRef)) {\r\n            const matcher = matcherMap.get(matcherRef);\r\n            if (matcher) {\r\n                matcherMap.delete(matcherRef);\r\n                matchers.splice(matchers.indexOf(matcher), 1);\r\n                matcher.children.forEach(removeRoute);\r\n                matcher.alias.forEach(removeRoute);\r\n            }\r\n        }\r\n        else {\r\n            const index = matchers.indexOf(matcherRef);\r\n            if (index > -1) {\r\n                matchers.splice(index, 1);\r\n                if (matcherRef.record.name)\r\n                    matcherMap.delete(matcherRef.record.name);\r\n                matcherRef.children.forEach(removeRoute);\r\n                matcherRef.alias.forEach(removeRoute);\r\n            }\r\n        }\r\n    }\r\n    function getRoutes() {\r\n        return matchers;\r\n    }\r\n    function insertMatcher(matcher) {\r\n        const index = findInsertionIndex(matcher, matchers);\r\n        matchers.splice(index, 0, matcher);\r\n        // only add the original record to the name map\r\n        if (matcher.record.name && !isAliasRecord(matcher))\r\n            matcherMap.set(matcher.record.name, matcher);\r\n    }\r\n    function resolve(location, currentLocation) {\r\n        let matcher;\r\n        let params = {};\r\n        let path;\r\n        let name;\r\n        if ('name' in location && location.name) {\r\n            matcher = matcherMap.get(location.name);\r\n            if (!matcher)\r\n                throw createRouterError(1 /* ErrorTypes.MATCHER_NOT_FOUND */, {\r\n                    location,\r\n                });\r\n            // warn if the user is passing invalid params so they can debug it better when they get removed\r\n            if ((process.env.NODE_ENV !== 'production')) {\r\n                const invalidParams = Object.keys(location.params || {}).filter(paramName => !matcher.keys.find(k => k.name === paramName));\r\n                if (invalidParams.length) {\r\n                    warn(`Discarded invalid param(s) \"${invalidParams.join('\", \"')}\" when navigating. See https://github.com/vuejs/router/blob/main/packages/router/CHANGELOG.md#414-2022-08-22 for more details.`);\r\n                }\r\n            }\r\n            name = matcher.record.name;\r\n            params = assign(\r\n            // paramsFromLocation is a new object\r\n            paramsFromLocation(currentLocation.params, \r\n            // only keep params that exist in the resolved location\r\n            // only keep optional params coming from a parent record\r\n            matcher.keys\r\n                .filter(k => !k.optional)\r\n                .concat(matcher.parent ? matcher.parent.keys.filter(k => k.optional) : [])\r\n                .map(k => k.name)), \r\n            // discard any existing params in the current location that do not exist here\r\n            // #1497 this ensures better active/exact matching\r\n            location.params &&\r\n                paramsFromLocation(location.params, matcher.keys.map(k => k.name)));\r\n            // throws if cannot be stringified\r\n            path = matcher.stringify(params);\r\n        }\r\n        else if (location.path != null) {\r\n            // no need to resolve the path with the matcher as it was provided\r\n            // this also allows the user to control the encoding\r\n            path = location.path;\r\n            if ((process.env.NODE_ENV !== 'production') && !path.startsWith('/')) {\r\n                warn(`The Matcher cannot resolve relative paths but received \"${path}\". Unless you directly called \\`matcher.resolve(\"${path}\")\\`, this is probably a bug in vue-router. Please open an issue at https://github.com/vuejs/router/issues/new/choose.`);\r\n            }\r\n            matcher = matchers.find(m => m.re.test(path));\r\n            // matcher should have a value after the loop\r\n            if (matcher) {\r\n                // we know the matcher works because we tested the regexp\r\n                params = matcher.parse(path);\r\n                name = matcher.record.name;\r\n            }\r\n            // location is a relative path\r\n        }\r\n        else {\r\n            // match by name or path of current route\r\n            matcher = currentLocation.name\r\n                ? matcherMap.get(currentLocation.name)\r\n                : matchers.find(m => m.re.test(currentLocation.path));\r\n            if (!matcher)\r\n                throw createRouterError(1 /* ErrorTypes.MATCHER_NOT_FOUND */, {\r\n                    location,\r\n                    currentLocation,\r\n                });\r\n            name = matcher.record.name;\r\n            // since we are navigating to the same location, we don't need to pick the\r\n            // params like when `name` is provided\r\n            params = assign({}, currentLocation.params, location.params);\r\n            path = matcher.stringify(params);\r\n        }\r\n        const matched = [];\r\n        let parentMatcher = matcher;\r\n        while (parentMatcher) {\r\n            // reversed order so parents are at the beginning\r\n            matched.unshift(parentMatcher.record);\r\n            parentMatcher = parentMatcher.parent;\r\n        }\r\n        return {\r\n            name,\r\n            path,\r\n            params,\r\n            matched,\r\n            meta: mergeMetaFields(matched),\r\n        };\r\n    }\r\n    // add initial routes\r\n    routes.forEach(route => addRoute(route));\r\n    function clearRoutes() {\r\n        matchers.length = 0;\r\n        matcherMap.clear();\r\n    }\r\n    return {\r\n        addRoute,\r\n        resolve,\r\n        removeRoute,\r\n        clearRoutes,\r\n        getRoutes,\r\n        getRecordMatcher,\r\n    };\r\n}\r\nfunction paramsFromLocation(params, keys) {\r\n    const newParams = {};\r\n    for (const key of keys) {\r\n        if (key in params)\r\n            newParams[key] = params[key];\r\n    }\r\n    return newParams;\r\n}\r\n/**\r\n * Normalizes a RouteRecordRaw. Creates a copy\r\n *\r\n * @param record\r\n * @returns the normalized version\r\n */\r\nfunction normalizeRouteRecord(record) {\r\n    const normalized = {\r\n        path: record.path,\r\n        redirect: record.redirect,\r\n        name: record.name,\r\n        meta: record.meta || {},\r\n        aliasOf: record.aliasOf,\r\n        beforeEnter: record.beforeEnter,\r\n        props: normalizeRecordProps(record),\r\n        children: record.children || [],\r\n        instances: {},\r\n        leaveGuards: new Set(),\r\n        updateGuards: new Set(),\r\n        enterCallbacks: {},\r\n        // must be declared afterwards\r\n        // mods: {},\r\n        components: 'components' in record\r\n            ? record.components || null\r\n            : record.component && { default: record.component },\r\n    };\r\n    // mods contain modules and shouldn't be copied,\r\n    // logged or anything. It's just used for internal\r\n    // advanced use cases like data loaders\r\n    Object.defineProperty(normalized, 'mods', {\r\n        value: {},\r\n    });\r\n    return normalized;\r\n}\r\n/**\r\n * Normalize the optional `props` in a record to always be an object similar to\r\n * components. Also accept a boolean for components.\r\n * @param record\r\n */\r\nfunction normalizeRecordProps(record) {\r\n    const propsObject = {};\r\n    // props does not exist on redirect records, but we can set false directly\r\n    const props = record.props || false;\r\n    if ('component' in record) {\r\n        propsObject.default = props;\r\n    }\r\n    else {\r\n        // NOTE: we could also allow a function to be applied to every component.\r\n        // Would need user feedback for use cases\r\n        for (const name in record.components)\r\n            propsObject[name] = typeof props === 'object' ? props[name] : props;\r\n    }\r\n    return propsObject;\r\n}\r\n/**\r\n * Checks if a record or any of its parent is an alias\r\n * @param record\r\n */\r\nfunction isAliasRecord(record) {\r\n    while (record) {\r\n        if (record.record.aliasOf)\r\n            return true;\r\n        record = record.parent;\r\n    }\r\n    return false;\r\n}\r\n/**\r\n * Merge meta fields of an array of records\r\n *\r\n * @param matched - array of matched records\r\n */\r\nfunction mergeMetaFields(matched) {\r\n    return matched.reduce((meta, record) => assign(meta, record.meta), {});\r\n}\r\nfunction mergeOptions(defaults, partialOptions) {\r\n    const options = {};\r\n    for (const key in defaults) {\r\n        options[key] = key in partialOptions ? partialOptions[key] : defaults[key];\r\n    }\r\n    return options;\r\n}\r\nfunction isSameParam(a, b) {\r\n    return (a.name === b.name &&\r\n        a.optional === b.optional &&\r\n        a.repeatable === b.repeatable);\r\n}\r\n/**\r\n * Check if a path and its alias have the same required params\r\n *\r\n * @param a - original record\r\n * @param b - alias record\r\n */\r\nfunction checkSameParams(a, b) {\r\n    for (const key of a.keys) {\r\n        if (!key.optional && !b.keys.find(isSameParam.bind(null, key)))\r\n            return warn(`Alias \"${b.record.path}\" and the original record: \"${a.record.path}\" must have the exact same param named \"${key.name}\"`);\r\n    }\r\n    for (const key of b.keys) {\r\n        if (!key.optional && !a.keys.find(isSameParam.bind(null, key)))\r\n            return warn(`Alias \"${b.record.path}\" and the original record: \"${a.record.path}\" must have the exact same param named \"${key.name}\"`);\r\n    }\r\n}\r\n/**\r\n * A route with a name and a child with an empty path without a name should warn when adding the route\r\n *\r\n * @param mainNormalizedRecord - RouteRecordNormalized\r\n * @param parent - RouteRecordMatcher\r\n */\r\nfunction checkChildMissingNameWithEmptyPath(mainNormalizedRecord, parent) {\r\n    if (parent &&\r\n        parent.record.name &&\r\n        !mainNormalizedRecord.name &&\r\n        !mainNormalizedRecord.path) {\r\n        warn(`The route named \"${String(parent.record.name)}\" has a child without a name and an empty path. Using that name won't render the empty path child so you probably want to move the name to the child instead. If this is intentional, add a name to the child route to remove the warning.`);\r\n    }\r\n}\r\nfunction checkSameNameAsAncestor(record, parent) {\r\n    for (let ancestor = parent; ancestor; ancestor = ancestor.parent) {\r\n        if (ancestor.record.name === record.name) {\r\n            throw new Error(`A route named \"${String(record.name)}\" has been added as a ${parent === ancestor ? 'child' : 'descendant'} of a route with the same name. Route names must be unique and a nested route cannot use the same name as an ancestor.`);\r\n        }\r\n    }\r\n}\r\nfunction checkMissingParamsInAbsolutePath(record, parent) {\r\n    for (const key of parent.keys) {\r\n        if (!record.keys.find(isSameParam.bind(null, key)))\r\n            return warn(`Absolute path \"${record.record.path}\" must have the exact same param named \"${key.name}\" as its parent \"${parent.record.path}\".`);\r\n    }\r\n}\r\n/**\r\n * Performs a binary search to find the correct insertion index for a new matcher.\r\n *\r\n * Matchers are primarily sorted by their score. If scores are tied then we also consider parent/child relationships,\r\n * with descendants coming before ancestors. If there's still a tie, new routes are inserted after existing routes.\r\n *\r\n * @param matcher - new matcher to be inserted\r\n * @param matchers - existing matchers\r\n */\r\nfunction findInsertionIndex(matcher, matchers) {\r\n    // First phase: binary search based on score\r\n    let lower = 0;\r\n    let upper = matchers.length;\r\n    while (lower !== upper) {\r\n        const mid = (lower + upper) >> 1;\r\n        const sortOrder = comparePathParserScore(matcher, matchers[mid]);\r\n        if (sortOrder < 0) {\r\n            upper = mid;\r\n        }\r\n        else {\r\n            lower = mid + 1;\r\n        }\r\n    }\r\n    // Second phase: check for an ancestor with the same score\r\n    const insertionAncestor = getInsertionAncestor(matcher);\r\n    if (insertionAncestor) {\r\n        upper = matchers.lastIndexOf(insertionAncestor, upper - 1);\r\n        if ((process.env.NODE_ENV !== 'production') && upper < 0) {\r\n            // This should never happen\r\n            warn(`Finding ancestor route \"${insertionAncestor.record.path}\" failed for \"${matcher.record.path}\"`);\r\n        }\r\n    }\r\n    return upper;\r\n}\r\nfunction getInsertionAncestor(matcher) {\r\n    let ancestor = matcher;\r\n    while ((ancestor = ancestor.parent)) {\r\n        if (isMatchable(ancestor) &&\r\n            comparePathParserScore(matcher, ancestor) === 0) {\r\n            return ancestor;\r\n        }\r\n    }\r\n    return;\r\n}\r\n/**\r\n * Checks if a matcher can be reachable. This means if it's possible to reach it as a route. For example, routes without\r\n * a component, or name, or redirect, are just used to group other routes.\r\n * @param matcher\r\n * @param matcher.record record of the matcher\r\n * @returns\r\n */\r\nfunction isMatchable({ record }) {\r\n    return !!(record.name ||\r\n        (record.components && Object.keys(record.components).length) ||\r\n        record.redirect);\r\n}\r\n\r\n/**\r\n * Transforms a queryString into a {@link LocationQuery} object. Accept both, a\r\n * version with the leading `?` and without Should work as URLSearchParams\r\n\r\n * @internal\r\n *\r\n * @param search - search string to parse\r\n * @returns a query object\r\n */\r\nfunction parseQuery(search) {\r\n    const query = {};\r\n    // avoid creating an object with an empty key and empty value\r\n    // because of split('&')\r\n    if (search === '' || search === '?')\r\n        return query;\r\n    const hasLeadingIM = search[0] === '?';\r\n    const searchParams = (hasLeadingIM ? search.slice(1) : search).split('&');\r\n    for (let i = 0; i < searchParams.length; ++i) {\r\n        // pre decode the + into space\r\n        const searchParam = searchParams[i].replace(PLUS_RE, ' ');\r\n        // allow the = character\r\n        const eqPos = searchParam.indexOf('=');\r\n        const key = decode(eqPos < 0 ? searchParam : searchParam.slice(0, eqPos));\r\n        const value = eqPos < 0 ? null : decode(searchParam.slice(eqPos + 1));\r\n        if (key in query) {\r\n            // an extra variable for ts types\r\n            let currentValue = query[key];\r\n            if (!isArray(currentValue)) {\r\n                currentValue = query[key] = [currentValue];\r\n            }\r\n            currentValue.push(value);\r\n        }\r\n        else {\r\n            query[key] = value;\r\n        }\r\n    }\r\n    return query;\r\n}\r\n/**\r\n * Stringifies a {@link LocationQueryRaw} object. Like `URLSearchParams`, it\r\n * doesn't prepend a `?`\r\n *\r\n * @internal\r\n *\r\n * @param query - query object to stringify\r\n * @returns string version of the query without the leading `?`\r\n */\r\nfunction stringifyQuery(query) {\r\n    let search = '';\r\n    for (let key in query) {\r\n        const value = query[key];\r\n        key = encodeQueryKey(key);\r\n        if (value == null) {\r\n            // only null adds the value\r\n            if (value !== undefined) {\r\n                search += (search.length ? '&' : '') + key;\r\n            }\r\n            continue;\r\n        }\r\n        // keep null values\r\n        const values = isArray(value)\r\n            ? value.map(v => v && encodeQueryValue(v))\r\n            : [value && encodeQueryValue(value)];\r\n        values.forEach(value => {\r\n            // skip undefined values in arrays as if they were not present\r\n            // smaller code than using filter\r\n            if (value !== undefined) {\r\n                // only append & with non-empty search\r\n                search += (search.length ? '&' : '') + key;\r\n                if (value != null)\r\n                    search += '=' + value;\r\n            }\r\n        });\r\n    }\r\n    return search;\r\n}\r\n/**\r\n * Transforms a {@link LocationQueryRaw} into a {@link LocationQuery} by casting\r\n * numbers into strings, removing keys with an undefined value and replacing\r\n * undefined with null in arrays\r\n *\r\n * @param query - query object to normalize\r\n * @returns a normalized query object\r\n */\r\nfunction normalizeQuery(query) {\r\n    const normalizedQuery = {};\r\n    for (const key in query) {\r\n        const value = query[key];\r\n        if (value !== undefined) {\r\n            normalizedQuery[key] = isArray(value)\r\n                ? value.map(v => (v == null ? null : '' + v))\r\n                : value == null\r\n                    ? value\r\n                    : '' + value;\r\n        }\r\n    }\r\n    return normalizedQuery;\r\n}\r\n\r\n/**\r\n * RouteRecord being rendered by the closest ancestor Router View. Used for\r\n * `onBeforeRouteUpdate` and `onBeforeRouteLeave`. rvlm stands for Router View\r\n * Location Matched\r\n *\r\n * @internal\r\n */\r\nconst matchedRouteKey = Symbol((process.env.NODE_ENV !== 'production') ? 'router view location matched' : '');\r\n/**\r\n * Allows overriding the router view depth to control which component in\r\n * `matched` is rendered. rvd stands for Router View Depth\r\n *\r\n * @internal\r\n */\r\nconst viewDepthKey = Symbol((process.env.NODE_ENV !== 'production') ? 'router view depth' : '');\r\n/**\r\n * Allows overriding the router instance returned by `useRouter` in tests. r\r\n * stands for router\r\n *\r\n * @internal\r\n */\r\nconst routerKey = Symbol((process.env.NODE_ENV !== 'production') ? 'router' : '');\r\n/**\r\n * Allows overriding the current route returned by `useRoute` in tests. rl\r\n * stands for route location\r\n *\r\n * @internal\r\n */\r\nconst routeLocationKey = Symbol((process.env.NODE_ENV !== 'production') ? 'route location' : '');\r\n/**\r\n * Allows overriding the current route used by router-view. Internally this is\r\n * used when the `route` prop is passed.\r\n *\r\n * @internal\r\n */\r\nconst routerViewLocationKey = Symbol((process.env.NODE_ENV !== 'production') ? 'router view location' : '');\r\n\r\n/**\r\n * Create a list of callbacks that can be reset. Used to create before and after navigation guards list\r\n */\r\nfunction useCallbacks() {\r\n    let handlers = [];\r\n    function add(handler) {\r\n        handlers.push(handler);\r\n        return () => {\r\n            const i = handlers.indexOf(handler);\r\n            if (i > -1)\r\n                handlers.splice(i, 1);\r\n        };\r\n    }\r\n    function reset() {\r\n        handlers = [];\r\n    }\r\n    return {\r\n        add,\r\n        list: () => handlers.slice(),\r\n        reset,\r\n    };\r\n}\r\n\r\nfunction registerGuard(record, name, guard) {\r\n    const removeFromList = () => {\r\n        record[name].delete(guard);\r\n    };\r\n    onUnmounted(removeFromList);\r\n    onDeactivated(removeFromList);\r\n    onActivated(() => {\r\n        record[name].add(guard);\r\n    });\r\n    record[name].add(guard);\r\n}\r\n/**\r\n * Add a navigation guard that triggers whenever the component for the current\r\n * location is about to be left. Similar to {@link beforeRouteLeave} but can be\r\n * used in any component. The guard is removed when the component is unmounted.\r\n *\r\n * @param leaveGuard - {@link NavigationGuard}\r\n */\r\nfunction onBeforeRouteLeave(leaveGuard) {\r\n    if ((process.env.NODE_ENV !== 'production') && !getCurrentInstance()) {\r\n        warn('getCurrentInstance() returned null. onBeforeRouteLeave() must be called at the top of a setup function');\r\n        return;\r\n    }\r\n    const activeRecord = inject(matchedRouteKey, \r\n    // to avoid warning\r\n    {}).value;\r\n    if (!activeRecord) {\r\n        (process.env.NODE_ENV !== 'production') &&\r\n            warn('No active route record was found when calling `onBeforeRouteLeave()`. Make sure you call this function inside a component child of <router-view>. Maybe you called it inside of App.vue?');\r\n        return;\r\n    }\r\n    registerGuard(activeRecord, 'leaveGuards', leaveGuard);\r\n}\r\n/**\r\n * Add a navigation guard that triggers whenever the current location is about\r\n * to be updated. Similar to {@link beforeRouteUpdate} but can be used in any\r\n * component. The guard is removed when the component is unmounted.\r\n *\r\n * @param updateGuard - {@link NavigationGuard}\r\n */\r\nfunction onBeforeRouteUpdate(updateGuard) {\r\n    if ((process.env.NODE_ENV !== 'production') && !getCurrentInstance()) {\r\n        warn('getCurrentInstance() returned null. onBeforeRouteUpdate() must be called at the top of a setup function');\r\n        return;\r\n    }\r\n    const activeRecord = inject(matchedRouteKey, \r\n    // to avoid warning\r\n    {}).value;\r\n    if (!activeRecord) {\r\n        (process.env.NODE_ENV !== 'production') &&\r\n            warn('No active route record was found when calling `onBeforeRouteUpdate()`. Make sure you call this function inside a component child of <router-view>. Maybe you called it inside of App.vue?');\r\n        return;\r\n    }\r\n    registerGuard(activeRecord, 'updateGuards', updateGuard);\r\n}\r\nfunction guardToPromiseFn(guard, to, from, record, name, runWithContext = fn => fn()) {\r\n    // keep a reference to the enterCallbackArray to prevent pushing callbacks if a new navigation took place\r\n    const enterCallbackArray = record &&\r\n        // name is defined if record is because of the function overload\r\n        (record.enterCallbacks[name] = record.enterCallbacks[name] || []);\r\n    return () => new Promise((resolve, reject) => {\r\n        const next = (valid) => {\r\n            if (valid === false) {\r\n                reject(createRouterError(4 /* ErrorTypes.NAVIGATION_ABORTED */, {\r\n                    from,\r\n                    to,\r\n                }));\r\n            }\r\n            else if (valid instanceof Error) {\r\n                reject(valid);\r\n            }\r\n            else if (isRouteLocation(valid)) {\r\n                reject(createRouterError(2 /* ErrorTypes.NAVIGATION_GUARD_REDIRECT */, {\r\n                    from: to,\r\n                    to: valid,\r\n                }));\r\n            }\r\n            else {\r\n                if (enterCallbackArray &&\r\n                    // since enterCallbackArray is truthy, both record and name also are\r\n                    record.enterCallbacks[name] === enterCallbackArray &&\r\n                    typeof valid === 'function') {\r\n                    enterCallbackArray.push(valid);\r\n                }\r\n                resolve();\r\n            }\r\n        };\r\n        // wrapping with Promise.resolve allows it to work with both async and sync guards\r\n        const guardReturn = runWithContext(() => guard.call(record && record.instances[name], to, from, (process.env.NODE_ENV !== 'production') ? canOnlyBeCalledOnce(next, to, from) : next));\r\n        let guardCall = Promise.resolve(guardReturn);\r\n        if (guard.length < 3)\r\n            guardCall = guardCall.then(next);\r\n        if ((process.env.NODE_ENV !== 'production') && guard.length > 2) {\r\n            const message = `The \"next\" callback was never called inside of ${guard.name ? '\"' + guard.name + '\"' : ''}:\\n${guard.toString()}\\n. If you are returning a value instead of calling \"next\", make sure to remove the \"next\" parameter from your function.`;\r\n            if (typeof guardReturn === 'object' && 'then' in guardReturn) {\r\n                guardCall = guardCall.then(resolvedValue => {\r\n                    // @ts-expect-error: _called is added at canOnlyBeCalledOnce\r\n                    if (!next._called) {\r\n                        warn(message);\r\n                        return Promise.reject(new Error('Invalid navigation guard'));\r\n                    }\r\n                    return resolvedValue;\r\n                });\r\n            }\r\n            else if (guardReturn !== undefined) {\r\n                // @ts-expect-error: _called is added at canOnlyBeCalledOnce\r\n                if (!next._called) {\r\n                    warn(message);\r\n                    reject(new Error('Invalid navigation guard'));\r\n                    return;\r\n                }\r\n            }\r\n        }\r\n        guardCall.catch(err => reject(err));\r\n    });\r\n}\r\nfunction canOnlyBeCalledOnce(next, to, from) {\r\n    let called = 0;\r\n    return function () {\r\n        if (called++ === 1)\r\n            warn(`The \"next\" callback was called more than once in one navigation guard when going from \"${from.fullPath}\" to \"${to.fullPath}\". It should be called exactly one time in each navigation guard. This will fail in production.`);\r\n        // @ts-expect-error: we put it in the original one because it's easier to check\r\n        next._called = true;\r\n        if (called === 1)\r\n            next.apply(null, arguments);\r\n    };\r\n}\r\nfunction extractComponentsGuards(matched, guardType, to, from, runWithContext = fn => fn()) {\r\n    const guards = [];\r\n    for (const record of matched) {\r\n        if ((process.env.NODE_ENV !== 'production') && !record.components && !record.children.length) {\r\n            warn(`Record with path \"${record.path}\" is either missing a \"component(s)\"` +\r\n                ` or \"children\" property.`);\r\n        }\r\n        for (const name in record.components) {\r\n            let rawComponent = record.components[name];\r\n            if ((process.env.NODE_ENV !== 'production')) {\r\n                if (!rawComponent ||\r\n                    (typeof rawComponent !== 'object' &&\r\n                        typeof rawComponent !== 'function')) {\r\n                    warn(`Component \"${name}\" in record with path \"${record.path}\" is not` +\r\n                        ` a valid component. Received \"${String(rawComponent)}\".`);\r\n                    // throw to ensure we stop here but warn to ensure the message isn't\r\n                    // missed by the user\r\n                    throw new Error('Invalid route component');\r\n                }\r\n                else if ('then' in rawComponent) {\r\n                    // warn if user wrote import('/component.vue') instead of () =>\r\n                    // import('./component.vue')\r\n                    warn(`Component \"${name}\" in record with path \"${record.path}\" is a ` +\r\n                        `Promise instead of a function that returns a Promise. Did you ` +\r\n                        `write \"import('./MyPage.vue')\" instead of ` +\r\n                        `\"() => import('./MyPage.vue')\" ? This will break in ` +\r\n                        `production if not fixed.`);\r\n                    const promise = rawComponent;\r\n                    rawComponent = () => promise;\r\n                }\r\n                else if (rawComponent.__asyncLoader &&\r\n                    // warn only once per component\r\n                    !rawComponent.__warnedDefineAsync) {\r\n                    rawComponent.__warnedDefineAsync = true;\r\n                    warn(`Component \"${name}\" in record with path \"${record.path}\" is defined ` +\r\n                        `using \"defineAsyncComponent()\". ` +\r\n                        `Write \"() => import('./MyPage.vue')\" instead of ` +\r\n                        `\"defineAsyncComponent(() => import('./MyPage.vue'))\".`);\r\n                }\r\n            }\r\n            // skip update and leave guards if the route component is not mounted\r\n            if (guardType !== 'beforeRouteEnter' && !record.instances[name])\r\n                continue;\r\n            if (isRouteComponent(rawComponent)) {\r\n                // __vccOpts is added by vue-class-component and contain the regular options\r\n                const options = rawComponent.__vccOpts || rawComponent;\r\n                const guard = options[guardType];\r\n                guard &&\r\n                    guards.push(guardToPromiseFn(guard, to, from, record, name, runWithContext));\r\n            }\r\n            else {\r\n                // start requesting the chunk already\r\n                let componentPromise = rawComponent();\r\n                if ((process.env.NODE_ENV !== 'production') && !('catch' in componentPromise)) {\r\n                    warn(`Component \"${name}\" in record with path \"${record.path}\" is a function that does not return a Promise. If you were passing a functional component, make sure to add a \"displayName\" to the component. This will break in production if not fixed.`);\r\n                    componentPromise = Promise.resolve(componentPromise);\r\n                }\r\n                guards.push(() => componentPromise.then(resolved => {\r\n                    if (!resolved)\r\n                        throw new Error(`Couldn't resolve component \"${name}\" at \"${record.path}\"`);\r\n                    const resolvedComponent = isESModule(resolved)\r\n                        ? resolved.default\r\n                        : resolved;\r\n                    // keep the resolved module for plugins like data loaders\r\n                    record.mods[name] = resolved;\r\n                    // replace the function with the resolved component\r\n                    // cannot be null or undefined because we went into the for loop\r\n                    record.components[name] = resolvedComponent;\r\n                    // __vccOpts is added by vue-class-component and contain the regular options\r\n                    const options = resolvedComponent.__vccOpts || resolvedComponent;\r\n                    const guard = options[guardType];\r\n                    return (guard &&\r\n                        guardToPromiseFn(guard, to, from, record, name, runWithContext)());\r\n                }));\r\n            }\r\n        }\r\n    }\r\n    return guards;\r\n}\r\n/**\r\n * Ensures a route is loaded, so it can be passed as o prop to `<RouterView>`.\r\n *\r\n * @param route - resolved route to load\r\n */\r\nfunction loadRouteLocation(route) {\r\n    return route.matched.every(record => record.redirect)\r\n        ? Promise.reject(new Error('Cannot load a route that redirects.'))\r\n        : Promise.all(route.matched.map(record => record.components &&\r\n            Promise.all(Object.keys(record.components).reduce((promises, name) => {\r\n                const rawComponent = record.components[name];\r\n                if (typeof rawComponent === 'function' &&\r\n                    !('displayName' in rawComponent)) {\r\n                    promises.push(rawComponent().then(resolved => {\r\n                        if (!resolved)\r\n                            return Promise.reject(new Error(`Couldn't resolve component \"${name}\" at \"${record.path}\". Ensure you passed a function that returns a promise.`));\r\n                        const resolvedComponent = isESModule(resolved)\r\n                            ? resolved.default\r\n                            : resolved;\r\n                        // keep the resolved module for plugins like data loaders\r\n                        record.mods[name] = resolved;\r\n                        // replace the function with the resolved component\r\n                        // cannot be null or undefined because we went into the for loop\r\n                        record.components[name] = resolvedComponent;\r\n                        return;\r\n                    }));\r\n                }\r\n                return promises;\r\n            }, [])))).then(() => route);\r\n}\r\n\r\n// TODO: we could allow currentRoute as a prop to expose `isActive` and\r\n// `isExactActive` behavior should go through an RFC\r\n/**\r\n * Returns the internal behavior of a {@link RouterLink} without the rendering part.\r\n *\r\n * @param props - a `to` location and an optional `replace` flag\r\n */\r\nfunction useLink(props) {\r\n    const router = inject(routerKey);\r\n    const currentRoute = inject(routeLocationKey);\r\n    let hasPrevious = false;\r\n    let previousTo = null;\r\n    const route = computed(() => {\r\n        const to = unref(props.to);\r\n        if ((process.env.NODE_ENV !== 'production') && (!hasPrevious || to !== previousTo)) {\r\n            if (!isRouteLocation(to)) {\r\n                if (hasPrevious) {\r\n                    warn(`Invalid value for prop \"to\" in useLink()\\n- to:`, to, `\\n- previous to:`, previousTo, `\\n- props:`, props);\r\n                }\r\n                else {\r\n                    warn(`Invalid value for prop \"to\" in useLink()\\n- to:`, to, `\\n- props:`, props);\r\n                }\r\n            }\r\n            previousTo = to;\r\n            hasPrevious = true;\r\n        }\r\n        return router.resolve(to);\r\n    });\r\n    const activeRecordIndex = computed(() => {\r\n        const { matched } = route.value;\r\n        const { length } = matched;\r\n        const routeMatched = matched[length - 1];\r\n        const currentMatched = currentRoute.matched;\r\n        if (!routeMatched || !currentMatched.length)\r\n            return -1;\r\n        const index = currentMatched.findIndex(isSameRouteRecord.bind(null, routeMatched));\r\n        if (index > -1)\r\n            return index;\r\n        // possible parent record\r\n        const parentRecordPath = getOriginalPath(matched[length - 2]);\r\n        return (\r\n        // we are dealing with nested routes\r\n        length > 1 &&\r\n            // if the parent and matched route have the same path, this link is\r\n            // referring to the empty child. Or we currently are on a different\r\n            // child of the same parent\r\n            getOriginalPath(routeMatched) === parentRecordPath &&\r\n            // avoid comparing the child with its parent\r\n            currentMatched[currentMatched.length - 1].path !== parentRecordPath\r\n            ? currentMatched.findIndex(isSameRouteRecord.bind(null, matched[length - 2]))\r\n            : index);\r\n    });\r\n    const isActive = computed(() => activeRecordIndex.value > -1 &&\r\n        includesParams(currentRoute.params, route.value.params));\r\n    const isExactActive = computed(() => activeRecordIndex.value > -1 &&\r\n        activeRecordIndex.value === currentRoute.matched.length - 1 &&\r\n        isSameRouteLocationParams(currentRoute.params, route.value.params));\r\n    function navigate(e = {}) {\r\n        if (guardEvent(e)) {\r\n            const p = router[unref(props.replace) ? 'replace' : 'push'](unref(props.to)\r\n            // avoid uncaught errors are they are logged anyway\r\n            ).catch(noop);\r\n            if (props.viewTransition &&\r\n                typeof document !== 'undefined' &&\r\n                'startViewTransition' in document) {\r\n                document.startViewTransition(() => p);\r\n            }\r\n            return p;\r\n        }\r\n        return Promise.resolve();\r\n    }\r\n    // devtools only\r\n    if (((process.env.NODE_ENV !== 'production') || __VUE_PROD_DEVTOOLS__) && isBrowser) {\r\n        const instance = getCurrentInstance();\r\n        if (instance) {\r\n            const linkContextDevtools = {\r\n                route: route.value,\r\n                isActive: isActive.value,\r\n                isExactActive: isExactActive.value,\r\n                error: null,\r\n            };\r\n            // @ts-expect-error: this is internal\r\n            instance.__vrl_devtools = instance.__vrl_devtools || [];\r\n            // @ts-expect-error: this is internal\r\n            instance.__vrl_devtools.push(linkContextDevtools);\r\n            watchEffect(() => {\r\n                linkContextDevtools.route = route.value;\r\n                linkContextDevtools.isActive = isActive.value;\r\n                linkContextDevtools.isExactActive = isExactActive.value;\r\n                linkContextDevtools.error = isRouteLocation(unref(props.to))\r\n                    ? null\r\n                    : 'Invalid \"to\" value';\r\n            }, { flush: 'post' });\r\n        }\r\n    }\r\n    /**\r\n     * NOTE: update {@link _RouterLinkI}'s `$slots` type when updating this\r\n     */\r\n    return {\r\n        route,\r\n        href: computed(() => route.value.href),\r\n        isActive,\r\n        isExactActive,\r\n        navigate,\r\n    };\r\n}\r\nfunction preferSingleVNode(vnodes) {\r\n    return vnodes.length === 1 ? vnodes[0] : vnodes;\r\n}\r\nconst RouterLinkImpl = /*#__PURE__*/ defineComponent({\r\n    name: 'RouterLink',\r\n    compatConfig: { MODE: 3 },\r\n    props: {\r\n        to: {\r\n            type: [String, Object],\r\n            required: true,\r\n        },\r\n        replace: Boolean,\r\n        activeClass: String,\r\n        // inactiveClass: String,\r\n        exactActiveClass: String,\r\n        custom: Boolean,\r\n        ariaCurrentValue: {\r\n            type: String,\r\n            default: 'page',\r\n        },\r\n        viewTransition: Boolean,\r\n    },\r\n    useLink,\r\n    setup(props, { slots }) {\r\n        const link = reactive(useLink(props));\r\n        const { options } = inject(routerKey);\r\n        const elClass = computed(() => ({\r\n            [getLinkClass(props.activeClass, options.linkActiveClass, 'router-link-active')]: link.isActive,\r\n            // [getLinkClass(\r\n            //   props.inactiveClass,\r\n            //   options.linkInactiveClass,\r\n            //   'router-link-inactive'\r\n            // )]: !link.isExactActive,\r\n            [getLinkClass(props.exactActiveClass, options.linkExactActiveClass, 'router-link-exact-active')]: link.isExactActive,\r\n        }));\r\n        return () => {\r\n            const children = slots.default && preferSingleVNode(slots.default(link));\r\n            return props.custom\r\n                ? children\r\n                : h('a', {\r\n                    'aria-current': link.isExactActive\r\n                        ? props.ariaCurrentValue\r\n                        : null,\r\n                    href: link.href,\r\n                    // this would override user added attrs but Vue will still add\r\n                    // the listener, so we end up triggering both\r\n                    onClick: link.navigate,\r\n                    class: elClass.value,\r\n                }, children);\r\n        };\r\n    },\r\n});\r\n// export the public type for h/tsx inference\r\n// also to avoid inline import() in generated d.ts files\r\n/**\r\n * Component to render a link that triggers a navigation on click.\r\n */\r\nconst RouterLink = RouterLinkImpl;\r\nfunction guardEvent(e) {\r\n    // don't redirect with control keys\r\n    if (e.metaKey || e.altKey || e.ctrlKey || e.shiftKey)\r\n        return;\r\n    // don't redirect when preventDefault called\r\n    if (e.defaultPrevented)\r\n        return;\r\n    // don't redirect on right click\r\n    if (e.button !== undefined && e.button !== 0)\r\n        return;\r\n    // don't redirect if `target=\"_blank\"`\r\n    // @ts-expect-error getAttribute does exist\r\n    if (e.currentTarget && e.currentTarget.getAttribute) {\r\n        // @ts-expect-error getAttribute exists\r\n        const target = e.currentTarget.getAttribute('target');\r\n        if (/\\b_blank\\b/i.test(target))\r\n            return;\r\n    }\r\n    // this may be a Weex event which doesn't have this method\r\n    if (e.preventDefault)\r\n        e.preventDefault();\r\n    return true;\r\n}\r\nfunction includesParams(outer, inner) {\r\n    for (const key in inner) {\r\n        const innerValue = inner[key];\r\n        const outerValue = outer[key];\r\n        if (typeof innerValue === 'string') {\r\n            if (innerValue !== outerValue)\r\n                return false;\r\n        }\r\n        else {\r\n            if (!isArray(outerValue) ||\r\n                outerValue.length !== innerValue.length ||\r\n                innerValue.some((value, i) => value !== outerValue[i]))\r\n                return false;\r\n        }\r\n    }\r\n    return true;\r\n}\r\n/**\r\n * Get the original path value of a record by following its aliasOf\r\n * @param record\r\n */\r\nfunction getOriginalPath(record) {\r\n    return record ? (record.aliasOf ? record.aliasOf.path : record.path) : '';\r\n}\r\n/**\r\n * Utility class to get the active class based on defaults.\r\n * @param propClass\r\n * @param globalClass\r\n * @param defaultClass\r\n */\r\nconst getLinkClass = (propClass, globalClass, defaultClass) => propClass != null\r\n    ? propClass\r\n    : globalClass != null\r\n        ? globalClass\r\n        : defaultClass;\r\n\r\nconst RouterViewImpl = /*#__PURE__*/ defineComponent({\r\n    name: 'RouterView',\r\n    // #674 we manually inherit them\r\n    inheritAttrs: false,\r\n    props: {\r\n        name: {\r\n            type: String,\r\n            default: 'default',\r\n        },\r\n        route: Object,\r\n    },\r\n    // Better compat for @vue/compat users\r\n    // https://github.com/vuejs/router/issues/1315\r\n    compatConfig: { MODE: 3 },\r\n    setup(props, { attrs, slots }) {\r\n        (process.env.NODE_ENV !== 'production') && warnDeprecatedUsage();\r\n        const injectedRoute = inject(routerViewLocationKey);\r\n        const routeToDisplay = computed(() => props.route || injectedRoute.value);\r\n        const injectedDepth = inject(viewDepthKey, 0);\r\n        // The depth changes based on empty components option, which allows passthrough routes e.g. routes with children\r\n        // that are used to reuse the `path` property\r\n        const depth = computed(() => {\r\n            let initialDepth = unref(injectedDepth);\r\n            const { matched } = routeToDisplay.value;\r\n            let matchedRoute;\r\n            while ((matchedRoute = matched[initialDepth]) &&\r\n                !matchedRoute.components) {\r\n                initialDepth++;\r\n            }\r\n            return initialDepth;\r\n        });\r\n        const matchedRouteRef = computed(() => routeToDisplay.value.matched[depth.value]);\r\n        provide(viewDepthKey, computed(() => depth.value + 1));\r\n        provide(matchedRouteKey, matchedRouteRef);\r\n        provide(routerViewLocationKey, routeToDisplay);\r\n        const viewRef = ref();\r\n        // watch at the same time the component instance, the route record we are\r\n        // rendering, and the name\r\n        watch(() => [viewRef.value, matchedRouteRef.value, props.name], ([instance, to, name], [oldInstance, from, oldName]) => {\r\n            // copy reused instances\r\n            if (to) {\r\n                // this will update the instance for new instances as well as reused\r\n                // instances when navigating to a new route\r\n                to.instances[name] = instance;\r\n                // the component instance is reused for a different route or name, so\r\n                // we copy any saved update or leave guards. With async setup, the\r\n                // mounting component will mount before the matchedRoute changes,\r\n                // making instance === oldInstance, so we check if guards have been\r\n                // added before. This works because we remove guards when\r\n                // unmounting/deactivating components\r\n                if (from && from !== to && instance && instance === oldInstance) {\r\n                    if (!to.leaveGuards.size) {\r\n                        to.leaveGuards = from.leaveGuards;\r\n                    }\r\n                    if (!to.updateGuards.size) {\r\n                        to.updateGuards = from.updateGuards;\r\n                    }\r\n                }\r\n            }\r\n            // trigger beforeRouteEnter next callbacks\r\n            if (instance &&\r\n                to &&\r\n                // if there is no instance but to and from are the same this might be\r\n                // the first visit\r\n                (!from || !isSameRouteRecord(to, from) || !oldInstance)) {\r\n                (to.enterCallbacks[name] || []).forEach(callback => callback(instance));\r\n            }\r\n        }, { flush: 'post' });\r\n        return () => {\r\n            const route = routeToDisplay.value;\r\n            // we need the value at the time we render because when we unmount, we\r\n            // navigated to a different location so the value is different\r\n            const currentName = props.name;\r\n            const matchedRoute = matchedRouteRef.value;\r\n            const ViewComponent = matchedRoute && matchedRoute.components[currentName];\r\n            if (!ViewComponent) {\r\n                return normalizeSlot(slots.default, { Component: ViewComponent, route });\r\n            }\r\n            // props from route configuration\r\n            const routePropsOption = matchedRoute.props[currentName];\r\n            const routeProps = routePropsOption\r\n                ? routePropsOption === true\r\n                    ? route.params\r\n                    : typeof routePropsOption === 'function'\r\n                        ? routePropsOption(route)\r\n                        : routePropsOption\r\n                : null;\r\n            const onVnodeUnmounted = vnode => {\r\n                // remove the instance reference to prevent leak\r\n                if (vnode.component.isUnmounted) {\r\n                    matchedRoute.instances[currentName] = null;\r\n                }\r\n            };\r\n            const component = h(ViewComponent, assign({}, routeProps, attrs, {\r\n                onVnodeUnmounted,\r\n                ref: viewRef,\r\n            }));\r\n            if (((process.env.NODE_ENV !== 'production') || __VUE_PROD_DEVTOOLS__) &&\r\n                isBrowser &&\r\n                component.ref) {\r\n                // TODO: can display if it's an alias, its props\r\n                const info = {\r\n                    depth: depth.value,\r\n                    name: matchedRoute.name,\r\n                    path: matchedRoute.path,\r\n                    meta: matchedRoute.meta,\r\n                };\r\n                const internalInstances = isArray(component.ref)\r\n                    ? component.ref.map(r => r.i)\r\n                    : [component.ref.i];\r\n                internalInstances.forEach(instance => {\r\n                    // @ts-expect-error\r\n                    instance.__vrv_devtools = info;\r\n                });\r\n            }\r\n            return (\r\n            // pass the vnode to the slot as a prop.\r\n            // h and <component :is=\"...\"> both accept vnodes\r\n            normalizeSlot(slots.default, { Component: component, route }) ||\r\n                component);\r\n        };\r\n    },\r\n});\r\nfunction normalizeSlot(slot, data) {\r\n    if (!slot)\r\n        return null;\r\n    const slotContent = slot(data);\r\n    return slotContent.length === 1 ? slotContent[0] : slotContent;\r\n}\r\n// export the public type for h/tsx inference\r\n// also to avoid inline import() in generated d.ts files\r\n/**\r\n * Component to display the current route the user is at.\r\n */\r\nconst RouterView = RouterViewImpl;\r\n// warn against deprecated usage with <transition> & <keep-alive>\r\n// due to functional component being no longer eager in Vue 3\r\nfunction warnDeprecatedUsage() {\r\n    const instance = getCurrentInstance();\r\n    const parentName = instance.parent && instance.parent.type.name;\r\n    const parentSubTreeType = instance.parent && instance.parent.subTree && instance.parent.subTree.type;\r\n    if (parentName &&\r\n        (parentName === 'KeepAlive' || parentName.includes('Transition')) &&\r\n        typeof parentSubTreeType === 'object' &&\r\n        parentSubTreeType.name === 'RouterView') {\r\n        const comp = parentName === 'KeepAlive' ? 'keep-alive' : 'transition';\r\n        warn(`<router-view> can no longer be used directly inside <transition> or <keep-alive>.\\n` +\r\n            `Use slot props instead:\\n\\n` +\r\n            `<router-view v-slot=\"{ Component }\">\\n` +\r\n            `  <${comp}>\\n` +\r\n            `    <component :is=\"Component\" />\\n` +\r\n            `  </${comp}>\\n` +\r\n            `</router-view>`);\r\n    }\r\n}\r\n\r\n/**\r\n * Copies a route location and removes any problematic properties that cannot be shown in devtools (e.g. Vue instances).\r\n *\r\n * @param routeLocation - routeLocation to format\r\n * @param tooltip - optional tooltip\r\n * @returns a copy of the routeLocation\r\n */\r\nfunction formatRouteLocation(routeLocation, tooltip) {\r\n    const copy = assign({}, routeLocation, {\r\n        // remove variables that can contain vue instances\r\n        matched: routeLocation.matched.map(matched => omit(matched, ['instances', 'children', 'aliasOf'])),\r\n    });\r\n    return {\r\n        _custom: {\r\n            type: null,\r\n            readOnly: true,\r\n            display: routeLocation.fullPath,\r\n            tooltip,\r\n            value: copy,\r\n        },\r\n    };\r\n}\r\nfunction formatDisplay(display) {\r\n    return {\r\n        _custom: {\r\n            display,\r\n        },\r\n    };\r\n}\r\n// to support multiple router instances\r\nlet routerId = 0;\r\nfunction addDevtools(app, router, matcher) {\r\n    // Take over router.beforeEach and afterEach\r\n    // make sure we are not registering the devtool twice\r\n    if (router.__hasDevtools)\r\n        return;\r\n    router.__hasDevtools = true;\r\n    // increment to support multiple router instances\r\n    const id = routerId++;\r\n    setupDevtoolsPlugin({\r\n        id: 'org.vuejs.router' + (id ? '.' + id : ''),\r\n        label: 'Vue Router',\r\n        packageName: 'vue-router',\r\n        homepage: 'https://router.vuejs.org',\r\n        logo: 'https://router.vuejs.org/logo.png',\r\n        componentStateTypes: ['Routing'],\r\n        app,\r\n    }, api => {\r\n        if (typeof api.now !== 'function') {\r\n            console.warn('[Vue Router]: You seem to be using an outdated version of Vue Devtools. Are you still using the Beta release instead of the stable one? You can find the links at https://devtools.vuejs.org/guide/installation.html.');\r\n        }\r\n        // display state added by the router\r\n        api.on.inspectComponent((payload, ctx) => {\r\n            if (payload.instanceData) {\r\n                payload.instanceData.state.push({\r\n                    type: 'Routing',\r\n                    key: '$route',\r\n                    editable: false,\r\n                    value: formatRouteLocation(router.currentRoute.value, 'Current Route'),\r\n                });\r\n            }\r\n        });\r\n        // mark router-link as active and display tags on router views\r\n        api.on.visitComponentTree(({ treeNode: node, componentInstance }) => {\r\n            if (componentInstance.__vrv_devtools) {\r\n                const info = componentInstance.__vrv_devtools;\r\n                node.tags.push({\r\n                    label: (info.name ? `${info.name.toString()}: ` : '') + info.path,\r\n                    textColor: 0,\r\n                    tooltip: 'This component is rendered by &lt;router-view&gt;',\r\n                    backgroundColor: PINK_500,\r\n                });\r\n            }\r\n            // if multiple useLink are used\r\n            if (isArray(componentInstance.__vrl_devtools)) {\r\n                componentInstance.__devtoolsApi = api;\r\n                componentInstance.__vrl_devtools.forEach(devtoolsData => {\r\n                    let label = devtoolsData.route.path;\r\n                    let backgroundColor = ORANGE_400;\r\n                    let tooltip = '';\r\n                    let textColor = 0;\r\n                    if (devtoolsData.error) {\r\n                        label = devtoolsData.error;\r\n                        backgroundColor = RED_100;\r\n                        textColor = RED_700;\r\n                    }\r\n                    else if (devtoolsData.isExactActive) {\r\n                        backgroundColor = LIME_500;\r\n                        tooltip = 'This is exactly active';\r\n                    }\r\n                    else if (devtoolsData.isActive) {\r\n                        backgroundColor = BLUE_600;\r\n                        tooltip = 'This link is active';\r\n                    }\r\n                    node.tags.push({\r\n                        label,\r\n                        textColor,\r\n                        tooltip,\r\n                        backgroundColor,\r\n                    });\r\n                });\r\n            }\r\n        });\r\n        watch(router.currentRoute, () => {\r\n            // refresh active state\r\n            refreshRoutesView();\r\n            api.notifyComponentUpdate();\r\n            api.sendInspectorTree(routerInspectorId);\r\n            api.sendInspectorState(routerInspectorId);\r\n        });\r\n        const navigationsLayerId = 'router:navigations:' + id;\r\n        api.addTimelineLayer({\r\n            id: navigationsLayerId,\r\n            label: `Router${id ? ' ' + id : ''} Navigations`,\r\n            color: 0x40a8c4,\r\n        });\r\n        // const errorsLayerId = 'router:errors'\r\n        // api.addTimelineLayer({\r\n        //   id: errorsLayerId,\r\n        //   label: 'Router Errors',\r\n        //   color: 0xea5455,\r\n        // })\r\n        router.onError((error, to) => {\r\n            api.addTimelineEvent({\r\n                layerId: navigationsLayerId,\r\n                event: {\r\n                    title: 'Error during Navigation',\r\n                    subtitle: to.fullPath,\r\n                    logType: 'error',\r\n                    time: api.now(),\r\n                    data: { error },\r\n                    groupId: to.meta.__navigationId,\r\n                },\r\n            });\r\n        });\r\n        // attached to `meta` and used to group events\r\n        let navigationId = 0;\r\n        router.beforeEach((to, from) => {\r\n            const data = {\r\n                guard: formatDisplay('beforeEach'),\r\n                from: formatRouteLocation(from, 'Current Location during this navigation'),\r\n                to: formatRouteLocation(to, 'Target location'),\r\n            };\r\n            // Used to group navigations together, hide from devtools\r\n            Object.defineProperty(to.meta, '__navigationId', {\r\n                value: navigationId++,\r\n            });\r\n            api.addTimelineEvent({\r\n                layerId: navigationsLayerId,\r\n                event: {\r\n                    time: api.now(),\r\n                    title: 'Start of navigation',\r\n                    subtitle: to.fullPath,\r\n                    data,\r\n                    groupId: to.meta.__navigationId,\r\n                },\r\n            });\r\n        });\r\n        router.afterEach((to, from, failure) => {\r\n            const data = {\r\n                guard: formatDisplay('afterEach'),\r\n            };\r\n            if (failure) {\r\n                data.failure = {\r\n                    _custom: {\r\n                        type: Error,\r\n                        readOnly: true,\r\n                        display: failure ? failure.message : '',\r\n                        tooltip: 'Navigation Failure',\r\n                        value: failure,\r\n                    },\r\n                };\r\n                data.status = formatDisplay('❌');\r\n            }\r\n            else {\r\n                data.status = formatDisplay('✅');\r\n            }\r\n            // we set here to have the right order\r\n            data.from = formatRouteLocation(from, 'Current Location during this navigation');\r\n            data.to = formatRouteLocation(to, 'Target location');\r\n            api.addTimelineEvent({\r\n                layerId: navigationsLayerId,\r\n                event: {\r\n                    title: 'End of navigation',\r\n                    subtitle: to.fullPath,\r\n                    time: api.now(),\r\n                    data,\r\n                    logType: failure ? 'warning' : 'default',\r\n                    groupId: to.meta.__navigationId,\r\n                },\r\n            });\r\n        });\r\n        /**\r\n         * Inspector of Existing routes\r\n         */\r\n        const routerInspectorId = 'router-inspector:' + id;\r\n        api.addInspector({\r\n            id: routerInspectorId,\r\n            label: 'Routes' + (id ? ' ' + id : ''),\r\n            icon: 'book',\r\n            treeFilterPlaceholder: 'Search routes',\r\n        });\r\n        function refreshRoutesView() {\r\n            // the routes view isn't active\r\n            if (!activeRoutesPayload)\r\n                return;\r\n            const payload = activeRoutesPayload;\r\n            // children routes will appear as nested\r\n            let routes = matcher.getRoutes().filter(route => !route.parent ||\r\n                // these routes have a parent with no component which will not appear in the view\r\n                // therefore we still need to include them\r\n                !route.parent.record.components);\r\n            // reset match state to false\r\n            routes.forEach(resetMatchStateOnRouteRecord);\r\n            // apply a match state if there is a payload\r\n            if (payload.filter) {\r\n                routes = routes.filter(route => \r\n                // save matches state based on the payload\r\n                isRouteMatching(route, payload.filter.toLowerCase()));\r\n            }\r\n            // mark active routes\r\n            routes.forEach(route => markRouteRecordActive(route, router.currentRoute.value));\r\n            payload.rootNodes = routes.map(formatRouteRecordForInspector);\r\n        }\r\n        let activeRoutesPayload;\r\n        api.on.getInspectorTree(payload => {\r\n            activeRoutesPayload = payload;\r\n            if (payload.app === app && payload.inspectorId === routerInspectorId) {\r\n                refreshRoutesView();\r\n            }\r\n        });\r\n        /**\r\n         * Display information about the currently selected route record\r\n         */\r\n        api.on.getInspectorState(payload => {\r\n            if (payload.app === app && payload.inspectorId === routerInspectorId) {\r\n                const routes = matcher.getRoutes();\r\n                const route = routes.find(route => route.record.__vd_id === payload.nodeId);\r\n                if (route) {\r\n                    payload.state = {\r\n                        options: formatRouteRecordMatcherForStateInspector(route),\r\n                    };\r\n                }\r\n            }\r\n        });\r\n        api.sendInspectorTree(routerInspectorId);\r\n        api.sendInspectorState(routerInspectorId);\r\n    });\r\n}\r\nfunction modifierForKey(key) {\r\n    if (key.optional) {\r\n        return key.repeatable ? '*' : '?';\r\n    }\r\n    else {\r\n        return key.repeatable ? '+' : '';\r\n    }\r\n}\r\nfunction formatRouteRecordMatcherForStateInspector(route) {\r\n    const { record } = route;\r\n    const fields = [\r\n        { editable: false, key: 'path', value: record.path },\r\n    ];\r\n    if (record.name != null) {\r\n        fields.push({\r\n            editable: false,\r\n            key: 'name',\r\n            value: record.name,\r\n        });\r\n    }\r\n    fields.push({ editable: false, key: 'regexp', value: route.re });\r\n    if (route.keys.length) {\r\n        fields.push({\r\n            editable: false,\r\n            key: 'keys',\r\n            value: {\r\n                _custom: {\r\n                    type: null,\r\n                    readOnly: true,\r\n                    display: route.keys\r\n                        .map(key => `${key.name}${modifierForKey(key)}`)\r\n                        .join(' '),\r\n                    tooltip: 'Param keys',\r\n                    value: route.keys,\r\n                },\r\n            },\r\n        });\r\n    }\r\n    if (record.redirect != null) {\r\n        fields.push({\r\n            editable: false,\r\n            key: 'redirect',\r\n            value: record.redirect,\r\n        });\r\n    }\r\n    if (route.alias.length) {\r\n        fields.push({\r\n            editable: false,\r\n            key: 'aliases',\r\n            value: route.alias.map(alias => alias.record.path),\r\n        });\r\n    }\r\n    if (Object.keys(route.record.meta).length) {\r\n        fields.push({\r\n            editable: false,\r\n            key: 'meta',\r\n            value: route.record.meta,\r\n        });\r\n    }\r\n    fields.push({\r\n        key: 'score',\r\n        editable: false,\r\n        value: {\r\n            _custom: {\r\n                type: null,\r\n                readOnly: true,\r\n                display: route.score.map(score => score.join(', ')).join(' | '),\r\n                tooltip: 'Score used to sort routes',\r\n                value: route.score,\r\n            },\r\n        },\r\n    });\r\n    return fields;\r\n}\r\n/**\r\n * Extracted from tailwind palette\r\n */\r\nconst PINK_500 = 0xec4899;\r\nconst BLUE_600 = 0x2563eb;\r\nconst LIME_500 = 0x84cc16;\r\nconst CYAN_400 = 0x22d3ee;\r\nconst ORANGE_400 = 0xfb923c;\r\n// const GRAY_100 = 0xf4f4f5\r\nconst DARK = 0x666666;\r\nconst RED_100 = 0xfee2e2;\r\nconst RED_700 = 0xb91c1c;\r\nfunction formatRouteRecordForInspector(route) {\r\n    const tags = [];\r\n    const { record } = route;\r\n    if (record.name != null) {\r\n        tags.push({\r\n            label: String(record.name),\r\n            textColor: 0,\r\n            backgroundColor: CYAN_400,\r\n        });\r\n    }\r\n    if (record.aliasOf) {\r\n        tags.push({\r\n            label: 'alias',\r\n            textColor: 0,\r\n            backgroundColor: ORANGE_400,\r\n        });\r\n    }\r\n    if (route.__vd_match) {\r\n        tags.push({\r\n            label: 'matches',\r\n            textColor: 0,\r\n            backgroundColor: PINK_500,\r\n        });\r\n    }\r\n    if (route.__vd_exactActive) {\r\n        tags.push({\r\n            label: 'exact',\r\n            textColor: 0,\r\n            backgroundColor: LIME_500,\r\n        });\r\n    }\r\n    if (route.__vd_active) {\r\n        tags.push({\r\n            label: 'active',\r\n            textColor: 0,\r\n            backgroundColor: BLUE_600,\r\n        });\r\n    }\r\n    if (record.redirect) {\r\n        tags.push({\r\n            label: typeof record.redirect === 'string'\r\n                ? `redirect: ${record.redirect}`\r\n                : 'redirects',\r\n            textColor: 0xffffff,\r\n            backgroundColor: DARK,\r\n        });\r\n    }\r\n    // add an id to be able to select it. Using the `path` is not possible because\r\n    // empty path children would collide with their parents\r\n    let id = record.__vd_id;\r\n    if (id == null) {\r\n        id = String(routeRecordId++);\r\n        record.__vd_id = id;\r\n    }\r\n    return {\r\n        id,\r\n        label: record.path,\r\n        tags,\r\n        children: route.children.map(formatRouteRecordForInspector),\r\n    };\r\n}\r\n//  incremental id for route records and inspector state\r\nlet routeRecordId = 0;\r\nconst EXTRACT_REGEXP_RE = /^\\/(.*)\\/([a-z]*)$/;\r\nfunction markRouteRecordActive(route, currentRoute) {\r\n    // no route will be active if matched is empty\r\n    // reset the matching state\r\n    const isExactActive = currentRoute.matched.length &&\r\n        isSameRouteRecord(currentRoute.matched[currentRoute.matched.length - 1], route.record);\r\n    route.__vd_exactActive = route.__vd_active = isExactActive;\r\n    if (!isExactActive) {\r\n        route.__vd_active = currentRoute.matched.some(match => isSameRouteRecord(match, route.record));\r\n    }\r\n    route.children.forEach(childRoute => markRouteRecordActive(childRoute, currentRoute));\r\n}\r\nfunction resetMatchStateOnRouteRecord(route) {\r\n    route.__vd_match = false;\r\n    route.children.forEach(resetMatchStateOnRouteRecord);\r\n}\r\nfunction isRouteMatching(route, filter) {\r\n    const found = String(route.re).match(EXTRACT_REGEXP_RE);\r\n    route.__vd_match = false;\r\n    if (!found || found.length < 3) {\r\n        return false;\r\n    }\r\n    // use a regexp without $ at the end to match nested routes better\r\n    const nonEndingRE = new RegExp(found[1].replace(/\\$$/, ''), found[2]);\r\n    if (nonEndingRE.test(filter)) {\r\n        // mark children as matches\r\n        route.children.forEach(child => isRouteMatching(child, filter));\r\n        // exception case: `/`\r\n        if (route.record.path !== '/' || filter === '/') {\r\n            route.__vd_match = route.re.test(filter);\r\n            return true;\r\n        }\r\n        // hide the / route\r\n        return false;\r\n    }\r\n    const path = route.record.path.toLowerCase();\r\n    const decodedPath = decode(path);\r\n    // also allow partial matching on the path\r\n    if (!filter.startsWith('/') &&\r\n        (decodedPath.includes(filter) || path.includes(filter)))\r\n        return true;\r\n    if (decodedPath.startsWith(filter) || path.startsWith(filter))\r\n        return true;\r\n    if (route.record.name && String(route.record.name).includes(filter))\r\n        return true;\r\n    return route.children.some(child => isRouteMatching(child, filter));\r\n}\r\nfunction omit(obj, keys) {\r\n    const ret = {};\r\n    for (const key in obj) {\r\n        if (!keys.includes(key)) {\r\n            // @ts-expect-error\r\n            ret[key] = obj[key];\r\n        }\r\n    }\r\n    return ret;\r\n}\r\n\r\n/**\r\n * Creates a Router instance that can be used by a Vue app.\r\n *\r\n * @param options - {@link RouterOptions}\r\n */\r\nfunction createRouter(options) {\r\n    const matcher = createRouterMatcher(options.routes, options);\r\n    const parseQuery$1 = options.parseQuery || parseQuery;\r\n    const stringifyQuery$1 = options.stringifyQuery || stringifyQuery;\r\n    const routerHistory = options.history;\r\n    if ((process.env.NODE_ENV !== 'production') && !routerHistory)\r\n        throw new Error('Provide the \"history\" option when calling \"createRouter()\":' +\r\n            ' https://router.vuejs.org/api/interfaces/RouterOptions.html#history');\r\n    const beforeGuards = useCallbacks();\r\n    const beforeResolveGuards = useCallbacks();\r\n    const afterGuards = useCallbacks();\r\n    const currentRoute = shallowRef(START_LOCATION_NORMALIZED);\r\n    let pendingLocation = START_LOCATION_NORMALIZED;\r\n    // leave the scrollRestoration if no scrollBehavior is provided\r\n    if (isBrowser && options.scrollBehavior && 'scrollRestoration' in history) {\r\n        history.scrollRestoration = 'manual';\r\n    }\r\n    const normalizeParams = applyToParams.bind(null, paramValue => '' + paramValue);\r\n    const encodeParams = applyToParams.bind(null, encodeParam);\r\n    const decodeParams = \r\n    // @ts-expect-error: intentionally avoid the type check\r\n    applyToParams.bind(null, decode);\r\n    function addRoute(parentOrRoute, route) {\r\n        let parent;\r\n        let record;\r\n        if (isRouteName(parentOrRoute)) {\r\n            parent = matcher.getRecordMatcher(parentOrRoute);\r\n            if ((process.env.NODE_ENV !== 'production') && !parent) {\r\n                warn(`Parent route \"${String(parentOrRoute)}\" not found when adding child route`, route);\r\n            }\r\n            record = route;\r\n        }\r\n        else {\r\n            record = parentOrRoute;\r\n        }\r\n        return matcher.addRoute(record, parent);\r\n    }\r\n    function removeRoute(name) {\r\n        const recordMatcher = matcher.getRecordMatcher(name);\r\n        if (recordMatcher) {\r\n            matcher.removeRoute(recordMatcher);\r\n        }\r\n        else if ((process.env.NODE_ENV !== 'production')) {\r\n            warn(`Cannot remove non-existent route \"${String(name)}\"`);\r\n        }\r\n    }\r\n    function getRoutes() {\r\n        return matcher.getRoutes().map(routeMatcher => routeMatcher.record);\r\n    }\r\n    function hasRoute(name) {\r\n        return !!matcher.getRecordMatcher(name);\r\n    }\r\n    function resolve(rawLocation, currentLocation) {\r\n        // const resolve: Router['resolve'] = (rawLocation: RouteLocationRaw, currentLocation) => {\r\n        // const objectLocation = routerLocationAsObject(rawLocation)\r\n        // we create a copy to modify it later\r\n        currentLocation = assign({}, currentLocation || currentRoute.value);\r\n        if (typeof rawLocation === 'string') {\r\n            const locationNormalized = parseURL(parseQuery$1, rawLocation, currentLocation.path);\r\n            const matchedRoute = matcher.resolve({ path: locationNormalized.path }, currentLocation);\r\n            const href = routerHistory.createHref(locationNormalized.fullPath);\r\n            if ((process.env.NODE_ENV !== 'production')) {\r\n                if (href.startsWith('//'))\r\n                    warn(`Location \"${rawLocation}\" resolved to \"${href}\". A resolved location cannot start with multiple slashes.`);\r\n                else if (!matchedRoute.matched.length) {\r\n                    warn(`No match found for location with path \"${rawLocation}\"`);\r\n                }\r\n            }\r\n            // locationNormalized is always a new object\r\n            return assign(locationNormalized, matchedRoute, {\r\n                params: decodeParams(matchedRoute.params),\r\n                hash: decode(locationNormalized.hash),\r\n                redirectedFrom: undefined,\r\n                href,\r\n            });\r\n        }\r\n        if ((process.env.NODE_ENV !== 'production') && !isRouteLocation(rawLocation)) {\r\n            warn(`router.resolve() was passed an invalid location. This will fail in production.\\n- Location:`, rawLocation);\r\n            return resolve({});\r\n        }\r\n        let matcherLocation;\r\n        // path could be relative in object as well\r\n        if (rawLocation.path != null) {\r\n            if ((process.env.NODE_ENV !== 'production') &&\r\n                'params' in rawLocation &&\r\n                !('name' in rawLocation) &&\r\n                // @ts-expect-error: the type is never\r\n                Object.keys(rawLocation.params).length) {\r\n                warn(`Path \"${rawLocation.path}\" was passed with params but they will be ignored. Use a named route alongside params instead.`);\r\n            }\r\n            matcherLocation = assign({}, rawLocation, {\r\n                path: parseURL(parseQuery$1, rawLocation.path, currentLocation.path).path,\r\n            });\r\n        }\r\n        else {\r\n            // remove any nullish param\r\n            const targetParams = assign({}, rawLocation.params);\r\n            for (const key in targetParams) {\r\n                if (targetParams[key] == null) {\r\n                    delete targetParams[key];\r\n                }\r\n            }\r\n            // pass encoded values to the matcher, so it can produce encoded path and fullPath\r\n            matcherLocation = assign({}, rawLocation, {\r\n                params: encodeParams(targetParams),\r\n            });\r\n            // current location params are decoded, we need to encode them in case the\r\n            // matcher merges the params\r\n            currentLocation.params = encodeParams(currentLocation.params);\r\n        }\r\n        const matchedRoute = matcher.resolve(matcherLocation, currentLocation);\r\n        const hash = rawLocation.hash || '';\r\n        if ((process.env.NODE_ENV !== 'production') && hash && !hash.startsWith('#')) {\r\n            warn(`A \\`hash\\` should always start with the character \"#\". Replace \"${hash}\" with \"#${hash}\".`);\r\n        }\r\n        // the matcher might have merged current location params, so\r\n        // we need to run the decoding again\r\n        matchedRoute.params = normalizeParams(decodeParams(matchedRoute.params));\r\n        const fullPath = stringifyURL(stringifyQuery$1, assign({}, rawLocation, {\r\n            hash: encodeHash(hash),\r\n            path: matchedRoute.path,\r\n        }));\r\n        const href = routerHistory.createHref(fullPath);\r\n        if ((process.env.NODE_ENV !== 'production')) {\r\n            if (href.startsWith('//')) {\r\n                warn(`Location \"${rawLocation}\" resolved to \"${href}\". A resolved location cannot start with multiple slashes.`);\r\n            }\r\n            else if (!matchedRoute.matched.length) {\r\n                warn(`No match found for location with path \"${rawLocation.path != null ? rawLocation.path : rawLocation}\"`);\r\n            }\r\n        }\r\n        return assign({\r\n            fullPath,\r\n            // keep the hash encoded so fullPath is effectively path + encodedQuery +\r\n            // hash\r\n            hash,\r\n            query: \r\n            // if the user is using a custom query lib like qs, we might have\r\n            // nested objects, so we keep the query as is, meaning it can contain\r\n            // numbers at `$route.query`, but at the point, the user will have to\r\n            // use their own type anyway.\r\n            // https://github.com/vuejs/router/issues/328#issuecomment-649481567\r\n            stringifyQuery$1 === stringifyQuery\r\n                ? normalizeQuery(rawLocation.query)\r\n                : (rawLocation.query || {}),\r\n        }, matchedRoute, {\r\n            redirectedFrom: undefined,\r\n            href,\r\n        });\r\n    }\r\n    function locationAsObject(to) {\r\n        return typeof to === 'string'\r\n            ? parseURL(parseQuery$1, to, currentRoute.value.path)\r\n            : assign({}, to);\r\n    }\r\n    function checkCanceledNavigation(to, from) {\r\n        if (pendingLocation !== to) {\r\n            return createRouterError(8 /* ErrorTypes.NAVIGATION_CANCELLED */, {\r\n                from,\r\n                to,\r\n            });\r\n        }\r\n    }\r\n    function push(to) {\r\n        return pushWithRedirect(to);\r\n    }\r\n    function replace(to) {\r\n        return push(assign(locationAsObject(to), { replace: true }));\r\n    }\r\n    function handleRedirectRecord(to) {\r\n        const lastMatched = to.matched[to.matched.length - 1];\r\n        if (lastMatched && lastMatched.redirect) {\r\n            const { redirect } = lastMatched;\r\n            let newTargetLocation = typeof redirect === 'function' ? redirect(to) : redirect;\r\n            if (typeof newTargetLocation === 'string') {\r\n                newTargetLocation =\r\n                    newTargetLocation.includes('?') || newTargetLocation.includes('#')\r\n                        ? (newTargetLocation = locationAsObject(newTargetLocation))\r\n                        : // force empty params\r\n                            { path: newTargetLocation };\r\n                // @ts-expect-error: force empty params when a string is passed to let\r\n                // the router parse them again\r\n                newTargetLocation.params = {};\r\n            }\r\n            if ((process.env.NODE_ENV !== 'production') &&\r\n                newTargetLocation.path == null &&\r\n                !('name' in newTargetLocation)) {\r\n                warn(`Invalid redirect found:\\n${JSON.stringify(newTargetLocation, null, 2)}\\n when navigating to \"${to.fullPath}\". A redirect must contain a name or path. This will break in production.`);\r\n                throw new Error('Invalid redirect');\r\n            }\r\n            return assign({\r\n                query: to.query,\r\n                hash: to.hash,\r\n                // avoid transferring params if the redirect has a path\r\n                params: newTargetLocation.path != null ? {} : to.params,\r\n            }, newTargetLocation);\r\n        }\r\n    }\r\n    function pushWithRedirect(to, redirectedFrom) {\r\n        const targetLocation = (pendingLocation = resolve(to));\r\n        const from = currentRoute.value;\r\n        const data = to.state;\r\n        const force = to.force;\r\n        // to could be a string where `replace` is a function\r\n        const replace = to.replace === true;\r\n        const shouldRedirect = handleRedirectRecord(targetLocation);\r\n        if (shouldRedirect)\r\n            return pushWithRedirect(assign(locationAsObject(shouldRedirect), {\r\n                state: typeof shouldRedirect === 'object'\r\n                    ? assign({}, data, shouldRedirect.state)\r\n                    : data,\r\n                force,\r\n                replace,\r\n            }), \r\n            // keep original redirectedFrom if it exists\r\n            redirectedFrom || targetLocation);\r\n        // if it was a redirect we already called `pushWithRedirect` above\r\n        const toLocation = targetLocation;\r\n        toLocation.redirectedFrom = redirectedFrom;\r\n        let failure;\r\n        if (!force && isSameRouteLocation(stringifyQuery$1, from, targetLocation)) {\r\n            failure = createRouterError(16 /* ErrorTypes.NAVIGATION_DUPLICATED */, { to: toLocation, from });\r\n            // trigger scroll to allow scrolling to the same anchor\r\n            handleScroll(from, from, \r\n            // this is a push, the only way for it to be triggered from a\r\n            // history.listen is with a redirect, which makes it become a push\r\n            true, \r\n            // This cannot be the first navigation because the initial location\r\n            // cannot be manually navigated to\r\n            false);\r\n        }\r\n        return (failure ? Promise.resolve(failure) : navigate(toLocation, from))\r\n            .catch((error) => isNavigationFailure(error)\r\n            ? // navigation redirects still mark the router as ready\r\n                isNavigationFailure(error, 2 /* ErrorTypes.NAVIGATION_GUARD_REDIRECT */)\r\n                    ? error\r\n                    : markAsReady(error) // also returns the error\r\n            : // reject any unknown error\r\n                triggerError(error, toLocation, from))\r\n            .then((failure) => {\r\n            if (failure) {\r\n                if (isNavigationFailure(failure, 2 /* ErrorTypes.NAVIGATION_GUARD_REDIRECT */)) {\r\n                    if ((process.env.NODE_ENV !== 'production') &&\r\n                        // we are redirecting to the same location we were already at\r\n                        isSameRouteLocation(stringifyQuery$1, resolve(failure.to), toLocation) &&\r\n                        // and we have done it a couple of times\r\n                        redirectedFrom &&\r\n                        // @ts-expect-error: added only in dev\r\n                        (redirectedFrom._count = redirectedFrom._count\r\n                            ? // @ts-expect-error\r\n                                redirectedFrom._count + 1\r\n                            : 1) > 30) {\r\n                        warn(`Detected a possibly infinite redirection in a navigation guard when going from \"${from.fullPath}\" to \"${toLocation.fullPath}\". Aborting to avoid a Stack Overflow.\\n Are you always returning a new location within a navigation guard? That would lead to this error. Only return when redirecting or aborting, that should fix this. This might break in production if not fixed.`);\r\n                        return Promise.reject(new Error('Infinite redirect in navigation guard'));\r\n                    }\r\n                    return pushWithRedirect(\r\n                    // keep options\r\n                    assign({\r\n                        // preserve an existing replacement but allow the redirect to override it\r\n                        replace,\r\n                    }, locationAsObject(failure.to), {\r\n                        state: typeof failure.to === 'object'\r\n                            ? assign({}, data, failure.to.state)\r\n                            : data,\r\n                        force,\r\n                    }), \r\n                    // preserve the original redirectedFrom if any\r\n                    redirectedFrom || toLocation);\r\n                }\r\n            }\r\n            else {\r\n                // if we fail we don't finalize the navigation\r\n                failure = finalizeNavigation(toLocation, from, true, replace, data);\r\n            }\r\n            triggerAfterEach(toLocation, from, failure);\r\n            return failure;\r\n        });\r\n    }\r\n    /**\r\n     * Helper to reject and skip all navigation guards if a new navigation happened\r\n     * @param to\r\n     * @param from\r\n     */\r\n    function checkCanceledNavigationAndReject(to, from) {\r\n        const error = checkCanceledNavigation(to, from);\r\n        return error ? Promise.reject(error) : Promise.resolve();\r\n    }\r\n    function runWithContext(fn) {\r\n        const app = installedApps.values().next().value;\r\n        // support Vue < 3.3\r\n        return app && typeof app.runWithContext === 'function'\r\n            ? app.runWithContext(fn)\r\n            : fn();\r\n    }\r\n    // TODO: refactor the whole before guards by internally using router.beforeEach\r\n    function navigate(to, from) {\r\n        let guards;\r\n        const [leavingRecords, updatingRecords, enteringRecords] = extractChangingRecords(to, from);\r\n        // all components here have been resolved once because we are leaving\r\n        guards = extractComponentsGuards(leavingRecords.reverse(), 'beforeRouteLeave', to, from);\r\n        // leavingRecords is already reversed\r\n        for (const record of leavingRecords) {\r\n            record.leaveGuards.forEach(guard => {\r\n                guards.push(guardToPromiseFn(guard, to, from));\r\n            });\r\n        }\r\n        const canceledNavigationCheck = checkCanceledNavigationAndReject.bind(null, to, from);\r\n        guards.push(canceledNavigationCheck);\r\n        // run the queue of per route beforeRouteLeave guards\r\n        return (runGuardQueue(guards)\r\n            .then(() => {\r\n            // check global guards beforeEach\r\n            guards = [];\r\n            for (const guard of beforeGuards.list()) {\r\n                guards.push(guardToPromiseFn(guard, to, from));\r\n            }\r\n            guards.push(canceledNavigationCheck);\r\n            return runGuardQueue(guards);\r\n        })\r\n            .then(() => {\r\n            // check in components beforeRouteUpdate\r\n            guards = extractComponentsGuards(updatingRecords, 'beforeRouteUpdate', to, from);\r\n            for (const record of updatingRecords) {\r\n                record.updateGuards.forEach(guard => {\r\n                    guards.push(guardToPromiseFn(guard, to, from));\r\n                });\r\n            }\r\n            guards.push(canceledNavigationCheck);\r\n            // run the queue of per route beforeEnter guards\r\n            return runGuardQueue(guards);\r\n        })\r\n            .then(() => {\r\n            // check the route beforeEnter\r\n            guards = [];\r\n            for (const record of enteringRecords) {\r\n                // do not trigger beforeEnter on reused views\r\n                if (record.beforeEnter) {\r\n                    if (isArray(record.beforeEnter)) {\r\n                        for (const beforeEnter of record.beforeEnter)\r\n                            guards.push(guardToPromiseFn(beforeEnter, to, from));\r\n                    }\r\n                    else {\r\n                        guards.push(guardToPromiseFn(record.beforeEnter, to, from));\r\n                    }\r\n                }\r\n            }\r\n            guards.push(canceledNavigationCheck);\r\n            // run the queue of per route beforeEnter guards\r\n            return runGuardQueue(guards);\r\n        })\r\n            .then(() => {\r\n            // NOTE: at this point to.matched is normalized and does not contain any () => Promise<Component>\r\n            // clear existing enterCallbacks, these are added by extractComponentsGuards\r\n            to.matched.forEach(record => (record.enterCallbacks = {}));\r\n            // check in-component beforeRouteEnter\r\n            guards = extractComponentsGuards(enteringRecords, 'beforeRouteEnter', to, from, runWithContext);\r\n            guards.push(canceledNavigationCheck);\r\n            // run the queue of per route beforeEnter guards\r\n            return runGuardQueue(guards);\r\n        })\r\n            .then(() => {\r\n            // check global guards beforeResolve\r\n            guards = [];\r\n            for (const guard of beforeResolveGuards.list()) {\r\n                guards.push(guardToPromiseFn(guard, to, from));\r\n            }\r\n            guards.push(canceledNavigationCheck);\r\n            return runGuardQueue(guards);\r\n        })\r\n            // catch any navigation canceled\r\n            .catch(err => isNavigationFailure(err, 8 /* ErrorTypes.NAVIGATION_CANCELLED */)\r\n            ? err\r\n            : Promise.reject(err)));\r\n    }\r\n    function triggerAfterEach(to, from, failure) {\r\n        // navigation is confirmed, call afterGuards\r\n        // TODO: wrap with error handlers\r\n        afterGuards\r\n            .list()\r\n            .forEach(guard => runWithContext(() => guard(to, from, failure)));\r\n    }\r\n    /**\r\n     * - Cleans up any navigation guards\r\n     * - Changes the url if necessary\r\n     * - Calls the scrollBehavior\r\n     */\r\n    function finalizeNavigation(toLocation, from, isPush, replace, data) {\r\n        // a more recent navigation took place\r\n        const error = checkCanceledNavigation(toLocation, from);\r\n        if (error)\r\n            return error;\r\n        // only consider as push if it's not the first navigation\r\n        const isFirstNavigation = from === START_LOCATION_NORMALIZED;\r\n        const state = !isBrowser ? {} : history.state;\r\n        // change URL only if the user did a push/replace and if it's not the initial navigation because\r\n        // it's just reflecting the url\r\n        if (isPush) {\r\n            // on the initial navigation, we want to reuse the scroll position from\r\n            // history state if it exists\r\n            if (replace || isFirstNavigation)\r\n                routerHistory.replace(toLocation.fullPath, assign({\r\n                    scroll: isFirstNavigation && state && state.scroll,\r\n                }, data));\r\n            else\r\n                routerHistory.push(toLocation.fullPath, data);\r\n        }\r\n        // accept current navigation\r\n        currentRoute.value = toLocation;\r\n        handleScroll(toLocation, from, isPush, isFirstNavigation);\r\n        markAsReady();\r\n    }\r\n    let removeHistoryListener;\r\n    // attach listener to history to trigger navigations\r\n    function setupListeners() {\r\n        // avoid setting up listeners twice due to an invalid first navigation\r\n        if (removeHistoryListener)\r\n            return;\r\n        removeHistoryListener = routerHistory.listen((to, _from, info) => {\r\n            if (!router.listening)\r\n                return;\r\n            // cannot be a redirect route because it was in history\r\n            const toLocation = resolve(to);\r\n            // due to dynamic routing, and to hash history with manual navigation\r\n            // (manually changing the url or calling history.hash = '#/somewhere'),\r\n            // there could be a redirect record in history\r\n            const shouldRedirect = handleRedirectRecord(toLocation);\r\n            if (shouldRedirect) {\r\n                pushWithRedirect(assign(shouldRedirect, { replace: true, force: true }), toLocation).catch(noop);\r\n                return;\r\n            }\r\n            pendingLocation = toLocation;\r\n            const from = currentRoute.value;\r\n            // TODO: should be moved to web history?\r\n            if (isBrowser) {\r\n                saveScrollPosition(getScrollKey(from.fullPath, info.delta), computeScrollPosition());\r\n            }\r\n            navigate(toLocation, from)\r\n                .catch((error) => {\r\n                if (isNavigationFailure(error, 4 /* ErrorTypes.NAVIGATION_ABORTED */ | 8 /* ErrorTypes.NAVIGATION_CANCELLED */)) {\r\n                    return error;\r\n                }\r\n                if (isNavigationFailure(error, 2 /* ErrorTypes.NAVIGATION_GUARD_REDIRECT */)) {\r\n                    // Here we could call if (info.delta) routerHistory.go(-info.delta,\r\n                    // false) but this is bug prone as we have no way to wait the\r\n                    // navigation to be finished before calling pushWithRedirect. Using\r\n                    // a setTimeout of 16ms seems to work but there is no guarantee for\r\n                    // it to work on every browser. So instead we do not restore the\r\n                    // history entry and trigger a new navigation as requested by the\r\n                    // navigation guard.\r\n                    // the error is already handled by router.push we just want to avoid\r\n                    // logging the error\r\n                    pushWithRedirect(assign(locationAsObject(error.to), {\r\n                        force: true,\r\n                    }), toLocation\r\n                    // avoid an uncaught rejection, let push call triggerError\r\n                    )\r\n                        .then(failure => {\r\n                        // manual change in hash history #916 ending up in the URL not\r\n                        // changing, but it was changed by the manual url change, so we\r\n                        // need to manually change it ourselves\r\n                        if (isNavigationFailure(failure, 4 /* ErrorTypes.NAVIGATION_ABORTED */ |\r\n                            16 /* ErrorTypes.NAVIGATION_DUPLICATED */) &&\r\n                            !info.delta &&\r\n                            info.type === NavigationType.pop) {\r\n                            routerHistory.go(-1, false);\r\n                        }\r\n                    })\r\n                        .catch(noop);\r\n                    // avoid the then branch\r\n                    return Promise.reject();\r\n                }\r\n                // do not restore history on unknown direction\r\n                if (info.delta) {\r\n                    routerHistory.go(-info.delta, false);\r\n                }\r\n                // unrecognized error, transfer to the global handler\r\n                return triggerError(error, toLocation, from);\r\n            })\r\n                .then((failure) => {\r\n                failure =\r\n                    failure ||\r\n                        finalizeNavigation(\r\n                        // after navigation, all matched components are resolved\r\n                        toLocation, from, false);\r\n                // revert the navigation\r\n                if (failure) {\r\n                    if (info.delta &&\r\n                        // a new navigation has been triggered, so we do not want to revert, that will change the current history\r\n                        // entry while a different route is displayed\r\n                        !isNavigationFailure(failure, 8 /* ErrorTypes.NAVIGATION_CANCELLED */)) {\r\n                        routerHistory.go(-info.delta, false);\r\n                    }\r\n                    else if (info.type === NavigationType.pop &&\r\n                        isNavigationFailure(failure, 4 /* ErrorTypes.NAVIGATION_ABORTED */ | 16 /* ErrorTypes.NAVIGATION_DUPLICATED */)) {\r\n                        // manual change in hash history #916\r\n                        // it's like a push but lacks the information of the direction\r\n                        routerHistory.go(-1, false);\r\n                    }\r\n                }\r\n                triggerAfterEach(toLocation, from, failure);\r\n            })\r\n                // avoid warnings in the console about uncaught rejections, they are logged by triggerErrors\r\n                .catch(noop);\r\n        });\r\n    }\r\n    // Initialization and Errors\r\n    let readyHandlers = useCallbacks();\r\n    let errorListeners = useCallbacks();\r\n    let ready;\r\n    /**\r\n     * Trigger errorListeners added via onError and throws the error as well\r\n     *\r\n     * @param error - error to throw\r\n     * @param to - location we were navigating to when the error happened\r\n     * @param from - location we were navigating from when the error happened\r\n     * @returns the error as a rejected promise\r\n     */\r\n    function triggerError(error, to, from) {\r\n        markAsReady(error);\r\n        const list = errorListeners.list();\r\n        if (list.length) {\r\n            list.forEach(handler => handler(error, to, from));\r\n        }\r\n        else {\r\n            if ((process.env.NODE_ENV !== 'production')) {\r\n                warn('uncaught error during route navigation:');\r\n            }\r\n            console.error(error);\r\n        }\r\n        // reject the error no matter there were error listeners or not\r\n        return Promise.reject(error);\r\n    }\r\n    function isReady() {\r\n        if (ready && currentRoute.value !== START_LOCATION_NORMALIZED)\r\n            return Promise.resolve();\r\n        return new Promise((resolve, reject) => {\r\n            readyHandlers.add([resolve, reject]);\r\n        });\r\n    }\r\n    function markAsReady(err) {\r\n        if (!ready) {\r\n            // still not ready if an error happened\r\n            ready = !err;\r\n            setupListeners();\r\n            readyHandlers\r\n                .list()\r\n                .forEach(([resolve, reject]) => (err ? reject(err) : resolve()));\r\n            readyHandlers.reset();\r\n        }\r\n        return err;\r\n    }\r\n    // Scroll behavior\r\n    function handleScroll(to, from, isPush, isFirstNavigation) {\r\n        const { scrollBehavior } = options;\r\n        if (!isBrowser || !scrollBehavior)\r\n            return Promise.resolve();\r\n        const scrollPosition = (!isPush && getSavedScrollPosition(getScrollKey(to.fullPath, 0))) ||\r\n            ((isFirstNavigation || !isPush) &&\r\n                history.state &&\r\n                history.state.scroll) ||\r\n            null;\r\n        return nextTick()\r\n            .then(() => scrollBehavior(to, from, scrollPosition))\r\n            .then(position => position && scrollToPosition(position))\r\n            .catch(err => triggerError(err, to, from));\r\n    }\r\n    const go = (delta) => routerHistory.go(delta);\r\n    let started;\r\n    const installedApps = new Set();\r\n    const router = {\r\n        currentRoute,\r\n        listening: true,\r\n        addRoute,\r\n        removeRoute,\r\n        clearRoutes: matcher.clearRoutes,\r\n        hasRoute,\r\n        getRoutes,\r\n        resolve,\r\n        options,\r\n        push,\r\n        replace,\r\n        go,\r\n        back: () => go(-1),\r\n        forward: () => go(1),\r\n        beforeEach: beforeGuards.add,\r\n        beforeResolve: beforeResolveGuards.add,\r\n        afterEach: afterGuards.add,\r\n        onError: errorListeners.add,\r\n        isReady,\r\n        install(app) {\r\n            const router = this;\r\n            app.component('RouterLink', RouterLink);\r\n            app.component('RouterView', RouterView);\r\n            app.config.globalProperties.$router = router;\r\n            Object.defineProperty(app.config.globalProperties, '$route', {\r\n                enumerable: true,\r\n                get: () => unref(currentRoute),\r\n            });\r\n            // this initial navigation is only necessary on client, on server it doesn't\r\n            // make sense because it will create an extra unnecessary navigation and could\r\n            // lead to problems\r\n            if (isBrowser &&\r\n                // used for the initial navigation client side to avoid pushing\r\n                // multiple times when the router is used in multiple apps\r\n                !started &&\r\n                currentRoute.value === START_LOCATION_NORMALIZED) {\r\n                // see above\r\n                started = true;\r\n                push(routerHistory.location).catch(err => {\r\n                    if ((process.env.NODE_ENV !== 'production'))\r\n                        warn('Unexpected error when starting the router:', err);\r\n                });\r\n            }\r\n            const reactiveRoute = {};\r\n            for (const key in START_LOCATION_NORMALIZED) {\r\n                Object.defineProperty(reactiveRoute, key, {\r\n                    get: () => currentRoute.value[key],\r\n                    enumerable: true,\r\n                });\r\n            }\r\n            app.provide(routerKey, router);\r\n            app.provide(routeLocationKey, shallowReactive(reactiveRoute));\r\n            app.provide(routerViewLocationKey, currentRoute);\r\n            const unmountApp = app.unmount;\r\n            installedApps.add(app);\r\n            app.unmount = function () {\r\n                installedApps.delete(app);\r\n                // the router is not attached to an app anymore\r\n                if (installedApps.size < 1) {\r\n                    // invalidate the current navigation\r\n                    pendingLocation = START_LOCATION_NORMALIZED;\r\n                    removeHistoryListener && removeHistoryListener();\r\n                    removeHistoryListener = null;\r\n                    currentRoute.value = START_LOCATION_NORMALIZED;\r\n                    started = false;\r\n                    ready = false;\r\n                }\r\n                unmountApp();\r\n            };\r\n            // TODO: this probably needs to be updated so it can be used by vue-termui\r\n            if (((process.env.NODE_ENV !== 'production') || __VUE_PROD_DEVTOOLS__) && isBrowser) {\r\n                addDevtools(app, router, matcher);\r\n            }\r\n        },\r\n    };\r\n    // TODO: type this as NavigationGuardReturn or similar instead of any\r\n    function runGuardQueue(guards) {\r\n        return guards.reduce((promise, guard) => promise.then(() => runWithContext(guard)), Promise.resolve());\r\n    }\r\n    return router;\r\n}\r\nfunction extractChangingRecords(to, from) {\r\n    const leavingRecords = [];\r\n    const updatingRecords = [];\r\n    const enteringRecords = [];\r\n    const len = Math.max(from.matched.length, to.matched.length);\r\n    for (let i = 0; i < len; i++) {\r\n        const recordFrom = from.matched[i];\r\n        if (recordFrom) {\r\n            if (to.matched.find(record => isSameRouteRecord(record, recordFrom)))\r\n                updatingRecords.push(recordFrom);\r\n            else\r\n                leavingRecords.push(recordFrom);\r\n        }\r\n        const recordTo = to.matched[i];\r\n        if (recordTo) {\r\n            // the type doesn't matter because we are comparing per reference\r\n            if (!from.matched.find(record => isSameRouteRecord(record, recordTo))) {\r\n                enteringRecords.push(recordTo);\r\n            }\r\n        }\r\n    }\r\n    return [leavingRecords, updatingRecords, enteringRecords];\r\n}\r\n\r\n/**\r\n * Returns the router instance. Equivalent to using `$router` inside\r\n * templates.\r\n */\r\nfunction useRouter() {\r\n    return inject(routerKey);\r\n}\r\n/**\r\n * Returns the current route location. Equivalent to using `$route` inside\r\n * templates.\r\n */\r\nfunction useRoute(_name) {\r\n    return inject(routeLocationKey);\r\n}\r\n\r\nexport { NavigationFailureType, RouterLink, RouterView, START_LOCATION_NORMALIZED as START_LOCATION, createMemoryHistory, createRouter, createRouterMatcher, createWebHashHistory, createWebHistory, isNavigationFailure, loadRouteLocation, matchedRouteKey, onBeforeRouteLeave, onBeforeRouteUpdate, parseQuery, routeLocationKey, routerKey, routerViewLocationKey, stringifyQuery, useLink, useRoute, useRouter, viewDepthKey };\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;AAQA,IAAM,YAAY,OAAO,aAAa;AAQtC,SAAS,iBAAiB,WAAW;AACjC,SAAQ,OAAO,cAAc,YACzB,iBAAiB,aACjB,WAAW,aACX,eAAe;AACvB;AACA,SAAS,WAAW,KAAK;AACrB,SAAQ,IAAI,cACR,IAAI,OAAO,WAAW,MAAM;AAAA;AAAA,EAG3B,IAAI,WAAW,iBAAiB,IAAI,OAAO;AACpD;AACA,IAAM,SAAS,OAAO;AACtB,SAAS,cAAc,IAAI,QAAQ;AAC/B,QAAM,YAAY,CAAC;AACnB,aAAW,OAAO,QAAQ;AACtB,UAAM,QAAQ,OAAO,GAAG;AACxB,cAAU,GAAG,IAAI,QAAQ,KAAK,IACxB,MAAM,IAAI,EAAE,IACZ,GAAG,KAAK;AAAA,EAClB;AACA,SAAO;AACX;AACA,IAAM,OAAO,MAAM;AAAE;AAKrB,IAAM,UAAU,MAAM;AAEtB,SAAS,KAAK,KAAK;AAEf,QAAM,OAAO,MAAM,KAAK,SAAS,EAAE,MAAM,CAAC;AAC1C,UAAQ,KAAK,MAAM,SAAS,CAAC,wBAAwB,GAAG,EAAE,OAAO,IAAI,CAAC;AAC1E;AAqBA,IAAM,UAAU;AAChB,IAAM,eAAe;AACrB,IAAM,WAAW;AACjB,IAAM,WAAW;AACjB,IAAM,QAAQ;AACd,IAAM,UAAU;AAehB,IAAM,sBAAsB;AAC5B,IAAM,uBAAuB;AAC7B,IAAM,eAAe;AACrB,IAAM,kBAAkB;AACxB,IAAM,oBAAoB;AAC1B,IAAM,cAAc;AACpB,IAAM,qBAAqB;AAC3B,IAAM,eAAe;AASrB,SAAS,aAAa,MAAM;AACxB,SAAO,UAAU,KAAK,IAAI,EACrB,QAAQ,aAAa,GAAG,EACxB,QAAQ,qBAAqB,GAAG,EAChC,QAAQ,sBAAsB,GAAG;AAC1C;AAOA,SAAS,WAAW,MAAM;AACtB,SAAO,aAAa,IAAI,EACnB,QAAQ,mBAAmB,GAAG,EAC9B,QAAQ,oBAAoB,GAAG,EAC/B,QAAQ,cAAc,GAAG;AAClC;AAQA,SAAS,iBAAiB,MAAM;AAC5B,SAAQ,aAAa,IAAI,EAEpB,QAAQ,SAAS,KAAK,EACtB,QAAQ,cAAc,GAAG,EACzB,QAAQ,SAAS,KAAK,EACtB,QAAQ,cAAc,KAAK,EAC3B,QAAQ,iBAAiB,GAAG,EAC5B,QAAQ,mBAAmB,GAAG,EAC9B,QAAQ,oBAAoB,GAAG,EAC/B,QAAQ,cAAc,GAAG;AAClC;AAMA,SAAS,eAAe,MAAM;AAC1B,SAAO,iBAAiB,IAAI,EAAE,QAAQ,UAAU,KAAK;AACzD;AAOA,SAAS,WAAW,MAAM;AACtB,SAAO,aAAa,IAAI,EAAE,QAAQ,SAAS,KAAK,EAAE,QAAQ,OAAO,KAAK;AAC1E;AAUA,SAAS,YAAY,MAAM;AACvB,SAAO,QAAQ,OAAO,KAAK,WAAW,IAAI,EAAE,QAAQ,UAAU,KAAK;AACvE;AAQA,SAAS,OAAO,MAAM;AAClB,MAAI;AACA,WAAO,mBAAmB,KAAK,IAAI;AAAA,EACvC,SACO,KAAK;AACR,IAA2C,KAAK,mBAAmB,IAAI,yBAAyB;AAAA,EACpG;AACA,SAAO,KAAK;AAChB;AAEA,IAAM,oBAAoB;AAC1B,IAAM,sBAAsB,CAAC,SAAS,KAAK,QAAQ,mBAAmB,EAAE;AAUxE,SAAS,SAASA,aAAYC,WAAU,kBAAkB,KAAK;AAC3D,MAAI,MAAM,QAAQ,CAAC,GAAG,eAAe,IAAI,OAAO;AAGhD,QAAM,UAAUA,UAAS,QAAQ,GAAG;AACpC,MAAI,YAAYA,UAAS,QAAQ,GAAG;AAEpC,MAAI,UAAU,aAAa,WAAW,GAAG;AACrC,gBAAY;AAAA,EAChB;AACA,MAAI,YAAY,IAAI;AAChB,WAAOA,UAAS,MAAM,GAAG,SAAS;AAClC,mBAAeA,UAAS,MAAM,YAAY,GAAG,UAAU,KAAK,UAAUA,UAAS,MAAM;AACrF,YAAQD,YAAW,YAAY;AAAA,EACnC;AACA,MAAI,UAAU,IAAI;AACd,WAAO,QAAQC,UAAS,MAAM,GAAG,OAAO;AAExC,WAAOA,UAAS,MAAM,SAASA,UAAS,MAAM;AAAA,EAClD;AAEA,SAAO,oBAAoB,QAAQ,OAAO,OAAOA,WAAU,eAAe;AAE1E,SAAO;AAAA,IACH,UAAU,QAAQ,gBAAgB,OAAO,eAAe;AAAA,IACxD;AAAA,IACA;AAAA,IACA,MAAM,OAAO,IAAI;AAAA,EACrB;AACJ;AAOA,SAAS,aAAaC,iBAAgBD,WAAU;AAC5C,QAAM,QAAQA,UAAS,QAAQC,gBAAeD,UAAS,KAAK,IAAI;AAChE,SAAOA,UAAS,QAAQ,SAAS,OAAO,SAASA,UAAS,QAAQ;AACtE;AAOA,SAAS,UAAU,UAAU,MAAM;AAE/B,MAAI,CAAC,QAAQ,CAAC,SAAS,YAAY,EAAE,WAAW,KAAK,YAAY,CAAC;AAC9D,WAAO;AACX,SAAO,SAAS,MAAM,KAAK,MAAM,KAAK;AAC1C;AAUA,SAAS,oBAAoBC,iBAAgB,GAAG,GAAG;AAC/C,QAAM,aAAa,EAAE,QAAQ,SAAS;AACtC,QAAM,aAAa,EAAE,QAAQ,SAAS;AACtC,SAAQ,aAAa,MACjB,eAAe,cACf,kBAAkB,EAAE,QAAQ,UAAU,GAAG,EAAE,QAAQ,UAAU,CAAC,KAC9D,0BAA0B,EAAE,QAAQ,EAAE,MAAM,KAC5CA,gBAAe,EAAE,KAAK,MAAMA,gBAAe,EAAE,KAAK,KAClD,EAAE,SAAS,EAAE;AACrB;AAQA,SAAS,kBAAkB,GAAG,GAAG;AAI7B,UAAQ,EAAE,WAAW,QAAQ,EAAE,WAAW;AAC9C;AACA,SAAS,0BAA0B,GAAG,GAAG;AACrC,MAAI,OAAO,KAAK,CAAC,EAAE,WAAW,OAAO,KAAK,CAAC,EAAE;AACzC,WAAO;AACX,aAAW,OAAO,GAAG;AACjB,QAAI,CAAC,+BAA+B,EAAE,GAAG,GAAG,EAAE,GAAG,CAAC;AAC9C,aAAO;AAAA,EACf;AACA,SAAO;AACX;AACA,SAAS,+BAA+B,GAAG,GAAG;AAC1C,SAAO,QAAQ,CAAC,IACV,kBAAkB,GAAG,CAAC,IACtB,QAAQ,CAAC,IACL,kBAAkB,GAAG,CAAC,IACtB,MAAM;AACpB;AAQA,SAAS,kBAAkB,GAAG,GAAG;AAC7B,SAAO,QAAQ,CAAC,IACV,EAAE,WAAW,EAAE,UAAU,EAAE,MAAM,CAAC,OAAO,MAAM,UAAU,EAAE,CAAC,CAAC,IAC7D,EAAE,WAAW,KAAK,EAAE,CAAC,MAAM;AACrC;AAOA,SAAS,oBAAoB,IAAI,MAAM;AACnC,MAAI,GAAG,WAAW,GAAG;AACjB,WAAO;AACX,MAA+C,CAAC,KAAK,WAAW,GAAG,GAAG;AAClE,SAAK,mFAAmF,EAAE,WAAW,IAAI,4BAA4B,IAAI,IAAI;AAC7I,WAAO;AAAA,EACX;AACA,MAAI,CAAC;AACD,WAAO;AACX,QAAM,eAAe,KAAK,MAAM,GAAG;AACnC,QAAM,aAAa,GAAG,MAAM,GAAG;AAC/B,QAAM,gBAAgB,WAAW,WAAW,SAAS,CAAC;AAGtD,MAAI,kBAAkB,QAAQ,kBAAkB,KAAK;AACjD,eAAW,KAAK,EAAE;AAAA,EACtB;AACA,MAAI,WAAW,aAAa,SAAS;AACrC,MAAI;AACJ,MAAI;AACJ,OAAK,aAAa,GAAG,aAAa,WAAW,QAAQ,cAAc;AAC/D,cAAU,WAAW,UAAU;AAE/B,QAAI,YAAY;AACZ;AAEJ,QAAI,YAAY,MAAM;AAElB,UAAI,WAAW;AACX;AAAA,IAER;AAGI;AAAA,EACR;AACA,SAAQ,aAAa,MAAM,GAAG,QAAQ,EAAE,KAAK,GAAG,IAC5C,MACA,WAAW,MAAM,UAAU,EAAE,KAAK,GAAG;AAC7C;AAgBA,IAAM,4BAA4B;AAAA,EAC9B,MAAM;AAAA;AAAA,EAEN,MAAM;AAAA,EACN,QAAQ,CAAC;AAAA,EACT,OAAO,CAAC;AAAA,EACR,MAAM;AAAA,EACN,UAAU;AAAA,EACV,SAAS,CAAC;AAAA,EACV,MAAM,CAAC;AAAA,EACP,gBAAgB;AACpB;AAEA,IAAI;AAAA,CACH,SAAUC,iBAAgB;AACvB,EAAAA,gBAAe,KAAK,IAAI;AACxB,EAAAA,gBAAe,MAAM,IAAI;AAC7B,GAAG,mBAAmB,iBAAiB,CAAC,EAAE;AAC1C,IAAI;AAAA,CACH,SAAUC,sBAAqB;AAC5B,EAAAA,qBAAoB,MAAM,IAAI;AAC9B,EAAAA,qBAAoB,SAAS,IAAI;AACjC,EAAAA,qBAAoB,SAAS,IAAI;AACrC,GAAG,wBAAwB,sBAAsB,CAAC,EAAE;AAIpD,IAAM,QAAQ;AAQd,SAAS,cAAc,MAAM;AACzB,MAAI,CAAC,MAAM;AACP,QAAI,WAAW;AAEX,YAAM,SAAS,SAAS,cAAc,MAAM;AAC5C,aAAQ,UAAU,OAAO,aAAa,MAAM,KAAM;AAElD,aAAO,KAAK,QAAQ,mBAAmB,EAAE;AAAA,IAC7C,OACK;AACD,aAAO;AAAA,IACX;AAAA,EACJ;AAIA,MAAI,KAAK,CAAC,MAAM,OAAO,KAAK,CAAC,MAAM;AAC/B,WAAO,MAAM;AAGjB,SAAO,oBAAoB,IAAI;AACnC;AAEA,IAAM,iBAAiB;AACvB,SAAS,WAAW,MAAMH,WAAU;AAChC,SAAO,KAAK,QAAQ,gBAAgB,GAAG,IAAIA;AAC/C;AAEA,SAAS,mBAAmB,IAAI,QAAQ;AACpC,QAAM,UAAU,SAAS,gBAAgB,sBAAsB;AAC/D,QAAM,SAAS,GAAG,sBAAsB;AACxC,SAAO;AAAA,IACH,UAAU,OAAO;AAAA,IACjB,MAAM,OAAO,OAAO,QAAQ,QAAQ,OAAO,QAAQ;AAAA,IACnD,KAAK,OAAO,MAAM,QAAQ,OAAO,OAAO,OAAO;AAAA,EACnD;AACJ;AACA,IAAM,wBAAwB,OAAO;AAAA,EACjC,MAAM,OAAO;AAAA,EACb,KAAK,OAAO;AAChB;AACA,SAAS,iBAAiB,UAAU;AAChC,MAAI;AACJ,MAAI,QAAQ,UAAU;AAClB,UAAM,aAAa,SAAS;AAC5B,UAAM,eAAe,OAAO,eAAe,YAAY,WAAW,WAAW,GAAG;AAsBhF,QAA+C,OAAO,SAAS,OAAO,UAAU;AAC5E,UAAI,CAAC,gBAAgB,CAAC,SAAS,eAAe,SAAS,GAAG,MAAM,CAAC,CAAC,GAAG;AACjE,YAAI;AACA,gBAAM,UAAU,SAAS,cAAc,SAAS,EAAE;AAClD,cAAI,gBAAgB,SAAS;AACzB,iBAAK,iBAAiB,SAAS,EAAE,sDAAsD,SAAS,EAAE,iCAAiC;AAEnI;AAAA,UACJ;AAAA,QACJ,SACO,KAAK;AACR,eAAK,iBAAiB,SAAS,EAAE,4QAA4Q;AAE7S;AAAA,QACJ;AAAA,MACJ;AAAA,IACJ;AACA,UAAM,KAAK,OAAO,eAAe,WAC3B,eACI,SAAS,eAAe,WAAW,MAAM,CAAC,CAAC,IAC3C,SAAS,cAAc,UAAU,IACrC;AACN,QAAI,CAAC,IAAI;AACL,MACI,KAAK,yCAAyC,SAAS,EAAE,+BAA+B;AAC5F;AAAA,IACJ;AACA,sBAAkB,mBAAmB,IAAI,QAAQ;AAAA,EACrD,OACK;AACD,sBAAkB;AAAA,EACtB;AACA,MAAI,oBAAoB,SAAS,gBAAgB;AAC7C,WAAO,SAAS,eAAe;AAAA,OAC9B;AACD,WAAO,SAAS,gBAAgB,QAAQ,OAAO,gBAAgB,OAAO,OAAO,SAAS,gBAAgB,OAAO,OAAO,gBAAgB,MAAM,OAAO,OAAO;AAAA,EAC5J;AACJ;AACA,SAAS,aAAa,MAAM,OAAO;AAC/B,QAAM,WAAW,QAAQ,QAAQ,QAAQ,MAAM,WAAW,QAAQ;AAClE,SAAO,WAAW;AACtB;AACA,IAAM,kBAAkB,oBAAI,IAAI;AAChC,SAAS,mBAAmB,KAAK,gBAAgB;AAC7C,kBAAgB,IAAI,KAAK,cAAc;AAC3C;AACA,SAAS,uBAAuB,KAAK;AACjC,QAAM,SAAS,gBAAgB,IAAI,GAAG;AAEtC,kBAAgB,OAAO,GAAG;AAC1B,SAAO;AACX;AAiBA,IAAI,qBAAqB,MAAM,SAAS,WAAW,OAAO,SAAS;AAMnE,SAAS,sBAAsB,MAAMA,WAAU;AAC3C,QAAM,EAAE,UAAU,QAAQ,KAAK,IAAIA;AAEnC,QAAM,UAAU,KAAK,QAAQ,GAAG;AAChC,MAAI,UAAU,IAAI;AACd,QAAI,WAAW,KAAK,SAAS,KAAK,MAAM,OAAO,CAAC,IAC1C,KAAK,MAAM,OAAO,EAAE,SACpB;AACN,QAAI,eAAe,KAAK,MAAM,QAAQ;AAEtC,QAAI,aAAa,CAAC,MAAM;AACpB,qBAAe,MAAM;AACzB,WAAO,UAAU,cAAc,EAAE;AAAA,EACrC;AACA,QAAM,OAAO,UAAU,UAAU,IAAI;AACrC,SAAO,OAAO,SAAS;AAC3B;AACA,SAAS,oBAAoB,MAAM,cAAc,iBAAiB,SAAS;AACvE,MAAI,YAAY,CAAC;AACjB,MAAI,YAAY,CAAC;AAGjB,MAAI,aAAa;AACjB,QAAM,kBAAkB,CAAC,EAAE,MAAO,MAAM;AACpC,UAAM,KAAK,sBAAsB,MAAM,QAAQ;AAC/C,UAAM,OAAO,gBAAgB;AAC7B,UAAM,YAAY,aAAa;AAC/B,QAAI,QAAQ;AACZ,QAAI,OAAO;AACP,sBAAgB,QAAQ;AACxB,mBAAa,QAAQ;AAErB,UAAI,cAAc,eAAe,MAAM;AACnC,qBAAa;AACb;AAAA,MACJ;AACA,cAAQ,YAAY,MAAM,WAAW,UAAU,WAAW;AAAA,IAC9D,OACK;AACD,cAAQ,EAAE;AAAA,IACd;AAMA,cAAU,QAAQ,cAAY;AAC1B,eAAS,gBAAgB,OAAO,MAAM;AAAA,QAClC;AAAA,QACA,MAAM,eAAe;AAAA,QACrB,WAAW,QACL,QAAQ,IACJ,oBAAoB,UACpB,oBAAoB,OACxB,oBAAoB;AAAA,MAC9B,CAAC;AAAA,IACL,CAAC;AAAA,EACL;AACA,WAAS,iBAAiB;AACtB,iBAAa,gBAAgB;AAAA,EACjC;AACA,WAAS,OAAO,UAAU;AAEtB,cAAU,KAAK,QAAQ;AACvB,UAAM,WAAW,MAAM;AACnB,YAAM,QAAQ,UAAU,QAAQ,QAAQ;AACxC,UAAI,QAAQ;AACR,kBAAU,OAAO,OAAO,CAAC;AAAA,IACjC;AACA,cAAU,KAAK,QAAQ;AACvB,WAAO;AAAA,EACX;AACA,WAAS,uBAAuB;AAC5B,UAAM,EAAE,SAAAI,SAAQ,IAAI;AACpB,QAAI,CAACA,SAAQ;AACT;AACJ,IAAAA,SAAQ,aAAa,OAAO,CAAC,GAAGA,SAAQ,OAAO,EAAE,QAAQ,sBAAsB,EAAE,CAAC,GAAG,EAAE;AAAA,EAC3F;AACA,WAAS,UAAU;AACf,eAAW,YAAY;AACnB,eAAS;AACb,gBAAY,CAAC;AACb,WAAO,oBAAoB,YAAY,eAAe;AACtD,WAAO,oBAAoB,gBAAgB,oBAAoB;AAAA,EACnE;AAEA,SAAO,iBAAiB,YAAY,eAAe;AAGnD,SAAO,iBAAiB,gBAAgB,sBAAsB;AAAA,IAC1D,SAAS;AAAA,EACb,CAAC;AACD,SAAO;AAAA,IACH;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AACJ;AAIA,SAAS,WAAW,MAAM,SAAS,SAAS,WAAW,OAAO,gBAAgB,OAAO;AACjF,SAAO;AAAA,IACH;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,UAAU,OAAO,QAAQ;AAAA,IACzB,QAAQ,gBAAgB,sBAAsB,IAAI;AAAA,EACtD;AACJ;AACA,SAAS,0BAA0B,MAAM;AACrC,QAAM,EAAE,SAAAA,UAAS,UAAAJ,UAAS,IAAI;AAE9B,QAAM,kBAAkB;AAAA,IACpB,OAAO,sBAAsB,MAAMA,SAAQ;AAAA,EAC/C;AACA,QAAM,eAAe,EAAE,OAAOI,SAAQ,MAAM;AAE5C,MAAI,CAAC,aAAa,OAAO;AACrB,mBAAe,gBAAgB,OAAO;AAAA,MAClC,MAAM;AAAA,MACN,SAAS,gBAAgB;AAAA,MACzB,SAAS;AAAA;AAAA,MAET,UAAUA,SAAQ,SAAS;AAAA,MAC3B,UAAU;AAAA;AAAA;AAAA,MAGV,QAAQ;AAAA,IACZ,GAAG,IAAI;AAAA,EACX;AACA,WAAS,eAAe,IAAI,OAAOC,UAAS;AAUxC,UAAM,YAAY,KAAK,QAAQ,GAAG;AAClC,UAAM,MAAM,YAAY,MACjBL,UAAS,QAAQ,SAAS,cAAc,MAAM,IAC3C,OACA,KAAK,MAAM,SAAS,KAAK,KAC7B,mBAAmB,IAAI,OAAO;AACpC,QAAI;AAGA,MAAAI,SAAQC,WAAU,iBAAiB,WAAW,EAAE,OAAO,IAAI,GAAG;AAC9D,mBAAa,QAAQ;AAAA,IACzB,SACO,KAAK;AACR,UAAK,MAAwC;AACzC,aAAK,iCAAiC,GAAG;AAAA,MAC7C,OACK;AACD,gBAAQ,MAAM,GAAG;AAAA,MACrB;AAEA,MAAAL,UAASK,WAAU,YAAY,QAAQ,EAAE,GAAG;AAAA,IAChD;AAAA,EACJ;AACA,WAAS,QAAQ,IAAI,MAAM;AACvB,UAAM,QAAQ,OAAO,CAAC,GAAGD,SAAQ,OAAO;AAAA,MAAW,aAAa,MAAM;AAAA;AAAA,MAEtE;AAAA,MAAI,aAAa,MAAM;AAAA,MAAS;AAAA,IAAI,GAAG,MAAM,EAAE,UAAU,aAAa,MAAM,SAAS,CAAC;AACtF,mBAAe,IAAI,OAAO,IAAI;AAC9B,oBAAgB,QAAQ;AAAA,EAC5B;AACA,WAAS,KAAK,IAAI,MAAM;AAGpB,UAAM,eAAe;AAAA,MAAO,CAAC;AAAA;AAAA;AAAA;AAAA,MAI7B,aAAa;AAAA,MAAOA,SAAQ;AAAA,MAAO;AAAA,QAC/B,SAAS;AAAA,QACT,QAAQ,sBAAsB;AAAA,MAClC;AAAA,IAAC;AACD,QAA+C,CAACA,SAAQ,OAAO;AAC3D,WAAK;AAAA;AAAA;AAAA;AAAA,kGAEkG;AAAA,IAC3G;AACA,mBAAe,aAAa,SAAS,cAAc,IAAI;AACvD,UAAM,QAAQ,OAAO,CAAC,GAAG,WAAW,gBAAgB,OAAO,IAAI,IAAI,GAAG,EAAE,UAAU,aAAa,WAAW,EAAE,GAAG,IAAI;AACnH,mBAAe,IAAI,OAAO,KAAK;AAC/B,oBAAgB,QAAQ;AAAA,EAC5B;AACA,SAAO;AAAA,IACH,UAAU;AAAA,IACV,OAAO;AAAA,IACP;AAAA,IACA;AAAA,EACJ;AACJ;AAMA,SAAS,iBAAiB,MAAM;AAC5B,SAAO,cAAc,IAAI;AACzB,QAAM,oBAAoB,0BAA0B,IAAI;AACxD,QAAM,mBAAmB,oBAAoB,MAAM,kBAAkB,OAAO,kBAAkB,UAAU,kBAAkB,OAAO;AACjI,WAAS,GAAG,OAAO,mBAAmB,MAAM;AACxC,QAAI,CAAC;AACD,uBAAiB,eAAe;AACpC,YAAQ,GAAG,KAAK;AAAA,EACpB;AACA,QAAM,gBAAgB,OAAO;AAAA;AAAA,IAEzB,UAAU;AAAA,IACV;AAAA,IACA;AAAA,IACA,YAAY,WAAW,KAAK,MAAM,IAAI;AAAA,EAC1C,GAAG,mBAAmB,gBAAgB;AACtC,SAAO,eAAe,eAAe,YAAY;AAAA,IAC7C,YAAY;AAAA,IACZ,KAAK,MAAM,kBAAkB,SAAS;AAAA,EAC1C,CAAC;AACD,SAAO,eAAe,eAAe,SAAS;AAAA,IAC1C,YAAY;AAAA,IACZ,KAAK,MAAM,kBAAkB,MAAM;AAAA,EACvC,CAAC;AACD,SAAO;AACX;AASA,SAAS,oBAAoB,OAAO,IAAI;AACpC,MAAI,YAAY,CAAC;AACjB,MAAI,QAAQ,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC;AACxB,MAAI,WAAW;AACf,SAAO,cAAc,IAAI;AACzB,WAAS,YAAYJ,WAAU,QAAQ,CAAC,GAAG;AACvC;AACA,QAAI,aAAa,MAAM,QAAQ;AAE3B,YAAM,OAAO,QAAQ;AAAA,IACzB;AACA,UAAM,KAAK,CAACA,WAAU,KAAK,CAAC;AAAA,EAChC;AACA,WAAS,iBAAiB,IAAI,MAAM,EAAE,WAAW,MAAM,GAAG;AACtD,UAAM,OAAO;AAAA,MACT;AAAA,MACA;AAAA,MACA,MAAM,eAAe;AAAA,IACzB;AACA,eAAW,YAAY,WAAW;AAC9B,eAAS,IAAI,MAAM,IAAI;AAAA,IAC3B;AAAA,EACJ;AACA,QAAM,gBAAgB;AAAA;AAAA,IAElB,UAAU;AAAA;AAAA,IAEV,OAAO,CAAC;AAAA,IACR;AAAA,IACA,YAAY,WAAW,KAAK,MAAM,IAAI;AAAA,IACtC,QAAQ,IAAI,OAAO;AAEf,YAAM,OAAO,YAAY,CAAC;AAC1B,kBAAY,IAAI,KAAK;AAAA,IACzB;AAAA,IACA,KAAK,IAAI,OAAO;AACZ,kBAAY,IAAI,KAAK;AAAA,IACzB;AAAA,IACA,OAAO,UAAU;AACb,gBAAU,KAAK,QAAQ;AACvB,aAAO,MAAM;AACT,cAAM,QAAQ,UAAU,QAAQ,QAAQ;AACxC,YAAI,QAAQ;AACR,oBAAU,OAAO,OAAO,CAAC;AAAA,MACjC;AAAA,IACJ;AAAA,IACA,UAAU;AACN,kBAAY,CAAC;AACb,cAAQ,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC;AACpB,iBAAW;AAAA,IACf;AAAA,IACA,GAAG,OAAO,gBAAgB,MAAM;AAC5B,YAAM,OAAO,KAAK;AAClB,YAAM;AAAA;AAAA;AAAA;AAAA,QAIN,QAAQ,IAAI,oBAAoB,OAAO,oBAAoB;AAAA;AAC3D,iBAAW,KAAK,IAAI,GAAG,KAAK,IAAI,WAAW,OAAO,MAAM,SAAS,CAAC,CAAC;AACnE,UAAI,eAAe;AACf,yBAAiB,KAAK,UAAU,MAAM;AAAA,UAClC;AAAA,UACA;AAAA,QACJ,CAAC;AAAA,MACL;AAAA,IACJ;AAAA,EACJ;AACA,SAAO,eAAe,eAAe,YAAY;AAAA,IAC7C,YAAY;AAAA,IACZ,KAAK,MAAM,MAAM,QAAQ,EAAE,CAAC;AAAA,EAChC,CAAC;AACD,SAAO,eAAe,eAAe,SAAS;AAAA,IAC1C,YAAY;AAAA,IACZ,KAAK,MAAM,MAAM,QAAQ,EAAE,CAAC;AAAA,EAChC,CAAC;AACD,SAAO;AACX;AA0BA,SAAS,qBAAqB,MAAM;AAIhC,SAAO,SAAS,OAAO,QAAQ,SAAS,WAAW,SAAS,SAAS;AAErE,MAAI,CAAC,KAAK,SAAS,GAAG;AAClB,YAAQ;AACZ,MAA+C,CAAC,KAAK,SAAS,IAAI,KAAK,CAAC,KAAK,SAAS,GAAG,GAAG;AACxF,SAAK;AAAA,GAAsC,IAAI,gBAAgB,KAAK,QAAQ,QAAQ,GAAG,CAAC,IAAI;AAAA,EAChG;AACA,SAAO,iBAAiB,IAAI;AAChC;AAEA,SAAS,gBAAgB,OAAO;AAC5B,SAAO,OAAO,UAAU,YAAa,SAAS,OAAO,UAAU;AACnE;AACA,SAAS,YAAY,MAAM;AACvB,SAAO,OAAO,SAAS,YAAY,OAAO,SAAS;AACvD;AAEA,IAAM,0BAA0B,OAAQ,OAAyC,uBAAuB,EAAE;AAK1G,IAAI;AAAA,CACH,SAAUM,wBAAuB;AAK9B,EAAAA,uBAAsBA,uBAAsB,SAAS,IAAI,CAAC,IAAI;AAK9D,EAAAA,uBAAsBA,uBAAsB,WAAW,IAAI,CAAC,IAAI;AAKhE,EAAAA,uBAAsBA,uBAAsB,YAAY,IAAI,EAAE,IAAI;AACtE,GAAG,0BAA0B,wBAAwB,CAAC,EAAE;AAExD,IAAM,oBAAoB;AAAA,EACtB;AAAA,IAAC;AAAA;AAAA,EAAoC,EAAE,EAAE,UAAAN,WAAU,gBAAgB,GAAG;AAClE,WAAO;AAAA,GAAkB,KAAK,UAAUA,SAAQ,CAAC,GAAG,kBAC9C,uBAAuB,KAAK,UAAU,eAAe,IACrD,EAAE;AAAA,EACZ;AAAA,EACA;AAAA,IAAC;AAAA;AAAA,EAA4C,EAAE,EAAE,MAAM,GAAI,GAAG;AAC1D,WAAO,oBAAoB,KAAK,QAAQ,SAAS,eAAe,EAAE,CAAC;AAAA,EACvE;AAAA,EACA;AAAA,IAAC;AAAA;AAAA,EAAqC,EAAE,EAAE,MAAM,GAAG,GAAG;AAClD,WAAO,4BAA4B,KAAK,QAAQ,SAAS,GAAG,QAAQ;AAAA,EACxE;AAAA,EACA;AAAA,IAAC;AAAA;AAAA,EAAuC,EAAE,EAAE,MAAM,GAAG,GAAG;AACpD,WAAO,8BAA8B,KAAK,QAAQ,SAAS,GAAG,QAAQ;AAAA,EAC1E;AAAA,EACA;AAAA,IAAC;AAAA;AAAA,EAAyC,EAAE,EAAE,MAAM,GAAG,GAAG;AACtD,WAAO,sDAAsD,KAAK,QAAQ;AAAA,EAC9E;AACJ;AAOA,SAAS,kBAAkB,MAAM,QAAQ;AAErC,MAAK,MAAiD;AAClD,WAAO,OAAO,IAAI,MAAM,kBAAkB,IAAI,EAAE,MAAM,CAAC,GAAG;AAAA,MACtD;AAAA,MACA,CAAC,uBAAuB,GAAG;AAAA,IAC/B,GAAG,MAAM;AAAA,EACb,OACK;AACD,WAAO,OAAO,IAAI,MAAM,GAAG;AAAA,MACvB;AAAA,MACA,CAAC,uBAAuB,GAAG;AAAA,IAC/B,GAAG,MAAM;AAAA,EACb;AACJ;AACA,SAAS,oBAAoB,OAAO,MAAM;AACtC,SAAQ,iBAAiB,SACrB,2BAA2B,UAC1B,QAAQ,QAAQ,CAAC,EAAE,MAAM,OAAO;AACzC;AACA,IAAM,kBAAkB,CAAC,UAAU,SAAS,MAAM;AAClD,SAAS,eAAe,IAAI;AACxB,MAAI,OAAO,OAAO;AACd,WAAO;AACX,MAAI,GAAG,QAAQ;AACX,WAAO,GAAG;AACd,QAAMA,YAAW,CAAC;AAClB,aAAW,OAAO,iBAAiB;AAC/B,QAAI,OAAO;AACP,MAAAA,UAAS,GAAG,IAAI,GAAG,GAAG;AAAA,EAC9B;AACA,SAAO,KAAK,UAAUA,WAAU,MAAM,CAAC;AAC3C;AAGA,IAAM,qBAAqB;AAC3B,IAAM,2BAA2B;AAAA,EAC7B,WAAW;AAAA,EACX,QAAQ;AAAA,EACR,OAAO;AAAA,EACP,KAAK;AACT;AAEA,IAAM,iBAAiB;AAQvB,SAAS,eAAe,UAAU,cAAc;AAC5C,QAAM,UAAU,OAAO,CAAC,GAAG,0BAA0B,YAAY;AAEjE,QAAM,QAAQ,CAAC;AAEf,MAAI,UAAU,QAAQ,QAAQ,MAAM;AAEpC,QAAM,OAAO,CAAC;AACd,aAAW,WAAW,UAAU;AAE5B,UAAM,gBAAgB,QAAQ,SAAS,CAAC,IAAI;AAAA,MAAC;AAAA;AAAA,IAAuB;AAEpE,QAAI,QAAQ,UAAU,CAAC,QAAQ;AAC3B,iBAAW;AACf,aAAS,aAAa,GAAG,aAAa,QAAQ,QAAQ,cAAc;AAChE,YAAM,QAAQ,QAAQ,UAAU;AAEhC,UAAI,kBAAkB,MACjB,QAAQ,YAAY,OAA0C;AACnE,UAAI,MAAM,SAAS,GAA0B;AAEzC,YAAI,CAAC;AACD,qBAAW;AACf,mBAAW,MAAM,MAAM,QAAQ,gBAAgB,MAAM;AACrD,2BAAmB;AAAA,MACvB,WACS,MAAM,SAAS,GAAyB;AAC7C,cAAM,EAAE,OAAO,YAAY,UAAU,OAAO,IAAI;AAChD,aAAK,KAAK;AAAA,UACN,MAAM;AAAA,UACN;AAAA,UACA;AAAA,QACJ,CAAC;AACD,cAAMO,MAAK,SAAS,SAAS;AAE7B,YAAIA,QAAO,oBAAoB;AAC3B,6BAAmB;AAEnB,cAAI;AACA,gBAAI,OAAO,IAAIA,GAAE,GAAG;AAAA,UACxB,SACO,KAAK;AACR,kBAAM,IAAI,MAAM,oCAAoC,KAAK,MAAMA,GAAE,QAC7D,IAAI,OAAO;AAAA,UACnB;AAAA,QACJ;AAEA,YAAI,aAAa,aAAa,OAAOA,GAAE,WAAWA,GAAE,SAAS,IAAIA,GAAE;AAEnE,YAAI,CAAC;AACD;AAAA;AAAA,UAGI,YAAY,QAAQ,SAAS,IACvB,OAAO,UAAU,MACjB,MAAM;AACpB,YAAI;AACA,wBAAc;AAClB,mBAAW;AACX,2BAAmB;AACnB,YAAI;AACA,6BAAmB;AACvB,YAAI;AACA,6BAAmB;AACvB,YAAIA,QAAO;AACP,6BAAmB;AAAA,MAC3B;AACA,oBAAc,KAAK,eAAe;AAAA,IACtC;AAGA,UAAM,KAAK,aAAa;AAAA,EAC5B;AAEA,MAAI,QAAQ,UAAU,QAAQ,KAAK;AAC/B,UAAM,IAAI,MAAM,SAAS;AACzB,UAAM,CAAC,EAAE,MAAM,CAAC,EAAE,SAAS,CAAC,KAAK;AAAA,EACrC;AAEA,MAAI,CAAC,QAAQ;AACT,eAAW;AACf,MAAI,QAAQ;AACR,eAAW;AAAA,WAEN,QAAQ,UAAU,CAAC,QAAQ,SAAS,GAAG;AAC5C,eAAW;AACf,QAAM,KAAK,IAAI,OAAO,SAAS,QAAQ,YAAY,KAAK,GAAG;AAC3D,WAAS,MAAM,MAAM;AACjB,UAAM,QAAQ,KAAK,MAAM,EAAE;AAC3B,UAAM,SAAS,CAAC;AAChB,QAAI,CAAC;AACD,aAAO;AACX,aAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACnC,YAAM,QAAQ,MAAM,CAAC,KAAK;AAC1B,YAAM,MAAM,KAAK,IAAI,CAAC;AACtB,aAAO,IAAI,IAAI,IAAI,SAAS,IAAI,aAAa,MAAM,MAAM,GAAG,IAAI;AAAA,IACpE;AACA,WAAO;AAAA,EACX;AACA,WAAS,UAAU,QAAQ;AACvB,QAAI,OAAO;AAEX,QAAI,uBAAuB;AAC3B,eAAW,WAAW,UAAU;AAC5B,UAAI,CAAC,wBAAwB,CAAC,KAAK,SAAS,GAAG;AAC3C,gBAAQ;AACZ,6BAAuB;AACvB,iBAAW,SAAS,SAAS;AACzB,YAAI,MAAM,SAAS,GAA0B;AACzC,kBAAQ,MAAM;AAAA,QAClB,WACS,MAAM,SAAS,GAAyB;AAC7C,gBAAM,EAAE,OAAO,YAAY,SAAS,IAAI;AACxC,gBAAM,QAAQ,SAAS,SAAS,OAAO,KAAK,IAAI;AAChD,cAAI,QAAQ,KAAK,KAAK,CAAC,YAAY;AAC/B,kBAAM,IAAI,MAAM,mBAAmB,KAAK,2DAA2D;AAAA,UACvG;AACA,gBAAM,OAAO,QAAQ,KAAK,IACpB,MAAM,KAAK,GAAG,IACd;AACN,cAAI,CAAC,MAAM;AACP,gBAAI,UAAU;AAEV,kBAAI,QAAQ,SAAS,GAAG;AAEpB,oBAAI,KAAK,SAAS,GAAG;AACjB,yBAAO,KAAK,MAAM,GAAG,EAAE;AAAA;AAGvB,yCAAuB;AAAA,cAC/B;AAAA,YACJ;AAEI,oBAAM,IAAI,MAAM,2BAA2B,KAAK,GAAG;AAAA,UAC3D;AACA,kBAAQ;AAAA,QACZ;AAAA,MACJ;AAAA,IACJ;AAEA,WAAO,QAAQ;AAAA,EACnB;AACA,SAAO;AAAA,IACH;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AACJ;AAUA,SAAS,kBAAkB,GAAG,GAAG;AAC7B,MAAI,IAAI;AACR,SAAO,IAAI,EAAE,UAAU,IAAI,EAAE,QAAQ;AACjC,UAAM,OAAO,EAAE,CAAC,IAAI,EAAE,CAAC;AAEvB,QAAI;AACA,aAAO;AACX;AAAA,EACJ;AAGA,MAAI,EAAE,SAAS,EAAE,QAAQ;AACrB,WAAO,EAAE,WAAW,KAAK,EAAE,CAAC,MAAM,KAA4B,KACxD,KACA;AAAA,EACV,WACS,EAAE,SAAS,EAAE,QAAQ;AAC1B,WAAO,EAAE,WAAW,KAAK,EAAE,CAAC,MAAM,KAA4B,KACxD,IACA;AAAA,EACV;AACA,SAAO;AACX;AAQA,SAAS,uBAAuB,GAAG,GAAG;AAClC,MAAI,IAAI;AACR,QAAM,SAAS,EAAE;AACjB,QAAM,SAAS,EAAE;AACjB,SAAO,IAAI,OAAO,UAAU,IAAI,OAAO,QAAQ;AAC3C,UAAM,OAAO,kBAAkB,OAAO,CAAC,GAAG,OAAO,CAAC,CAAC;AAEnD,QAAI;AACA,aAAO;AACX;AAAA,EACJ;AACA,MAAI,KAAK,IAAI,OAAO,SAAS,OAAO,MAAM,MAAM,GAAG;AAC/C,QAAI,oBAAoB,MAAM;AAC1B,aAAO;AACX,QAAI,oBAAoB,MAAM;AAC1B,aAAO;AAAA,EACf;AAEA,SAAO,OAAO,SAAS,OAAO;AAOlC;AAOA,SAAS,oBAAoB,OAAO;AAChC,QAAM,OAAO,MAAM,MAAM,SAAS,CAAC;AACnC,SAAO,MAAM,SAAS,KAAK,KAAK,KAAK,SAAS,CAAC,IAAI;AACvD;AAEA,IAAM,aAAa;AAAA,EACf,MAAM;AAAA,EACN,OAAO;AACX;AACA,IAAM,iBAAiB;AAIvB,SAAS,aAAa,MAAM;AACxB,MAAI,CAAC;AACD,WAAO,CAAC,CAAC,CAAC;AACd,MAAI,SAAS;AACT,WAAO,CAAC,CAAC,UAAU,CAAC;AACxB,MAAI,CAAC,KAAK,WAAW,GAAG,GAAG;AACvB,UAAM,IAAI,MAAO,OACX,yCAAyC,IAAI,iBAAiB,IAAI,OAClE,iBAAiB,IAAI,GAAG;AAAA,EAClC;AAEA,WAAS,MAAM,SAAS;AACpB,UAAM,IAAI,MAAM,QAAQ,KAAK,MAAM,MAAM,MAAM,OAAO,EAAE;AAAA,EAC5D;AACA,MAAI,QAAQ;AACZ,MAAI,gBAAgB;AACpB,QAAM,SAAS,CAAC;AAGhB,MAAI;AACJ,WAAS,kBAAkB;AACvB,QAAI;AACA,aAAO,KAAK,OAAO;AACvB,cAAU,CAAC;AAAA,EACf;AAEA,MAAI,IAAI;AAER,MAAI;AAEJ,MAAI,SAAS;AAEb,MAAI,WAAW;AACf,WAAS,gBAAgB;AACrB,QAAI,CAAC;AACD;AACJ,QAAI,UAAU,GAA+B;AACzC,cAAQ,KAAK;AAAA,QACT,MAAM;AAAA,QACN,OAAO;AAAA,MACX,CAAC;AAAA,IACL,WACS,UAAU,KACf,UAAU,KACV,UAAU,GAAuC;AACjD,UAAI,QAAQ,SAAS,MAAM,SAAS,OAAO,SAAS;AAChD,cAAM,uBAAuB,MAAM,8CAA8C;AACrF,cAAQ,KAAK;AAAA,QACT,MAAM;AAAA,QACN,OAAO;AAAA,QACP,QAAQ;AAAA,QACR,YAAY,SAAS,OAAO,SAAS;AAAA,QACrC,UAAU,SAAS,OAAO,SAAS;AAAA,MACvC,CAAC;AAAA,IACL,OACK;AACD,YAAM,iCAAiC;AAAA,IAC3C;AACA,aAAS;AAAA,EACb;AACA,WAAS,kBAAkB;AACvB,cAAU;AAAA,EACd;AACA,SAAO,IAAI,KAAK,QAAQ;AACpB,WAAO,KAAK,GAAG;AACf,QAAI,SAAS,QAAQ,UAAU,GAAoC;AAC/D,sBAAgB;AAChB,cAAQ;AACR;AAAA,IACJ;AACA,YAAQ,OAAO;AAAA,MACX,KAAK;AACD,YAAI,SAAS,KAAK;AACd,cAAI,QAAQ;AACR,0BAAc;AAAA,UAClB;AACA,0BAAgB;AAAA,QACpB,WACS,SAAS,KAAK;AACnB,wBAAc;AACd,kBAAQ;AAAA,QACZ,OACK;AACD,0BAAgB;AAAA,QACpB;AACA;AAAA,MACJ,KAAK;AACD,wBAAgB;AAChB,gBAAQ;AACR;AAAA,MACJ,KAAK;AACD,YAAI,SAAS,KAAK;AACd,kBAAQ;AAAA,QACZ,WACS,eAAe,KAAK,IAAI,GAAG;AAChC,0BAAgB;AAAA,QACpB,OACK;AACD,wBAAc;AACd,kBAAQ;AAER,cAAI,SAAS,OAAO,SAAS,OAAO,SAAS;AACzC;AAAA,QACR;AACA;AAAA,MACJ,KAAK;AAMD,YAAI,SAAS,KAAK;AAEd,cAAI,SAAS,SAAS,SAAS,CAAC,KAAK;AACjC,uBAAW,SAAS,MAAM,GAAG,EAAE,IAAI;AAAA;AAEnC,oBAAQ;AAAA,QAChB,OACK;AACD,sBAAY;AAAA,QAChB;AACA;AAAA,MACJ,KAAK;AAED,sBAAc;AACd,gBAAQ;AAER,YAAI,SAAS,OAAO,SAAS,OAAO,SAAS;AACzC;AACJ,mBAAW;AACX;AAAA,MACJ;AACI,cAAM,eAAe;AACrB;AAAA,IACR;AAAA,EACJ;AACA,MAAI,UAAU;AACV,UAAM,uCAAuC,MAAM,GAAG;AAC1D,gBAAc;AACd,kBAAgB;AAEhB,SAAO;AACX;AAEA,SAAS,yBAAyB,QAAQ,QAAQ,SAAS;AACvD,QAAM,SAAS,eAAe,aAAa,OAAO,IAAI,GAAG,OAAO;AAEhE,MAAK,MAAwC;AACzC,UAAM,eAAe,oBAAI,IAAI;AAC7B,eAAW,OAAO,OAAO,MAAM;AAC3B,UAAI,aAAa,IAAI,IAAI,IAAI;AACzB,aAAK,sCAAsC,IAAI,IAAI,eAAe,OAAO,IAAI,4DAA4D;AAC7I,mBAAa,IAAI,IAAI,IAAI;AAAA,IAC7B;AAAA,EACJ;AACA,QAAM,UAAU,OAAO,QAAQ;AAAA,IAC3B;AAAA,IACA;AAAA;AAAA,IAEA,UAAU,CAAC;AAAA,IACX,OAAO,CAAC;AAAA,EACZ,CAAC;AACD,MAAI,QAAQ;AAIR,QAAI,CAAC,QAAQ,OAAO,YAAY,CAAC,OAAO,OAAO;AAC3C,aAAO,SAAS,KAAK,OAAO;AAAA,EACpC;AACA,SAAO;AACX;AASA,SAAS,oBAAoB,QAAQ,eAAe;AAEhD,QAAM,WAAW,CAAC;AAClB,QAAM,aAAa,oBAAI,IAAI;AAC3B,kBAAgB,aAAa,EAAE,QAAQ,OAAO,KAAK,MAAM,WAAW,MAAM,GAAG,aAAa;AAC1F,WAAS,iBAAiB,MAAM;AAC5B,WAAO,WAAW,IAAI,IAAI;AAAA,EAC9B;AACA,WAAS,SAAS,QAAQ,QAAQ,gBAAgB;AAE9C,UAAM,YAAY,CAAC;AACnB,UAAM,uBAAuB,qBAAqB,MAAM;AACxD,QAAK,MAAwC;AACzC,yCAAmC,sBAAsB,MAAM;AAAA,IACnE;AAEA,yBAAqB,UAAU,kBAAkB,eAAe;AAChE,UAAM,UAAU,aAAa,eAAe,MAAM;AAElD,UAAM,oBAAoB,CAAC,oBAAoB;AAC/C,QAAI,WAAW,QAAQ;AACnB,YAAM,UAAU,OAAO,OAAO,UAAU,WAAW,CAAC,OAAO,KAAK,IAAI,OAAO;AAC3E,iBAAW,SAAS,SAAS;AACzB,0BAAkB;AAAA;AAAA;AAAA,UAGlB,qBAAqB,OAAO,CAAC,GAAG,sBAAsB;AAAA;AAAA;AAAA,YAGlD,YAAY,iBACN,eAAe,OAAO,aACtB,qBAAqB;AAAA,YAC3B,MAAM;AAAA;AAAA,YAEN,SAAS,iBACH,eAAe,SACf;AAAA;AAAA;AAAA,UAGV,CAAC,CAAC;AAAA,QAAC;AAAA,MACP;AAAA,IACJ;AACA,QAAI;AACJ,QAAI;AACJ,eAAW,oBAAoB,mBAAmB;AAC9C,YAAM,EAAE,KAAK,IAAI;AAIjB,UAAI,UAAU,KAAK,CAAC,MAAM,KAAK;AAC3B,cAAM,aAAa,OAAO,OAAO;AACjC,cAAM,kBAAkB,WAAW,WAAW,SAAS,CAAC,MAAM,MAAM,KAAK;AACzE,yBAAiB,OACb,OAAO,OAAO,QAAQ,QAAQ,kBAAkB;AAAA,MACxD;AACA,UAA+C,iBAAiB,SAAS,KAAK;AAC1E,cAAM,IAAI,MAAM,yKAC6E;AAAA,MACjG;AAEA,gBAAU,yBAAyB,kBAAkB,QAAQ,OAAO;AACpE,UAA+C,UAAU,KAAK,CAAC,MAAM;AACjE,yCAAiC,SAAS,MAAM;AAGpD,UAAI,gBAAgB;AAChB,uBAAe,MAAM,KAAK,OAAO;AACjC,YAAK,MAAwC;AACzC,0BAAgB,gBAAgB,OAAO;AAAA,QAC3C;AAAA,MACJ,OACK;AAED,0BAAkB,mBAAmB;AACrC,YAAI,oBAAoB;AACpB,0BAAgB,MAAM,KAAK,OAAO;AAGtC,YAAI,aAAa,OAAO,QAAQ,CAAC,cAAc,OAAO,GAAG;AACrD,cAAK,MAAwC;AACzC,oCAAwB,QAAQ,MAAM;AAAA,UAC1C;AACA,sBAAY,OAAO,IAAI;AAAA,QAC3B;AAAA,MACJ;AAGA,UAAI,YAAY,OAAO,GAAG;AACtB,sBAAc,OAAO;AAAA,MACzB;AACA,UAAI,qBAAqB,UAAU;AAC/B,cAAM,WAAW,qBAAqB;AACtC,iBAAS,IAAI,GAAG,IAAI,SAAS,QAAQ,KAAK;AACtC,mBAAS,SAAS,CAAC,GAAG,SAAS,kBAAkB,eAAe,SAAS,CAAC,CAAC;AAAA,QAC/E;AAAA,MACJ;AAGA,uBAAiB,kBAAkB;AAAA,IAKvC;AACA,WAAO,kBACD,MAAM;AAEJ,kBAAY,eAAe;AAAA,IAC/B,IACE;AAAA,EACV;AACA,WAAS,YAAY,YAAY;AAC7B,QAAI,YAAY,UAAU,GAAG;AACzB,YAAM,UAAU,WAAW,IAAI,UAAU;AACzC,UAAI,SAAS;AACT,mBAAW,OAAO,UAAU;AAC5B,iBAAS,OAAO,SAAS,QAAQ,OAAO,GAAG,CAAC;AAC5C,gBAAQ,SAAS,QAAQ,WAAW;AACpC,gBAAQ,MAAM,QAAQ,WAAW;AAAA,MACrC;AAAA,IACJ,OACK;AACD,YAAM,QAAQ,SAAS,QAAQ,UAAU;AACzC,UAAI,QAAQ,IAAI;AACZ,iBAAS,OAAO,OAAO,CAAC;AACxB,YAAI,WAAW,OAAO;AAClB,qBAAW,OAAO,WAAW,OAAO,IAAI;AAC5C,mBAAW,SAAS,QAAQ,WAAW;AACvC,mBAAW,MAAM,QAAQ,WAAW;AAAA,MACxC;AAAA,IACJ;AAAA,EACJ;AACA,WAAS,YAAY;AACjB,WAAO;AAAA,EACX;AACA,WAAS,cAAc,SAAS;AAC5B,UAAM,QAAQ,mBAAmB,SAAS,QAAQ;AAClD,aAAS,OAAO,OAAO,GAAG,OAAO;AAEjC,QAAI,QAAQ,OAAO,QAAQ,CAAC,cAAc,OAAO;AAC7C,iBAAW,IAAI,QAAQ,OAAO,MAAM,OAAO;AAAA,EACnD;AACA,WAAS,QAAQP,WAAU,iBAAiB;AACxC,QAAI;AACJ,QAAI,SAAS,CAAC;AACd,QAAI;AACJ,QAAI;AACJ,QAAI,UAAUA,aAAYA,UAAS,MAAM;AACrC,gBAAU,WAAW,IAAIA,UAAS,IAAI;AACtC,UAAI,CAAC;AACD,cAAM,kBAAkB,GAAsC;AAAA,UAC1D,UAAAA;AAAA,QACJ,CAAC;AAEL,UAAK,MAAwC;AACzC,cAAM,gBAAgB,OAAO,KAAKA,UAAS,UAAU,CAAC,CAAC,EAAE,OAAO,eAAa,CAAC,QAAQ,KAAK,KAAK,OAAK,EAAE,SAAS,SAAS,CAAC;AAC1H,YAAI,cAAc,QAAQ;AACtB,eAAK,+BAA+B,cAAc,KAAK,MAAM,CAAC,gIAAgI;AAAA,QAClM;AAAA,MACJ;AACA,aAAO,QAAQ,OAAO;AACtB,eAAS;AAAA;AAAA,QAET;AAAA,UAAmB,gBAAgB;AAAA;AAAA;AAAA,UAGnC,QAAQ,KACH,OAAO,OAAK,CAAC,EAAE,QAAQ,EACvB,OAAO,QAAQ,SAAS,QAAQ,OAAO,KAAK,OAAO,OAAK,EAAE,QAAQ,IAAI,CAAC,CAAC,EACxE,IAAI,OAAK,EAAE,IAAI;AAAA,QAAC;AAAA;AAAA;AAAA,QAGrBA,UAAS,UACL,mBAAmBA,UAAS,QAAQ,QAAQ,KAAK,IAAI,OAAK,EAAE,IAAI,CAAC;AAAA,MAAC;AAEtE,aAAO,QAAQ,UAAU,MAAM;AAAA,IACnC,WACSA,UAAS,QAAQ,MAAM;AAG5B,aAAOA,UAAS;AAChB,UAA+C,CAAC,KAAK,WAAW,GAAG,GAAG;AAClE,aAAK,2DAA2D,IAAI,oDAAoD,IAAI,wHAAwH;AAAA,MACxP;AACA,gBAAU,SAAS,KAAK,OAAK,EAAE,GAAG,KAAK,IAAI,CAAC;AAE5C,UAAI,SAAS;AAET,iBAAS,QAAQ,MAAM,IAAI;AAC3B,eAAO,QAAQ,OAAO;AAAA,MAC1B;AAAA,IAEJ,OACK;AAED,gBAAU,gBAAgB,OACpB,WAAW,IAAI,gBAAgB,IAAI,IACnC,SAAS,KAAK,OAAK,EAAE,GAAG,KAAK,gBAAgB,IAAI,CAAC;AACxD,UAAI,CAAC;AACD,cAAM,kBAAkB,GAAsC;AAAA,UAC1D,UAAAA;AAAA,UACA;AAAA,QACJ,CAAC;AACL,aAAO,QAAQ,OAAO;AAGtB,eAAS,OAAO,CAAC,GAAG,gBAAgB,QAAQA,UAAS,MAAM;AAC3D,aAAO,QAAQ,UAAU,MAAM;AAAA,IACnC;AACA,UAAM,UAAU,CAAC;AACjB,QAAI,gBAAgB;AACpB,WAAO,eAAe;AAElB,cAAQ,QAAQ,cAAc,MAAM;AACpC,sBAAgB,cAAc;AAAA,IAClC;AACA,WAAO;AAAA,MACH;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA,MAAM,gBAAgB,OAAO;AAAA,IACjC;AAAA,EACJ;AAEA,SAAO,QAAQ,WAAS,SAAS,KAAK,CAAC;AACvC,WAAS,cAAc;AACnB,aAAS,SAAS;AAClB,eAAW,MAAM;AAAA,EACrB;AACA,SAAO;AAAA,IACH;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AACJ;AACA,SAAS,mBAAmB,QAAQ,MAAM;AACtC,QAAM,YAAY,CAAC;AACnB,aAAW,OAAO,MAAM;AACpB,QAAI,OAAO;AACP,gBAAU,GAAG,IAAI,OAAO,GAAG;AAAA,EACnC;AACA,SAAO;AACX;AAOA,SAAS,qBAAqB,QAAQ;AAClC,QAAM,aAAa;AAAA,IACf,MAAM,OAAO;AAAA,IACb,UAAU,OAAO;AAAA,IACjB,MAAM,OAAO;AAAA,IACb,MAAM,OAAO,QAAQ,CAAC;AAAA,IACtB,SAAS,OAAO;AAAA,IAChB,aAAa,OAAO;AAAA,IACpB,OAAO,qBAAqB,MAAM;AAAA,IAClC,UAAU,OAAO,YAAY,CAAC;AAAA,IAC9B,WAAW,CAAC;AAAA,IACZ,aAAa,oBAAI,IAAI;AAAA,IACrB,cAAc,oBAAI,IAAI;AAAA,IACtB,gBAAgB,CAAC;AAAA;AAAA;AAAA,IAGjB,YAAY,gBAAgB,SACtB,OAAO,cAAc,OACrB,OAAO,aAAa,EAAE,SAAS,OAAO,UAAU;AAAA,EAC1D;AAIA,SAAO,eAAe,YAAY,QAAQ;AAAA,IACtC,OAAO,CAAC;AAAA,EACZ,CAAC;AACD,SAAO;AACX;AAMA,SAAS,qBAAqB,QAAQ;AAClC,QAAM,cAAc,CAAC;AAErB,QAAM,QAAQ,OAAO,SAAS;AAC9B,MAAI,eAAe,QAAQ;AACvB,gBAAY,UAAU;AAAA,EAC1B,OACK;AAGD,eAAW,QAAQ,OAAO;AACtB,kBAAY,IAAI,IAAI,OAAO,UAAU,WAAW,MAAM,IAAI,IAAI;AAAA,EACtE;AACA,SAAO;AACX;AAKA,SAAS,cAAc,QAAQ;AAC3B,SAAO,QAAQ;AACX,QAAI,OAAO,OAAO;AACd,aAAO;AACX,aAAS,OAAO;AAAA,EACpB;AACA,SAAO;AACX;AAMA,SAAS,gBAAgB,SAAS;AAC9B,SAAO,QAAQ,OAAO,CAAC,MAAM,WAAW,OAAO,MAAM,OAAO,IAAI,GAAG,CAAC,CAAC;AACzE;AACA,SAAS,aAAa,UAAU,gBAAgB;AAC5C,QAAM,UAAU,CAAC;AACjB,aAAW,OAAO,UAAU;AACxB,YAAQ,GAAG,IAAI,OAAO,iBAAiB,eAAe,GAAG,IAAI,SAAS,GAAG;AAAA,EAC7E;AACA,SAAO;AACX;AACA,SAAS,YAAY,GAAG,GAAG;AACvB,SAAQ,EAAE,SAAS,EAAE,QACjB,EAAE,aAAa,EAAE,YACjB,EAAE,eAAe,EAAE;AAC3B;AAOA,SAAS,gBAAgB,GAAG,GAAG;AAC3B,aAAW,OAAO,EAAE,MAAM;AACtB,QAAI,CAAC,IAAI,YAAY,CAAC,EAAE,KAAK,KAAK,YAAY,KAAK,MAAM,GAAG,CAAC;AACzD,aAAO,KAAK,UAAU,EAAE,OAAO,IAAI,+BAA+B,EAAE,OAAO,IAAI,2CAA2C,IAAI,IAAI,GAAG;AAAA,EAC7I;AACA,aAAW,OAAO,EAAE,MAAM;AACtB,QAAI,CAAC,IAAI,YAAY,CAAC,EAAE,KAAK,KAAK,YAAY,KAAK,MAAM,GAAG,CAAC;AACzD,aAAO,KAAK,UAAU,EAAE,OAAO,IAAI,+BAA+B,EAAE,OAAO,IAAI,2CAA2C,IAAI,IAAI,GAAG;AAAA,EAC7I;AACJ;AAOA,SAAS,mCAAmC,sBAAsB,QAAQ;AACtE,MAAI,UACA,OAAO,OAAO,QACd,CAAC,qBAAqB,QACtB,CAAC,qBAAqB,MAAM;AAC5B,SAAK,oBAAoB,OAAO,OAAO,OAAO,IAAI,CAAC,4OAA4O;AAAA,EACnS;AACJ;AACA,SAAS,wBAAwB,QAAQ,QAAQ;AAC7C,WAAS,WAAW,QAAQ,UAAU,WAAW,SAAS,QAAQ;AAC9D,QAAI,SAAS,OAAO,SAAS,OAAO,MAAM;AACtC,YAAM,IAAI,MAAM,kBAAkB,OAAO,OAAO,IAAI,CAAC,yBAAyB,WAAW,WAAW,UAAU,YAAY,wHAAwH;AAAA,IACtP;AAAA,EACJ;AACJ;AACA,SAAS,iCAAiC,QAAQ,QAAQ;AACtD,aAAW,OAAO,OAAO,MAAM;AAC3B,QAAI,CAAC,OAAO,KAAK,KAAK,YAAY,KAAK,MAAM,GAAG,CAAC;AAC7C,aAAO,KAAK,kBAAkB,OAAO,OAAO,IAAI,2CAA2C,IAAI,IAAI,oBAAoB,OAAO,OAAO,IAAI,IAAI;AAAA,EACrJ;AACJ;AAUA,SAAS,mBAAmB,SAAS,UAAU;AAE3C,MAAI,QAAQ;AACZ,MAAI,QAAQ,SAAS;AACrB,SAAO,UAAU,OAAO;AACpB,UAAM,MAAO,QAAQ,SAAU;AAC/B,UAAM,YAAY,uBAAuB,SAAS,SAAS,GAAG,CAAC;AAC/D,QAAI,YAAY,GAAG;AACf,cAAQ;AAAA,IACZ,OACK;AACD,cAAQ,MAAM;AAAA,IAClB;AAAA,EACJ;AAEA,QAAM,oBAAoB,qBAAqB,OAAO;AACtD,MAAI,mBAAmB;AACnB,YAAQ,SAAS,YAAY,mBAAmB,QAAQ,CAAC;AACzD,QAA+C,QAAQ,GAAG;AAEtD,WAAK,2BAA2B,kBAAkB,OAAO,IAAI,iBAAiB,QAAQ,OAAO,IAAI,GAAG;AAAA,IACxG;AAAA,EACJ;AACA,SAAO;AACX;AACA,SAAS,qBAAqB,SAAS;AACnC,MAAI,WAAW;AACf,SAAQ,WAAW,SAAS,QAAS;AACjC,QAAI,YAAY,QAAQ,KACpB,uBAAuB,SAAS,QAAQ,MAAM,GAAG;AACjD,aAAO;AAAA,IACX;AAAA,EACJ;AACA;AACJ;AAQA,SAAS,YAAY,EAAE,OAAO,GAAG;AAC7B,SAAO,CAAC,EAAE,OAAO,QACZ,OAAO,cAAc,OAAO,KAAK,OAAO,UAAU,EAAE,UACrD,OAAO;AACf;AAWA,SAAS,WAAW,QAAQ;AACxB,QAAM,QAAQ,CAAC;AAGf,MAAI,WAAW,MAAM,WAAW;AAC5B,WAAO;AACX,QAAM,eAAe,OAAO,CAAC,MAAM;AACnC,QAAM,gBAAgB,eAAe,OAAO,MAAM,CAAC,IAAI,QAAQ,MAAM,GAAG;AACxE,WAAS,IAAI,GAAG,IAAI,aAAa,QAAQ,EAAE,GAAG;AAE1C,UAAM,cAAc,aAAa,CAAC,EAAE,QAAQ,SAAS,GAAG;AAExD,UAAM,QAAQ,YAAY,QAAQ,GAAG;AACrC,UAAM,MAAM,OAAO,QAAQ,IAAI,cAAc,YAAY,MAAM,GAAG,KAAK,CAAC;AACxE,UAAM,QAAQ,QAAQ,IAAI,OAAO,OAAO,YAAY,MAAM,QAAQ,CAAC,CAAC;AACpE,QAAI,OAAO,OAAO;AAEd,UAAI,eAAe,MAAM,GAAG;AAC5B,UAAI,CAAC,QAAQ,YAAY,GAAG;AACxB,uBAAe,MAAM,GAAG,IAAI,CAAC,YAAY;AAAA,MAC7C;AACA,mBAAa,KAAK,KAAK;AAAA,IAC3B,OACK;AACD,YAAM,GAAG,IAAI;AAAA,IACjB;AAAA,EACJ;AACA,SAAO;AACX;AAUA,SAAS,eAAe,OAAO;AAC3B,MAAI,SAAS;AACb,WAAS,OAAO,OAAO;AACnB,UAAM,QAAQ,MAAM,GAAG;AACvB,UAAM,eAAe,GAAG;AACxB,QAAI,SAAS,MAAM;AAEf,UAAI,UAAU,QAAW;AACrB,mBAAW,OAAO,SAAS,MAAM,MAAM;AAAA,MAC3C;AACA;AAAA,IACJ;AAEA,UAAM,SAAS,QAAQ,KAAK,IACtB,MAAM,IAAI,OAAK,KAAK,iBAAiB,CAAC,CAAC,IACvC,CAAC,SAAS,iBAAiB,KAAK,CAAC;AACvC,WAAO,QAAQ,CAAAQ,WAAS;AAGpB,UAAIA,WAAU,QAAW;AAErB,mBAAW,OAAO,SAAS,MAAM,MAAM;AACvC,YAAIA,UAAS;AACT,oBAAU,MAAMA;AAAA,MACxB;AAAA,IACJ,CAAC;AAAA,EACL;AACA,SAAO;AACX;AASA,SAAS,eAAe,OAAO;AAC3B,QAAM,kBAAkB,CAAC;AACzB,aAAW,OAAO,OAAO;AACrB,UAAM,QAAQ,MAAM,GAAG;AACvB,QAAI,UAAU,QAAW;AACrB,sBAAgB,GAAG,IAAI,QAAQ,KAAK,IAC9B,MAAM,IAAI,OAAM,KAAK,OAAO,OAAO,KAAK,CAAE,IAC1C,SAAS,OACL,QACA,KAAK;AAAA,IACnB;AAAA,EACJ;AACA,SAAO;AACX;AASA,IAAM,kBAAkB,OAAQ,OAAyC,iCAAiC,EAAE;AAO5G,IAAM,eAAe,OAAQ,OAAyC,sBAAsB,EAAE;AAO9F,IAAM,YAAY,OAAQ,OAAyC,WAAW,EAAE;AAOhF,IAAM,mBAAmB,OAAQ,OAAyC,mBAAmB,EAAE;AAO/F,IAAM,wBAAwB,OAAQ,OAAyC,yBAAyB,EAAE;AAK1G,SAAS,eAAe;AACpB,MAAI,WAAW,CAAC;AAChB,WAAS,IAAI,SAAS;AAClB,aAAS,KAAK,OAAO;AACrB,WAAO,MAAM;AACT,YAAM,IAAI,SAAS,QAAQ,OAAO;AAClC,UAAI,IAAI;AACJ,iBAAS,OAAO,GAAG,CAAC;AAAA,IAC5B;AAAA,EACJ;AACA,WAAS,QAAQ;AACb,eAAW,CAAC;AAAA,EAChB;AACA,SAAO;AAAA,IACH;AAAA,IACA,MAAM,MAAM,SAAS,MAAM;AAAA,IAC3B;AAAA,EACJ;AACJ;AAEA,SAAS,cAAc,QAAQ,MAAM,OAAO;AACxC,QAAM,iBAAiB,MAAM;AACzB,WAAO,IAAI,EAAE,OAAO,KAAK;AAAA,EAC7B;AACA,cAAY,cAAc;AAC1B,gBAAc,cAAc;AAC5B,cAAY,MAAM;AACd,WAAO,IAAI,EAAE,IAAI,KAAK;AAAA,EAC1B,CAAC;AACD,SAAO,IAAI,EAAE,IAAI,KAAK;AAC1B;AAQA,SAAS,mBAAmB,YAAY;AACpC,MAA+C,CAAC,mBAAmB,GAAG;AAClE,SAAK,wGAAwG;AAC7G;AAAA,EACJ;AACA,QAAM,eAAe;AAAA,IAAO;AAAA;AAAA,IAE5B,CAAC;AAAA,EAAC,EAAE;AACJ,MAAI,CAAC,cAAc;AACf,IACI,KAAK,0LAA0L;AACnM;AAAA,EACJ;AACA,gBAAc,cAAc,eAAe,UAAU;AACzD;AAQA,SAAS,oBAAoB,aAAa;AACtC,MAA+C,CAAC,mBAAmB,GAAG;AAClE,SAAK,yGAAyG;AAC9G;AAAA,EACJ;AACA,QAAM,eAAe;AAAA,IAAO;AAAA;AAAA,IAE5B,CAAC;AAAA,EAAC,EAAE;AACJ,MAAI,CAAC,cAAc;AACf,IACI,KAAK,2LAA2L;AACpM;AAAA,EACJ;AACA,gBAAc,cAAc,gBAAgB,WAAW;AAC3D;AACA,SAAS,iBAAiB,OAAO,IAAI,MAAM,QAAQ,MAAM,iBAAiB,QAAM,GAAG,GAAG;AAElF,QAAM,qBAAqB;AAAA,GAEtB,OAAO,eAAe,IAAI,IAAI,OAAO,eAAe,IAAI,KAAK,CAAC;AACnE,SAAO,MAAM,IAAI,QAAQ,CAAC,SAAS,WAAW;AAC1C,UAAM,OAAO,CAAC,UAAU;AACpB,UAAI,UAAU,OAAO;AACjB,eAAO,kBAAkB,GAAuC;AAAA,UAC5D;AAAA,UACA;AAAA,QACJ,CAAC,CAAC;AAAA,MACN,WACS,iBAAiB,OAAO;AAC7B,eAAO,KAAK;AAAA,MAChB,WACS,gBAAgB,KAAK,GAAG;AAC7B,eAAO,kBAAkB,GAA8C;AAAA,UACnE,MAAM;AAAA,UACN,IAAI;AAAA,QACR,CAAC,CAAC;AAAA,MACN,OACK;AACD,YAAI;AAAA,QAEA,OAAO,eAAe,IAAI,MAAM,sBAChC,OAAO,UAAU,YAAY;AAC7B,6BAAmB,KAAK,KAAK;AAAA,QACjC;AACA,gBAAQ;AAAA,MACZ;AAAA,IACJ;AAEA,UAAM,cAAc,eAAe,MAAM,MAAM,KAAK,UAAU,OAAO,UAAU,IAAI,GAAG,IAAI,MAAO,OAAyC,oBAAoB,MAAM,IAAI,IAAI,IAAI,IAAI,CAAC;AACrL,QAAI,YAAY,QAAQ,QAAQ,WAAW;AAC3C,QAAI,MAAM,SAAS;AACf,kBAAY,UAAU,KAAK,IAAI;AACnC,QAA+C,MAAM,SAAS,GAAG;AAC7D,YAAM,UAAU,kDAAkD,MAAM,OAAO,MAAM,MAAM,OAAO,MAAM,EAAE;AAAA,EAAM,MAAM,SAAS,CAAC;AAAA;AAChI,UAAI,OAAO,gBAAgB,YAAY,UAAU,aAAa;AAC1D,oBAAY,UAAU,KAAK,mBAAiB;AAExC,cAAI,CAAC,KAAK,SAAS;AACf,iBAAK,OAAO;AACZ,mBAAO,QAAQ,OAAO,IAAI,MAAM,0BAA0B,CAAC;AAAA,UAC/D;AACA,iBAAO;AAAA,QACX,CAAC;AAAA,MACL,WACS,gBAAgB,QAAW;AAEhC,YAAI,CAAC,KAAK,SAAS;AACf,eAAK,OAAO;AACZ,iBAAO,IAAI,MAAM,0BAA0B,CAAC;AAC5C;AAAA,QACJ;AAAA,MACJ;AAAA,IACJ;AACA,cAAU,MAAM,SAAO,OAAO,GAAG,CAAC;AAAA,EACtC,CAAC;AACL;AACA,SAAS,oBAAoB,MAAM,IAAI,MAAM;AACzC,MAAI,SAAS;AACb,SAAO,WAAY;AACf,QAAI,aAAa;AACb,WAAK,0FAA0F,KAAK,QAAQ,SAAS,GAAG,QAAQ,iGAAiG;AAErO,SAAK,UAAU;AACf,QAAI,WAAW;AACX,WAAK,MAAM,MAAM,SAAS;AAAA,EAClC;AACJ;AACA,SAAS,wBAAwB,SAAS,WAAW,IAAI,MAAM,iBAAiB,QAAM,GAAG,GAAG;AACxF,QAAM,SAAS,CAAC;AAChB,aAAW,UAAU,SAAS;AAC1B,QAA+C,CAAC,OAAO,cAAc,CAAC,OAAO,SAAS,QAAQ;AAC1F,WAAK,qBAAqB,OAAO,IAAI,8DACP;AAAA,IAClC;AACA,eAAW,QAAQ,OAAO,YAAY;AAClC,UAAI,eAAe,OAAO,WAAW,IAAI;AACzC,UAAK,MAAwC;AACzC,YAAI,CAAC,gBACA,OAAO,iBAAiB,YACrB,OAAO,iBAAiB,YAAa;AACzC,eAAK,cAAc,IAAI,0BAA0B,OAAO,IAAI,yCACvB,OAAO,YAAY,CAAC,IAAI;AAG7D,gBAAM,IAAI,MAAM,yBAAyB;AAAA,QAC7C,WACS,UAAU,cAAc;AAG7B,eAAK,cAAc,IAAI,0BAA0B,OAAO,IAAI,6LAI9B;AAC9B,gBAAM,UAAU;AAChB,yBAAe,MAAM;AAAA,QACzB,WACS,aAAa;AAAA,QAElB,CAAC,aAAa,qBAAqB;AACnC,uBAAa,sBAAsB;AACnC,eAAK,cAAc,IAAI,0BAA0B,OAAO,IAAI,oJAGD;AAAA,QAC/D;AAAA,MACJ;AAEA,UAAI,cAAc,sBAAsB,CAAC,OAAO,UAAU,IAAI;AAC1D;AACJ,UAAI,iBAAiB,YAAY,GAAG;AAEhC,cAAM,UAAU,aAAa,aAAa;AAC1C,cAAM,QAAQ,QAAQ,SAAS;AAC/B,iBACI,OAAO,KAAK,iBAAiB,OAAO,IAAI,MAAM,QAAQ,MAAM,cAAc,CAAC;AAAA,MACnF,OACK;AAED,YAAI,mBAAmB,aAAa;AACpC,YAA+C,EAAE,WAAW,mBAAmB;AAC3E,eAAK,cAAc,IAAI,0BAA0B,OAAO,IAAI,4LAA4L;AACxP,6BAAmB,QAAQ,QAAQ,gBAAgB;AAAA,QACvD;AACA,eAAO,KAAK,MAAM,iBAAiB,KAAK,cAAY;AAChD,cAAI,CAAC;AACD,kBAAM,IAAI,MAAM,+BAA+B,IAAI,SAAS,OAAO,IAAI,GAAG;AAC9E,gBAAM,oBAAoB,WAAW,QAAQ,IACvC,SAAS,UACT;AAEN,iBAAO,KAAK,IAAI,IAAI;AAGpB,iBAAO,WAAW,IAAI,IAAI;AAE1B,gBAAM,UAAU,kBAAkB,aAAa;AAC/C,gBAAM,QAAQ,QAAQ,SAAS;AAC/B,iBAAQ,SACJ,iBAAiB,OAAO,IAAI,MAAM,QAAQ,MAAM,cAAc,EAAE;AAAA,QACxE,CAAC,CAAC;AAAA,MACN;AAAA,IACJ;AAAA,EACJ;AACA,SAAO;AACX;AAMA,SAAS,kBAAkB,OAAO;AAC9B,SAAO,MAAM,QAAQ,MAAM,YAAU,OAAO,QAAQ,IAC9C,QAAQ,OAAO,IAAI,MAAM,qCAAqC,CAAC,IAC/D,QAAQ,IAAI,MAAM,QAAQ,IAAI,YAAU,OAAO,cAC7C,QAAQ,IAAI,OAAO,KAAK,OAAO,UAAU,EAAE,OAAO,CAAC,UAAU,SAAS;AAClE,UAAM,eAAe,OAAO,WAAW,IAAI;AAC3C,QAAI,OAAO,iBAAiB,cACxB,EAAE,iBAAiB,eAAe;AAClC,eAAS,KAAK,aAAa,EAAE,KAAK,cAAY;AAC1C,YAAI,CAAC;AACD,iBAAO,QAAQ,OAAO,IAAI,MAAM,+BAA+B,IAAI,SAAS,OAAO,IAAI,yDAAyD,CAAC;AACrJ,cAAM,oBAAoB,WAAW,QAAQ,IACvC,SAAS,UACT;AAEN,eAAO,KAAK,IAAI,IAAI;AAGpB,eAAO,WAAW,IAAI,IAAI;AAC1B;AAAA,MACJ,CAAC,CAAC;AAAA,IACN;AACA,WAAO;AAAA,EACX,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,KAAK,MAAM,KAAK;AACtC;AASA,SAAS,QAAQ,OAAO;AACpB,QAAM,SAAS,OAAO,SAAS;AAC/B,QAAM,eAAe,OAAO,gBAAgB;AAC5C,MAAI,cAAc;AAClB,MAAI,aAAa;AACjB,QAAM,QAAQ,SAAS,MAAM;AACzB,UAAM,KAAK,MAAM,MAAM,EAAE;AACzB,QAAgD,CAAC,eAAe,OAAO,YAAa;AAChF,UAAI,CAAC,gBAAgB,EAAE,GAAG;AACtB,YAAI,aAAa;AACb,eAAK;AAAA,QAAmD,IAAI;AAAA,iBAAoB,YAAY;AAAA,WAAc,KAAK;AAAA,QACnH,OACK;AACD,eAAK;AAAA,QAAmD,IAAI;AAAA,WAAc,KAAK;AAAA,QACnF;AAAA,MACJ;AACA,mBAAa;AACb,oBAAc;AAAA,IAClB;AACA,WAAO,OAAO,QAAQ,EAAE;AAAA,EAC5B,CAAC;AACD,QAAM,oBAAoB,SAAS,MAAM;AACrC,UAAM,EAAE,QAAQ,IAAI,MAAM;AAC1B,UAAM,EAAE,OAAO,IAAI;AACnB,UAAM,eAAe,QAAQ,SAAS,CAAC;AACvC,UAAM,iBAAiB,aAAa;AACpC,QAAI,CAAC,gBAAgB,CAAC,eAAe;AACjC,aAAO;AACX,UAAM,QAAQ,eAAe,UAAU,kBAAkB,KAAK,MAAM,YAAY,CAAC;AACjF,QAAI,QAAQ;AACR,aAAO;AAEX,UAAM,mBAAmB,gBAAgB,QAAQ,SAAS,CAAC,CAAC;AAC5D;AAAA;AAAA,MAEA,SAAS;AAAA;AAAA;AAAA,MAIL,gBAAgB,YAAY,MAAM;AAAA,MAElC,eAAe,eAAe,SAAS,CAAC,EAAE,SAAS,mBACjD,eAAe,UAAU,kBAAkB,KAAK,MAAM,QAAQ,SAAS,CAAC,CAAC,CAAC,IAC1E;AAAA;AAAA,EACV,CAAC;AACD,QAAM,WAAW,SAAS,MAAM,kBAAkB,QAAQ,MACtD,eAAe,aAAa,QAAQ,MAAM,MAAM,MAAM,CAAC;AAC3D,QAAM,gBAAgB,SAAS,MAAM,kBAAkB,QAAQ,MAC3D,kBAAkB,UAAU,aAAa,QAAQ,SAAS,KAC1D,0BAA0B,aAAa,QAAQ,MAAM,MAAM,MAAM,CAAC;AACtE,WAAS,SAAS,IAAI,CAAC,GAAG;AACtB,QAAI,WAAW,CAAC,GAAG;AACf,YAAM,IAAI,OAAO,MAAM,MAAM,OAAO,IAAI,YAAY,MAAM;AAAA,QAAE,MAAM,MAAM,EAAE;AAAA;AAAA,MAE1E,EAAE,MAAM,IAAI;AACZ,UAAI,MAAM,kBACN,OAAO,aAAa,eACpB,yBAAyB,UAAU;AACnC,iBAAS,oBAAoB,MAAM,CAAC;AAAA,MACxC;AACA,aAAO;AAAA,IACX;AACA,WAAO,QAAQ,QAAQ;AAAA,EAC3B;AAEA,MAA0E,WAAW;AACjF,UAAM,WAAW,mBAAmB;AACpC,QAAI,UAAU;AACV,YAAM,sBAAsB;AAAA,QACxB,OAAO,MAAM;AAAA,QACb,UAAU,SAAS;AAAA,QACnB,eAAe,cAAc;AAAA,QAC7B,OAAO;AAAA,MACX;AAEA,eAAS,iBAAiB,SAAS,kBAAkB,CAAC;AAEtD,eAAS,eAAe,KAAK,mBAAmB;AAChD,kBAAY,MAAM;AACd,4BAAoB,QAAQ,MAAM;AAClC,4BAAoB,WAAW,SAAS;AACxC,4BAAoB,gBAAgB,cAAc;AAClD,4BAAoB,QAAQ,gBAAgB,MAAM,MAAM,EAAE,CAAC,IACrD,OACA;AAAA,MACV,GAAG,EAAE,OAAO,OAAO,CAAC;AAAA,IACxB;AAAA,EACJ;AAIA,SAAO;AAAA,IACH;AAAA,IACA,MAAM,SAAS,MAAM,MAAM,MAAM,IAAI;AAAA,IACrC;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AACJ;AACA,SAAS,kBAAkB,QAAQ;AAC/B,SAAO,OAAO,WAAW,IAAI,OAAO,CAAC,IAAI;AAC7C;AACA,IAAM,iBAA+B,gBAAgB;AAAA,EACjD,MAAM;AAAA,EACN,cAAc,EAAE,MAAM,EAAE;AAAA,EACxB,OAAO;AAAA,IACH,IAAI;AAAA,MACA,MAAM,CAAC,QAAQ,MAAM;AAAA,MACrB,UAAU;AAAA,IACd;AAAA,IACA,SAAS;AAAA,IACT,aAAa;AAAA;AAAA,IAEb,kBAAkB;AAAA,IAClB,QAAQ;AAAA,IACR,kBAAkB;AAAA,MACd,MAAM;AAAA,MACN,SAAS;AAAA,IACb;AAAA,IACA,gBAAgB;AAAA,EACpB;AAAA,EACA;AAAA,EACA,MAAM,OAAO,EAAE,MAAM,GAAG;AACpB,UAAM,OAAO,SAAS,QAAQ,KAAK,CAAC;AACpC,UAAM,EAAE,QAAQ,IAAI,OAAO,SAAS;AACpC,UAAM,UAAU,SAAS,OAAO;AAAA,MAC5B,CAAC,aAAa,MAAM,aAAa,QAAQ,iBAAiB,oBAAoB,CAAC,GAAG,KAAK;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAMvF,CAAC,aAAa,MAAM,kBAAkB,QAAQ,sBAAsB,0BAA0B,CAAC,GAAG,KAAK;AAAA,IAC3G,EAAE;AACF,WAAO,MAAM;AACT,YAAM,WAAW,MAAM,WAAW,kBAAkB,MAAM,QAAQ,IAAI,CAAC;AACvE,aAAO,MAAM,SACP,WACA,EAAE,KAAK;AAAA,QACL,gBAAgB,KAAK,gBACf,MAAM,mBACN;AAAA,QACN,MAAM,KAAK;AAAA;AAAA;AAAA,QAGX,SAAS,KAAK;AAAA,QACd,OAAO,QAAQ;AAAA,MACnB,GAAG,QAAQ;AAAA,IACnB;AAAA,EACJ;AACJ,CAAC;AAMD,IAAM,aAAa;AACnB,SAAS,WAAW,GAAG;AAEnB,MAAI,EAAE,WAAW,EAAE,UAAU,EAAE,WAAW,EAAE;AACxC;AAEJ,MAAI,EAAE;AACF;AAEJ,MAAI,EAAE,WAAW,UAAa,EAAE,WAAW;AACvC;AAGJ,MAAI,EAAE,iBAAiB,EAAE,cAAc,cAAc;AAEjD,UAAM,SAAS,EAAE,cAAc,aAAa,QAAQ;AACpD,QAAI,cAAc,KAAK,MAAM;AACzB;AAAA,EACR;AAEA,MAAI,EAAE;AACF,MAAE,eAAe;AACrB,SAAO;AACX;AACA,SAAS,eAAe,OAAO,OAAO;AAClC,aAAW,OAAO,OAAO;AACrB,UAAM,aAAa,MAAM,GAAG;AAC5B,UAAM,aAAa,MAAM,GAAG;AAC5B,QAAI,OAAO,eAAe,UAAU;AAChC,UAAI,eAAe;AACf,eAAO;AAAA,IACf,OACK;AACD,UAAI,CAAC,QAAQ,UAAU,KACnB,WAAW,WAAW,WAAW,UACjC,WAAW,KAAK,CAAC,OAAO,MAAM,UAAU,WAAW,CAAC,CAAC;AACrD,eAAO;AAAA,IACf;AAAA,EACJ;AACA,SAAO;AACX;AAKA,SAAS,gBAAgB,QAAQ;AAC7B,SAAO,SAAU,OAAO,UAAU,OAAO,QAAQ,OAAO,OAAO,OAAQ;AAC3E;AAOA,IAAM,eAAe,CAAC,WAAW,aAAa,iBAAiB,aAAa,OACtE,YACA,eAAe,OACX,cACA;AAEV,IAAM,iBAA+B,gBAAgB;AAAA,EACjD,MAAM;AAAA;AAAA,EAEN,cAAc;AAAA,EACd,OAAO;AAAA,IACH,MAAM;AAAA,MACF,MAAM;AAAA,MACN,SAAS;AAAA,IACb;AAAA,IACA,OAAO;AAAA,EACX;AAAA;AAAA;AAAA,EAGA,cAAc,EAAE,MAAM,EAAE;AAAA,EACxB,MAAM,OAAO,EAAE,OAAO,MAAM,GAAG;AAC3B,IAA2C,oBAAoB;AAC/D,UAAM,gBAAgB,OAAO,qBAAqB;AAClD,UAAM,iBAAiB,SAAS,MAAM,MAAM,SAAS,cAAc,KAAK;AACxE,UAAM,gBAAgB,OAAO,cAAc,CAAC;AAG5C,UAAM,QAAQ,SAAS,MAAM;AACzB,UAAI,eAAe,MAAM,aAAa;AACtC,YAAM,EAAE,QAAQ,IAAI,eAAe;AACnC,UAAI;AACJ,cAAQ,eAAe,QAAQ,YAAY,MACvC,CAAC,aAAa,YAAY;AAC1B;AAAA,MACJ;AACA,aAAO;AAAA,IACX,CAAC;AACD,UAAM,kBAAkB,SAAS,MAAM,eAAe,MAAM,QAAQ,MAAM,KAAK,CAAC;AAChF,YAAQ,cAAc,SAAS,MAAM,MAAM,QAAQ,CAAC,CAAC;AACrD,YAAQ,iBAAiB,eAAe;AACxC,YAAQ,uBAAuB,cAAc;AAC7C,UAAM,UAAU,IAAI;AAGpB,UAAM,MAAM,CAAC,QAAQ,OAAO,gBAAgB,OAAO,MAAM,IAAI,GAAG,CAAC,CAAC,UAAU,IAAI,IAAI,GAAG,CAAC,aAAa,MAAM,OAAO,MAAM;AAEpH,UAAI,IAAI;AAGJ,WAAG,UAAU,IAAI,IAAI;AAOrB,YAAI,QAAQ,SAAS,MAAM,YAAY,aAAa,aAAa;AAC7D,cAAI,CAAC,GAAG,YAAY,MAAM;AACtB,eAAG,cAAc,KAAK;AAAA,UAC1B;AACA,cAAI,CAAC,GAAG,aAAa,MAAM;AACvB,eAAG,eAAe,KAAK;AAAA,UAC3B;AAAA,QACJ;AAAA,MACJ;AAEA,UAAI,YACA;AAAA;AAAA,OAGC,CAAC,QAAQ,CAAC,kBAAkB,IAAI,IAAI,KAAK,CAAC,cAAc;AACzD,SAAC,GAAG,eAAe,IAAI,KAAK,CAAC,GAAG,QAAQ,cAAY,SAAS,QAAQ,CAAC;AAAA,MAC1E;AAAA,IACJ,GAAG,EAAE,OAAO,OAAO,CAAC;AACpB,WAAO,MAAM;AACT,YAAM,QAAQ,eAAe;AAG7B,YAAM,cAAc,MAAM;AAC1B,YAAM,eAAe,gBAAgB;AACrC,YAAM,gBAAgB,gBAAgB,aAAa,WAAW,WAAW;AACzE,UAAI,CAAC,eAAe;AAChB,eAAO,cAAc,MAAM,SAAS,EAAE,WAAW,eAAe,MAAM,CAAC;AAAA,MAC3E;AAEA,YAAM,mBAAmB,aAAa,MAAM,WAAW;AACvD,YAAM,aAAa,mBACb,qBAAqB,OACjB,MAAM,SACN,OAAO,qBAAqB,aACxB,iBAAiB,KAAK,IACtB,mBACR;AACN,YAAM,mBAAmB,WAAS;AAE9B,YAAI,MAAM,UAAU,aAAa;AAC7B,uBAAa,UAAU,WAAW,IAAI;AAAA,QAC1C;AAAA,MACJ;AACA,YAAM,YAAY,EAAE,eAAe,OAAO,CAAC,GAAG,YAAY,OAAO;AAAA,QAC7D;AAAA,QACA,KAAK;AAAA,MACT,CAAC,CAAC;AACF,UACI,aACA,UAAU,KAAK;AAEf,cAAM,OAAO;AAAA,UACT,OAAO,MAAM;AAAA,UACb,MAAM,aAAa;AAAA,UACnB,MAAM,aAAa;AAAA,UACnB,MAAM,aAAa;AAAA,QACvB;AACA,cAAM,oBAAoB,QAAQ,UAAU,GAAG,IACzC,UAAU,IAAI,IAAI,OAAK,EAAE,CAAC,IAC1B,CAAC,UAAU,IAAI,CAAC;AACtB,0BAAkB,QAAQ,cAAY;AAElC,mBAAS,iBAAiB;AAAA,QAC9B,CAAC;AAAA,MACL;AACA;AAAA;AAAA;AAAA,QAGA,cAAc,MAAM,SAAS,EAAE,WAAW,WAAW,MAAM,CAAC,KACxD;AAAA;AAAA,IACR;AAAA,EACJ;AACJ,CAAC;AACD,SAAS,cAAc,MAAM,MAAM;AAC/B,MAAI,CAAC;AACD,WAAO;AACX,QAAM,cAAc,KAAK,IAAI;AAC7B,SAAO,YAAY,WAAW,IAAI,YAAY,CAAC,IAAI;AACvD;AAMA,IAAM,aAAa;AAGnB,SAAS,sBAAsB;AAC3B,QAAM,WAAW,mBAAmB;AACpC,QAAM,aAAa,SAAS,UAAU,SAAS,OAAO,KAAK;AAC3D,QAAM,oBAAoB,SAAS,UAAU,SAAS,OAAO,WAAW,SAAS,OAAO,QAAQ;AAChG,MAAI,eACC,eAAe,eAAe,WAAW,SAAS,YAAY,MAC/D,OAAO,sBAAsB,YAC7B,kBAAkB,SAAS,cAAc;AACzC,UAAM,OAAO,eAAe,cAAc,eAAe;AACzD,SAAK;AAAA;AAAA;AAAA;AAAA,KAGK,IAAI;AAAA;AAAA,MAEH,IAAI;AAAA,eACK;AAAA,EACxB;AACJ;AASA,SAAS,oBAAoB,eAAe,SAAS;AACjD,QAAM,OAAO,OAAO,CAAC,GAAG,eAAe;AAAA;AAAA,IAEnC,SAAS,cAAc,QAAQ,IAAI,aAAW,KAAK,SAAS,CAAC,aAAa,YAAY,SAAS,CAAC,CAAC;AAAA,EACrG,CAAC;AACD,SAAO;AAAA,IACH,SAAS;AAAA,MACL,MAAM;AAAA,MACN,UAAU;AAAA,MACV,SAAS,cAAc;AAAA,MACvB;AAAA,MACA,OAAO;AAAA,IACX;AAAA,EACJ;AACJ;AACA,SAAS,cAAc,SAAS;AAC5B,SAAO;AAAA,IACH,SAAS;AAAA,MACL;AAAA,IACJ;AAAA,EACJ;AACJ;AAEA,IAAI,WAAW;AACf,SAAS,YAAY,KAAK,QAAQ,SAAS;AAGvC,MAAI,OAAO;AACP;AACJ,SAAO,gBAAgB;AAEvB,QAAM,KAAK;AACX,sBAAoB;AAAA,IAChB,IAAI,sBAAsB,KAAK,MAAM,KAAK;AAAA,IAC1C,OAAO;AAAA,IACP,aAAa;AAAA,IACb,UAAU;AAAA,IACV,MAAM;AAAA,IACN,qBAAqB,CAAC,SAAS;AAAA,IAC/B;AAAA,EACJ,GAAG,SAAO;AACN,QAAI,OAAO,IAAI,QAAQ,YAAY;AAC/B,cAAQ,KAAK,uNAAuN;AAAA,IACxO;AAEA,QAAI,GAAG,iBAAiB,CAAC,SAAS,QAAQ;AACtC,UAAI,QAAQ,cAAc;AACtB,gBAAQ,aAAa,MAAM,KAAK;AAAA,UAC5B,MAAM;AAAA,UACN,KAAK;AAAA,UACL,UAAU;AAAA,UACV,OAAO,oBAAoB,OAAO,aAAa,OAAO,eAAe;AAAA,QACzE,CAAC;AAAA,MACL;AAAA,IACJ,CAAC;AAED,QAAI,GAAG,mBAAmB,CAAC,EAAE,UAAU,MAAM,kBAAkB,MAAM;AACjE,UAAI,kBAAkB,gBAAgB;AAClC,cAAM,OAAO,kBAAkB;AAC/B,aAAK,KAAK,KAAK;AAAA,UACX,QAAQ,KAAK,OAAO,GAAG,KAAK,KAAK,SAAS,CAAC,OAAO,MAAM,KAAK;AAAA,UAC7D,WAAW;AAAA,UACX,SAAS;AAAA,UACT,iBAAiB;AAAA,QACrB,CAAC;AAAA,MACL;AAEA,UAAI,QAAQ,kBAAkB,cAAc,GAAG;AAC3C,0BAAkB,gBAAgB;AAClC,0BAAkB,eAAe,QAAQ,kBAAgB;AACrD,cAAI,QAAQ,aAAa,MAAM;AAC/B,cAAI,kBAAkB;AACtB,cAAI,UAAU;AACd,cAAI,YAAY;AAChB,cAAI,aAAa,OAAO;AACpB,oBAAQ,aAAa;AACrB,8BAAkB;AAClB,wBAAY;AAAA,UAChB,WACS,aAAa,eAAe;AACjC,8BAAkB;AAClB,sBAAU;AAAA,UACd,WACS,aAAa,UAAU;AAC5B,8BAAkB;AAClB,sBAAU;AAAA,UACd;AACA,eAAK,KAAK,KAAK;AAAA,YACX;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,UACJ,CAAC;AAAA,QACL,CAAC;AAAA,MACL;AAAA,IACJ,CAAC;AACD,UAAM,OAAO,cAAc,MAAM;AAE7B,wBAAkB;AAClB,UAAI,sBAAsB;AAC1B,UAAI,kBAAkB,iBAAiB;AACvC,UAAI,mBAAmB,iBAAiB;AAAA,IAC5C,CAAC;AACD,UAAM,qBAAqB,wBAAwB;AACnD,QAAI,iBAAiB;AAAA,MACjB,IAAI;AAAA,MACJ,OAAO,SAAS,KAAK,MAAM,KAAK,EAAE;AAAA,MAClC,OAAO;AAAA,IACX,CAAC;AAOD,WAAO,QAAQ,CAAC,OAAO,OAAO;AAC1B,UAAI,iBAAiB;AAAA,QACjB,SAAS;AAAA,QACT,OAAO;AAAA,UACH,OAAO;AAAA,UACP,UAAU,GAAG;AAAA,UACb,SAAS;AAAA,UACT,MAAM,IAAI,IAAI;AAAA,UACd,MAAM,EAAE,MAAM;AAAA,UACd,SAAS,GAAG,KAAK;AAAA,QACrB;AAAA,MACJ,CAAC;AAAA,IACL,CAAC;AAED,QAAI,eAAe;AACnB,WAAO,WAAW,CAAC,IAAI,SAAS;AAC5B,YAAM,OAAO;AAAA,QACT,OAAO,cAAc,YAAY;AAAA,QACjC,MAAM,oBAAoB,MAAM,yCAAyC;AAAA,QACzE,IAAI,oBAAoB,IAAI,iBAAiB;AAAA,MACjD;AAEA,aAAO,eAAe,GAAG,MAAM,kBAAkB;AAAA,QAC7C,OAAO;AAAA,MACX,CAAC;AACD,UAAI,iBAAiB;AAAA,QACjB,SAAS;AAAA,QACT,OAAO;AAAA,UACH,MAAM,IAAI,IAAI;AAAA,UACd,OAAO;AAAA,UACP,UAAU,GAAG;AAAA,UACb;AAAA,UACA,SAAS,GAAG,KAAK;AAAA,QACrB;AAAA,MACJ,CAAC;AAAA,IACL,CAAC;AACD,WAAO,UAAU,CAAC,IAAI,MAAM,YAAY;AACpC,YAAM,OAAO;AAAA,QACT,OAAO,cAAc,WAAW;AAAA,MACpC;AACA,UAAI,SAAS;AACT,aAAK,UAAU;AAAA,UACX,SAAS;AAAA,YACL,MAAM;AAAA,YACN,UAAU;AAAA,YACV,SAAS,UAAU,QAAQ,UAAU;AAAA,YACrC,SAAS;AAAA,YACT,OAAO;AAAA,UACX;AAAA,QACJ;AACA,aAAK,SAAS,cAAc,GAAG;AAAA,MACnC,OACK;AACD,aAAK,SAAS,cAAc,GAAG;AAAA,MACnC;AAEA,WAAK,OAAO,oBAAoB,MAAM,yCAAyC;AAC/E,WAAK,KAAK,oBAAoB,IAAI,iBAAiB;AACnD,UAAI,iBAAiB;AAAA,QACjB,SAAS;AAAA,QACT,OAAO;AAAA,UACH,OAAO;AAAA,UACP,UAAU,GAAG;AAAA,UACb,MAAM,IAAI,IAAI;AAAA,UACd;AAAA,UACA,SAAS,UAAU,YAAY;AAAA,UAC/B,SAAS,GAAG,KAAK;AAAA,QACrB;AAAA,MACJ,CAAC;AAAA,IACL,CAAC;AAID,UAAM,oBAAoB,sBAAsB;AAChD,QAAI,aAAa;AAAA,MACb,IAAI;AAAA,MACJ,OAAO,YAAY,KAAK,MAAM,KAAK;AAAA,MACnC,MAAM;AAAA,MACN,uBAAuB;AAAA,IAC3B,CAAC;AACD,aAAS,oBAAoB;AAEzB,UAAI,CAAC;AACD;AACJ,YAAM,UAAU;AAEhB,UAAI,SAAS,QAAQ,UAAU,EAAE,OAAO,WAAS,CAAC,MAAM;AAAA;AAAA,MAGpD,CAAC,MAAM,OAAO,OAAO,UAAU;AAEnC,aAAO,QAAQ,4BAA4B;AAE3C,UAAI,QAAQ,QAAQ;AAChB,iBAAS,OAAO,OAAO;AAAA;AAAA,UAEvB,gBAAgB,OAAO,QAAQ,OAAO,YAAY,CAAC;AAAA,SAAC;AAAA,MACxD;AAEA,aAAO,QAAQ,WAAS,sBAAsB,OAAO,OAAO,aAAa,KAAK,CAAC;AAC/E,cAAQ,YAAY,OAAO,IAAI,6BAA6B;AAAA,IAChE;AACA,QAAI;AACJ,QAAI,GAAG,iBAAiB,aAAW;AAC/B,4BAAsB;AACtB,UAAI,QAAQ,QAAQ,OAAO,QAAQ,gBAAgB,mBAAmB;AAClE,0BAAkB;AAAA,MACtB;AAAA,IACJ,CAAC;AAID,QAAI,GAAG,kBAAkB,aAAW;AAChC,UAAI,QAAQ,QAAQ,OAAO,QAAQ,gBAAgB,mBAAmB;AAClE,cAAM,SAAS,QAAQ,UAAU;AACjC,cAAM,QAAQ,OAAO,KAAK,CAAAC,WAASA,OAAM,OAAO,YAAY,QAAQ,MAAM;AAC1E,YAAI,OAAO;AACP,kBAAQ,QAAQ;AAAA,YACZ,SAAS,0CAA0C,KAAK;AAAA,UAC5D;AAAA,QACJ;AAAA,MACJ;AAAA,IACJ,CAAC;AACD,QAAI,kBAAkB,iBAAiB;AACvC,QAAI,mBAAmB,iBAAiB;AAAA,EAC5C,CAAC;AACL;AACA,SAAS,eAAe,KAAK;AACzB,MAAI,IAAI,UAAU;AACd,WAAO,IAAI,aAAa,MAAM;AAAA,EAClC,OACK;AACD,WAAO,IAAI,aAAa,MAAM;AAAA,EAClC;AACJ;AACA,SAAS,0CAA0C,OAAO;AACtD,QAAM,EAAE,OAAO,IAAI;AACnB,QAAM,SAAS;AAAA,IACX,EAAE,UAAU,OAAO,KAAK,QAAQ,OAAO,OAAO,KAAK;AAAA,EACvD;AACA,MAAI,OAAO,QAAQ,MAAM;AACrB,WAAO,KAAK;AAAA,MACR,UAAU;AAAA,MACV,KAAK;AAAA,MACL,OAAO,OAAO;AAAA,IAClB,CAAC;AAAA,EACL;AACA,SAAO,KAAK,EAAE,UAAU,OAAO,KAAK,UAAU,OAAO,MAAM,GAAG,CAAC;AAC/D,MAAI,MAAM,KAAK,QAAQ;AACnB,WAAO,KAAK;AAAA,MACR,UAAU;AAAA,MACV,KAAK;AAAA,MACL,OAAO;AAAA,QACH,SAAS;AAAA,UACL,MAAM;AAAA,UACN,UAAU;AAAA,UACV,SAAS,MAAM,KACV,IAAI,SAAO,GAAG,IAAI,IAAI,GAAG,eAAe,GAAG,CAAC,EAAE,EAC9C,KAAK,GAAG;AAAA,UACb,SAAS;AAAA,UACT,OAAO,MAAM;AAAA,QACjB;AAAA,MACJ;AAAA,IACJ,CAAC;AAAA,EACL;AACA,MAAI,OAAO,YAAY,MAAM;AACzB,WAAO,KAAK;AAAA,MACR,UAAU;AAAA,MACV,KAAK;AAAA,MACL,OAAO,OAAO;AAAA,IAClB,CAAC;AAAA,EACL;AACA,MAAI,MAAM,MAAM,QAAQ;AACpB,WAAO,KAAK;AAAA,MACR,UAAU;AAAA,MACV,KAAK;AAAA,MACL,OAAO,MAAM,MAAM,IAAI,WAAS,MAAM,OAAO,IAAI;AAAA,IACrD,CAAC;AAAA,EACL;AACA,MAAI,OAAO,KAAK,MAAM,OAAO,IAAI,EAAE,QAAQ;AACvC,WAAO,KAAK;AAAA,MACR,UAAU;AAAA,MACV,KAAK;AAAA,MACL,OAAO,MAAM,OAAO;AAAA,IACxB,CAAC;AAAA,EACL;AACA,SAAO,KAAK;AAAA,IACR,KAAK;AAAA,IACL,UAAU;AAAA,IACV,OAAO;AAAA,MACH,SAAS;AAAA,QACL,MAAM;AAAA,QACN,UAAU;AAAA,QACV,SAAS,MAAM,MAAM,IAAI,WAAS,MAAM,KAAK,IAAI,CAAC,EAAE,KAAK,KAAK;AAAA,QAC9D,SAAS;AAAA,QACT,OAAO,MAAM;AAAA,MACjB;AAAA,IACJ;AAAA,EACJ,CAAC;AACD,SAAO;AACX;AAIA,IAAM,WAAW;AACjB,IAAM,WAAW;AACjB,IAAM,WAAW;AACjB,IAAM,WAAW;AACjB,IAAM,aAAa;AAEnB,IAAM,OAAO;AACb,IAAM,UAAU;AAChB,IAAM,UAAU;AAChB,SAAS,8BAA8B,OAAO;AAC1C,QAAM,OAAO,CAAC;AACd,QAAM,EAAE,OAAO,IAAI;AACnB,MAAI,OAAO,QAAQ,MAAM;AACrB,SAAK,KAAK;AAAA,MACN,OAAO,OAAO,OAAO,IAAI;AAAA,MACzB,WAAW;AAAA,MACX,iBAAiB;AAAA,IACrB,CAAC;AAAA,EACL;AACA,MAAI,OAAO,SAAS;AAChB,SAAK,KAAK;AAAA,MACN,OAAO;AAAA,MACP,WAAW;AAAA,MACX,iBAAiB;AAAA,IACrB,CAAC;AAAA,EACL;AACA,MAAI,MAAM,YAAY;AAClB,SAAK,KAAK;AAAA,MACN,OAAO;AAAA,MACP,WAAW;AAAA,MACX,iBAAiB;AAAA,IACrB,CAAC;AAAA,EACL;AACA,MAAI,MAAM,kBAAkB;AACxB,SAAK,KAAK;AAAA,MACN,OAAO;AAAA,MACP,WAAW;AAAA,MACX,iBAAiB;AAAA,IACrB,CAAC;AAAA,EACL;AACA,MAAI,MAAM,aAAa;AACnB,SAAK,KAAK;AAAA,MACN,OAAO;AAAA,MACP,WAAW;AAAA,MACX,iBAAiB;AAAA,IACrB,CAAC;AAAA,EACL;AACA,MAAI,OAAO,UAAU;AACjB,SAAK,KAAK;AAAA,MACN,OAAO,OAAO,OAAO,aAAa,WAC5B,aAAa,OAAO,QAAQ,KAC5B;AAAA,MACN,WAAW;AAAA,MACX,iBAAiB;AAAA,IACrB,CAAC;AAAA,EACL;AAGA,MAAI,KAAK,OAAO;AAChB,MAAI,MAAM,MAAM;AACZ,SAAK,OAAO,eAAe;AAC3B,WAAO,UAAU;AAAA,EACrB;AACA,SAAO;AAAA,IACH;AAAA,IACA,OAAO,OAAO;AAAA,IACd;AAAA,IACA,UAAU,MAAM,SAAS,IAAI,6BAA6B;AAAA,EAC9D;AACJ;AAEA,IAAI,gBAAgB;AACpB,IAAM,oBAAoB;AAC1B,SAAS,sBAAsB,OAAO,cAAc;AAGhD,QAAM,gBAAgB,aAAa,QAAQ,UACvC,kBAAkB,aAAa,QAAQ,aAAa,QAAQ,SAAS,CAAC,GAAG,MAAM,MAAM;AACzF,QAAM,mBAAmB,MAAM,cAAc;AAC7C,MAAI,CAAC,eAAe;AAChB,UAAM,cAAc,aAAa,QAAQ,KAAK,WAAS,kBAAkB,OAAO,MAAM,MAAM,CAAC;AAAA,EACjG;AACA,QAAM,SAAS,QAAQ,gBAAc,sBAAsB,YAAY,YAAY,CAAC;AACxF;AACA,SAAS,6BAA6B,OAAO;AACzC,QAAM,aAAa;AACnB,QAAM,SAAS,QAAQ,4BAA4B;AACvD;AACA,SAAS,gBAAgB,OAAO,QAAQ;AACpC,QAAM,QAAQ,OAAO,MAAM,EAAE,EAAE,MAAM,iBAAiB;AACtD,QAAM,aAAa;AACnB,MAAI,CAAC,SAAS,MAAM,SAAS,GAAG;AAC5B,WAAO;AAAA,EACX;AAEA,QAAM,cAAc,IAAI,OAAO,MAAM,CAAC,EAAE,QAAQ,OAAO,EAAE,GAAG,MAAM,CAAC,CAAC;AACpE,MAAI,YAAY,KAAK,MAAM,GAAG;AAE1B,UAAM,SAAS,QAAQ,WAAS,gBAAgB,OAAO,MAAM,CAAC;AAE9D,QAAI,MAAM,OAAO,SAAS,OAAO,WAAW,KAAK;AAC7C,YAAM,aAAa,MAAM,GAAG,KAAK,MAAM;AACvC,aAAO;AAAA,IACX;AAEA,WAAO;AAAA,EACX;AACA,QAAM,OAAO,MAAM,OAAO,KAAK,YAAY;AAC3C,QAAM,cAAc,OAAO,IAAI;AAE/B,MAAI,CAAC,OAAO,WAAW,GAAG,MACrB,YAAY,SAAS,MAAM,KAAK,KAAK,SAAS,MAAM;AACrD,WAAO;AACX,MAAI,YAAY,WAAW,MAAM,KAAK,KAAK,WAAW,MAAM;AACxD,WAAO;AACX,MAAI,MAAM,OAAO,QAAQ,OAAO,MAAM,OAAO,IAAI,EAAE,SAAS,MAAM;AAC9D,WAAO;AACX,SAAO,MAAM,SAAS,KAAK,WAAS,gBAAgB,OAAO,MAAM,CAAC;AACtE;AACA,SAAS,KAAK,KAAK,MAAM;AACrB,QAAM,MAAM,CAAC;AACb,aAAW,OAAO,KAAK;AACnB,QAAI,CAAC,KAAK,SAAS,GAAG,GAAG;AAErB,UAAI,GAAG,IAAI,IAAI,GAAG;AAAA,IACtB;AAAA,EACJ;AACA,SAAO;AACX;AAOA,SAAS,aAAa,SAAS;AAC3B,QAAM,UAAU,oBAAoB,QAAQ,QAAQ,OAAO;AAC3D,QAAM,eAAe,QAAQ,cAAc;AAC3C,QAAM,mBAAmB,QAAQ,kBAAkB;AACnD,QAAM,gBAAgB,QAAQ;AAC9B,MAA+C,CAAC;AAC5C,UAAM,IAAI,MAAM,gIACyD;AAC7E,QAAM,eAAe,aAAa;AAClC,QAAM,sBAAsB,aAAa;AACzC,QAAM,cAAc,aAAa;AACjC,QAAM,eAAe,WAAW,yBAAyB;AACzD,MAAI,kBAAkB;AAEtB,MAAI,aAAa,QAAQ,kBAAkB,uBAAuB,SAAS;AACvE,YAAQ,oBAAoB;AAAA,EAChC;AACA,QAAM,kBAAkB,cAAc,KAAK,MAAM,gBAAc,KAAK,UAAU;AAC9E,QAAM,eAAe,cAAc,KAAK,MAAM,WAAW;AACzD,QAAM;AAAA;AAAA,IAEN,cAAc,KAAK,MAAM,MAAM;AAAA;AAC/B,WAAS,SAAS,eAAe,OAAO;AACpC,QAAI;AACJ,QAAI;AACJ,QAAI,YAAY,aAAa,GAAG;AAC5B,eAAS,QAAQ,iBAAiB,aAAa;AAC/C,UAA+C,CAAC,QAAQ;AACpD,aAAK,iBAAiB,OAAO,aAAa,CAAC,uCAAuC,KAAK;AAAA,MAC3F;AACA,eAAS;AAAA,IACb,OACK;AACD,eAAS;AAAA,IACb;AACA,WAAO,QAAQ,SAAS,QAAQ,MAAM;AAAA,EAC1C;AACA,WAAS,YAAY,MAAM;AACvB,UAAM,gBAAgB,QAAQ,iBAAiB,IAAI;AACnD,QAAI,eAAe;AACf,cAAQ,YAAY,aAAa;AAAA,IACrC,WACU,MAAwC;AAC9C,WAAK,qCAAqC,OAAO,IAAI,CAAC,GAAG;AAAA,IAC7D;AAAA,EACJ;AACA,WAAS,YAAY;AACjB,WAAO,QAAQ,UAAU,EAAE,IAAI,kBAAgB,aAAa,MAAM;AAAA,EACtE;AACA,WAAS,SAAS,MAAM;AACpB,WAAO,CAAC,CAAC,QAAQ,iBAAiB,IAAI;AAAA,EAC1C;AACA,WAAS,QAAQ,aAAa,iBAAiB;AAI3C,sBAAkB,OAAO,CAAC,GAAG,mBAAmB,aAAa,KAAK;AAClE,QAAI,OAAO,gBAAgB,UAAU;AACjC,YAAM,qBAAqB,SAAS,cAAc,aAAa,gBAAgB,IAAI;AACnF,YAAMC,gBAAe,QAAQ,QAAQ,EAAE,MAAM,mBAAmB,KAAK,GAAG,eAAe;AACvF,YAAMC,QAAO,cAAc,WAAW,mBAAmB,QAAQ;AACjE,UAAK,MAAwC;AACzC,YAAIA,MAAK,WAAW,IAAI;AACpB,eAAK,aAAa,WAAW,kBAAkBA,KAAI,4DAA4D;AAAA,iBAC1G,CAACD,cAAa,QAAQ,QAAQ;AACnC,eAAK,0CAA0C,WAAW,GAAG;AAAA,QACjE;AAAA,MACJ;AAEA,aAAO,OAAO,oBAAoBA,eAAc;AAAA,QAC5C,QAAQ,aAAaA,cAAa,MAAM;AAAA,QACxC,MAAM,OAAO,mBAAmB,IAAI;AAAA,QACpC,gBAAgB;AAAA,QAChB,MAAAC;AAAA,MACJ,CAAC;AAAA,IACL;AACA,QAA+C,CAAC,gBAAgB,WAAW,GAAG;AAC1E,WAAK;AAAA,cAA+F,WAAW;AAC/G,aAAO,QAAQ,CAAC,CAAC;AAAA,IACrB;AACA,QAAI;AAEJ,QAAI,YAAY,QAAQ,MAAM;AAC1B,UACI,YAAY,eACZ,EAAE,UAAU;AAAA,MAEZ,OAAO,KAAK,YAAY,MAAM,EAAE,QAAQ;AACxC,aAAK,SAAS,YAAY,IAAI,gGAAgG;AAAA,MAClI;AACA,wBAAkB,OAAO,CAAC,GAAG,aAAa;AAAA,QACtC,MAAM,SAAS,cAAc,YAAY,MAAM,gBAAgB,IAAI,EAAE;AAAA,MACzE,CAAC;AAAA,IACL,OACK;AAED,YAAM,eAAe,OAAO,CAAC,GAAG,YAAY,MAAM;AAClD,iBAAW,OAAO,cAAc;AAC5B,YAAI,aAAa,GAAG,KAAK,MAAM;AAC3B,iBAAO,aAAa,GAAG;AAAA,QAC3B;AAAA,MACJ;AAEA,wBAAkB,OAAO,CAAC,GAAG,aAAa;AAAA,QACtC,QAAQ,aAAa,YAAY;AAAA,MACrC,CAAC;AAGD,sBAAgB,SAAS,aAAa,gBAAgB,MAAM;AAAA,IAChE;AACA,UAAM,eAAe,QAAQ,QAAQ,iBAAiB,eAAe;AACrE,UAAM,OAAO,YAAY,QAAQ;AACjC,QAA+C,QAAQ,CAAC,KAAK,WAAW,GAAG,GAAG;AAC1E,WAAK,mEAAmE,IAAI,YAAY,IAAI,IAAI;AAAA,IACpG;AAGA,iBAAa,SAAS,gBAAgB,aAAa,aAAa,MAAM,CAAC;AACvE,UAAM,WAAW,aAAa,kBAAkB,OAAO,CAAC,GAAG,aAAa;AAAA,MACpE,MAAM,WAAW,IAAI;AAAA,MACrB,MAAM,aAAa;AAAA,IACvB,CAAC,CAAC;AACF,UAAM,OAAO,cAAc,WAAW,QAAQ;AAC9C,QAAK,MAAwC;AACzC,UAAI,KAAK,WAAW,IAAI,GAAG;AACvB,aAAK,aAAa,WAAW,kBAAkB,IAAI,4DAA4D;AAAA,MACnH,WACS,CAAC,aAAa,QAAQ,QAAQ;AACnC,aAAK,0CAA0C,YAAY,QAAQ,OAAO,YAAY,OAAO,WAAW,GAAG;AAAA,MAC/G;AAAA,IACJ;AACA,WAAO,OAAO;AAAA,MACV;AAAA;AAAA;AAAA,MAGA;AAAA,MACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,QAMA,qBAAqB,iBACf,eAAe,YAAY,KAAK,IAC/B,YAAY,SAAS,CAAC;AAAA;AAAA,IACjC,GAAG,cAAc;AAAA,MACb,gBAAgB;AAAA,MAChB;AAAA,IACJ,CAAC;AAAA,EACL;AACA,WAAS,iBAAiB,IAAI;AAC1B,WAAO,OAAO,OAAO,WACf,SAAS,cAAc,IAAI,aAAa,MAAM,IAAI,IAClD,OAAO,CAAC,GAAG,EAAE;AAAA,EACvB;AACA,WAAS,wBAAwB,IAAI,MAAM;AACvC,QAAI,oBAAoB,IAAI;AACxB,aAAO,kBAAkB,GAAyC;AAAA,QAC9D;AAAA,QACA;AAAA,MACJ,CAAC;AAAA,IACL;AAAA,EACJ;AACA,WAAS,KAAK,IAAI;AACd,WAAO,iBAAiB,EAAE;AAAA,EAC9B;AACA,WAAS,QAAQ,IAAI;AACjB,WAAO,KAAK,OAAO,iBAAiB,EAAE,GAAG,EAAE,SAAS,KAAK,CAAC,CAAC;AAAA,EAC/D;AACA,WAAS,qBAAqB,IAAI;AAC9B,UAAM,cAAc,GAAG,QAAQ,GAAG,QAAQ,SAAS,CAAC;AACpD,QAAI,eAAe,YAAY,UAAU;AACrC,YAAM,EAAE,SAAS,IAAI;AACrB,UAAI,oBAAoB,OAAO,aAAa,aAAa,SAAS,EAAE,IAAI;AACxE,UAAI,OAAO,sBAAsB,UAAU;AACvC,4BACI,kBAAkB,SAAS,GAAG,KAAK,kBAAkB,SAAS,GAAG,IAC1D,oBAAoB,iBAAiB,iBAAiB;AAAA;AAAA,UAErD,EAAE,MAAM,kBAAkB;AAAA;AAGtC,0BAAkB,SAAS,CAAC;AAAA,MAChC;AACA,UACI,kBAAkB,QAAQ,QAC1B,EAAE,UAAU,oBAAoB;AAChC,aAAK;AAAA,EAA4B,KAAK,UAAU,mBAAmB,MAAM,CAAC,CAAC;AAAA,uBAA0B,GAAG,QAAQ,2EAA2E;AAC3L,cAAM,IAAI,MAAM,kBAAkB;AAAA,MACtC;AACA,aAAO,OAAO;AAAA,QACV,OAAO,GAAG;AAAA,QACV,MAAM,GAAG;AAAA;AAAA,QAET,QAAQ,kBAAkB,QAAQ,OAAO,CAAC,IAAI,GAAG;AAAA,MACrD,GAAG,iBAAiB;AAAA,IACxB;AAAA,EACJ;AACA,WAAS,iBAAiB,IAAI,gBAAgB;AAC1C,UAAM,iBAAkB,kBAAkB,QAAQ,EAAE;AACpD,UAAM,OAAO,aAAa;AAC1B,UAAM,OAAO,GAAG;AAChB,UAAM,QAAQ,GAAG;AAEjB,UAAMN,WAAU,GAAG,YAAY;AAC/B,UAAM,iBAAiB,qBAAqB,cAAc;AAC1D,QAAI;AACA,aAAO;AAAA,QAAiB,OAAO,iBAAiB,cAAc,GAAG;AAAA,UAC7D,OAAO,OAAO,mBAAmB,WAC3B,OAAO,CAAC,GAAG,MAAM,eAAe,KAAK,IACrC;AAAA,UACN;AAAA,UACA,SAAAA;AAAA,QACJ,CAAC;AAAA;AAAA,QAED,kBAAkB;AAAA,MAAc;AAEpC,UAAM,aAAa;AACnB,eAAW,iBAAiB;AAC5B,QAAI;AACJ,QAAI,CAAC,SAAS,oBAAoB,kBAAkB,MAAM,cAAc,GAAG;AACvE,gBAAU,kBAAkB,IAA2C,EAAE,IAAI,YAAY,KAAK,CAAC;AAE/F;AAAA,QAAa;AAAA,QAAM;AAAA;AAAA;AAAA,QAGnB;AAAA;AAAA;AAAA,QAGA;AAAA,MAAK;AAAA,IACT;AACA,YAAQ,UAAU,QAAQ,QAAQ,OAAO,IAAI,SAAS,YAAY,IAAI,GACjE,MAAM,CAAC,UAAU,oBAAoB,KAAK;AAAA;AAAA,MAEvC;AAAA,QAAoB;AAAA,QAAO;AAAA;AAAA,MAA4C,IACjE,QACA,YAAY,KAAK;AAAA;AAAA;AAAA,MAEvB,aAAa,OAAO,YAAY,IAAI;AAAA,KAAC,EACxC,KAAK,CAACO,aAAY;AACnB,UAAIA,UAAS;AACT,YAAI;AAAA,UAAoBA;AAAA,UAAS;AAAA;AAAA,QAA4C,GAAG;AAC5E;AAAA,UAEI,oBAAoB,kBAAkB,QAAQA,SAAQ,EAAE,GAAG,UAAU;AAAA,UAErE;AAAA,WAEC,eAAe,SAAS,eAAe;AAAA;AAAA,YAEhC,eAAe,SAAS;AAAA,cAC1B,KAAK,IAAI;AACf,iBAAK,mFAAmF,KAAK,QAAQ,SAAS,WAAW,QAAQ;AAAA,gNAAyP;AAC1X,mBAAO,QAAQ,OAAO,IAAI,MAAM,uCAAuC,CAAC;AAAA,UAC5E;AACA,iBAAO;AAAA;AAAA,YAEP,OAAO;AAAA;AAAA,cAEH,SAAAP;AAAA,YACJ,GAAG,iBAAiBO,SAAQ,EAAE,GAAG;AAAA,cAC7B,OAAO,OAAOA,SAAQ,OAAO,WACvB,OAAO,CAAC,GAAG,MAAMA,SAAQ,GAAG,KAAK,IACjC;AAAA,cACN;AAAA,YACJ,CAAC;AAAA;AAAA,YAED,kBAAkB;AAAA,UAAU;AAAA,QAChC;AAAA,MACJ,OACK;AAED,QAAAA,WAAU,mBAAmB,YAAY,MAAM,MAAMP,UAAS,IAAI;AAAA,MACtE;AACA,uBAAiB,YAAY,MAAMO,QAAO;AAC1C,aAAOA;AAAA,IACX,CAAC;AAAA,EACL;AAMA,WAAS,iCAAiC,IAAI,MAAM;AAChD,UAAM,QAAQ,wBAAwB,IAAI,IAAI;AAC9C,WAAO,QAAQ,QAAQ,OAAO,KAAK,IAAI,QAAQ,QAAQ;AAAA,EAC3D;AACA,WAAS,eAAe,IAAI;AACxB,UAAM,MAAM,cAAc,OAAO,EAAE,KAAK,EAAE;AAE1C,WAAO,OAAO,OAAO,IAAI,mBAAmB,aACtC,IAAI,eAAe,EAAE,IACrB,GAAG;AAAA,EACb;AAEA,WAAS,SAAS,IAAI,MAAM;AACxB,QAAI;AACJ,UAAM,CAAC,gBAAgB,iBAAiB,eAAe,IAAI,uBAAuB,IAAI,IAAI;AAE1F,aAAS,wBAAwB,eAAe,QAAQ,GAAG,oBAAoB,IAAI,IAAI;AAEvF,eAAW,UAAU,gBAAgB;AACjC,aAAO,YAAY,QAAQ,WAAS;AAChC,eAAO,KAAK,iBAAiB,OAAO,IAAI,IAAI,CAAC;AAAA,MACjD,CAAC;AAAA,IACL;AACA,UAAM,0BAA0B,iCAAiC,KAAK,MAAM,IAAI,IAAI;AACpF,WAAO,KAAK,uBAAuB;AAEnC,WAAQ,cAAc,MAAM,EACvB,KAAK,MAAM;AAEZ,eAAS,CAAC;AACV,iBAAW,SAAS,aAAa,KAAK,GAAG;AACrC,eAAO,KAAK,iBAAiB,OAAO,IAAI,IAAI,CAAC;AAAA,MACjD;AACA,aAAO,KAAK,uBAAuB;AACnC,aAAO,cAAc,MAAM;AAAA,IAC/B,CAAC,EACI,KAAK,MAAM;AAEZ,eAAS,wBAAwB,iBAAiB,qBAAqB,IAAI,IAAI;AAC/E,iBAAW,UAAU,iBAAiB;AAClC,eAAO,aAAa,QAAQ,WAAS;AACjC,iBAAO,KAAK,iBAAiB,OAAO,IAAI,IAAI,CAAC;AAAA,QACjD,CAAC;AAAA,MACL;AACA,aAAO,KAAK,uBAAuB;AAEnC,aAAO,cAAc,MAAM;AAAA,IAC/B,CAAC,EACI,KAAK,MAAM;AAEZ,eAAS,CAAC;AACV,iBAAW,UAAU,iBAAiB;AAElC,YAAI,OAAO,aAAa;AACpB,cAAI,QAAQ,OAAO,WAAW,GAAG;AAC7B,uBAAW,eAAe,OAAO;AAC7B,qBAAO,KAAK,iBAAiB,aAAa,IAAI,IAAI,CAAC;AAAA,UAC3D,OACK;AACD,mBAAO,KAAK,iBAAiB,OAAO,aAAa,IAAI,IAAI,CAAC;AAAA,UAC9D;AAAA,QACJ;AAAA,MACJ;AACA,aAAO,KAAK,uBAAuB;AAEnC,aAAO,cAAc,MAAM;AAAA,IAC/B,CAAC,EACI,KAAK,MAAM;AAGZ,SAAG,QAAQ,QAAQ,YAAW,OAAO,iBAAiB,CAAC,CAAE;AAEzD,eAAS,wBAAwB,iBAAiB,oBAAoB,IAAI,MAAM,cAAc;AAC9F,aAAO,KAAK,uBAAuB;AAEnC,aAAO,cAAc,MAAM;AAAA,IAC/B,CAAC,EACI,KAAK,MAAM;AAEZ,eAAS,CAAC;AACV,iBAAW,SAAS,oBAAoB,KAAK,GAAG;AAC5C,eAAO,KAAK,iBAAiB,OAAO,IAAI,IAAI,CAAC;AAAA,MACjD;AACA,aAAO,KAAK,uBAAuB;AACnC,aAAO,cAAc,MAAM;AAAA,IAC/B,CAAC,EAEI,MAAM,SAAO;AAAA,MAAoB;AAAA,MAAK;AAAA;AAAA,IAAuC,IAC5E,MACA,QAAQ,OAAO,GAAG,CAAC;AAAA,EAC7B;AACA,WAAS,iBAAiB,IAAI,MAAM,SAAS;AAGzC,gBACK,KAAK,EACL,QAAQ,WAAS,eAAe,MAAM,MAAM,IAAI,MAAM,OAAO,CAAC,CAAC;AAAA,EACxE;AAMA,WAAS,mBAAmB,YAAY,MAAM,QAAQP,UAAS,MAAM;AAEjE,UAAM,QAAQ,wBAAwB,YAAY,IAAI;AACtD,QAAI;AACA,aAAO;AAEX,UAAM,oBAAoB,SAAS;AACnC,UAAM,QAAQ,CAAC,YAAY,CAAC,IAAI,QAAQ;AAGxC,QAAI,QAAQ;AAGR,UAAIA,YAAW;AACX,sBAAc,QAAQ,WAAW,UAAU,OAAO;AAAA,UAC9C,QAAQ,qBAAqB,SAAS,MAAM;AAAA,QAChD,GAAG,IAAI,CAAC;AAAA;AAER,sBAAc,KAAK,WAAW,UAAU,IAAI;AAAA,IACpD;AAEA,iBAAa,QAAQ;AACrB,iBAAa,YAAY,MAAM,QAAQ,iBAAiB;AACxD,gBAAY;AAAA,EAChB;AACA,MAAI;AAEJ,WAAS,iBAAiB;AAEtB,QAAI;AACA;AACJ,4BAAwB,cAAc,OAAO,CAAC,IAAI,OAAO,SAAS;AAC9D,UAAI,CAAC,OAAO;AACR;AAEJ,YAAM,aAAa,QAAQ,EAAE;AAI7B,YAAM,iBAAiB,qBAAqB,UAAU;AACtD,UAAI,gBAAgB;AAChB,yBAAiB,OAAO,gBAAgB,EAAE,SAAS,MAAM,OAAO,KAAK,CAAC,GAAG,UAAU,EAAE,MAAM,IAAI;AAC/F;AAAA,MACJ;AACA,wBAAkB;AAClB,YAAM,OAAO,aAAa;AAE1B,UAAI,WAAW;AACX,2BAAmB,aAAa,KAAK,UAAU,KAAK,KAAK,GAAG,sBAAsB,CAAC;AAAA,MACvF;AACA,eAAS,YAAY,IAAI,EACpB,MAAM,CAAC,UAAU;AAClB,YAAI;AAAA,UAAoB;AAAA,UAAO,IAAwC;AAAA;AAAA,QAAuC,GAAG;AAC7G,iBAAO;AAAA,QACX;AACA,YAAI;AAAA,UAAoB;AAAA,UAAO;AAAA;AAAA,QAA4C,GAAG;AAU1E;AAAA,YAAiB,OAAO,iBAAiB,MAAM,EAAE,GAAG;AAAA,cAChD,OAAO;AAAA,YACX,CAAC;AAAA,YAAG;AAAA;AAAA,UAEJ,EACK,KAAK,aAAW;AAIjB,gBAAI;AAAA,cAAoB;AAAA,cAAS,IAC7B;AAAA;AAAA,YAAyC,KACzC,CAAC,KAAK,SACN,KAAK,SAAS,eAAe,KAAK;AAClC,4BAAc,GAAG,IAAI,KAAK;AAAA,YAC9B;AAAA,UACJ,CAAC,EACI,MAAM,IAAI;AAEf,iBAAO,QAAQ,OAAO;AAAA,QAC1B;AAEA,YAAI,KAAK,OAAO;AACZ,wBAAc,GAAG,CAAC,KAAK,OAAO,KAAK;AAAA,QACvC;AAEA,eAAO,aAAa,OAAO,YAAY,IAAI;AAAA,MAC/C,CAAC,EACI,KAAK,CAAC,YAAY;AACnB,kBACI,WACI;AAAA;AAAA,UAEA;AAAA,UAAY;AAAA,UAAM;AAAA,QAAK;AAE/B,YAAI,SAAS;AACT,cAAI,KAAK;AAAA;AAAA,UAGL,CAAC;AAAA,YAAoB;AAAA,YAAS;AAAA;AAAA,UAAuC,GAAG;AACxE,0BAAc,GAAG,CAAC,KAAK,OAAO,KAAK;AAAA,UACvC,WACS,KAAK,SAAS,eAAe,OAClC;AAAA,YAAoB;AAAA,YAAS,IAAwC;AAAA;AAAA,UAAyC,GAAG;AAGjH,0BAAc,GAAG,IAAI,KAAK;AAAA,UAC9B;AAAA,QACJ;AACA,yBAAiB,YAAY,MAAM,OAAO;AAAA,MAC9C,CAAC,EAEI,MAAM,IAAI;AAAA,IACnB,CAAC;AAAA,EACL;AAEA,MAAI,gBAAgB,aAAa;AACjC,MAAI,iBAAiB,aAAa;AAClC,MAAI;AASJ,WAAS,aAAa,OAAO,IAAI,MAAM;AACnC,gBAAY,KAAK;AACjB,UAAM,OAAO,eAAe,KAAK;AACjC,QAAI,KAAK,QAAQ;AACb,WAAK,QAAQ,aAAW,QAAQ,OAAO,IAAI,IAAI,CAAC;AAAA,IACpD,OACK;AACD,UAAK,MAAwC;AACzC,aAAK,yCAAyC;AAAA,MAClD;AACA,cAAQ,MAAM,KAAK;AAAA,IACvB;AAEA,WAAO,QAAQ,OAAO,KAAK;AAAA,EAC/B;AACA,WAAS,UAAU;AACf,QAAI,SAAS,aAAa,UAAU;AAChC,aAAO,QAAQ,QAAQ;AAC3B,WAAO,IAAI,QAAQ,CAACQ,UAAS,WAAW;AACpC,oBAAc,IAAI,CAACA,UAAS,MAAM,CAAC;AAAA,IACvC,CAAC;AAAA,EACL;AACA,WAAS,YAAY,KAAK;AACtB,QAAI,CAAC,OAAO;AAER,cAAQ,CAAC;AACT,qBAAe;AACf,oBACK,KAAK,EACL,QAAQ,CAAC,CAACA,UAAS,MAAM,MAAO,MAAM,OAAO,GAAG,IAAIA,SAAQ,CAAE;AACnE,oBAAc,MAAM;AAAA,IACxB;AACA,WAAO;AAAA,EACX;AAEA,WAAS,aAAa,IAAI,MAAM,QAAQ,mBAAmB;AACvD,UAAM,EAAE,eAAe,IAAI;AAC3B,QAAI,CAAC,aAAa,CAAC;AACf,aAAO,QAAQ,QAAQ;AAC3B,UAAM,iBAAkB,CAAC,UAAU,uBAAuB,aAAa,GAAG,UAAU,CAAC,CAAC,MAChF,qBAAqB,CAAC,WACpB,QAAQ,SACR,QAAQ,MAAM,UAClB;AACJ,WAAO,SAAS,EACX,KAAK,MAAM,eAAe,IAAI,MAAM,cAAc,CAAC,EACnD,KAAK,cAAY,YAAY,iBAAiB,QAAQ,CAAC,EACvD,MAAM,SAAO,aAAa,KAAK,IAAI,IAAI,CAAC;AAAA,EACjD;AACA,QAAM,KAAK,CAAC,UAAU,cAAc,GAAG,KAAK;AAC5C,MAAI;AACJ,QAAM,gBAAgB,oBAAI,IAAI;AAC9B,QAAM,SAAS;AAAA,IACX;AAAA,IACA,WAAW;AAAA,IACX;AAAA,IACA;AAAA,IACA,aAAa,QAAQ;AAAA,IACrB;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,MAAM,MAAM,GAAG,EAAE;AAAA,IACjB,SAAS,MAAM,GAAG,CAAC;AAAA,IACnB,YAAY,aAAa;AAAA,IACzB,eAAe,oBAAoB;AAAA,IACnC,WAAW,YAAY;AAAA,IACvB,SAAS,eAAe;AAAA,IACxB;AAAA,IACA,QAAQ,KAAK;AACT,YAAMC,UAAS;AACf,UAAI,UAAU,cAAc,UAAU;AACtC,UAAI,UAAU,cAAc,UAAU;AACtC,UAAI,OAAO,iBAAiB,UAAUA;AACtC,aAAO,eAAe,IAAI,OAAO,kBAAkB,UAAU;AAAA,QACzD,YAAY;AAAA,QACZ,KAAK,MAAM,MAAM,YAAY;AAAA,MACjC,CAAC;AAID,UAAI;AAAA;AAAA,MAGA,CAAC,WACD,aAAa,UAAU,2BAA2B;AAElD,kBAAU;AACV,aAAK,cAAc,QAAQ,EAAE,MAAM,SAAO;AACtC,cAAK;AACD,iBAAK,8CAA8C,GAAG;AAAA,QAC9D,CAAC;AAAA,MACL;AACA,YAAM,gBAAgB,CAAC;AACvB,iBAAW,OAAO,2BAA2B;AACzC,eAAO,eAAe,eAAe,KAAK;AAAA,UACtC,KAAK,MAAM,aAAa,MAAM,GAAG;AAAA,UACjC,YAAY;AAAA,QAChB,CAAC;AAAA,MACL;AACA,UAAI,QAAQ,WAAWA,OAAM;AAC7B,UAAI,QAAQ,kBAAkB,gBAAgB,aAAa,CAAC;AAC5D,UAAI,QAAQ,uBAAuB,YAAY;AAC/C,YAAM,aAAa,IAAI;AACvB,oBAAc,IAAI,GAAG;AACrB,UAAI,UAAU,WAAY;AACtB,sBAAc,OAAO,GAAG;AAExB,YAAI,cAAc,OAAO,GAAG;AAExB,4BAAkB;AAClB,mCAAyB,sBAAsB;AAC/C,kCAAwB;AACxB,uBAAa,QAAQ;AACrB,oBAAU;AACV,kBAAQ;AAAA,QACZ;AACA,mBAAW;AAAA,MACf;AAEA,UAA0E,WAAW;AACjF,oBAAY,KAAKA,SAAQ,OAAO;AAAA,MACpC;AAAA,IACJ;AAAA,EACJ;AAEA,WAAS,cAAc,QAAQ;AAC3B,WAAO,OAAO,OAAO,CAAC,SAAS,UAAU,QAAQ,KAAK,MAAM,eAAe,KAAK,CAAC,GAAG,QAAQ,QAAQ,CAAC;AAAA,EACzG;AACA,SAAO;AACX;AACA,SAAS,uBAAuB,IAAI,MAAM;AACtC,QAAM,iBAAiB,CAAC;AACxB,QAAM,kBAAkB,CAAC;AACzB,QAAM,kBAAkB,CAAC;AACzB,QAAM,MAAM,KAAK,IAAI,KAAK,QAAQ,QAAQ,GAAG,QAAQ,MAAM;AAC3D,WAAS,IAAI,GAAG,IAAI,KAAK,KAAK;AAC1B,UAAM,aAAa,KAAK,QAAQ,CAAC;AACjC,QAAI,YAAY;AACZ,UAAI,GAAG,QAAQ,KAAK,YAAU,kBAAkB,QAAQ,UAAU,CAAC;AAC/D,wBAAgB,KAAK,UAAU;AAAA;AAE/B,uBAAe,KAAK,UAAU;AAAA,IACtC;AACA,UAAM,WAAW,GAAG,QAAQ,CAAC;AAC7B,QAAI,UAAU;AAEV,UAAI,CAAC,KAAK,QAAQ,KAAK,YAAU,kBAAkB,QAAQ,QAAQ,CAAC,GAAG;AACnE,wBAAgB,KAAK,QAAQ;AAAA,MACjC;AAAA,IACJ;AAAA,EACJ;AACA,SAAO,CAAC,gBAAgB,iBAAiB,eAAe;AAC5D;AAMA,SAAS,YAAY;AACjB,SAAO,OAAO,SAAS;AAC3B;AAKA,SAAS,SAAS,OAAO;AACrB,SAAO,OAAO,gBAAgB;AAClC;", "names": ["parse<PERSON><PERSON>y", "location", "stringifyQuery", "NavigationType", "NavigationDirection", "history", "replace", "NavigationFailureType", "re", "value", "route", "matchedRoute", "href", "failure", "resolve", "router"]}