/**
 * @license
 * Copyright 2017 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
import { Token } from '../../api/credentials';
import { DatabaseInfo } from '../../core/database_info';
import { Stream } from '../../remote/connection';
import { RestConnection } from '../../remote/rest_connection';
import { StringMap } from '../../util/types';
export declare class WebChannelConnection extends RestConnection {
    private readonly forceLongPolling;
    private readonly autoDetectLongPolling;
    private readonly useFetchStreams;
    private readonly longPollingOptions;
    constructor(info: DatabaseInfo);
    protected performRPCRequest<Req, Resp>(rpcName: string, url: string, headers: StringMap, body: Req): Promise<Resp>;
    openStream<Req, Resp>(rpcName: string, authToken: Token | null, appCheckToken: Token | null): Stream<Req, Resp>;
}
