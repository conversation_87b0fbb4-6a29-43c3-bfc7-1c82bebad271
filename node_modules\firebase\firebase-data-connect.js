import{_removeServiceInstance as e,getApp as t,_getProvider,_registerComponent as r,registerVersion as n,SDK_VERSION as i}from"https://www.gstatic.com/firebasejs/10.14.1/firebase-app.js";class FirebaseError extends Error{constructor(e,t,r){super(t),this.code=e,this.customData=r,this.name="FirebaseError",Object.setPrototypeOf(this,FirebaseError.prototype),Error.captureStackTrace&&Error.captureStackTrace(this,ErrorFactory.prototype.create)}}class ErrorFactory{constructor(e,t,r){this.service=e,this.serviceName=t,this.errors=r}create(e,...t){const r=t[0]||{},n=`${this.service}/${e}`,i=this.errors[e],s=i?function replaceTemplate(e,t){return e.replace(o,((e,r)=>{const n=t[r];return null!=n?String(n):`<${r}?>`}))}(i,r):"Error",a=`${this.serviceName}: ${s} (${n}).`;return new FirebaseError(n,a,r)}}const o=/\{\$([^}]+)}/g;class Component{constructor(e,t,r){this.name=e,this.instanceFactory=t,this.type=r,this.multipleInstances=!1,this.serviceProps={},this.instantiationMode="LAZY",this.onInstanceCreated=null}setInstantiationMode(e){return this.instantiationMode=e,this}setMultipleInstances(e){return this.multipleInstances=e,this}setServiceProps(e){return this.serviceProps=e,this}setInstanceCreatedCallback(e){return this.onInstanceCreated=e,this}}var s;!function(e){e[e.DEBUG=0]="DEBUG",e[e.VERBOSE=1]="VERBOSE",e[e.INFO=2]="INFO",e[e.WARN=3]="WARN",e[e.ERROR=4]="ERROR",e[e.SILENT=5]="SILENT"}(s||(s={}));const a={debug:s.DEBUG,verbose:s.VERBOSE,info:s.INFO,warn:s.WARN,error:s.ERROR,silent:s.SILENT},c=s.INFO,h={[s.DEBUG]:"log",[s.VERBOSE]:"log",[s.INFO]:"info",[s.WARN]:"warn",[s.ERROR]:"error"},defaultLogHandler=(e,t,...r)=>{if(t<e.logLevel)return;const n=(new Date).toISOString(),i=h[t];if(!i)throw new Error(`Attempted to log a message with an invalid logType (value: ${t})`);console[i](`[${n}]  ${e.name}:`,...r)};const l="@firebase/data-connect";let u="";class AppCheckTokenProvider{constructor(e,t){this.appName_=e,this.appCheckProvider=t,this.appCheck=null==t?void 0:t.getImmediate({optional:!0}),this.appCheck||null==t||t.get().then((e=>this.appCheck=e)).catch()}getToken(e){return this.appCheck?this.appCheck.getToken(e):new Promise(((t,r)=>{setTimeout((()=>{this.appCheck?this.getToken(e).then(t,r):t(null)}),0)}))}addTokenChangeListener(e){var t;null===(t=this.appCheckProvider)||void 0===t||t.get().then((t=>t.addTokenListener(e)))}}const p="other",d="already-initialized",g="invalid-argument",f="unauthorized";class DataConnectError extends FirebaseError{constructor(e,t){super(e,t),this.code=e,this.message=t,this.toString=()=>`${this.name}: [code=${this.code}]: ${this.message}`}}const _=new class Logger{constructor(e){this.name=e,this._logLevel=c,this._logHandler=defaultLogHandler,this._userLogHandler=null}get logLevel(){return this._logLevel}set logLevel(e){if(!(e in s))throw new TypeError(`Invalid value "${e}" assigned to \`logLevel\``);this._logLevel=e}setLogLevel(e){this._logLevel="string"==typeof e?a[e]:e}get logHandler(){return this._logHandler}set logHandler(e){if("function"!=typeof e)throw new TypeError("Value assigned to `logHandler` must be a function");this._logHandler=e}get userLogHandler(){return this._userLogHandler}set userLogHandler(e){this._userLogHandler=e}debug(...e){this._userLogHandler&&this._userLogHandler(this,s.DEBUG,...e),this._logHandler(this,s.DEBUG,...e)}log(...e){this._userLogHandler&&this._userLogHandler(this,s.VERBOSE,...e),this._logHandler(this,s.VERBOSE,...e)}info(...e){this._userLogHandler&&this._userLogHandler(this,s.INFO,...e),this._logHandler(this,s.INFO,...e)}warn(...e){this._userLogHandler&&this._userLogHandler(this,s.WARN,...e),this._logHandler(this,s.WARN,...e)}error(...e){this._userLogHandler&&this._userLogHandler(this,s.ERROR,...e),this._logHandler(this,s.ERROR,...e)}}("@firebase/data-connect");function setLogLevel(e){_.setLogLevel(e)}function logDebug(e){_.debug(`DataConnect (${u}): ${e}`)}function logError(e){_.error(`DataConnect (${u}): ${e}`)}class FirebaseAuthProvider{constructor(e,t,r){this._appName=e,this._options=t,this._authProvider=r,this._auth=r.getImmediate({optional:!0}),this._auth||r.onInit((e=>this._auth=e))}getToken(e){return this._auth?this._auth.getToken(e).catch((e=>e&&"auth/token-not-initialized"===e.code?(logDebug("Got auth/token-not-initialized error.  Treating as null token."),null):(logError("Error received when attempting to retrieve token: "+JSON.stringify(e)),Promise.reject(e)))):new Promise(((t,r)=>{setTimeout((()=>{this._auth?this.getToken(e).then(t,r):t(null)}),0)}))}addTokenChangeListener(e){var t;null===(t=this._auth)||void 0===t||t.addAuthTokenListener(e)}removeTokenChangeListener(e){this._authProvider.get().then((t=>t.removeAuthTokenListener(e))).catch((e=>logError(e)))}}const C="query",v="mutation",m="SERVER",E="CACHE";let b;function getRefSerializer(e,t,r){return function toJSON(){return{data:t,refInfo:{name:e.name,variables:e.variables,connectorConfig:Object.assign({projectId:e.dataConnect.app.options.projectId},e.dataConnect.getSettings())},fetchTime:Date.now().toLocaleString(),source:r}}}!function setEncoder(e){b=e}((e=>JSON.stringify(e)));class QueryManager{constructor(e){this.transport=e,this._queries=new Map}track(e,t,r){const n={name:e,variables:t,refType:"query"},i=b(n),o={ref:n,subscriptions:[],currentCache:r||null,lastError:null};return function setIfNotExists(e,t,r){e.has(t)||e.set(t,r)}(this._queries,i,o),this._queries.get(i)}addSubscription(e,t,r,n){const i=b({name:e.name,variables:e.variables,refType:"query"}),o=this._queries.get(i),s={userCallback:t,errCallback:r},unsubscribe=()=>{const e=this._queries.get(i);e.subscriptions=e.subscriptions.filter((e=>e!==s))};if(n&&o.currentCache!==n&&(logDebug("Initial cache found. Comparing dates."),(!o.currentCache||o.currentCache&&function compareDates(e,t){const r=new Date(e),n=new Date(t);return r.getTime()<n.getTime()}(o.currentCache.fetchTime,n.fetchTime))&&(o.currentCache=n)),null!==o.currentCache){t({data:o.currentCache.data,source:"CACHE",ref:e,toJSON:getRefSerializer(e,o.currentCache.data,"CACHE"),fetchTime:o.currentCache.fetchTime}),null!==o.lastError&&r&&r(void 0)}if(o.subscriptions.push({userCallback:t,errCallback:r,unsubscribe:unsubscribe}),!o.currentCache){logDebug(`No cache available for query ${e.name} with variables ${JSON.stringify(e.variables)}. Calling executeQuery.`);this.executeQuery(e).then(void 0,(e=>{}))}return unsubscribe}executeQuery(e){const t=b({name:e.name,variables:e.variables,refType:"query"}),r=this._queries.get(t);return this.transport.invokeQuery(e.name,e.variables).then((t=>{const n=(new Date).toString(),i=Object.assign(Object.assign({},t),{source:"SERVER",ref:e,toJSON:getRefSerializer(e,t.data,"SERVER"),fetchTime:n});return r.subscriptions.forEach((e=>{e.userCallback(i)})),r.currentCache={data:t.data,source:"CACHE",fetchTime:n},i}),(e=>{throw r.lastError=e,r.subscriptions.forEach((t=>{t.errCallback&&t.errCallback(e)})),e}))}enableEmulator(e,t){this.transport.useEmulator(e,t)}}function addToken(e,t){if(!t)return e;const r=new URL(e);return r.searchParams.append("key",t),r.toString()}let k=globalThis.fetch;function getGoogApiClientValue(e){let t="gl-js/ fire/"+u;return e&&(t+=" web/gen"),t}function dcFetch(e,t,{signal:r},n,i,o,s){if(!k)throw new DataConnectError(p,"No Fetch Implementation detected!");const a={"Content-Type":"application/json","X-Goog-Api-Client":getGoogApiClientValue(s)};i&&(a["X-Firebase-Auth-Token"]=i),n&&(a["x-firebase-gmpid"]=n),o&&(a["X-Firebase-AppCheck"]=o);const c=JSON.stringify(t);return logDebug(`Making request out to ${e} with body: ${c}`),k(e,{body:c,method:"POST",headers:a,signal:r}).catch((e=>{throw new DataConnectError(p,"Failed to fetch: "+JSON.stringify(e))})).then((async e=>{let t=null;try{t=await e.json()}catch(e){throw new DataConnectError(p,JSON.stringify(e))}const r=function getMessage(e){if("message"in e)return e.message;return JSON.stringify(e)}(t);if(e.status>=400){if(logError("Error while performing request: "+JSON.stringify(t)),401===e.status)throw new DataConnectError(f,r);throw new DataConnectError(p,r)}return t})).then((e=>{if(e.errors&&e.errors.length){const t=JSON.stringify(e.errors);throw logError("DataConnect error while performing request: "+t),new DataConnectError(p,t)}return e}))}class RESTTransport{constructor(e,t,r,n,i,o,s=!1){var a,c;this.apiKey=t,this.appId=r,this.authProvider=n,this.appCheckProvider=i,this._isUsingGen=s,this._host="",this._location="l",this._connectorName="",this._secure=!0,this._project="p",this._accessToken=null,this._appCheckToken=null,this._lastToken=null,this.invokeQuery=(e,t)=>{const r=new AbortController,n=this.withRetry((()=>dcFetch(addToken(`${this.endpointUrl}:executeQuery`,this.apiKey),{name:`projects/${this._project}/locations/${this._location}/services/${this._serviceName}/connectors/${this._connectorName}`,operationName:e,variables:t},r,this.appId,this._accessToken,this._appCheckToken,this._isUsingGen)));return{then:n.then.bind(n),catch:n.catch.bind(n)}},this.invokeMutation=(e,t)=>{const r=new AbortController,n=this.withRetry((()=>dcFetch(addToken(`${this.endpointUrl}:executeMutation`,this.apiKey),{name:`projects/${this._project}/locations/${this._location}/services/${this._serviceName}/connectors/${this._connectorName}`,operationName:e,variables:t},r,this.appId,this._accessToken,this._appCheckToken,this._isUsingGen)));return{then:n.then.bind(n),cancel:()=>r.abort()}},o&&("number"==typeof o.port&&(this._port=o.port),void 0!==o.sslEnabled&&(this._secure=o.sslEnabled),this._host=o.host);const{location:h,projectId:l,connector:u,service:p}=e;if(h&&(this._location=h),l&&(this._project=l),this._serviceName=p,!u)throw new DataConnectError(g,"Connector Name required!");this._connectorName=u,null===(a=this.authProvider)||void 0===a||a.addTokenChangeListener((e=>{logDebug(`New Token Available: ${e}`),this._accessToken=e})),null===(c=this.appCheckProvider)||void 0===c||c.addTokenChangeListener((e=>{const{token:t}=e;logDebug(`New App Check Token Available: ${t}`),this._appCheckToken=t}))}get endpointUrl(){return function urlBuilder(e,t){const{connector:r,location:n,projectId:i,service:o}=e,{host:s,sslEnabled:a,port:c}=t;let h=`${a?"https":"http"}://${s||"firebasedataconnect.googleapis.com"}`;if("number"==typeof c)h+=`:${c}`;else if(void 0!==c)throw logError("Port type is of an invalid type"),new DataConnectError(g,"Incorrect type for port passed in!");return`${h}/v1beta/projects/${i}/locations/${n}/services/${o}/connectors/${r}`}({connector:this._connectorName,location:this._location,projectId:this._project,service:this._serviceName},{host:this._host,sslEnabled:this._secure,port:this._port})}useEmulator(e,t,r){this._host=e,"number"==typeof t&&(this._port=t),void 0!==r&&(this._secure=r)}onTokenChanged(e){this._accessToken=e}async getWithAuth(e=!1){var t;let r=new Promise((e=>e(this._accessToken)));return this.appCheckProvider&&(this._appCheckToken=null===(t=await this.appCheckProvider.getToken())||void 0===t?void 0:t.token),r=this.authProvider?this.authProvider.getToken(e).then((e=>e?(this._accessToken=e.accessToken,this._accessToken):null)):new Promise((e=>e(""))),r}_setLastToken(e){this._lastToken=e}withRetry(e,t=!1){let r=!1;return this.getWithAuth(t).then((e=>(r=this._lastToken!==e,this._lastToken=e,e))).then(e).catch((n=>{if("code"in n&&n.code===f&&!t&&r)return logDebug("Retrying due to unauthorized"),this.withRetry(e,!0);throw n}))}}function mutationRef(e,t,r){e.setInitialized();return{dataConnect:e,name:t,refType:"mutation",variables:r}}class MutationManager{constructor(e){this._transport=e,this._inflight=[]}executeMutation(e){const t=this._transport.invokeMutation(e.name,e.variables),r=t.then((t=>Object.assign(Object.assign({},t),{source:"SERVER",ref:e,fetchTime:Date.now().toLocaleString()})));this._inflight.push(t);const removePromise=()=>this._inflight=this._inflight.filter((e=>e!==t));return t.then(removePromise,removePromise),r}}function executeMutation(e){return e.dataConnect._mutationManager.executeMutation(e)}function parseOptions(e){const[t,r]=e.split("://"),n="https"===t,[i,o]=r.split(":");return{host:i,port:Number(o),sslEnabled:n}}class DataConnect{constructor(e,t,r,n){if(this.app=e,this.dataConnectOptions=t,this._authProvider=r,this._appCheckProvider=n,this.isEmulator=!1,this._initialized=!1,this._isUsingGeneratedSdk=!1,"undefined"!=typeof process&&process.env){const e=process.env.FIREBASE_DATA_CONNECT_EMULATOR_HOST;e&&(logDebug("Found custom host. Using emulator"),this.isEmulator=!0,this._transportOptions=parseOptions(e))}}_useGeneratedSdk(){this._isUsingGeneratedSdk||(this._isUsingGeneratedSdk=!0)}_delete(){return e(this.app,"data-connect",JSON.stringify(this.getSettings())),Promise.resolve()}getSettings(){const e=JSON.parse(JSON.stringify(this.dataConnectOptions));return delete e.projectId,e}setInitialized(){this._initialized||(void 0===this._transportClass&&(logDebug("transportClass not provided. Defaulting to RESTTransport."),this._transportClass=RESTTransport),this._authProvider&&(this._authTokenProvider=new FirebaseAuthProvider(this.app.name,this.app.options,this._authProvider)),this._appCheckProvider&&(this._appCheckTokenProvider=new AppCheckTokenProvider(this.app.name,this._appCheckProvider)),this._initialized=!0,this._transport=new this._transportClass(this.dataConnectOptions,this.app.options.apiKey,this.app.options.appId,this._authTokenProvider,this._appCheckTokenProvider,void 0,this._isUsingGeneratedSdk),this._transportOptions&&this._transport.useEmulator(this._transportOptions.host,this._transportOptions.port,this._transportOptions.sslEnabled),this._queryManager=new QueryManager(this._transport),this._mutationManager=new MutationManager(this._transport))}enableEmulator(e){if(this._initialized)throw logError("enableEmulator called after initialization"),new DataConnectError(d,"DataConnect instance already initialized!");this._transportOptions=e,this.isEmulator=!0}}function connectDataConnectEmulator(e,t,r,n=!1){e.enableEmulator({host:t,port:r,sslEnabled:n})}function getDataConnect(e,r){let n,i;"location"in e?(i=e,n=t()):(i=r,n=e),n&&0!==Object.keys(n).length||(n=t());const o=_getProvider(n,"data-connect"),s=JSON.stringify(i);if(o.isInitialized(s)){const e=o.getImmediate({identifier:s}),t=o.getOptions(s);if(Object.keys(t).length>0)return logDebug("Re-using cached instance"),e}return validateDCOptions(i),logDebug("Creating new DataConnect instance"),o.initialize({instanceIdentifier:s,options:i})}function validateDCOptions(e){if(!e)throw new DataConnectError(g,"DC Option Required");return["connector","location","service"].forEach((t=>{if(null===e[t]||void 0===e[t])throw new DataConnectError(g,`${t} Required`)})),!0}function terminate(e){return e._delete()}function executeQuery(e){return e.dataConnect._queryManager.executeQuery(e)}function queryRef(e,t,r,n){return e.setInitialized(),e._queryManager.track(t,r,n),{dataConnect:e,refType:"query",name:t,variables:r}}function toQueryRef(e){const{refInfo:{name:t,variables:r,connectorConfig:n}}=e;return queryRef(getDataConnect(n),t,r)}function validateArgs(e,t,r,n){let i,o;if(t&&"enableEmulator"in t?(i=t,o=r):(i=getDataConnect(e),o=t),!i||!o&&n)throw new DataConnectError(g,"Variables required.");return{dc:i,vars:o}}function subscribe(e,t,r,n){let i,o,s;if("refInfo"in e){const t=e,{data:r,source:n,fetchTime:s}=t;o={data:r,source:n,fetchTime:s},i=toQueryRef(t)}else i=e;if("function"==typeof t?s=t:(s=t.onNext,r=t.onErr,t.onComplete),!s)throw new DataConnectError(g,"Must provide onNext");return i.dataConnect._queryManager.addSubscription(i,s,r,o)}!function registerDataConnect(e){!function setSDKVersion(e){u=e}(i),r(new Component("data-connect",((e,{instanceIdentifier:t,options:r})=>{const n=e.getProvider("app").getImmediate(),i=e.getProvider("auth-internal"),o=e.getProvider("app-check-internal");let s=r;if(t&&(s=JSON.parse(t)),!n.options.projectId)throw new DataConnectError(g,"Project ID must be provided. Did you pass in a proper projectId to initializeApp?");return new DataConnect(n,Object.assign(Object.assign({},s),{projectId:n.options.projectId}),i,o)}),"PUBLIC").setMultipleInstances(!0)),n(l,"0.1.0",e),n(l,"0.1.0","esm2017")}();export{DataConnect,v as MUTATION_STR,MutationManager,C as QUERY_STR,E as SOURCE_CACHE,m as SOURCE_SERVER,connectDataConnectEmulator,executeMutation,executeQuery,getDataConnect,mutationRef,parseOptions,queryRef,setLogLevel,subscribe,terminate,toQueryRef,validateArgs,validateDCOptions};

//# sourceMappingURL=firebase-data-connect.js.map
