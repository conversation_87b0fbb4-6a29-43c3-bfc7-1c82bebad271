import { db } from '../firebase/config'
import { collection, doc, setDoc, onSnapshot, updateDoc, deleteDoc, getDocs, getDoc } from 'firebase/firestore'
import 'webrtc-adapter'

// ICE servers configuration for STUN/TURN
const iceServers = {
  iceServers: [
    { urls: 'stun:stun.l.google.com:19302' },
    { urls: 'stun:stun1.l.google.com:19302' },
    { urls: 'stun:stun2.l.google.com:19302' },
  ]
}

// Class to handle WebRTC connections
export class WebRTCHandler {
  constructor(meetingId, userId, onRemoteStreamAdded, onRemoteStreamRemoved, onParticipantJoined, onParticipantLeft) {
    this.meetingId = meetingId
    this.userId = userId
    this.peerConnections = {}
    this.localStream = null
    this.onRemoteStreamAdded = onRemoteStreamAdded
    this.onRemoteStreamRemoved = onRemoteStreamRemoved
    this.onParticipantJoined = onParticipantJoined
    this.onParticipantLeft = onParticipantLeft
  }

  // Initialize local media stream
  async initLocalStream(audioEnabled = true, videoEnabled = true) {
    try {
      this.localStream = await navigator.mediaDevices.getUserMedia({
        audio: audioEnabled,
        video: videoEnabled
      })
      return this.localStream
    } catch (error) {
      console.error('Error accessing media devices:', error)
      throw error
    }
  }

  // Join a meeting
  async joinMeeting() {
    try {
      // Check if this is the meeting creator
      const meetingDoc = await getDoc(doc(db, 'meetings', this.meetingId))

      if (!meetingDoc.exists()) {
        throw new Error('Meeting not found')
      }

      const meetingData = meetingDoc.data()
      const isAdmin = meetingData.createdBy === this.userId

      console.log(`Joining meeting. isAdmin: ${isAdmin}, requiresApproval: ${meetingData.requiresApproval}`)

      if (isAdmin) {
        // If user is admin, join directly
        await this.addParticipantToMeeting()
        // Set up meeting listeners
        this.setupMeetingListeners()
      } else {
        // Check if the meeting requires approval
        const requiresApproval = meetingData.requiresApproval === true

        if (requiresApproval) {
          console.log('Meeting requires approval, sending join request')

          // Get user name from local storage
          const userName = localStorage.getItem('userName') || 'Anonymous'

          // Show waiting message to user
          alert(`Your request to join this meeting has been sent. Please wait for the host to approve.`)

          // Send a join request
          await this.sendJoinRequest()

          // Listen for approval
          this.joinRequestUnsubscribe = onSnapshot(
            doc(db, 'meetings', this.meetingId, 'participants', this.userId),
            (doc) => {
              if (doc.exists()) {
                // Request was approved, join the meeting
                console.log('Join request approved, joining meeting')
                alert('Your request to join has been approved!')

                if (this.joinRequestUnsubscribe) {
                  this.joinRequestUnsubscribe()
                }

                // Set up meeting listeners
                this.setupMeetingListeners()
              }
            }
          )

          // Return early, we'll join when approved
          return
        } else {
          // No approval required, join directly
          console.log('No approval required, joining directly')
          await this.addParticipantToMeeting()
          // Set up meeting listeners
          this.setupMeetingListeners()
        }
      }
    } catch (error) {
      console.error('Error joining meeting:', error)
      throw error
    }
  }

  // Add participant to the meeting
  async addParticipantToMeeting() {
    // Create a reference to the meeting in Firestore
    const meetingRef = collection(db, 'meetings', this.meetingId, 'participants')

    // Add the current user to the meeting
    await setDoc(doc(meetingRef, this.userId), {
      joined: new Date().toISOString(),
      userId: this.userId,
      audioMuted: false,
      videoMuted: false
    })
  }

  // Send a join request
  async sendJoinRequest() {
    try {
      // Get user name from local storage or use a default
      const userName = localStorage.getItem('userName') || 'Anonymous'

      // Create a join request
      await setDoc(doc(db, 'meetings', this.meetingId, 'joinRequests', this.userId), {
        userId: this.userId,
        userName: userName,
        timestamp: new Date().toISOString(),
        status: 'pending'
      })

      console.log(`Join request sent for user ${userName}, waiting for approval`)
    } catch (error) {
      console.error('Error sending join request:', error)
      throw error
    }
  }

  // Set up meeting listeners
  setupMeetingListeners() {
    // Listen for new participants
    const participantsRef = collection(db, 'meetings', this.meetingId, 'participants')
    this.participantsUnsubscribe = onSnapshot(participantsRef, (snapshot) => {
      snapshot.docChanges().forEach((change) => {
        const participant = change.doc.data()
        const participantId = change.doc.id

        // Skip if it's the current user
        if (participantId === this.userId) return

        if (change.type === 'added') {
          // New participant joined
          this.onParticipantJoined(participant)
          this.createPeerConnection(participantId)
          this.createOffer(participantId)
        }

        if (change.type === 'removed') {
          // Participant left
          this.onParticipantLeft(participantId)
          this.closePeerConnection(participantId)
        }

        if (change.type === 'modified') {
          // Participant updated (e.g., muted)
          // You can handle updates to participant state here
        }
      })
    })

    // Listen for WebRTC signaling
    this.signalingUnsubscribe = onSnapshot(
      collection(db, 'meetings', this.meetingId, 'signaling'),
      (snapshot) => {
        snapshot.docChanges().forEach((change) => {
          const signal = change.doc.data()

          // Skip signals created by the current user
          if (signal.from === this.userId) return

          // Handle different signal types
          if (signal.type === 'offer' && signal.to === this.userId) {
            this.handleOffer(signal)
          } else if (signal.type === 'answer' && signal.to === this.userId) {
            this.handleAnswer(signal)
          } else if (signal.type === 'ice-candidate' && signal.to === this.userId) {
            this.handleIceCandidate(signal)
          }
        })
      }
    )
  }

  // Create a peer connection for a participant
  createPeerConnection(participantId) {
    // Create a new RTCPeerConnection
    const peerConnection = new RTCPeerConnection(iceServers)

    // Add local stream tracks to the connection
    if (this.localStream) {
      this.localStream.getTracks().forEach(track => {
        peerConnection.addTrack(track, this.localStream)
      })
    }

    // Handle ICE candidates
    peerConnection.onicecandidate = (event) => {
      if (event.candidate) {
        this.sendSignal({
          type: 'ice-candidate',
          from: this.userId,
          to: participantId,
          candidate: event.candidate.toJSON()
        })
      }
    }

    // Handle connection state changes
    peerConnection.onconnectionstatechange = (event) => {
      console.log(`Connection state change: ${peerConnection.connectionState}`)

      // If connection failed, try to reconnect
      if (peerConnection.connectionState === 'failed' || peerConnection.connectionState === 'disconnected') {
        console.log(`Connection to ${participantId} failed or disconnected. Attempting to reconnect...`)
        this.closePeerConnection(participantId)
        setTimeout(() => {
          this.createPeerConnection(participantId)
          this.createOffer(participantId)
        }, 2000)
      }
    }

    // Handle ICE connection state changes
    peerConnection.oniceconnectionstatechange = (event) => {
      console.log(`ICE connection state change: ${peerConnection.iceConnectionState}`)
    }

    // Handle signaling state changes
    peerConnection.onsignalingstatechange = (event) => {
      console.log(`Signaling state change: ${peerConnection.signalingState}`)
    }

    // Handle remote stream
    peerConnection.ontrack = (event) => {
      if (event.streams && event.streams[0]) {
        this.onRemoteStreamAdded(participantId, event.streams[0])
      }
    }

    // Store the peer connection
    this.peerConnections[participantId] = peerConnection

    return peerConnection
  }

  // Create and send an offer to a participant
  async createOffer(participantId) {
    try {
      const peerConnection = this.peerConnections[participantId]

      // Check if the connection is in a valid state to create an offer
      if (!peerConnection || peerConnection.signalingState === 'closed') {
        console.log(`Cannot create offer: peer connection is closed or doesn't exist`)
        return
      }

      // Don't create an offer if we're already in the process of negotiating
      if (peerConnection.signalingState !== 'stable') {
        console.log(`Cannot create offer: signaling state is ${peerConnection.signalingState}`)

        // If we're in have-remote-offer state, we need to wait for the process to complete
        if (peerConnection.signalingState === 'have-remote-offer') {
          console.log('Already have a remote offer, waiting for stable state')

          // Create a promise that resolves when the signaling state becomes stable
          await new Promise(resolve => {
            const checkState = () => {
              if (peerConnection.signalingState === 'stable') {
                peerConnection.removeEventListener('signalingstatechange', checkState)
                resolve()
              }
            }

            peerConnection.addEventListener('signalingstatechange', checkState)

            // Add a timeout in case it never becomes stable
            setTimeout(resolve, 5000)
          })
        } else {
          return
        }
      }

      // Double-check state again after waiting
      if (peerConnection.signalingState !== 'stable') {
        console.log(`Still cannot create offer: signaling state is ${peerConnection.signalingState}`)
        return
      }

      const offer = await peerConnection.createOffer({
        offerToReceiveAudio: true,
        offerToReceiveVideo: true
      })

      await peerConnection.setLocalDescription(offer)

      // Wait for ICE gathering to complete
      await this.waitForIceGathering(peerConnection)

      this.sendSignal({
        type: 'offer',
        from: this.userId,
        to: participantId,
        sdp: peerConnection.localDescription.toJSON()
      })
    } catch (error) {
      console.error('Error creating offer:', error)
    }
  }

  // Handle an offer from a participant
  async handleOffer(signal) {
    try {
      const participantId = signal.from

      // Create peer connection if it doesn't exist
      if (!this.peerConnections[participantId]) {
        this.createPeerConnection(participantId)
      }

      const peerConnection = this.peerConnections[participantId]

      // Check if the connection is in a valid state to handle an offer
      if (peerConnection.signalingState === 'closed') {
        console.log(`Cannot handle offer: peer connection is closed`)
        return
      }

      // If we're already negotiating, we need to determine who has priority
      if (peerConnection.signalingState !== 'stable') {
        console.log(`Signaling state is ${peerConnection.signalingState}, applying rollback`)
        await peerConnection.setLocalDescription({type: "rollback"})
      }

      // Set remote description
      await peerConnection.setRemoteDescription(new RTCSessionDescription(signal.sdp))

      // Create answer
      const answer = await peerConnection.createAnswer()
      await peerConnection.setLocalDescription(answer)

      // Wait for ICE gathering to complete
      await this.waitForIceGathering(peerConnection)

      // Send answer
      this.sendSignal({
        type: 'answer',
        from: this.userId,
        to: participantId,
        sdp: peerConnection.localDescription.toJSON()
      })
    } catch (error) {
      console.error('Error handling offer:', error)
    }
  }

  // Handle an answer from a participant
  async handleAnswer(signal) {
    try {
      const participantId = signal.from
      const peerConnection = this.peerConnections[participantId]

      if (!peerConnection) {
        console.log(`Cannot handle answer: peer connection doesn't exist`)
        return
      }

      // Check if the connection is in a valid state to handle an answer
      if (peerConnection.signalingState === 'closed') {
        console.log(`Cannot handle answer: peer connection is closed`)
        return
      }

      // Only set remote description if we're in the right state
      if (peerConnection.signalingState === 'have-local-offer') {
        await peerConnection.setRemoteDescription(new RTCSessionDescription(signal.sdp))
      } else {
        console.log(`Cannot handle answer: signaling state is ${peerConnection.signalingState}`)
      }
    } catch (error) {
      console.error('Error handling answer:', error)
    }
  }

  // Handle an ICE candidate from a participant
  async handleIceCandidate(signal) {
    try {
      const participantId = signal.from
      const peerConnection = this.peerConnections[participantId]

      if (!peerConnection) {
        console.log(`Cannot handle ICE candidate: peer connection doesn't exist`)
        return
      }

      // Check if the connection is in a valid state to add ICE candidates
      if (peerConnection.signalingState === 'closed') {
        console.log(`Cannot handle ICE candidate: peer connection is closed`)
        return
      }

      // Only add ICE candidate if we have a remote description
      if (peerConnection.remoteDescription) {
        await peerConnection.addIceCandidate(new RTCIceCandidate(signal.candidate))
      } else {
        console.log(`Cannot add ICE candidate: remote description is null`)
      }
    } catch (error) {
      console.error('Error handling ICE candidate:', error)
    }
  }

  // Wait for ICE gathering to complete
  async waitForIceGathering(peerConnection) {
    if (peerConnection.iceGatheringState === 'complete') {
      return
    }

    return new Promise(resolve => {
      const checkState = () => {
        if (peerConnection.iceGatheringState === 'complete') {
          peerConnection.removeEventListener('icegatheringstatechange', checkState)
          resolve()
        }
      }

      peerConnection.addEventListener('icegatheringstatechange', checkState)

      // Add a timeout in case ICE gathering takes too long
      setTimeout(resolve, 1000)
    })
  }

  // Send a signaling message
  async sendSignal(signal) {
    try {
      const signalingRef = collection(db, 'meetings', this.meetingId, 'signaling')
      await setDoc(doc(signalingRef), {
        ...signal,
        timestamp: new Date().toISOString()
      })
    } catch (error) {
      console.error('Error sending signal:', error)
    }
  }

  // Close a peer connection
  closePeerConnection(participantId) {
    const peerConnection = this.peerConnections[participantId]

    if (peerConnection) {
      peerConnection.close()
      delete this.peerConnections[participantId]
      this.onRemoteStreamRemoved(participantId)
    }
  }

  // Leave the meeting
  async leaveMeeting() {
    // Close all peer connections
    Object.keys(this.peerConnections).forEach(participantId => {
      this.closePeerConnection(participantId)
    })

    // Stop local stream tracks
    if (this.localStream) {
      this.localStream.getTracks().forEach(track => track.stop())
    }

    // Remove the user from the meeting
    try {
      await deleteDoc(doc(db, 'meetings', this.meetingId, 'participants', this.userId))

      // Check if this user is the last one in the meeting
      this.checkAndEndMeetingIfEmpty()
    } catch (error) {
      console.error('Error leaving meeting:', error)
    }

    // Unsubscribe from Firestore listeners
    if (this.participantsUnsubscribe) {
      this.participantsUnsubscribe()
    }

    if (this.signalingUnsubscribe) {
      this.signalingUnsubscribe()
    }

    if (this.joinRequestUnsubscribe) {
      this.joinRequestUnsubscribe()
    }
  }

  // Check if meeting is empty and end it if it is
  async checkAndEndMeetingIfEmpty() {
    try {
      const participantsRef = collection(db, 'meetings', this.meetingId, 'participants')
      const snapshot = await getDocs(participantsRef)

      // If there are no participants left, end the meeting
      if (snapshot.empty) {
        console.log('No participants left in the meeting. Ending the meeting.')
        await setDoc(doc(db, 'meetings', this.meetingId), {
          active: false,
          endedAt: new Date().toISOString()
        }, { merge: true })
      }
    } catch (error) {
      console.error('Error checking if meeting is empty:', error)
    }
  }

  // Remove a participant from the meeting (admin only)
  async removeParticipant(participantId) {
    try {
      // Close the peer connection
      this.closePeerConnection(participantId)

      // Remove the participant from the meeting
      await deleteDoc(doc(db, 'meetings', this.meetingId, 'participants', participantId))

      return true
    } catch (error) {
      console.error('Error removing participant:', error)
      return false
    }
  }

  // Toggle screen sharing
  async toggleScreenSharing(enabled) {
    if (enabled) {
      try {
        const screenStream = await navigator.mediaDevices.getDisplayMedia({
          video: true
        })

        // Replace video track in all peer connections
        const videoTrack = screenStream.getVideoTracks()[0]

        Object.values(this.peerConnections).forEach(peerConnection => {
          const sender = peerConnection.getSenders().find(s =>
            s.track && s.track.kind === 'video'
          )

          if (sender) {
            sender.replaceTrack(videoTrack)
          }
        })

        // Store the screen sharing track
        this.screenTrack = videoTrack

        // Listen for the end of screen sharing
        this.screenTrack.onended = () => {
          this.toggleScreenSharing(false)
        }

        return screenStream
      } catch (error) {
        console.error('Error starting screen sharing:', error)
        throw error
      }
    } else {
      // Revert to camera video
      if (this.localStream) {
        const videoTrack = this.localStream.getVideoTracks()[0]

        if (videoTrack) {
          Object.values(this.peerConnections).forEach(peerConnection => {
            const sender = peerConnection.getSenders().find(s =>
              s.track && s.track.kind === 'video'
            )

            if (sender) {
              sender.replaceTrack(videoTrack)
            }
          })
        }
      }

      // Stop screen sharing track
      if (this.screenTrack) {
        this.screenTrack.stop()
        this.screenTrack = null
      }
    }
  }

  // Update video track in all peer connections
  async updateVideoTrack(newVideoTrack) {
    try {
      // Update all peer connections with the new video track
      Object.values(this.peerConnections).forEach(peerConnection => {
        const sender = peerConnection.getSenders().find(s =>
          s.track && s.track.kind === 'video'
        )

        if (sender) {
          sender.replaceTrack(newVideoTrack)
        }
      })
    } catch (error) {
      console.error('Error updating video track:', error)
      throw error
    }
  }
}
