{"version": 3, "file": "firebase-storage-compat.js", "sources": ["../storage/src/implementation/error.ts", "../storage/src/implementation/connection.ts", "../storage-compat/src/index.ts", "../util/src/crypt.ts", "../util/src/errors.ts", "../util/src/compat.ts", "../component/src/component.ts", "../storage/src/implementation/constants.ts", "../storage/src/implementation/location.ts", "../storage/src/implementation/failrequest.ts", "../storage/src/implementation/type.ts", "../storage/src/implementation/url.ts", "../storage/src/implementation/utils.ts", "../storage/src/implementation/request.ts", "../storage/src/implementation/backoff.ts", "../storage/src/implementation/fs.ts", "../storage/src/platform/browser/base64.ts", "../storage/src/implementation/string.ts", "../storage/src/implementation/blob.ts", "../storage/src/implementation/json.ts", "../storage/src/implementation/path.ts", "../storage/src/implementation/metadata.ts", "../storage/src/implementation/list.ts", "../storage/src/implementation/requestinfo.ts", "../storage/src/implementation/requests.ts", "../storage/src/implementation/taskenums.ts", "../storage/src/implementation/observer.ts", "../storage/src/implementation/async.ts", "../storage/src/platform/browser/connection.ts", "../storage/src/task.ts", "../storage/src/reference.ts", "../storage/src/service.ts", "../util/src/emulator.ts", "../storage/src/api.ts", "../storage/src/index.ts", "../storage/src/constants.ts", "../storage-compat/src/tasksnapshot.ts", "../storage-compat/src/task.ts", "../storage-compat/src/list.ts", "../storage-compat/src/reference.ts", "../storage-compat/src/service.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { FirebaseError } from '@firebase/util';\n\nimport { CONFIG_STORAGE_BUCKET_KEY } from './constants';\n\n/**\n * An error returned by the Firebase Storage SDK.\n * @public\n */\nexport class StorageError extends FirebaseError {\n  private readonly _baseMessage: string;\n  /**\n   * Stores custom error data unique to the `StorageError`.\n   */\n  customData: { serverResponse: string | null } = { serverResponse: null };\n\n  /**\n   * @param code - A `StorageErrorCode` string to be prefixed with 'storage/' and\n   *  added to the end of the message.\n   * @param message  - Error message.\n   * @param status_ - Corresponding HTTP Status Code\n   */\n  constructor(code: StorageErrorCode, message: string, private status_ = 0) {\n    super(\n      prependCode(code),\n      `Firebase Storage: ${message} (${prependCode(code)})`\n    );\n    this._baseMessage = this.message;\n    // Without this, `instanceof StorageError`, in tests for example,\n    // returns false.\n    Object.setPrototypeOf(this, StorageError.prototype);\n  }\n\n  get status(): number {\n    return this.status_;\n  }\n\n  set status(status: number) {\n    this.status_ = status;\n  }\n\n  /**\n   * Compares a `StorageErrorCode` against this error's code, filtering out the prefix.\n   */\n  _codeEquals(code: StorageErrorCode): boolean {\n    return prependCode(code) === this.code;\n  }\n\n  /**\n   * Optional response message that was added by the server.\n   */\n  get serverResponse(): null | string {\n    return this.customData.serverResponse;\n  }\n\n  set serverResponse(serverResponse: string | null) {\n    this.customData.serverResponse = serverResponse;\n    if (this.customData.serverResponse) {\n      this.message = `${this._baseMessage}\\n${this.customData.serverResponse}`;\n    } else {\n      this.message = this._baseMessage;\n    }\n  }\n}\n\nexport const errors = {};\n\n/**\n * @public\n * Error codes that can be attached to `StorageError` objects.\n */\nexport enum StorageErrorCode {\n  // Shared between all platforms\n  UNKNOWN = 'unknown',\n  OBJECT_NOT_FOUND = 'object-not-found',\n  BUCKET_NOT_FOUND = 'bucket-not-found',\n  PROJECT_NOT_FOUND = 'project-not-found',\n  QUOTA_EXCEEDED = 'quota-exceeded',\n  UNAUTHENTICATED = 'unauthenticated',\n  UNAUTHORIZED = 'unauthorized',\n  UNAUTHORIZED_APP = 'unauthorized-app',\n  RETRY_LIMIT_EXCEEDED = 'retry-limit-exceeded',\n  INVALID_CHECKSUM = 'invalid-checksum',\n  CANCELED = 'canceled',\n  // JS specific\n  INVALID_EVENT_NAME = 'invalid-event-name',\n  INVALID_URL = 'invalid-url',\n  INVALID_DEFAULT_BUCKET = 'invalid-default-bucket',\n  NO_DEFAULT_BUCKET = 'no-default-bucket',\n  CANNOT_SLICE_BLOB = 'cannot-slice-blob',\n  SERVER_FILE_WRONG_SIZE = 'server-file-wrong-size',\n  NO_DOWNLOAD_URL = 'no-download-url',\n  INVALID_ARGUMENT = 'invalid-argument',\n  INVALID_ARGUMENT_COUNT = 'invalid-argument-count',\n  APP_DELETED = 'app-deleted',\n  INVALID_ROOT_OPERATION = 'invalid-root-operation',\n  INVALID_FORMAT = 'invalid-format',\n  INTERNAL_ERROR = 'internal-error',\n  UNSUPPORTED_ENVIRONMENT = 'unsupported-environment'\n}\n\nexport function prependCode(code: StorageErrorCode): string {\n  return 'storage/' + code;\n}\n\nexport function unknown(): StorageError {\n  const message =\n    'An unknown error occurred, please check the error payload for ' +\n    'server response.';\n  return new StorageError(StorageErrorCode.UNKNOWN, message);\n}\n\nexport function objectNotFound(path: string): StorageError {\n  return new StorageError(\n    StorageErrorCode.OBJECT_NOT_FOUND,\n    \"Object '\" + path + \"' does not exist.\"\n  );\n}\n\nexport function bucketNotFound(bucket: string): StorageError {\n  return new StorageError(\n    StorageErrorCode.BUCKET_NOT_FOUND,\n    \"Bucket '\" + bucket + \"' does not exist.\"\n  );\n}\n\nexport function projectNotFound(project: string): StorageError {\n  return new StorageError(\n    StorageErrorCode.PROJECT_NOT_FOUND,\n    \"Project '\" + project + \"' does not exist.\"\n  );\n}\n\nexport function quotaExceeded(bucket: string): StorageError {\n  return new StorageError(\n    StorageErrorCode.QUOTA_EXCEEDED,\n    \"Quota for bucket '\" +\n      bucket +\n      \"' exceeded, please view quota on \" +\n      'https://firebase.google.com/pricing/.'\n  );\n}\n\nexport function unauthenticated(): StorageError {\n  const message =\n    'User is not authenticated, please authenticate using Firebase ' +\n    'Authentication and try again.';\n  return new StorageError(StorageErrorCode.UNAUTHENTICATED, message);\n}\n\nexport function unauthorizedApp(): StorageError {\n  return new StorageError(\n    StorageErrorCode.UNAUTHORIZED_APP,\n    'This app does not have permission to access Firebase Storage on this project.'\n  );\n}\n\nexport function unauthorized(path: string): StorageError {\n  return new StorageError(\n    StorageErrorCode.UNAUTHORIZED,\n    \"User does not have permission to access '\" + path + \"'.\"\n  );\n}\n\nexport function retryLimitExceeded(): StorageError {\n  return new StorageError(\n    StorageErrorCode.RETRY_LIMIT_EXCEEDED,\n    'Max retry time for operation exceeded, please try again.'\n  );\n}\n\nexport function invalidChecksum(\n  path: string,\n  checksum: string,\n  calculated: string\n): StorageError {\n  return new StorageError(\n    StorageErrorCode.INVALID_CHECKSUM,\n    \"Uploaded/downloaded object '\" +\n      path +\n      \"' has checksum '\" +\n      checksum +\n      \"' which does not match '\" +\n      calculated +\n      \"'. Please retry the upload/download.\"\n  );\n}\n\nexport function canceled(): StorageError {\n  return new StorageError(\n    StorageErrorCode.CANCELED,\n    'User canceled the upload/download.'\n  );\n}\n\nexport function invalidEventName(name: string): StorageError {\n  return new StorageError(\n    StorageErrorCode.INVALID_EVENT_NAME,\n    \"Invalid event name '\" + name + \"'.\"\n  );\n}\n\nexport function invalidUrl(url: string): StorageError {\n  return new StorageError(\n    StorageErrorCode.INVALID_URL,\n    \"Invalid URL '\" + url + \"'.\"\n  );\n}\n\nexport function invalidDefaultBucket(bucket: string): StorageError {\n  return new StorageError(\n    StorageErrorCode.INVALID_DEFAULT_BUCKET,\n    \"Invalid default bucket '\" + bucket + \"'.\"\n  );\n}\n\nexport function noDefaultBucket(): StorageError {\n  return new StorageError(\n    StorageErrorCode.NO_DEFAULT_BUCKET,\n    'No default bucket ' +\n      \"found. Did you set the '\" +\n      CONFIG_STORAGE_BUCKET_KEY +\n      \"' property when initializing the app?\"\n  );\n}\n\nexport function cannotSliceBlob(): StorageError {\n  return new StorageError(\n    StorageErrorCode.CANNOT_SLICE_BLOB,\n    'Cannot slice blob for upload. Please retry the upload.'\n  );\n}\n\nexport function serverFileWrongSize(): StorageError {\n  return new StorageError(\n    StorageErrorCode.SERVER_FILE_WRONG_SIZE,\n    'Server recorded incorrect upload file size, please retry the upload.'\n  );\n}\n\nexport function noDownloadURL(): StorageError {\n  return new StorageError(\n    StorageErrorCode.NO_DOWNLOAD_URL,\n    'The given file does not have any download URLs.'\n  );\n}\n\nexport function missingPolyFill(polyFill: string): StorageError {\n  return new StorageError(\n    StorageErrorCode.UNSUPPORTED_ENVIRONMENT,\n    `${polyFill} is missing. Make sure to install the required polyfills. See https://firebase.google.com/docs/web/environments-js-sdk#polyfills for more information.`\n  );\n}\n\n/**\n * @internal\n */\nexport function invalidArgument(message: string): StorageError {\n  return new StorageError(StorageErrorCode.INVALID_ARGUMENT, message);\n}\n\nexport function invalidArgumentCount(\n  argMin: number,\n  argMax: number,\n  fnName: string,\n  real: number\n): StorageError {\n  let countPart;\n  let plural;\n  if (argMin === argMax) {\n    countPart = argMin;\n    plural = argMin === 1 ? 'argument' : 'arguments';\n  } else {\n    countPart = 'between ' + argMin + ' and ' + argMax;\n    plural = 'arguments';\n  }\n  return new StorageError(\n    StorageErrorCode.INVALID_ARGUMENT_COUNT,\n    'Invalid argument count in `' +\n      fnName +\n      '`: Expected ' +\n      countPart +\n      ' ' +\n      plural +\n      ', received ' +\n      real +\n      '.'\n  );\n}\n\nexport function appDeleted(): StorageError {\n  return new StorageError(\n    StorageErrorCode.APP_DELETED,\n    'The Firebase app was deleted.'\n  );\n}\n\n/**\n * @param name - The name of the operation that was invalid.\n *\n * @internal\n */\nexport function invalidRootOperation(name: string): StorageError {\n  return new StorageError(\n    StorageErrorCode.INVALID_ROOT_OPERATION,\n    \"The operation '\" +\n      name +\n      \"' cannot be performed on a root reference, create a non-root \" +\n      \"reference using child, such as .child('file.png').\"\n  );\n}\n\n/**\n * @param format - The format that was not valid.\n * @param message - A message describing the format violation.\n */\nexport function invalidFormat(format: string, message: string): StorageError {\n  return new StorageError(\n    StorageErrorCode.INVALID_FORMAT,\n    \"String does not match format '\" + format + \"': \" + message\n  );\n}\n\n/**\n * @param message - A message describing the internal error.\n */\nexport function unsupportedEnvironment(message: string): StorageError {\n  throw new StorageError(StorageErrorCode.UNSUPPORTED_ENVIRONMENT, message);\n}\n\n/**\n * @param message - A message describing the internal error.\n */\nexport function internalError(message: string): StorageError {\n  throw new StorageError(\n    StorageErrorCode.INTERNAL_ERROR,\n    'Internal error: ' + message\n  );\n}\n", "/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/** Network headers */\nexport type Headers = Record<string, string>;\n\n/** Response type exposed by the networking APIs. */\nexport type ConnectionType =\n  | string\n  | ArrayBuffer\n  | Blob\n  | ReadableStream<Uint8Array>;\n\n/**\n * A lightweight wrapper around XMLHttpRequest with a\n * goog.net.XhrIo-like interface.\n *\n * You can create a new connection by invoking `newTextConnection()`,\n * `newBytesConnection()` or `newStreamConnection()`.\n */\nexport interface Connection<T extends ConnectionType> {\n  /**\n   * Sends a request to the provided URL.\n   *\n   * This method never rejects its promise. In case of encountering an error,\n   * it sets an error code internally which can be accessed by calling\n   * getErrorCode() by callers.\n   */\n  send(\n    url: string,\n    method: string,\n    body?: ArrayBufferView | Blob | string | null,\n    headers?: Headers\n  ): Promise<void>;\n\n  getErrorCode(): ErrorCode;\n\n  getStatus(): number;\n\n  getResponse(): T;\n\n  getErrorText(): string;\n\n  /**\n   * Abort the request.\n   */\n  abort(): void;\n\n  getResponseHeader(header: string): string | null;\n\n  addUploadProgressListener(listener: (p1: ProgressEvent) => void): void;\n\n  removeUploadProgressListener(listener: (p1: ProgressEvent) => void): void;\n}\n\n/**\n * Error codes for requests made by the XhrIo wrapper.\n */\nexport enum ErrorCode {\n  NO_ERROR = 0,\n  NETWORK_ERROR = 1,\n  ABORT = 2\n}\n", "/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n// eslint-disable-next-line import/no-extraneous-dependencies\nimport firebase from '@firebase/app-compat';\nimport { _FirebaseNamespace } from '@firebase/app-types/private';\nimport {\n  StringFormat,\n  _TaskEvent as TaskEvent,\n  _TaskState as TaskState\n} from '@firebase/storage';\n\nimport { ReferenceCompat } from './reference';\nimport { StorageServiceCompat } from './service';\nimport * as types from '@firebase/storage-types';\nimport {\n  Component,\n  ComponentType,\n  ComponentContainer,\n  InstanceFactoryOptions\n} from '@firebase/component';\n\nimport { name, version } from '../package.json';\n\n/**\n * Type constant for Firebase Storage.\n */\nconst STORAGE_TYPE = 'storage-compat';\n\nfunction factory(\n  container: ComponentContainer,\n  { instanceIdentifier: url }: InstanceFactoryOptions\n): types.FirebaseStorage {\n  // Dependencies\n  const app = container.getProvider('app-compat').getImmediate();\n  const storageExp = container\n    .getProvider('storage')\n    .getImmediate({ identifier: url });\n\n  const storageServiceCompat: StorageServiceCompat = new StorageServiceCompat(\n    app,\n    storageExp\n  );\n  return storageServiceCompat;\n}\n\nexport function registerStorage(instance: _FirebaseNamespace): void {\n  const namespaceExports = {\n    // no-inline\n    TaskState,\n    TaskEvent,\n    StringFormat,\n    Storage: StorageServiceCompat,\n    Reference: ReferenceCompat\n  };\n  instance.INTERNAL.registerComponent(\n    new Component(STORAGE_TYPE, factory, ComponentType.PUBLIC)\n      .setServiceProps(namespaceExports)\n      .setMultipleInstances(true)\n  );\n\n  instance.registerVersion(name, version);\n}\n\nregisterStorage(firebase as unknown as _FirebaseNamespace);\n\n/**\n * Define extension behavior for `registerStorage`\n */\ndeclare module '@firebase/app-compat' {\n  interface FirebaseNamespace {\n    storage?: {\n      (app?: FirebaseApp, url?: string): types.FirebaseStorage;\n      Storage: typeof types.FirebaseStorage;\n\n      StringFormat: {\n        BASE64: types.StringFormat;\n        BASE64URL: types.StringFormat;\n        DATA_URL: types.StringFormat;\n        RAW: types.StringFormat;\n      };\n      TaskEvent: {\n        STATE_CHANGED: types.TaskEvent;\n      };\n      TaskState: {\n        CANCELED: types.TaskState;\n        ERROR: types.TaskState;\n        PAUSED: types.TaskState;\n        RUNNING: types.TaskState;\n        SUCCESS: types.TaskState;\n      };\n    };\n  }\n  interface FirebaseApp {\n    storage?(storageBucket?: string): types.FirebaseStorage;\n  }\n}\n", "/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nconst stringToByteArray = function (str: string): number[] {\n  // TODO(user): Use native implementations if/when available\n  const out: number[] = [];\n  let p = 0;\n  for (let i = 0; i < str.length; i++) {\n    let c = str.charCodeAt(i);\n    if (c < 128) {\n      out[p++] = c;\n    } else if (c < 2048) {\n      out[p++] = (c >> 6) | 192;\n      out[p++] = (c & 63) | 128;\n    } else if (\n      (c & 0xfc00) === 0xd800 &&\n      i + 1 < str.length &&\n      (str.charCodeAt(i + 1) & 0xfc00) === 0xdc00\n    ) {\n      // Surrogate Pair\n      c = 0x10000 + ((c & 0x03ff) << 10) + (str.charCodeAt(++i) & 0x03ff);\n      out[p++] = (c >> 18) | 240;\n      out[p++] = ((c >> 12) & 63) | 128;\n      out[p++] = ((c >> 6) & 63) | 128;\n      out[p++] = (c & 63) | 128;\n    } else {\n      out[p++] = (c >> 12) | 224;\n      out[p++] = ((c >> 6) & 63) | 128;\n      out[p++] = (c & 63) | 128;\n    }\n  }\n  return out;\n};\n\n/**\n * Turns an array of numbers into the string given by the concatenation of the\n * characters to which the numbers correspond.\n * @param bytes Array of numbers representing characters.\n * @return Stringification of the array.\n */\nconst byteArrayToString = function (bytes: number[]): string {\n  // TODO(user): Use native implementations if/when available\n  const out: string[] = [];\n  let pos = 0,\n    c = 0;\n  while (pos < bytes.length) {\n    const c1 = bytes[pos++];\n    if (c1 < 128) {\n      out[c++] = String.fromCharCode(c1);\n    } else if (c1 > 191 && c1 < 224) {\n      const c2 = bytes[pos++];\n      out[c++] = String.fromCharCode(((c1 & 31) << 6) | (c2 & 63));\n    } else if (c1 > 239 && c1 < 365) {\n      // Surrogate Pair\n      const c2 = bytes[pos++];\n      const c3 = bytes[pos++];\n      const c4 = bytes[pos++];\n      const u =\n        (((c1 & 7) << 18) | ((c2 & 63) << 12) | ((c3 & 63) << 6) | (c4 & 63)) -\n        0x10000;\n      out[c++] = String.fromCharCode(0xd800 + (u >> 10));\n      out[c++] = String.fromCharCode(0xdc00 + (u & 1023));\n    } else {\n      const c2 = bytes[pos++];\n      const c3 = bytes[pos++];\n      out[c++] = String.fromCharCode(\n        ((c1 & 15) << 12) | ((c2 & 63) << 6) | (c3 & 63)\n      );\n    }\n  }\n  return out.join('');\n};\n\ninterface Base64 {\n  byteToCharMap_: { [key: number]: string } | null;\n  charToByteMap_: { [key: string]: number } | null;\n  byteToCharMapWebSafe_: { [key: number]: string } | null;\n  charToByteMapWebSafe_: { [key: string]: number } | null;\n  ENCODED_VALS_BASE: string;\n  readonly ENCODED_VALS: string;\n  readonly ENCODED_VALS_WEBSAFE: string;\n  HAS_NATIVE_SUPPORT: boolean;\n  encodeByteArray(input: number[] | Uint8Array, webSafe?: boolean): string;\n  encodeString(input: string, webSafe?: boolean): string;\n  decodeString(input: string, webSafe: boolean): string;\n  decodeStringToByteArray(input: string, webSafe: boolean): number[];\n  init_(): void;\n}\n\n// We define it as an object literal instead of a class because a class compiled down to es5 can't\n// be treeshaked. https://github.com/rollup/rollup/issues/1691\n// Static lookup maps, lazily populated by init_()\nexport const base64: Base64 = {\n  /**\n   * Maps bytes to characters.\n   */\n  byteToCharMap_: null,\n\n  /**\n   * Maps characters to bytes.\n   */\n  charToByteMap_: null,\n\n  /**\n   * Maps bytes to websafe characters.\n   * @private\n   */\n  byteToCharMapWebSafe_: null,\n\n  /**\n   * Maps websafe characters to bytes.\n   * @private\n   */\n  charToByteMapWebSafe_: null,\n\n  /**\n   * Our default alphabet, shared between\n   * ENCODED_VALS and ENCODED_VALS_WEBSAFE\n   */\n  ENCODED_VALS_BASE:\n    'ABCDEFGHIJKLMNOPQRSTUVWXYZ' + 'abcdefghijklmnopqrstuvwxyz' + '0123456789',\n\n  /**\n   * Our default alphabet. Value 64 (=) is special; it means \"nothing.\"\n   */\n  get ENCODED_VALS() {\n    return this.ENCODED_VALS_BASE + '+/=';\n  },\n\n  /**\n   * Our websafe alphabet.\n   */\n  get ENCODED_VALS_WEBSAFE() {\n    return this.ENCODED_VALS_BASE + '-_.';\n  },\n\n  /**\n   * Whether this browser supports the atob and btoa functions. This extension\n   * started at Mozilla but is now implemented by many browsers. We use the\n   * ASSUME_* variables to avoid pulling in the full useragent detection library\n   * but still allowing the standard per-browser compilations.\n   *\n   */\n  HAS_NATIVE_SUPPORT: typeof atob === 'function',\n\n  /**\n   * Base64-encode an array of bytes.\n   *\n   * @param input An array of bytes (numbers with\n   *     value in [0, 255]) to encode.\n   * @param webSafe Boolean indicating we should use the\n   *     alternative alphabet.\n   * @return The base64 encoded string.\n   */\n  encodeByteArray(input: number[] | Uint8Array, webSafe?: boolean): string {\n    if (!Array.isArray(input)) {\n      throw Error('encodeByteArray takes an array as a parameter');\n    }\n\n    this.init_();\n\n    const byteToCharMap = webSafe\n      ? this.byteToCharMapWebSafe_!\n      : this.byteToCharMap_!;\n\n    const output = [];\n\n    for (let i = 0; i < input.length; i += 3) {\n      const byte1 = input[i];\n      const haveByte2 = i + 1 < input.length;\n      const byte2 = haveByte2 ? input[i + 1] : 0;\n      const haveByte3 = i + 2 < input.length;\n      const byte3 = haveByte3 ? input[i + 2] : 0;\n\n      const outByte1 = byte1 >> 2;\n      const outByte2 = ((byte1 & 0x03) << 4) | (byte2 >> 4);\n      let outByte3 = ((byte2 & 0x0f) << 2) | (byte3 >> 6);\n      let outByte4 = byte3 & 0x3f;\n\n      if (!haveByte3) {\n        outByte4 = 64;\n\n        if (!haveByte2) {\n          outByte3 = 64;\n        }\n      }\n\n      output.push(\n        byteToCharMap[outByte1],\n        byteToCharMap[outByte2],\n        byteToCharMap[outByte3],\n        byteToCharMap[outByte4]\n      );\n    }\n\n    return output.join('');\n  },\n\n  /**\n   * Base64-encode a string.\n   *\n   * @param input A string to encode.\n   * @param webSafe If true, we should use the\n   *     alternative alphabet.\n   * @return The base64 encoded string.\n   */\n  encodeString(input: string, webSafe?: boolean): string {\n    // Shortcut for Mozilla browsers that implement\n    // a native base64 encoder in the form of \"btoa/atob\"\n    if (this.HAS_NATIVE_SUPPORT && !webSafe) {\n      return btoa(input);\n    }\n    return this.encodeByteArray(stringToByteArray(input), webSafe);\n  },\n\n  /**\n   * Base64-decode a string.\n   *\n   * @param input to decode.\n   * @param webSafe True if we should use the\n   *     alternative alphabet.\n   * @return string representing the decoded value.\n   */\n  decodeString(input: string, webSafe: boolean): string {\n    // Shortcut for Mozilla browsers that implement\n    // a native base64 encoder in the form of \"btoa/atob\"\n    if (this.HAS_NATIVE_SUPPORT && !webSafe) {\n      return atob(input);\n    }\n    return byteArrayToString(this.decodeStringToByteArray(input, webSafe));\n  },\n\n  /**\n   * Base64-decode a string.\n   *\n   * In base-64 decoding, groups of four characters are converted into three\n   * bytes.  If the encoder did not apply padding, the input length may not\n   * be a multiple of 4.\n   *\n   * In this case, the last group will have fewer than 4 characters, and\n   * padding will be inferred.  If the group has one or two characters, it decodes\n   * to one byte.  If the group has three characters, it decodes to two bytes.\n   *\n   * @param input Input to decode.\n   * @param webSafe True if we should use the web-safe alphabet.\n   * @return bytes representing the decoded value.\n   */\n  decodeStringToByteArray(input: string, webSafe: boolean): number[] {\n    this.init_();\n\n    const charToByteMap = webSafe\n      ? this.charToByteMapWebSafe_!\n      : this.charToByteMap_!;\n\n    const output: number[] = [];\n\n    for (let i = 0; i < input.length; ) {\n      const byte1 = charToByteMap[input.charAt(i++)];\n\n      const haveByte2 = i < input.length;\n      const byte2 = haveByte2 ? charToByteMap[input.charAt(i)] : 0;\n      ++i;\n\n      const haveByte3 = i < input.length;\n      const byte3 = haveByte3 ? charToByteMap[input.charAt(i)] : 64;\n      ++i;\n\n      const haveByte4 = i < input.length;\n      const byte4 = haveByte4 ? charToByteMap[input.charAt(i)] : 64;\n      ++i;\n\n      if (byte1 == null || byte2 == null || byte3 == null || byte4 == null) {\n        throw new DecodeBase64StringError();\n      }\n\n      const outByte1 = (byte1 << 2) | (byte2 >> 4);\n      output.push(outByte1);\n\n      if (byte3 !== 64) {\n        const outByte2 = ((byte2 << 4) & 0xf0) | (byte3 >> 2);\n        output.push(outByte2);\n\n        if (byte4 !== 64) {\n          const outByte3 = ((byte3 << 6) & 0xc0) | byte4;\n          output.push(outByte3);\n        }\n      }\n    }\n\n    return output;\n  },\n\n  /**\n   * Lazy static initialization function. Called before\n   * accessing any of the static map variables.\n   * @private\n   */\n  init_() {\n    if (!this.byteToCharMap_) {\n      this.byteToCharMap_ = {};\n      this.charToByteMap_ = {};\n      this.byteToCharMapWebSafe_ = {};\n      this.charToByteMapWebSafe_ = {};\n\n      // We want quick mappings back and forth, so we precompute two maps.\n      for (let i = 0; i < this.ENCODED_VALS.length; i++) {\n        this.byteToCharMap_[i] = this.ENCODED_VALS.charAt(i);\n        this.charToByteMap_[this.byteToCharMap_[i]] = i;\n        this.byteToCharMapWebSafe_[i] = this.ENCODED_VALS_WEBSAFE.charAt(i);\n        this.charToByteMapWebSafe_[this.byteToCharMapWebSafe_[i]] = i;\n\n        // Be forgiving when decoding and correctly decode both encodings.\n        if (i >= this.ENCODED_VALS_BASE.length) {\n          this.charToByteMap_[this.ENCODED_VALS_WEBSAFE.charAt(i)] = i;\n          this.charToByteMapWebSafe_[this.ENCODED_VALS.charAt(i)] = i;\n        }\n      }\n    }\n  }\n};\n\n/**\n * An error encountered while decoding base64 string.\n */\nexport class DecodeBase64StringError extends Error {\n  readonly name = 'DecodeBase64StringError';\n}\n\n/**\n * URL-safe base64 encoding\n */\nexport const base64Encode = function (str: string): string {\n  const utf8Bytes = stringToByteArray(str);\n  return base64.encodeByteArray(utf8Bytes, true);\n};\n\n/**\n * URL-safe base64 encoding (without \".\" padding in the end).\n * e.g. Used in JSON Web Token (JWT) parts.\n */\nexport const base64urlEncodeWithoutPadding = function (str: string): string {\n  // Use base64url encoding and remove padding in the end (dot characters).\n  return base64Encode(str).replace(/\\./g, '');\n};\n\n/**\n * URL-safe base64 decoding\n *\n * NOTE: DO NOT use the global atob() function - it does NOT support the\n * base64Url variant encoding.\n *\n * @param str To be decoded\n * @return Decoded result, if possible\n */\nexport const base64Decode = function (str: string): string | null {\n  try {\n    return base64.decodeString(str, true);\n  } catch (e) {\n    console.error('base64Decode failed: ', e);\n  }\n  return null;\n};\n", "/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n * @fileoverview Standardized Firebase Error.\n *\n * Usage:\n *\n *   // TypeScript string literals for type-safe codes\n *   type Err =\n *     'unknown' |\n *     'object-not-found'\n *     ;\n *\n *   // Closure enum for type-safe error codes\n *   // at-enum {string}\n *   var Err = {\n *     UNKNOWN: 'unknown',\n *     OBJECT_NOT_FOUND: 'object-not-found',\n *   }\n *\n *   let errors: Map<Err, string> = {\n *     'generic-error': \"Unknown error\",\n *     'file-not-found': \"Could not find file: {$file}\",\n *   };\n *\n *   // Type-safe function - must pass a valid error code as param.\n *   let error = new ErrorFactory<Err>('service', 'Service', errors);\n *\n *   ...\n *   throw error.create(Err.GENERIC);\n *   ...\n *   throw error.create(Err.FILE_NOT_FOUND, {'file': fileName});\n *   ...\n *   // Service: Could not file file: foo.txt (service/file-not-found).\n *\n *   catch (e) {\n *     assert(e.message === \"Could not find file: foo.txt.\");\n *     if ((e as FirebaseError)?.code === 'service/file-not-found') {\n *       console.log(\"Could not read file: \" + e['file']);\n *     }\n *   }\n */\n\nexport type ErrorMap<ErrorCode extends string> = {\n  readonly [K in ErrorCode]: string;\n};\n\nconst ERROR_NAME = 'FirebaseError';\n\nexport interface StringLike {\n  toString(): string;\n}\n\nexport interface ErrorData {\n  [key: string]: unknown;\n}\n\n// Based on code from:\n// https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Error#Custom_Error_Types\nexport class FirebaseError extends Error {\n  /** The custom name for all FirebaseErrors. */\n  readonly name: string = ERROR_NAME;\n\n  constructor(\n    /** The error code for this error. */\n    readonly code: string,\n    message: string,\n    /** Custom data for this error. */\n    public customData?: Record<string, unknown>\n  ) {\n    super(message);\n\n    // Fix For ES5\n    // https://github.com/Microsoft/TypeScript-wiki/blob/master/Breaking-Changes.md#extending-built-ins-like-error-array-and-map-may-no-longer-work\n    Object.setPrototypeOf(this, FirebaseError.prototype);\n\n    // Maintains proper stack trace for where our error was thrown.\n    // Only available on V8.\n    if (Error.captureStackTrace) {\n      Error.captureStackTrace(this, ErrorFactory.prototype.create);\n    }\n  }\n}\n\nexport class ErrorFactory<\n  ErrorCode extends string,\n  ErrorParams extends { readonly [K in ErrorCode]?: ErrorData } = {}\n> {\n  constructor(\n    private readonly service: string,\n    private readonly serviceName: string,\n    private readonly errors: ErrorMap<ErrorCode>\n  ) {}\n\n  create<K extends ErrorCode>(\n    code: K,\n    ...data: K extends keyof ErrorParams ? [ErrorParams[K]] : []\n  ): FirebaseError {\n    const customData = (data[0] as ErrorData) || {};\n    const fullCode = `${this.service}/${code}`;\n    const template = this.errors[code];\n\n    const message = template ? replaceTemplate(template, customData) : 'Error';\n    // Service Name: Error message (service/code).\n    const fullMessage = `${this.serviceName}: ${message} (${fullCode}).`;\n\n    const error = new FirebaseError(fullCode, fullMessage, customData);\n\n    return error;\n  }\n}\n\nfunction replaceTemplate(template: string, data: ErrorData): string {\n  return template.replace(PATTERN, (_, key) => {\n    const value = data[key];\n    return value != null ? String(value) : `<${key}?>`;\n  });\n}\n\nconst PATTERN = /\\{\\$([^}]+)}/g;\n", "/**\n * @license\n * Copyright 2021 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nexport interface Compat<T> {\n  _delegate: T;\n}\n\nexport function getModularInstance<ExpService>(\n  service: Compat<ExpService> | ExpService\n): ExpService {\n  if (service && (service as Compat<ExpService>)._delegate) {\n    return (service as Compat<ExpService>)._delegate;\n  } else {\n    return service as ExpService;\n  }\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nimport {\n  InstantiationMode,\n  InstanceFactory,\n  ComponentType,\n  Dictionary,\n  Name,\n  onInstanceCreatedCallback\n} from './types';\n\n/**\n * Component for service name T, e.g. `auth`, `auth-internal`\n */\nexport class Component<T extends Name = Name> {\n  multipleInstances = false;\n  /**\n   * Properties to be added to the service namespace\n   */\n  serviceProps: Dictionary = {};\n\n  instantiationMode = InstantiationMode.LAZY;\n\n  onInstanceCreated: onInstanceCreatedCallback<T> | null = null;\n\n  /**\n   *\n   * @param name The public service name, e.g. app, auth, firestore, database\n   * @param instanceFactory Service factory responsible for creating the public interface\n   * @param type whether the service provided by the component is public or private\n   */\n  constructor(\n    readonly name: T,\n    readonly instanceFactory: InstanceFactory<T>,\n    readonly type: ComponentType\n  ) {}\n\n  setInstantiationMode(mode: InstantiationMode): this {\n    this.instantiationMode = mode;\n    return this;\n  }\n\n  setMultipleInstances(multipleInstances: boolean): this {\n    this.multipleInstances = multipleInstances;\n    return this;\n  }\n\n  setServiceProps(props: Dictionary): this {\n    this.serviceProps = props;\n    return this;\n  }\n\n  setInstanceCreatedCallback(callback: onInstanceCreatedCallback<T>): this {\n    this.onInstanceCreated = callback;\n    return this;\n  }\n}\n", "/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n * @fileoverview Constants used in the Firebase Storage library.\n */\n\n/**\n * Domain name for firebase storage.\n */\nexport const DEFAULT_HOST = 'firebasestorage.googleapis.com';\n\n/**\n * The key in Firebase config json for the storage bucket.\n */\nexport const CONFIG_STORAGE_BUCKET_KEY = 'storageBucket';\n\n/**\n * 2 minutes\n *\n * The timeout for all operations except upload.\n */\nexport const DEFAULT_MAX_OPERATION_RETRY_TIME = 2 * 60 * 1000;\n\n/**\n * 10 minutes\n *\n * The timeout for upload.\n */\nexport const DEFAULT_MAX_UPLOAD_RETRY_TIME = 10 * 60 * 1000;\n\n/**\n * 1 second\n */\nexport const DEFAULT_MIN_SLEEP_TIME_MILLIS = 1000;\n\n/**\n * This is the value of Number.MIN_SAFE_INTEGER, which is not well supported\n * enough for us to use it directly.\n */\nexport const MIN_SAFE_INTEGER = -9007199254740991;\n", "/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n/**\n * @fileoverview Functionality related to the parsing/composition of bucket/\n * object location.\n */\n\nimport { invalidDefaultBucket, invalidUrl } from './error';\nimport { DEFAULT_HOST } from './constants';\n\n/**\n * Firebase Storage location data.\n *\n * @internal\n */\nexport class Location {\n  private path_: string;\n\n  constructor(public readonly bucket: string, path: string) {\n    this.path_ = path;\n  }\n\n  get path(): string {\n    return this.path_;\n  }\n\n  get isRoot(): boolean {\n    return this.path.length === 0;\n  }\n\n  fullServerUrl(): string {\n    const encode = encodeURIComponent;\n    return '/b/' + encode(this.bucket) + '/o/' + encode(this.path);\n  }\n\n  bucketOnlyServerUrl(): string {\n    const encode = encodeURIComponent;\n    return '/b/' + encode(this.bucket) + '/o';\n  }\n\n  static makeFromBucketSpec(bucketString: string, host: string): Location {\n    let bucketLocation;\n    try {\n      bucketLocation = Location.makeFromUrl(bucketString, host);\n    } catch (e) {\n      // Not valid URL, use as-is. This lets you put bare bucket names in\n      // config.\n      return new Location(bucketString, '');\n    }\n    if (bucketLocation.path === '') {\n      return bucketLocation;\n    } else {\n      throw invalidDefaultBucket(bucketString);\n    }\n  }\n\n  static makeFromUrl(url: string, host: string): Location {\n    let location: Location | null = null;\n    const bucketDomain = '([A-Za-z0-9.\\\\-_]+)';\n\n    function gsModify(loc: Location): void {\n      if (loc.path.charAt(loc.path.length - 1) === '/') {\n        loc.path_ = loc.path_.slice(0, -1);\n      }\n    }\n    const gsPath = '(/(.*))?$';\n    const gsRegex = new RegExp('^gs://' + bucketDomain + gsPath, 'i');\n    const gsIndices = { bucket: 1, path: 3 };\n\n    function httpModify(loc: Location): void {\n      loc.path_ = decodeURIComponent(loc.path);\n    }\n    const version = 'v[A-Za-z0-9_]+';\n    const firebaseStorageHost = host.replace(/[.]/g, '\\\\.');\n    const firebaseStoragePath = '(/([^?#]*).*)?$';\n    const firebaseStorageRegExp = new RegExp(\n      `^https?://${firebaseStorageHost}/${version}/b/${bucketDomain}/o${firebaseStoragePath}`,\n      'i'\n    );\n    const firebaseStorageIndices = { bucket: 1, path: 3 };\n\n    const cloudStorageHost =\n      host === DEFAULT_HOST\n        ? '(?:storage.googleapis.com|storage.cloud.google.com)'\n        : host;\n    const cloudStoragePath = '([^?#]*)';\n    const cloudStorageRegExp = new RegExp(\n      `^https?://${cloudStorageHost}/${bucketDomain}/${cloudStoragePath}`,\n      'i'\n    );\n    const cloudStorageIndices = { bucket: 1, path: 2 };\n\n    const groups = [\n      { regex: gsRegex, indices: gsIndices, postModify: gsModify },\n      {\n        regex: firebaseStorageRegExp,\n        indices: firebaseStorageIndices,\n        postModify: httpModify\n      },\n      {\n        regex: cloudStorageRegExp,\n        indices: cloudStorageIndices,\n        postModify: httpModify\n      }\n    ];\n    for (let i = 0; i < groups.length; i++) {\n      const group = groups[i];\n      const captures = group.regex.exec(url);\n      if (captures) {\n        const bucketValue = captures[group.indices.bucket];\n        let pathValue = captures[group.indices.path];\n        if (!pathValue) {\n          pathValue = '';\n        }\n        location = new Location(bucketValue, pathValue);\n        group.postModify(location);\n        break;\n      }\n    }\n    if (location == null) {\n      throw invalidUrl(url);\n    }\n    return location;\n  }\n}\n", "/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nimport { StorageError } from './error';\nimport { Request } from './request';\n\n/**\n * A request whose promise always fails.\n */\nexport class FailRequest<T> implements Request<T> {\n  promise_: Promise<T>;\n\n  constructor(error: StorageError) {\n    this.promise_ = Promise.reject<T>(error);\n  }\n\n  /** @inheritDoc */\n  getPromise(): Promise<T> {\n    return this.promise_;\n  }\n\n  /** @inheritDoc */\n  cancel(_appDelete = false): void {}\n}\n", "/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { invalidArgument } from './error';\n\nexport function isJustDef<T>(p: T | null | undefined): p is T | null {\n  return p !== void 0;\n}\n\n// eslint-disable-next-line @typescript-eslint/ban-types\nexport function isFunction(p: unknown): p is Function {\n  return typeof p === 'function';\n}\n\nexport function isNonArrayObject(p: unknown): boolean {\n  return typeof p === 'object' && !Array.isArray(p);\n}\n\nexport function isString(p: unknown): p is string {\n  return typeof p === 'string' || p instanceof String;\n}\n\nexport function isNativeBlob(p: unknown): p is Blob {\n  return isNativeBlobDefined() && p instanceof Blob;\n}\n\nexport function isNativeBlobDefined(): boolean {\n  return typeof Blob !== 'undefined';\n}\n\nexport function validateNumber(\n  argument: string,\n  minValue: number,\n  maxValue: number,\n  value: number\n): void {\n  if (value < minValue) {\n    throw invalidArgument(\n      `Invalid value for '${argument}'. Expected ${minValue} or greater.`\n    );\n  }\n  if (value > maxValue) {\n    throw invalidArgument(\n      `Invalid value for '${argument}'. Expected ${maxValue} or less.`\n    );\n  }\n}\n", "/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n/**\n * @fileoverview Functions to create and manipulate URLs for the server API.\n */\nimport { UrlParams } from './requestinfo';\n\nexport function makeUrl(\n  urlPart: string,\n  host: string,\n  protocol: string\n): string {\n  let origin = host;\n  if (protocol == null) {\n    origin = `https://${host}`;\n  }\n  return `${protocol}://${origin}/v0${urlPart}`;\n}\n\nexport function makeQueryString(params: UrlParams): string {\n  const encode = encodeURIComponent;\n  let queryPart = '?';\n  for (const key in params) {\n    if (params.hasOwnProperty(key)) {\n      const nextPart = encode(key) + '=' + encode(params[key]);\n      queryPart = queryPart + nextPart + '&';\n    }\n  }\n\n  // Chop off the extra '&' or '?' on the end\n  queryPart = queryPart.slice(0, -1);\n  return queryPart;\n}\n", "/**\n * @license\n * Copyright 2022 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n/**\n * Checks the status code to see if the action should be retried.\n *\n * @param status Current HTTP status code returned by server.\n * @param additionalRetryCodes additional retry codes to check against\n */\nexport function isRetryStatusCode(\n  status: number,\n  additionalRetryCodes: number[]\n): boolean {\n  // The codes for which to retry came from this page:\n  // https://cloud.google.com/storage/docs/exponential-backoff\n  const isFiveHundredCode = status >= 500 && status < 600;\n  const extraRetryCodes = [\n    // Request Timeout: web server didn't receive full request in time.\n    408,\n    // Too Many Requests: you're getting rate-limited, basically.\n    429\n  ];\n  const isExtraRetryCode = extraRetryCodes.indexOf(status) !== -1;\n  const isAdditionalRetryCode = additionalRetryCodes.indexOf(status) !== -1;\n  return isFiveHundredCode || isExtraRetryCode || isAdditionalRetryCode;\n}\n", "/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n/**\n * @fileoverview Defines methods used to actually send HTTP requests from\n * abstract representations.\n */\n\nimport { id as backoffId, start, stop } from './backoff';\nimport { appDeleted, canceled, retryLimitExceeded, unknown } from './error';\nimport { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, RequestHandler, RequestInfo } from './requestinfo';\nimport { isJustDef } from './type';\nimport { makeQueryString } from './url';\nimport { Connection, ErrorCode, Headers, ConnectionType } from './connection';\nimport { isRetryStatusCode } from './utils';\n\nexport interface Request<T> {\n  getPromise(): Promise<T>;\n\n  /**\n   * Cancels the request. IMPORTANT: the promise may still be resolved with an\n   * appropriate value (if the request is finished before you call this method,\n   * but the promise has not yet been resolved), so don't just assume it will be\n   * rejected if you call this function.\n   * @param appDelete - True if the cancelation came from the app being deleted.\n   */\n  cancel(appDelete?: boolean): void;\n}\n\n/**\n * Handles network logic for all Storage Requests, including error reporting and\n * retries with backoff.\n *\n * @param I - the type of the backend's network response.\n * @param - O the output type used by the rest of the SDK. The conversion\n * happens in the specified `callback_`.\n */\nclass NetworkRequest<I extends ConnectionType, O> implements Request<O> {\n  private pendingConnection_: Connection<I> | null = null;\n  private backoffId_: backoffId | null = null;\n  private resolve_!: (value?: O | PromiseLike<O>) => void;\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  private reject_!: (reason?: any) => void;\n  private canceled_: boolean = false;\n  private appDelete_: boolean = false;\n  private promise_: Promise<O>;\n\n  constructor(\n    private url_: string,\n    private method_: string,\n    private headers_: Headers,\n    private body_: string | Blob | Uint8Array | null,\n    private successCodes_: number[],\n    private additionalRetryCodes_: number[],\n    private callback_: RequestHandler<I, O>,\n    private errorCallback_: ErrorHandler | null,\n    private timeout_: number,\n    private progressCallback_: ((p1: number, p2: number) => void) | null,\n    private connectionFactory_: () => Connection<I>,\n    private retry = true\n  ) {\n    this.promise_ = new Promise((resolve, reject) => {\n      this.resolve_ = resolve as (value?: O | PromiseLike<O>) => void;\n      this.reject_ = reject;\n      this.start_();\n    });\n  }\n\n  /**\n   * Actually starts the retry loop.\n   */\n  private start_(): void {\n    const doTheRequest: (\n      backoffCallback: (success: boolean, ...p2: unknown[]) => void,\n      canceled: boolean\n    ) => void = (backoffCallback, canceled) => {\n      if (canceled) {\n        backoffCallback(false, new RequestEndStatus(false, null, true));\n        return;\n      }\n      const connection = this.connectionFactory_();\n      this.pendingConnection_ = connection;\n\n      const progressListener: (\n        progressEvent: ProgressEvent\n      ) => void = progressEvent => {\n        const loaded = progressEvent.loaded;\n        const total = progressEvent.lengthComputable ? progressEvent.total : -1;\n        if (this.progressCallback_ !== null) {\n          this.progressCallback_(loaded, total);\n        }\n      };\n      if (this.progressCallback_ !== null) {\n        connection.addUploadProgressListener(progressListener);\n      }\n\n      // connection.send() never rejects, so we don't need to have a error handler or use catch on the returned promise.\n      // eslint-disable-next-line @typescript-eslint/no-floating-promises\n      connection\n        .send(this.url_, this.method_, this.body_, this.headers_)\n        .then(() => {\n          if (this.progressCallback_ !== null) {\n            connection.removeUploadProgressListener(progressListener);\n          }\n          this.pendingConnection_ = null;\n          const hitServer = connection.getErrorCode() === ErrorCode.NO_ERROR;\n          const status = connection.getStatus();\n          if (\n            !hitServer ||\n            (isRetryStatusCode(status, this.additionalRetryCodes_) &&\n              this.retry)\n          ) {\n            const wasCanceled = connection.getErrorCode() === ErrorCode.ABORT;\n            backoffCallback(\n              false,\n              new RequestEndStatus(false, null, wasCanceled)\n            );\n            return;\n          }\n          const successCode = this.successCodes_.indexOf(status) !== -1;\n          backoffCallback(true, new RequestEndStatus(successCode, connection));\n        });\n    };\n\n    /**\n     * @param requestWentThrough - True if the request eventually went\n     *     through, false if it hit the retry limit or was canceled.\n     */\n    const backoffDone: (\n      requestWentThrough: boolean,\n      status: RequestEndStatus<I>\n    ) => void = (requestWentThrough, status) => {\n      const resolve = this.resolve_;\n      const reject = this.reject_;\n      const connection = status.connection as Connection<I>;\n      if (status.wasSuccessCode) {\n        try {\n          const result = this.callback_(connection, connection.getResponse());\n          if (isJustDef(result)) {\n            resolve(result);\n          } else {\n            resolve();\n          }\n        } catch (e) {\n          reject(e);\n        }\n      } else {\n        if (connection !== null) {\n          const err = unknown();\n          err.serverResponse = connection.getErrorText();\n          if (this.errorCallback_) {\n            reject(this.errorCallback_(connection, err));\n          } else {\n            reject(err);\n          }\n        } else {\n          if (status.canceled) {\n            const err = this.appDelete_ ? appDeleted() : canceled();\n            reject(err);\n          } else {\n            const err = retryLimitExceeded();\n            reject(err);\n          }\n        }\n      }\n    };\n    if (this.canceled_) {\n      backoffDone(false, new RequestEndStatus(false, null, true));\n    } else {\n      this.backoffId_ = start(doTheRequest, backoffDone, this.timeout_);\n    }\n  }\n\n  /** @inheritDoc */\n  getPromise(): Promise<O> {\n    return this.promise_;\n  }\n\n  /** @inheritDoc */\n  cancel(appDelete?: boolean): void {\n    this.canceled_ = true;\n    this.appDelete_ = appDelete || false;\n    if (this.backoffId_ !== null) {\n      stop(this.backoffId_);\n    }\n    if (this.pendingConnection_ !== null) {\n      this.pendingConnection_.abort();\n    }\n  }\n}\n\n/**\n * A collection of information about the result of a network request.\n * @param opt_canceled - Defaults to false.\n */\nexport class RequestEndStatus<I extends ConnectionType> {\n  /**\n   * True if the request was canceled.\n   */\n  canceled: boolean;\n\n  constructor(\n    public wasSuccessCode: boolean,\n    public connection: Connection<I> | null,\n    canceled?: boolean\n  ) {\n    this.canceled = !!canceled;\n  }\n}\n\nexport function addAuthHeader_(\n  headers: Headers,\n  authToken: string | null\n): void {\n  if (authToken !== null && authToken.length > 0) {\n    headers['Authorization'] = 'Firebase ' + authToken;\n  }\n}\n\nexport function addVersionHeader_(\n  headers: Headers,\n  firebaseVersion?: string\n): void {\n  headers['X-Firebase-Storage-Version'] =\n    'webjs/' + (firebaseVersion ?? 'AppManager');\n}\n\nexport function addGmpidHeader_(headers: Headers, appId: string | null): void {\n  if (appId) {\n    headers['X-Firebase-GMPID'] = appId;\n  }\n}\n\nexport function addAppCheckHeader_(\n  headers: Headers,\n  appCheckToken: string | null\n): void {\n  if (appCheckToken !== null) {\n    headers['X-Firebase-AppCheck'] = appCheckToken;\n  }\n}\n\nexport function makeRequest<I extends ConnectionType, O>(\n  requestInfo: RequestInfo<I, O>,\n  appId: string | null,\n  authToken: string | null,\n  appCheckToken: string | null,\n  requestFactory: () => Connection<I>,\n  firebaseVersion?: string,\n  retry = true\n): Request<O> {\n  const queryPart = makeQueryString(requestInfo.urlParams);\n  const url = requestInfo.url + queryPart;\n  const headers = Object.assign({}, requestInfo.headers);\n  addGmpidHeader_(headers, appId);\n  addAuthHeader_(headers, authToken);\n  addVersionHeader_(headers, firebaseVersion);\n  addAppCheckHeader_(headers, appCheckToken);\n  return new NetworkRequest<I, O>(\n    url,\n    requestInfo.method,\n    headers,\n    requestInfo.body,\n    requestInfo.successCodes,\n    requestInfo.additionalRetryCodes,\n    requestInfo.handler,\n    requestInfo.errorHandler,\n    requestInfo.timeout,\n    requestInfo.progressCallback,\n    requestFactory,\n    retry\n  );\n}\n", "/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n/**\n * @fileoverview Provides a method for running a function with exponential\n * backoff.\n */\ntype id = (p1: boolean) => void;\n\nexport { id };\n\n/**\n * Accepts a callback for an action to perform (`doRequest`),\n * and then a callback for when the backoff has completed (`backoffCompleteCb`).\n * The callback sent to start requires an argument to call (`onRequestComplete`).\n * When `start` calls `doRequest`, it passes a callback for when the request has\n * completed, `onRequestComplete`. Based on this, the backoff continues, with\n * another call to `doRequest` and the above loop continues until the timeout\n * is hit, or a successful response occurs.\n * @description\n * @param doRequest Callback to perform request\n * @param backoffCompleteCb Callback to call when backoff has been completed\n */\nexport function start(\n  doRequest: (\n    onRequestComplete: (success: boolean) => void,\n    canceled: boolean\n  ) => void,\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  backoffCompleteCb: (...args: any[]) => unknown,\n  timeout: number\n): id {\n  // TODO(andysoto): make this code cleaner (probably refactor into an actual\n  // type instead of a bunch of functions with state shared in the closure)\n  let waitSeconds = 1;\n  // Would type this as \"number\" but that doesn't work for Node so ¯\\_(ツ)_/¯\n  // TODO: find a way to exclude Node type definition for storage because storage only works in browser\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  let retryTimeoutId: any = null;\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  let globalTimeoutId: any = null;\n  let hitTimeout = false;\n  let cancelState = 0;\n\n  function canceled(): boolean {\n    return cancelState === 2;\n  }\n  let triggeredCallback = false;\n\n  function triggerCallback(...args: any[]): void {\n    if (!triggeredCallback) {\n      triggeredCallback = true;\n      backoffCompleteCb.apply(null, args);\n    }\n  }\n\n  function callWithDelay(millis: number): void {\n    retryTimeoutId = setTimeout(() => {\n      retryTimeoutId = null;\n      doRequest(responseHandler, canceled());\n    }, millis);\n  }\n\n  function clearGlobalTimeout(): void {\n    if (globalTimeoutId) {\n      clearTimeout(globalTimeoutId);\n    }\n  }\n\n  function responseHandler(success: boolean, ...args: any[]): void {\n    if (triggeredCallback) {\n      clearGlobalTimeout();\n      return;\n    }\n    if (success) {\n      clearGlobalTimeout();\n      triggerCallback.call(null, success, ...args);\n      return;\n    }\n    const mustStop = canceled() || hitTimeout;\n    if (mustStop) {\n      clearGlobalTimeout();\n      triggerCallback.call(null, success, ...args);\n      return;\n    }\n    if (waitSeconds < 64) {\n      /* TODO(andysoto): don't back off so quickly if we know we're offline. */\n      waitSeconds *= 2;\n    }\n    let waitMillis;\n    if (cancelState === 1) {\n      cancelState = 2;\n      waitMillis = 0;\n    } else {\n      waitMillis = (waitSeconds + Math.random()) * 1000;\n    }\n    callWithDelay(waitMillis);\n  }\n  let stopped = false;\n\n  function stop(wasTimeout: boolean): void {\n    if (stopped) {\n      return;\n    }\n    stopped = true;\n    clearGlobalTimeout();\n    if (triggeredCallback) {\n      return;\n    }\n    if (retryTimeoutId !== null) {\n      if (!wasTimeout) {\n        cancelState = 2;\n      }\n      clearTimeout(retryTimeoutId);\n      callWithDelay(0);\n    } else {\n      if (!wasTimeout) {\n        cancelState = 1;\n      }\n    }\n  }\n  callWithDelay(0);\n  globalTimeoutId = setTimeout(() => {\n    hitTimeout = true;\n    stop(true);\n  }, timeout);\n  return stop;\n}\n\n/**\n * Stops the retry loop from repeating.\n * If the function is currently \"in between\" retries, it is invoked immediately\n * with the second parameter as \"true\". Otherwise, it will be invoked once more\n * after the current invocation finishes iff the current invocation would have\n * triggered another retry.\n */\nexport function stop(id: id): void {\n  id(false);\n}\n", "/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n * @fileoverview Some methods copied from goog.fs.\n * We don't include goog.fs because it pulls in a bunch of Deferred code that\n * bloats the size of the released binary.\n */\nimport { isNativeBlobDefined } from './type';\nimport { StorageErrorCode, StorageError } from './error';\n\nfunction getBlobBuilder(): typeof IBlobBuilder | undefined {\n  if (typeof BlobBuilder !== 'undefined') {\n    return BlobBuilder;\n  } else if (typeof WebKitBlobBuilder !== 'undefined') {\n    return WebKitBlobBuilder;\n  } else {\n    return undefined;\n  }\n}\n\n/**\n * Concatenates one or more values together and converts them to a Blob.\n *\n * @param args The values that will make up the resulting blob.\n * @return The blob.\n */\nexport function getBlob(...args: Array<string | Blob | ArrayBuffer>): Blob {\n  const BlobBuilder = getBlobBuilder();\n  if (BlobBuilder !== undefined) {\n    const bb = new BlobBuilder();\n    for (let i = 0; i < args.length; i++) {\n      bb.append(args[i]);\n    }\n    return bb.getBlob();\n  } else {\n    if (isNativeBlobDefined()) {\n      return new Blob(args);\n    } else {\n      throw new StorageError(\n        StorageErrorCode.UNSUPPORTED_ENVIRONMENT,\n        \"This browser doesn't seem to support creating Blobs\"\n      );\n    }\n  }\n}\n\n/**\n * Slices the blob. The returned blob contains data from the start byte\n * (inclusive) till the end byte (exclusive). Negative indices cannot be used.\n *\n * @param blob The blob to be sliced.\n * @param start Index of the starting byte.\n * @param end Index of the ending byte.\n * @return The blob slice or null if not supported.\n */\nexport function sliceBlob(blob: Blob, start: number, end: number): Blob | null {\n  if (blob.webkitSlice) {\n    return blob.webkitSlice(start, end);\n  } else if (blob.mozSlice) {\n    return blob.mozSlice(start, end);\n  } else if (blob.slice) {\n    return blob.slice(start, end);\n  }\n  return null;\n}\n", "/**\n * @license\n * Copyright 2021 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { missingPolyFill } from '../../implementation/error';\n\n/** Converts a Base64 encoded string to a binary string. */\nexport function decodeBase64(encoded: string): string {\n  if (typeof atob === 'undefined') {\n    throw missingPolyFill('base-64');\n  }\n  return atob(encoded);\n}\n\nexport function decodeUint8Array(data: Uint8Array): string {\n  return new TextDecoder().decode(data);\n}\n", "/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { unknown, invalidFormat } from './error';\nimport { decodeBase64 } from '../platform/base64';\n\n/**\n * An enumeration of the possible string formats for upload.\n * @public\n */\nexport type StringFormat = (typeof StringFormat)[keyof typeof StringFormat];\n/**\n * An enumeration of the possible string formats for upload.\n * @public\n */\nexport const StringFormat = {\n  /**\n   * Indicates the string should be interpreted \"raw\", that is, as normal text.\n   * The string will be interpreted as UTF-16, then uploaded as a UTF-8 byte\n   * sequence.\n   * Example: The string 'Hello! \\\\ud83d\\\\ude0a' becomes the byte sequence\n   * 48 65 6c 6c 6f 21 20 f0 9f 98 8a\n   */\n  RAW: 'raw',\n  /**\n   * Indicates the string should be interpreted as base64-encoded data.\n   * Padding characters (trailing '='s) are optional.\n   * Example: The string 'rWmO++E6t7/rlw==' becomes the byte sequence\n   * ad 69 8e fb e1 3a b7 bf eb 97\n   */\n  BASE64: 'base64',\n  /**\n   * Indicates the string should be interpreted as base64url-encoded data.\n   * Padding characters (trailing '='s) are optional.\n   * Example: The string 'rWmO--E6t7_rlw==' becomes the byte sequence\n   * ad 69 8e fb e1 3a b7 bf eb 97\n   */\n  BASE64URL: 'base64url',\n  /**\n   * Indicates the string is a data URL, such as one obtained from\n   * canvas.toDataURL().\n   * Example: the string 'data:application/octet-stream;base64,aaaa'\n   * becomes the byte sequence\n   * 69 a6 9a\n   * (the content-type \"application/octet-stream\" is also applied, but can\n   * be overridden in the metadata object).\n   */\n  DATA_URL: 'data_url'\n} as const;\n\nexport class StringData {\n  contentType: string | null;\n\n  constructor(public data: Uint8Array, contentType?: string | null) {\n    this.contentType = contentType || null;\n  }\n}\n\n/**\n * @internal\n */\nexport function dataFromString(\n  format: StringFormat,\n  stringData: string\n): StringData {\n  switch (format) {\n    case StringFormat.RAW:\n      return new StringData(utf8Bytes_(stringData));\n    case StringFormat.BASE64:\n    case StringFormat.BASE64URL:\n      return new StringData(base64Bytes_(format, stringData));\n    case StringFormat.DATA_URL:\n      return new StringData(\n        dataURLBytes_(stringData),\n        dataURLContentType_(stringData)\n      );\n    default:\n    // do nothing\n  }\n\n  // assert(false);\n  throw unknown();\n}\n\nexport function utf8Bytes_(value: string): Uint8Array {\n  const b: number[] = [];\n  for (let i = 0; i < value.length; i++) {\n    let c = value.charCodeAt(i);\n    if (c <= 127) {\n      b.push(c);\n    } else {\n      if (c <= 2047) {\n        b.push(192 | (c >> 6), 128 | (c & 63));\n      } else {\n        if ((c & 64512) === 55296) {\n          // The start of a surrogate pair.\n          const valid =\n            i < value.length - 1 && (value.charCodeAt(i + 1) & 64512) === 56320;\n          if (!valid) {\n            // The second surrogate wasn't there.\n            b.push(239, 191, 189);\n          } else {\n            const hi = c;\n            const lo = value.charCodeAt(++i);\n            c = 65536 | ((hi & 1023) << 10) | (lo & 1023);\n            b.push(\n              240 | (c >> 18),\n              128 | ((c >> 12) & 63),\n              128 | ((c >> 6) & 63),\n              128 | (c & 63)\n            );\n          }\n        } else {\n          if ((c & 64512) === 56320) {\n            // Invalid low surrogate.\n            b.push(239, 191, 189);\n          } else {\n            b.push(224 | (c >> 12), 128 | ((c >> 6) & 63), 128 | (c & 63));\n          }\n        }\n      }\n    }\n  }\n  return new Uint8Array(b);\n}\n\nexport function percentEncodedBytes_(value: string): Uint8Array {\n  let decoded;\n  try {\n    decoded = decodeURIComponent(value);\n  } catch (e) {\n    throw invalidFormat(StringFormat.DATA_URL, 'Malformed data URL.');\n  }\n  return utf8Bytes_(decoded);\n}\n\nexport function base64Bytes_(format: StringFormat, value: string): Uint8Array {\n  switch (format) {\n    case StringFormat.BASE64: {\n      const hasMinus = value.indexOf('-') !== -1;\n      const hasUnder = value.indexOf('_') !== -1;\n      if (hasMinus || hasUnder) {\n        const invalidChar = hasMinus ? '-' : '_';\n        throw invalidFormat(\n          format,\n          \"Invalid character '\" +\n            invalidChar +\n            \"' found: is it base64url encoded?\"\n        );\n      }\n      break;\n    }\n    case StringFormat.BASE64URL: {\n      const hasPlus = value.indexOf('+') !== -1;\n      const hasSlash = value.indexOf('/') !== -1;\n      if (hasPlus || hasSlash) {\n        const invalidChar = hasPlus ? '+' : '/';\n        throw invalidFormat(\n          format,\n          \"Invalid character '\" + invalidChar + \"' found: is it base64 encoded?\"\n        );\n      }\n      value = value.replace(/-/g, '+').replace(/_/g, '/');\n      break;\n    }\n    default:\n    // do nothing\n  }\n  let bytes;\n  try {\n    bytes = decodeBase64(value);\n  } catch (e) {\n    if ((e as Error).message.includes('polyfill')) {\n      throw e;\n    }\n    throw invalidFormat(format, 'Invalid character found');\n  }\n  const array = new Uint8Array(bytes.length);\n  for (let i = 0; i < bytes.length; i++) {\n    array[i] = bytes.charCodeAt(i);\n  }\n  return array;\n}\n\nclass DataURLParts {\n  base64: boolean = false;\n  contentType: string | null = null;\n  rest: string;\n\n  constructor(dataURL: string) {\n    const matches = dataURL.match(/^data:([^,]+)?,/);\n    if (matches === null) {\n      throw invalidFormat(\n        StringFormat.DATA_URL,\n        \"Must be formatted 'data:[<mediatype>][;base64],<data>\"\n      );\n    }\n    const middle = matches[1] || null;\n    if (middle != null) {\n      this.base64 = endsWith(middle, ';base64');\n      this.contentType = this.base64\n        ? middle.substring(0, middle.length - ';base64'.length)\n        : middle;\n    }\n    this.rest = dataURL.substring(dataURL.indexOf(',') + 1);\n  }\n}\n\nexport function dataURLBytes_(dataUrl: string): Uint8Array {\n  const parts = new DataURLParts(dataUrl);\n  if (parts.base64) {\n    return base64Bytes_(StringFormat.BASE64, parts.rest);\n  } else {\n    return percentEncodedBytes_(parts.rest);\n  }\n}\n\nexport function dataURLContentType_(dataUrl: string): string | null {\n  const parts = new DataURLParts(dataUrl);\n  return parts.contentType;\n}\n\nfunction endsWith(s: string, end: string): boolean {\n  const longEnough = s.length >= end.length;\n  if (!longEnough) {\n    return false;\n  }\n\n  return s.substring(s.length - end.length) === end;\n}\n", "/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n/**\n * @file Provides a Blob-like wrapper for various binary types (including the\n * native Blob type). This makes it possible to upload types like ArrayBuffers,\n * making uploads possible in environments without the native Blob type.\n */\nimport { sliceBlob, getBlob } from './fs';\nimport { StringFormat, dataFromString } from './string';\nimport { isNativeBlob, isNativeBlobDefined, isString } from './type';\n\n/**\n * @param opt_elideCopy - If true, doesn't copy mutable input data\n *     (e.g. Uint8Arrays). Pass true only if you know the objects will not be\n *     modified after this blob's construction.\n *\n * @internal\n */\nexport class FbsBlob {\n  private data_!: Blob | Uint8Array;\n  private size_: number;\n  private type_: string;\n\n  constructor(data: Blob | Uint8Array | ArrayBuffer, elideCopy?: boolean) {\n    let size: number = 0;\n    let blobType: string = '';\n    if (isNativeBlob(data)) {\n      this.data_ = data as Blob;\n      size = (data as Blob).size;\n      blobType = (data as Blob).type;\n    } else if (data instanceof ArrayBuffer) {\n      if (elideCopy) {\n        this.data_ = new Uint8Array(data);\n      } else {\n        this.data_ = new Uint8Array(data.byteLength);\n        this.data_.set(new Uint8Array(data));\n      }\n      size = this.data_.length;\n    } else if (data instanceof Uint8Array) {\n      if (elideCopy) {\n        this.data_ = data as Uint8Array;\n      } else {\n        this.data_ = new Uint8Array(data.length);\n        this.data_.set(data as Uint8Array);\n      }\n      size = data.length;\n    }\n    this.size_ = size;\n    this.type_ = blobType;\n  }\n\n  size(): number {\n    return this.size_;\n  }\n\n  type(): string {\n    return this.type_;\n  }\n\n  slice(startByte: number, endByte: number): FbsBlob | null {\n    if (isNativeBlob(this.data_)) {\n      const realBlob = this.data_ as Blob;\n      const sliced = sliceBlob(realBlob, startByte, endByte);\n      if (sliced === null) {\n        return null;\n      }\n      return new FbsBlob(sliced);\n    } else {\n      const slice = new Uint8Array(\n        (this.data_ as Uint8Array).buffer,\n        startByte,\n        endByte - startByte\n      );\n      return new FbsBlob(slice, true);\n    }\n  }\n\n  static getBlob(...args: Array<string | FbsBlob>): FbsBlob | null {\n    if (isNativeBlobDefined()) {\n      const blobby: Array<Blob | Uint8Array | string> = args.map(\n        (val: string | FbsBlob): Blob | Uint8Array | string => {\n          if (val instanceof FbsBlob) {\n            return val.data_;\n          } else {\n            return val;\n          }\n        }\n      );\n      return new FbsBlob(getBlob.apply(null, blobby));\n    } else {\n      const uint8Arrays: Uint8Array[] = args.map(\n        (val: string | FbsBlob): Uint8Array => {\n          if (isString(val)) {\n            return dataFromString(StringFormat.RAW, val as string).data;\n          } else {\n            // Blobs don't exist, so this has to be a Uint8Array.\n            return (val as FbsBlob).data_ as Uint8Array;\n          }\n        }\n      );\n      let finalLength = 0;\n      uint8Arrays.forEach((array: Uint8Array): void => {\n        finalLength += array.byteLength;\n      });\n      const merged = new Uint8Array(finalLength);\n      let index = 0;\n      uint8Arrays.forEach((array: Uint8Array) => {\n        for (let i = 0; i < array.length; i++) {\n          merged[index++] = array[i];\n        }\n      });\n      return new FbsBlob(merged, true);\n    }\n  }\n\n  uploadData(): Blob | Uint8Array {\n    return this.data_;\n  }\n}\n", "/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nimport { isNonArrayObject } from './type';\n\n/**\n * Returns the Object resulting from parsing the given JSON, or null if the\n * given string does not represent a JSON object.\n */\nexport function jsonObjectOrNull(\n  s: string\n): { [name: string]: unknown } | null {\n  let obj;\n  try {\n    obj = JSON.parse(s);\n  } catch (e) {\n    return null;\n  }\n  if (isNonArrayObject(obj)) {\n    return obj;\n  } else {\n    return null;\n  }\n}\n", "/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n/**\n * @fileoverview Contains helper methods for manipulating paths.\n */\n\n/**\n * @return Null if the path is already at the root.\n */\nexport function parent(path: string): string | null {\n  if (path.length === 0) {\n    return null;\n  }\n  const index = path.lastIndexOf('/');\n  if (index === -1) {\n    return '';\n  }\n  const newPath = path.slice(0, index);\n  return newPath;\n}\n\nexport function child(path: string, childPath: string): string {\n  const canonicalChildPath = childPath\n    .split('/')\n    .filter(component => component.length > 0)\n    .join('/');\n  if (path.length === 0) {\n    return canonicalChildPath;\n  } else {\n    return path + '/' + canonicalChildPath;\n  }\n}\n\n/**\n * Returns the last component of a path.\n * '/foo/bar' -> 'bar'\n * '/foo/bar/baz/' -> 'baz/'\n * '/a' -> 'a'\n */\nexport function lastComponent(path: string): string {\n  const index = path.lastIndexOf('/', path.length - 2);\n  if (index === -1) {\n    return path;\n  } else {\n    return path.slice(index + 1);\n  }\n}\n", "/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n/**\n * @fileoverview Documentation for the metadata format\n */\nimport { Metadata } from '../metadata';\n\nimport { jsonObjectOrNull } from './json';\nimport { Location } from './location';\nimport { lastComponent } from './path';\nimport { isString } from './type';\nimport { makeUrl, makeQueryString } from './url';\nimport { Reference } from '../reference';\nimport { FirebaseStorageImpl } from '../service';\n\nexport function noXform_<T>(metadata: Metadata, value: T): T {\n  return value;\n}\n\nclass Mapping<T> {\n  local: string;\n  writable: boolean;\n  xform: (p1: Metadata, p2?: T) => T | undefined;\n\n  constructor(\n    public server: string,\n    local?: string | null,\n    writable?: boolean,\n    xform?: ((p1: Metadata, p2?: T) => T | undefined) | null\n  ) {\n    this.local = local || server;\n    this.writable = !!writable;\n    this.xform = xform || noXform_;\n  }\n}\ntype Mappings = Array<Mapping<string> | Mapping<number>>;\n\nexport { Mappings };\n\nlet mappings_: Mappings | null = null;\n\nexport function xformPath(fullPath: string | undefined): string | undefined {\n  if (!isString(fullPath) || fullPath.length < 2) {\n    return fullPath;\n  } else {\n    return lastComponent(fullPath);\n  }\n}\n\nexport function getMappings(): Mappings {\n  if (mappings_) {\n    return mappings_;\n  }\n  const mappings: Mappings = [];\n  mappings.push(new Mapping<string>('bucket'));\n  mappings.push(new Mapping<string>('generation'));\n  mappings.push(new Mapping<string>('metageneration'));\n  mappings.push(new Mapping<string>('name', 'fullPath', true));\n\n  function mappingsXformPath(\n    _metadata: Metadata,\n    fullPath: string | undefined\n  ): string | undefined {\n    return xformPath(fullPath);\n  }\n  const nameMapping = new Mapping<string>('name');\n  nameMapping.xform = mappingsXformPath;\n  mappings.push(nameMapping);\n\n  /**\n   * Coerces the second param to a number, if it is defined.\n   */\n  function xformSize(\n    _metadata: Metadata,\n    size?: number | string\n  ): number | undefined {\n    if (size !== undefined) {\n      return Number(size);\n    } else {\n      return size;\n    }\n  }\n  const sizeMapping = new Mapping<number>('size');\n  sizeMapping.xform = xformSize;\n  mappings.push(sizeMapping);\n  mappings.push(new Mapping<number>('timeCreated'));\n  mappings.push(new Mapping<string>('updated'));\n  mappings.push(new Mapping<string>('md5Hash', null, true));\n  mappings.push(new Mapping<string>('cacheControl', null, true));\n  mappings.push(new Mapping<string>('contentDisposition', null, true));\n  mappings.push(new Mapping<string>('contentEncoding', null, true));\n  mappings.push(new Mapping<string>('contentLanguage', null, true));\n  mappings.push(new Mapping<string>('contentType', null, true));\n  mappings.push(new Mapping<string>('metadata', 'customMetadata', true));\n  mappings_ = mappings;\n  return mappings_;\n}\n\nexport function addRef(metadata: Metadata, service: FirebaseStorageImpl): void {\n  function generateRef(): Reference {\n    const bucket: string = metadata['bucket'] as string;\n    const path: string = metadata['fullPath'] as string;\n    const loc = new Location(bucket, path);\n    return service._makeStorageReference(loc);\n  }\n  Object.defineProperty(metadata, 'ref', { get: generateRef });\n}\n\nexport function fromResource(\n  service: FirebaseStorageImpl,\n  resource: { [name: string]: unknown },\n  mappings: Mappings\n): Metadata {\n  const metadata: Metadata = {} as Metadata;\n  metadata['type'] = 'file';\n  const len = mappings.length;\n  for (let i = 0; i < len; i++) {\n    const mapping = mappings[i];\n    metadata[mapping.local] = (mapping as Mapping<unknown>).xform(\n      metadata,\n      resource[mapping.server]\n    );\n  }\n  addRef(metadata, service);\n  return metadata;\n}\n\nexport function fromResourceString(\n  service: FirebaseStorageImpl,\n  resourceString: string,\n  mappings: Mappings\n): Metadata | null {\n  const obj = jsonObjectOrNull(resourceString);\n  if (obj === null) {\n    return null;\n  }\n  const resource = obj as Metadata;\n  return fromResource(service, resource, mappings);\n}\n\nexport function downloadUrlFromResourceString(\n  metadata: Metadata,\n  resourceString: string,\n  host: string,\n  protocol: string\n): string | null {\n  const obj = jsonObjectOrNull(resourceString);\n  if (obj === null) {\n    return null;\n  }\n  if (!isString(obj['downloadTokens'])) {\n    // This can happen if objects are uploaded through GCS and retrieved\n    // through list, so we don't want to throw an Error.\n    return null;\n  }\n  const tokens: string = obj['downloadTokens'] as string;\n  if (tokens.length === 0) {\n    return null;\n  }\n  const encode = encodeURIComponent;\n  const tokensList = tokens.split(',');\n  const urls = tokensList.map((token: string): string => {\n    const bucket: string = metadata['bucket'] as string;\n    const path: string = metadata['fullPath'] as string;\n    const urlPart = '/b/' + encode(bucket) + '/o/' + encode(path);\n    const base = makeUrl(urlPart, host, protocol);\n    const queryString = makeQueryString({\n      alt: 'media',\n      token\n    });\n    return base + queryString;\n  });\n  return urls[0];\n}\n\nexport function toResourceString(\n  metadata: Partial<Metadata>,\n  mappings: Mappings\n): string {\n  const resource: {\n    [prop: string]: unknown;\n  } = {};\n  const len = mappings.length;\n  for (let i = 0; i < len; i++) {\n    const mapping = mappings[i];\n    if (mapping.writable) {\n      resource[mapping.server] = metadata[mapping.local];\n    }\n  }\n  return JSON.stringify(resource);\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n/**\n * @fileoverview Documentation for the listOptions and listResult format\n */\nimport { Location } from './location';\nimport { jsonObjectOrNull } from './json';\nimport { ListResult } from '../list';\nimport { FirebaseStorageImpl } from '../service';\n\n/**\n * Represents the simplified object metadata returned by List API.\n * Other fields are filtered because list in Firebase Rules does not grant\n * the permission to read the metadata.\n */\ninterface ListMetadataResponse {\n  name: string;\n  bucket: string;\n}\n\n/**\n * Represents the JSON response of List API.\n */\ninterface ListResultResponse {\n  prefixes: string[];\n  items: ListMetadataResponse[];\n  nextPageToken?: string;\n}\n\nconst PREFIXES_KEY = 'prefixes';\nconst ITEMS_KEY = 'items';\n\nfunction fromBackendResponse(\n  service: FirebaseStorageImpl,\n  bucket: string,\n  resource: ListResultResponse\n): ListResult {\n  const listResult: ListResult = {\n    prefixes: [],\n    items: [],\n    nextPageToken: resource['nextPageToken']\n  };\n  if (resource[PREFIXES_KEY]) {\n    for (const path of resource[PREFIXES_KEY]) {\n      const pathWithoutTrailingSlash = path.replace(/\\/$/, '');\n      const reference = service._makeStorageReference(\n        new Location(bucket, pathWithoutTrailingSlash)\n      );\n      listResult.prefixes.push(reference);\n    }\n  }\n\n  if (resource[ITEMS_KEY]) {\n    for (const item of resource[ITEMS_KEY]) {\n      const reference = service._makeStorageReference(\n        new Location(bucket, item['name'])\n      );\n      listResult.items.push(reference);\n    }\n  }\n  return listResult;\n}\n\nexport function fromResponseString(\n  service: FirebaseStorageImpl,\n  bucket: string,\n  resourceString: string\n): ListResult | null {\n  const obj = jsonObjectOrNull(resourceString);\n  if (obj === null) {\n    return null;\n  }\n  const resource = obj as unknown as ListResultResponse;\n  return fromBackendResponse(service, bucket, resource);\n}\n", "/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nimport { StorageError } from './error';\nimport { Headers, Connection, ConnectionType } from './connection';\n\n/**\n * Type for url params stored in RequestInfo.\n */\nexport interface UrlParams {\n  [name: string]: string | number;\n}\n\n/**\n * A function that converts a server response to the API type expected by the\n * SDK.\n *\n * @param I - the type of the backend's network response\n * @param O - the output response type used by the rest of the SDK.\n */\nexport type RequestHandler<I extends ConnectionType, O> = (\n  connection: Connection<I>,\n  response: I\n) => O;\n\n/** A function to handle an error. */\nexport type ErrorHandler = (\n  connection: Connection<ConnectionType>,\n  response: StorageError\n) => StorageError;\n\n/**\n * Contains a fully specified request.\n *\n * @param I - the type of the backend's network response.\n * @param O - the output response type used by the rest of the SDK.\n */\nexport class RequestInfo<I extends ConnectionType, O> {\n  urlParams: UrlParams = {};\n  headers: Headers = {};\n  body: Blob | string | Uint8Array | null = null;\n  errorHandler: ErrorHandler | null = null;\n\n  /**\n   * Called with the current number of bytes uploaded and total size (-1 if not\n   * computable) of the request body (i.e. used to report upload progress).\n   */\n  progressCallback: ((p1: number, p2: number) => void) | null = null;\n  successCodes: number[] = [200];\n  additionalRetryCodes: number[] = [];\n\n  constructor(\n    public url: string,\n    public method: string,\n    /**\n     * Returns the value with which to resolve the request's promise. Only called\n     * if the request is successful. Throw from this function to reject the\n     * returned Request's promise with the thrown error.\n     * Note: The XhrIo passed to this function may be reused after this callback\n     * returns. Do not keep a reference to it in any way.\n     */\n    public handler: RequestHandler<I, O>,\n    public timeout: number\n  ) {}\n}\n", "/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n/**\n * @fileoverview Defines methods for interacting with the network.\n */\n\nimport { Metadata } from '../metadata';\nimport { ListResult } from '../list';\nimport { FbsBlob } from './blob';\nimport {\n  StorageError,\n  cannotSliceBlob,\n  unauthenticated,\n  quotaExceeded,\n  unauthorized,\n  objectNotFound,\n  serverFileWrongSize,\n  unknown,\n  unauthorizedApp\n} from './error';\nimport { Location } from './location';\nimport {\n  Mappings,\n  fromResourceString,\n  downloadUrlFromResourceString,\n  toResourceString\n} from './metadata';\nimport { fromResponseString } from './list';\nimport { RequestInfo, UrlParams } from './requestinfo';\nimport { isString } from './type';\nimport { makeUrl } from './url';\nimport { Connection, ConnectionType } from './connection';\nimport { FirebaseStorageImpl } from '../service';\n\n/**\n * Throws the UNKNOWN StorageError if cndn is false.\n */\nexport function handlerCheck(cndn: boolean): void {\n  if (!cndn) {\n    throw unknown();\n  }\n}\n\nexport function metadataHandler(\n  service: FirebaseStorageImpl,\n  mappings: Mappings\n): (p1: Connection<string>, p2: string) => Metadata {\n  function handler(xhr: Connection<string>, text: string): Metadata {\n    const metadata = fromResourceString(service, text, mappings);\n    handlerCheck(metadata !== null);\n    return metadata as Metadata;\n  }\n  return handler;\n}\n\nexport function listHandler(\n  service: FirebaseStorageImpl,\n  bucket: string\n): (p1: Connection<string>, p2: string) => ListResult {\n  function handler(xhr: Connection<string>, text: string): ListResult {\n    const listResult = fromResponseString(service, bucket, text);\n    handlerCheck(listResult !== null);\n    return listResult as ListResult;\n  }\n  return handler;\n}\n\nexport function downloadUrlHandler(\n  service: FirebaseStorageImpl,\n  mappings: Mappings\n): (p1: Connection<string>, p2: string) => string | null {\n  function handler(xhr: Connection<string>, text: string): string | null {\n    const metadata = fromResourceString(service, text, mappings);\n    handlerCheck(metadata !== null);\n    return downloadUrlFromResourceString(\n      metadata as Metadata,\n      text,\n      service.host,\n      service._protocol\n    );\n  }\n  return handler;\n}\n\nexport function sharedErrorHandler(\n  location: Location\n): (p1: Connection<ConnectionType>, p2: StorageError) => StorageError {\n  function errorHandler(\n    xhr: Connection<ConnectionType>,\n    err: StorageError\n  ): StorageError {\n    let newErr: StorageError;\n    if (xhr.getStatus() === 401) {\n      if (\n        // This exact message string is the only consistent part of the\n        // server's error response that identifies it as an App Check error.\n        xhr.getErrorText().includes('Firebase App Check token is invalid')\n      ) {\n        newErr = unauthorizedApp();\n      } else {\n        newErr = unauthenticated();\n      }\n    } else {\n      if (xhr.getStatus() === 402) {\n        newErr = quotaExceeded(location.bucket);\n      } else {\n        if (xhr.getStatus() === 403) {\n          newErr = unauthorized(location.path);\n        } else {\n          newErr = err;\n        }\n      }\n    }\n    newErr.status = xhr.getStatus();\n    newErr.serverResponse = err.serverResponse;\n    return newErr;\n  }\n  return errorHandler;\n}\n\nexport function objectErrorHandler(\n  location: Location\n): (p1: Connection<ConnectionType>, p2: StorageError) => StorageError {\n  const shared = sharedErrorHandler(location);\n\n  function errorHandler(\n    xhr: Connection<ConnectionType>,\n    err: StorageError\n  ): StorageError {\n    let newErr = shared(xhr, err);\n    if (xhr.getStatus() === 404) {\n      newErr = objectNotFound(location.path);\n    }\n    newErr.serverResponse = err.serverResponse;\n    return newErr;\n  }\n  return errorHandler;\n}\n\nexport function getMetadata(\n  service: FirebaseStorageImpl,\n  location: Location,\n  mappings: Mappings\n): RequestInfo<string, Metadata> {\n  const urlPart = location.fullServerUrl();\n  const url = makeUrl(urlPart, service.host, service._protocol);\n  const method = 'GET';\n  const timeout = service.maxOperationRetryTime;\n  const requestInfo = new RequestInfo(\n    url,\n    method,\n    metadataHandler(service, mappings),\n    timeout\n  );\n  requestInfo.errorHandler = objectErrorHandler(location);\n  return requestInfo;\n}\n\nexport function list(\n  service: FirebaseStorageImpl,\n  location: Location,\n  delimiter?: string,\n  pageToken?: string | null,\n  maxResults?: number | null\n): RequestInfo<string, ListResult> {\n  const urlParams: UrlParams = {};\n  if (location.isRoot) {\n    urlParams['prefix'] = '';\n  } else {\n    urlParams['prefix'] = location.path + '/';\n  }\n  if (delimiter && delimiter.length > 0) {\n    urlParams['delimiter'] = delimiter;\n  }\n  if (pageToken) {\n    urlParams['pageToken'] = pageToken;\n  }\n  if (maxResults) {\n    urlParams['maxResults'] = maxResults;\n  }\n  const urlPart = location.bucketOnlyServerUrl();\n  const url = makeUrl(urlPart, service.host, service._protocol);\n  const method = 'GET';\n  const timeout = service.maxOperationRetryTime;\n  const requestInfo = new RequestInfo(\n    url,\n    method,\n    listHandler(service, location.bucket),\n    timeout\n  );\n  requestInfo.urlParams = urlParams;\n  requestInfo.errorHandler = sharedErrorHandler(location);\n  return requestInfo;\n}\n\nexport function getBytes<I extends ConnectionType>(\n  service: FirebaseStorageImpl,\n  location: Location,\n  maxDownloadSizeBytes?: number\n): RequestInfo<I, I> {\n  const urlPart = location.fullServerUrl();\n  const url = makeUrl(urlPart, service.host, service._protocol) + '?alt=media';\n  const method = 'GET';\n  const timeout = service.maxOperationRetryTime;\n  const requestInfo = new RequestInfo(\n    url,\n    method,\n    (_: Connection<I>, data: I) => data,\n    timeout\n  );\n  requestInfo.errorHandler = objectErrorHandler(location);\n  if (maxDownloadSizeBytes !== undefined) {\n    requestInfo.headers['Range'] = `bytes=0-${maxDownloadSizeBytes}`;\n    requestInfo.successCodes = [200 /* OK */, 206 /* Partial Content */];\n  }\n  return requestInfo;\n}\n\nexport function getDownloadUrl(\n  service: FirebaseStorageImpl,\n  location: Location,\n  mappings: Mappings\n): RequestInfo<string, string | null> {\n  const urlPart = location.fullServerUrl();\n  const url = makeUrl(urlPart, service.host, service._protocol);\n  const method = 'GET';\n  const timeout = service.maxOperationRetryTime;\n  const requestInfo = new RequestInfo(\n    url,\n    method,\n    downloadUrlHandler(service, mappings),\n    timeout\n  );\n  requestInfo.errorHandler = objectErrorHandler(location);\n  return requestInfo;\n}\n\nexport function updateMetadata(\n  service: FirebaseStorageImpl,\n  location: Location,\n  metadata: Partial<Metadata>,\n  mappings: Mappings\n): RequestInfo<string, Metadata> {\n  const urlPart = location.fullServerUrl();\n  const url = makeUrl(urlPart, service.host, service._protocol);\n  const method = 'PATCH';\n  const body = toResourceString(metadata, mappings);\n  const headers = { 'Content-Type': 'application/json; charset=utf-8' };\n  const timeout = service.maxOperationRetryTime;\n  const requestInfo = new RequestInfo(\n    url,\n    method,\n    metadataHandler(service, mappings),\n    timeout\n  );\n  requestInfo.headers = headers;\n  requestInfo.body = body;\n  requestInfo.errorHandler = objectErrorHandler(location);\n  return requestInfo;\n}\n\nexport function deleteObject(\n  service: FirebaseStorageImpl,\n  location: Location\n): RequestInfo<string, void> {\n  const urlPart = location.fullServerUrl();\n  const url = makeUrl(urlPart, service.host, service._protocol);\n  const method = 'DELETE';\n  const timeout = service.maxOperationRetryTime;\n\n  function handler(_xhr: Connection<string>, _text: string): void {}\n  const requestInfo = new RequestInfo(url, method, handler, timeout);\n  requestInfo.successCodes = [200, 204];\n  requestInfo.errorHandler = objectErrorHandler(location);\n  return requestInfo;\n}\n\nexport function determineContentType_(\n  metadata: Metadata | null,\n  blob: FbsBlob | null\n): string {\n  return (\n    (metadata && metadata['contentType']) ||\n    (blob && blob.type()) ||\n    'application/octet-stream'\n  );\n}\n\nexport function metadataForUpload_(\n  location: Location,\n  blob: FbsBlob,\n  metadata?: Metadata | null\n): Metadata {\n  const metadataClone = Object.assign({}, metadata);\n  metadataClone['fullPath'] = location.path;\n  metadataClone['size'] = blob.size();\n  if (!metadataClone['contentType']) {\n    metadataClone['contentType'] = determineContentType_(null, blob);\n  }\n  return metadataClone;\n}\n\n/**\n * Prepare RequestInfo for uploads as Content-Type: multipart.\n */\nexport function multipartUpload(\n  service: FirebaseStorageImpl,\n  location: Location,\n  mappings: Mappings,\n  blob: FbsBlob,\n  metadata?: Metadata | null\n): RequestInfo<string, Metadata> {\n  const urlPart = location.bucketOnlyServerUrl();\n  const headers: { [prop: string]: string } = {\n    'X-Goog-Upload-Protocol': 'multipart'\n  };\n\n  function genBoundary(): string {\n    let str = '';\n    for (let i = 0; i < 2; i++) {\n      str = str + Math.random().toString().slice(2);\n    }\n    return str;\n  }\n  const boundary = genBoundary();\n  headers['Content-Type'] = 'multipart/related; boundary=' + boundary;\n  const metadata_ = metadataForUpload_(location, blob, metadata);\n  const metadataString = toResourceString(metadata_, mappings);\n  const preBlobPart =\n    '--' +\n    boundary +\n    '\\r\\n' +\n    'Content-Type: application/json; charset=utf-8\\r\\n\\r\\n' +\n    metadataString +\n    '\\r\\n--' +\n    boundary +\n    '\\r\\n' +\n    'Content-Type: ' +\n    metadata_['contentType'] +\n    '\\r\\n\\r\\n';\n  const postBlobPart = '\\r\\n--' + boundary + '--';\n  const body = FbsBlob.getBlob(preBlobPart, blob, postBlobPart);\n  if (body === null) {\n    throw cannotSliceBlob();\n  }\n  const urlParams: UrlParams = { name: metadata_['fullPath']! };\n  const url = makeUrl(urlPart, service.host, service._protocol);\n  const method = 'POST';\n  const timeout = service.maxUploadRetryTime;\n  const requestInfo = new RequestInfo(\n    url,\n    method,\n    metadataHandler(service, mappings),\n    timeout\n  );\n  requestInfo.urlParams = urlParams;\n  requestInfo.headers = headers;\n  requestInfo.body = body.uploadData();\n  requestInfo.errorHandler = sharedErrorHandler(location);\n  return requestInfo;\n}\n\n/**\n * @param current The number of bytes that have been uploaded so far.\n * @param total The total number of bytes in the upload.\n * @param opt_finalized True if the server has finished the upload.\n * @param opt_metadata The upload metadata, should\n *     only be passed if opt_finalized is true.\n */\nexport class ResumableUploadStatus {\n  finalized: boolean;\n  metadata: Metadata | null;\n\n  constructor(\n    public current: number,\n    public total: number,\n    finalized?: boolean,\n    metadata?: Metadata | null\n  ) {\n    this.finalized = !!finalized;\n    this.metadata = metadata || null;\n  }\n}\n\nexport function checkResumeHeader_(\n  xhr: Connection<string>,\n  allowed?: string[]\n): string {\n  let status: string | null = null;\n  try {\n    status = xhr.getResponseHeader('X-Goog-Upload-Status');\n  } catch (e) {\n    handlerCheck(false);\n  }\n  const allowedStatus = allowed || ['active'];\n  handlerCheck(!!status && allowedStatus.indexOf(status) !== -1);\n  return status as string;\n}\n\nexport function createResumableUpload(\n  service: FirebaseStorageImpl,\n  location: Location,\n  mappings: Mappings,\n  blob: FbsBlob,\n  metadata?: Metadata | null\n): RequestInfo<string, string> {\n  const urlPart = location.bucketOnlyServerUrl();\n  const metadataForUpload = metadataForUpload_(location, blob, metadata);\n  const urlParams: UrlParams = { name: metadataForUpload['fullPath']! };\n  const url = makeUrl(urlPart, service.host, service._protocol);\n  const method = 'POST';\n  const headers = {\n    'X-Goog-Upload-Protocol': 'resumable',\n    'X-Goog-Upload-Command': 'start',\n    'X-Goog-Upload-Header-Content-Length': `${blob.size()}`,\n    'X-Goog-Upload-Header-Content-Type': metadataForUpload['contentType']!,\n    'Content-Type': 'application/json; charset=utf-8'\n  };\n  const body = toResourceString(metadataForUpload, mappings);\n  const timeout = service.maxUploadRetryTime;\n\n  function handler(xhr: Connection<string>): string {\n    checkResumeHeader_(xhr);\n    let url;\n    try {\n      url = xhr.getResponseHeader('X-Goog-Upload-URL');\n    } catch (e) {\n      handlerCheck(false);\n    }\n    handlerCheck(isString(url));\n    return url as string;\n  }\n  const requestInfo = new RequestInfo(url, method, handler, timeout);\n  requestInfo.urlParams = urlParams;\n  requestInfo.headers = headers;\n  requestInfo.body = body;\n  requestInfo.errorHandler = sharedErrorHandler(location);\n  return requestInfo;\n}\n\n/**\n * @param url From a call to fbs.requests.createResumableUpload.\n */\nexport function getResumableUploadStatus(\n  service: FirebaseStorageImpl,\n  location: Location,\n  url: string,\n  blob: FbsBlob\n): RequestInfo<string, ResumableUploadStatus> {\n  const headers = { 'X-Goog-Upload-Command': 'query' };\n\n  function handler(xhr: Connection<string>): ResumableUploadStatus {\n    const status = checkResumeHeader_(xhr, ['active', 'final']);\n    let sizeString: string | null = null;\n    try {\n      sizeString = xhr.getResponseHeader('X-Goog-Upload-Size-Received');\n    } catch (e) {\n      handlerCheck(false);\n    }\n\n    if (!sizeString) {\n      // null or empty string\n      handlerCheck(false);\n    }\n\n    const size = Number(sizeString);\n    handlerCheck(!isNaN(size));\n    return new ResumableUploadStatus(size, blob.size(), status === 'final');\n  }\n  const method = 'POST';\n  const timeout = service.maxUploadRetryTime;\n  const requestInfo = new RequestInfo(url, method, handler, timeout);\n  requestInfo.headers = headers;\n  requestInfo.errorHandler = sharedErrorHandler(location);\n  return requestInfo;\n}\n\n/**\n * Any uploads via the resumable upload API must transfer a number of bytes\n * that is a multiple of this number.\n */\nexport const RESUMABLE_UPLOAD_CHUNK_SIZE: number = 256 * 1024;\n\n/**\n * @param url From a call to fbs.requests.createResumableUpload.\n * @param chunkSize Number of bytes to upload.\n * @param status The previous status.\n *     If not passed or null, we start from the beginning.\n * @throws fbs.Error If the upload is already complete, the passed in status\n *     has a final size inconsistent with the blob, or the blob cannot be sliced\n *     for upload.\n */\nexport function continueResumableUpload(\n  location: Location,\n  service: FirebaseStorageImpl,\n  url: string,\n  blob: FbsBlob,\n  chunkSize: number,\n  mappings: Mappings,\n  status?: ResumableUploadStatus | null,\n  progressCallback?: ((p1: number, p2: number) => void) | null\n): RequestInfo<string, ResumableUploadStatus> {\n  // TODO(andysoto): standardize on internal asserts\n  // assert(!(opt_status && opt_status.finalized));\n  const status_ = new ResumableUploadStatus(0, 0);\n  if (status) {\n    status_.current = status.current;\n    status_.total = status.total;\n  } else {\n    status_.current = 0;\n    status_.total = blob.size();\n  }\n  if (blob.size() !== status_.total) {\n    throw serverFileWrongSize();\n  }\n  const bytesLeft = status_.total - status_.current;\n  let bytesToUpload = bytesLeft;\n  if (chunkSize > 0) {\n    bytesToUpload = Math.min(bytesToUpload, chunkSize);\n  }\n  const startByte = status_.current;\n  const endByte = startByte + bytesToUpload;\n  let uploadCommand = '';\n  if (bytesToUpload === 0) {\n    uploadCommand = 'finalize';\n  } else if (bytesLeft === bytesToUpload) {\n    uploadCommand = 'upload, finalize';\n  } else {\n    uploadCommand = 'upload';\n  }\n  const headers = {\n    'X-Goog-Upload-Command': uploadCommand,\n    'X-Goog-Upload-Offset': `${status_.current}`\n  };\n  const body = blob.slice(startByte, endByte);\n  if (body === null) {\n    throw cannotSliceBlob();\n  }\n\n  function handler(\n    xhr: Connection<string>,\n    text: string\n  ): ResumableUploadStatus {\n    // TODO(andysoto): Verify the MD5 of each uploaded range:\n    // the 'x-range-md5' header comes back with status code 308 responses.\n    // We'll only be able to bail out though, because you can't re-upload a\n    // range that you previously uploaded.\n    const uploadStatus = checkResumeHeader_(xhr, ['active', 'final']);\n    const newCurrent = status_.current + bytesToUpload;\n    const size = blob.size();\n    let metadata;\n    if (uploadStatus === 'final') {\n      metadata = metadataHandler(service, mappings)(xhr, text);\n    } else {\n      metadata = null;\n    }\n    return new ResumableUploadStatus(\n      newCurrent,\n      size,\n      uploadStatus === 'final',\n      metadata\n    );\n  }\n  const method = 'POST';\n  const timeout = service.maxUploadRetryTime;\n  const requestInfo = new RequestInfo(url, method, handler, timeout);\n  requestInfo.headers = headers;\n  requestInfo.body = body.uploadData();\n  requestInfo.progressCallback = progressCallback || null;\n  requestInfo.errorHandler = sharedErrorHandler(location);\n  return requestInfo;\n}\n", "/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n/**\n * @fileoverview Enumerations used for upload tasks.\n */\n\n/**\n * An event that is triggered on a task.\n * @internal\n */\nexport type TaskEvent = string;\n\n/**\n * An event that is triggered on a task.\n * @internal\n */\nexport const TaskEvent = {\n  /**\n   * For this event,\n   * <ul>\n   *   <li>The `next` function is triggered on progress updates and when the\n   *       task is paused/resumed with an `UploadTaskSnapshot` as the first\n   *       argument.</li>\n   *   <li>The `error` function is triggered if the upload is canceled or fails\n   *       for another reason.</li>\n   *   <li>The `complete` function is triggered if the upload completes\n   *       successfully.</li>\n   * </ul>\n   */\n  STATE_CHANGED: 'state_changed'\n};\n\n/**\n * Internal enum for task state.\n */\nexport const enum InternalTaskState {\n  RUNNING = 'running',\n  PAUSING = 'pausing',\n  PAUSED = 'paused',\n  SUCCESS = 'success',\n  CANCELING = 'canceling',\n  CANCELED = 'canceled',\n  ERROR = 'error'\n}\n\n/**\n * Represents the current state of a running upload.\n * @internal\n */\nexport type TaskState = (typeof TaskState)[keyof typeof TaskState];\n\n// type keys = keyof TaskState\n/**\n * Represents the current state of a running upload.\n * @internal\n */\nexport const TaskState = {\n  /** The task is currently transferring data. */\n  RUNNING: 'running',\n\n  /** The task was paused by the user. */\n  PAUSED: 'paused',\n\n  /** The task completed successfully. */\n  SUCCESS: 'success',\n\n  /** The task was canceled. */\n  CANCELED: 'canceled',\n\n  /** The task failed with an error. */\n  ERROR: 'error'\n} as const;\n\nexport function taskStateFromInternalTaskState(\n  state: InternalTaskState\n): TaskState {\n  switch (state) {\n    case InternalTaskState.RUNNING:\n    case InternalTaskState.PAUSING:\n    case InternalTaskState.CANCELING:\n      return TaskState.RUNNING;\n    case InternalTaskState.PAUSED:\n      return TaskState.PAUSED;\n    case InternalTaskState.SUCCESS:\n      return TaskState.SUCCESS;\n    case InternalTaskState.CANCELED:\n      return TaskState.CANCELED;\n    case InternalTaskState.ERROR:\n      return TaskState.ERROR;\n    default:\n      // TODO(andysoto): assert(false);\n      return TaskState.ERROR;\n  }\n}\n", "/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nimport { isFunction } from './type';\nimport { StorageError } from './error';\n\n/**\n * Function that is called once for each value in a stream of values.\n */\nexport type NextFn<T> = (value: T) => void;\n\n/**\n * A function that is called with a `StorageError`\n * if the event stream ends due to an error.\n */\nexport type ErrorFn = (error: StorageError) => void;\n\n/**\n * A function that is called if the event stream ends normally.\n */\nexport type CompleteFn = () => void;\n\n/**\n * Unsubscribes from a stream.\n */\nexport type Unsubscribe = () => void;\n\n/**\n * An observer identical to the `Observer` defined in packages/util except the\n * error passed into the ErrorFn is specifically a `StorageError`.\n */\nexport interface StorageObserver<T> {\n  /**\n   * Function that is called once for each value in the event stream.\n   */\n  next?: NextFn<T>;\n  /**\n   * A function that is called with a `StorageError`\n   * if the event stream ends due to an error.\n   */\n  error?: ErrorFn;\n  /**\n   * A function that is called if the event stream ends normally.\n   */\n  complete?: CompleteFn;\n}\n\n/**\n * Subscribes to an event stream.\n */\nexport type Subscribe<T> = (\n  next?: NextFn<T> | StorageObserver<T>,\n  error?: ErrorFn,\n  complete?: CompleteFn\n) => Unsubscribe;\n\nexport class Observer<T> implements StorageObserver<T> {\n  next?: NextFn<T>;\n  error?: ErrorFn;\n  complete?: CompleteFn;\n\n  constructor(\n    nextOrObserver?: NextFn<T> | StorageObserver<T>,\n    error?: ErrorFn,\n    complete?: CompleteFn\n  ) {\n    const asFunctions =\n      isFunction(nextOrObserver) || error != null || complete != null;\n    if (asFunctions) {\n      this.next = nextOrObserver as NextFn<T>;\n      this.error = error ?? undefined;\n      this.complete = complete ?? undefined;\n    } else {\n      const observer = nextOrObserver as {\n        next?: NextFn<T>;\n        error?: ErrorFn;\n        complete?: CompleteFn;\n      };\n      this.next = observer.next;\n      this.error = observer.error;\n      this.complete = observer.complete;\n    }\n  }\n}\n", "/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n/**\n * Returns a function that invokes f with its arguments asynchronously as a\n * microtask, i.e. as soon as possible after the current script returns back\n * into browser code.\n */\n// eslint-disable-next-line @typescript-eslint/ban-types\nexport function async(f: Function): Function {\n  return (...argsToForward: unknown[]) => {\n    // eslint-disable-next-line @typescript-eslint/no-floating-promises\n    Promise.resolve().then(() => f(...argsToForward));\n  };\n}\n", "/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {\n  Connection,\n  ConnectionType,\n  ErrorCode,\n  Headers\n} from '../../implementation/connection';\nimport { internalError } from '../../implementation/error';\n\n/** An override for the text-based Connection. Used in tests. */\nlet textFactoryOverride: (() => Connection<string>) | null = null;\n\n/**\n * Network layer for browsers. We use this instead of goog.net.XhrIo because\n * goog.net.XhrIo is hyuuuuge and doesn't work in React Native on Android.\n */\nabstract class XhrConnection<T extends ConnectionType>\n  implements Connection<T>\n{\n  protected xhr_: XMLHttpRequest;\n  private errorCode_: ErrorCode;\n  private sendPromise_: Promise<void>;\n  protected sent_: boolean = false;\n\n  constructor() {\n    this.xhr_ = new XMLHttpRequest();\n    this.initXhr();\n    this.errorCode_ = ErrorCode.NO_ERROR;\n    this.sendPromise_ = new Promise(resolve => {\n      this.xhr_.addEventListener('abort', () => {\n        this.errorCode_ = ErrorCode.ABORT;\n        resolve();\n      });\n      this.xhr_.addEventListener('error', () => {\n        this.errorCode_ = ErrorCode.NETWORK_ERROR;\n        resolve();\n      });\n      this.xhr_.addEventListener('load', () => {\n        resolve();\n      });\n    });\n  }\n\n  abstract initXhr(): void;\n\n  send(\n    url: string,\n    method: string,\n    body?: ArrayBufferView | Blob | string,\n    headers?: Headers\n  ): Promise<void> {\n    if (this.sent_) {\n      throw internalError('cannot .send() more than once');\n    }\n    this.sent_ = true;\n    this.xhr_.open(method, url, true);\n    if (headers !== undefined) {\n      for (const key in headers) {\n        if (headers.hasOwnProperty(key)) {\n          this.xhr_.setRequestHeader(key, headers[key].toString());\n        }\n      }\n    }\n    if (body !== undefined) {\n      this.xhr_.send(body);\n    } else {\n      this.xhr_.send();\n    }\n    return this.sendPromise_;\n  }\n\n  getErrorCode(): ErrorCode {\n    if (!this.sent_) {\n      throw internalError('cannot .getErrorCode() before sending');\n    }\n    return this.errorCode_;\n  }\n\n  getStatus(): number {\n    if (!this.sent_) {\n      throw internalError('cannot .getStatus() before sending');\n    }\n    try {\n      return this.xhr_.status;\n    } catch (e) {\n      return -1;\n    }\n  }\n\n  getResponse(): T {\n    if (!this.sent_) {\n      throw internalError('cannot .getResponse() before sending');\n    }\n    return this.xhr_.response;\n  }\n\n  getErrorText(): string {\n    if (!this.sent_) {\n      throw internalError('cannot .getErrorText() before sending');\n    }\n    return this.xhr_.statusText;\n  }\n\n  /** Aborts the request. */\n  abort(): void {\n    this.xhr_.abort();\n  }\n\n  getResponseHeader(header: string): string | null {\n    return this.xhr_.getResponseHeader(header);\n  }\n\n  addUploadProgressListener(listener: (p1: ProgressEvent) => void): void {\n    if (this.xhr_.upload != null) {\n      this.xhr_.upload.addEventListener('progress', listener);\n    }\n  }\n\n  removeUploadProgressListener(listener: (p1: ProgressEvent) => void): void {\n    if (this.xhr_.upload != null) {\n      this.xhr_.upload.removeEventListener('progress', listener);\n    }\n  }\n}\n\nexport class XhrTextConnection extends XhrConnection<string> {\n  initXhr(): void {\n    this.xhr_.responseType = 'text';\n  }\n}\n\nexport function newTextConnection(): Connection<string> {\n  return textFactoryOverride ? textFactoryOverride() : new XhrTextConnection();\n}\n\nexport class XhrBytesConnection extends XhrConnection<ArrayBuffer> {\n  private data_?: ArrayBuffer;\n\n  initXhr(): void {\n    this.xhr_.responseType = 'arraybuffer';\n  }\n}\n\nexport function newBytesConnection(): Connection<ArrayBuffer> {\n  return new XhrBytesConnection();\n}\n\nexport class XhrBlobConnection extends XhrConnection<Blob> {\n  initXhr(): void {\n    this.xhr_.responseType = 'blob';\n  }\n}\n\nexport function newBlobConnection(): Connection<Blob> {\n  return new XhrBlobConnection();\n}\n\nexport function newStreamConnection(): Connection<ReadableStream> {\n  throw new Error('Streams are only supported on Node');\n}\n\nexport function injectTestConnection(\n  factory: (() => Connection<string>) | null\n): void {\n  textFactoryOverride = factory;\n}\n", "/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n * @fileoverview Defines types for interacting with blob transfer tasks.\n */\n\nimport { FbsBlob } from './implementation/blob';\nimport {\n  canceled,\n  StorageErrorCode,\n  StorageError,\n  retryLimitExceeded\n} from './implementation/error';\nimport {\n  InternalTaskState,\n  TaskEvent,\n  TaskState,\n  taskStateFromInternalTaskState\n} from './implementation/taskenums';\nimport { Metadata } from './metadata';\nimport {\n  Observer,\n  Subscribe,\n  Unsubscribe,\n  StorageObserver as StorageObserverInternal,\n  NextFn\n} from './implementation/observer';\nimport { Request } from './implementation/request';\nimport { UploadTaskSnapshot, StorageObserver } from './public-types';\nimport { async as fbsAsync } from './implementation/async';\nimport { Mappings, getMappings } from './implementation/metadata';\nimport {\n  createResumableUpload,\n  getResumableUploadStatus,\n  RESUMABLE_UPLOAD_CHUNK_SIZE,\n  ResumableUploadStatus,\n  continueResumableUpload,\n  getMetadata,\n  multipartUpload\n} from './implementation/requests';\nimport { Reference } from './reference';\nimport { newTextConnection } from './platform/connection';\nimport { isRetryStatusCode } from './implementation/utils';\nimport { CompleteFn } from '@firebase/util';\nimport { DEFAULT_MIN_SLEEP_TIME_MILLIS } from './implementation/constants';\n\n/**\n * Represents a blob being uploaded. Can be used to pause/resume/cancel the\n * upload and manage callbacks for various events.\n * @internal\n */\nexport class UploadTask {\n  private _ref: Reference;\n  /**\n   * The data to be uploaded.\n   */\n  _blob: FbsBlob;\n  /**\n   * Metadata related to the upload.\n   */\n  _metadata: Metadata | null;\n  private _mappings: Mappings;\n  /**\n   * Number of bytes transferred so far.\n   */\n  _transferred: number = 0;\n  private _needToFetchStatus: boolean = false;\n  private _needToFetchMetadata: boolean = false;\n  private _observers: Array<StorageObserverInternal<UploadTaskSnapshot>> = [];\n  private _resumable: boolean;\n  /**\n   * Upload state.\n   */\n  _state: InternalTaskState;\n  private _error?: StorageError = undefined;\n  private _uploadUrl?: string = undefined;\n  private _request?: Request<unknown> = undefined;\n  private _chunkMultiplier: number = 1;\n  private _errorHandler: (p1: StorageError) => void;\n  private _metadataErrorHandler: (p1: StorageError) => void;\n  private _resolve?: (p1: UploadTaskSnapshot) => void = undefined;\n  private _reject?: (p1: StorageError) => void = undefined;\n  private pendingTimeout?: ReturnType<typeof setTimeout>;\n  private _promise: Promise<UploadTaskSnapshot>;\n\n  private sleepTime: number;\n\n  private maxSleepTime: number;\n\n  isExponentialBackoffExpired(): boolean {\n    return this.sleepTime > this.maxSleepTime;\n  }\n\n  /**\n   * @param ref - The firebaseStorage.Reference object this task came\n   *     from, untyped to avoid cyclic dependencies.\n   * @param blob - The blob to upload.\n   */\n  constructor(ref: Reference, blob: FbsBlob, metadata: Metadata | null = null) {\n    this._ref = ref;\n    this._blob = blob;\n    this._metadata = metadata;\n    this._mappings = getMappings();\n    this._resumable = this._shouldDoResumable(this._blob);\n    this._state = InternalTaskState.RUNNING;\n    this._errorHandler = error => {\n      this._request = undefined;\n      this._chunkMultiplier = 1;\n      if (error._codeEquals(StorageErrorCode.CANCELED)) {\n        this._needToFetchStatus = true;\n        this.completeTransitions_();\n      } else {\n        const backoffExpired = this.isExponentialBackoffExpired();\n        if (isRetryStatusCode(error.status, [])) {\n          if (backoffExpired) {\n            error = retryLimitExceeded();\n          } else {\n            this.sleepTime = Math.max(\n              this.sleepTime * 2,\n              DEFAULT_MIN_SLEEP_TIME_MILLIS\n            );\n            this._needToFetchStatus = true;\n            this.completeTransitions_();\n            return;\n          }\n        }\n        this._error = error;\n        this._transition(InternalTaskState.ERROR);\n      }\n    };\n    this._metadataErrorHandler = error => {\n      this._request = undefined;\n      if (error._codeEquals(StorageErrorCode.CANCELED)) {\n        this.completeTransitions_();\n      } else {\n        this._error = error;\n        this._transition(InternalTaskState.ERROR);\n      }\n    };\n    this.sleepTime = 0;\n    this.maxSleepTime = this._ref.storage.maxUploadRetryTime;\n    this._promise = new Promise((resolve, reject) => {\n      this._resolve = resolve;\n      this._reject = reject;\n      this._start();\n    });\n\n    // Prevent uncaught rejections on the internal promise from bubbling out\n    // to the top level with a dummy handler.\n    this._promise.then(null, () => {});\n  }\n\n  private _makeProgressCallback(): (p1: number, p2: number) => void {\n    const sizeBefore = this._transferred;\n    return loaded => this._updateProgress(sizeBefore + loaded);\n  }\n\n  private _shouldDoResumable(blob: FbsBlob): boolean {\n    return blob.size() > 256 * 1024;\n  }\n\n  private _start(): void {\n    if (this._state !== InternalTaskState.RUNNING) {\n      // This can happen if someone pauses us in a resume callback, for example.\n      return;\n    }\n    if (this._request !== undefined) {\n      return;\n    }\n    if (this._resumable) {\n      if (this._uploadUrl === undefined) {\n        this._createResumable();\n      } else {\n        if (this._needToFetchStatus) {\n          this._fetchStatus();\n        } else {\n          if (this._needToFetchMetadata) {\n            // Happens if we miss the metadata on upload completion.\n            this._fetchMetadata();\n          } else {\n            this.pendingTimeout = setTimeout(() => {\n              this.pendingTimeout = undefined;\n              this._continueUpload();\n            }, this.sleepTime);\n          }\n        }\n      }\n    } else {\n      this._oneShotUpload();\n    }\n  }\n\n  private _resolveToken(\n    callback: (authToken: string | null, appCheckToken: string | null) => void\n  ): void {\n    // eslint-disable-next-line @typescript-eslint/no-floating-promises\n    Promise.all([\n      this._ref.storage._getAuthToken(),\n      this._ref.storage._getAppCheckToken()\n    ]).then(([authToken, appCheckToken]) => {\n      switch (this._state) {\n        case InternalTaskState.RUNNING:\n          callback(authToken, appCheckToken);\n          break;\n        case InternalTaskState.CANCELING:\n          this._transition(InternalTaskState.CANCELED);\n          break;\n        case InternalTaskState.PAUSING:\n          this._transition(InternalTaskState.PAUSED);\n          break;\n        default:\n      }\n    });\n  }\n\n  // TODO(andysoto): assert false\n\n  private _createResumable(): void {\n    this._resolveToken((authToken, appCheckToken) => {\n      const requestInfo = createResumableUpload(\n        this._ref.storage,\n        this._ref._location,\n        this._mappings,\n        this._blob,\n        this._metadata\n      );\n      const createRequest = this._ref.storage._makeRequest(\n        requestInfo,\n        newTextConnection,\n        authToken,\n        appCheckToken\n      );\n      this._request = createRequest;\n      createRequest.getPromise().then((url: string) => {\n        this._request = undefined;\n        this._uploadUrl = url;\n        this._needToFetchStatus = false;\n        this.completeTransitions_();\n      }, this._errorHandler);\n    });\n  }\n\n  private _fetchStatus(): void {\n    // TODO(andysoto): assert(this.uploadUrl_ !== null);\n    const url = this._uploadUrl as string;\n    this._resolveToken((authToken, appCheckToken) => {\n      const requestInfo = getResumableUploadStatus(\n        this._ref.storage,\n        this._ref._location,\n        url,\n        this._blob\n      );\n      const statusRequest = this._ref.storage._makeRequest(\n        requestInfo,\n        newTextConnection,\n        authToken,\n        appCheckToken\n      );\n      this._request = statusRequest;\n      statusRequest.getPromise().then(status => {\n        status = status as ResumableUploadStatus;\n        this._request = undefined;\n        this._updateProgress(status.current);\n        this._needToFetchStatus = false;\n        if (status.finalized) {\n          this._needToFetchMetadata = true;\n        }\n        this.completeTransitions_();\n      }, this._errorHandler);\n    });\n  }\n\n  private _continueUpload(): void {\n    const chunkSize = RESUMABLE_UPLOAD_CHUNK_SIZE * this._chunkMultiplier;\n    const status = new ResumableUploadStatus(\n      this._transferred,\n      this._blob.size()\n    );\n\n    // TODO(andysoto): assert(this.uploadUrl_ !== null);\n    const url = this._uploadUrl as string;\n    this._resolveToken((authToken, appCheckToken) => {\n      let requestInfo;\n      try {\n        requestInfo = continueResumableUpload(\n          this._ref._location,\n          this._ref.storage,\n          url,\n          this._blob,\n          chunkSize,\n          this._mappings,\n          status,\n          this._makeProgressCallback()\n        );\n      } catch (e) {\n        this._error = e as StorageError;\n        this._transition(InternalTaskState.ERROR);\n        return;\n      }\n      const uploadRequest = this._ref.storage._makeRequest(\n        requestInfo,\n        newTextConnection,\n        authToken,\n        appCheckToken,\n        /*retry=*/ false // Upload requests should not be retried as each retry should be preceded by another query request. Which is handled in this file.\n      );\n      this._request = uploadRequest;\n      uploadRequest.getPromise().then((newStatus: ResumableUploadStatus) => {\n        this._increaseMultiplier();\n        this._request = undefined;\n        this._updateProgress(newStatus.current);\n        if (newStatus.finalized) {\n          this._metadata = newStatus.metadata;\n          this._transition(InternalTaskState.SUCCESS);\n        } else {\n          this.completeTransitions_();\n        }\n      }, this._errorHandler);\n    });\n  }\n\n  private _increaseMultiplier(): void {\n    const currentSize = RESUMABLE_UPLOAD_CHUNK_SIZE * this._chunkMultiplier;\n\n    // Max chunk size is 32M.\n    if (currentSize * 2 < 32 * 1024 * 1024) {\n      this._chunkMultiplier *= 2;\n    }\n  }\n\n  private _fetchMetadata(): void {\n    this._resolveToken((authToken, appCheckToken) => {\n      const requestInfo = getMetadata(\n        this._ref.storage,\n        this._ref._location,\n        this._mappings\n      );\n      const metadataRequest = this._ref.storage._makeRequest(\n        requestInfo,\n        newTextConnection,\n        authToken,\n        appCheckToken\n      );\n      this._request = metadataRequest;\n      metadataRequest.getPromise().then(metadata => {\n        this._request = undefined;\n        this._metadata = metadata;\n        this._transition(InternalTaskState.SUCCESS);\n      }, this._metadataErrorHandler);\n    });\n  }\n\n  private _oneShotUpload(): void {\n    this._resolveToken((authToken, appCheckToken) => {\n      const requestInfo = multipartUpload(\n        this._ref.storage,\n        this._ref._location,\n        this._mappings,\n        this._blob,\n        this._metadata\n      );\n      const multipartRequest = this._ref.storage._makeRequest(\n        requestInfo,\n        newTextConnection,\n        authToken,\n        appCheckToken\n      );\n      this._request = multipartRequest;\n      multipartRequest.getPromise().then(metadata => {\n        this._request = undefined;\n        this._metadata = metadata;\n        this._updateProgress(this._blob.size());\n        this._transition(InternalTaskState.SUCCESS);\n      }, this._errorHandler);\n    });\n  }\n\n  private _updateProgress(transferred: number): void {\n    const old = this._transferred;\n    this._transferred = transferred;\n\n    // A progress update can make the \"transferred\" value smaller (e.g. a\n    // partial upload not completed by server, after which the \"transferred\"\n    // value may reset to the value at the beginning of the request).\n    if (this._transferred !== old) {\n      this._notifyObservers();\n    }\n  }\n\n  private _transition(state: InternalTaskState): void {\n    if (this._state === state) {\n      return;\n    }\n    switch (state) {\n      case InternalTaskState.CANCELING:\n      case InternalTaskState.PAUSING:\n        // TODO(andysoto):\n        // assert(this.state_ === InternalTaskState.RUNNING ||\n        //        this.state_ === InternalTaskState.PAUSING);\n        this._state = state;\n        if (this._request !== undefined) {\n          this._request.cancel();\n        } else if (this.pendingTimeout) {\n          clearTimeout(this.pendingTimeout);\n          this.pendingTimeout = undefined;\n          this.completeTransitions_();\n        }\n        break;\n      case InternalTaskState.RUNNING:\n        // TODO(andysoto):\n        // assert(this.state_ === InternalTaskState.PAUSED ||\n        //        this.state_ === InternalTaskState.PAUSING);\n        const wasPaused = this._state === InternalTaskState.PAUSED;\n        this._state = state;\n        if (wasPaused) {\n          this._notifyObservers();\n          this._start();\n        }\n        break;\n      case InternalTaskState.PAUSED:\n        // TODO(andysoto):\n        // assert(this.state_ === InternalTaskState.PAUSING);\n        this._state = state;\n        this._notifyObservers();\n        break;\n      case InternalTaskState.CANCELED:\n        // TODO(andysoto):\n        // assert(this.state_ === InternalTaskState.PAUSED ||\n        //        this.state_ === InternalTaskState.CANCELING);\n        this._error = canceled();\n        this._state = state;\n        this._notifyObservers();\n        break;\n      case InternalTaskState.ERROR:\n        // TODO(andysoto):\n        // assert(this.state_ === InternalTaskState.RUNNING ||\n        //        this.state_ === InternalTaskState.PAUSING ||\n        //        this.state_ === InternalTaskState.CANCELING);\n        this._state = state;\n        this._notifyObservers();\n        break;\n      case InternalTaskState.SUCCESS:\n        // TODO(andysoto):\n        // assert(this.state_ === InternalTaskState.RUNNING ||\n        //        this.state_ === InternalTaskState.PAUSING ||\n        //        this.state_ === InternalTaskState.CANCELING);\n        this._state = state;\n        this._notifyObservers();\n        break;\n      default: // Ignore\n    }\n  }\n\n  private completeTransitions_(): void {\n    switch (this._state) {\n      case InternalTaskState.PAUSING:\n        this._transition(InternalTaskState.PAUSED);\n        break;\n      case InternalTaskState.CANCELING:\n        this._transition(InternalTaskState.CANCELED);\n        break;\n      case InternalTaskState.RUNNING:\n        this._start();\n        break;\n      default:\n        // TODO(andysoto): assert(false);\n        break;\n    }\n  }\n\n  /**\n   * A snapshot of the current task state.\n   */\n  get snapshot(): UploadTaskSnapshot {\n    const externalState = taskStateFromInternalTaskState(this._state);\n    return {\n      bytesTransferred: this._transferred,\n      totalBytes: this._blob.size(),\n      state: externalState,\n      metadata: this._metadata!,\n      task: this,\n      ref: this._ref\n    };\n  }\n\n  /**\n   * Adds a callback for an event.\n   * @param type - The type of event to listen for.\n   * @param nextOrObserver -\n   *     The `next` function, which gets called for each item in\n   *     the event stream, or an observer object with some or all of these three\n   *     properties (`next`, `error`, `complete`).\n   * @param error - A function that gets called with a `StorageError`\n   *     if the event stream ends due to an error.\n   * @param completed - A function that gets called if the\n   *     event stream ends normally.\n   * @returns\n   *     If only the event argument is passed, returns a function you can use to\n   *     add callbacks (see the examples above). If more than just the event\n   *     argument is passed, returns a function you can call to unregister the\n   *     callbacks.\n   */\n  on(\n    type: TaskEvent,\n    nextOrObserver?:\n      | StorageObserver<UploadTaskSnapshot>\n      | null\n      | ((snapshot: UploadTaskSnapshot) => unknown),\n    error?: ((a: StorageError) => unknown) | null,\n    completed?: CompleteFn | null\n  ): Unsubscribe | Subscribe<UploadTaskSnapshot> {\n    // Note: `type` isn't being used. Its type is also incorrect. TaskEvent should not be a string.\n    const observer = new Observer(\n      (nextOrObserver as\n        | StorageObserverInternal<UploadTaskSnapshot>\n        | NextFn<UploadTaskSnapshot>) || undefined,\n      error || undefined,\n      completed || undefined\n    );\n    this._addObserver(observer);\n    return () => {\n      this._removeObserver(observer);\n    };\n  }\n\n  /**\n   * This object behaves like a Promise, and resolves with its snapshot data\n   * when the upload completes.\n   * @param onFulfilled - The fulfillment callback. Promise chaining works as normal.\n   * @param onRejected - The rejection callback.\n   */\n  then<U>(\n    onFulfilled?: ((value: UploadTaskSnapshot) => U | Promise<U>) | null,\n    onRejected?: ((error: StorageError) => U | Promise<U>) | null\n  ): Promise<U> {\n    // These casts are needed so that TypeScript can infer the types of the\n    // resulting Promise.\n    return this._promise.then<U>(\n      onFulfilled as (value: UploadTaskSnapshot) => U | Promise<U>,\n      onRejected as ((error: unknown) => Promise<never>) | null\n    );\n  }\n\n  /**\n   * Equivalent to calling `then(null, onRejected)`.\n   */\n  catch<T>(onRejected: (p1: StorageError) => T | Promise<T>): Promise<T> {\n    return this.then(null, onRejected);\n  }\n\n  /**\n   * Adds the given observer.\n   */\n  private _addObserver(observer: Observer<UploadTaskSnapshot>): void {\n    this._observers.push(observer);\n    this._notifyObserver(observer);\n  }\n\n  /**\n   * Removes the given observer.\n   */\n  private _removeObserver(observer: Observer<UploadTaskSnapshot>): void {\n    const i = this._observers.indexOf(observer);\n    if (i !== -1) {\n      this._observers.splice(i, 1);\n    }\n  }\n\n  private _notifyObservers(): void {\n    this._finishPromise();\n    const observers = this._observers.slice();\n    observers.forEach(observer => {\n      this._notifyObserver(observer);\n    });\n  }\n\n  private _finishPromise(): void {\n    if (this._resolve !== undefined) {\n      let triggered = true;\n      switch (taskStateFromInternalTaskState(this._state)) {\n        case TaskState.SUCCESS:\n          fbsAsync(this._resolve.bind(null, this.snapshot))();\n          break;\n        case TaskState.CANCELED:\n        case TaskState.ERROR:\n          const toCall = this._reject as (p1: StorageError) => void;\n          fbsAsync(toCall.bind(null, this._error as StorageError))();\n          break;\n        default:\n          triggered = false;\n          break;\n      }\n      if (triggered) {\n        this._resolve = undefined;\n        this._reject = undefined;\n      }\n    }\n  }\n\n  private _notifyObserver(observer: Observer<UploadTaskSnapshot>): void {\n    const externalState = taskStateFromInternalTaskState(this._state);\n    switch (externalState) {\n      case TaskState.RUNNING:\n      case TaskState.PAUSED:\n        if (observer.next) {\n          fbsAsync(observer.next.bind(observer, this.snapshot))();\n        }\n        break;\n      case TaskState.SUCCESS:\n        if (observer.complete) {\n          fbsAsync(observer.complete.bind(observer))();\n        }\n        break;\n      case TaskState.CANCELED:\n      case TaskState.ERROR:\n        if (observer.error) {\n          fbsAsync(\n            observer.error.bind(observer, this._error as StorageError)\n          )();\n        }\n        break;\n      default:\n        // TODO(andysoto): assert(false);\n        if (observer.error) {\n          fbsAsync(\n            observer.error.bind(observer, this._error as StorageError)\n          )();\n        }\n    }\n  }\n\n  /**\n   * Resumes a paused task. Has no effect on a currently running or failed task.\n   * @returns True if the operation took effect, false if ignored.\n   */\n  resume(): boolean {\n    const valid =\n      this._state === InternalTaskState.PAUSED ||\n      this._state === InternalTaskState.PAUSING;\n    if (valid) {\n      this._transition(InternalTaskState.RUNNING);\n    }\n    return valid;\n  }\n\n  /**\n   * Pauses a currently running task. Has no effect on a paused or failed task.\n   * @returns True if the operation took effect, false if ignored.\n   */\n  pause(): boolean {\n    const valid = this._state === InternalTaskState.RUNNING;\n    if (valid) {\n      this._transition(InternalTaskState.PAUSING);\n    }\n    return valid;\n  }\n\n  /**\n   * Cancels a currently running or paused task. Has no effect on a complete or\n   * failed task.\n   * @returns True if the operation took effect, false if ignored.\n   */\n  cancel(): boolean {\n    const valid =\n      this._state === InternalTaskState.RUNNING ||\n      this._state === InternalTaskState.PAUSING;\n    if (valid) {\n      this._transition(InternalTaskState.CANCELING);\n    }\n    return valid;\n  }\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n/**\n * @fileoverview Defines the Firebase StorageReference class.\n */\n\nimport { FbsBlob } from './implementation/blob';\nimport { Location } from './implementation/location';\nimport { getMappings } from './implementation/metadata';\nimport { child, lastComponent, parent } from './implementation/path';\nimport {\n  deleteObject as requestsDeleteObject,\n  getBytes,\n  getDownloadUrl as requestsGetDownloadUrl,\n  getMetadata as requestsGetMetadata,\n  list as requestsList,\n  multipartUpload,\n  updateMetadata as requestsUpdateMetadata\n} from './implementation/requests';\nimport { ListOptions, UploadResult } from './public-types';\nimport { dataFromString, StringFormat } from './implementation/string';\nimport { Metadata } from './metadata';\nimport { FirebaseStorageImpl } from './service';\nimport { ListResult } from './list';\nimport { UploadTask } from './task';\nimport { invalidRootOperation, noDownloadURL } from './implementation/error';\nimport { validateNumber } from './implementation/type';\nimport {\n  newBlobConnection,\n  newBytesConnection,\n  newStreamConnection,\n  newTextConnection\n} from './platform/connection';\nimport { RequestInfo } from './implementation/requestinfo';\n\n/**\n * Provides methods to interact with a bucket in the Firebase Storage service.\n * @internal\n * @param _location - An fbs.location, or the URL at\n *     which to base this object, in one of the following forms:\n *         gs://<bucket>/<object-path>\n *         http[s]://firebasestorage.googleapis.com/\n *                     <api-version>/b/<bucket>/o/<object-path>\n *     Any query or fragment strings will be ignored in the http[s]\n *     format. If no value is passed, the storage object will use a URL based on\n *     the project ID of the base firebase.App instance.\n */\nexport class Reference {\n  _location: Location;\n\n  constructor(\n    private _service: FirebaseStorageImpl,\n    location: string | Location\n  ) {\n    if (location instanceof Location) {\n      this._location = location;\n    } else {\n      this._location = Location.makeFromUrl(location, _service.host);\n    }\n  }\n\n  /**\n   * Returns the URL for the bucket and path this object references,\n   *     in the form gs://<bucket>/<object-path>\n   * @override\n   */\n  toString(): string {\n    return 'gs://' + this._location.bucket + '/' + this._location.path;\n  }\n\n  protected _newRef(\n    service: FirebaseStorageImpl,\n    location: Location\n  ): Reference {\n    return new Reference(service, location);\n  }\n\n  /**\n   * A reference to the root of this object's bucket.\n   */\n  get root(): Reference {\n    const location = new Location(this._location.bucket, '');\n    return this._newRef(this._service, location);\n  }\n\n  /**\n   * The name of the bucket containing this reference's object.\n   */\n  get bucket(): string {\n    return this._location.bucket;\n  }\n\n  /**\n   * The full path of this object.\n   */\n  get fullPath(): string {\n    return this._location.path;\n  }\n\n  /**\n   * The short name of this object, which is the last component of the full path.\n   * For example, if fullPath is 'full/path/image.png', name is 'image.png'.\n   */\n  get name(): string {\n    return lastComponent(this._location.path);\n  }\n\n  /**\n   * The `StorageService` instance this `StorageReference` is associated with.\n   */\n  get storage(): FirebaseStorageImpl {\n    return this._service;\n  }\n\n  /**\n   * A `StorageReference` pointing to the parent location of this `StorageReference`, or null if\n   * this reference is the root.\n   */\n  get parent(): Reference | null {\n    const newPath = parent(this._location.path);\n    if (newPath === null) {\n      return null;\n    }\n    const location = new Location(this._location.bucket, newPath);\n    return new Reference(this._service, location);\n  }\n\n  /**\n   * Utility function to throw an error in methods that do not accept a root reference.\n   */\n  _throwIfRoot(name: string): void {\n    if (this._location.path === '') {\n      throw invalidRootOperation(name);\n    }\n  }\n}\n\n/**\n * Download the bytes at the object's location.\n * @returns A Promise containing the downloaded bytes.\n */\nexport function getBytesInternal(\n  ref: Reference,\n  maxDownloadSizeBytes?: number\n): Promise<ArrayBuffer> {\n  ref._throwIfRoot('getBytes');\n  const requestInfo = getBytes(\n    ref.storage,\n    ref._location,\n    maxDownloadSizeBytes\n  );\n  return ref.storage\n    .makeRequestWithTokens(requestInfo, newBytesConnection)\n    .then(bytes =>\n      maxDownloadSizeBytes !== undefined\n        ? // GCS may not honor the Range header for small files\n          (bytes as ArrayBuffer).slice(0, maxDownloadSizeBytes)\n        : (bytes as ArrayBuffer)\n    );\n}\n\n/**\n * Download the bytes at the object's location.\n * @returns A Promise containing the downloaded blob.\n */\nexport function getBlobInternal(\n  ref: Reference,\n  maxDownloadSizeBytes?: number\n): Promise<Blob> {\n  ref._throwIfRoot('getBlob');\n  const requestInfo = getBytes(\n    ref.storage,\n    ref._location,\n    maxDownloadSizeBytes\n  );\n  return ref.storage\n    .makeRequestWithTokens(requestInfo, newBlobConnection)\n    .then(blob =>\n      maxDownloadSizeBytes !== undefined\n        ? // GCS may not honor the Range header for small files\n          (blob as Blob).slice(0, maxDownloadSizeBytes)\n        : (blob as Blob)\n    );\n}\n\n/** Stream the bytes at the object's location. */\nexport function getStreamInternal(\n  ref: Reference,\n  maxDownloadSizeBytes?: number\n): ReadableStream {\n  ref._throwIfRoot('getStream');\n  const requestInfo: RequestInfo<ReadableStream, ReadableStream> = getBytes(\n    ref.storage,\n    ref._location,\n    maxDownloadSizeBytes\n  );\n\n  // Transforms the stream so that only `maxDownloadSizeBytes` bytes are piped to the result\n  const newMaxSizeTransform = (n: number): Transformer => {\n    let missingBytes = n;\n    return {\n      transform(chunk, controller: TransformStreamDefaultController) {\n        // GCS may not honor the Range header for small files\n        if (chunk.length < missingBytes) {\n          controller.enqueue(chunk);\n          missingBytes -= chunk.length;\n        } else {\n          controller.enqueue(chunk.slice(0, missingBytes));\n          controller.terminate();\n        }\n      }\n    };\n  };\n\n  const result =\n    maxDownloadSizeBytes !== undefined\n      ? new TransformStream(newMaxSizeTransform(maxDownloadSizeBytes))\n      : new TransformStream(); // The default transformer forwards all chunks to its readable side\n\n  ref.storage\n    .makeRequestWithTokens(requestInfo, newStreamConnection)\n    .then(readableStream => readableStream.pipeThrough(result))\n    .catch(err => result.writable.abort(err));\n\n  return result.readable;\n}\n\n/**\n * Uploads data to this object's location.\n * The upload is not resumable.\n *\n * @param ref - StorageReference where data should be uploaded.\n * @param data - The data to upload.\n * @param metadata - Metadata for the newly uploaded data.\n * @returns A Promise containing an UploadResult\n */\nexport function uploadBytes(\n  ref: Reference,\n  data: Blob | Uint8Array | ArrayBuffer,\n  metadata?: Metadata\n): Promise<UploadResult> {\n  ref._throwIfRoot('uploadBytes');\n  const requestInfo = multipartUpload(\n    ref.storage,\n    ref._location,\n    getMappings(),\n    new FbsBlob(data, true),\n    metadata\n  );\n  return ref.storage\n    .makeRequestWithTokens(requestInfo, newTextConnection)\n    .then(finalMetadata => {\n      return {\n        metadata: finalMetadata,\n        ref\n      };\n    });\n}\n\n/**\n * Uploads data to this object's location.\n * The upload can be paused and resumed, and exposes progress updates.\n * @public\n * @param ref - StorageReference where data should be uploaded.\n * @param data - The data to upload.\n * @param metadata - Metadata for the newly uploaded data.\n * @returns An UploadTask\n */\nexport function uploadBytesResumable(\n  ref: Reference,\n  data: Blob | Uint8Array | ArrayBuffer,\n  metadata?: Metadata\n): UploadTask {\n  ref._throwIfRoot('uploadBytesResumable');\n  return new UploadTask(ref, new FbsBlob(data), metadata);\n}\n\n/**\n * Uploads a string to this object's location.\n * The upload is not resumable.\n * @public\n * @param ref - StorageReference where string should be uploaded.\n * @param value - The string to upload.\n * @param format - The format of the string to upload.\n * @param metadata - Metadata for the newly uploaded string.\n * @returns A Promise containing an UploadResult\n */\nexport function uploadString(\n  ref: Reference,\n  value: string,\n  format: StringFormat = StringFormat.RAW,\n  metadata?: Metadata\n): Promise<UploadResult> {\n  ref._throwIfRoot('uploadString');\n  const data = dataFromString(format, value);\n  const metadataClone = { ...metadata } as Metadata;\n  if (metadataClone['contentType'] == null && data.contentType != null) {\n    metadataClone['contentType'] = data.contentType!;\n  }\n  return uploadBytes(ref, data.data, metadataClone);\n}\n\n/**\n * List all items (files) and prefixes (folders) under this storage reference.\n *\n * This is a helper method for calling list() repeatedly until there are\n * no more results. The default pagination size is 1000.\n *\n * Note: The results may not be consistent if objects are changed while this\n * operation is running.\n *\n * Warning: listAll may potentially consume too many resources if there are\n * too many results.\n * @public\n * @param ref - StorageReference to get list from.\n *\n * @returns A Promise that resolves with all the items and prefixes under\n *      the current storage reference. `prefixes` contains references to\n *      sub-directories and `items` contains references to objects in this\n *      folder. `nextPageToken` is never returned.\n */\nexport function listAll(ref: Reference): Promise<ListResult> {\n  const accumulator: ListResult = {\n    prefixes: [],\n    items: []\n  };\n  return listAllHelper(ref, accumulator).then(() => accumulator);\n}\n\n/**\n * Separated from listAll because async functions can't use \"arguments\".\n * @param ref\n * @param accumulator\n * @param pageToken\n */\nasync function listAllHelper(\n  ref: Reference,\n  accumulator: ListResult,\n  pageToken?: string\n): Promise<void> {\n  const opt: ListOptions = {\n    // maxResults is 1000 by default.\n    pageToken\n  };\n  const nextPage = await list(ref, opt);\n  accumulator.prefixes.push(...nextPage.prefixes);\n  accumulator.items.push(...nextPage.items);\n  if (nextPage.nextPageToken != null) {\n    await listAllHelper(ref, accumulator, nextPage.nextPageToken);\n  }\n}\n\n/**\n * List items (files) and prefixes (folders) under this storage reference.\n *\n * List API is only available for Firebase Rules Version 2.\n *\n * GCS is a key-blob store. Firebase Storage imposes the semantic of '/'\n * delimited folder structure.\n * Refer to GCS's List API if you want to learn more.\n *\n * To adhere to Firebase Rules's Semantics, Firebase Storage does not\n * support objects whose paths end with \"/\" or contain two consecutive\n * \"/\"s. Firebase Storage List API will filter these unsupported objects.\n * list() may fail if there are too many unsupported objects in the bucket.\n * @public\n *\n * @param ref - StorageReference to get list from.\n * @param options - See ListOptions for details.\n * @returns A Promise that resolves with the items and prefixes.\n *      `prefixes` contains references to sub-folders and `items`\n *      contains references to objects in this folder. `nextPageToken`\n *      can be used to get the rest of the results.\n */\nexport function list(\n  ref: Reference,\n  options?: ListOptions | null\n): Promise<ListResult> {\n  if (options != null) {\n    if (typeof options.maxResults === 'number') {\n      validateNumber(\n        'options.maxResults',\n        /* minValue= */ 1,\n        /* maxValue= */ 1000,\n        options.maxResults\n      );\n    }\n  }\n  const op = options || {};\n  const requestInfo = requestsList(\n    ref.storage,\n    ref._location,\n    /*delimiter= */ '/',\n    op.pageToken,\n    op.maxResults\n  );\n  return ref.storage.makeRequestWithTokens(requestInfo, newTextConnection);\n}\n\n/**\n * A `Promise` that resolves with the metadata for this object. If this\n * object doesn't exist or metadata cannot be retrieved, the promise is\n * rejected.\n * @public\n * @param ref - StorageReference to get metadata from.\n */\nexport function getMetadata(ref: Reference): Promise<Metadata> {\n  ref._throwIfRoot('getMetadata');\n  const requestInfo = requestsGetMetadata(\n    ref.storage,\n    ref._location,\n    getMappings()\n  );\n  return ref.storage.makeRequestWithTokens(requestInfo, newTextConnection);\n}\n\n/**\n * Updates the metadata for this object.\n * @public\n * @param ref - StorageReference to update metadata for.\n * @param metadata - The new metadata for the object.\n *     Only values that have been explicitly set will be changed. Explicitly\n *     setting a value to null will remove the metadata.\n * @returns A `Promise` that resolves\n *     with the new metadata for this object.\n *     See `firebaseStorage.Reference.prototype.getMetadata`\n */\nexport function updateMetadata(\n  ref: Reference,\n  metadata: Partial<Metadata>\n): Promise<Metadata> {\n  ref._throwIfRoot('updateMetadata');\n  const requestInfo = requestsUpdateMetadata(\n    ref.storage,\n    ref._location,\n    metadata,\n    getMappings()\n  );\n  return ref.storage.makeRequestWithTokens(requestInfo, newTextConnection);\n}\n\n/**\n * Returns the download URL for the given Reference.\n * @public\n * @returns A `Promise` that resolves with the download\n *     URL for this object.\n */\nexport function getDownloadURL(ref: Reference): Promise<string> {\n  ref._throwIfRoot('getDownloadURL');\n  const requestInfo = requestsGetDownloadUrl(\n    ref.storage,\n    ref._location,\n    getMappings()\n  );\n  return ref.storage\n    .makeRequestWithTokens(requestInfo, newTextConnection)\n    .then(url => {\n      if (url === null) {\n        throw noDownloadURL();\n      }\n      return url;\n    });\n}\n\n/**\n * Deletes the object at this location.\n * @public\n * @param ref - StorageReference for object to delete.\n * @returns A `Promise` that resolves if the deletion succeeds.\n */\nexport function deleteObject(ref: Reference): Promise<void> {\n  ref._throwIfRoot('deleteObject');\n  const requestInfo = requestsDeleteObject(ref.storage, ref._location);\n  return ref.storage.makeRequestWithTokens(requestInfo, newTextConnection);\n}\n\n/**\n * Returns reference for object obtained by appending `childPath` to `ref`.\n *\n * @param ref - StorageReference to get child of.\n * @param childPath - Child path from provided ref.\n * @returns A reference to the object obtained by\n * appending childPath, removing any duplicate, beginning, or trailing\n * slashes.\n *\n */\nexport function _getChild(ref: Reference, childPath: string): Reference {\n  const newPath = child(ref._location.path, childPath);\n  const location = new Location(ref._location.bucket, newPath);\n  return new Reference(ref.storage, location);\n}\n", "/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { Location } from './implementation/location';\nimport { FailRequest } from './implementation/failrequest';\nimport { Request, makeRequest } from './implementation/request';\nimport { RequestInfo } from './implementation/requestinfo';\nimport { Reference, _getChild } from './reference';\nimport { Provider } from '@firebase/component';\nimport { FirebaseAuthInternalName } from '@firebase/auth-interop-types';\nimport { AppCheckInternalComponentName } from '@firebase/app-check-interop-types';\n// eslint-disable-next-line import/no-extraneous-dependencies\nimport { FirebaseApp, FirebaseOptions } from '@firebase/app';\nimport {\n  CONFIG_STORAGE_BUCKET_KEY,\n  DEFAULT_HOST,\n  DEFAULT_MAX_OPERATION_RETRY_TIME,\n  DEFAULT_MAX_UPLOAD_RETRY_TIME\n} from './implementation/constants';\nimport {\n  invalidArgument,\n  appDeleted,\n  noDefaultBucket\n} from './implementation/error';\nimport { validateNumber } from './implementation/type';\nimport { FirebaseStorage } from './public-types';\nimport { createMockUserToken, EmulatorMockTokenOptions } from '@firebase/util';\nimport { Connection, ConnectionType } from './implementation/connection';\n\nexport function isUrl(path?: string): boolean {\n  return /^[A-Za-z]+:\\/\\//.test(path as string);\n}\n\n/**\n * Returns a firebaseStorage.Reference for the given url.\n */\nfunction refFromURL(service: FirebaseStorageImpl, url: string): Reference {\n  return new Reference(service, url);\n}\n\n/**\n * Returns a firebaseStorage.Reference for the given path in the default\n * bucket.\n */\nfunction refFromPath(\n  ref: FirebaseStorageImpl | Reference,\n  path?: string\n): Reference {\n  if (ref instanceof FirebaseStorageImpl) {\n    const service = ref;\n    if (service._bucket == null) {\n      throw noDefaultBucket();\n    }\n    const reference = new Reference(service, service._bucket!);\n    if (path != null) {\n      return refFromPath(reference, path);\n    } else {\n      return reference;\n    }\n  } else {\n    // ref is a Reference\n    if (path !== undefined) {\n      return _getChild(ref, path);\n    } else {\n      return ref;\n    }\n  }\n}\n\n/**\n * Returns a storage Reference for the given url.\n * @param storage - `Storage` instance.\n * @param url - URL. If empty, returns root reference.\n * @public\n */\nexport function ref(storage: FirebaseStorageImpl, url?: string): Reference;\n/**\n * Returns a storage Reference for the given path in the\n * default bucket.\n * @param storageOrRef - `Storage` service or storage `Reference`.\n * @param pathOrUrlStorage - path. If empty, returns root reference (if Storage\n * instance provided) or returns same reference (if Reference provided).\n * @public\n */\nexport function ref(\n  storageOrRef: FirebaseStorageImpl | Reference,\n  path?: string\n): Reference;\nexport function ref(\n  serviceOrRef: FirebaseStorageImpl | Reference,\n  pathOrUrl?: string\n): Reference | null {\n  if (pathOrUrl && isUrl(pathOrUrl)) {\n    if (serviceOrRef instanceof FirebaseStorageImpl) {\n      return refFromURL(serviceOrRef, pathOrUrl);\n    } else {\n      throw invalidArgument(\n        'To use ref(service, url), the first argument must be a Storage instance.'\n      );\n    }\n  } else {\n    return refFromPath(serviceOrRef, pathOrUrl);\n  }\n}\n\nfunction extractBucket(\n  host: string,\n  config?: FirebaseOptions\n): Location | null {\n  const bucketString = config?.[CONFIG_STORAGE_BUCKET_KEY];\n  if (bucketString == null) {\n    return null;\n  }\n  return Location.makeFromBucketSpec(bucketString, host);\n}\n\nexport function connectStorageEmulator(\n  storage: FirebaseStorageImpl,\n  host: string,\n  port: number,\n  options: {\n    mockUserToken?: EmulatorMockTokenOptions | string;\n  } = {}\n): void {\n  storage.host = `${host}:${port}`;\n  storage._protocol = 'http';\n  const { mockUserToken } = options;\n  if (mockUserToken) {\n    storage._overrideAuthToken =\n      typeof mockUserToken === 'string'\n        ? mockUserToken\n        : createMockUserToken(mockUserToken, storage.app.options.projectId);\n  }\n}\n\n/**\n * A service that provides Firebase Storage Reference instances.\n * @param opt_url - gs:// url to a custom Storage Bucket\n *\n * @internal\n */\nexport class FirebaseStorageImpl implements FirebaseStorage {\n  _bucket: Location | null = null;\n  /**\n   * This string can be in the formats:\n   * - host\n   * - host:port\n   */\n  private _host: string = DEFAULT_HOST;\n  _protocol: string = 'https';\n  protected readonly _appId: string | null = null;\n  private readonly _requests: Set<Request<unknown>>;\n  private _deleted: boolean = false;\n  private _maxOperationRetryTime: number;\n  private _maxUploadRetryTime: number;\n  _overrideAuthToken?: string;\n\n  constructor(\n    /**\n     * FirebaseApp associated with this StorageService instance.\n     */\n    readonly app: FirebaseApp,\n    readonly _authProvider: Provider<FirebaseAuthInternalName>,\n    /**\n     * @internal\n     */\n    readonly _appCheckProvider: Provider<AppCheckInternalComponentName>,\n    /**\n     * @internal\n     */\n    readonly _url?: string,\n    readonly _firebaseVersion?: string\n  ) {\n    this._maxOperationRetryTime = DEFAULT_MAX_OPERATION_RETRY_TIME;\n    this._maxUploadRetryTime = DEFAULT_MAX_UPLOAD_RETRY_TIME;\n    this._requests = new Set();\n    if (_url != null) {\n      this._bucket = Location.makeFromBucketSpec(_url, this._host);\n    } else {\n      this._bucket = extractBucket(this._host, this.app.options);\n    }\n  }\n\n  /**\n   * The host string for this service, in the form of `host` or\n   * `host:port`.\n   */\n  get host(): string {\n    return this._host;\n  }\n\n  set host(host: string) {\n    this._host = host;\n    if (this._url != null) {\n      this._bucket = Location.makeFromBucketSpec(this._url, host);\n    } else {\n      this._bucket = extractBucket(host, this.app.options);\n    }\n  }\n\n  /**\n   * The maximum time to retry uploads in milliseconds.\n   */\n  get maxUploadRetryTime(): number {\n    return this._maxUploadRetryTime;\n  }\n\n  set maxUploadRetryTime(time: number) {\n    validateNumber(\n      'time',\n      /* minValue=*/ 0,\n      /* maxValue= */ Number.POSITIVE_INFINITY,\n      time\n    );\n    this._maxUploadRetryTime = time;\n  }\n\n  /**\n   * The maximum time to retry operations other than uploads or downloads in\n   * milliseconds.\n   */\n  get maxOperationRetryTime(): number {\n    return this._maxOperationRetryTime;\n  }\n\n  set maxOperationRetryTime(time: number) {\n    validateNumber(\n      'time',\n      /* minValue=*/ 0,\n      /* maxValue= */ Number.POSITIVE_INFINITY,\n      time\n    );\n    this._maxOperationRetryTime = time;\n  }\n\n  async _getAuthToken(): Promise<string | null> {\n    if (this._overrideAuthToken) {\n      return this._overrideAuthToken;\n    }\n    const auth = this._authProvider.getImmediate({ optional: true });\n    if (auth) {\n      const tokenData = await auth.getToken();\n      if (tokenData !== null) {\n        return tokenData.accessToken;\n      }\n    }\n    return null;\n  }\n\n  async _getAppCheckToken(): Promise<string | null> {\n    const appCheck = this._appCheckProvider.getImmediate({ optional: true });\n    if (appCheck) {\n      const result = await appCheck.getToken();\n      // TODO: What do we want to do if there is an error getting the token?\n      // Context: appCheck.getToken() will never throw even if an error happened. In the error case, a dummy token will be\n      // returned along with an error field describing the error. In general, we shouldn't care about the error condition and just use\n      // the token (actual or dummy) to send requests.\n      return result.token;\n    }\n    return null;\n  }\n\n  /**\n   * Stop running requests and prevent more from being created.\n   */\n  _delete(): Promise<void> {\n    if (!this._deleted) {\n      this._deleted = true;\n      this._requests.forEach(request => request.cancel());\n      this._requests.clear();\n    }\n    return Promise.resolve();\n  }\n\n  /**\n   * Returns a new firebaseStorage.Reference object referencing this StorageService\n   * at the given Location.\n   */\n  _makeStorageReference(loc: Location): Reference {\n    return new Reference(this, loc);\n  }\n\n  /**\n   * @param requestInfo - HTTP RequestInfo object\n   * @param authToken - Firebase auth token\n   */\n  _makeRequest<I extends ConnectionType, O>(\n    requestInfo: RequestInfo<I, O>,\n    requestFactory: () => Connection<I>,\n    authToken: string | null,\n    appCheckToken: string | null,\n    retry = true\n  ): Request<O> {\n    if (!this._deleted) {\n      const request = makeRequest(\n        requestInfo,\n        this._appId,\n        authToken,\n        appCheckToken,\n        requestFactory,\n        this._firebaseVersion,\n        retry\n      );\n      this._requests.add(request);\n      // Request removes itself from set when complete.\n      request.getPromise().then(\n        () => this._requests.delete(request),\n        () => this._requests.delete(request)\n      );\n      return request;\n    } else {\n      return new FailRequest(appDeleted());\n    }\n  }\n\n  async makeRequestWithTokens<I extends ConnectionType, O>(\n    requestInfo: RequestInfo<I, O>,\n    requestFactory: () => Connection<I>\n  ): Promise<O> {\n    const [authToken, appCheckToken] = await Promise.all([\n      this._getAuthToken(),\n      this._getAppCheckToken()\n    ]);\n\n    return this._makeRequest(\n      requestInfo,\n      requestFactory,\n      authToken,\n      appCheckToken\n    ).getPromise();\n  }\n}\n", "/**\n * @license\n * Copyright 2021 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { base64urlEncodeWithoutPadding } from './crypt';\n\n// Firebase Auth tokens contain snake_case claims following the JWT standard / convention.\n/* eslint-disable camelcase */\n\nexport type FirebaseSignInProvider =\n  | 'custom'\n  | 'email'\n  | 'password'\n  | 'phone'\n  | 'anonymous'\n  | 'google.com'\n  | 'facebook.com'\n  | 'github.com'\n  | 'twitter.com'\n  | 'microsoft.com'\n  | 'apple.com';\n\ninterface FirebaseIdToken {\n  // Always set to https://securetoken.google.com/PROJECT_ID\n  iss: string;\n\n  // Always set to PROJECT_ID\n  aud: string;\n\n  // The user's unique ID\n  sub: string;\n\n  // The token issue time, in seconds since epoch\n  iat: number;\n\n  // The token expiry time, normally 'iat' + 3600\n  exp: number;\n\n  // The user's unique ID. Must be equal to 'sub'\n  user_id: string;\n\n  // The time the user authenticated, normally 'iat'\n  auth_time: number;\n\n  // The sign in provider, only set when the provider is 'anonymous'\n  provider_id?: 'anonymous';\n\n  // The user's primary email\n  email?: string;\n\n  // The user's email verification status\n  email_verified?: boolean;\n\n  // The user's primary phone number\n  phone_number?: string;\n\n  // The user's display name\n  name?: string;\n\n  // The user's profile photo URL\n  picture?: string;\n\n  // Information on all identities linked to this user\n  firebase: {\n    // The primary sign-in provider\n    sign_in_provider: FirebaseSignInProvider;\n\n    // A map of providers to the user's list of unique identifiers from\n    // each provider\n    identities?: { [provider in FirebaseSignInProvider]?: string[] };\n  };\n\n  // Custom claims set by the developer\n  [claim: string]: unknown;\n\n  uid?: never; // Try to catch a common mistake of \"uid\" (should be \"sub\" instead).\n}\n\nexport type EmulatorMockTokenOptions = ({ user_id: string } | { sub: string }) &\n  Partial<FirebaseIdToken>;\n\nexport function createMockUserToken(\n  token: EmulatorMockTokenOptions,\n  projectId?: string\n): string {\n  if (token.uid) {\n    throw new Error(\n      'The \"uid\" field is no longer supported by mockUserToken. Please use \"sub\" instead for Firebase Auth User ID.'\n    );\n  }\n  // Unsecured JWTs use \"none\" as the algorithm.\n  const header = {\n    alg: 'none',\n    type: 'JWT'\n  };\n\n  const project = projectId || 'demo-project';\n  const iat = token.iat || 0;\n  const sub = token.sub || token.user_id;\n  if (!sub) {\n    throw new Error(\"mockUserToken must contain 'sub' or 'user_id' field!\");\n  }\n\n  const payload: FirebaseIdToken = {\n    // Set all required fields to decent defaults\n    iss: `https://securetoken.google.com/${project}`,\n    aud: project,\n    iat,\n    exp: iat + 3600,\n    auth_time: iat,\n    sub,\n    user_id: sub,\n    firebase: {\n      sign_in_provider: 'custom',\n      identities: {}\n    },\n\n    // Override with user options\n    ...token\n  };\n\n  // Unsecured JWTs use the empty string as a signature.\n  const signature = '';\n  return [\n    base64urlEncodeWithoutPadding(JSON.stringify(header)),\n    base64urlEncodeWithoutPadding(JSON.stringify(payload)),\n    signature\n  ].join('.');\n}\n", "/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nimport { _getProvider, FirebaseApp, getApp } from '@firebase/app';\n\nimport {\n  ref as refInternal,\n  FirebaseStorageImpl,\n  connectStorageEmulator as connectEmulatorInternal\n} from './service';\nimport { Provider } from '@firebase/component';\n\nimport {\n  StorageReference,\n  FirebaseStorage,\n  UploadResult,\n  ListOptions,\n  ListResult,\n  UploadTask,\n  SettableMetadata,\n  UploadMetadata,\n  FullMetadata\n} from './public-types';\nimport { Metadata as MetadataInternal } from './metadata';\nimport {\n  uploadBytes as uploadBytesInternal,\n  uploadBytesResumable as uploadBytesResumableInternal,\n  uploadString as uploadStringInternal,\n  getMetadata as getMetadataInternal,\n  updateMetadata as updateMetadataInternal,\n  list as listInternal,\n  listAll as listAllInternal,\n  getDownloadURL as getDownloadURLInternal,\n  deleteObject as deleteObjectInternal,\n  Reference,\n  _getChild as _getChildInternal,\n  getBytesInternal\n} from './reference';\nimport { STORAGE_TYPE } from './constants';\nimport {\n  EmulatorMockTokenOptions,\n  getModularInstance,\n  getDefaultEmulatorHostnameAndPort\n} from '@firebase/util';\nimport { StringFormat } from './implementation/string';\n\nexport { EmulatorMockTokenOptions } from '@firebase/util';\n\nexport { StorageError, StorageErrorCode } from './implementation/error';\n\n/**\n * Public types.\n */\nexport * from './public-types';\n\nexport { Location as _Location } from './implementation/location';\nexport { UploadTask as _UploadTask } from './task';\nexport type { Reference as _Reference } from './reference';\nexport type { FirebaseStorageImpl as _FirebaseStorageImpl } from './service';\nexport { FbsBlob as _FbsBlob } from './implementation/blob';\nexport { dataFromString as _dataFromString } from './implementation/string';\nexport {\n  invalidRootOperation as _invalidRootOperation,\n  invalidArgument as _invalidArgument\n} from './implementation/error';\nexport {\n  TaskEvent as _TaskEvent,\n  TaskState as _TaskState\n} from './implementation/taskenums';\nexport { StringFormat };\n\n/**\n * Downloads the data at the object's location. Returns an error if the object\n * is not found.\n *\n * To use this functionality, you have to whitelist your app's origin in your\n * Cloud Storage bucket. See also\n * https://cloud.google.com/storage/docs/configuring-cors\n *\n * @public\n * @param ref - StorageReference where data should be downloaded.\n * @param maxDownloadSizeBytes - If set, the maximum allowed size in bytes to\n * retrieve.\n * @returns A Promise containing the object's bytes\n */\nexport function getBytes(\n  ref: StorageReference,\n  maxDownloadSizeBytes?: number\n): Promise<ArrayBuffer> {\n  ref = getModularInstance(ref);\n  return getBytesInternal(ref as Reference, maxDownloadSizeBytes);\n}\n\n/**\n * Uploads data to this object's location.\n * The upload is not resumable.\n * @public\n * @param ref - {@link StorageReference} where data should be uploaded.\n * @param data - The data to upload.\n * @param metadata - Metadata for the data to upload.\n * @returns A Promise containing an UploadResult\n */\nexport function uploadBytes(\n  ref: StorageReference,\n  data: Blob | Uint8Array | ArrayBuffer,\n  metadata?: UploadMetadata\n): Promise<UploadResult> {\n  ref = getModularInstance(ref);\n  return uploadBytesInternal(\n    ref as Reference,\n    data,\n    metadata as MetadataInternal\n  );\n}\n\n/**\n * Uploads a string to this object's location.\n * The upload is not resumable.\n * @public\n * @param ref - {@link StorageReference} where string should be uploaded.\n * @param value - The string to upload.\n * @param format - The format of the string to upload.\n * @param metadata - Metadata for the string to upload.\n * @returns A Promise containing an UploadResult\n */\nexport function uploadString(\n  ref: StorageReference,\n  value: string,\n  format?: StringFormat,\n  metadata?: UploadMetadata\n): Promise<UploadResult> {\n  ref = getModularInstance(ref);\n  return uploadStringInternal(\n    ref as Reference,\n    value,\n    format,\n    metadata as MetadataInternal\n  );\n}\n\n/**\n * Uploads data to this object's location.\n * The upload can be paused and resumed, and exposes progress updates.\n * @public\n * @param ref - {@link StorageReference} where data should be uploaded.\n * @param data - The data to upload.\n * @param metadata - Metadata for the data to upload.\n * @returns An UploadTask\n */\nexport function uploadBytesResumable(\n  ref: StorageReference,\n  data: Blob | Uint8Array | ArrayBuffer,\n  metadata?: UploadMetadata\n): UploadTask {\n  ref = getModularInstance(ref);\n  return uploadBytesResumableInternal(\n    ref as Reference,\n    data,\n    metadata as MetadataInternal\n  ) as UploadTask;\n}\n\n/**\n * A `Promise` that resolves with the metadata for this object. If this\n * object doesn't exist or metadata cannot be retrieved, the promise is\n * rejected.\n * @public\n * @param ref - {@link StorageReference} to get metadata from.\n */\nexport function getMetadata(ref: StorageReference): Promise<FullMetadata> {\n  ref = getModularInstance(ref);\n  return getMetadataInternal(ref as Reference) as Promise<FullMetadata>;\n}\n\n/**\n * Updates the metadata for this object.\n * @public\n * @param ref - {@link StorageReference} to update metadata for.\n * @param metadata - The new metadata for the object.\n *     Only values that have been explicitly set will be changed. Explicitly\n *     setting a value to null will remove the metadata.\n * @returns A `Promise` that resolves with the new metadata for this object.\n */\nexport function updateMetadata(\n  ref: StorageReference,\n  metadata: SettableMetadata\n): Promise<FullMetadata> {\n  ref = getModularInstance(ref);\n  return updateMetadataInternal(\n    ref as Reference,\n    metadata as Partial<MetadataInternal>\n  ) as Promise<FullMetadata>;\n}\n\n/**\n * List items (files) and prefixes (folders) under this storage reference.\n *\n * List API is only available for Firebase Rules Version 2.\n *\n * GCS is a key-blob store. Firebase Storage imposes the semantic of '/'\n * delimited folder structure.\n * Refer to GCS's List API if you want to learn more.\n *\n * To adhere to Firebase Rules's Semantics, Firebase Storage does not\n * support objects whose paths end with \"/\" or contain two consecutive\n * \"/\"s. Firebase Storage List API will filter these unsupported objects.\n * list() may fail if there are too many unsupported objects in the bucket.\n * @public\n *\n * @param ref - {@link StorageReference} to get list from.\n * @param options - See {@link ListOptions} for details.\n * @returns A `Promise` that resolves with the items and prefixes.\n *      `prefixes` contains references to sub-folders and `items`\n *      contains references to objects in this folder. `nextPageToken`\n *      can be used to get the rest of the results.\n */\nexport function list(\n  ref: StorageReference,\n  options?: ListOptions\n): Promise<ListResult> {\n  ref = getModularInstance(ref);\n  return listInternal(ref as Reference, options);\n}\n\n/**\n * List all items (files) and prefixes (folders) under this storage reference.\n *\n * This is a helper method for calling list() repeatedly until there are\n * no more results. The default pagination size is 1000.\n *\n * Note: The results may not be consistent if objects are changed while this\n * operation is running.\n *\n * Warning: `listAll` may potentially consume too many resources if there are\n * too many results.\n * @public\n * @param ref - {@link StorageReference} to get list from.\n *\n * @returns A `Promise` that resolves with all the items and prefixes under\n *      the current storage reference. `prefixes` contains references to\n *      sub-directories and `items` contains references to objects in this\n *      folder. `nextPageToken` is never returned.\n */\nexport function listAll(ref: StorageReference): Promise<ListResult> {\n  ref = getModularInstance(ref);\n  return listAllInternal(ref as Reference);\n}\n\n/**\n * Returns the download URL for the given {@link StorageReference}.\n * @public\n * @param ref - {@link StorageReference} to get the download URL for.\n * @returns A `Promise` that resolves with the download\n *     URL for this object.\n */\nexport function getDownloadURL(ref: StorageReference): Promise<string> {\n  ref = getModularInstance(ref);\n  return getDownloadURLInternal(ref as Reference);\n}\n\n/**\n * Deletes the object at this location.\n * @public\n * @param ref - {@link StorageReference} for object to delete.\n * @returns A `Promise` that resolves if the deletion succeeds.\n */\nexport function deleteObject(ref: StorageReference): Promise<void> {\n  ref = getModularInstance(ref);\n  return deleteObjectInternal(ref as Reference);\n}\n\n/**\n * Returns a {@link StorageReference} for the given url.\n * @param storage - {@link FirebaseStorage} instance.\n * @param url - URL. If empty, returns root reference.\n * @public\n */\nexport function ref(storage: FirebaseStorage, url?: string): StorageReference;\n/**\n * Returns a {@link StorageReference} for the given path in the\n * default bucket.\n * @param storageOrRef - {@link FirebaseStorage} or {@link StorageReference}.\n * @param pathOrUrlStorage - path. If empty, returns root reference (if {@link FirebaseStorage}\n * instance provided) or returns same reference (if {@link StorageReference} provided).\n * @public\n */\nexport function ref(\n  storageOrRef: FirebaseStorage | StorageReference,\n  path?: string\n): StorageReference;\nexport function ref(\n  serviceOrRef: FirebaseStorage | StorageReference,\n  pathOrUrl?: string\n): StorageReference | null {\n  serviceOrRef = getModularInstance(serviceOrRef);\n  return refInternal(\n    serviceOrRef as FirebaseStorageImpl | Reference,\n    pathOrUrl\n  );\n}\n\n/**\n * @internal\n */\nexport function _getChild(ref: StorageReference, childPath: string): Reference {\n  return _getChildInternal(ref as Reference, childPath);\n}\n\n/**\n * Gets a {@link FirebaseStorage} instance for the given Firebase app.\n * @public\n * @param app - Firebase app to get {@link FirebaseStorage} instance for.\n * @param bucketUrl - The gs:// url to your Firebase Storage Bucket.\n * If not passed, uses the app's default Storage Bucket.\n * @returns A {@link FirebaseStorage} instance.\n */\nexport function getStorage(\n  app: FirebaseApp = getApp(),\n  bucketUrl?: string\n): FirebaseStorage {\n  app = getModularInstance(app);\n  const storageProvider: Provider<'storage'> = _getProvider(app, STORAGE_TYPE);\n  const storageInstance = storageProvider.getImmediate({\n    identifier: bucketUrl\n  });\n  const emulator = getDefaultEmulatorHostnameAndPort('storage');\n  if (emulator) {\n    connectStorageEmulator(storageInstance, ...emulator);\n  }\n  return storageInstance;\n}\n\n/**\n * Modify this {@link FirebaseStorage} instance to communicate with the Cloud Storage emulator.\n *\n * @param storage - The {@link FirebaseStorage} instance\n * @param host - The emulator host (ex: localhost)\n * @param port - The emulator port (ex: 5001)\n * @param options - Emulator options. `options.mockUserToken` is the mock auth\n * token to use for unit testing Security Rules.\n * @public\n */\nexport function connectStorageEmulator(\n  storage: FirebaseStorage,\n  host: string,\n  port: number,\n  options: {\n    mockUserToken?: EmulatorMockTokenOptions | string;\n  } = {}\n): void {\n  connectEmulatorInternal(storage as FirebaseStorageImpl, host, port, options);\n}\n", "/**\n * Cloud Storage for Firebase\n *\n * @packageDocumentation\n */\n\n/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n// eslint-disable-next-line import/no-extraneous-dependencies\nimport {\n  _registerComponent,\n  registerVersion,\n  SDK_VERSION\n} from '@firebase/app';\n\nimport { FirebaseStorageImpl } from '../src/service';\nimport {\n  Component,\n  ComponentType,\n  ComponentContainer,\n  InstanceFactoryOptions\n} from '@firebase/component';\n\nimport { name, version } from '../package.json';\n\nimport { FirebaseStorage } from './public-types';\nimport { STORAGE_TYPE } from './constants';\n\nexport * from './api';\nexport * from './api.browser';\n\nfunction factory(\n  container: ComponentContainer,\n  { instanceIdentifier: url }: InstanceFactoryOptions\n): FirebaseStorage {\n  const app = container.getProvider('app').getImmediate();\n  const authProvider = container.getProvider('auth-internal');\n  const appCheckProvider = container.getProvider('app-check-internal');\n\n  return new FirebaseStorageImpl(\n    app,\n    authProvider,\n    appCheckProvider,\n    url,\n    SDK_VERSION\n  );\n}\n\nfunction registerStorage(): void {\n  _registerComponent(\n    new Component(\n      STORAGE_TYPE,\n      factory,\n      ComponentType.PUBLIC\n    ).setMultipleInstances(true)\n  );\n  //RUNTIME_ENV will be replaced during the compilation to \"node\" for nodejs and an empty string for browser\n  registerVersion(name, version, '__RUNTIME_ENV__');\n  // BUILD_TARGET will be replaced by values like esm5, esm2017, cjs5, etc during the compilation\n  registerVersion(name, version, '__BUILD_TARGET__');\n}\n\nregisterStorage();\n", "/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n/**\n * Type constant for Firebase Storage.\n */\nexport const STORAGE_TYPE = 'storage';\n", "/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { UploadTaskSnapshot } from '@firebase/storage';\nimport { ReferenceCompat } from './reference';\nimport { UploadTaskCompat } from './task';\nimport * as types from '@firebase/storage-types';\nimport { Compat } from '@firebase/util';\n\nexport class UploadTaskSnapshotCompat\n  implements types.UploadTaskSnapshot, Compat<UploadTaskSnapshot>\n{\n  constructor(\n    readonly _delegate: UploadTaskSnapshot,\n    readonly task: UploadTaskCompat,\n    readonly ref: ReferenceCompat\n  ) {}\n\n  get bytesTransferred(): number {\n    return this._delegate.bytesTransferred;\n  }\n  get metadata(): types.FullMetadata {\n    return this._delegate.metadata as types.FullMetadata;\n  }\n  get state(): string {\n    return this._delegate.state;\n  }\n  get totalBytes(): number {\n    return this._delegate.totalBytes;\n  }\n}\n", "/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {\n  UploadTask,\n  StorageError,\n  UploadTaskSnapshot,\n  TaskEvent,\n  StorageObserver\n} from '@firebase/storage';\nimport { UploadTaskSnapshotCompat } from './tasksnapshot';\nimport { ReferenceCompat } from './reference';\nimport * as types from '@firebase/storage-types';\nimport { Compat } from '@firebase/util';\n\nexport class UploadTaskCompat implements types.UploadTask, Compat<UploadTask> {\n  constructor(\n    readonly _delegate: UploadTask,\n    private readonly _ref: ReferenceCompat\n  ) {}\n\n  get snapshot(): UploadTaskSnapshotCompat {\n    return new UploadTaskSnapshotCompat(\n      this._delegate.snapshot,\n      this,\n      this._ref\n    );\n  }\n\n  cancel = this._delegate.cancel.bind(this._delegate);\n  catch = this._delegate.catch.bind(this._delegate);\n  pause = this._delegate.pause.bind(this._delegate);\n  resume = this._delegate.resume.bind(this._delegate);\n\n  then(\n    onFulfilled?: ((a: UploadTaskSnapshotCompat) => unknown) | null,\n    onRejected?: ((a: StorageError) => unknown) | null\n  ): Promise<unknown> {\n    return this._delegate.then(snapshot => {\n      if (onFulfilled) {\n        return onFulfilled(\n          new UploadTaskSnapshotCompat(snapshot, this, this._ref)\n        );\n      }\n    }, onRejected);\n  }\n\n  on(\n    type: TaskEvent,\n    nextOrObserver?:\n      | types.StorageObserver<UploadTaskSnapshotCompat>\n      | null\n      | ((a: UploadTaskSnapshotCompat) => unknown),\n    error?: ((error: StorageError) => void) | null,\n    completed?: () => void | null\n  ): Unsubscribe | Subscribe<UploadTaskSnapshotCompat> {\n    let wrappedNextOrObserver:\n      | StorageObserver<UploadTaskSnapshot>\n      | undefined\n      | ((a: UploadTaskSnapshot) => unknown) = undefined;\n    if (!!nextOrObserver) {\n      if (typeof nextOrObserver === 'function') {\n        wrappedNextOrObserver = (taskSnapshot: UploadTaskSnapshot) =>\n          nextOrObserver(\n            new UploadTaskSnapshotCompat(taskSnapshot, this, this._ref)\n          );\n      } else {\n        wrappedNextOrObserver = {\n          next: !!nextOrObserver.next\n            ? (taskSnapshot: UploadTaskSnapshot) =>\n                nextOrObserver.next!(\n                  new UploadTaskSnapshotCompat(taskSnapshot, this, this._ref)\n                )\n            : undefined,\n          complete: nextOrObserver.complete || undefined,\n          error: nextOrObserver.error || undefined\n        };\n      }\n    }\n    return this._delegate.on(\n      type,\n      wrappedNextOrObserver,\n      error || undefined,\n      completed || undefined\n    );\n  }\n}\n\n/**\n * Subscribes to an event stream.\n */\nexport type Subscribe<T> = (\n  next?: NextFn<T> | StorageObserver<T>,\n  error?: ErrorFn,\n  complete?: CompleteFn\n) => Unsubscribe;\n\n/**\n * Unsubscribes from a stream.\n */\nexport type Unsubscribe = () => void;\n\n/**\n * Function that is called once for each value in a stream of values.\n */\nexport type NextFn<T> = (value: T) => void;\n\n/**\n * A function that is called with a `FirebaseStorageError`\n * if the event stream ends due to an error.\n */\nexport type ErrorFn = (error: StorageError) => void;\n\n/**\n * A function that is called if the event stream ends normally.\n */\nexport type CompleteFn = () => void;\n", "/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nimport { ListResult } from '@firebase/storage';\nimport * as types from '@firebase/storage-types';\nimport { ReferenceCompat } from './reference';\nimport { StorageServiceCompat } from './service';\nimport { Compat } from '@firebase/util';\n\nexport class ListResultCompat implements types.ListResult, Compat<ListResult> {\n  constructor(\n    readonly _delegate: ListResult,\n    private readonly _service: StorageServiceCompat\n  ) {}\n\n  get prefixes(): ReferenceCompat[] {\n    return this._delegate.prefixes.map(\n      ref => new ReferenceCompat(ref, this._service)\n    );\n  }\n  get items(): ReferenceCompat[] {\n    return this._delegate.items.map(\n      ref => new ReferenceCompat(ref, this._service)\n    );\n  }\n  get nextPageToken(): string | null {\n    return this._delegate.nextPageToken || null;\n  }\n}\n", "/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *  http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {\n  StorageReference,\n  uploadBytesResumable,\n  list,\n  listAll,\n  getDownloadURL,\n  getMetadata,\n  updateMetadata,\n  deleteObject,\n  UploadTask,\n  StringFormat,\n  UploadMetadata,\n  FullMetadata,\n  SettableMetadata,\n  _UploadTask,\n  _getChild,\n  _Reference,\n  _FbsBlob,\n  _dataFromString,\n  _invalidRootOperation\n} from '@firebase/storage';\n\nimport { UploadTaskCompat } from './task';\nimport { ListResultCompat } from './list';\nimport { StorageServiceCompat } from './service';\n\nimport * as types from '@firebase/storage-types';\nimport { Compat } from '@firebase/util';\n\nexport class ReferenceCompat\n  implements types.Reference, Compat<StorageReference>\n{\n  constructor(\n    readonly _delegate: StorageReference,\n    public storage: StorageServiceCompat\n  ) {}\n\n  get name(): string {\n    return this._delegate.name;\n  }\n\n  get bucket(): string {\n    return this._delegate.bucket;\n  }\n\n  get fullPath(): string {\n    return this._delegate.fullPath;\n  }\n\n  toString(): string {\n    return this._delegate.toString();\n  }\n\n  /**\n   * @returns A reference to the object obtained by\n   * appending childPath, removing any duplicate, beginning, or trailing\n   * slashes.\n   */\n  child(childPath: string): types.Reference {\n    const reference = _getChild(this._delegate, childPath);\n    return new ReferenceCompat(reference, this.storage);\n  }\n\n  get root(): types.Reference {\n    return new ReferenceCompat(this._delegate.root, this.storage);\n  }\n\n  /**\n   * @returns A reference to the parent of the\n   * current object, or null if the current object is the root.\n   */\n  get parent(): types.Reference | null {\n    const reference = this._delegate.parent;\n    if (reference == null) {\n      return null;\n    }\n    return new ReferenceCompat(reference, this.storage);\n  }\n\n  /**\n   * Uploads a blob to this object's location.\n   * @param data - The blob to upload.\n   * @returns An UploadTask that lets you control and\n   * observe the upload.\n   */\n  put(\n    data: Blob | Uint8Array | ArrayBuffer,\n    metadata?: types.FullMetadata\n  ): types.UploadTask {\n    this._throwIfRoot('put');\n    return new UploadTaskCompat(\n      uploadBytesResumable(this._delegate, data, metadata as UploadMetadata),\n      this\n    );\n  }\n\n  /**\n   * Uploads a string to this object's location.\n   * @param value - The string to upload.\n   * @param format - The format of the string to upload.\n   * @returns An UploadTask that lets you control and\n   * observe the upload.\n   */\n  putString(\n    value: string,\n    format: StringFormat = StringFormat.RAW,\n    metadata?: types.UploadMetadata\n  ): types.UploadTask {\n    this._throwIfRoot('putString');\n    const data = _dataFromString(format, value);\n    const metadataClone = { ...metadata };\n    if (metadataClone['contentType'] == null && data.contentType != null) {\n      metadataClone['contentType'] = data.contentType;\n    }\n    return new UploadTaskCompat(\n      new _UploadTask(\n        this._delegate as _Reference,\n        new _FbsBlob(data.data, true),\n        metadataClone as FullMetadata & { [k: string]: string }\n      ) as UploadTask,\n      this\n    );\n  }\n\n  /**\n   * List all items (files) and prefixes (folders) under this storage reference.\n   *\n   * This is a helper method for calling list() repeatedly until there are\n   * no more results. The default pagination size is 1000.\n   *\n   * Note: The results may not be consistent if objects are changed while this\n   * operation is running.\n   *\n   * Warning: listAll may potentially consume too many resources if there are\n   * too many results.\n   *\n   * @returns A Promise that resolves with all the items and prefixes under\n   *  the current storage reference. `prefixes` contains references to\n   *  sub-directories and `items` contains references to objects in this\n   *  folder. `nextPageToken` is never returned.\n   */\n  listAll(): Promise<types.ListResult> {\n    return listAll(this._delegate).then(\n      r => new ListResultCompat(r, this.storage)\n    );\n  }\n\n  /**\n   * List items (files) and prefixes (folders) under this storage reference.\n   *\n   * List API is only available for Firebase Rules Version 2.\n   *\n   * GCS is a key-blob store. Firebase Storage imposes the semantic of '/'\n   * delimited folder structure. Refer to GCS's List API if you want to learn more.\n   *\n   * To adhere to Firebase Rules's Semantics, Firebase Storage does not\n   * support objects whose paths end with \"/\" or contain two consecutive\n   * \"/\"s. Firebase Storage List API will filter these unsupported objects.\n   * list() may fail if there are too many unsupported objects in the bucket.\n   *\n   * @param options - See ListOptions for details.\n   * @returns A Promise that resolves with the items and prefixes.\n   * `prefixes` contains references to sub-folders and `items`\n   * contains references to objects in this folder. `nextPageToken`\n   * can be used to get the rest of the results.\n   */\n  list(options?: types.ListOptions | null): Promise<types.ListResult> {\n    return list(this._delegate, options || undefined).then(\n      r => new ListResultCompat(r, this.storage)\n    );\n  }\n\n  /**\n   * A `Promise` that resolves with the metadata for this object. If this\n   * object doesn't exist or metadata cannot be retrieved, the promise is\n   * rejected.\n   */\n  getMetadata(): Promise<types.FullMetadata> {\n    return getMetadata(this._delegate) as Promise<types.FullMetadata>;\n  }\n\n  /**\n   * Updates the metadata for this object.\n   * @param metadata - The new metadata for the object.\n   * Only values that have been explicitly set will be changed. Explicitly\n   * setting a value to null will remove the metadata.\n   * @returns A `Promise` that resolves\n   * with the new metadata for this object.\n   * @see firebaseStorage.Reference.prototype.getMetadata\n   */\n  updateMetadata(\n    metadata: types.SettableMetadata\n  ): Promise<types.FullMetadata> {\n    return updateMetadata(\n      this._delegate,\n      metadata as SettableMetadata\n    ) as Promise<types.FullMetadata>;\n  }\n\n  /**\n   * @returns A `Promise` that resolves with the download\n   * URL for this object.\n   */\n  getDownloadURL(): Promise<string> {\n    return getDownloadURL(this._delegate);\n  }\n\n  /**\n   * Deletes the object at this location.\n   * @returns A `Promise` that resolves if the deletion succeeds.\n   */\n  delete(): Promise<void> {\n    this._throwIfRoot('delete');\n    return deleteObject(this._delegate);\n  }\n\n  private _throwIfRoot(name: string): void {\n    if ((this._delegate as _Reference)._location.path === '') {\n      throw _invalidRootOperation(name);\n    }\n  }\n}\n", "/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport * as types from '@firebase/storage-types';\nimport { FirebaseApp } from '@firebase/app-types';\n\nimport {\n  ref,\n  connectStorageEmulator,\n  FirebaseStorage,\n  _Location,\n  _invalidArgument,\n  _FirebaseStorageImpl\n} from '@firebase/storage';\nimport { ReferenceCompat } from './reference';\nimport { Compat, EmulatorMockTokenOptions } from '@firebase/util';\n\n/**\n * A service that provides firebaseStorage.Reference instances.\n * @param opt_url gs:// url to a custom Storage Bucket\n */\nexport class StorageServiceCompat\n  implements types.FirebaseStorage, Compat<FirebaseStorage>\n{\n  constructor(public app: FirebaseApp, readonly _delegate: FirebaseStorage) {}\n\n  get maxOperationRetryTime(): number {\n    return this._delegate.maxOperationRetryTime;\n  }\n\n  get maxUploadRetryTime(): number {\n    return this._delegate.maxUploadRetryTime;\n  }\n\n  /**\n   * Returns a firebaseStorage.Reference for the given path in the default\n   * bucket.\n   */\n  ref(path?: string): types.Reference {\n    if (isUrl(path)) {\n      throw _invalidArgument(\n        'ref() expected a child path but got a URL, use refFromURL instead.'\n      );\n    }\n    return new ReferenceCompat(ref(this._delegate, path), this);\n  }\n\n  /**\n   * Returns a firebaseStorage.Reference object for the given absolute URL,\n   * which must be a gs:// or http[s]:// URL.\n   */\n  refFromURL(url: string): types.Reference {\n    if (!isUrl(url)) {\n      throw _invalidArgument(\n        'refFromURL() expected a full URL but got a child path, use ref() instead.'\n      );\n    }\n    try {\n      _Location.makeFromUrl(url, (this._delegate as _FirebaseStorageImpl).host);\n    } catch (e) {\n      throw _invalidArgument(\n        'refFromUrl() expected a valid full URL but got an invalid one.'\n      );\n    }\n    return new ReferenceCompat(ref(this._delegate, url), this);\n  }\n\n  setMaxUploadRetryTime(time: number): void {\n    this._delegate.maxUploadRetryTime = time;\n  }\n\n  setMaxOperationRetryTime(time: number): void {\n    this._delegate.maxOperationRetryTime = time;\n  }\n\n  useEmulator(\n    host: string,\n    port: number,\n    options: {\n      mockUserToken?: EmulatorMockTokenOptions | string;\n    } = {}\n  ): void {\n    connectStorageEmulator(this._delegate, host, port, options);\n  }\n}\n\nfunction isUrl(path?: string): boolean {\n  return /^[A-Za-z]+:\\/\\//.test(path as string);\n}\n"], "names": ["StorageErrorCode", "ErrorCode", "instance", "namespaceExports", "stringToByteArray", "str", "out", "p", "i", "length", "c", "charCodeAt", "base64", "byteToCharMap_", "charToByteMap_", "byteToCharMapWebSafe_", "charToByteMapWebSafe_", "ENCODED_VALS_BASE", "ENCODED_VALS", "this", "ENCODED_VALS_WEBSAFE", "HAS_NATIVE_SUPPORT", "atob", "encodeByteArray", "input", "webSafe", "Array", "isArray", "Error", "init_", "byteToCharMap", "output", "byte1", "haveByte2", "byte2", "haveByte3", "byte3", "outByte3", "outByte4", "push", "join", "encodeString", "btoa", "decodeString", "bytes", "pos", "c2", "c3", "c1", "String", "fromCharCode", "u", "byteArrayToString", "decodeStringToByteArray", "charToByteMap", "char<PERSON>t", "byte4", "DecodeBase64StringError", "constructor", "name", "base64urlEncodeWithoutPadding", "base64Encode", "replace", "utf8Bytes", "FirebaseError", "code", "message", "customData", "super", "Object", "setPrototypeOf", "prototype", "captureStackTrace", "ErrorFactory", "create", "service", "serviceName", "errors", "data", "fullCode", "template", "PATTERN", "_", "key", "value", "fullMessage", "getModularInstance", "_delegate", "Component", "instanceFactory", "type", "multipleInstances", "serviceProps", "instantiationMode", "onInstanceCreated", "setInstantiationMode", "mode", "setMultipleInstances", "setServiceProps", "props", "setInstanceCreatedCallback", "callback", "DEFAULT_HOST", "CONFIG_STORAGE_BUCKET_KEY", "StorageError", "status_", "prependCode", "serverResponse", "_baseMessage", "status", "_codeEquals", "unknown", "UNKNOWN", "retryLimitExceeded", "RETRY_LIMIT_EXCEEDED", "canceled", "CANCELED", "cannotSliceBlob", "CANNOT_SLICE_BLOB", "invalidArgument", "INVALID_ARGUMENT", "appDeleted", "APP_DELETED", "invalidRootOperation", "INVALID_ROOT_OPERATION", "invalidFormat", "format", "INVALID_FORMAT", "internalError", "INTERNAL_ERROR", "Location", "bucket", "path", "path_", "isRoot", "fullServerUrl", "encode", "encodeURIComponent", "bucketOnlyServerUrl", "makeFromBucketSpec", "bucketString", "host", "bucketLocation", "makeFromUrl", "e", "INVALID_DEFAULT_BUCKET", "url", "location", "bucketDomain", "gsRegex", "RegExp", "httpModify", "loc", "decodeURIComponent", "firebaseStorageHost", "firebaseStorageRegExp", "cloudStorageHost", "groups", "regex", "indices", "postModify", "slice", "group", "captures", "exec", "bucketValue", "pathValue", "INVALID_URL", "FailRequest", "error", "promise_", "Promise", "reject", "getPromise", "cancel", "_appDelete", "isString", "isNativeBlob", "isNativeBlobDefined", "Blob", "validateNumber", "argument", "minValue", "maxValue", "makeUrl", "urlPart", "protocol", "origin", "makeQueryString", "params", "query<PERSON>art", "nextPart", "hasOwnProperty", "isRetryStatusCode", "additionalRetryCodes", "isFiveHundredCode", "isExtraRetryCode", "indexOf", "isAdditionalRetryCode", "NetworkRequest", "url_", "method_", "headers_", "body_", "successCodes_", "additionalRetryCodes_", "callback_", "errorCallback_", "timeout_", "progressCallback_", "connectionFactory_", "retry", "pendingConnection_", "backoffId_", "canceled_", "appDelete_", "resolve", "resolve_", "reject_", "start_", "doTheRequest", "backoff<PERSON>allback", "RequestEndStatus", "connection", "progressListener", "progressEvent", "loaded", "total", "lengthComputable", "addUploadProgressListener", "send", "then", "removeUploadProgressListener", "hitServer", "getErrorCode", "NO_ERROR", "getStatus", "wasCanceled", "ABORT", "successCode", "backoffDone", "requestWentThrough", "wasSuccessCode", "result", "getResponse", "err", "getErrorText", "doRequest", "backoffCompleteCb", "timeout", "waitSeconds", "retryTimeoutId", "globalTimeoutId", "hitTimeout", "cancelState", "triggeredCallback", "triggerCallback", "args", "apply", "callWithDelay", "millis", "setTimeout", "response<PERSON><PERSON>ler", "clearGlobalTimeout", "clearTimeout", "success", "call", "<PERSON><PERSON><PERSON><PERSON>", "Math", "random", "stopped", "stop", "wasTimeout", "start", "appDelete", "id", "abort", "getBlob", "BlobBuilder", "WebKitBlobBuilder", "undefined", "bb", "append", "UNSUPPORTED_ENVIRONMENT", "decodeBase64", "encoded", "StringFormat", "RAW", "BASE64", "BASE64URL", "DATA_URL", "StringData", "contentType", "dataFromString", "stringData", "utf8Bytes_", "base64Bytes_", "dataUrl", "parts", "DataURLParts", "rest", "decoded", "percentEncodedBytes_", "b", "hi", "lo", "Uint8Array", "hasMinus", "hasUnder", "hasPlus", "hasSlash", "includes", "array", "dataURL", "s", "end", "matches", "match", "middle", "substring", "FbsBlob", "elideCopy", "size", "blobType", "data_", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "byteLength", "set", "size_", "type_", "startByte", "endByte", "realBlob", "sliced", "blob", "webkitSlice", "mozSlice", "buffer", "blobby", "map", "val", "uint8Arrays", "finalLength", "for<PERSON>ach", "merged", "index", "uploadData", "jsonObjectOrNull", "obj", "JSON", "parse", "lastComponent", "lastIndexOf", "noXform_", "metadata", "Mapping", "server", "local", "writable", "xform", "mappings_", "getMappings", "mappings", "nameMapping", "_metadata", "fullPath", "sizeMapping", "Number", "addRef", "defineProperty", "get", "_makeStorageReference", "fromResourceString", "resourceString", "resource", "len", "mapping", "fromResource", "toResourceString", "stringify", "PREFIXES_KEY", "fromResponseString", "listResult", "prefixes", "items", "nextPageToken", "pathWithoutTrailingSlash", "reference", "item", "fromBackendResponse", "RequestInfo", "method", "handler", "urlParams", "headers", "body", "<PERSON><PERSON><PERSON><PERSON>", "progressCallback", "successCodes", "handler<PERSON><PERSON><PERSON>", "cndn", "metadataHandler", "xhr", "text", "<PERSON><PERSON><PERSON><PERSON>", "downloadUrlHandler", "tokens", "tokensList", "split", "alt", "token", "downloadUrlFromResourceString", "_protocol", "sharedErrorHandler", "newErr", "UNAUTHORIZED_APP", "UNAUTHENTICATED", "QUOTA_EXCEEDED", "UNAUTHORIZED", "objectErrorHandler", "shared", "OBJECT_NOT_FOUND", "getMetadata", "maxOperationRetryTime", "requestInfo", "metadataForUpload_", "metadataClone", "assign", "multipartUpload", "X-Goog-Upload-Protocol", "boundary", "toString", "genBoundary", "metadata_", "preBlobPart", "postBlobPart", "maxUploadRetryTime", "ResumableUploadStatus", "current", "finalized", "checkResumeHeader_", "allowed", "getResponseHeader", "allowedStatus", "createResumableUpload", "metadataForUpload", "X-Goog-Upload-Command", "X-Goog-Upload-Header-Content-Length", "X-Goog-Upload-Header-Content-Type", "Content-Type", "getResumableUploadStatus", "sizeString", "isNaN", "continueResumableUpload", "chunkSize", "SERVER_FILE_WRONG_SIZE", "bytesLeft", "bytesToUpload", "min", "uploadCommand", "X-Goog-Upload-Offset", "uploadStatus", "newCurrent", "TaskEvent", "STATE_CHANGED", "TaskState", "RUNNING", "PAUSED", "SUCCESS", "ERROR", "taskStateFromInternalTaskState", "state", "Observer", "nextOrObserver", "complete", "observer", "next", "async", "f", "argsToForward", "XhrTextConnection", "sent_", "xhr_", "XMLHttpRequest", "initXhr", "errorCode_", "sendPromise_", "addEventListener", "NETWORK_ERROR", "open", "setRequestHeader", "response", "statusText", "header", "listener", "upload", "removeEventListener", "responseType", "newTextConnection", "UploadTask", "ref", "_transferred", "_needToFetchStatus", "_needToFetchMetadata", "_observers", "_error", "_uploadUrl", "_request", "_chunkMultiplier", "_resolve", "_reject", "_ref", "_blob", "_mappings", "_resumable", "_shouldDoResumable", "_state", "_error<PERSON><PERSON><PERSON>", "completeTransitions_", "backoffExpired", "isExponentialBackoffExpired", "sleepTime", "max", "_transition", "_metadataErrorHandler", "maxSleepTime", "storage", "_promise", "_start", "_makeProgressCallback", "sizeBefore", "_updateProgress", "_createResumable", "_fetchStatus", "_fetchMetadata", "pendingTimeout", "_continueUpload", "_oneShotUpload", "_resolveToken", "all", "_getAuthToken", "_getAppCheckToken", "authToken", "appCheckToken", "_location", "createRequest", "_makeRequest", "statusRequest", "uploadRequest", "_increaseMultiplier", "newStatus", "metadataRequest", "multipartRequest", "transferred", "old", "_notifyObservers", "wasPaused", "snapshot", "externalState", "bytesTransferred", "totalBytes", "task", "on", "completed", "_addObserver", "_removeObserver", "onFulfilled", "onRejected", "catch", "_notifyObserver", "splice", "_finishPromise", "observers", "triggered", "fbsAsync", "bind", "toCall", "resume", "valid", "pause", "Reference", "_service", "_newRef", "root", "parent", "newPath", "_throwIfRoot", "listAll", "accumulator", "listAllHelper", "pageToken", "opt", "nextPage", "list", "options", "maxResults", "op", "delimiter", "requestsList", "makeRequestWithTokens", "updateMetadata", "requestsUpdateMetadata", "getDownloadURL", "requestsGetDownloadUrl", "NO_DOWNLOAD_URL", "deleteObject", "_xhr", "_text", "requestsDeleteObject", "_get<PERSON><PERSON>d", "child<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "filter", "component", "ref<PERSON><PERSON><PERSON><PERSON>", "FirebaseStorageImpl", "_bucket", "NO_DEFAULT_BUCKET", "serviceOrRef", "pathOrUrl", "test", "extractBucket", "config", "connectStorageEmulator", "port", "mockUserToken", "_overrideAuthToken", "projectId", "uid", "project", "iat", "sub", "user_id", "payload", "iss", "aud", "exp", "auth_time", "firebase", "sign_in_provider", "identities", "alg", "createMockUserToken", "app", "_authProvider", "_appCheckProvider", "_url", "_firebaseVersion", "_host", "_appId", "_deleted", "_maxOperationRetryTime", "_maxUploadRetryTime", "_requests", "Set", "time", "POSITIVE_INFINITY", "auth", "getImmediate", "optional", "tokenData", "getToken", "accessToken", "appCheck", "_delete", "request", "clear", "requestFactory", "makeRequest", "appId", "firebaseVersion", "add", "delete", "uploadBytesResumable", "requestsGetMetadata", "getMetadataInternal", "refInternal", "factory", "container", "instanceIdentifier", "get<PERSON><PERSON><PERSON>", "authProvider", "appCheckProvider", "SDK_VERSION", "_registerComponent", "registerVersion", "UploadTaskSnapshotCompat", "UploadTaskCompat", "wrappedNextOrObserver", "taskSnapshot", "ListResultCompat", "ReferenceCompat", "child", "_getChildInternal", "put", "putString", "_dataFromString", "_UploadTask", "_FbsBlob", "listAllInternal", "r", "listInternal", "updateMetadataInternal", "getDownloadURLInternal", "deleteObjectInternal", "_invalidRootOperation", "StorageServiceCompat", "isUrl", "_invalidArgument", "refFromURL", "_Location", "setMaxUploadRetryTime", "setMaxOperationRetryTime", "useEmulator", "connectEmulatorInternal", "storageExp", "identifier", "Storage", "INTERNAL", "registerComponent"], "mappings": "2bAuFYA,EChBAC,EAAAA,ECXoBC,EACxBC,UC5CkB,SAApBC,EAA8BC,GAElC,MAAMC,EAAgB,GACtB,IAAIC,EAAI,EACR,IAAK,IAAIC,EAAI,EAAGA,EAAIH,EAAII,OAAQD,IAAK,CACnC,IAAIE,EAAIL,EAAIM,WAAWH,GACnBE,EAAI,IACNJ,EAAIC,KAAOG,GACFA,EAAI,KACbJ,EAAIC,KAAQG,GAAK,EAAK,KAGL,QAAZ,MAAJA,IACDF,EAAI,EAAIH,EAAII,QACyB,QAAZ,MAAxBJ,EAAIM,WAAWH,EAAI,KAGpBE,EAAI,QAAgB,KAAJA,IAAe,KAA6B,KAAtBL,EAAIM,aAAaH,IACvDF,EAAIC,KAAQG,GAAK,GAAM,IACvBJ,EAAIC,KAASG,GAAK,GAAM,GAAM,KAI9BJ,EAAIC,KAAQG,GAAK,GAAM,IAHvBJ,EAAIC,KAASG,GAAK,EAAK,GAAM,KAV7BJ,EAAIC,KAAY,GAAJG,EAAU,KAkB1B,OAAOJ,EA5BT,MAyFaM,EAAiB,CAI5BC,eAAgB,KAKhBC,eAAgB,KAMhBC,sBAAuB,KAMvBC,sBAAuB,KAMvBC,kBACE,iEAKFC,mBACE,OAAOC,KAAKF,kBAAoB,OAMlCG,2BACE,OAAOD,KAAKF,kBAAoB,OAUlCI,mBAAoC,mBAATC,KAW3BC,gBAAgBC,EAA8BC,GAC5C,IAAKC,MAAMC,QAAQH,GACjB,MAAMI,MAAM,iDAGdT,KAAKU,QAEL,IAAMC,EAAgBL,EAClBN,KAAKJ,sBACLI,KAAKN,eAET,MAAMkB,EAAS,GAEf,IAAK,IAAIvB,EAAI,EAAGA,EAAIgB,EAAMf,OAAQD,GAAK,EAAG,CACxC,IAAMwB,EAAQR,EAAMhB,GACdyB,EAAYzB,EAAI,EAAIgB,EAAMf,OAC1ByB,EAAQD,EAAYT,EAAMhB,EAAI,GAAK,EACnC2B,EAAY3B,EAAI,EAAIgB,EAAMf,OAC1B2B,EAAQD,EAAYX,EAAMhB,EAAI,GAAK,EAIzC,IAAI6B,GAAqB,GAARH,IAAiB,EAAME,GAAS,EAC7CE,EAAmB,GAARF,EAEVD,IACHG,EAAW,GAENL,IACHI,EAAW,KAIfN,EAAOQ,KACLT,EAdeE,GAAS,GAexBF,GAdyB,EAARE,IAAiB,EAAME,GAAS,GAejDJ,EAAcO,GACdP,EAAcQ,IAIlB,OAAOP,EAAOS,KAAK,KAWrBC,aAAajB,EAAeC,GAG1B,OAAIN,KAAKE,qBAAuBI,EACvBiB,KAAKlB,GAEPL,KAAKI,gBAAgBnB,EAAkBoB,GAAQC,IAWxDkB,aAAanB,EAAeC,GAG1B,OAAIN,KAAKE,qBAAuBI,EACvBH,KAAKE,GA3LQ,SAAUoB,GAElC,MAAMtC,EAAgB,GACtB,IAAIuC,EAAM,EACRnC,EAAI,EACN,KAAOmC,EAAMD,EAAMnC,QAAQ,CACzB,IAiBQqC,EACAC,EAlBFC,EAAKJ,EAAMC,KACbG,EAAK,IACP1C,EAAII,KAAOuC,OAAOC,aAAaF,GACjB,IAALA,GAAYA,EAAK,KACpBF,EAAKF,EAAMC,KACjBvC,EAAII,KAAOuC,OAAOC,cAAoB,GAALF,IAAY,EAAW,GAALF,IACrC,IAALE,GAAYA,EAAK,KAKpBG,IACI,EAALH,IAAW,IAAa,GAJlBJ,EAAMC,OAImB,IAAa,GAHtCD,EAAMC,OAGuC,EAAW,GAFxDD,EAAMC,MAGf,MACFvC,EAAII,KAAOuC,OAAOC,aAAa,OAAUC,GAAK,KAC9C7C,EAAII,KAAOuC,OAAOC,aAAa,OAAc,KAAJC,MAEnCL,EAAKF,EAAMC,KACXE,EAAKH,EAAMC,KACjBvC,EAAII,KAAOuC,OAAOC,cACT,GAALF,IAAY,IAAa,GAALF,IAAY,EAAW,GAALC,IAI9C,OAAOzC,EAAIkC,KAAK,IA+JPY,CAAkBjC,KAAKkC,wBAAwB7B,EAAOC,KAkB/D4B,wBAAwB7B,EAAeC,GACrCN,KAAKU,QAEL,IAAMyB,EAAgB7B,EAClBN,KAAKH,sBACLG,KAAKL,eAET,MAAMiB,EAAmB,GAEzB,IAAK,IAAIvB,EAAI,EAAGA,EAAIgB,EAAMf,QAAU,CAClC,IAAMuB,EAAQsB,EAAc9B,EAAM+B,OAAO/C,MAGnC0B,EADY1B,EAAIgB,EAAMf,OACF6C,EAAc9B,EAAM+B,OAAO/C,IAAM,IACzDA,EAEF,IACM4B,EADY5B,EAAIgB,EAAMf,OACF6C,EAAc9B,EAAM+B,OAAO/C,IAAM,KACzDA,EAEF,IACMgD,EADYhD,EAAIgB,EAAMf,OACF6C,EAAc9B,EAAM+B,OAAO/C,IAAM,GAG3D,KAFEA,EAEW,MAATwB,GAA0B,MAATE,GAA0B,MAATE,GAA0B,MAAToB,EACrD,MAAM,IAAIC,EAIZ1B,EAAOQ,KADWP,GAAS,EAAME,GAAS,GAG5B,KAAVE,IAEFL,EAAOQ,KADYL,GAAS,EAAK,IAASE,GAAS,GAGrC,KAAVoB,GAEFzB,EAAOQ,KADYH,GAAS,EAAK,IAAQoB,IAM/C,OAAOzB,GAQTF,QACE,IAAKV,KAAKN,eAAgB,CACxBM,KAAKN,eAAiB,GACtBM,KAAKL,eAAiB,GACtBK,KAAKJ,sBAAwB,GAC7BI,KAAKH,sBAAwB,GAG7B,IAAK,IAAIR,EAAI,EAAGA,EAAIW,KAAKD,aAAaT,OAAQD,IAC5CW,KAAKN,eAAeL,GAAKW,KAAKD,aAAaqC,OAAO/C,GAClDW,KAAKL,eAAeK,KAAKN,eAAeL,IAAMA,EAC9CW,KAAKJ,sBAAsBP,GAAKW,KAAKC,qBAAqBmC,OAAO/C,GACjEW,KAAKH,sBAAsBG,KAAKJ,sBAAsBP,IAAMA,EAGxDA,GAAKW,KAAKF,kBAAkBR,SAC9BU,KAAKL,eAAeK,KAAKC,qBAAqBmC,OAAO/C,IAAMA,EAC3DW,KAAKH,sBAAsBG,KAAKD,aAAaqC,OAAO/C,IAAMA,YAUvDiD,UAAgC7B,MAA7C8B,kCACWvC,KAAIwC,KAAG,2BAe2B,SAAhCC,EAA0CvD,GAErD,OAAOwD,EAAaxD,GAAKyD,QAAQ,MAAO,IAXnC,MAAMD,EAAe,SAAUxD,GACpC,IAAM0D,EAAY3D,EAAkBC,GACpC,OAAOO,EAAOW,gBAAgBwC,GAAW,UClR9BC,UAAsBpC,MAIjC8B,YAEWO,EACTC,EAEOC,GAEPC,MAAMF,GALG/C,KAAI8C,KAAJA,EAGF9C,KAAUgD,WAAVA,EAPAhD,KAAIwC,KAdI,gBA2BfU,OAAOC,eAAenD,KAAM6C,EAAcO,WAItC3C,MAAM4C,mBACR5C,MAAM4C,kBAAkBrD,KAAMsD,EAAaF,UAAUG,eAK9CD,EAIXf,YACmBiB,EACAC,EACAC,GAFA1D,KAAOwD,QAAPA,EACAxD,KAAWyD,YAAXA,EACAzD,KAAM0D,OAANA,EAGnBH,OACET,KACGa,GAEH,IAcuCA,EAdjCX,EAAcW,EAAK,IAAoB,GACvCC,KAAc5D,KAAKwD,WAAWV,IAC9Be,EAAW7D,KAAK0D,OAAOZ,GAEvBC,EAAUc,GAUuBF,EAVcX,EAAVa,EAW7BlB,QAAQmB,EAAS,CAACC,EAAGC,KACnC,IAAMC,EAAQN,EAAKK,GACnB,OAAgB,MAATC,EAAgBnC,OAAOmC,OAAaD,SAbwB,QAE7DE,KAAiBlE,KAAKyD,gBAAgBV,MAAYa,MAIxD,OAFc,IAAIf,EAAce,EAAUM,EAAalB,IAa3D,MAAMc,EAAU,gBChHV,SAAUK,EACdX,GAEA,OAAIA,GAAYA,EAA+BY,UACrCZ,EAA+BY,UAEhCZ,QCCEa,EAiBX9B,YACWC,EACA8B,EACAC,GAFAvE,KAAIwC,KAAJA,EACAxC,KAAesE,gBAAfA,EACAtE,KAAIuE,KAAJA,EAnBXvE,KAAiBwE,mBAAG,EAIpBxE,KAAYyE,aAAe,GAE3BzE,KAAA0E,kBAA2C,OAE3C1E,KAAiB2E,kBAAwC,KAczDC,qBAAqBC,GAEnB,OADA7E,KAAK0E,kBAAoBG,EAClB7E,KAGT8E,qBAAqBN,GAEnB,OADAxE,KAAKwE,kBAAoBA,EAClBxE,KAGT+E,gBAAgBC,GAEd,OADAhF,KAAKyE,aAAeO,EACbhF,KAGTiF,2BAA2BC,GAEzB,OADAlF,KAAK2E,kBAAoBO,EAClBlF,MC7CJ,MAAMmF,EAAe,iCAKfC,EAA4B,sBPH5BC,UAAqBxC,EAahCN,YAAYO,EAAwBC,EAAyBuC,EAAU,GACrErC,MACEsC,EAAYzC,wBACSC,MAAYwC,EAAYzC,OAHY9C,KAAOsF,QAAPA,EAR7DtF,KAAAgD,WAAgD,CAAEwC,eAAgB,MAahExF,KAAKyF,aAAezF,KAAK+C,QAGzBG,OAAOC,eAAenD,KAAMqF,EAAajC,WAG3CsC,aACE,OAAO1F,KAAKsF,QAGdI,WAAWA,GACT1F,KAAKsF,QAAUI,EAMjBC,YAAY7C,GACV,OAAOyC,EAAYzC,KAAU9C,KAAK8C,KAMpC0C,qBACE,OAAOxF,KAAKgD,WAAWwC,eAGzBA,mBAAmBA,GACjBxF,KAAKgD,WAAWwC,eAAiBA,EAC7BxF,KAAKgD,WAAWwC,eAClBxF,KAAK+C,WAAa/C,KAAKyF,iBAAiBzF,KAAKgD,WAAWwC,iBAExDxF,KAAK+C,QAAU/C,KAAKyF,cAyCpB,SAAUF,EAAYzC,GAC1B,MAAO,WAAaA,EAGN,SAAA8C,IAId,OAAO,IAAIP,EAAaxG,EAAiBgH,QAFvC,kFAyDY,SAAAC,IACd,OAAO,IAAIT,EACTxG,EAAiBkH,qBACjB,4DAqBY,SAAAC,IACd,OAAO,IAAIX,EACTxG,EAAiBoH,SACjB,sCAmCY,SAAAC,IACd,OAAO,IAAIb,EACTxG,EAAiBsH,kBACjB,0DA4BE,SAAUC,EAAgBrD,GAC9B,OAAO,IAAIsC,EAAaxG,EAAiBwH,iBAAkBtD,GAgC7C,SAAAuD,IACd,OAAO,IAAIjB,EACTxG,EAAiB0H,YACjB,iCASE,SAAUC,EAAqBhE,GACnC,OAAO,IAAI6C,EACTxG,EAAiB4H,uBACjB,kBACEjE,EACA,mHASU,SAAAkE,EAAcC,EAAgB5D,GAC5C,OAAO,IAAIsC,EACTxG,EAAiB+H,eACjB,iCAAmCD,EAAS,MAAQ5D,GAclD,SAAU8D,EAAc9D,GAC5B,MAAM,IAAIsC,EACRxG,EAAiBiI,eACjB,mBAAqB/D,IAzQblE,EAAAA,EAAAA,GA4BX,IA1BC,QAAA,UACAA,EAAA,iBAAA,mBACAA,EAAA,iBAAA,mBACAA,EAAA,kBAAA,oBACAA,EAAA,eAAA,iBACAA,EAAA,gBAAA,kBACAA,EAAA,aAAA,eACAA,EAAA,iBAAA,mBACAA,EAAA,qBAAA,uBACAA,EAAA,iBAAA,mBACAA,EAAA,SAAA,WAEAA,EAAA,mBAAA,qBACAA,EAAA,YAAA,cACAA,EAAA,uBAAA,yBACAA,EAAA,kBAAA,oBACAA,EAAA,kBAAA,oBACAA,EAAA,uBAAA,yBACAA,EAAA,gBAAA,kBACAA,EAAA,iBAAA,mBACAA,EAAA,uBAAA,yBACAA,EAAA,YAAA,cACAA,EAAA,uBAAA,yBACAA,EAAA,eAAA,iBACAA,EAAA,eAAA,iBACAA,EAAA,wBAAA,gCQpFWkI,EAGXxE,YAA4ByE,EAAgBC,GAAhBjH,KAAMgH,OAANA,EAC1BhH,KAAKkH,MAAQD,EAGfA,WACE,OAAOjH,KAAKkH,MAGdC,aACE,OAA4B,IAArBnH,KAAKiH,KAAK3H,OAGnB8H,gBACE,MAAMC,EAASC,mBACf,MAAO,MAAQD,EAAOrH,KAAKgH,QAAU,MAAQK,EAAOrH,KAAKiH,MAG3DM,sBACE,MAAMF,EAASC,mBACf,MAAO,MAAQD,EAAOrH,KAAKgH,QAAU,KAGvCQ,0BAA0BC,EAAsBC,GAC9C,IAAIC,EACJ,IACEA,EAAiBZ,EAASa,YAAYH,EAAcC,GACpD,MAAOG,GAGP,OAAO,IAAId,EAASU,EAAc,IAEpC,GAA4B,KAAxBE,EAAeV,KACjB,OAAOU,EAEP,MR8J+BX,EQ9JJS,ER+JxB,IAAIpC,EACTxG,EAAiBiJ,uBACjB,2BAA6Bd,EAAS,MQ7JxCY,mBAAmBG,EAAaL,GAC9B,IAAIM,EAA4B,KAChC,IAAMC,EAAe,sBAOrB,IACMC,EAAU,IAAIC,OAAO,SAAWF,EADvB,YAC8C,KAG7D,SAASG,EAAWC,GAClBA,EAAInB,MAAQoB,mBAAmBD,EAAIpB,MAErC,IACMsB,EAAsBb,EAAK/E,QAAQ,OAAQ,OAE3C6F,EAAwB,IAAIL,oBACnBI,sBAAoCN,qBACjD,KAIIQ,EACJf,IAASvC,EACL,sDACAuC,EAQAgB,EAAS,CACb,CAAEC,MAAOT,EAASU,QA1BF,CAAE5B,OAAQ,EAAGC,KAAM,GA0BG4B,WAjCxC,SAAkBR,GAC6B,MAAzCA,EAAIpB,KAAK7E,OAAOiG,EAAIpB,KAAK3H,OAAS,KACpC+I,EAAInB,MAAQmB,EAAInB,MAAM4B,MAAM,GAAI,MAgClC,CACEH,MAAOH,EACPI,QAjB2B,CAAE5B,OAAQ,EAAGC,KAAM,GAkB9C4B,WAAYT,GAEd,CACEO,MAduB,IAAIR,oBAChBM,KAAoBR,aACjC,KAaEW,QAXwB,CAAE5B,OAAQ,EAAGC,KAAM,GAY3C4B,WAAYT,IAGhB,IAAK,IAAI/I,EAAI,EAAGA,EAAIqJ,EAAOpJ,OAAQD,IAAK,CACtC,MAAM0J,EAAQL,EAAOrJ,GACrB,IAAM2J,EAAWD,EAAMJ,MAAMM,KAAKlB,GAClC,GAAIiB,EAAU,CACZ,IAAME,EAAcF,EAASD,EAAMH,QAAQ5B,QAC3C,IAAImC,EAAYH,EAASD,EAAMH,QAAQ3B,MAErCkC,EADGA,GACS,GAEdnB,EAAW,IAAIjB,EAASmC,EAAaC,GACrCJ,EAAMF,WAAWb,GACjB,OAGJ,GAAgB,MAAZA,EACF,MRmFqBD,EQnFJA,ERoFd,IAAI1C,EACTxG,EAAiBuK,YACjB,gBAAkBrB,EAAM,MQpFxB,OAAOC,SCnHEqB,EAGX9G,YAAY+G,GACVtJ,KAAKuJ,SAAWC,QAAQC,OAAUH,GAIpCI,aACE,OAAO1J,KAAKuJ,SAIdI,OAAOC,EAAAA,KCHH,SAAUC,EAASzK,GACvB,MAAoB,iBAANA,GAAkBA,aAAa0C,OAGzC,SAAUgI,EAAa1K,GAC3B,OAAO2K,KAAyB3K,aAAa4K,KAG/B,SAAAD,IACd,MAAuB,oBAATC,KAGV,SAAUC,EACdC,EACAC,EACAC,EACAnG,GAEA,GAAIA,EAAQkG,EACV,MAAM/D,wBACkB8D,gBAAuBC,iBAGjD,GAAYC,EAARnG,EACF,MAAMmC,wBACkB8D,gBAAuBE,cCnCnC,SAAAC,EACdC,EACA5C,EACA6C,GAEA,IAAIC,EACY,MAAZD,aACkB7C,IAFTA,EAIb,SAAU6C,OAAcC,OAAYF,IAGhC,SAAUG,EAAgBC,GAC9B,MAAMrD,EAASC,mBACf,IAAIqD,EAAY,IAChB,IAAK,MAAM3G,KAAO0G,EAAQ,CACxB,IACQE,EADJF,EAAOG,eAAe7G,KAClB4G,EAAWvD,EAAOrD,GAAO,IAAMqD,EAAOqD,EAAO1G,IACnD2G,EAAYA,EAAYC,EAAW,KAMvC,OADAD,EAAYA,EAAU7B,MAAM,GAAI,GACzB6B,ECvBO,SAAAG,EACdpF,EACAqF,GAIA,IAAMC,EAA8B,KAAVtF,GAAiBA,EAAS,IAO9CuF,GAAwD,IANtC,CAEtB,IAEA,KAEuCC,QAAQxF,GAC3CyF,GAAkE,IAA1CJ,EAAqBG,QAAQxF,GAC3D,OAAOsF,GAAqBC,GAAoBE,GXiCtCrM,EAAAA,EAAAA,GAIX,IAHCA,EAAA,SAAA,GAAA,WACAA,EAAAA,EAAA,cAAA,GAAA,gBACAA,EAAAA,EAAA,MAAA,GAAA,cYvBIsM,EAUJ7I,YACU8I,EACAC,EACAC,EACAC,EACAC,EACAC,EACAC,EACAC,EACAC,EACAC,EACAC,EACAC,GAAQ,GAXRhM,KAAIqL,KAAJA,EACArL,KAAOsL,QAAPA,EACAtL,KAAQuL,SAARA,EACAvL,KAAKwL,MAALA,EACAxL,KAAayL,cAAbA,EACAzL,KAAqB0L,sBAArBA,EACA1L,KAAS2L,UAATA,EACA3L,KAAc4L,eAAdA,EACA5L,KAAQ6L,SAARA,EACA7L,KAAiB8L,kBAAjBA,EACA9L,KAAkB+L,mBAAlBA,EACA/L,KAAKgM,MAALA,EArBFhM,KAAkBiM,mBAAyB,KAC3CjM,KAAUkM,WAAqB,KAI/BlM,KAASmM,WAAY,EACrBnM,KAAUoM,YAAY,EAiB5BpM,KAAKuJ,SAAW,IAAIC,QAAQ,CAAC6C,EAAS5C,KACpCzJ,KAAKsM,SAAWD,EAChBrM,KAAKuM,QAAU9C,EACfzJ,KAAKwM,WAODA,SACN,IAAMC,EAGM,CAACC,EAAiB1G,KAC5B,GAAIA,EACF0G,GAAgB,EAAO,IAAIC,GAAiB,EAAO,MAAM,QAD3D,CAIA,MAAMC,EAAa5M,KAAK+L,qBACxB/L,KAAKiM,mBAAqBW,EAE1B,MAAMC,EAEMC,IACV,IAAMC,EAASD,EAAcC,OACvBC,EAAQF,EAAcG,iBAAmBH,EAAcE,OAAS,EACvC,OAA3BhN,KAAK8L,mBACP9L,KAAK8L,kBAAkBiB,EAAQC,IAGJ,OAA3BhN,KAAK8L,mBACPc,EAAWM,0BAA0BL,GAKvCD,EACGO,KAAKnN,KAAKqL,KAAMrL,KAAKsL,QAAStL,KAAKwL,MAAOxL,KAAKuL,UAC/C6B,KAAK,KAC2B,OAA3BpN,KAAK8L,mBACPc,EAAWS,6BAA6BR,GAE1C7M,KAAKiM,mBAAqB,KAC1B,IAAMqB,EAAYV,EAAWW,iBAAmBzO,EAAU0O,SACpD9H,EAASkH,EAAWa,aAEvBH,GACAxC,EAAkBpF,EAAQ1F,KAAK0L,wBAC9B1L,KAAKgM,OAED0B,EAAcd,EAAWW,iBAAmBzO,EAAU6O,MAC5DjB,GACE,EACA,IAAIC,GAAiB,EAAO,KAAMe,MAIhCE,GAAsD,IAAxC5N,KAAKyL,cAAcP,QAAQxF,GAC/CgH,GAAgB,EAAM,IAAIC,EAAiBiB,EAAahB,SAQxDiB,EAGM,CAACC,EAAoBpI,KAC/B,MAAM2G,EAAUrM,KAAKsM,SACf7C,EAASzJ,KAAKuM,QACdK,EAAalH,EAAOkH,WAC1B,GAAIlH,EAAOqI,eACT,IACE,IAAMC,EAAShO,KAAK2L,UAAUiB,EAAYA,EAAWqB,oBHnIhD,IGoISD,EACZ3B,EAAQ2B,GAER3B,IAEF,MAAOxE,GACP4B,EAAO5B,QAGT,GAAmB,OAAf+E,EAAqB,CACvB,MAAMsB,EAAMtI,IACZsI,EAAI1I,eAAiBoH,EAAWuB,eAC5BnO,KAAK4L,eACPnC,EAAOzJ,KAAK4L,eAAegB,EAAYsB,IAEvCzE,EAAOyE,OAEJ,CACL,IAIQA,EAJJxI,EAAOM,UACHkI,GAAMlO,KAAKoM,WAAa9F,EAAeN,KAC7CyD,EAAOyE,KAEDA,EAAMpI,IACZ2D,EAAOyE,MAKXlO,KAAKmM,UACP0B,EAAY,EAAO,IAAIlB,GAAiB,EAAO,MAAM,IAErD3M,KAAKkM,WClJL,SACJkC,EAKAC,EACAC,GAIA,IAAIC,EAAc,EAIdC,EAAsB,KAEtBC,EAAuB,KACvBC,GAAa,EACbC,EAAc,EAElB,SAAS3I,IACP,OAAuB,IAAhB2I,EAET,IAAIC,GAAoB,EAExB,SAASC,KAAmBC,GACrBF,IACHA,GAAoB,EACpBP,EAAkBU,MAAM,KAAMD,IAIlC,SAASE,EAAcC,GACrBT,EAAiBU,WAAW,KAC1BV,EAAiB,KACjBJ,EAAUe,EAAiBnJ,MAC1BiJ,GAGL,SAASG,IACHX,GACFY,aAAaZ,GAIjB,SAASU,EAAgBG,KAAqBR,GAC5C,GAAIF,EACFQ,QADF,CAIA,GAAIE,EAGF,OAFAF,SACAP,EAAgBU,KAAK,KAAMD,KAAYR,GAIzC,GADiB9I,KAAc0I,EAI7B,OAFAU,SACAP,EAAgBU,KAAK,KAAMD,KAAYR,GAGrCP,EAAc,KAEhBA,GAAe,GAEjB,IAAIiB,EAGFA,EAFkB,IAAhBb,GACFA,EAAc,EACD,GAEgC,KAA/BJ,EAAckB,KAAKC,UAEnCV,EAAcQ,IAEhB,IAAIG,GAAU,EAEd,SAASC,EAAKC,GACRF,IAGJA,GAAU,EACVP,IACIR,IAGmB,OAAnBJ,GACGqB,IACHlB,EAAc,GAEhBU,aAAab,GACbQ,EAAc,IAETa,IACHlB,EAAc,KASpB,OALAK,EAAc,GACdP,EAAkBS,WAAW,KAE3BU,EADAlB,GAAa,IAEZJ,GACIsB,ED2CeE,CAAMrD,EAAcoB,EAAa7N,KAAK6L,UAK5DnC,aACE,OAAO1J,KAAKuJ,SAIdI,OAAOoG,GACL/P,KAAKmM,WAAY,EACjBnM,KAAKoM,WAAa2D,IAAa,EACP,OAApB/P,KAAKkM,aC7CX8D,ED8CShQ,KAAKkM,aC9CX,GDgD+B,OAA5BlM,KAAKiM,oBACPjM,KAAKiM,mBAAmBgE,eASjBtD,EAMXpK,YACSwL,EACAnB,EACP5G,GAFOhG,KAAc+N,eAAdA,EACA/N,KAAU4M,WAAVA,EAGP5M,KAAKgG,WAAaA,GEpLN,SAAAkK,KAAWpB,GACzB,MAAMqB,EAhBqB,oBAAhBA,YACFA,YAC+B,oBAAtBC,kBACTA,uBADF,EAeP,QAAoBC,IAAhBF,EAA2B,CAC7B,MAAMG,EAAK,IAAIH,EACf,IAAK,IAAI9Q,EAAI,EAAGA,EAAIyP,EAAKxP,OAAQD,IAC/BiR,EAAGC,OAAOzB,EAAKzP,IAEjB,OAAOiR,EAAGJ,UAEV,GAAInG,IACF,OAAO,IAAIC,KAAK8E,GAEhB,MAAM,IAAIzJ,EACRxG,EAAiB2R,wBACjB,uDClCF,SAAUC,EAAaC,GAC3B,GAAoB,oBAATvQ,KACT,MhBkPK,IAAIkF,EACTxG,EAAiB2R,wBACjB,iKgBlPF,OAAOrQ,KAAKuQ,GCKD,MAAAC,EAAe,CAQ1BC,IAAK,MAOLC,OAAQ,SAORC,UAAW,YAUXC,SAAU,kBAGCC,EAGXzO,YAAmBoB,EAAkBsN,GAAlBjR,KAAI2D,KAAJA,EACjB3D,KAAKiR,YAAcA,GAAe,MAOtB,SAAAC,EACdvK,EACAwK,GAEA,OAAQxK,GACN,KAAKgK,EAAaC,IAChB,OAAO,IAAII,EAAWI,EAAWD,IACnC,KAAKR,EAAaE,OAClB,KAAKF,EAAaG,UAChB,OAAO,IAAIE,EAAWK,EAAa1K,EAAQwK,IAC7C,KAAKR,EAAaI,SAChB,OAAO,IAAIC,GAwIaM,EAvIRH,GAwIdI,EAAQ,IAAIC,EAAaF,IACrB7R,OACD4R,EAAaV,EAAaE,OAAQU,EAAME,MArF7C,SAA+BxN,GACnC,IAAIyN,EACJ,IACEA,EAAUpJ,mBAAmBrE,GAC7B,MAAO4D,GACP,MAAMnB,EAAciK,EAAaI,SAAU,uBAE7C,OAAOK,EAAWM,GAgFTC,CAAqBJ,EAAME,QAIFH,EA/IRH,EAgJZ,IAAIK,EAAaF,GAClBL,cAFT,IATwBK,EACtBC,EAhIN,MAAM3L,IAGF,SAAUwL,EAAWnN,GACzB,MAAM2N,EAAc,GACpB,IAAK,IAAIvS,EAAI,EAAGA,EAAI4E,EAAM3E,OAAQD,IAAK,CACrC,IAAIE,EAAI0E,EAAMzE,WAAWH,GACzB,IAccwS,EACAC,EAfVvS,GAAK,IACPqS,EAAExQ,KAAK7B,GAEHA,GAAK,KACPqS,EAAExQ,KAAK,IAAO7B,GAAK,EAAI,IAAW,GAAJA,GAEV,QAAX,MAAJA,GAGDF,EAAI4E,EAAM3E,OAAS,GAA2C,QAAX,MAA1B2E,EAAMzE,WAAWH,EAAI,KAKxCwS,EAAKtS,EACLuS,EAAK7N,EAAMzE,aAAaH,GAC9BE,EAAI,OAAe,KAALsS,IAAc,GAAY,KAALC,EACnCF,EAAExQ,KACA,IAAO7B,GAAK,GACZ,IAAQA,GAAK,GAAM,GACnB,IAAQA,GAAK,EAAK,GAClB,IAAW,GAAJA,IATTqS,EAAExQ,KAAK,IAAK,IAAK,KAaC,QAAX,MAAJ7B,GAEHqS,EAAExQ,KAAK,IAAK,IAAK,KAEjBwQ,EAAExQ,KAAK,IAAO7B,GAAK,GAAK,IAAQA,GAAK,EAAK,GAAK,IAAW,GAAJA,GAMhE,OAAO,IAAIwS,WAAWH,GAaR,SAAAP,EAAa1K,EAAsB1C,GACjD,OAAQ0C,GACN,KAAKgK,EAAaE,OAChB,IAAMmB,GAAmC,IAAxB/N,EAAMiH,QAAQ,KACzB+G,GAAmC,IAAxBhO,EAAMiH,QAAQ,KAC/B,GAAI8G,GAAYC,EAEd,MAAMvL,EACJC,EACA,uBAHkBqL,EAAW,IAAM,KAKjC,qCAGN,MAEF,KAAKrB,EAAaG,UACVoB,GAAkC,IAAxBjO,EAAMiH,QAAQ,KACxBiH,GAAmC,IAAxBlO,EAAMiH,QAAQ,KAC/B,GAAIgH,GAAWC,EAEb,MAAMzL,EACJC,EACA,uBAHkBuL,EAAU,IAAM,KAGI,kCAG1CjO,EAAQA,EAAMtB,QAAQ,KAAM,KAAKA,QAAQ,KAAM,KAMnD,IAAIlB,EACJ,IACEA,EAAQgP,EAAaxM,GACrB,MAAO4D,GACP,GAAKA,EAAY9E,QAAQqP,SAAS,YAChC,MAAMvK,EAER,MAAMnB,EAAcC,EAAQ,2BAE9B,MAAM0L,EAAQ,IAAIN,WAAWtQ,EAAMnC,QACnC,IAAK,IAAID,EAAI,EAAGA,EAAIoC,EAAMnC,OAAQD,IAChCgT,EAAMhT,GAAKoC,EAAMjC,WAAWH,GAE9B,OAAOgT,QAGHb,EAKJjP,YAAY+P,GAJZtS,KAAMP,QAAY,EAClBO,KAAWiR,YAAkB,KAI3B,IAgCcsB,EAAWC,EAhCnBC,EAAUH,EAAQI,MAAM,mBAC9B,GAAgB,OAAZD,EACF,MAAM/L,EACJiK,EAAaI,SACb,yDAGJ,MAAM4B,EAASF,EAAQ,IAAM,KACf,MAAVE,IACF3S,KAAKP,QAuBO8S,EAvBWI,EAuBAH,EAvBQ,UAwBhBD,EAAEjT,QAAUkT,EAAIlT,QAK5BiT,EAAEK,UAAUL,EAAEjT,OAASkT,EAAIlT,UAAYkT,GA5B1CxS,KAAKiR,YAAcjR,KAAKP,OACpBkT,EAAOC,UAAU,EAAGD,EAAOrT,OAAS,UAAUA,QAC9CqT,GAEN3S,KAAKyR,KAAOa,EAAQM,UAAUN,EAAQpH,QAAQ,KAAO,UCzL5C2H,EAKXtQ,YAAYoB,EAAuCmP,GACjD,IAAIC,EAAe,EACfC,EAAmB,GACnBlJ,EAAanG,IACf3D,KAAKiT,MAAQtP,EACboP,EAAQpP,EAAcoP,KACtBC,EAAYrP,EAAcY,MACjBZ,aAAgBuP,aACrBJ,EACF9S,KAAKiT,MAAQ,IAAIlB,WAAWpO,IAE5B3D,KAAKiT,MAAQ,IAAIlB,WAAWpO,EAAKwP,YACjCnT,KAAKiT,MAAMG,IAAI,IAAIrB,WAAWpO,KAEhCoP,EAAO/S,KAAKiT,MAAM3T,QACTqE,aAAgBoO,aACrBe,EACF9S,KAAKiT,MAAQtP,GAEb3D,KAAKiT,MAAQ,IAAIlB,WAAWpO,EAAKrE,QACjCU,KAAKiT,MAAMG,IAAIzP,IAEjBoP,EAAOpP,EAAKrE,QAEdU,KAAKqT,MAAQN,EACb/S,KAAKsT,MAAQN,EAGfD,OACE,OAAO/S,KAAKqT,MAGd9O,OACE,OAAOvE,KAAKsT,MAGdxK,MAAMyK,EAAmBC,GACvB,GAAI1J,EAAa9J,KAAKiT,OAAQ,CAC5B,IAAMQ,EAAWzT,KAAKiT,MAChBS,GHR0B5D,EGQGyD,EHRYf,EGQDgB,GHR1BG,EGQKF,GHPpBG,YACAD,EAAKC,YAAY9D,EAAO0C,GACtBmB,EAAKE,SACPF,EAAKE,SAAS/D,EAAO0C,GACnBmB,EAAK7K,MACP6K,EAAK7K,MAAMgH,EAAO0C,GAEpB,MGCH,OAAe,OAAXkB,EACK,KAEF,IAAIb,EAAQa,GAEnB,IHdoBC,EAAY7D,EAAe0C,EGczC1J,EAAQ,IAAIiJ,WACf/R,KAAKiT,MAAqBa,OAC3BP,EACAC,EAAUD,GAEZ,OAAO,IAAIV,EAAQ/J,GAAO,GAI9BoH,kBAAkBpB,GAChB,GAAI/E,IAAuB,CACzB,IAAMgK,EAA4CjF,EAAKkF,IACrD,GACMC,aAAepB,EACVoB,EAAIhB,MAEJgB,GAIb,OAAO,IAAIpB,EAAQ3C,EAAQnB,MAAM,KAAMgF,IAClC,CACL,MAAMG,EAA4BpF,EAAKkF,IACrC,GACMnK,EAASoK,GACJ/C,EAAeP,EAAaC,IAAKqD,GAAetQ,KAG/CsQ,EAAgBhB,OAI9B,IAAIkB,EAAc,EAClBD,EAAYE,QAAQ,IAClBD,GAAe9B,EAAMc,aAEvB,MAAMkB,EAAS,IAAItC,WAAWoC,GAC9B,IAAIG,EAAQ,EAMZ,OALAJ,EAAYE,QAAQ,IAClB,IAAK,IAAI/U,EAAI,EAAGA,EAAIgT,EAAM/S,OAAQD,IAChCgV,EAAOC,KAAWjC,EAAMhT,KAGrB,IAAIwT,EAAQwB,GAAQ,IAI/BE,aACE,OAAOvU,KAAKiT,OC7GV,SAAUuB,EACdjC,GAEA,IAAIkC,EACJ,IACEA,EAAMC,KAAKC,MAAMpC,GACjB,MAAO1K,GACP,OAAO,KAET,MTFoB,iBADWzI,ESGVqV,ITFYlU,MAAMC,QAAQpB,GSKtC,KAFAqV,ECsBL,SAAUG,EAAc3N,GAC5B,IAAMqN,EAAQrN,EAAK4N,YAAY,IAAK5N,EAAK3H,OAAS,GAClD,OAAe,IAAXgV,EACKrN,EAEAA,EAAK6B,MAAMwL,EAAQ,GC7Bd,SAAAQ,EAAYC,EAAoB9Q,GAC9C,OAAOA,QAGH+Q,EAKJzS,YACS0S,EACPC,EACAC,EACAC,GAHOpV,KAAMiV,OAANA,EAKPjV,KAAKkV,MAAQA,GAASD,EACtBjV,KAAKmV,WAAaA,EAClBnV,KAAKoV,MAAQA,GAASN,GAO1B,IAAIO,GAA6B,KAUjB,SAAAC,KACd,GAAID,GACF,OAAOA,GAET,MAAME,EAAqB,GAC3BA,EAASnU,KAAK,IAAI4T,EAAgB,WAClCO,EAASnU,KAAK,IAAI4T,EAAgB,eAClCO,EAASnU,KAAK,IAAI4T,EAAgB,mBAClCO,EAASnU,KAAK,IAAI4T,EAAgB,OAAQ,YAAY,IAQtD,MAAMQ,EAAc,IAAIR,EAAgB,QACxCQ,EAAYJ,MAPZ,SACEK,EACAC,GAEA,OArBG7L,EADmB6L,EAsBLA,IArBQA,EAASpW,OAAS,EACpCoW,EAEAd,EAAcc,IAsBvBH,EAASnU,KAAKoU,GAed,MAAMG,EAAc,IAAIX,EAAgB,QAaxC,OAZAW,EAAYP,MAXZ,SACEK,EACA1C,GAEA,YAAa1C,IAAT0C,EACK6C,OAAO7C,GAEPA,GAKXwC,EAASnU,KAAKuU,GACdJ,EAASnU,KAAK,IAAI4T,EAAgB,gBAClCO,EAASnU,KAAK,IAAI4T,EAAgB,YAClCO,EAASnU,KAAK,IAAI4T,EAAgB,UAAW,MAAM,IACnDO,EAASnU,KAAK,IAAI4T,EAAgB,eAAgB,MAAM,IACxDO,EAASnU,KAAK,IAAI4T,EAAgB,qBAAsB,MAAM,IAC9DO,EAASnU,KAAK,IAAI4T,EAAgB,kBAAmB,MAAM,IAC3DO,EAASnU,KAAK,IAAI4T,EAAgB,kBAAmB,MAAM,IAC3DO,EAASnU,KAAK,IAAI4T,EAAgB,cAAe,MAAM,IACvDO,EAASnU,KAAK,IAAI4T,EAAgB,WAAY,kBAAkB,IAChEK,GAAYE,EACLF,GAGO,SAAAQ,GAAOd,EAAoBvR,GAOzCN,OAAO4S,eAAef,EAAU,MAAO,CAAEgB,IANzC,WACE,IAAM/O,EAAiB+N,EAAiB,OAClC9N,EAAe8N,EAAmB,SAClC1M,EAAM,IAAItB,EAASC,EAAQC,GACjC,OAAOzD,EAAQwS,sBAAsB3N,MAwBzB,SAAA4N,GACdzS,EACA0S,EACAX,GAEA,IAAMd,EAAMD,EAAiB0B,GAC7B,OAAY,OAARzB,EACK,KA1BK,SACdjR,EACA2S,EACAZ,GAEA,MAAMR,EAAqB,CAC3BxQ,KAAmB,QACnB,IAAM6R,EAAMb,EAASjW,OACrB,IAAK,IAAID,EAAI,EAAGA,EAAI+W,EAAK/W,IAAK,CAC5B,MAAMgX,EAAUd,EAASlW,GACzB0V,EAASsB,EAAQnB,OAAUmB,EAA6BjB,MACtDL,EACAoB,EAASE,EAAQpB,SAIrB,OADAY,GAAOd,EAAUvR,GACVuR,EAaAuB,CAAa9S,EADHiR,EACsBc,GAsCzB,SAAAgB,GACdxB,EACAQ,GAEA,MAAMY,EAEF,GACJ,IAAMC,EAAMb,EAASjW,OACrB,IAAK,IAAID,EAAI,EAAGA,EAAI+W,EAAK/W,IAAK,CAC5B,IAAMgX,EAAUd,EAASlW,GACrBgX,EAAQlB,WACVgB,EAASE,EAAQpB,QAAUF,EAASsB,EAAQnB,QAGhD,OAAOR,KAAK8B,UAAUL,GChKxB,MAAMM,GAAe,WAkCL,SAAAC,GACdlT,EACAwD,EACAkP,GAEA,IAAMzB,EAAMD,EAAiB0B,GAC7B,OAAY,OAARzB,EACK,KAtCX,SACEjR,EACAwD,EACAmP,GAEA,MAAMQ,EAAyB,CAC7BC,SAAU,GACVC,MAAO,GACPC,cAAeX,EAAwB,eAEzC,GAAIA,EAASM,IACX,IAAK,MAAMxP,KAAQkP,EAASM,IAAe,CACzC,IAAMM,EAA2B9P,EAAKtE,QAAQ,MAAO,IAC/CqU,EAAYxT,EAAQwS,sBACxB,IAAIjP,EAASC,EAAQ+P,IAEvBJ,EAAWC,SAASxV,KAAK4V,GAI7B,GAAIb,EAAkB,MACpB,IAAK,MAAMc,KAAQd,EAAkB,MAAG,CACtC,IAAMa,EAAYxT,EAAQwS,sBACxB,IAAIjP,EAASC,EAAQiQ,EAAW,OAElCN,EAAWE,MAAMzV,KAAK4V,GAG1B,OAAOL,EAaAO,CAAoB1T,EAASwD,EADnByN,SCrCN0C,GAcX5U,YACSwF,EACAqP,EAQAC,EACA/I,GAVAtO,KAAG+H,IAAHA,EACA/H,KAAMoX,OAANA,EAQApX,KAAOqX,QAAPA,EACArX,KAAOsO,QAAPA,EAxBTtO,KAASsX,UAAc,GACvBtX,KAAOuX,QAAY,GACnBvX,KAAIwX,KAAsC,KAC1CxX,KAAYyX,aAAwB,KAMpCzX,KAAgB0X,iBAA8C,KAC9D1X,KAAA2X,aAAyB,CAAC,KAC1B3X,KAAoB+K,qBAAa,ICV7B,SAAU6M,GAAaC,GAC3B,IAAKA,EACH,MAAMjS,IAIM,SAAAkS,GACdtU,EACA+R,GAOA,OALA,SAAiBwC,EAAyBC,GACxC,IAAMjD,EAAWkB,GAAmBzS,EAASwU,EAAMzC,GAEnD,OADAqC,GAA0B,OAAb7C,GACNA,GAKK,SAAAkD,GACdzU,EACAwD,GAOA,OALA,SAAiB+Q,EAAyBC,GACxC,IAAMrB,EAAaD,GAAmBlT,EAASwD,EAAQgR,GAEvD,OADAJ,GAA4B,OAAfjB,GACNA,GAKK,SAAAuB,GACd1U,EACA+R,GAYA,OAVA,SAAiBwC,EAAyBC,GACxC,IAAMjD,EAAWkB,GAAmBzS,EAASwU,EAAMzC,GAEnD,OADAqC,GAA0B,OAAb7C,GHmEX,SACJA,EACAmB,EACAxO,EACA6C,GAEA,IAAMkK,EAAMD,EAAiB0B,GAC7B,GAAY,OAARzB,EACF,OAAO,KAET,IAAK5K,EAAS4K,EAAoB,gBAGhC,OAAO,KAET,MAAM0D,EAAiB1D,EAAoB,eAC3C,GAAsB,IAAlB0D,EAAO7Y,OACT,OAAO,KAET,MAAM+H,EAASC,mBACT8Q,EAAaD,EAAOE,MAAM,KAYhC,OAXaD,EAAWpE,IAAI,IAC1B,IAAMhN,EAAiB+N,EAAiB,OAClC9N,EAAe8N,EAAmB,SAOxC,OALa1K,EADG,MAAQhD,EAAOL,GAAU,MAAQK,EAAOJ,GAC1BS,EAAM6C,GAChBE,EAAgB,CAClC6N,IAAK,QACLC,MAAAA,MAIQ,GGlGHC,CACLzD,EACAiD,EACAxU,EAAQkE,KACRlE,EAAQiV,YAMR,SAAUC,GACd1Q,GAgCA,OA9BA,SACE+P,EACA7J,GAEA,IAAIyK,ExBmEF,IAxBwB3R,EwBnB1B,OAjBI2R,EANoB,MAApBZ,EAAItK,YAIJsK,EAAI5J,eAAeiE,SAAS,uCxBwD3B,IAAI/M,EACTxG,EAAiB+Z,iBACjB,iFANK,IAAIvT,EAAaxG,EAAiBga,gBAFvC,+FwB3C0B,MAApBd,EAAItK,axB+BgBzG,EwB9BCgB,EAAShB,OxB+B/B,IAAI3B,EACTxG,EAAiBia,eACjB,qBACE9R,EACA,2EwBjC0B,MAApB+Q,EAAItK,axBoDaxG,EwBnDGe,EAASf,KxBoDhC,IAAI5B,EACTxG,EAAiBka,aACjB,4CAA8C9R,EAAO,OwBpDtCiH,EAIfyK,EAAOjT,OAASqS,EAAItK,YACpBkL,EAAOnT,eAAiB0I,EAAI1I,eACrBmT,GAKL,SAAUK,GACdhR,GAEA,MAAMiR,EAASP,GAAmB1Q,GAalC,OAXA,SACE+P,EACA7J,GAEA,IAAIyK,EAASM,EAAOlB,EAAK7J,GAKzB,OAJwB,MAApB6J,EAAItK,cACNkL,GxBlByB1R,EwBkBDe,EAASf,KxBjB9B,IAAI5B,EACTxG,EAAiBqa,iBACjB,WAAajS,EAAO,uBwBiBpB0R,EAAOnT,eAAiB0I,EAAI1I,eACrBmT,GAKKQ,SAAAA,GACd3V,EACAwE,EACAuN,GAEA,IACMxN,EAAMsC,EADIrC,EAASZ,gBACI5D,EAAQkE,KAAMlE,EAAQiV,WAE7CnK,EAAU9K,EAAQ4V,sBACxB,MAAMC,EAAc,IAAIlC,GACtBpP,EAHa,MAKb+P,GAAgBtU,EAAS+R,GACzBjH,GAGF,OADA+K,EAAY5B,aAAeuB,GAAmBhR,GACvCqR,EAqIO,SAAAC,GACdtR,EACA2L,EACAoB,GAEA,MAAMwE,EAAgBrW,OAAOsW,OAAO,GAAIzE,GAMxC,OALAwE,EAAwB,SAAIvR,EAASf,KACrCsS,EAAoB,KAAI5F,EAAKZ,OACxBwG,EAA2B,cAC9BA,EAA2B,aAlB7B5F,EAkB6DA,GAnB7DoB,EAmBuD,OAfxCA,EAAsB,aAClCpB,GAAQA,EAAKpP,QACd,6BAeKgV,EAMH,SAAUE,GACdjW,EACAwE,EACAuN,EACA5B,EACAoB,GAEA,IAAMzK,EAAUtC,EAAST,sBACzB,MAAMgQ,EAAsC,CAC1CmC,yBAA0B,aAU5B,IAAMC,EAPN,WACE,IAAIza,EAAM,GACV,IAAK,IAAIG,EAAI,EAAGA,EAAI,EAAGA,IACrBH,GAAYuQ,KAAKC,SAASkK,WAAW9Q,MAAM,GAE7C,OAAO5J,EAEQ2a,GACjBtC,EAAQ,gBAAkB,+BAAiCoC,EAC3D,IAAMG,EAAYR,GAAmBtR,EAAU2L,EAAMoB,GAE/CgF,EACJ,KACAJ,EACA,4DAJqBpD,GAAiBuD,EAAWvE,GAOjD,SACAoE,EACA,qBAEAG,EAAuB,YACvB,WACIE,EAAe,SAAWL,EAAW,KAC3C,MAAMnC,EAAO3E,EAAQ3C,QAAQ6J,EAAapG,EAAMqG,GAChD,GAAa,OAATxC,EACF,MAAMtR,IAEFoR,EAAuB,CAAE9U,KAAMsX,EAAoB,UACnD/R,EAAMsC,EAAQC,EAAS9G,EAAQkE,KAAMlE,EAAQiV,WAE7CnK,EAAU9K,EAAQyW,mBACxB,MAAMZ,EAAc,IAAIlC,GACtBpP,EAHa,OAKb+P,GAAgBtU,EAAS+R,GACzBjH,GAMF,OAJA+K,EAAY/B,UAAYA,EACxB+B,EAAY9B,QAAUA,EACtB8B,EAAY7B,KAAOA,EAAKjD,aACxB8E,EAAY5B,aAAeiB,GAAmB1Q,GACvCqR,QAUIa,GAIX3X,YACS4X,EACAnN,EACPoN,EACArF,GAHO/U,KAAOma,QAAPA,EACAna,KAAKgN,MAALA,EAIPhN,KAAKoa,YAAcA,EACnBpa,KAAK+U,SAAWA,GAAY,MAIhB,SAAAsF,GACdtC,EACAuC,GAEA,IAAI5U,EAAwB,KAC5B,IACEA,EAASqS,EAAIwC,kBAAkB,wBAC/B,MAAO1S,GACP+P,IAAa,GAEf,MAAM4C,EAAgBF,GAAW,CAAC,UAElC,OADA1C,KAAelS,IAA6C,IAAnC8U,EAActP,QAAQxF,IACxCA,EAGH,SAAU+U,GACdjX,EACAwE,EACAuN,EACA5B,EACAoB,GAEA,IAAMzK,EAAUtC,EAAST,sBACnBmT,EAAoBpB,GAAmBtR,EAAU2L,EAAMoB,GACvDuC,EAAuB,CAAE9U,KAAMkY,EAA4B,UAC3D3S,EAAMsC,EAAQC,EAAS9G,EAAQkE,KAAMlE,EAAQiV,WAE7ClB,EAAU,CACdmC,yBAA0B,YAC1BiB,wBAAyB,QACzBC,yCAA0CjH,EAAKZ,SAC/C8H,oCAAqCH,EAA+B,YACpEI,eAAgB,mCAEZtD,EAAOjB,GAAiBmE,EAAmBnF,GAC3CjH,EAAU9K,EAAQyW,mBAaxB,MAAMZ,EAAc,IAAIlC,GAAYpP,EAtBrB,OAWf,SAAiBgQ,GACfsC,GAAmBtC,GACnB,IAAIhQ,EACJ,IACEA,EAAMgQ,EAAIwC,kBAAkB,qBAC5B,MAAO1S,GACP+P,IAAa,GAGf,OADAA,GAAa/N,EAAS9B,IACfA,GAEiDuG,GAK1D,OAJA+K,EAAY/B,UAAYA,EACxB+B,EAAY9B,QAAUA,EACtB8B,EAAY7B,KAAOA,EACnB6B,EAAY5B,aAAeiB,GAAmB1Q,GACvCqR,EAMH,SAAU0B,GACdvX,EACAwE,EACAD,EACA4L,GAsBA,IACMrF,EAAU9K,EAAQyW,mBACxB,MAAMZ,EAAc,IAAIlC,GAAYpP,EAFrB,OAlBf,SAAiBgQ,GACf,IAAMrS,EAAS2U,GAAmBtC,EAAK,CAAC,SAAU,UAClD,IAAIiD,EAA4B,KAChC,IACEA,EAAajD,EAAIwC,kBAAkB,+BACnC,MAAO1S,GACP+P,IAAa,GAGVoD,GAEHpD,IAAa,GAGf,IAAM7E,EAAO6C,OAAOoF,GAEpB,OADApD,IAAcqD,MAAMlI,IACb,IAAImH,GAAsBnH,EAAMY,EAAKZ,OAAmB,UAAXrN,IAII4I,GAG1D,OAFA+K,EAAY9B,QAvBI,CAAEoD,wBAAyB,SAwB3CtB,EAAY5B,aAAeiB,GAAmB1Q,GACvCqR,EAkBO,SAAA6B,GACdlT,EACAxE,EACAuE,EACA4L,EACAwH,EACA5F,EACA7P,EACAgS,GAIA,MAAMpS,EAAU,IAAI4U,GAAsB,EAAG,GAQ7C,GAPIxU,GACFJ,EAAQ6U,QAAUzU,EAAOyU,QACzB7U,EAAQ0H,MAAQtH,EAAOsH,QAEvB1H,EAAQ6U,QAAU,EAClB7U,EAAQ0H,MAAQ2G,EAAKZ,QAEnBY,EAAKZ,SAAWzN,EAAQ0H,MAC1B,MxBtRK,IAAI3H,EACTxG,EAAiBuc,uBACjB,wEwBsRF,IAAMC,EAAY/V,EAAQ0H,MAAQ1H,EAAQ6U,QAC1C,IAAImB,EAAgBD,EACJ,EAAZF,IACFG,EAAgB7L,KAAK8L,IAAID,EAAeH,IAE1C,IAAM5H,EAAYjO,EAAQ6U,QACpB3G,EAAUD,EAAY+H,EAC5B,IAAIE,EAAgB,GAElBA,EADoB,IAAlBF,EACc,WACPD,IAAcC,EACP,mBAEA,SAEZ/D,EAAU,CACdoD,wBAAyBa,EACzBC,0BAA2BnW,EAAQ6U,WAErC,MAAM3C,EAAO7D,EAAK7K,MAAMyK,EAAWC,GACnC,GAAa,OAATgE,EACF,MAAMtR,IA4BFoI,EAAU9K,EAAQyW,mBACxB,MAAMZ,EAAc,IAAIlC,GAAYpP,EAFrB,OAxBf,SACEgQ,EACAC,GAMA,IAAM0D,EAAerB,GAAmBtC,EAAK,CAAC,SAAU,UAClD4D,EAAarW,EAAQ6U,QAAUmB,EAC/BvI,EAAOY,EAAKZ,OAClB,IAAIgC,EAMJ,OAJEA,EADmB,UAAjB2G,EACS5D,GAAgBtU,EAAS+R,EAAzBuC,CAAmCC,EAAKC,GAExC,KAEN,IAAIkC,GACTyB,EACA5I,EACiB,UAAjB2I,EACA3G,IAKsDzG,GAK1D,OAJA+K,EAAY9B,QAAUA,EACtB8B,EAAY7B,KAAOA,EAAKjD,aACxB8E,EAAY3B,iBAAmBA,GAAoB,KACnD2B,EAAY5B,aAAeiB,GAAmB1Q,GACvCqR,EC1iBI,MAAAuC,GAAY,CAavBC,cAAe,iBA2BJC,GAAY,CAEvBC,QAAS,UAGTC,OAAQ,SAGRC,QAAS,UAGThW,SAAU,WAGViW,MAAO,SAGH,SAAUC,GACdC,GAEA,OAAQA,GACN,IAA+B,UAC/B,IAA+B,UAC/B,IAAA,YACE,OAAON,GAAUC,QACnB,IAAA,SACE,OAAOD,GAAUE,OACnB,IAAA,UACE,OAAOF,GAAUG,QACnB,IAAA,WACE,OAAOH,GAAU7V,SAGnB,QAEE,OAAO6V,GAAUI,aCrCVG,GAKX9Z,YACE+Z,EACAhT,EACAiT,GAEA,IAOQC,EhB7DU,mBgBuDLF,GAA4B,MAAThT,GAA6B,MAAZiT,GAE/Cvc,KAAKyc,KAAOH,EACZtc,KAAKsJ,MAAQA,MAAAA,EAAAA,OAAS+G,EACtBrQ,KAAKuc,SAAWA,MAAAA,EAAAA,OAAYlM,IAO5BrQ,KAAKyc,MALCD,EAAWF,GAKIG,KACrBzc,KAAKsJ,MAAQkT,EAASlT,MACtBtJ,KAAKuc,SAAWC,EAASD,WCtEzB,SAAUG,GAAMC,GACpB,MAAO,IAAIC,KAETpT,QAAQ6C,UAAUe,KAAK,IAAMuP,KAAKC,WCmHzBC,iBArGXta,cAFUvC,KAAK8c,OAAY,EAGzB9c,KAAK+c,KAAO,IAAIC,eAChBhd,KAAKid,UACLjd,KAAKkd,WAAape,EAAU0O,SAC5BxN,KAAKmd,aAAe,IAAI3T,QAAQ6C,IAC9BrM,KAAK+c,KAAKK,iBAAiB,QAAS,KAClCpd,KAAKkd,WAAape,EAAU6O,MAC5BtB,MAEFrM,KAAK+c,KAAKK,iBAAiB,QAAS,KAClCpd,KAAKkd,WAAape,EAAUue,cAC5BhR,MAEFrM,KAAK+c,KAAKK,iBAAiB,OAAQ,KACjC/Q,QAONc,KACEpF,EACAqP,EACAI,EACAD,GAEA,GAAIvX,KAAK8c,MACP,MAAMjW,EAAc,iCAItB,GAFA7G,KAAK8c,OAAQ,EACb9c,KAAK+c,KAAKO,KAAKlG,EAAQrP,GAAK,QACZsI,IAAZkH,EACF,IAAK,MAAMvT,KAAOuT,EACZA,EAAQ1M,eAAe7G,IACzBhE,KAAK+c,KAAKQ,iBAAiBvZ,EAAKuT,EAAQvT,GAAK4V,YASnD,YALavJ,IAATmH,EACFxX,KAAK+c,KAAK5P,KAAKqK,GAEfxX,KAAK+c,KAAK5P,OAELnN,KAAKmd,aAGd5P,eACE,IAAKvN,KAAK8c,MACR,MAAMjW,EAAc,yCAEtB,OAAO7G,KAAKkd,WAGdzP,YACE,IAAKzN,KAAK8c,MACR,MAAMjW,EAAc,sCAEtB,IACE,OAAO7G,KAAK+c,KAAKrX,OACjB,MAAOmC,GACP,OAAQ,GAIZoG,cACE,IAAKjO,KAAK8c,MACR,MAAMjW,EAAc,wCAEtB,OAAO7G,KAAK+c,KAAKS,SAGnBrP,eACE,IAAKnO,KAAK8c,MACR,MAAMjW,EAAc,yCAEtB,OAAO7G,KAAK+c,KAAKU,WAInBxN,QACEjQ,KAAK+c,KAAK9M,QAGZsK,kBAAkBmD,GAChB,OAAO1d,KAAK+c,KAAKxC,kBAAkBmD,GAGrCxQ,0BAA0ByQ,GACA,MAApB3d,KAAK+c,KAAKa,QACZ5d,KAAK+c,KAAKa,OAAOR,iBAAiB,WAAYO,GAIlDtQ,6BAA6BsQ,GACH,MAApB3d,KAAK+c,KAAKa,QACZ5d,KAAK+c,KAAKa,OAAOC,oBAAoB,WAAYF,KAMrDV,UACEjd,KAAK+c,KAAKe,aAAe,QAIb,SAAAC,KACd,OAAqD,IAAIlB,SCnF9CmB,GA+CXzb,YAAY0b,EAAgBtK,EAAeoB,EAA4B,MAjCvE/U,KAAYke,aAAW,EACfle,KAAkBme,oBAAY,EAC9Bne,KAAoBoe,sBAAY,EAChCpe,KAAUqe,WAAuD,GAMjEre,KAAMse,YAAkBjO,EACxBrQ,KAAUue,gBAAYlO,EACtBrQ,KAAQwe,cAAsBnO,EAC9BrQ,KAAgBye,iBAAW,EAG3Bze,KAAQ0e,cAAsCrO,EAC9CrQ,KAAO2e,aAAgCtO,EAkB7CrQ,KAAK4e,KAAOX,EACZje,KAAK6e,MAAQlL,EACb3T,KAAKyV,UAAYV,EACjB/U,KAAK8e,UAAYxJ,KACjBtV,KAAK+e,WAAa/e,KAAKgf,mBAAmBhf,KAAK6e,OAC/C7e,KAAKif,OAAM,UACXjf,KAAKkf,cAAgB5V,IAGnB,GAFAtJ,KAAKwe,cAAWnO,EAChBrQ,KAAKye,iBAAmB,EACpBnV,EAAM3D,YAAY9G,EAAiBoH,UACrCjG,KAAKme,oBAAqB,EAC1Bne,KAAKmf,2BACA,CACL,IAAMC,EAAiBpf,KAAKqf,8BAC5B,GAAIvU,EAAkBxB,EAAM5D,OAAQ,IAAK,CACvC,IAAI0Z,EASF,OANApf,KAAKsf,UAAY7P,KAAK8P,IACH,EAAjBvf,KAAKsf,UtBrF0B,KsBwFjCtf,KAAKme,oBAAqB,OAC1Bne,KAAKmf,uBAPL7V,EAAQxD,IAWZ9F,KAAKse,OAAShV,EACdtJ,KAAKwf,YAAW,WAGpBxf,KAAKyf,sBAAwBnW,IAC3BtJ,KAAKwe,cAAWnO,EACZ/G,EAAM3D,YAAY9G,EAAiBoH,UACrCjG,KAAKmf,wBAELnf,KAAKse,OAAShV,EACdtJ,KAAKwf,YAAW,WAGpBxf,KAAKsf,UAAY,EACjBtf,KAAK0f,aAAe1f,KAAK4e,KAAKe,QAAQ1F,mBACtCja,KAAK4f,SAAW,IAAIpW,QAAQ,CAAC6C,EAAS5C,KACpCzJ,KAAK0e,SAAWrS,EAChBrM,KAAK2e,QAAUlV,EACfzJ,KAAK6f,WAKP7f,KAAK4f,SAASxS,KAAK,KAAM,QA5D3BiS,8BACE,OAAOrf,KAAKsf,UAAYtf,KAAK0f,aA8DvBI,wBACN,MAAMC,EAAa/f,KAAKke,aACxB,OAAOnR,GAAU/M,KAAKggB,gBAAgBD,EAAahT,GAG7CiS,mBAAmBrL,GACzB,OAAqB,OAAdA,EAAKZ,OAGN8M,SACS,YAAX7f,KAAKif,aAIa5O,IAAlBrQ,KAAKwe,WAGLxe,KAAK+e,gBACiB1O,IAApBrQ,KAAKue,WACPve,KAAKigB,mBAEDjgB,KAAKme,mBACPne,KAAKkgB,eAEDlgB,KAAKoe,qBAEPpe,KAAKmgB,iBAELngB,KAAKogB,eAAiBlR,WAAW,KAC/BlP,KAAKogB,oBAAiB/P,EACtBrQ,KAAKqgB,mBACJrgB,KAAKsf,WAKdtf,KAAKsgB,kBAIDC,cACNrb,GAGAsE,QAAQgX,IAAI,CACVxgB,KAAK4e,KAAKe,QAAQc,gBAClBzgB,KAAK4e,KAAKe,QAAQe,sBACjBtT,KAAK,CAAA,CAAEuT,EAAWC,MACnB,OAAQ5gB,KAAKif,QACX,IAAA,UACE/Z,EAASyb,EAAWC,GACpB,MACF,IAAA,YACE5gB,KAAKwf,YAAW,YAChB,MACF,IAAA,UACExf,KAAKwf,YAAW,aAShBS,mBACNjgB,KAAKugB,cAAc,CAACI,EAAWC,KAC7B,IAAMvH,EAAcoB,GAClBza,KAAK4e,KAAKe,QACV3f,KAAK4e,KAAKiC,UACV7gB,KAAK8e,UACL9e,KAAK6e,MACL7e,KAAKyV,WAEP,MAAMqL,EAAgB9gB,KAAK4e,KAAKe,QAAQoB,aACtC1H,EACA0E,GACA4C,EACAC,GAEF5gB,KAAKwe,SAAWsC,EAChBA,EAAcpX,aAAa0D,KAAK,IAC9BpN,KAAKwe,cAAWnO,EAChBrQ,KAAKue,WAAaxW,EAClB/H,KAAKme,oBAAqB,EAC1Bne,KAAKmf,wBACJnf,KAAKkf,iBAIJgB,eAEN,MAAMnY,EAAM/H,KAAKue,WACjBve,KAAKugB,cAAc,CAACI,EAAWC,KAC7B,IAAMvH,EAAc0B,GAClB/a,KAAK4e,KAAKe,QACV3f,KAAK4e,KAAKiC,UACV9Y,EACA/H,KAAK6e,OAEP,MAAMmC,EAAgBhhB,KAAK4e,KAAKe,QAAQoB,aACtC1H,EACA0E,GACA4C,EACAC,GAEF5gB,KAAKwe,SAAWwC,EAChBA,EAActX,aAAa0D,KAAK1H,IAE9B1F,KAAKwe,cAAWnO,EAChBrQ,KAAKggB,gBAAgBta,EAAOyU,SAC5Bna,KAAKme,oBAAqB,EACtBzY,EAAO0U,YACTpa,KAAKoe,sBAAuB,GAE9Bpe,KAAKmf,wBACJnf,KAAKkf,iBAIJmB,kBACN,MAAMlF,ELiNyC,OKjNCnb,KAAKye,iBAC/C/Y,EAAS,IAAIwU,GACjBla,KAAKke,aACLle,KAAK6e,MAAM9L,QAIPhL,EAAM/H,KAAKue,WACjBve,KAAKugB,cAAc,CAACI,EAAWC,KAC7B,IAAIvH,EACJ,IACEA,EAAc6B,GACZlb,KAAK4e,KAAKiC,UACV7gB,KAAK4e,KAAKe,QACV5X,EACA/H,KAAK6e,MACL1D,EACAnb,KAAK8e,UACLpZ,EACA1F,KAAK8f,yBAEP,MAAOjY,GAGP,OAFA7H,KAAKse,OAASzW,OACd7H,KAAKwf,YAAW,SAGlB,MAAMyB,EAAgBjhB,KAAK4e,KAAKe,QAAQoB,aACtC1H,EACA0E,GACA4C,EACAC,GACW,GAEb5gB,KAAKwe,SAAWyC,EAChBA,EAAcvX,aAAa0D,KAAK,IAC9BpN,KAAKkhB,sBACLlhB,KAAKwe,cAAWnO,EAChBrQ,KAAKggB,gBAAgBmB,EAAUhH,SAC3BgH,EAAU/G,WACZpa,KAAKyV,UAAY0L,EAAUpM,SAC3B/U,KAAKwf,YAAW,YAEhBxf,KAAKmf,wBAENnf,KAAKkf,iBAIJgC,sBAIY,GL6J6B,OKhKGlhB,KAAKye,kBAGjC,WACpBze,KAAKye,kBAAoB,GAIrB0B,iBACNngB,KAAKugB,cAAc,CAACI,EAAWC,KAC7B,IAAMvH,EAAcF,GAClBnZ,KAAK4e,KAAKe,QACV3f,KAAK4e,KAAKiC,UACV7gB,KAAK8e,WAEP,MAAMsC,EAAkBphB,KAAK4e,KAAKe,QAAQoB,aACxC1H,EACA0E,GACA4C,EACAC,GAEF5gB,KAAKwe,SAAW4C,EAChBA,EAAgB1X,aAAa0D,KAAK2H,IAChC/U,KAAKwe,cAAWnO,EAChBrQ,KAAKyV,UAAYV,EACjB/U,KAAKwf,YAAW,YACfxf,KAAKyf,yBAIJa,iBACNtgB,KAAKugB,cAAc,CAACI,EAAWC,KAC7B,IAAMvH,EAAcI,GAClBzZ,KAAK4e,KAAKe,QACV3f,KAAK4e,KAAKiC,UACV7gB,KAAK8e,UACL9e,KAAK6e,MACL7e,KAAKyV,WAEP,MAAM4L,EAAmBrhB,KAAK4e,KAAKe,QAAQoB,aACzC1H,EACA0E,GACA4C,EACAC,GAEF5gB,KAAKwe,SAAW6C,EAChBA,EAAiB3X,aAAa0D,KAAK2H,IACjC/U,KAAKwe,cAAWnO,EAChBrQ,KAAKyV,UAAYV,EACjB/U,KAAKggB,gBAAgBhgB,KAAK6e,MAAM9L,QAChC/S,KAAKwf,YAAW,YACfxf,KAAKkf,iBAIJc,gBAAgBsB,GACtB,IAAMC,EAAMvhB,KAAKke,aACjBle,KAAKke,aAAeoD,EAKhBthB,KAAKke,eAAiBqD,GACxBvhB,KAAKwhB,mBAIDhC,YAAYpD,GAClB,GAAIpc,KAAKif,SAAW7C,EAGpB,OAAQA,GACN,IAAiC,YACjC,IAAA,UAIEpc,KAAKif,OAAS7C,OACQ/L,IAAlBrQ,KAAKwe,SACPxe,KAAKwe,SAAS7U,SACL3J,KAAKogB,iBACd/Q,aAAarP,KAAKogB,gBAClBpgB,KAAKogB,oBAAiB/P,EACtBrQ,KAAKmf,wBAEP,MACF,IAAA,UAIE,IAAMsC,EAAqD,WAAzCzhB,KAAKif,OACvBjf,KAAKif,OAAS7C,EACVqF,IACFzhB,KAAKwhB,mBACLxhB,KAAK6f,UAEP,MACF,IAAA,SAGE7f,KAAKif,OAAS7C,EACdpc,KAAKwhB,mBACL,MACF,IAAA,WAIExhB,KAAKse,OAAStY,IACdhG,KAAKif,OAAS7C,EACdpc,KAAKwhB,mBACL,MACF,IAAA,QAQA,IAAA,UAKExhB,KAAKif,OAAS7C,EACdpc,KAAKwhB,oBAMHrC,uBACN,OAAQnf,KAAKif,QACX,IAAA,UACEjf,KAAKwf,YAAW,UAChB,MACF,IAAA,YACExf,KAAKwf,YAAW,YAChB,MACF,IAAA,UACExf,KAAK6f,UAWX6B,eACE,IAAMC,EAAgBxF,GAA+Bnc,KAAKif,QAC1D,MAAO,CACL2C,iBAAkB5hB,KAAKke,aACvB2D,WAAY7hB,KAAK6e,MAAM9L,OACvBqJ,MAAOuF,EACP5M,SAAU/U,KAAKyV,UACfqM,KAAM9hB,KACNie,IAAKje,KAAK4e,MAqBdmD,GACExd,EACA+X,EAIAhT,EACA0Y,GAGA,MAAMxF,EAAW,IAAIH,GAClBC,QAEkCjM,EACnC/G,QAAS+G,EACT2R,QAAa3R,GAGf,OADArQ,KAAKiiB,aAAazF,GACX,KACLxc,KAAKkiB,gBAAgB1F,IAUzBpP,KACE+U,EACAC,GAIA,OAAOpiB,KAAK4f,SAASxS,KACnB+U,EACAC,GAOJC,MAASD,GACP,OAAOpiB,KAAKoN,KAAK,KAAMgV,GAMjBH,aAAazF,GACnBxc,KAAKqe,WAAWjd,KAAKob,GACrBxc,KAAKsiB,gBAAgB9F,GAMf0F,gBAAgB1F,GACtB,IAAMnd,EAAIW,KAAKqe,WAAWnT,QAAQsR,IACvB,IAAPnd,GACFW,KAAKqe,WAAWkE,OAAOljB,EAAG,GAItBmiB,mBACNxhB,KAAKwiB,iBACL,MAAMC,EAAYziB,KAAKqe,WAAWvV,QAClC2Z,EAAUrO,QAAQoI,IAChBxc,KAAKsiB,gBAAgB9F,KAIjBgG,iBACN,QAAsBnS,IAAlBrQ,KAAK0e,SAAwB,CAC/B,IAAIgE,GAAY,EAChB,OAAQvG,GAA+Bnc,KAAKif,SAC1C,KAAKnD,GAAUG,QACb0G,GAAS3iB,KAAK0e,SAASkE,KAAK,KAAM5iB,KAAK0hB,UAAvCiB,GACA,MACF,KAAK7G,GAAU7V,SACf,KAAK6V,GAAUI,MACb,MAAM2G,EAAS7iB,KAAK2e,QACpBgE,GAASE,EAAOD,KAAK,KAAM5iB,KAAKse,QAAhCqE,GACA,MACF,QACED,GAAY,EAGZA,IACF1iB,KAAK0e,cAAWrO,EAChBrQ,KAAK2e,aAAUtO,IAKbiS,gBAAgB9F,GAEtB,OADsBL,GAA+Bnc,KAAKif,SAExD,KAAKnD,GAAUC,QACf,KAAKD,GAAUE,OACTQ,EAASC,MACXkG,GAASnG,EAASC,KAAKmG,KAAKpG,EAAUxc,KAAK0hB,UAA3CiB,GAEF,MACF,KAAK7G,GAAUG,QACTO,EAASD,UACXoG,GAASnG,EAASD,SAASqG,KAAKpG,GAAhCmG,GAEF,MACF,KAAK7G,GAAU7V,SACf,KAAK6V,GAAUI,MAOf,QAEMM,EAASlT,OACXqZ,GACEnG,EAASlT,MAAMsZ,KAAKpG,EAAUxc,KAAKse,QADrCqE,IAWRG,SACE,IAAMC,EACoC,WAAxC/iB,KAAKif,QACM,YAAXjf,KAAKif,OAIP,OAHI8D,GACF/iB,KAAKwf,YAAW,WAEXuD,EAOTC,QACE,IAAMD,EAAkD,YAA1C/iB,KAAKif,OAInB,OAHI8D,GACF/iB,KAAKwf,YAAW,WAEXuD,EAQTpZ,SACE,IAAMoZ,EACqC,YAAzC/iB,KAAKif,QACM,YAAXjf,KAAKif,OAIP,OAHI8D,GACF/iB,KAAKwf,YAAW,aAEXuD,SC7mBEE,GAGX1gB,YACU2gB,EACRlb,GADQhI,KAAQkjB,SAARA,EAGJlb,aAAoBjB,EACtB/G,KAAK6gB,UAAY7Y,EAEjBhI,KAAK6gB,UAAY9Z,EAASa,YAAYI,EAAUkb,EAASxb,MAS7DkS,WACE,MAAO,QAAU5Z,KAAK6gB,UAAU7Z,OAAS,IAAMhH,KAAK6gB,UAAU5Z,KAGtDkc,QACR3f,EACAwE,GAEA,OAAO,IAAIib,GAAUzf,EAASwE,GAMhCob,WACE,IAAMpb,EAAW,IAAIjB,EAAS/G,KAAK6gB,UAAU7Z,OAAQ,IACrD,OAAOhH,KAAKmjB,QAAQnjB,KAAKkjB,SAAUlb,GAMrChB,aACE,OAAOhH,KAAK6gB,UAAU7Z,OAMxB0O,eACE,OAAO1V,KAAK6gB,UAAU5Z,KAOxBzE,WACE,OAAOoS,EAAc5U,KAAK6gB,UAAU5Z,MAMtC0Y,cACE,OAAO3f,KAAKkjB,SAOdG,aACE,IAAMC,EV9GJ,SAAiBrc,GACrB,GAAoB,IAAhBA,EAAK3H,OACP,OAAO,KAET,IAAMgV,EAAQrN,EAAK4N,YAAY,KAC/B,OAAe,IAAXP,EACK,GAEOrN,EAAK6B,MAAM,EAAGwL,GUsGZ+O,CAAOrjB,KAAK6gB,UAAU5Z,MACtC,GAAgB,OAAZqc,EACF,OAAO,KAEHtb,EAAW,IAAIjB,EAAS/G,KAAK6gB,UAAU7Z,OAAQsc,GACrD,OAAO,IAAIL,GAAUjjB,KAAKkjB,SAAUlb,GAMtCub,aAAa/gB,GACX,GAA4B,KAAxBxC,KAAK6gB,UAAU5Z,KACjB,MAAMT,EAAqBhE,IA6L3B,SAAUghB,GAAQvF,GACtB,MAAMwF,EAA0B,CAC9B7M,SAAU,GACVC,MAAO,IAET,OASF6F,eAAegH,EACbzF,EACAwF,EACAE,GAEA,MAAMC,EAAmB,CAEvBD,UAAAA,GAEF,MAAME,QAAiBC,GAAK7F,EAAK2F,GACjCH,EAAY7M,SAASxV,QAAQyiB,EAASjN,UACtC6M,EAAY5M,MAAMzV,QAAQyiB,EAAShN,OACL,MAA1BgN,EAAS/M,qBACL4M,EAAczF,EAAKwF,EAAaI,EAAS/M,eAtB1C4M,CAAczF,EAAKwF,GAAarW,KAAK,IAAMqW,GAgDpC,SAAAK,GACd7F,EACA8F,GAEe,MAAXA,GACgC,iBAAvBA,EAAQC,YACjB/Z,EACE,qBACgB,EACA,IAChB8Z,EAAQC,YAId,IAAMC,EAAKF,GAAW,GAChB1K,ENvOF,SACJ7V,EACAwE,EACAkc,EACAP,EACAK,GAEA,MAAM1M,EAAuB,GACzBtP,EAASb,OACXmQ,EAAkB,OAAI,GAEtBA,EAAkB,OAAItP,EAASf,KAAO,IAEpCid,GAAgC,EAAnBA,EAAU5kB,SACzBgY,EAAqB,UAAI4M,GAEvBP,IACFrM,EAAqB,UAAIqM,GAEvBK,IACF1M,EAAsB,WAAI0M,GAE5B,IACMjc,EAAMsC,EADIrC,EAAST,sBACI/D,EAAQkE,KAAMlE,EAAQiV,WAE7CnK,EAAU9K,EAAQ4V,sBACxB,MAAMC,EAAc,IAAIlC,GACtBpP,EAHa,MAKbkQ,GAAYzU,EAASwE,EAAShB,QAC9BsH,GAIF,OAFA+K,EAAY/B,UAAYA,EACxB+B,EAAY5B,aAAeiB,GAAmB1Q,GACvCqR,EMqMa8K,CAClBlG,EAAI0B,QACJ1B,EAAI4C,UACY,IAChBoD,EAAGN,UACHM,EAAGD,YAEL,OAAO/F,EAAI0B,QAAQyE,sBAAsB/K,EAAa0E,IA+BxC,SAAAsG,GACdpG,EACAlJ,GAEAkJ,EAAIsF,aAAa,kBACjB,IAAMlK,ENnMF,SACJ7V,EACAwE,EACA+M,EACAQ,GAEA,IACMxN,EAAMsC,EADIrC,EAASZ,gBACI5D,EAAQkE,KAAMlE,EAAQiV,WAE7CjB,EAAOjB,GAAiBxB,EAAUQ,GAElCjH,EAAU9K,EAAQ4V,sBACxB,MAAMC,EAAc,IAAIlC,GACtBpP,EALa,QAOb+P,GAAgBtU,EAAS+R,GACzBjH,GAKF,OAHA+K,EAAY9B,QARI,CAAEuD,eAAgB,mCASlCzB,EAAY7B,KAAOA,EACnB6B,EAAY5B,aAAeuB,GAAmBhR,GACvCqR,EM8KaiL,CAClBrG,EAAI0B,QACJ1B,EAAI4C,UACJ9L,EACAO,MAEF,OAAO2I,EAAI0B,QAAQyE,sBAAsB/K,EAAa0E,IASlD,SAAUwG,GAAetG,GAC7BA,EAAIsF,aAAa,kBACjB,IAAMlK,ENvOQ,SACd7V,EACAwE,EACAuN,GAEA,IACMxN,EAAMsC,EADIrC,EAASZ,gBACI5D,EAAQkE,KAAMlE,EAAQiV,WAE7CnK,EAAU9K,EAAQ4V,sBACxB,MAAMC,EAAc,IAAIlC,GACtBpP,EAHa,MAKbmQ,GAAmB1U,EAAS+R,GAC5BjH,GAGF,OADA+K,EAAY5B,aAAeuB,GAAmBhR,GACvCqR,EMuNamL,CAClBvG,EAAI0B,QACJ1B,EAAI4C,UACJvL,MAEF,OAAO2I,EAAI0B,QACRyE,sBAAsB/K,EAAa0E,IACnC3Q,KAAKrF,IACJ,GAAY,OAARA,EACF,M9BxNC,IAAI1C,EACTxG,EAAiB4lB,gBACjB,mD8BwNE,OAAO1c,IAUP,SAAU2c,GAAazG,GAC3BA,EAAIsF,aAAa,gBACjB,IAAMlK,ENnNQ,SACd7V,EACAwE,GAEA,IACMD,EAAMsC,EADIrC,EAASZ,gBACI5D,EAAQkE,KAAMlE,EAAQiV,WAE7CnK,EAAU9K,EAAQ4V,sBAGxB,MAAMC,EAAc,IAAIlC,GAAYpP,EAJrB,SAGf,SAAiB4c,EAA0BC,KACetW,GAG1D,OAFA+K,EAAY1B,aAAe,CAAC,IAAK,KACjC0B,EAAY5B,aAAeuB,GAAmBhR,GACvCqR,EMsMawL,CAAqB5G,EAAI0B,QAAS1B,EAAI4C,WAC1D,OAAO5C,EAAI0B,QAAQyE,sBAAsB/K,EAAa0E,IAaxC,SAAA+G,GAAU7G,EAAgB8G,GACxC,IVldoB9d,EUkddqc,GVldcrc,EUkdEgX,EAAI4C,UAAU5Z,KVjd9B+d,EUidoCD,EVhdvC1M,MAAM,KACN4M,OAAOC,GAAgC,EAAnBA,EAAU5lB,QAC9B+B,KAAK,KACY,IAAhB4F,EAAK3H,OACA0lB,EAEA/d,EAAO,IAAM+d,GU2chBhd,EAAW,IAAIjB,EAASkX,EAAI4C,UAAU7Z,OAAQsc,GACpD,OAAO,IAAIL,GAAUhF,EAAI0B,QAAS3X,GC9bpC,SAASmd,GACPlH,EACAhX,GAEA,GAAIgX,aAAemH,GAAqB,CACtC,IAAM5hB,EAAUya,EAChB,GAAuB,MAAnBza,EAAQ6hB,QACV,M/BwKG,IAAIhgB,EACTxG,EAAiBymB,kBACjB,6CAEElgB,EACA,yC+B3KI4R,EAAY,IAAIiM,GAAUzf,EAASA,EAAQ6hB,SACjD,OAAY,MAARpe,EACKke,GAAYnO,EAAW/P,GAEvB+P,EAIT,YAAa3G,IAATpJ,EACK6d,GAAU7G,EAAKhX,GAEfgX,EAwBG,SAAAA,GACdsH,EACAC,GAEA,GAAIA,GA9DG,kBAAkBC,KA8DFD,GAAY,CACjC,GAAID,aAAwBH,GAC1B,OA1Dc5hB,EA0DI+hB,EA1D0Bxd,EA0DZyd,EAzD7B,IAAIvC,GAAUzf,EAASuE,GA2D1B,MAAM3B,EACJ,4EAIJ,OAAO+e,GAAYI,EAAcC,GAjErC,IAAoBhiB,EAA8BuE,EAqElD,SAAS2d,GACPhe,EACAie,GAEA,IAAMle,EAAeke,MAAAA,OAAM,EAANA,EAASvgB,GAC9B,OAAoB,MAAhBqC,EACK,KAEFV,EAASS,mBAAmBC,EAAcC,GAG7C,SAAUke,GACdjG,EACAjY,EACAme,EACA9B,EAEI,IAEJpE,EAAQjY,QAAUA,KAAQme,IAC1BlG,EAAQlH,UAAY,OACpB,IAAQqN,EAAkB/B,EAAlB+B,iBACJA,IACFnG,EAAQoG,mBACmB,iBAAlBD,EACHA,EClDM,SACdvN,EACAyN,GAEA,GAAIzN,EAAM0N,IACR,MAAM,IAAIxlB,MACR,gHAIJ,IAKMylB,EAAUF,GAAa,eACvBG,EAAM5N,EAAM4N,KAAO,EACnBC,EAAM7N,EAAM6N,KAAO7N,EAAM8N,QAC/B,IAAKD,EACH,MAAM,IAAI3lB,MAAM,wDAuBlB,OApBM6lB,EAAOpjB,OAAAsW,OAAA,CAEX+M,sCAAuCL,IACvCM,IAAKN,EACLC,IAAAA,EACAM,IAAKN,EAAM,KACXO,UAAWP,EACXC,IAAAA,EACAC,QAASD,EACTO,SAAU,CACRC,iBAAkB,SAClBC,WAAY,KAIXtO,GAKE,CACL9V,EAA8BiS,KAAK8B,UAjCtB,CACbsQ,IAAK,OACLviB,KAAM,SAgCN9B,EAA8BiS,KAAK8B,UAAU8P,IAH7B,IAKhBjlB,KAAK,KDKC0lB,CAAoBjB,EAAenG,EAAQqH,IAAIjD,QAAQiC,kBAUpDZ,GAgBX7iB,YAIWykB,EACAC,EAIAC,EAIAC,EACAC,GAVApnB,KAAGgnB,IAAHA,EACAhnB,KAAainB,cAAbA,EAIAjnB,KAAiBknB,kBAAjBA,EAIAlnB,KAAImnB,KAAJA,EACAnnB,KAAgBonB,iBAAhBA,EA7BXpnB,KAAOqlB,QAAoB,KAMnBrlB,KAAKqnB,MAAWliB,EACxBnF,KAASyY,UAAW,QACDzY,KAAMsnB,OAAkB,KAEnCtnB,KAAQunB,UAAY,EAqB1BvnB,KAAKwnB,uBxBxJuC,KwByJ5CxnB,KAAKynB,oBxBlJoC,IwBmJzCznB,KAAK0nB,UAAY,IAAIC,IAEnB3nB,KAAKqlB,QADK,MAAR8B,EACapgB,EAASS,mBAAmB2f,EAAMnnB,KAAKqnB,OAEvC3B,GAAc1lB,KAAKqnB,MAAOrnB,KAAKgnB,IAAIjD,SAQtDrc,WACE,OAAO1H,KAAKqnB,MAGd3f,SAASA,GACP1H,KAAKqnB,MAAQ3f,EACI,MAAb1H,KAAKmnB,KACPnnB,KAAKqlB,QAAUte,EAASS,mBAAmBxH,KAAKmnB,KAAMzf,GAEtD1H,KAAKqlB,QAAUK,GAAche,EAAM1H,KAAKgnB,IAAIjD,SAOhD9J,yBACE,OAAOja,KAAKynB,oBAGdxN,uBAAuB2N,GACrB3d,EACE,OACe,EACC2L,OAAOiS,kBACvBD,GAEF5nB,KAAKynB,oBAAsBG,EAO7BxO,4BACE,OAAOpZ,KAAKwnB,uBAGdpO,0BAA0BwO,GACxB3d,EACE,OACe,EACC2L,OAAOiS,kBACvBD,GAEF5nB,KAAKwnB,uBAAyBI,EAGhCnH,sBACE,GAAIzgB,KAAK+lB,mBACP,OAAO/lB,KAAK+lB,mBAEd,MAAM+B,EAAO9nB,KAAKinB,cAAcc,aAAa,CAAEC,UAAU,IACzD,GAAIF,EAAM,CACR,IAAMG,QAAkBH,EAAKI,WAC7B,GAAkB,OAAdD,EACF,OAAOA,EAAUE,YAGrB,OAAO,KAGTzH,0BACE,MAAM0H,EAAWpoB,KAAKknB,kBAAkBa,aAAa,CAAEC,UAAU,IACjE,OAAII,SACmBA,EAASF,YAKhB3P,MAET,KAMT8P,UAME,OALKroB,KAAKunB,WACRvnB,KAAKunB,UAAW,EAChBvnB,KAAK0nB,UAAUtT,QAAQkU,GAAWA,EAAQ3e,UAC1C3J,KAAK0nB,UAAUa,SAEV/e,QAAQ6C,UAOjB2J,sBAAsB3N,GACpB,OAAO,IAAI4a,GAAUjjB,KAAMqI,GAO7B0Y,aACE1H,EACAmP,EACA7H,EACAC,EACA5U,GAAQ,GAER,GAAKhM,KAAKunB,SAkBR,OAAO,IAAIle,EAAY/C,KAlBL,CAClB,MAAMgiB,GAAUG,ClBnDpBpP,EACAqP,EACA/H,EACAC,EACA4H,EACAG,EACA3c,GAAQ,GkB6CYyc,CACdpP,EACArZ,KAAKsnB,OACL3G,EACAC,EACA4H,EACAxoB,KAAKonB,iBACLpb,GlBlDArB,EAAYF,EAAgB4O,EAAY/B,WACxCvP,EAAMsR,EAAYtR,IAAM4C,EACxB4M,EAAUrU,OAAOsW,OAAO,GAAIH,EAAY9B,SA1BhBA,EA2BdA,GA3BgCmR,EA2BvBA,KAzBvBnR,EAAQ,oBAAsBmR,GAlBhCnR,EA4CeA,EAzCG,QAFlBoJ,EA2CwBA,IAzCqB,EAAnBA,EAAUrhB,SAClCiY,EAAuB,cAAI,YAAcoJ,GAM3CgI,EAmC2BA,EAATpR,EAjCV,8BACN,UAAYoR,MAAAA,EAAAA,EAAmB,cAUjCpR,EAuBmBA,EApBG,QAFtBqJ,EAsB4BA,KAnB1BrJ,EAAQ,uBAAyBqJ,GAoB5B,IAAIxV,EACTrD,EACAsR,EAAYjC,OACZG,EACA8B,EAAY7B,KACZ6B,EAAY1B,aACZ0B,EAAYtO,qBACZsO,EAAYhC,QACZgC,EAAY5B,aACZ4B,EAAY/K,QACZ+K,EAAY3B,iBACZ8Q,EACAxc,IkBuCE,OANAhM,KAAK0nB,UAAUkB,IAAIN,GAEnBA,EAAQ5e,aAAa0D,KACnB,IAAMpN,KAAK0nB,UAAUmB,OAAOP,GAC5B,IAAMtoB,KAAK0nB,UAAUmB,OAAOP,IAEvBA,ElBnEG,IACdjP,EACAqP,EACA/H,EAMMhW,EACA5C,EkB+DNqc,4BACE/K,EACAmP,GAEA,GAAM,CAAC7H,EAAWC,SAAuBpX,QAAQgX,IAAI,CACnDxgB,KAAKygB,gBACLzgB,KAAK0gB,sBAGP,OAAO1gB,KAAK+gB,aACV1H,EACAmP,EACA7H,EACAC,GACAlX,2CErLU,SAAAof,GACd7K,EACAta,EACAoR,GAGA,OADAkJ,EAAM9Z,EAAmB8Z,GHsHzBta,EGnHEA,EHoHFoR,EGnHEA,GHiHFkJ,EGnHEA,GHuHEsF,aAAa,wBACV,IAAIvF,GAAWC,EAAK,IAAIpL,EAAQlP,GAAOoR,GG3G1C,SAAUoE,GAAY8E,GAE1B,OH6OI,SAAsBA,GAC1BA,EAAIsF,aAAa,eACjB,IAAMlK,EAAc0P,GAClB9K,EAAI0B,QACJ1B,EAAI4C,UACJvL,MAEF,OAAO2I,EAAI0B,QAAQyE,sBAAsB/K,EAAa0E,IGpP/CiL,CADP/K,EAAM9Z,EAAmB8Z,IAwHX,SAAAA,GACdsH,EACAC,GAGA,OAAOyD,GADP1D,EAAephB,EAAmBohB,GAGhCC,GCzQJ,SAAS0D,GACPC,EACA,CAAEC,mBAAoBrhB,IAEtB,IAAMif,EAAMmC,EAAUE,YAAY,OAAOtB,eACnCuB,EAAeH,EAAUE,YAAY,iBACrCE,EAAmBJ,EAAUE,YAAY,sBAE/C,OAAO,IAAIjE,GACT4B,EACAsC,EACAC,EACAxhB,EACAyhB,GAAAA,aAKFC,sBACE,IAAIplB,EC5CoB,UD8CtB6kB,GAED,UAACpkB,sBAAqB,IAGzB4kB,GAAAA,gBAAgBlnB,YAAe,IAE/BknB,GAAAA,gBAAgBlnB,YAAe,iBElDpBmnB,GAGXpnB,YACW6B,EACA0d,EACA7D,GAFAje,KAASoE,UAATA,EACApE,KAAI8hB,KAAJA,EACA9hB,KAAGie,IAAHA,EAGX2D,uBACE,OAAO5hB,KAAKoE,UAAUwd,iBAExB7M,eACE,OAAO/U,KAAKoE,UAAU2Q,SAExBqH,YACE,OAAOpc,KAAKoE,UAAUgY,MAExByF,iBACE,OAAO7hB,KAAKoE,UAAUyd,kBCbb+H,GACXrnB,YACW6B,EACQwa,GADR5e,KAASoE,UAATA,EACQpE,KAAI4e,KAAJA,EAWnB5e,KAAA2J,OAAS3J,KAAKoE,UAAUuF,OAAOiZ,KAAK5iB,KAAKoE,WACzCpE,KAAAqiB,MAAQriB,KAAKoE,UAAUie,MAAMO,KAAK5iB,KAAKoE,WACvCpE,KAAAgjB,MAAQhjB,KAAKoE,UAAU4e,MAAMJ,KAAK5iB,KAAKoE,WACvCpE,KAAA8iB,OAAS9iB,KAAKoE,UAAU0e,OAAOF,KAAK5iB,KAAKoE,WAXzCsd,eACE,OAAO,IAAIiI,GACT3pB,KAAKoE,UAAUsd,SACf1hB,KACAA,KAAK4e,MASTxR,KACE+U,EACAC,GAEA,OAAOpiB,KAAKoE,UAAUgJ,KAAKsU,IACzB,GAAIS,EACF,OAAOA,EACL,IAAIwH,GAAyBjI,EAAU1hB,KAAMA,KAAK4e,QAGrDwD,GAGLL,GACExd,EACA+X,EAIAhT,EACA0Y,GAEA,IAAI6H,OAGuCxZ,EAoB3C,OAnBMiM,IAEFuN,EAD4B,mBAAnBvN,EACe,GACtBA,EACE,IAAIqN,GAAyBG,EAAc9pB,KAAMA,KAAK4e,OAGlC,CACtBnC,KAAQH,EAAeG,KACnB,GACEH,EAAeG,KACb,IAAIkN,GAAyBG,EAAc9pB,KAAMA,KAAK4e,YAE1DvO,EACJkM,SAAUD,EAAeC,eAAYlM,EACrC/G,MAAOgT,EAAehT,YAAS+G,IAI9BrQ,KAAKoE,UAAU2d,GACpBxd,EACAslB,EACAvgB,QAAS+G,EACT2R,QAAa3R,UC3EN0Z,GACXxnB,YACW6B,EACQ8e,GADRljB,KAASoE,UAATA,EACQpE,KAAQkjB,SAARA,EAGnBtM,eACE,OAAO5W,KAAKoE,UAAUwS,SAAS5C,IAC7BiK,GAAO,IAAI+L,GAAgB/L,EAAKje,KAAKkjB,WAGzCrM,YACE,OAAO7W,KAAKoE,UAAUyS,MAAM7C,IAC1BiK,GAAO,IAAI+L,GAAgB/L,EAAKje,KAAKkjB,WAGzCpM,oBACE,OAAO9W,KAAKoE,UAAU0S,eAAiB,YCO9BkT,GAGXznB,YACW6B,EACFub,GADE3f,KAASoE,UAATA,EACFpE,KAAO2f,QAAPA,EAGTnd,WACE,OAAOxC,KAAKoE,UAAU5B,KAGxBwE,aACE,OAAOhH,KAAKoE,UAAU4C,OAGxB0O,eACE,OAAO1V,KAAKoE,UAAUsR,SAGxBkE,WACE,OAAO5Z,KAAKoE,UAAUwV,WAQxBqQ,MAAMlF,GACJ,IAAM/N,ENkPDkT,GMlPuBlqB,KAAKoE,UAAW2gB,GAC5C,OAAO,IAAIiF,GAAgBhT,EAAWhX,KAAK2f,SAG7CyD,WACE,OAAO,IAAI4G,GAAgBhqB,KAAKoE,UAAUgf,KAAMpjB,KAAK2f,SAOvD0D,aACE,IAAMrM,EAAYhX,KAAKoE,UAAUif,OACjC,OAAiB,MAAbrM,EACK,KAEF,IAAIgT,GAAgBhT,EAAWhX,KAAK2f,SAS7CwK,IACExmB,EACAoR,GAGA,OADA/U,KAAKujB,aAAa,OACX,IAAIqG,GACTd,GAAqB9oB,KAAKoE,UAAWT,EAAMoR,GAC3C/U,MAWJoqB,UACEnmB,EACA0C,EAAuBgK,EAAaC,IACpCmE,GAEA/U,KAAKujB,aAAa,aAClB,IAAM5f,EAAO0mB,EAAgB1jB,EAAQ1C,GACrC,MAAMsV,EAAarW,OAAAsW,OAAA,GAAQzE,GAI3B,OAHoC,MAAhCwE,EAA2B,aAAiC,MAApB5V,EAAKsN,cAC/CsI,EAA2B,YAAI5V,EAAKsN,aAE/B,IAAI2Y,GACT,IAAIU,GACFtqB,KAAKoE,UACL,IAAImmB,EAAS5mB,EAAKA,MAAM,GACxB4V,GAEFvZ,MAqBJwjB,UACE,ONmGKgH,GADDrmB,EMlGWnE,KAAKoE,YAAWgJ,KAC7Bqd,GAAK,IAAIV,GAAiBU,EAAGzqB,KAAK2f,UAuBtCmE,KAAKC,GACH,ON8CF9F,EM9Ccje,KAAKoE,UN+CnB2f,EM/C8BA,QAAW1T,ENkDlCqa,GADPzM,EAAM9Z,EAAmB8Z,GACa8F,GMlDc3W,KAChDqd,GAAK,IAAIV,GAAiBU,EAAGzqB,KAAK2f,UN4CxB,IACd1B,EMpCA9E,cACE,OAAOA,GAAYnZ,KAAKoE,WAY1BigB,eACEtP,GAEA,ONTK4V,GADDxmB,EMWFnE,KAAKoE,WACL2Q,GAQJwP,iBACE,ONiDKqG,GADDzmB,EMhDkBnE,KAAKoE,YAO7BykB,SAEE,OADA7oB,KAAKujB,aAAa,UNoDbsH,GADD1mB,EMlDgBnE,KAAKoE,YAGnBmf,aAAa/gB,GACnB,GAAsD,KAAjDxC,KAAKoE,UAAyByc,UAAU5Z,KAC3C,MAAM6jB,EAAsBtoB,UCxMrBuoB,GAGXxoB,YAAmBykB,EAA2B5iB,GAA3BpE,KAAGgnB,IAAHA,EAA2BhnB,KAASoE,UAATA,EAE9CgV,4BACE,OAAOpZ,KAAKoE,UAAUgV,sBAGxBa,yBACE,OAAOja,KAAKoE,UAAU6V,mBAOxBgE,IAAIhX,GACF,GAAI+jB,GAAM/jB,GACR,MAAMgkB,EACJ,sEAGJ,OAAO,IAAIjB,GAAgB/L,GAAIje,KAAKoE,UAAW6C,GAAOjH,MAOxDkrB,WAAWnjB,GACT,IAAKijB,GAAMjjB,GACT,MAAMkjB,EACJ,6EAGJ,IACEE,EAAUvjB,YAAYG,EAAM/H,KAAKoE,UAAmCsD,MACpE,MAAOG,GACP,MAAMojB,EACJ,kEAGJ,OAAO,IAAIjB,GAAgB/L,GAAIje,KAAKoE,UAAW2D,GAAM/H,MAGvDorB,sBAAsBxD,GACpB5nB,KAAKoE,UAAU6V,mBAAqB2N,EAGtCyD,yBAAyBzD,GACvB5nB,KAAKoE,UAAUgV,sBAAwBwO,EAGzC0D,YACE5jB,EACAme,EACA9B,EAEI,IPqQF,IACJpE,EOpQEiG,CPoQFjG,EACAjY,EACAme,EACA9B,EAEI,IOzQF6B,CAAuB5lB,KAAKoE,UAAWsD,EAAMme,EAAM9B,GP2QrDwH,GAAwB5L,EAAgCjY,EAAMme,EAAM9B,IOvQtE,SAASiH,GAAM/jB,GACb,MAAO,kBAAkBwe,KAAKxe,GtC1DhC,SAASiiB,GACPC,EACA,CAAEC,mBAAoBrhB,IAGtB,IAAMif,EAAMmC,EAAUE,YAAY,cAActB,eAC1CyD,EAAarC,EAChBE,YAAY,WACZtB,aAAa,CAAE0D,WAAY1jB,IAM9B,OAJmD,IAAIgjB,GACrD/D,EACAwE,GAK4BzsB,EAkBhB4nB,UAjBR3nB,EAAmB,WAEvB8c,aACAF,GACAjL,aAAAA,EACA+a,QAASX,GACT9H,UAAW+G,IAEbjrB,EAAS4sB,SAASC,kBAChB,IAAIvnB,EA7Ba,iBA6BW6kB,GAA8B,UACvDnkB,gBAAgB/F,GAChB8F,sBAAqB,IAG1B/F,EAAS2qB"}