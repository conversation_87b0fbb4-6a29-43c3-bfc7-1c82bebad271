import{_ as oe,u as se,r as f,M as ne,E as O,o as F,c as U,a as i,m as n,h as w,b as t,f as m,i as P,v as C,H as ie,t as c,n as A,p as D,F as J,k as N,e as le,l as ae}from"./index-2cGdSAEs.js";import{u as re}from"./extensionsStore-DVoXHZtn.js";import{B as ue}from"./BlockRenderer-DTQXxKLs.js";const M={type:"object",properties:{name:{type:"string",minLength:3,maxLength:100},version:{type:"string",pattern:"^[0-9]+\\.[0-9]+\\.[0-9]+$"},description:{type:"string",maxLength:500},permissions:{type:"array",items:{type:"string",enum:["api_access","read_user_data"]},uniqueItems:!0,default:[]},tags:{type:"array",items:{type:"string",maxLength:20},uniqueItems:!0,default:[]},dev_metadata:{type:"object",properties:{author_id:{type:"string"},created_at:{type:"string",format:"date-time"},updated_at:{type:"string",format:"date-time"}},required:["author_id"],additionalProperties:!1},ui_blocks:{type:"array",items:{type:"object",properties:{id:{type:"string",description:"Unique identifier for this block"},type:{type:"string",enum:["label","button","input","image_display","container"]},content:{type:"string",description:"Text content for label, button, etc."},placeholder:{type:"string",description:"Placeholder for input fields"},src:{type:"string",format:"url",description:"URL for image_display"},altText:{type:"string",description:"Alt text for image_display"},children:{$ref:"#/properties/ui_blocks/items/properties/children"},styles:{type:"object",additionalProperties:{type:"string"},description:'CSS-like styles, e.g., {"color": "red", "fontSize": "12px"}'},events:{type:"object",properties:{onClick:{type:"string",description:"Name of the function to call on click"},onChange:{type:"string",description:"Name of the function to call on change (for inputs)"}},additionalProperties:!1}},required:["id","type"]},default:[]},logic:{type:"object",properties:{functions:{type:"object",additionalProperties:{type:"object",properties:{actions:{type:"array",items:{type:"object",properties:{actionType:{type:"string",enum:["api_call","update_state","log_message","navigate"]},target:{type:"string",description:"e.g., API endpoint, state variable name, or route path"},payload:{type:"object",additionalProperties:!0,description:"Data for the action"}},required:["actionType","target"]}}},required:["actions"]}},event_mappings:{type:"object",additionalProperties:{type:"string"}},initial_state:{type:"object",additionalProperties:!0,description:"Initial values for the extension state variables"}},default:{functions:{},event_mappings:{},initial_state:{}}}},required:["name","version","description","ui_blocks","logic"],additionalProperties:!1};M.properties.ui_blocks.items.properties.children={type:"array",items:{$ref:"#/properties/ui_blocks/items"},default:[]};const de=()=>{let p=null;return{compile:l=>(p=l,u=>{if(!p||typeof u!="object"||u===null)return!1;const g=[];for(const d of p.required||[])u[d]===void 0&&g.push({instancePath:`/${d}`,schemaPath:"#/required",keyword:"required",params:{missingProperty:d},message:`must have required property '${d}'`});return u.name&&typeof u.name!="string"&&g.push({message:"property 'name' must be a string"}),u.version&&typeof u.version!="string"&&g.push({message:"property 'version' must be a string"}),g.length>0?((void 0).errors=g,!1):((void 0).errors=null,!0)}),addFormat:(l,u)=>{console.warn(`AJV Mock: addFormat for '${l}' called, but not implemented in mock.`)}}};let L,T;try{if(typeof window<"u"&&window.Ajv)L=window.Ajv,T=window.addFormats;else throw new Error("AJV not found, using mock.")}catch{console.warn("AJV library not found. Using a basic mock for schema validation. Functionality will be limited. Please install 'ajv' and 'ajv-formats' for full schema validation.");const l=de();L=function(){return l},T=function(u){u.addFormat()}}const R=new L({allErrors:!0,useDefaults:!0});T(R);let S;try{S=R.compile(M)}catch(p){console.error("Error compiling extension schema with AJV:",p),S=l=>(console.warn("Schema compilation failed. Validation is not active."),S.errors=[{message:"Schema compilation failed."}],!1)}function V(p){if(!S)return V.errors=[{message:"Validator not initialized."}],!1;const l=S(p);return l?V.errors=null:V.errors=S.errors,l}function ce(p){let l,u=null;try{l=JSON.parse(p)}catch(d){return u=d.message,{data:null,isValid:!1,errors:null,parseError:u}}const g=V(l);return{data:l,isValid:g,errors:V.errors,parseError:null}}const pe={class:"extension-builder"},ve={key:0,class:"builder-auth-guard"},me={key:0,class:"login-error"},ge={key:1,class:"not-dev-msg"},ye={key:1},fe={class:"builder-toolbar"},be={key:0,class:"extension-id-display"},he={class:"toolbar-actions"},ke=["disabled"],_e={class:"builder-main-area"},xe={class:"builder-content"},Ee={key:0,class:"code-mode-ide"},we={key:0,class:"validation-errors"},Se={key:0},Je={key:1},Ne={key:1,class:"validation-success"},Ve={key:1,class:"nocode-mode-visual-generator"},Ie={class:"nocode-layout"},je={class:"nocode-canvas-area"},Be={class:"canvas-blocks"},Oe={class:"block-type"},Pe=["onClick"],Ce={key:0,class:"canvas-placeholder"},Ae={class:"nocode-tools-palette"},De=["onClick"],Le={class:"builder-sidebar"},Te={class:"sidebar-section preview-panel"},qe={key:0,class:"preview-content"},$e={key:1,class:"preview-placeholder"},Fe={class:"sidebar-section event-debugger-panel"},Ue={key:0,class:"event-log-list"},Me={key:0},Re={key:1,class:"debugger-placeholder"},Ge={__name:"ExtensionBuilderLayout",props:{initialJson:{type:String,default:""}},setup(p){const l=se(),u=f(""),g=f(""),d=f("");async function G(){d.value="";try{await l.loginWithGoogle()}catch(o){d.value=o.message||"Google login failed."}}async function W(){d.value="";try{await l.loginWithEmail(u.value,g.value)}catch(o){d.value=o.message||"Email login failed."}}const z=["button","input","label","image_display","container"],_=f([]);function H(o){let s={id:`block_${Date.now()}_${Math.floor(Math.random()*1e4)}`,type:o};o==="button"&&(s.content="Button"),o==="input"&&(s.placeholder="Input"),o==="label"&&(s.content="Label"),o==="image_display"&&(s.src="https://via.placeholder.com/150"),o==="container"&&(s.children=[]),_.value.push(s),I()}function Y(o){_.value.splice(o,1),I()}function I(){try{const o=JSON.parse(a.value||"{}");o.ui_blocks=_.value,a.value=JSON.stringify(o,null,2),h()}catch{}}function j(){try{const o=JSON.parse(a.value||"{}");_.value=Array.isArray(o.ui_blocks)?[...o.ui_blocks]:[]}catch{_.value=[]}}const x=p,K=ae(),Q=ne(),B=re(),y=f(Q.params.id||null),b=f("code"),a=f(""),r=f(null);O(b,(o,e)=>{o==="nocode"?j():e==="nocode"&&o==="code"&&I()}),O(a,(o,e)=>{b.value==="nocode"&&j()}),F(()=>{b.value==="nocode"&&j()});const E=f([]),v=f(null);F(async()=>{if(y.value)if(x.initialJson)a.value=x.initialJson,h();else{const o=B.getExtensionById(y.value);o?(a.value=JSON.stringify(o,null,2),h()):(console.error(`Extension with ID ${y.value} not found for editing.`),v.value={type:"error",message:`Error: Extension with ID ${y.value} not found.`},a.value="")}else if(x.initialJson)a.value=x.initialJson,h();else{const o={name:"My New Extension",version:"0.1.0",description:"A cool new extension.",ui_blocks:[],logic:{},permissions:[],tags:[],is_public:!1};a.value=JSON.stringify(o,null,2),h()}});const q=U(()=>{var o;return((o=r.value)==null?void 0:o.isValid)&&a.value.trim()!==""});async function X(){if(!q.value){v.value={type:"error",message:"JSON is invalid or empty. Cannot save."},setTimeout(()=>v.value=null,3e3);return}if(!l.isDev){v.value={type:"error",message:"Only developers can save extensions."},setTimeout(()=>v.value=null,3e3);return}try{const e={...JSON.parse(a.value)};y.value&&(e.id=y.value);const s=await B.saveExtension(e);s?(v.value={type:"success",message:"Extension saved successfully!"},!y.value&&s.id&&(y.value=s.id,K.replace({name:"ExtensionEditor",params:{id:s.id}})),a.value=JSON.stringify(s,null,2),h()):v.value={type:"error",message:B.error||"Failed to save extension."}}catch(o){console.error("Error saving extension:",o),v.value={type:"error",message:`Error saving: ${o.message}`}}setTimeout(()=>v.value=null,4e3)}function Z(o,e){let s;return(...k)=>{clearTimeout(s),s=setTimeout(()=>o.apply(this,k),e)}}const ee=Z(()=>{if(a.value.trim()===""){r.value=null,E.value=[];return}r.value=ce(a.value),r.value.isValid||(E.value=[])},500);function h(){ee()}const $=U(()=>r.value&&r.value.isValid&&r.value.data?r.value.data.ui_blocks||[]:[]);function te(o){console.log("Block event received in layout:",o),E.value.unshift(o),E.value.length>20&&E.value.pop()}return x.initialJson&&(a.value=x.initialJson,h()),O(()=>x.initialJson,o=>{o!==a.value&&(a.value=o,h())}),(o,e)=>(n(),i("div",pe,[!w(l).isLoggedIn||!w(l).isDev?(n(),i("div",ve,[e[8]||(e[8]=t("h2",null,"Developer Login Required",-1)),e[9]||(e[9]=t("p",null,"Only logged-in developers can create extensions. Please log in:",-1)),t("button",{onClick:G,class:"login-btn dev"},"Login with Google"),t("form",{onSubmit:ie(W,["prevent"]),class:"email-login-form"},[P(t("input",{"onUpdate:modelValue":e[0]||(e[0]=s=>u.value=s),type:"email",placeholder:"Email",required:""},null,512),[[C,u.value]]),P(t("input",{"onUpdate:modelValue":e[1]||(e[1]=s=>g.value=s),type:"password",placeholder:"Password",required:""},null,512),[[C,g.value]]),e[6]||(e[6]=t("button",{type:"submit",class:"login-btn user"},"Login with Email",-1))],32),e[10]||(e[10]=t("p",{class:"login-note"},"Use Google or email/password to log in. Only developers can create extensions.",-1)),d.value?(n(),i("div",me,c(d.value),1)):m("",!0),w(l).isLoggedIn&&!w(l).isDev?(n(),i("div",ge,[e[7]||(e[7]=t("p",null,"You are logged in as a non-developer. Only developers can create extensions.",-1)),t("button",{onClick:e[2]||(e[2]=(...s)=>w(l).logout&&w(l).logout(...s)),class:"logout-btn"},"Logout")])):m("",!0)])):(n(),i("div",ye,[t("div",fe,[t("h2",null,[e[11]||(e[11]=A("Extension Builder ")),y.value?(n(),i("span",be,"(Editing ID: "+c(y.value)+")",1)):m("",!0)]),t("div",he,[t("button",{onClick:X,disabled:!q.value,class:"action-button save-button",title:"Save changes to this extension"},e[12]||(e[12]=[t("i",{class:"fas fa-save"},null,-1),A(" Save Extension ")]),8,ke),t("button",{onClick:e[3]||(e[3]=s=>b.value="code"),class:D([{active:b.value==="code"},"mode-toggle"])},"Code Mode",2),t("button",{onClick:e[4]||(e[4]=s=>b.value="nocode"),class:D([{active:b.value==="nocode"},"mode-toggle"])},"No-Code Mode",2)])]),v.value?(n(),i("div",{key:0,class:D(["save-status-toast",v.value.type])},c(v.value.message),3)):m("",!0),t("div",_e,[t("div",xe,[b.value==="code"?(n(),i("div",Ee,[e[15]||(e[15]=t("h3",null,"JSON IDE",-1)),e[16]||(e[16]=t("p",null,"Edit the extension JSON directly. Validation errors will appear below.",-1)),P(t("textarea",{"onUpdate:modelValue":e[5]||(e[5]=s=>a.value=s),onInput:h,placeholder:"Enter your extension JSON here...",rows:"20"},null,544),[[C,a.value]]),r.value&&!r.value.isValid?(n(),i("div",we,[e[13]||(e[13]=t("h4",null,"Validation Errors:",-1)),r.value.parseError?(n(),i("ul",Se,[t("li",null,"Parse Error: "+c(r.value.parseError),1)])):m("",!0),r.value.errors?(n(),i("ul",Je,[(n(!0),i(J,null,N(r.value.errors,(s,k)=>(n(),i("li",{key:k},c(s.instancePath||"JSON root")+": "+c(s.message),1))),128))])):m("",!0)])):m("",!0),r.value&&r.value.isValid&&a.value.trim()!==""?(n(),i("div",Ne,e[14]||(e[14]=[t("p",null,"JSON is valid according to the schema!",-1)]))):m("",!0)])):m("",!0),b.value==="nocode"?(n(),i("div",Ve,[e[19]||(e[19]=t("h3",null,"Visual JSON Generator (No-Code)",-1)),t("div",Ie,[t("div",je,[e[17]||(e[17]=t("p",null,"Add UI blocks to your extension:",-1)),t("div",Be,[(n(!0),i(J,null,N(_.value,(s,k)=>(n(),i("div",{key:s.id,class:"canvas-block"},[t("span",Oe,c(s.type),1),t("button",{class:"remove-block",onClick:We=>Y(k)},"Remove",8,Pe)]))),128)),_.value.length===0?(n(),i("div",Ce,"No blocks yet. Add from palette.")):m("",!0)])]),t("div",Ae,[e[18]||(e[18]=t("p",null,"UI Blocks Palette",-1)),t("ul",null,[(n(),i(J,null,N(z,s=>t("li",{key:s},[t("button",{onClick:k=>H(s)},c(s),9,De)])),64))])])])])):m("",!0)]),t("div",Le,[e[22]||(e[22]=t("h3",null,"Live Preview & Debug",-1)),t("div",Te,[e[20]||(e[20]=t("h4",null,"Extension Preview",-1)),$.value.length>0?(n(),i("div",qe,[(n(!0),i(J,null,N($.value,s=>(n(),le(ue,{key:s.id,block:s,onBlockEvent:te},null,8,["block"]))),128))])):(n(),i("div",$e,c(r.value&&r.value.isValid&&a.value.trim()!==""?"No UI blocks defined or previewable.":"Enter valid JSON with UI blocks to see a preview."),1))]),t("div",Fe,[e[21]||(e[21]=t("h4",null,"Event Debugger",-1)),E.value.length>0?(n(),i("ul",Ue,[(n(!0),i(J,null,N(E.value,(s,k)=>(n(),i("li",{key:k},[t("strong",null,c(s.eventName),1),A(" on '"+c(s.blockId)+"' ("+c(s.blockType)+") ",1),s.handlerFunction?(n(),i("span",Me," -> calls '"+c(s.handlerFunction)+"'",1)):m("",!0)]))),128))])):(n(),i("div",Re,"Event logs will appear here."))]),e[23]||(e[23]=t("div",{class:"sidebar-section state-inspector-panel"},[t("h4",null,"State Inspector"),t("div",{class:"inspector-placeholder"},"Current state values will appear here.")],-1))])])]))]))}},Ke=oe(Ge,[["__scopeId","data-v-2032b30b"]]);export{Ke as E};
