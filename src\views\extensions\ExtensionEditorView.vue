<template>
  <div class="min-h-[60vh] flex flex-col items-center justify-center px-2 sm:px-6 py-8 w-full max-w-4xl mx-auto">
    <div v-if="isLoading" class="w-full flex flex-col items-center justify-center text-gray-500 text-lg py-12">
      Loading extension editor...
    </div>
    <div v-else-if="!currentExtension && !isLoading" class="w-full flex flex-col items-center justify-center text-red-500 text-lg py-12">
      Extension not found or you do not have permission to edit it.
    </div>
    <div v-else class="w-full">
      <ExtensionBuilderLayout :initial-json="initialJsonContent" />
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue';
import { useRoute } from 'vue-router';
import { useExtensionsStore } from '../../store/extensionsStore';
import { useAuthStore } from '../../store/authStore';
import ExtensionBuilderLayout from '../../components/extensions/ExtensionBuilderLayout.vue';

const route = useRoute();
const extensionsStore = useExtensionsStore();
const authStore = useAuthStore();

const isLoading = ref(true);
const currentExtension = ref(null);
const initialJsonContent = ref('');

onMounted(async () => {
  isLoading.value = true;
  const extensionId = route.params.id;

  if (extensionId) {
    // Ensure extensions are loaded in the store
    if (extensionsStore.extensions.length === 0) {
      await extensionsStore.fetchExtensions();
    }
    const fetchedExtension = extensionsStore.getExtensionById(extensionId);

    // Security check: Ensure the logged-in user is the author
    if (fetchedExtension && authStore.user && fetchedExtension.dev_metadata.author_id === authStore.user.id) {
      currentExtension.value = fetchedExtension;
      initialJsonContent.value = JSON.stringify(fetchedExtension, null, 2);
    } else {
      console.error(`Cannot edit extension ${extensionId}: Not found or not authorized.`);
      // Error state will be shown based on currentExtension being null
    }
  } else {
    // This case should ideally not happen if routing is correct (editor implies ID)
    // but ExtensionBuilderLayout handles creating a new template if no ID/initialJson.
    console.warn("ExtensionEditorView opened without an ID.");
  }
  isLoading.value = false;
});

</script>

