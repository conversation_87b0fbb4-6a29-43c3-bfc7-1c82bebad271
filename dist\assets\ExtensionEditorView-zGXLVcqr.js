import{c as d,r as a,f as x,I as f,g as n,x as m,m as s}from"./index-DX9maXWO.js";import{u as _}from"./extensionsStore-PEOhYxsJ.js";import{E as p}from"./ExtensionBuilderLayout-k43vcjBf.js";import"./BlockRenderer-DmC888_9.js";const h={class:"min-h-[60vh] flex flex-col items-center justify-center px-2 sm:px-6 py-8 w-full max-w-4xl mx-auto"},v={key:0,class:"w-full flex flex-col items-center justify-center text-gray-500 text-lg py-12"},y={key:1,class:"w-full flex flex-col items-center justify-center text-red-500 text-lg py-12"},E={key:2,class:"w-full"},B={__name:"ExtensionEditorView",setup(w){const c=f(),i=_(),l=d(),t=a(!0),r=a(null),u=a("");return x(async()=>{t.value=!0;const o=c.params.id;if(o){i.extensions.length===0&&await i.fetchExtensions();const e=i.getExtensionById(o);e&&l.user&&e.dev_metadata.author_id===l.user.id?(r.value=e,u.value=JSON.stringify(e,null,2)):console.error(`Cannot edit extension ${o}: Not found or not authorized.`)}else console.warn("ExtensionEditorView opened without an ID.");t.value=!1}),(o,e)=>(s(),n("div",h,[t.value?(s(),n("div",v," Loading extension editor... ")):!r.value&&!t.value?(s(),n("div",y," Extension not found or you do not have permission to edit it. ")):(s(),n("div",E,[m(p,{"initial-json":u.value},null,8,["initial-json"])]))]))}};export{B as default};
