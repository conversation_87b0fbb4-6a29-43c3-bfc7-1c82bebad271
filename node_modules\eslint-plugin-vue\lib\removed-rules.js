'use strict'

/**
 * @typedef {object} RemovedRule
 * @property {string} ruleName
 * @property {string[]} replacedBy
 * @property {string} deprecatedInVersion
 * @property {string} removedInVersion
 */

/** @type {RemovedRule[]} */
module.exports = [
  {
    ruleName: 'experimental-script-setup-vars',
    replacedBy: [],
    deprecatedInVersion: 'v7.13.0',
    removedInVersion: 'v9.0.0'
  },
  {
    ruleName: 'name-property-casing',
    replacedBy: ['component-definition-name-casing'],
    deprecatedInVersion: 'v7.0.0',
    removedInVersion: 'v9.0.0'
  },
  {
    ruleName: 'no-confusing-v-for-v-if',
    replacedBy: ['no-use-v-if-with-v-for'],
    deprecatedInVersion: 'v5.0.0',
    removedInVersion: 'v9.0.0'
  },
  {
    ruleName: 'no-unregistered-components',
    replacedBy: ['no-undef-components'],
    deprecatedInVersion: 'v8.4.0',
    removedInVersion: 'v9.0.0'
  }
]
