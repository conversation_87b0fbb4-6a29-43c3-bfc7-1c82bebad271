import{r as n,a as m,b as t,i,v as b,j as c,l as x,m as g}from"./index-oz5Qducj.js";import{a as y,n as f}from"./meetings-Bk-V09ki.js";const k={class:"container mx-auto px-4 py-8"},E={class:"max-w-md mx-auto"},h={class:"bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6"},I={class:"mb-4"},_={class:"mb-4"},w={class:"mb-6"},M={class:"flex items-center"},N={class:"flex items-center mt-2"},J={class:"flex justify-between"},j=["disabled"],S={key:0},V={key:1},B={__name:"Join",setup(C){const v=x(),o=n(""),s=n(localStorage.getItem("userName")||""),d=n(!0),u=n(!0),l=n(!1),p=async()=>{if(!(!o.value.trim()||!s.value.trim()))try{if(l.value=!0,!await y(o.value)){alert("Meeting not found or has ended."),l.value=!1;return}const e=f();localStorage.setItem("userName",s.value),localStorage.setItem("userId",e),localStorage.setItem("audioEnabled",d.value),localStorage.setItem("videoEnabled",u.value),v.push(`/meeting/${o.value}`)}catch(r){console.error("Error joining meeting:",r),alert("Failed to join meeting. Please try again."),l.value=!1}};return(r,e)=>(g(),m("div",k,[t("div",E,[t("div",h,[e[9]||(e[9]=t("h1",{class:"text-2xl font-bold text-gray-900 dark:text-white mb-6"},"Join Meeting",-1)),t("div",I,[e[5]||(e[5]=t("label",{for:"meetingId",class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"}," Meeting ID ",-1)),i(t("input",{id:"meetingId","onUpdate:modelValue":e[0]||(e[0]=a=>o.value=a),type:"text",placeholder:"Enter meeting ID",class:"input",required:""},null,512),[[b,o.value]])]),t("div",_,[e[6]||(e[6]=t("label",{for:"userName",class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"}," Your Name ",-1)),i(t("input",{id:"userName","onUpdate:modelValue":e[1]||(e[1]=a=>s.value=a),type:"text",placeholder:"John Doe",class:"input",required:""},null,512),[[b,s.value]])]),t("div",w,[t("div",M,[i(t("input",{id:"audioEnabled","onUpdate:modelValue":e[2]||(e[2]=a=>d.value=a),type:"checkbox",class:"h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded"},null,512),[[c,d.value]]),e[7]||(e[7]=t("label",{for:"audioEnabled",class:"ml-2 block text-sm text-gray-700 dark:text-gray-300"}," Enable microphone ",-1))]),t("div",N,[i(t("input",{id:"videoEnabled","onUpdate:modelValue":e[3]||(e[3]=a=>u.value=a),type:"checkbox",class:"h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded"},null,512),[[c,u.value]]),e[8]||(e[8]=t("label",{for:"videoEnabled",class:"ml-2 block text-sm text-gray-700 dark:text-gray-300"}," Enable camera ",-1))])]),t("div",J,[t("button",{onClick:e[4]||(e[4]=a=>r.$router.push("/")),class:"btn btn-outline"}," Cancel "),t("button",{onClick:p,class:"btn btn-primary",disabled:!o.value.trim()||!s.value.trim()||l.value},[l.value?(g(),m("span",S,"Joining...")):(g(),m("span",V,"Join Meeting"))],8,j)])])])]))}};export{B as default};
