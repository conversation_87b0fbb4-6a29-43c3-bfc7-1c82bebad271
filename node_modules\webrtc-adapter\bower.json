{"name": "webrtc-adapter", "description": "A shim to insulate apps from WebRTC spec changes and browser prefix differences", "license": "BSD-3-<PERSON><PERSON>", "main": "./release/adapter.js", "repository": {"type": "git", "url": "https://github.com/webrtchacks/adapter.git"}, "authors": ["The WebRTC project authors (https://www.webrtc.org/)", "The adapter.js project authors (https://github.com/webrtchacks/adapter/)"], "moduleType": ["node"], "ignore": ["test/*"], "keywords": ["WebRTC", "RTCPeerConnection", "getUserMedia"]}