/*
 *  Copyright (c) 2016 The WebRTC project authors. All Rights Reserved.
 *
 *  Use of this source code is governed by a BSD-style license
 *  that can be found in the LICENSE file in the root of the source
 *  tree.
 */
/* eslint-env node */

'use strict';

import {adapterFactory} from './adapter_factory.js';

const adapter =
  adapterFactory({window: typeof window === 'undefined' ? undefined : window});
module.exports = adapter; // this is the difference from adapter_core.
