<template>
  <nav class="navbar" :class="{ mobile: isMobile }">
    <div class="navbar-left">
      <router-link to="/" class="app-logo">TheMeet</router-link>
    </div>

    <div class="navbar-center" v-if="!isMobile">
      <router-link to="/" class="nav-link">Home</router-link>
      <router-link to="/join" class="nav-link">Join Meeting</router-link>
      <router-link to="/marketplace" class="nav-link">Marketplace</router-link>
      <router-link to="/extensions" class="nav-link">Extensions</router-link>
      <router-link v-if="isDev" to="/dashboard" class="nav-link">Dashboard</router-link>
      <router-link v-if="isDev" to="/extensions/create" class="nav-link">Create Extension</router-link>
    </div>

    <div class="navbar-right" v-if="!isMobile">
      <div v-if="authStore.isLoggedIn" class="user-info">
        <img v-if="authStore.user?.photoURL" :src="authStore.user.photoURL" :alt="authStore.user.name" class="avatar" />
        <div v-else class="avatar-placeholder">
          <i class="fas fa-user"></i>
        </div>
        <span class="user-name">{{ authStore.user?.name || 'User' }}</span>
        <span v-if="isDev" class="dev-badge">DEV</span>
        <button @click="logout" class="logout-btn">Logout</button>
      </div>
      <div v-else class="auth-buttons">
        <router-link to="/auth/login" class="login-btn">Login</router-link>
      </div>
    </div>

    <div v-if="isMobile" class="mobile-menu">
      <button @click="showMenu = !showMenu" class="menu-toggle">
        <i class="fas fa-bars"></i>
      </button>
      <div v-if="showMenu" class="dropdown">
        <router-link to="/" class="dropdown-link" @click="showMenu = false">Home</router-link>
        <router-link to="/join" class="dropdown-link" @click="showMenu = false">Join Meeting</router-link>
        <router-link to="/marketplace" class="dropdown-link" @click="showMenu = false">Marketplace</router-link>
        <router-link to="/extensions" class="dropdown-link" @click="showMenu = false">Extensions</router-link>
        <router-link v-if="isDev" to="/dashboard" class="dropdown-link" @click="showMenu = false">Dashboard</router-link>
        <router-link v-if="isDev" to="/extensions/create" class="dropdown-link" @click="showMenu = false">Create Extension</router-link>
        <div class="dropdown-divider"></div>
        <div v-if="authStore.isLoggedIn" class="dropdown-user">
          <div class="user-info-mobile">
            <img v-if="authStore.user?.photoURL" :src="authStore.user.photoURL" :alt="authStore.user.name" class="avatar-small" />
            <div v-else class="avatar-placeholder-small">
              <i class="fas fa-user"></i>
            </div>
            <span class="user-name-mobile">{{ authStore.user?.name || 'User' }}</span>
            <span v-if="isDev" class="dev-badge-small">DEV</span>
          </div>
          <button @click="logout" class="logout-btn-mobile">Logout</button>
        </div>
        <router-link v-else to="/auth/login" class="dropdown-link" @click="showMenu = false">Login</router-link>
      </div>
    </div>
  </nav>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted } from 'vue';
import { useRouter } from 'vue-router';
import { useAuthStore } from '../store/authStore';

const router = useRouter();
const authStore = useAuthStore();

const isDev = computed(() => authStore.user?.role === 'dev');
const showMenu = ref(false);
const isMobile = ref(false);

function checkMobile() {
  isMobile.value = window.innerWidth <= 768;
}

const logout = async () => {
  try {
    await authStore.logout();
    showMenu.value = false;
    router.push('/');
  } catch (error) {
    console.error('Logout error:', error);
  }
};

onMounted(() => {
  checkMobile();
  window.addEventListener('resize', checkMobile);
});

onUnmounted(() => {
  window.removeEventListener('resize', checkMobile);
});
</script>

<style scoped>
.navbar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0.5rem 1rem;
  background: #fff;
  border-bottom: 1px solid #eee;
  min-height: 56px;
  position: sticky;
  top: 0;
  z-index: 50;
}

.dark .navbar {
  background: #1f2937;
  border-bottom-color: #374151;
}

.navbar-left {
  flex-shrink: 0;
}

.navbar-center {
  display: flex;
  align-items: center;
  justify-content: center;
  flex: 1;
}

.navbar-right {
  flex-shrink: 0;
}

.app-logo {
  font-weight: bold;
  font-size: 1.3em;
  color: #007bff;
  text-decoration: none;
  transition: color 0.2s;
}

.app-logo:hover {
  color: #0056b3;
}

.nav-link {
  margin: 0 0.7em;
  color: #333;
  text-decoration: none;
  font-weight: 500;
  padding: 0.5rem 0.75rem;
  border-radius: 0.375rem;
  transition: all 0.2s;
}

.dark .nav-link {
  color: #d1d5db;
}

.nav-link:hover {
  background-color: #f3f4f6;
  color: #007bff;
}

.dark .nav-link:hover {
  background-color: #374151;
  color: #60a5fa;
}

.nav-link.router-link-exact-active {
  color: #007bff;
  background-color: #eff6ff;
}

.dark .nav-link.router-link-exact-active {
  color: #60a5fa;
  background-color: #1e3a8a;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 0.5em;
}

.avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  object-fit: cover;
  border: 2px solid #e5e7eb;
}

.dark .avatar {
  border-color: #4b5563;
}

.avatar-placeholder {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background-color: #e5e7eb;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #6b7280;
}

.dark .avatar-placeholder {
  background-color: #4b5563;
  color: #9ca3af;
}

.user-name {
  font-size: 1em;
  color: #222;
  font-weight: 500;
}

.dark .user-name {
  color: #f9fafb;
}

.dev-badge {
  background: #007bff;
  color: #fff;
  border-radius: 4px;
  font-size: 0.75em;
  padding: 2px 6px;
  margin-left: 4px;
  font-weight: 600;
}

.logout-btn {
  background: none;
  border: none;
  color: #dc3545;
  font-weight: 500;
  cursor: pointer;
  margin-left: 8px;
  padding: 0.25rem 0.5rem;
  border-radius: 0.25rem;
  transition: background-color 0.2s;
}

.logout-btn:hover {
  background-color: #fee2e2;
}

.dark .logout-btn:hover {
  background-color: #7f1d1d;
}

.login-btn {
  background: #007bff;
  color: #fff;
  border: none;
  border-radius: 4px;
  padding: 7px 14px;
  font-size: 1em;
  cursor: pointer;
  text-decoration: none;
  font-weight: 500;
  transition: background-color 0.2s;
}

.login-btn:hover {
  background-color: #0056b3;
}

.mobile-menu {
  display: none;
  position: relative;
}

.menu-toggle {
  background: none;
  border: none;
  font-size: 1.2em;
  color: #333;
  cursor: pointer;
  padding: 0.5rem;
}

.dark .menu-toggle {
  color: #d1d5db;
}
@media (max-width: 768px) {
  .navbar-center,
  .navbar-right {
    display: none;
  }

  .mobile-menu {
    display: block;
    margin-left: 1em;
  }

  .dropdown {
    position: absolute;
    top: 56px;
    right: 10px;
    background: #fff;
    border: 1px solid #eee;
    border-radius: 6px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.08);
    z-index: 100;
    padding: 0.7em 1.2em;
    min-width: 200px;
  }

  .dark .dropdown {
    background: #1f2937;
    border-color: #374151;
  }

  .dropdown-link {
    display: block;
    color: #333;
    text-decoration: none;
    margin-bottom: 0.7em;
    font-weight: 500;
    padding: 0.5rem;
    border-radius: 0.25rem;
    transition: background-color 0.2s;
  }

  .dark .dropdown-link {
    color: #d1d5db;
  }

  .dropdown-link:hover {
    background-color: #f3f4f6;
  }

  .dark .dropdown-link:hover {
    background-color: #374151;
  }

  .dropdown-link:last-child {
    margin-bottom: 0;
  }

  .dropdown-divider {
    height: 1px;
    background-color: #e5e7eb;
    margin: 0.5rem 0;
  }

  .dark .dropdown-divider {
    background-color: #4b5563;
  }

  .dropdown-user {
    padding-top: 0.5rem;
  }

  .user-info-mobile {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 0.5rem;
  }

  .avatar-small {
    width: 24px;
    height: 24px;
    border-radius: 50%;
    object-fit: cover;
  }

  .avatar-placeholder-small {
    width: 24px;
    height: 24px;
    border-radius: 50%;
    background-color: #e5e7eb;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #6b7280;
    font-size: 0.75rem;
  }

  .dark .avatar-placeholder-small {
    background-color: #4b5563;
    color: #9ca3af;
  }

  .user-name-mobile {
    font-size: 0.875rem;
    color: #333;
    font-weight: 500;
  }

  .dark .user-name-mobile {
    color: #d1d5db;
  }

  .dev-badge-small {
    background: #007bff;
    color: #fff;
    border-radius: 3px;
    font-size: 0.625rem;
    padding: 1px 4px;
    font-weight: 600;
  }

  .logout-btn-mobile {
    background: none;
    border: none;
    color: #dc3545;
    font-weight: 500;
    cursor: pointer;
    padding: 0.25rem 0.5rem;
    border-radius: 0.25rem;
    font-size: 0.875rem;
    width: 100%;
    text-align: left;
  }

  .logout-btn-mobile:hover {
    background-color: #fee2e2;
  }

  .dark .logout-btn-mobile:hover {
    background-color: #7f1d1d;
  }
}
</style>
