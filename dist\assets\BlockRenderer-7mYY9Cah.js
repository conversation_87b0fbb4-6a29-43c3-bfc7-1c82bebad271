import{_ as m,r as f,g as h,a as o,f as g,m as t,t as k,H as b,F as B,k as C,e as w,J as I,p as x}from"./index-oz5Qducj.js";const E={key:0},R=["type","placeholder","value"],V=["src","alt"],F={key:4,class:"preview-container-children"},T={key:5},$={__name:"BlockRenderer",props:{block:{type:Object,required:!0}},emits:["block-event"],setup(e,{emit:u}){const n=e,i=u,s=f(n.block.type==="input"&&n.block.defaultValue||"");function d(l){n.block.type==="input"&&(s.value=l.target.value)}function y(){}function r(l){const c={blockId:n.block.id,blockType:n.block.type,eventName:l};n.block.events&&n.block.events[l]&&(c.handlerFunction=n.block.events[l]),i("block-event",c)}function v(l){i("block-event",l)}return(l,c)=>{const p=h("BlockRenderer",!0);return e.block?(t(),o("div",{key:0,class:x(["preview-block",`preview-block-${e.block.type}`]),style:I(e.block.styles),onClick:b(y,["stop"])},[e.block.type==="label"?(t(),o("span",E,k(e.block.content||"Label"),1)):e.block.type==="button"?(t(),o("button",{key:1,onClick:c[0]||(c[0]=b(a=>r("onClick"),["stop"]))},k(e.block.content||"Button"),1)):e.block.type==="input"?(t(),o("input",{key:2,type:e.block.inputType||"text",placeholder:e.block.placeholder||"Input",value:s.value,onInput:d,onChange:c[1]||(c[1]=a=>r("onChange"))},null,40,R)):e.block.type==="image_display"?(t(),o("img",{key:3,src:e.block.src||"https://via.placeholder.com/150",alt:e.block.altText||"Image preview",class:"preview-image"},null,8,V)):e.block.type==="container"?(t(),o("div",F,[(t(!0),o(B,null,C(e.block.children,a=>(t(),w(p,{key:a.id,block:a,onBlockEvent:v},null,8,["block"]))),128))])):(t(),o("span",T,"Unknown block type: "+k(e.block.type),1))],6)):g("",!0)}}},D=m($,[["__scopeId","data-v-72429dfc"]]);export{D as B};
