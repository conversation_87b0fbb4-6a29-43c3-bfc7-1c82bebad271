# TheMeet  
*A Modern Web App for Hosting Seamless Video Meetings*

---

## 🚀 Tech Stack

**Frontend:**  
- **Vue.js 3** with Composition API
- **Tailwind CSS** for styling
- **Font Awesome** for icons

**Backend:**  
- **Firebase** (Authentication, Firestore)

**Real-Time Communication:**  
- **WebRTC** for peer-to-peer video/audio
- **Firebase Firestore** for signaling and real-time updates

**Build Tools:**
- **Vite** for fast development and optimized builds

---

## ⚙️ Key Features

1. **Ultra-Fast Real-Time Video Meetings**  
   - Low-latency video/audio communication via WebRTC
   - Optimized peer connections with STUN/TURN servers

2. **Live Chat & Collaboration**  
   - Real-time chat with message history
   - File sharing capabilities

3. **Advanced Meeting Controls**  
   - Mute/unmute audio
   - Enable/disable video
   - Screen sharing
   - Virtual backgrounds
   - Interactive whiteboard
   - Meeting recording with auto-download
   - Breakout rooms for small group discussions

4. **Accessibility Features**
   - Audible Impaired System (AIS) with speech-to-text
   - Multi-language support with automatic translation
   - Dark/light mode toggle

5. **Security & Admin Controls**
   - Join request approval system
   - <PERSON>min can remove participants
   - Participant leave reason tracking
   - Secure Firebase authentication

6. **Interactive Elements**
   - Real-time reactions with animations
   - Polls and voting system
   - Hand raising feature

---

## 📁 Project Structure

```bash
TheMeet/
├── public/                       # Static assets
│   ├── css/                      # Tailwind CSS builds
│   └── assets/                   # Images, backgrounds, etc.
├── src/                          # Source code
│   ├── components/               # Vue components
│   │   ├── AudibleImpairedSystem.vue    # Speech-to-text system
│   │   ├── BreakoutRooms.vue            # Breakout rooms management
│   │   ├── MeetingControls.vue          # Main meeting control buttons
│   │   ├── ReactionDisplay.vue          # Shows reactions in meeting
│   │   ├── ReactionSelector.vue         # UI for selecting reactions
│   │   ├── RecordingControls.vue        # Recording functionality
│   │   ├── VirtualBackgroundSelector.vue # Background selection
│   │   └── Whiteboard.vue               # Interactive whiteboard
│   ├── views/                    # Page components
│   │   ├── Home.vue              # Landing page
│   │   ├── Join.vue              # Join meeting page
│   │   ├── Meeting.vue           # Main meeting interface
│   │   └── NotFound.vue          # 404 page
│   ├── router/                   # Vue Router setup
│   │   └── index.js              # Route definitions
│   ├── firebase/                 # Firebase configuration
│   │   ├── config.js             # Firebase initialization
│   │   └── meetings.js           # Meeting-related Firebase functions
│   ├── utils/                    # Utility functions
│   │   ├── meetingFeatures.js    # Meeting features implementation
│   │   ├── virtualBackground.js  # Virtual background processing
│   │   └── webrtc.js             # WebRTC connection handling
│   ├── App.vue                   # Root component
│   └── main.js                   # Application entry point
├── netlify.toml                  # Netlify deployment configuration
├── tailwind.config.js            # Tailwind CSS configuration
├── vite.config.js                # Vite build configuration
├── package.json                  # Dependencies and scripts
└── README.md                     # This file
```

## 🧩 Component Details

### Core Components

- **MeetingControls**: Central control panel with buttons for all meeting functions (mic, camera, screen sharing, chat, etc.)
- **AudibleImpairedSystem**: Converts speech to text with language detection and translation
- **BreakoutRooms**: Creates and manages separate rooms for small group discussions
- **Whiteboard**: Interactive drawing canvas with multiple tools (pen, shapes, text, etc.)
- **VirtualBackgroundSelector**: Allows users to choose or upload custom backgrounds
- **RecordingControls**: Manages meeting recording with auto-download of audio, video, and transcripts
- **ReactionSelector/Display**: Allows users to send and view animated reactions during meetings

### Views

- **Home**: Landing page with options to create or join meetings
- **Join**: Interface for entering meeting details and user information
- **Meeting**: Main meeting interface that integrates all components
- **NotFound**: 404 error page for invalid routes

### Utilities

- **webrtc.js**: Handles WebRTC peer connections, media streams, and signaling
- **meetingFeatures.js**: Implements core meeting features (chat, reactions, polls, etc.)
- **virtualBackground.js**: Processes video streams to apply virtual backgrounds

## 🔧 Firebase Integration

The application uses Firebase for:

1. **Authentication**: User sign-in and identity management
2. **Firestore Database**: 
   - Meeting metadata and configuration
   - Real-time participant tracking
   - Chat messages and reactions
   - Breakout room management
   - Join requests and approvals
   - Recording metadata
   - Transcripts from speech-to-text

## 🚀 Deployment

The application is deployed on Netlify with environment variables configured in the Netlify dashboard instead of using a local .env file, enhancing security by keeping sensitive information out of the codebase.
