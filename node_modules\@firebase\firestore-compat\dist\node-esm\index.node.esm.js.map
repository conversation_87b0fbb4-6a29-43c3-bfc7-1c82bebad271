{"version": 3, "file": "index.node.esm.js", "sources": ["../../src/util/input_validation.ts", "../../src/api/blob.ts", "../../src/api/observer.ts", "../../src/api/database.ts", "../../src/api/field_path.ts", "../../src/api/field_value.ts", "../../src/config.ts", "../../src/index.node.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2021 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { FirestoreError } from '@firebase/firestore';\nimport { SetOptions } from '@firebase/firestore-types';\n\nexport function validateSetOptions(\n  methodName: string,\n  options: SetOptions | undefined\n): SetOptions {\n  if (options === undefined) {\n    return {\n      merge: false\n    };\n  }\n\n  if (options.mergeFields !== undefined && options.merge !== undefined) {\n    throw new FirestoreError(\n      'invalid-argument',\n      `Invalid options passed to function ${methodName}(): You cannot ` +\n        'specify both \"merge\" and \"mergeFields\".'\n    );\n  }\n\n  return options;\n}\n", "/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { Bytes, FirestoreError, _isBase64Available } from '@firebase/firestore';\nimport { Compat } from '@firebase/util';\n\n/** Helper function to assert Uint8Array is available at runtime. */\nfunction assertUint8ArrayAvailable(): void {\n  if (typeof Uint8Array === 'undefined') {\n    throw new FirestoreError(\n      'unimplemented',\n      'Uint8Arrays are not available in this environment.'\n    );\n  }\n}\n\n/** Helper function to assert Base64 functions are available at runtime. */\nfunction assertBase64Available(): void {\n  if (!_isBase64Available()) {\n    throw new FirestoreError(\n      'unimplemented',\n      'Blobs are unavailable in Firestore in this environment.'\n    );\n  }\n}\n\n/** Immutable class holding a blob (binary data) */\nexport class Blob implements Compat<Bytes> {\n  constructor(readonly _delegate: Bytes) {}\n  static fromBase64String(base64: string): Blob {\n    assertBase64Available();\n    return new Blob(Bytes.fromBase64String(base64));\n  }\n\n  static fromUint8Array(array: Uint8Array): Blob {\n    assertUint8ArrayAvailable();\n    return new Blob(Bytes.fromUint8Array(array));\n  }\n\n  toBase64(): string {\n    assertBase64Available();\n    return this._delegate.toBase64();\n  }\n\n  toUint8Array(): Uint8Array {\n    assertUint8ArrayAvailable();\n    return this._delegate.toUint8Array();\n  }\n\n  isEqual(other: Blob): boolean {\n    return this._delegate.isEqual(other._delegate);\n  }\n\n  toString(): string {\n    return 'Blob(base64: ' + this.toBase64() + ')';\n  }\n}\n", "/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { FirestoreError } from '@firebase/firestore';\n\n/**\n * Observer/Subscribe interfaces.\n */\nexport type NextFn<T> = (value: T) => void;\nexport type ErrorFn = (error: FirestoreError) => void;\nexport type CompleteFn = () => void;\n\n// Allow for any of the Observer methods to be undefined.\nexport interface PartialObserver<T> {\n  next?: NextFn<T>;\n  error?: ErrorFn;\n  complete?: CompleteFn;\n}\n\nexport function isPartialObserver<T>(obj: unknown): obj is PartialObserver<T> {\n  return implementsAnyMethods(obj, ['next', 'error', 'complete']);\n}\n\n/**\n * Returns true if obj is an object and contains at least one of the specified\n * methods.\n */\nfunction implementsAnyMethods(obj: unknown, methods: string[]): boolean {\n  if (typeof obj !== 'object' || obj === null) {\n    return false;\n  }\n\n  const object = obj as Record<string, unknown>;\n  for (const method of methods) {\n    if (method in object && typeof object[method] === 'function') {\n      return true;\n    }\n  }\n  return false;\n}\n", "/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { FirebaseApp } from '@firebase/app-types';\nimport { _FirebaseApp, FirebaseService } from '@firebase/app-types/private';\nimport {\n  LoadBundleTask,\n  Bytes,\n  clearIndexedDbPersistence,\n  disableNetwork,\n  enableIndexedDbPersistence,\n  enableMultiTabIndexedDbPersistence,\n  enableNetwork,\n  ensureFirestoreConfigured,\n  Firestore as ExpFirestore,\n  connectFirestoreEmulator,\n  waitForPendingWrites,\n  FieldPath as ExpFieldPath,\n  limit,\n  limitToLast,\n  where,\n  orderBy,\n  startAfter,\n  startAt,\n  query,\n  endBefore,\n  endAt,\n  doc,\n  collection,\n  collectionGroup,\n  queryEqual,\n  Query as ExpQuery,\n  CollectionReference as ExpCollectionReference,\n  DocumentReference as ExpDocumentReference,\n  refEqual,\n  addDoc,\n  deleteDoc,\n  executeWrite,\n  getDoc,\n  getDocFromCache,\n  getDocFromServer,\n  getDocs,\n  getDocsFromCache,\n  getDocsFromServer,\n  onSnapshot,\n  onSnapshotsInSync,\n  setDoc,\n  updateDoc,\n  Unsubscribe,\n  DocumentChange as ExpDocumentChange,\n  DocumentSnapshot as ExpDocumentSnapshot,\n  QueryDocumentSnapshot as ExpQueryDocumentSnapshot,\n  QuerySnapshot as ExpQuerySnapshot,\n  snapshotEqual,\n  SnapshotMetadata,\n  runTransaction,\n  Transaction as ExpTransaction,\n  WriteBatch as ExpWriteBatch,\n  AbstractUserDataWriter,\n  FirestoreError,\n  FirestoreDataConverter as ModularFirestoreDataConverter,\n  setLogLevel as setClientLogLevel,\n  _DatabaseId,\n  _validateIsNotUsedTogether,\n  _cast,\n  _DocumentKey,\n  _debugAssert,\n  _FieldPath,\n  _ResourcePath,\n  _ByteString,\n  _logWarn,\n  namedQuery,\n  loadBundle,\n  PartialWithFieldValue,\n  WithFieldValue\n} from '@firebase/firestore';\nimport {\n  CollectionReference as PublicCollectionReference,\n  DocumentChange as PublicDocumentChange,\n  DocumentChangeType as PublicDocumentChangeType,\n  DocumentData,\n  DocumentData as PublicDocumentData,\n  DocumentReference as PublicDocumentReference,\n  DocumentSnapshot as PublicDocumentSnapshot,\n  FieldPath as PublicFieldPath,\n  FirebaseFirestore as PublicFirestore,\n  FirestoreDataConverter as PublicFirestoreDataConverter,\n  GetOptions as PublicGetOptions,\n  LogLevel as PublicLogLevel,\n  OrderByDirection as PublicOrderByDirection,\n  PersistenceSettings as PublicPersistenceSettings,\n  Query as PublicQuery,\n  QueryDocumentSnapshot as PublicQueryDocumentSnapshot,\n  QuerySnapshot as PublicQuerySnapshot,\n  SetOptions as PublicSetOptions,\n  Settings as PublicSettings,\n  SnapshotListenOptions as PublicSnapshotListenOptions,\n  SnapshotOptions as PublicSnapshotOptions,\n  Transaction as PublicTransaction,\n  UpdateData as PublicUpdateData,\n  WhereFilterOp as PublicWhereFilterOp,\n  WriteBatch as PublicWriteBatch\n} from '@firebase/firestore-types';\nimport {\n  Compat,\n  EmulatorMockTokenOptions,\n  getModularInstance\n} from '@firebase/util';\n\nimport { validateSetOptions } from '../util/input_validation';\n\nimport { Blob } from './blob';\nimport {\n  CompleteFn,\n  ErrorFn,\n  isPartialObserver,\n  NextFn,\n  PartialObserver\n} from './observer';\n\n/**\n * A persistence provider for either memory-only or IndexedDB persistence.\n * Mainly used to allow optional inclusion of IndexedDB code.\n */\nexport interface PersistenceProvider {\n  enableIndexedDbPersistence(\n    firestore: Firestore,\n    forceOwnership: boolean\n  ): Promise<void>;\n  enableMultiTabIndexedDbPersistence(firestore: Firestore): Promise<void>;\n  clearIndexedDbPersistence(firestore: Firestore): Promise<void>;\n}\n\n/**\n * The persistence provider included with the full Firestore SDK.\n */\nexport class IndexedDbPersistenceProvider implements PersistenceProvider {\n  enableIndexedDbPersistence(\n    firestore: Firestore,\n    forceOwnership: boolean\n  ): Promise<void> {\n    return enableIndexedDbPersistence(firestore._delegate, { forceOwnership });\n  }\n  enableMultiTabIndexedDbPersistence(firestore: Firestore): Promise<void> {\n    return enableMultiTabIndexedDbPersistence(firestore._delegate);\n  }\n  clearIndexedDbPersistence(firestore: Firestore): Promise<void> {\n    return clearIndexedDbPersistence(firestore._delegate);\n  }\n}\n\n/**\n * Compat class for Firestore. Exposes Firestore Legacy API, but delegates\n * to the functional API of firestore-exp.\n */\nexport class Firestore\n  implements PublicFirestore, FirebaseService, Compat<ExpFirestore>\n{\n  _appCompat?: FirebaseApp;\n  constructor(\n    databaseIdOrApp: _DatabaseId | FirebaseApp,\n    readonly _delegate: ExpFirestore,\n    private _persistenceProvider: PersistenceProvider\n  ) {\n    if (!(databaseIdOrApp instanceof _DatabaseId)) {\n      this._appCompat = databaseIdOrApp as FirebaseApp;\n    }\n  }\n\n  get _databaseId(): _DatabaseId {\n    return this._delegate._databaseId;\n  }\n\n  settings(settingsLiteral: PublicSettings): void {\n    const currentSettings = this._delegate._getSettings();\n    if (\n      !settingsLiteral.merge &&\n      currentSettings.host !== settingsLiteral.host\n    ) {\n      _logWarn(\n        'You are overriding the original host. If you did not intend ' +\n          'to override your settings, use {merge: true}.'\n      );\n    }\n\n    if (settingsLiteral.merge) {\n      settingsLiteral = {\n        ...currentSettings,\n        ...settingsLiteral\n      };\n      // Remove the property from the settings once the merge is completed\n      delete settingsLiteral.merge;\n    }\n\n    this._delegate._setSettings(settingsLiteral);\n  }\n\n  useEmulator(\n    host: string,\n    port: number,\n    options: {\n      mockUserToken?: EmulatorMockTokenOptions | string;\n    } = {}\n  ): void {\n    connectFirestoreEmulator(this._delegate, host, port, options);\n  }\n\n  enableNetwork(): Promise<void> {\n    return enableNetwork(this._delegate);\n  }\n\n  disableNetwork(): Promise<void> {\n    return disableNetwork(this._delegate);\n  }\n\n  enablePersistence(settings?: PublicPersistenceSettings): Promise<void> {\n    let synchronizeTabs = false;\n    let experimentalForceOwningTab = false;\n\n    if (settings) {\n      synchronizeTabs = !!settings.synchronizeTabs;\n      experimentalForceOwningTab = !!settings.experimentalForceOwningTab;\n\n      _validateIsNotUsedTogether(\n        'synchronizeTabs',\n        synchronizeTabs,\n        'experimentalForceOwningTab',\n        experimentalForceOwningTab\n      );\n    }\n\n    return synchronizeTabs\n      ? this._persistenceProvider.enableMultiTabIndexedDbPersistence(this)\n      : this._persistenceProvider.enableIndexedDbPersistence(\n          this,\n          experimentalForceOwningTab\n        );\n  }\n\n  clearPersistence(): Promise<void> {\n    return this._persistenceProvider.clearIndexedDbPersistence(this);\n  }\n\n  terminate(): Promise<void> {\n    if (this._appCompat) {\n      (this._appCompat as _FirebaseApp)._removeServiceInstance(\n        'firestore-compat'\n      );\n      (this._appCompat as _FirebaseApp)._removeServiceInstance('firestore');\n    }\n    return this._delegate._delete();\n  }\n\n  waitForPendingWrites(): Promise<void> {\n    return waitForPendingWrites(this._delegate);\n  }\n\n  onSnapshotsInSync(observer: PartialObserver<void>): Unsubscribe;\n  onSnapshotsInSync(onSync: () => void): Unsubscribe;\n  onSnapshotsInSync(arg: unknown): Unsubscribe {\n    return onSnapshotsInSync(this._delegate, arg as PartialObserver<void>);\n  }\n\n  get app(): FirebaseApp {\n    if (!this._appCompat) {\n      throw new FirestoreError(\n        'failed-precondition',\n        \"Firestore was not initialized using the Firebase SDK. 'app' is \" +\n          'not available'\n      );\n    }\n    return this._appCompat as FirebaseApp;\n  }\n\n  INTERNAL = {\n    delete: () => this.terminate()\n  };\n\n  collection(pathString: string): PublicCollectionReference {\n    try {\n      return new CollectionReference(\n        this,\n        collection(this._delegate, pathString)\n      );\n    } catch (e) {\n      throw replaceFunctionName(\n        e as Error,\n        'collection()',\n        'Firestore.collection()'\n      );\n    }\n  }\n\n  doc(pathString: string): PublicDocumentReference {\n    try {\n      return new DocumentReference(this, doc(this._delegate, pathString));\n    } catch (e) {\n      throw replaceFunctionName(e as Error, 'doc()', 'Firestore.doc()');\n    }\n  }\n\n  collectionGroup(collectionId: string): PublicQuery {\n    try {\n      return new Query(this, collectionGroup(this._delegate, collectionId));\n    } catch (e) {\n      throw replaceFunctionName(\n        e as Error,\n        'collectionGroup()',\n        'Firestore.collectionGroup()'\n      );\n    }\n  }\n\n  runTransaction<T>(\n    updateFunction: (transaction: PublicTransaction) => Promise<T>\n  ): Promise<T> {\n    return runTransaction(this._delegate, transaction =>\n      updateFunction(new Transaction(this, transaction))\n    );\n  }\n\n  batch(): PublicWriteBatch {\n    ensureFirestoreConfigured(this._delegate);\n    return new WriteBatch(\n      new ExpWriteBatch(this._delegate, mutations =>\n        executeWrite(this._delegate, mutations)\n      )\n    );\n  }\n\n  loadBundle(\n    bundleData: ArrayBuffer | ReadableStream<Uint8Array> | string\n  ): LoadBundleTask {\n    return loadBundle(this._delegate, bundleData);\n  }\n\n  namedQuery(name: string): Promise<PublicQuery<DocumentData> | null> {\n    return namedQuery(this._delegate, name).then(expQuery => {\n      if (!expQuery) {\n        return null;\n      }\n      return new Query(\n        this,\n        // We can pass `expQuery` here directly since named queries don't have a UserDataConverter.\n        // Otherwise, we would have to create a new ExpQuery and pass the old UserDataConverter.\n        expQuery\n      );\n    });\n  }\n}\n\nexport class UserDataWriter extends AbstractUserDataWriter {\n  constructor(protected firestore: Firestore) {\n    super();\n  }\n\n  protected convertBytes(bytes: _ByteString): Blob {\n    return new Blob(new Bytes(bytes));\n  }\n\n  protected convertReference(name: string): DocumentReference {\n    const key = this.convertDocumentKey(name, this.firestore._databaseId);\n    return DocumentReference.forKey(key, this.firestore, /* converter= */ null);\n  }\n}\n\nexport function setLogLevel(level: PublicLogLevel): void {\n  setClientLogLevel(level);\n}\n\n/**\n * A reference to a transaction.\n */\nexport class Transaction implements PublicTransaction, Compat<ExpTransaction> {\n  private _userDataWriter: UserDataWriter;\n\n  constructor(\n    private readonly _firestore: Firestore,\n    readonly _delegate: ExpTransaction\n  ) {\n    this._userDataWriter = new UserDataWriter(_firestore);\n  }\n\n  get<T>(\n    documentRef: PublicDocumentReference<T>\n  ): Promise<PublicDocumentSnapshot<T>> {\n    const ref = castReference(documentRef);\n    return this._delegate\n      .get(ref)\n      .then(\n        result =>\n          new DocumentSnapshot(\n            this._firestore,\n            new ExpDocumentSnapshot<T>(\n              this._firestore._delegate,\n              this._userDataWriter,\n              result._key,\n              result._document,\n              result.metadata,\n              ref.converter\n            )\n          )\n      );\n  }\n\n  set<T>(\n    documentRef: DocumentReference<T>,\n    data: Partial<T>,\n    options: PublicSetOptions\n  ): Transaction;\n  set<T>(documentRef: DocumentReference<T>, data: T): Transaction;\n  set<T>(\n    documentRef: PublicDocumentReference<T>,\n    data: T | Partial<T>,\n    options?: PublicSetOptions\n  ): Transaction {\n    const ref = castReference(documentRef);\n    if (options) {\n      validateSetOptions('Transaction.set', options);\n      this._delegate.set(ref, data as PartialWithFieldValue<T>, options);\n    } else {\n      this._delegate.set(ref, data as WithFieldValue<T>);\n    }\n    return this;\n  }\n\n  update(\n    documentRef: PublicDocumentReference<unknown>,\n    data: PublicUpdateData\n  ): Transaction;\n  update(\n    documentRef: PublicDocumentReference<unknown>,\n    field: string | PublicFieldPath,\n    value: unknown,\n    ...moreFieldsAndValues: unknown[]\n  ): Transaction;\n  update(\n    documentRef: PublicDocumentReference<unknown>,\n    dataOrField: unknown,\n    value?: unknown,\n    ...moreFieldsAndValues: unknown[]\n  ): Transaction {\n    const ref = castReference(documentRef);\n    if (arguments.length === 2) {\n      this._delegate.update(ref, dataOrField as PublicUpdateData);\n    } else {\n      this._delegate.update(\n        ref,\n        dataOrField as string | ExpFieldPath,\n        value,\n        ...moreFieldsAndValues\n      );\n    }\n\n    return this;\n  }\n\n  delete(documentRef: PublicDocumentReference<unknown>): Transaction {\n    const ref = castReference(documentRef);\n    this._delegate.delete(ref);\n    return this;\n  }\n}\n\nexport class WriteBatch implements PublicWriteBatch, Compat<ExpWriteBatch> {\n  constructor(readonly _delegate: ExpWriteBatch) {}\n  set<T>(\n    documentRef: DocumentReference<T>,\n    data: Partial<T>,\n    options: PublicSetOptions\n  ): WriteBatch;\n  set<T>(documentRef: DocumentReference<T>, data: T): WriteBatch;\n  set<T>(\n    documentRef: PublicDocumentReference<T>,\n    data: T | Partial<T>,\n    options?: PublicSetOptions\n  ): WriteBatch {\n    const ref = castReference(documentRef);\n    if (options) {\n      validateSetOptions('WriteBatch.set', options);\n      this._delegate.set(ref, data as PartialWithFieldValue<T>, options);\n    } else {\n      this._delegate.set(ref, data as WithFieldValue<T>);\n    }\n    return this;\n  }\n\n  update(\n    documentRef: PublicDocumentReference<unknown>,\n    data: PublicUpdateData\n  ): WriteBatch;\n  update(\n    documentRef: PublicDocumentReference<unknown>,\n    field: string | PublicFieldPath,\n    value: unknown,\n    ...moreFieldsAndValues: unknown[]\n  ): WriteBatch;\n  update(\n    documentRef: PublicDocumentReference<unknown>,\n    dataOrField: string | PublicFieldPath | PublicUpdateData,\n    value?: unknown,\n    ...moreFieldsAndValues: unknown[]\n  ): WriteBatch {\n    const ref = castReference(documentRef);\n    if (arguments.length === 2) {\n      this._delegate.update(ref, dataOrField as PublicUpdateData);\n    } else {\n      this._delegate.update(\n        ref,\n        dataOrField as string | ExpFieldPath,\n        value,\n        ...moreFieldsAndValues\n      );\n    }\n    return this;\n  }\n\n  delete(documentRef: PublicDocumentReference<unknown>): WriteBatch {\n    const ref = castReference(documentRef);\n    this._delegate.delete(ref);\n    return this;\n  }\n\n  commit(): Promise<void> {\n    return this._delegate.commit();\n  }\n}\n\n/**\n * Wraps a `PublicFirestoreDataConverter` translating the types from the\n * experimental SDK into corresponding types from the Classic SDK before passing\n * them to the wrapped converter.\n */\nclass FirestoreDataConverter<U>\n  implements\n    ModularFirestoreDataConverter<U>,\n    Compat<PublicFirestoreDataConverter<U>>\n{\n  private static readonly INSTANCES = new WeakMap();\n\n  private constructor(\n    private readonly _firestore: Firestore,\n    private readonly _userDataWriter: UserDataWriter,\n    readonly _delegate: PublicFirestoreDataConverter<U>\n  ) {}\n\n  fromFirestore(\n    snapshot: ExpQueryDocumentSnapshot,\n    options?: PublicSnapshotOptions\n  ): U {\n    const expSnapshot = new ExpQueryDocumentSnapshot(\n      this._firestore._delegate,\n      this._userDataWriter,\n      snapshot._key,\n      snapshot._document,\n      snapshot.metadata,\n      /* converter= */ null\n    );\n    return this._delegate.fromFirestore(\n      new QueryDocumentSnapshot(this._firestore, expSnapshot),\n      options ?? {}\n    );\n  }\n\n  toFirestore(modelObject: WithFieldValue<U>): PublicDocumentData;\n  toFirestore(\n    modelObject: PartialWithFieldValue<U>,\n    options: PublicSetOptions\n  ): PublicDocumentData;\n  toFirestore(\n    modelObject: WithFieldValue<U> | PartialWithFieldValue<U>,\n    options?: PublicSetOptions\n  ): PublicDocumentData {\n    if (!options) {\n      return this._delegate.toFirestore(modelObject as U);\n    } else {\n      return this._delegate.toFirestore(modelObject as Partial<U>, options);\n    }\n  }\n\n  // Use the same instance of `FirestoreDataConverter` for the given instances\n  // of `Firestore` and `PublicFirestoreDataConverter` so that isEqual() will\n  // compare equal for two objects created with the same converter instance.\n  static getInstance<U>(\n    firestore: Firestore,\n    converter: PublicFirestoreDataConverter<U>\n  ): FirestoreDataConverter<U> {\n    const converterMapByFirestore = FirestoreDataConverter.INSTANCES;\n    let untypedConverterByConverter = converterMapByFirestore.get(firestore);\n    if (!untypedConverterByConverter) {\n      untypedConverterByConverter = new WeakMap();\n      converterMapByFirestore.set(firestore, untypedConverterByConverter);\n    }\n\n    let instance = untypedConverterByConverter.get(converter);\n    if (!instance) {\n      instance = new FirestoreDataConverter(\n        firestore,\n        new UserDataWriter(firestore),\n        converter\n      );\n      untypedConverterByConverter.set(converter, instance);\n    }\n\n    return instance;\n  }\n}\n\n/**\n * A reference to a particular document in a collection in the database.\n */\nexport class DocumentReference<T = PublicDocumentData>\n  implements PublicDocumentReference<T>, Compat<ExpDocumentReference<T>>\n{\n  private _userDataWriter: UserDataWriter;\n\n  constructor(\n    readonly firestore: Firestore,\n    readonly _delegate: ExpDocumentReference<T>\n  ) {\n    this._userDataWriter = new UserDataWriter(firestore);\n  }\n\n  static forPath<U>(\n    path: _ResourcePath,\n    firestore: Firestore,\n    converter: ModularFirestoreDataConverter<U> | null\n  ): DocumentReference<U> {\n    if (path.length % 2 !== 0) {\n      throw new FirestoreError(\n        'invalid-argument',\n        'Invalid document reference. Document ' +\n          'references must have an even number of segments, but ' +\n          `${path.canonicalString()} has ${path.length}`\n      );\n    }\n    return new DocumentReference(\n      firestore,\n      new ExpDocumentReference(\n        firestore._delegate,\n        converter,\n        new _DocumentKey(path)\n      )\n    );\n  }\n\n  static forKey<U>(\n    key: _DocumentKey,\n    firestore: Firestore,\n    converter: ModularFirestoreDataConverter<U> | null\n  ): DocumentReference<U> {\n    return new DocumentReference(\n      firestore,\n      new ExpDocumentReference(firestore._delegate, converter, key)\n    );\n  }\n\n  get id(): string {\n    return this._delegate.id;\n  }\n\n  get parent(): PublicCollectionReference<T> {\n    return new CollectionReference(this.firestore, this._delegate.parent);\n  }\n\n  get path(): string {\n    return this._delegate.path;\n  }\n\n  collection(\n    pathString: string\n  ): PublicCollectionReference<PublicDocumentData> {\n    try {\n      return new CollectionReference(\n        this.firestore,\n        collection(this._delegate, pathString)\n      );\n    } catch (e) {\n      throw replaceFunctionName(\n        e as Error,\n        'collection()',\n        'DocumentReference.collection()'\n      );\n    }\n  }\n\n  isEqual(other: PublicDocumentReference<T>): boolean {\n    other = getModularInstance<PublicDocumentReference<T>>(other);\n\n    if (!(other instanceof ExpDocumentReference)) {\n      return false;\n    }\n    return refEqual(this._delegate, other);\n  }\n\n  set(value: Partial<T>, options: PublicSetOptions): Promise<void>;\n  set(value: T): Promise<void>;\n  set(value: T | Partial<T>, options?: PublicSetOptions): Promise<void> {\n    options = validateSetOptions('DocumentReference.set', options);\n    try {\n      if (options) {\n        return setDoc(\n          this._delegate,\n          value as PartialWithFieldValue<T>,\n          options\n        );\n      } else {\n        return setDoc(this._delegate, value as WithFieldValue<T>);\n      }\n    } catch (e) {\n      throw replaceFunctionName(\n        e as Error,\n        'setDoc()',\n        'DocumentReference.set()'\n      );\n    }\n  }\n\n  update(value: PublicUpdateData): Promise<void>;\n  update(\n    field: string | PublicFieldPath,\n    value: unknown,\n    ...moreFieldsAndValues: unknown[]\n  ): Promise<void>;\n  update(\n    fieldOrUpdateData: string | PublicFieldPath | PublicUpdateData,\n    value?: unknown,\n    ...moreFieldsAndValues: unknown[]\n  ): Promise<void> {\n    try {\n      if (arguments.length === 1) {\n        return updateDoc(this._delegate, fieldOrUpdateData as PublicUpdateData);\n      } else {\n        return updateDoc(\n          this._delegate,\n          fieldOrUpdateData as string | ExpFieldPath,\n          value,\n          ...moreFieldsAndValues\n        );\n      }\n    } catch (e) {\n      throw replaceFunctionName(\n        e as Error,\n        'updateDoc()',\n        'DocumentReference.update()'\n      );\n    }\n  }\n\n  delete(): Promise<void> {\n    return deleteDoc(this._delegate);\n  }\n\n  onSnapshot(observer: PartialObserver<PublicDocumentSnapshot<T>>): Unsubscribe;\n  onSnapshot(\n    options: PublicSnapshotListenOptions,\n    observer: PartialObserver<PublicDocumentSnapshot<T>>\n  ): Unsubscribe;\n  onSnapshot(\n    onNext: NextFn<PublicDocumentSnapshot<T>>,\n    onError?: ErrorFn,\n    onCompletion?: CompleteFn\n  ): Unsubscribe;\n  onSnapshot(\n    options: PublicSnapshotListenOptions,\n    onNext: NextFn<PublicDocumentSnapshot<T>>,\n    onError?: ErrorFn,\n    onCompletion?: CompleteFn\n  ): Unsubscribe;\n\n  onSnapshot(...args: unknown[]): Unsubscribe {\n    const options = extractSnapshotOptions(args);\n    const observer = wrapObserver<DocumentSnapshot<T>, ExpDocumentSnapshot<T>>(\n      args,\n      result =>\n        new DocumentSnapshot(\n          this.firestore,\n          new ExpDocumentSnapshot(\n            this.firestore._delegate,\n            this._userDataWriter,\n            result._key,\n            result._document,\n            result.metadata,\n            this._delegate.converter\n          )\n        )\n    );\n    return onSnapshot(this._delegate, options, observer);\n  }\n\n  get(options?: PublicGetOptions): Promise<PublicDocumentSnapshot<T>> {\n    let snap: Promise<ExpDocumentSnapshot<T>>;\n    if (options?.source === 'cache') {\n      snap = getDocFromCache(this._delegate);\n    } else if (options?.source === 'server') {\n      snap = getDocFromServer(this._delegate);\n    } else {\n      snap = getDoc(this._delegate);\n    }\n\n    return snap.then(\n      result =>\n        new DocumentSnapshot(\n          this.firestore,\n          new ExpDocumentSnapshot(\n            this.firestore._delegate,\n            this._userDataWriter,\n            result._key,\n            result._document,\n            result.metadata,\n            this._delegate.converter\n          )\n        )\n    );\n  }\n\n  withConverter(converter: null): PublicDocumentReference<PublicDocumentData>;\n  withConverter<U>(\n    converter: PublicFirestoreDataConverter<U>\n  ): PublicDocumentReference<U>;\n  withConverter<U>(\n    converter: PublicFirestoreDataConverter<U> | null\n  ): PublicDocumentReference<U> {\n    return new DocumentReference<U>(\n      this.firestore,\n      converter\n        ? this._delegate.withConverter(\n            FirestoreDataConverter.getInstance(this.firestore, converter)\n          )\n        : (this._delegate.withConverter(null) as ExpDocumentReference<U>)\n    );\n  }\n}\n\n/**\n * Replaces the function name in an error thrown by the firestore-exp API\n * with the function names used in the classic API.\n */\nfunction replaceFunctionName(\n  e: Error,\n  original: string | RegExp,\n  updated: string\n): Error {\n  e.message = e.message.replace(original, updated);\n  return e;\n}\n\n/**\n * Iterates the list of arguments from an `onSnapshot` call and returns the\n * first argument that may be an `SnapshotListenOptions` object. Returns an\n * empty object if none is found.\n */\nexport function extractSnapshotOptions(\n  args: unknown[]\n): PublicSnapshotListenOptions {\n  for (const arg of args) {\n    if (typeof arg === 'object' && !isPartialObserver(arg)) {\n      return arg as PublicSnapshotListenOptions;\n    }\n  }\n  return {};\n}\n\n/**\n * Creates an observer that can be passed to the firestore-exp SDK. The\n * observer converts all observed values into the format expected by the classic\n * SDK.\n *\n * @param args - The list of arguments from an `onSnapshot` call.\n * @param wrapper - The function that converts the firestore-exp type into the\n * type used by this shim.\n */\nexport function wrapObserver<CompatType, ExpType>(\n  args: unknown[],\n  wrapper: (val: ExpType) => CompatType\n): PartialObserver<ExpType> {\n  let userObserver: PartialObserver<CompatType>;\n  if (isPartialObserver(args[0])) {\n    userObserver = args[0] as PartialObserver<CompatType>;\n  } else if (isPartialObserver(args[1])) {\n    userObserver = args[1];\n  } else if (typeof args[0] === 'function') {\n    userObserver = {\n      next: args[0] as NextFn<CompatType> | undefined,\n      error: args[1] as ErrorFn | undefined,\n      complete: args[2] as CompleteFn | undefined\n    };\n  } else {\n    userObserver = {\n      next: args[1] as NextFn<CompatType> | undefined,\n      error: args[2] as ErrorFn | undefined,\n      complete: args[3] as CompleteFn | undefined\n    };\n  }\n\n  return {\n    next: val => {\n      if (userObserver!.next) {\n        userObserver!.next(wrapper(val));\n      }\n    },\n    error: userObserver.error?.bind(userObserver),\n    complete: userObserver.complete?.bind(userObserver)\n  };\n}\n\n/**\n * Options interface that can be provided to configure the deserialization of\n * DocumentSnapshots.\n */\nexport interface SnapshotOptions extends PublicSnapshotOptions {}\n\nexport class DocumentSnapshot<T = PublicDocumentData>\n  implements PublicDocumentSnapshot<T>, Compat<ExpDocumentSnapshot<T>>\n{\n  constructor(\n    private readonly _firestore: Firestore,\n    readonly _delegate: ExpDocumentSnapshot<T>\n  ) {}\n\n  get ref(): DocumentReference<T> {\n    return new DocumentReference<T>(this._firestore, this._delegate.ref);\n  }\n\n  get id(): string {\n    return this._delegate.id;\n  }\n\n  get metadata(): SnapshotMetadata {\n    return this._delegate.metadata;\n  }\n\n  get exists(): boolean {\n    return this._delegate.exists();\n  }\n\n  data(options?: PublicSnapshotOptions): T | undefined {\n    return this._delegate.data(options);\n  }\n\n  get(\n    fieldPath: string | PublicFieldPath,\n    options?: PublicSnapshotOptions\n    // We are using `any` here to avoid an explicit cast by our users.\n    // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  ): any {\n    return this._delegate.get(fieldPath as string | ExpFieldPath, options);\n  }\n\n  isEqual(other: DocumentSnapshot<T>): boolean {\n    return snapshotEqual(this._delegate, other._delegate);\n  }\n}\n\nexport class QueryDocumentSnapshot<T = PublicDocumentData>\n  extends DocumentSnapshot<T>\n  implements PublicQueryDocumentSnapshot<T>\n{\n  data(options?: PublicSnapshotOptions): T {\n    const data = this._delegate.data(options);\n    if (this._delegate._converter) {\n      // Undefined is a possible valid value from converter.\n      return data as T;\n    } else {\n      _debugAssert(\n        data !== undefined,\n        'Document in a QueryDocumentSnapshot should exist'\n      );\n      return data;\n    }\n  }\n}\n\nexport class Query<T = PublicDocumentData>\n  implements PublicQuery<T>, Compat<ExpQuery<T>>\n{\n  private readonly _userDataWriter: UserDataWriter;\n\n  constructor(readonly firestore: Firestore, readonly _delegate: ExpQuery<T>) {\n    this._userDataWriter = new UserDataWriter(firestore);\n  }\n\n  where(\n    fieldPath: string | _FieldPath,\n    opStr: PublicWhereFilterOp,\n    value: unknown\n  ): Query<T> {\n    try {\n      // The \"as string\" cast is a little bit of a hack. `where` accepts the\n      // FieldPath Compat type as input, but is not typed as such in order to\n      // not expose this via our public typings file.\n      return new Query<T>(\n        this.firestore,\n        query(this._delegate, where(fieldPath as string, opStr, value))\n      );\n    } catch (e) {\n      throw replaceFunctionName(\n        e as Error,\n        /(orderBy|where)\\(\\)/,\n        'Query.$1()'\n      );\n    }\n  }\n\n  orderBy(\n    fieldPath: string | _FieldPath,\n    directionStr?: PublicOrderByDirection\n  ): Query<T> {\n    try {\n      // The \"as string\" cast is a little bit of a hack. `orderBy` accepts the\n      // FieldPath Compat type as input, but is not typed as such in order to\n      // not expose this via our public typings file.\n      return new Query<T>(\n        this.firestore,\n        query(this._delegate, orderBy(fieldPath as string, directionStr))\n      );\n    } catch (e) {\n      throw replaceFunctionName(\n        e as Error,\n        /(orderBy|where)\\(\\)/,\n        'Query.$1()'\n      );\n    }\n  }\n\n  limit(n: number): Query<T> {\n    try {\n      return new Query<T>(this.firestore, query(this._delegate, limit(n)));\n    } catch (e) {\n      throw replaceFunctionName(e as Error, 'limit()', 'Query.limit()');\n    }\n  }\n\n  limitToLast(n: number): Query<T> {\n    try {\n      return new Query<T>(\n        this.firestore,\n        query(this._delegate, limitToLast(n))\n      );\n    } catch (e) {\n      throw replaceFunctionName(\n        e as Error,\n        'limitToLast()',\n        'Query.limitToLast()'\n      );\n    }\n  }\n\n  startAt(...args: any[]): Query<T> {\n    try {\n      return new Query(this.firestore, query(this._delegate, startAt(...args)));\n    } catch (e) {\n      throw replaceFunctionName(e as Error, 'startAt()', 'Query.startAt()');\n    }\n  }\n\n  startAfter(...args: any[]): Query<T> {\n    try {\n      return new Query(\n        this.firestore,\n        query(this._delegate, startAfter(...args))\n      );\n    } catch (e) {\n      throw replaceFunctionName(\n        e as Error,\n        'startAfter()',\n        'Query.startAfter()'\n      );\n    }\n  }\n\n  endBefore(...args: any[]): Query<T> {\n    try {\n      return new Query(\n        this.firestore,\n        query(this._delegate, endBefore(...args))\n      );\n    } catch (e) {\n      throw replaceFunctionName(e as Error, 'endBefore()', 'Query.endBefore()');\n    }\n  }\n\n  endAt(...args: any[]): Query<T> {\n    try {\n      return new Query(this.firestore, query(this._delegate, endAt(...args)));\n    } catch (e) {\n      throw replaceFunctionName(e as Error, 'endAt()', 'Query.endAt()');\n    }\n  }\n\n  isEqual(other: PublicQuery<T>): boolean {\n    return queryEqual(this._delegate, (other as Query<T>)._delegate);\n  }\n\n  get(options?: PublicGetOptions): Promise<QuerySnapshot<T>> {\n    let query: Promise<ExpQuerySnapshot<T>>;\n    if (options?.source === 'cache') {\n      query = getDocsFromCache(this._delegate);\n    } else if (options?.source === 'server') {\n      query = getDocsFromServer(this._delegate);\n    } else {\n      query = getDocs(this._delegate);\n    }\n    return query.then(\n      result =>\n        new QuerySnapshot(\n          this.firestore,\n          new ExpQuerySnapshot<T>(\n            this.firestore._delegate,\n            this._userDataWriter,\n            this._delegate,\n            result._snapshot\n          )\n        )\n    );\n  }\n\n  onSnapshot(observer: PartialObserver<PublicQuerySnapshot<T>>): Unsubscribe;\n  onSnapshot(\n    options: PublicSnapshotListenOptions,\n    observer: PartialObserver<PublicQuerySnapshot<T>>\n  ): Unsubscribe;\n  onSnapshot(\n    onNext: NextFn<PublicQuerySnapshot<T>>,\n    onError?: ErrorFn,\n    onCompletion?: CompleteFn\n  ): Unsubscribe;\n  onSnapshot(\n    options: PublicSnapshotListenOptions,\n    onNext: NextFn<PublicQuerySnapshot<T>>,\n    onError?: ErrorFn,\n    onCompletion?: CompleteFn\n  ): Unsubscribe;\n\n  onSnapshot(...args: unknown[]): Unsubscribe {\n    const options = extractSnapshotOptions(args);\n    const observer = wrapObserver<QuerySnapshot<T>, ExpQuerySnapshot<T>>(\n      args,\n      snap =>\n        new QuerySnapshot(\n          this.firestore,\n          new ExpQuerySnapshot<T>(\n            this.firestore._delegate,\n            this._userDataWriter,\n            this._delegate,\n            snap._snapshot\n          )\n        )\n    );\n    return onSnapshot(this._delegate, options, observer);\n  }\n\n  withConverter(converter: null): Query<PublicDocumentData>;\n  withConverter<U>(converter: PublicFirestoreDataConverter<U>): Query<U>;\n  withConverter<U>(\n    converter: PublicFirestoreDataConverter<U> | null\n  ): Query<U> {\n    return new Query<U>(\n      this.firestore,\n      converter\n        ? this._delegate.withConverter(\n            FirestoreDataConverter.getInstance(this.firestore, converter)\n          )\n        : (this._delegate.withConverter(null) as ExpQuery<U>)\n    );\n  }\n}\n\nexport class DocumentChange<T = PublicDocumentData>\n  implements PublicDocumentChange<T>, Compat<ExpDocumentChange<T>>\n{\n  constructor(\n    private readonly _firestore: Firestore,\n    readonly _delegate: ExpDocumentChange<T>\n  ) {}\n\n  get type(): PublicDocumentChangeType {\n    return this._delegate.type;\n  }\n\n  get doc(): QueryDocumentSnapshot<T> {\n    return new QueryDocumentSnapshot<T>(this._firestore, this._delegate.doc);\n  }\n\n  get oldIndex(): number {\n    return this._delegate.oldIndex;\n  }\n\n  get newIndex(): number {\n    return this._delegate.newIndex;\n  }\n}\n\nexport class QuerySnapshot<T = PublicDocumentData>\n  implements PublicQuerySnapshot<T>, Compat<ExpQuerySnapshot<T>>\n{\n  constructor(\n    readonly _firestore: Firestore,\n    readonly _delegate: ExpQuerySnapshot<T>\n  ) {}\n\n  get query(): Query<T> {\n    return new Query(this._firestore, this._delegate.query);\n  }\n\n  get metadata(): SnapshotMetadata {\n    return this._delegate.metadata;\n  }\n\n  get size(): number {\n    return this._delegate.size;\n  }\n\n  get empty(): boolean {\n    return this._delegate.empty;\n  }\n\n  get docs(): Array<QueryDocumentSnapshot<T>> {\n    return this._delegate.docs.map(\n      doc => new QueryDocumentSnapshot<T>(this._firestore, doc)\n    );\n  }\n\n  docChanges(\n    options?: PublicSnapshotListenOptions\n  ): Array<PublicDocumentChange<T>> {\n    return this._delegate\n      .docChanges(options)\n      .map(docChange => new DocumentChange<T>(this._firestore, docChange));\n  }\n\n  forEach(\n    callback: (result: QueryDocumentSnapshot<T>) => void,\n    thisArg?: unknown\n  ): void {\n    this._delegate.forEach(snapshot => {\n      callback.call(\n        thisArg,\n        new QueryDocumentSnapshot(this._firestore, snapshot)\n      );\n    });\n  }\n\n  isEqual(other: QuerySnapshot<T>): boolean {\n    return snapshotEqual(this._delegate, other._delegate);\n  }\n}\n\nexport class CollectionReference<T = PublicDocumentData>\n  extends Query<T>\n  implements PublicCollectionReference<T>\n{\n  constructor(\n    readonly firestore: Firestore,\n    readonly _delegate: ExpCollectionReference<T>\n  ) {\n    super(firestore, _delegate);\n  }\n\n  get id(): string {\n    return this._delegate.id;\n  }\n\n  get path(): string {\n    return this._delegate.path;\n  }\n\n  get parent(): DocumentReference<PublicDocumentData> | null {\n    const docRef = this._delegate.parent;\n    return docRef ? new DocumentReference(this.firestore, docRef) : null;\n  }\n\n  doc(documentPath?: string): DocumentReference<T> {\n    try {\n      if (documentPath === undefined) {\n        // Call `doc` without `documentPath` if `documentPath` is `undefined`\n        // as `doc` validates the number of arguments to prevent users from\n        // accidentally passing `undefined`.\n        return new DocumentReference(this.firestore, doc(this._delegate));\n      } else {\n        return new DocumentReference(\n          this.firestore,\n          doc(this._delegate, documentPath)\n        );\n      }\n    } catch (e) {\n      throw replaceFunctionName(\n        e as Error,\n        'doc()',\n        'CollectionReference.doc()'\n      );\n    }\n  }\n\n  add(data: T): Promise<DocumentReference<T>> {\n    return addDoc(this._delegate, data as WithFieldValue<T>).then(\n      docRef => new DocumentReference(this.firestore, docRef)\n    );\n  }\n\n  isEqual(other: CollectionReference<T>): boolean {\n    return refEqual(this._delegate, other._delegate);\n  }\n\n  withConverter(converter: null): CollectionReference<PublicDocumentData>;\n  withConverter<U>(\n    converter: PublicFirestoreDataConverter<U>\n  ): CollectionReference<U>;\n  withConverter<U>(\n    converter: PublicFirestoreDataConverter<U> | null\n  ): CollectionReference<U> {\n    return new CollectionReference<U>(\n      this.firestore,\n      converter\n        ? this._delegate.withConverter(\n            FirestoreDataConverter.getInstance(this.firestore, converter)\n          )\n        : (this._delegate.withConverter(null) as ExpCollectionReference<U>)\n    );\n  }\n}\n\nfunction castReference<T>(\n  documentRef: PublicDocumentReference<T>\n): ExpDocumentReference<T> {\n  return _cast<ExpDocumentReference<T>>(documentRef, ExpDocumentReference);\n}\n", "/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {\n  FieldPath as ExpFieldPath,\n  _FieldPath as InternalFieldPath\n} from '@firebase/firestore';\nimport { FieldPath as PublicFieldPath } from '@firebase/firestore-types';\nimport { Compat, getModularInstance } from '@firebase/util';\n\n// The objects that are a part of this API are exposed to third-parties as\n// compiled javascript so we want to flag our private members with a leading\n// underscore to discourage their use.\n\n/**\n * A `FieldPath` refers to a field in a document. The path may consist of a\n * single field name (referring to a top-level field in the document), or a list\n * of field names (referring to a nested field in the document).\n */\nexport class FieldPath implements PublicFieldPath, Compat<ExpFieldPath> {\n  readonly _delegate: ExpFieldPath;\n  /**\n   * Creates a FieldPath from the provided field names. If more than one field\n   * name is provided, the path will point to a nested field in a document.\n   *\n   * @param fieldNames - A list of field names.\n   */\n  constructor(...fieldNames: string[]) {\n    this._delegate = new ExpFieldPath(...fieldNames);\n  }\n\n  static documentId(): FieldPath {\n    /**\n     * Internal Note: The backend doesn't technically support querying by\n     * document ID. Instead it queries by the entire document name (full path\n     * included), but in the cases we currently support documentId(), the net\n     * effect is the same.\n     */\n    return new FieldPath(InternalFieldPath.keyField().canonicalString());\n  }\n\n  isEqual(other: PublicFieldPath): boolean {\n    other = getModularInstance(other);\n\n    if (!(other instanceof ExpFieldPath)) {\n      return false;\n    }\n    return this._delegate._internalPath.isEqual(other._internalPath);\n  }\n}\n", "/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {\n  arrayRemove,\n  arrayUnion,\n  deleteField,\n  FieldValue as FieldValue1,\n  increment,\n  serverTimestamp\n} from '@firebase/firestore';\nimport { FieldValue as PublicFieldValue } from '@firebase/firestore-types';\nimport { Compat } from '@firebase/util';\n\nexport class FieldValue implements PublicFieldValue, Compat<FieldValue1> {\n  static serverTimestamp(): FieldValue {\n    const delegate = serverTimestamp();\n    delegate._methodName = 'FieldValue.serverTimestamp';\n    return new FieldValue(delegate);\n  }\n\n  static delete(): FieldValue {\n    const delegate = deleteField();\n    delegate._methodName = 'FieldValue.delete';\n    return new FieldValue(delegate);\n  }\n\n  static arrayUnion(...elements: unknown[]): FieldValue {\n    const delegate = arrayUnion(...elements);\n    delegate._methodName = 'FieldValue.arrayUnion';\n    return new FieldValue(delegate);\n  }\n\n  static arrayRemove(...elements: unknown[]): FieldValue {\n    const delegate = arrayRemove(...elements);\n    delegate._methodName = 'FieldValue.arrayRemove';\n    return new FieldValue(delegate);\n  }\n\n  static increment(n: number): FieldValue {\n    const delegate = increment(n);\n    delegate._methodName = 'FieldValue.increment';\n    return new FieldValue(delegate);\n  }\n\n  constructor(readonly _delegate: FieldValue1) {}\n\n  isEqual(other: FieldValue): boolean {\n    return this._delegate.isEqual(other._delegate);\n  }\n}\n", "/**\n * @license\n * Copyright 2021 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n// eslint-disable-next-line import/no-extraneous-dependencies\nimport { FirebaseApp } from '@firebase/app-compat';\nimport { FirebaseNamespace } from '@firebase/app-types';\nimport { _FirebaseNamespace } from '@firebase/app-types/private';\nimport { Component, ComponentType } from '@firebase/component';\nimport {\n  Firestore as ModularFirestore,\n  CACHE_SIZE_UNLIMITED,\n  GeoPoint,\n  Timestamp\n} from '@firebase/firestore';\n\nimport { Blob } from './api/blob';\nimport {\n  Firestore,\n  Transaction,\n  CollectionReference,\n  DocumentReference,\n  DocumentSnapshot,\n  Query,\n  QueryDocumentSnapshot,\n  QuerySnapshot,\n  WriteBatch,\n  setLogLevel\n} from './api/database';\nimport { FieldPath } from './api/field_path';\nimport { FieldValue } from './api/field_value';\n\nconst firestoreNamespace = {\n  Firestore,\n  GeoPoint,\n  Timestamp,\n  Blob,\n  Transaction,\n  WriteBatch,\n  DocumentReference,\n  DocumentSnapshot,\n  Query,\n  QueryDocumentSnapshot,\n  QuerySnapshot,\n  CollectionReference,\n  FieldPath,\n  FieldValue,\n  setLogLevel,\n  CACHE_SIZE_UNLIMITED\n};\n\n/**\n * Configures Firestore as part of the Firebase SDK by calling registerComponent.\n *\n * @param firebase - The FirebaseNamespace to register Firestore with\n * @param firestoreFactory - A factory function that returns a new Firestore\n *    instance.\n */\nexport function configureForFirebase(\n  firebase: FirebaseNamespace,\n  firestoreFactory: (\n    app: FirebaseApp,\n    firestoreExp: ModularFirestore\n  ) => Firestore\n): void {\n  (firebase as _FirebaseNamespace).INTERNAL.registerComponent(\n    new Component(\n      'firestore-compat',\n      container => {\n        const app = container.getProvider('app-compat').getImmediate()!;\n        const firestoreExp = container.getProvider('firestore').getImmediate()!;\n        return firestoreFactory(app, firestoreExp);\n      },\n      'PUBLIC' as ComponentType.PUBLIC\n    ).setServiceProps({ ...firestoreNamespace })\n  );\n}\n", "/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n// eslint-disable-next-line import/no-extraneous-dependencies\nimport firebase from '@firebase/app-compat';\nimport { FirebaseNamespace } from '@firebase/app-types';\n\nimport { name, version } from '../package.json';\n\nimport { Firestore, IndexedDbPersistenceProvider } from './api/database';\nimport { configureForFirebase } from './config';\n\n/**\n * Registers the main Firestore Node build with the components framework.\n * Persistence can be enabled via `firebase.firestore().enablePersistence()`.\n */\nexport function registerFirestore(instance: FirebaseNamespace): void {\n  configureForFirebase(\n    instance,\n    (app, firestoreExp) =>\n      new Firestore(app, firestoreExp, new IndexedDbPersistenceProvider())\n  );\n  instance.registerVersion(name, version, 'node');\n}\n\nregisterFirestore(firebase as unknown as FirebaseNamespace);\n"], "names": ["ExpWriteBatch", "setClientLogLevel", "ExpDocumentSnapshot", "ExpQueryDocumentSnapshot", "ExpDocumentReference", "ExpQuerySnapshot", "ExpFieldPath", "InternalFieldPath"], "mappings": ";;;;;;;;AAAA;;;;;;;;;;;;;;;AAeG;AAKa,SAAA,kBAAkB,CAChC,UAAkB,EAClB,OAA+B,EAAA;IAE/B,IAAI,OAAO,KAAK,SAAS,EAAE;QACzB,OAAO;AACL,YAAA,KAAK,EAAE,KAAK;SACb,CAAC;AACH,KAAA;IAED,IAAI,OAAO,CAAC,WAAW,KAAK,SAAS,IAAI,OAAO,CAAC,KAAK,KAAK,SAAS,EAAE;AACpE,QAAA,MAAM,IAAI,cAAc,CACtB,kBAAkB,EAClB,CAAA,mCAAA,EAAsC,UAAU,CAAiB,eAAA,CAAA;AAC/D,YAAA,yCAAyC,CAC5C,CAAC;AACH,KAAA;AAED,IAAA,OAAO,OAAO,CAAC;AACjB;;ACvCA;;;;;;;;;;;;;;;AAeG;AAKH;AACA,SAAS,yBAAyB,GAAA;AAChC,IAAA,IAAI,OAAO,UAAU,KAAK,WAAW,EAAE;AACrC,QAAA,MAAM,IAAI,cAAc,CACtB,eAAe,EACf,oDAAoD,CACrD,CAAC;AACH,KAAA;AACH,CAAC;AAED;AACA,SAAS,qBAAqB,GAAA;IAC5B,IAAI,CAAC,kBAAkB,EAAE,EAAE;AACzB,QAAA,MAAM,IAAI,cAAc,CACtB,eAAe,EACf,yDAAyD,CAC1D,CAAC;AACH,KAAA;AACH,CAAC;AAED;MACa,IAAI,CAAA;AACf,IAAA,WAAA,CAAqB,SAAgB,EAAA;QAAhB,IAAS,CAAA,SAAA,GAAT,SAAS,CAAO;KAAI;IACzC,OAAO,gBAAgB,CAAC,MAAc,EAAA;AACpC,QAAA,qBAAqB,EAAE,CAAC;QACxB,OAAO,IAAI,IAAI,CAAC,KAAK,CAAC,gBAAgB,CAAC,MAAM,CAAC,CAAC,CAAC;KACjD;IAED,OAAO,cAAc,CAAC,KAAiB,EAAA;AACrC,QAAA,yBAAyB,EAAE,CAAC;QAC5B,OAAO,IAAI,IAAI,CAAC,KAAK,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC,CAAC;KAC9C;IAED,QAAQ,GAAA;AACN,QAAA,qBAAqB,EAAE,CAAC;AACxB,QAAA,OAAO,IAAI,CAAC,SAAS,CAAC,QAAQ,EAAE,CAAC;KAClC;IAED,YAAY,GAAA;AACV,QAAA,yBAAyB,EAAE,CAAC;AAC5B,QAAA,OAAO,IAAI,CAAC,SAAS,CAAC,YAAY,EAAE,CAAC;KACtC;AAED,IAAA,OAAO,CAAC,KAAW,EAAA;QACjB,OAAO,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC;KAChD;IAED,QAAQ,GAAA;QACN,OAAO,eAAe,GAAG,IAAI,CAAC,QAAQ,EAAE,GAAG,GAAG,CAAC;KAChD;AACF;;ACtED;;;;;;;;;;;;;;;AAeG;AAkBG,SAAU,iBAAiB,CAAI,GAAY,EAAA;AAC/C,IAAA,OAAO,oBAAoB,CAAC,GAAG,EAAE,CAAC,MAAM,EAAE,OAAO,EAAE,UAAU,CAAC,CAAC,CAAC;AAClE,CAAC;AAED;;;AAGG;AACH,SAAS,oBAAoB,CAAC,GAAY,EAAE,OAAiB,EAAA;IAC3D,IAAI,OAAO,GAAG,KAAK,QAAQ,IAAI,GAAG,KAAK,IAAI,EAAE;AAC3C,QAAA,OAAO,KAAK,CAAC;AACd,KAAA;IAED,MAAM,MAAM,GAAG,GAA8B,CAAC;AAC9C,IAAA,KAAK,MAAM,MAAM,IAAI,OAAO,EAAE;QAC5B,IAAI,MAAM,IAAI,MAAM,IAAI,OAAO,MAAM,CAAC,MAAM,CAAC,KAAK,UAAU,EAAE;AAC5D,YAAA,OAAO,IAAI,CAAC;AACb,SAAA;AACF,KAAA;AACD,IAAA,OAAO,KAAK,CAAC;AACf;;ACrDA;;;;;;;;;;;;;;;AAeG;AAoIH;;AAEG;MACU,4BAA4B,CAAA;IACvC,0BAA0B,CACxB,SAAoB,EACpB,cAAuB,EAAA;QAEvB,OAAO,0BAA0B,CAAC,SAAS,CAAC,SAAS,EAAE,EAAE,cAAc,EAAE,CAAC,CAAC;KAC5E;AACD,IAAA,kCAAkC,CAAC,SAAoB,EAAA;AACrD,QAAA,OAAO,kCAAkC,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC;KAChE;AACD,IAAA,yBAAyB,CAAC,SAAoB,EAAA;AAC5C,QAAA,OAAO,yBAAyB,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC;KACvD;AACF,CAAA;AAED;;;AAGG;MACU,SAAS,CAAA;AAIpB,IAAA,WAAA,CACE,eAA0C,EACjC,SAAuB,EACxB,oBAAyC,EAAA;QADxC,IAAS,CAAA,SAAA,GAAT,SAAS,CAAc;QACxB,IAAoB,CAAA,oBAAA,GAApB,oBAAoB,CAAqB;AAgHnD,QAAA,IAAA,CAAA,QAAQ,GAAG;AACT,YAAA,MAAM,EAAE,MAAM,IAAI,CAAC,SAAS,EAAE;SAC/B,CAAC;AAhHA,QAAA,IAAI,EAAE,eAAe,YAAY,WAAW,CAAC,EAAE;AAC7C,YAAA,IAAI,CAAC,UAAU,GAAG,eAA8B,CAAC;AAClD,SAAA;KACF;AAED,IAAA,IAAI,WAAW,GAAA;AACb,QAAA,OAAO,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC;KACnC;AAED,IAAA,QAAQ,CAAC,eAA+B,EAAA;QACtC,MAAM,eAAe,GAAG,IAAI,CAAC,SAAS,CAAC,YAAY,EAAE,CAAC;QACtD,IACE,CAAC,eAAe,CAAC,KAAK;AACtB,YAAA,eAAe,CAAC,IAAI,KAAK,eAAe,CAAC,IAAI,EAC7C;AACA,YAAA,QAAQ,CACN,8DAA8D;AAC5D,gBAAA,+CAA+C,CAClD,CAAC;AACH,SAAA;QAED,IAAI,eAAe,CAAC,KAAK,EAAE;AACzB,YAAA,eAAe,GACV,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,EAAA,EAAA,eAAe,CACf,EAAA,eAAe,CACnB,CAAC;;YAEF,OAAO,eAAe,CAAC,KAAK,CAAC;AAC9B,SAAA;AAED,QAAA,IAAI,CAAC,SAAS,CAAC,YAAY,CAAC,eAAe,CAAC,CAAC;KAC9C;AAED,IAAA,WAAW,CACT,IAAY,EACZ,IAAY,EACZ,UAEI,EAAE,EAAA;QAEN,wBAAwB,CAAC,IAAI,CAAC,SAAS,EAAE,IAAI,EAAE,IAAI,EAAE,OAAO,CAAC,CAAC;KAC/D;IAED,aAAa,GAAA;AACX,QAAA,OAAO,aAAa,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;KACtC;IAED,cAAc,GAAA;AACZ,QAAA,OAAO,cAAc,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;KACvC;AAED,IAAA,iBAAiB,CAAC,QAAoC,EAAA;QACpD,IAAI,eAAe,GAAG,KAAK,CAAC;QAC5B,IAAI,0BAA0B,GAAG,KAAK,CAAC;AAEvC,QAAA,IAAI,QAAQ,EAAE;AACZ,YAAA,eAAe,GAAG,CAAC,CAAC,QAAQ,CAAC,eAAe,CAAC;AAC7C,YAAA,0BAA0B,GAAG,CAAC,CAAC,QAAQ,CAAC,0BAA0B,CAAC;YAEnE,0BAA0B,CACxB,iBAAiB,EACjB,eAAe,EACf,4BAA4B,EAC5B,0BAA0B,CAC3B,CAAC;AACH,SAAA;AAED,QAAA,OAAO,eAAe;cAClB,IAAI,CAAC,oBAAoB,CAAC,kCAAkC,CAAC,IAAI,CAAC;cAClE,IAAI,CAAC,oBAAoB,CAAC,0BAA0B,CAClD,IAAI,EACJ,0BAA0B,CAC3B,CAAC;KACP;IAED,gBAAgB,GAAA;QACd,OAAO,IAAI,CAAC,oBAAoB,CAAC,yBAAyB,CAAC,IAAI,CAAC,CAAC;KAClE;IAED,SAAS,GAAA;QACP,IAAI,IAAI,CAAC,UAAU,EAAE;AAClB,YAAA,IAAI,CAAC,UAA2B,CAAC,sBAAsB,CACtD,kBAAkB,CACnB,CAAC;AACD,YAAA,IAAI,CAAC,UAA2B,CAAC,sBAAsB,CAAC,WAAW,CAAC,CAAC;AACvE,SAAA;AACD,QAAA,OAAO,IAAI,CAAC,SAAS,CAAC,OAAO,EAAE,CAAC;KACjC;IAED,oBAAoB,GAAA;AAClB,QAAA,OAAO,oBAAoB,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;KAC7C;AAID,IAAA,iBAAiB,CAAC,GAAY,EAAA;QAC5B,OAAO,iBAAiB,CAAC,IAAI,CAAC,SAAS,EAAE,GAA4B,CAAC,CAAC;KACxE;AAED,IAAA,IAAI,GAAG,GAAA;AACL,QAAA,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE;AACpB,YAAA,MAAM,IAAI,cAAc,CACtB,qBAAqB,EACrB,iEAAiE;AAC/D,gBAAA,eAAe,CAClB,CAAC;AACH,SAAA;QACD,OAAO,IAAI,CAAC,UAAyB,CAAC;KACvC;AAMD,IAAA,UAAU,CAAC,UAAkB,EAAA;QAC3B,IAAI;AACF,YAAA,OAAO,IAAI,mBAAmB,CAC5B,IAAI,EACJ,UAAU,CAAC,IAAI,CAAC,SAAS,EAAE,UAAU,CAAC,CACvC,CAAC;AACH,SAAA;AAAC,QAAA,OAAO,CAAC,EAAE;YACV,MAAM,mBAAmB,CACvB,CAAU,EACV,cAAc,EACd,wBAAwB,CACzB,CAAC;AACH,SAAA;KACF;AAED,IAAA,GAAG,CAAC,UAAkB,EAAA;QACpB,IAAI;AACF,YAAA,OAAO,IAAI,iBAAiB,CAAC,IAAI,EAAE,GAAG,CAAC,IAAI,CAAC,SAAS,EAAE,UAAU,CAAC,CAAC,CAAC;AACrE,SAAA;AAAC,QAAA,OAAO,CAAC,EAAE;YACV,MAAM,mBAAmB,CAAC,CAAU,EAAE,OAAO,EAAE,iBAAiB,CAAC,CAAC;AACnE,SAAA;KACF;AAED,IAAA,eAAe,CAAC,YAAoB,EAAA;QAClC,IAAI;AACF,YAAA,OAAO,IAAI,KAAK,CAAC,IAAI,EAAE,eAAe,CAAC,IAAI,CAAC,SAAS,EAAE,YAAY,CAAC,CAAC,CAAC;AACvE,SAAA;AAAC,QAAA,OAAO,CAAC,EAAE;YACV,MAAM,mBAAmB,CACvB,CAAU,EACV,mBAAmB,EACnB,6BAA6B,CAC9B,CAAC;AACH,SAAA;KACF;AAED,IAAA,cAAc,CACZ,cAA8D,EAAA;QAE9D,OAAO,cAAc,CAAC,IAAI,CAAC,SAAS,EAAE,WAAW,IAC/C,cAAc,CAAC,IAAI,WAAW,CAAC,IAAI,EAAE,WAAW,CAAC,CAAC,CACnD,CAAC;KACH;IAED,KAAK,GAAA;AACH,QAAA,yBAAyB,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;QAC1C,OAAO,IAAI,UAAU,CACnB,IAAIA,YAAa,CAAC,IAAI,CAAC,SAAS,EAAE,SAAS,IACzC,YAAY,CAAC,IAAI,CAAC,SAAS,EAAE,SAAS,CAAC,CACxC,CACF,CAAC;KACH;AAED,IAAA,UAAU,CACR,UAA6D,EAAA;QAE7D,OAAO,UAAU,CAAC,IAAI,CAAC,SAAS,EAAE,UAAU,CAAC,CAAC;KAC/C;AAED,IAAA,UAAU,CAAC,IAAY,EAAA;AACrB,QAAA,OAAO,UAAU,CAAC,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,CAAC,IAAI,CAAC,QAAQ,IAAG;YACtD,IAAI,CAAC,QAAQ,EAAE;AACb,gBAAA,OAAO,IAAI,CAAC;AACb,aAAA;YACD,OAAO,IAAI,KAAK,CACd,IAAI;;;AAGJ,YAAA,QAAQ,CACT,CAAC;AACJ,SAAC,CAAC,CAAC;KACJ;AACF,CAAA;AAEK,MAAO,cAAe,SAAQ,sBAAsB,CAAA;AACxD,IAAA,WAAA,CAAsB,SAAoB,EAAA;AACxC,QAAA,KAAK,EAAE,CAAC;QADY,IAAS,CAAA,SAAA,GAAT,SAAS,CAAW;KAEzC;AAES,IAAA,YAAY,CAAC,KAAkB,EAAA;QACvC,OAAO,IAAI,IAAI,CAAC,IAAI,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC;KACnC;AAES,IAAA,gBAAgB,CAAC,IAAY,EAAA;AACrC,QAAA,MAAM,GAAG,GAAG,IAAI,CAAC,kBAAkB,CAAC,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC,CAAC;AACtE,QAAA,OAAO,iBAAiB,CAAC,MAAM,CAAC,GAAG,EAAE,IAAI,CAAC,SAAS,mBAAmB,IAAI,CAAC,CAAC;KAC7E;AACF,CAAA;AAEK,SAAU,WAAW,CAAC,KAAqB,EAAA;IAC/CC,aAAiB,CAAC,KAAK,CAAC,CAAC;AAC3B,CAAC;AAED;;AAEG;MACU,WAAW,CAAA;IAGtB,WACmB,CAAA,UAAqB,EAC7B,SAAyB,EAAA;QADjB,IAAU,CAAA,UAAA,GAAV,UAAU,CAAW;QAC7B,IAAS,CAAA,SAAA,GAAT,SAAS,CAAgB;QAElC,IAAI,CAAC,eAAe,GAAG,IAAI,cAAc,CAAC,UAAU,CAAC,CAAC;KACvD;AAED,IAAA,GAAG,CACD,WAAuC,EAAA;AAEvC,QAAA,MAAM,GAAG,GAAG,aAAa,CAAC,WAAW,CAAC,CAAC;QACvC,OAAO,IAAI,CAAC,SAAS;aAClB,GAAG,CAAC,GAAG,CAAC;AACR,aAAA,IAAI,CACH,MAAM,IACJ,IAAI,gBAAgB,CAClB,IAAI,CAAC,UAAU,EACf,IAAIC,kBAAmB,CACrB,IAAI,CAAC,UAAU,CAAC,SAAS,EACzB,IAAI,CAAC,eAAe,EACpB,MAAM,CAAC,IAAI,EACX,MAAM,CAAC,SAAS,EAChB,MAAM,CAAC,QAAQ,EACf,GAAG,CAAC,SAAS,CACd,CACF,CACJ,CAAC;KACL;AAQD,IAAA,GAAG,CACD,WAAuC,EACvC,IAAoB,EACpB,OAA0B,EAAA;AAE1B,QAAA,MAAM,GAAG,GAAG,aAAa,CAAC,WAAW,CAAC,CAAC;AACvC,QAAA,IAAI,OAAO,EAAE;AACX,YAAA,kBAAkB,CAAC,iBAAiB,EAAE,OAAO,CAAC,CAAC;YAC/C,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,GAAG,EAAE,IAAgC,EAAE,OAAO,CAAC,CAAC;AACpE,SAAA;AAAM,aAAA;YACL,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,GAAG,EAAE,IAAyB,CAAC,CAAC;AACpD,SAAA;AACD,QAAA,OAAO,IAAI,CAAC;KACb;IAYD,MAAM,CACJ,WAA6C,EAC7C,WAAoB,EACpB,KAAe,EACf,GAAG,mBAA8B,EAAA;AAEjC,QAAA,MAAM,GAAG,GAAG,aAAa,CAAC,WAAW,CAAC,CAAC;AACvC,QAAA,IAAI,SAAS,CAAC,MAAM,KAAK,CAAC,EAAE;YAC1B,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,GAAG,EAAE,WAA+B,CAAC,CAAC;AAC7D,SAAA;AAAM,aAAA;AACL,YAAA,IAAI,CAAC,SAAS,CAAC,MAAM,CACnB,GAAG,EACH,WAAoC,EACpC,KAAK,EACL,GAAG,mBAAmB,CACvB,CAAC;AACH,SAAA;AAED,QAAA,OAAO,IAAI,CAAC;KACb;AAED,IAAA,MAAM,CAAC,WAA6C,EAAA;AAClD,QAAA,MAAM,GAAG,GAAG,aAAa,CAAC,WAAW,CAAC,CAAC;AACvC,QAAA,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;AAC3B,QAAA,OAAO,IAAI,CAAC;KACb;AACF,CAAA;MAEY,UAAU,CAAA;AACrB,IAAA,WAAA,CAAqB,SAAwB,EAAA;QAAxB,IAAS,CAAA,SAAA,GAAT,SAAS,CAAe;KAAI;AAOjD,IAAA,GAAG,CACD,WAAuC,EACvC,IAAoB,EACpB,OAA0B,EAAA;AAE1B,QAAA,MAAM,GAAG,GAAG,aAAa,CAAC,WAAW,CAAC,CAAC;AACvC,QAAA,IAAI,OAAO,EAAE;AACX,YAAA,kBAAkB,CAAC,gBAAgB,EAAE,OAAO,CAAC,CAAC;YAC9C,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,GAAG,EAAE,IAAgC,EAAE,OAAO,CAAC,CAAC;AACpE,SAAA;AAAM,aAAA;YACL,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,GAAG,EAAE,IAAyB,CAAC,CAAC;AACpD,SAAA;AACD,QAAA,OAAO,IAAI,CAAC;KACb;IAYD,MAAM,CACJ,WAA6C,EAC7C,WAAwD,EACxD,KAAe,EACf,GAAG,mBAA8B,EAAA;AAEjC,QAAA,MAAM,GAAG,GAAG,aAAa,CAAC,WAAW,CAAC,CAAC;AACvC,QAAA,IAAI,SAAS,CAAC,MAAM,KAAK,CAAC,EAAE;YAC1B,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,GAAG,EAAE,WAA+B,CAAC,CAAC;AAC7D,SAAA;AAAM,aAAA;AACL,YAAA,IAAI,CAAC,SAAS,CAAC,MAAM,CACnB,GAAG,EACH,WAAoC,EACpC,KAAK,EACL,GAAG,mBAAmB,CACvB,CAAC;AACH,SAAA;AACD,QAAA,OAAO,IAAI,CAAC;KACb;AAED,IAAA,MAAM,CAAC,WAA6C,EAAA;AAClD,QAAA,MAAM,GAAG,GAAG,aAAa,CAAC,WAAW,CAAC,CAAC;AACvC,QAAA,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;AAC3B,QAAA,OAAO,IAAI,CAAC;KACb;IAED,MAAM,GAAA;AACJ,QAAA,OAAO,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,CAAC;KAChC;AACF,CAAA;AAED;;;;AAIG;AACH,MAAM,sBAAsB,CAAA;AAO1B,IAAA,WAAA,CACmB,UAAqB,EACrB,eAA+B,EACvC,SAA0C,EAAA;QAFlC,IAAU,CAAA,UAAA,GAAV,UAAU,CAAW;QACrB,IAAe,CAAA,eAAA,GAAf,eAAe,CAAgB;QACvC,IAAS,CAAA,SAAA,GAAT,SAAS,CAAiC;KACjD;IAEJ,aAAa,CACX,QAAkC,EAClC,OAA+B,EAAA;QAE/B,MAAM,WAAW,GAAG,IAAIC,uBAAwB,CAC9C,IAAI,CAAC,UAAU,CAAC,SAAS,EACzB,IAAI,CAAC,eAAe,EACpB,QAAQ,CAAC,IAAI,EACb,QAAQ,CAAC,SAAS,EAClB,QAAQ,CAAC,QAAQ;yBACA,IAAI,CACtB,CAAC;QACF,OAAO,IAAI,CAAC,SAAS,CAAC,aAAa,CACjC,IAAI,qBAAqB,CAAC,IAAI,CAAC,UAAU,EAAE,WAAW,CAAC,EACvD,OAAO,KAAP,IAAA,IAAA,OAAO,cAAP,OAAO,GAAI,EAAE,CACd,CAAC;KACH;IAOD,WAAW,CACT,WAAyD,EACzD,OAA0B,EAAA;QAE1B,IAAI,CAAC,OAAO,EAAE;YACZ,OAAO,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC,WAAgB,CAAC,CAAC;AACrD,SAAA;AAAM,aAAA;YACL,OAAO,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC,WAAyB,EAAE,OAAO,CAAC,CAAC;AACvE,SAAA;KACF;;;;AAKD,IAAA,OAAO,WAAW,CAChB,SAAoB,EACpB,SAA0C,EAAA;AAE1C,QAAA,MAAM,uBAAuB,GAAG,sBAAsB,CAAC,SAAS,CAAC;QACjE,IAAI,2BAA2B,GAAG,uBAAuB,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;QACzE,IAAI,CAAC,2BAA2B,EAAE;AAChC,YAAA,2BAA2B,GAAG,IAAI,OAAO,EAAE,CAAC;AAC5C,YAAA,uBAAuB,CAAC,GAAG,CAAC,SAAS,EAAE,2BAA2B,CAAC,CAAC;AACrE,SAAA;QAED,IAAI,QAAQ,GAAG,2BAA2B,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;QAC1D,IAAI,CAAC,QAAQ,EAAE;AACb,YAAA,QAAQ,GAAG,IAAI,sBAAsB,CACnC,SAAS,EACT,IAAI,cAAc,CAAC,SAAS,CAAC,EAC7B,SAAS,CACV,CAAC;AACF,YAAA,2BAA2B,CAAC,GAAG,CAAC,SAAS,EAAE,QAAQ,CAAC,CAAC;AACtD,SAAA;AAED,QAAA,OAAO,QAAQ,CAAC;KACjB;;AAnEuB,sBAAA,CAAA,SAAS,GAAG,IAAI,OAAO,EAAE,CAAC;AAsEpD;;AAEG;MACU,iBAAiB,CAAA;IAK5B,WACW,CAAA,SAAoB,EACpB,SAAkC,EAAA;QADlC,IAAS,CAAA,SAAA,GAAT,SAAS,CAAW;QACpB,IAAS,CAAA,SAAA,GAAT,SAAS,CAAyB;QAE3C,IAAI,CAAC,eAAe,GAAG,IAAI,cAAc,CAAC,SAAS,CAAC,CAAC;KACtD;AAED,IAAA,OAAO,OAAO,CACZ,IAAmB,EACnB,SAAoB,EACpB,SAAkD,EAAA;AAElD,QAAA,IAAI,IAAI,CAAC,MAAM,GAAG,CAAC,KAAK,CAAC,EAAE;AACzB,YAAA,MAAM,IAAI,cAAc,CACtB,kBAAkB,EAClB,uCAAuC;gBACrC,uDAAuD;gBACvD,CAAG,EAAA,IAAI,CAAC,eAAe,EAAE,CAAA,KAAA,EAAQ,IAAI,CAAC,MAAM,CAAE,CAAA,CACjD,CAAC;AACH,SAAA;QACD,OAAO,IAAI,iBAAiB,CAC1B,SAAS,EACT,IAAIC,mBAAoB,CACtB,SAAS,CAAC,SAAS,EACnB,SAAS,EACT,IAAI,YAAY,CAAC,IAAI,CAAC,CACvB,CACF,CAAC;KACH;AAED,IAAA,OAAO,MAAM,CACX,GAAiB,EACjB,SAAoB,EACpB,SAAkD,EAAA;AAElD,QAAA,OAAO,IAAI,iBAAiB,CAC1B,SAAS,EACT,IAAIA,mBAAoB,CAAC,SAAS,CAAC,SAAS,EAAE,SAAS,EAAE,GAAG,CAAC,CAC9D,CAAC;KACH;AAED,IAAA,IAAI,EAAE,GAAA;AACJ,QAAA,OAAO,IAAI,CAAC,SAAS,CAAC,EAAE,CAAC;KAC1B;AAED,IAAA,IAAI,MAAM,GAAA;AACR,QAAA,OAAO,IAAI,mBAAmB,CAAC,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC;KACvE;AAED,IAAA,IAAI,IAAI,GAAA;AACN,QAAA,OAAO,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC;KAC5B;AAED,IAAA,UAAU,CACR,UAAkB,EAAA;QAElB,IAAI;AACF,YAAA,OAAO,IAAI,mBAAmB,CAC5B,IAAI,CAAC,SAAS,EACd,UAAU,CAAC,IAAI,CAAC,SAAS,EAAE,UAAU,CAAC,CACvC,CAAC;AACH,SAAA;AAAC,QAAA,OAAO,CAAC,EAAE;YACV,MAAM,mBAAmB,CACvB,CAAU,EACV,cAAc,EACd,gCAAgC,CACjC,CAAC;AACH,SAAA;KACF;AAED,IAAA,OAAO,CAAC,KAAiC,EAAA;AACvC,QAAA,KAAK,GAAG,kBAAkB,CAA6B,KAAK,CAAC,CAAC;AAE9D,QAAA,IAAI,EAAE,KAAK,YAAYA,mBAAoB,CAAC,EAAE;AAC5C,YAAA,OAAO,KAAK,CAAC;AACd,SAAA;QACD,OAAO,QAAQ,CAAC,IAAI,CAAC,SAAS,EAAE,KAAK,CAAC,CAAC;KACxC;IAID,GAAG,CAAC,KAAqB,EAAE,OAA0B,EAAA;AACnD,QAAA,OAAO,GAAG,kBAAkB,CAAC,uBAAuB,EAAE,OAAO,CAAC,CAAC;QAC/D,IAAI;AACF,YAAA,IAAI,OAAO,EAAE;gBACX,OAAO,MAAM,CACX,IAAI,CAAC,SAAS,EACd,KAAiC,EACjC,OAAO,CACR,CAAC;AACH,aAAA;AAAM,iBAAA;gBACL,OAAO,MAAM,CAAC,IAAI,CAAC,SAAS,EAAE,KAA0B,CAAC,CAAC;AAC3D,aAAA;AACF,SAAA;AAAC,QAAA,OAAO,CAAC,EAAE;YACV,MAAM,mBAAmB,CACvB,CAAU,EACV,UAAU,EACV,yBAAyB,CAC1B,CAAC;AACH,SAAA;KACF;AAQD,IAAA,MAAM,CACJ,iBAA8D,EAC9D,KAAe,EACf,GAAG,mBAA8B,EAAA;QAEjC,IAAI;AACF,YAAA,IAAI,SAAS,CAAC,MAAM,KAAK,CAAC,EAAE;gBAC1B,OAAO,SAAS,CAAC,IAAI,CAAC,SAAS,EAAE,iBAAqC,CAAC,CAAC;AACzE,aAAA;AAAM,iBAAA;AACL,gBAAA,OAAO,SAAS,CACd,IAAI,CAAC,SAAS,EACd,iBAA0C,EAC1C,KAAK,EACL,GAAG,mBAAmB,CACvB,CAAC;AACH,aAAA;AACF,SAAA;AAAC,QAAA,OAAO,CAAC,EAAE;YACV,MAAM,mBAAmB,CACvB,CAAU,EACV,aAAa,EACb,4BAA4B,CAC7B,CAAC;AACH,SAAA;KACF;IAED,MAAM,GAAA;AACJ,QAAA,OAAO,SAAS,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;KAClC;IAmBD,UAAU,CAAC,GAAG,IAAe,EAAA;AAC3B,QAAA,MAAM,OAAO,GAAG,sBAAsB,CAAC,IAAI,CAAC,CAAC;QAC7C,MAAM,QAAQ,GAAG,YAAY,CAC3B,IAAI,EACJ,MAAM,IACJ,IAAI,gBAAgB,CAClB,IAAI,CAAC,SAAS,EACd,IAAIF,kBAAmB,CACrB,IAAI,CAAC,SAAS,CAAC,SAAS,EACxB,IAAI,CAAC,eAAe,EACpB,MAAM,CAAC,IAAI,EACX,MAAM,CAAC,SAAS,EAChB,MAAM,CAAC,QAAQ,EACf,IAAI,CAAC,SAAS,CAAC,SAAS,CACzB,CACF,CACJ,CAAC;QACF,OAAO,UAAU,CAAC,IAAI,CAAC,SAAS,EAAE,OAAO,EAAE,QAAQ,CAAC,CAAC;KACtD;AAED,IAAA,GAAG,CAAC,OAA0B,EAAA;AAC5B,QAAA,IAAI,IAAqC,CAAC;QAC1C,IAAI,CAAA,OAAO,KAAA,IAAA,IAAP,OAAO,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAP,OAAO,CAAE,MAAM,MAAK,OAAO,EAAE;AAC/B,YAAA,IAAI,GAAG,eAAe,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;AACxC,SAAA;aAAM,IAAI,CAAA,OAAO,KAAA,IAAA,IAAP,OAAO,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAP,OAAO,CAAE,MAAM,MAAK,QAAQ,EAAE;AACvC,YAAA,IAAI,GAAG,gBAAgB,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;AACzC,SAAA;AAAM,aAAA;AACL,YAAA,IAAI,GAAG,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;AAC/B,SAAA;QAED,OAAO,IAAI,CAAC,IAAI,CACd,MAAM,IACJ,IAAI,gBAAgB,CAClB,IAAI,CAAC,SAAS,EACd,IAAIA,kBAAmB,CACrB,IAAI,CAAC,SAAS,CAAC,SAAS,EACxB,IAAI,CAAC,eAAe,EACpB,MAAM,CAAC,IAAI,EACX,MAAM,CAAC,SAAS,EAChB,MAAM,CAAC,QAAQ,EACf,IAAI,CAAC,SAAS,CAAC,SAAS,CACzB,CACF,CACJ,CAAC;KACH;AAMD,IAAA,aAAa,CACX,SAAiD,EAAA;AAEjD,QAAA,OAAO,IAAI,iBAAiB,CAC1B,IAAI,CAAC,SAAS,EACd,SAAS;AACP,cAAE,IAAI,CAAC,SAAS,CAAC,aAAa,CAC1B,sBAAsB,CAAC,WAAW,CAAC,IAAI,CAAC,SAAS,EAAE,SAAS,CAAC,CAC9D;cACA,IAAI,CAAC,SAAS,CAAC,aAAa,CAAC,IAAI,CAA6B,CACpE,CAAC;KACH;AACF,CAAA;AAED;;;AAGG;AACH,SAAS,mBAAmB,CAC1B,CAAQ,EACR,QAAyB,EACzB,OAAe,EAAA;AAEf,IAAA,CAAC,CAAC,OAAO,GAAG,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC;AACjD,IAAA,OAAO,CAAC,CAAC;AACX,CAAC;AAED;;;;AAIG;AACG,SAAU,sBAAsB,CACpC,IAAe,EAAA;AAEf,IAAA,KAAK,MAAM,GAAG,IAAI,IAAI,EAAE;QACtB,IAAI,OAAO,GAAG,KAAK,QAAQ,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,EAAE;AACtD,YAAA,OAAO,GAAkC,CAAC;AAC3C,SAAA;AACF,KAAA;AACD,IAAA,OAAO,EAAE,CAAC;AACZ,CAAC;AAED;;;;;;;;AAQG;AACa,SAAA,YAAY,CAC1B,IAAe,EACf,OAAqC,EAAA;;AAErC,IAAA,IAAI,YAAyC,CAAC;AAC9C,IAAA,IAAI,iBAAiB,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,EAAE;AAC9B,QAAA,YAAY,GAAG,IAAI,CAAC,CAAC,CAAgC,CAAC;AACvD,KAAA;AAAM,SAAA,IAAI,iBAAiB,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,EAAE;AACrC,QAAA,YAAY,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;AACxB,KAAA;AAAM,SAAA,IAAI,OAAO,IAAI,CAAC,CAAC,CAAC,KAAK,UAAU,EAAE;AACxC,QAAA,YAAY,GAAG;AACb,YAAA,IAAI,EAAE,IAAI,CAAC,CAAC,CAAmC;AAC/C,YAAA,KAAK,EAAE,IAAI,CAAC,CAAC,CAAwB;AACrC,YAAA,QAAQ,EAAE,IAAI,CAAC,CAAC,CAA2B;SAC5C,CAAC;AACH,KAAA;AAAM,SAAA;AACL,QAAA,YAAY,GAAG;AACb,YAAA,IAAI,EAAE,IAAI,CAAC,CAAC,CAAmC;AAC/C,YAAA,KAAK,EAAE,IAAI,CAAC,CAAC,CAAwB;AACrC,YAAA,QAAQ,EAAE,IAAI,CAAC,CAAC,CAA2B;SAC5C,CAAC;AACH,KAAA;IAED,OAAO;QACL,IAAI,EAAE,GAAG,IAAG;YACV,IAAI,YAAa,CAAC,IAAI,EAAE;gBACtB,YAAa,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC;AAClC,aAAA;SACF;QACD,KAAK,EAAE,MAAA,YAAY,CAAC,KAAK,MAAE,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,IAAI,CAAC,YAAY,CAAC;QAC7C,QAAQ,EAAE,MAAA,YAAY,CAAC,QAAQ,MAAE,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,IAAI,CAAC,YAAY,CAAC;KACpD,CAAC;AACJ,CAAC;MAQY,gBAAgB,CAAA;IAG3B,WACmB,CAAA,UAAqB,EAC7B,SAAiC,EAAA;QADzB,IAAU,CAAA,UAAA,GAAV,UAAU,CAAW;QAC7B,IAAS,CAAA,SAAA,GAAT,SAAS,CAAwB;KACxC;AAEJ,IAAA,IAAI,GAAG,GAAA;AACL,QAAA,OAAO,IAAI,iBAAiB,CAAI,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC;KACtE;AAED,IAAA,IAAI,EAAE,GAAA;AACJ,QAAA,OAAO,IAAI,CAAC,SAAS,CAAC,EAAE,CAAC;KAC1B;AAED,IAAA,IAAI,QAAQ,GAAA;AACV,QAAA,OAAO,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC;KAChC;AAED,IAAA,IAAI,MAAM,GAAA;AACR,QAAA,OAAO,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,CAAC;KAChC;AAED,IAAA,IAAI,CAAC,OAA+B,EAAA;QAClC,OAAO,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;KACrC;IAED,GAAG,CACD,SAAmC,EACnC,OAA+B;;;;QAI/B,OAAO,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,SAAkC,EAAE,OAAO,CAAC,CAAC;KACxE;AAED,IAAA,OAAO,CAAC,KAA0B,EAAA;QAChC,OAAO,aAAa,CAAC,IAAI,CAAC,SAAS,EAAE,KAAK,CAAC,SAAS,CAAC,CAAC;KACvD;AACF,CAAA;AAEK,MAAO,qBACX,SAAQ,gBAAmB,CAAA;AAG3B,IAAA,IAAI,CAAC,OAA+B,EAAA;QAClC,MAAM,IAAI,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;AAC1C,QAAA,IAAI,IAAI,CAAC,SAAS,CAAC,UAAU,EAAE;;AAE7B,YAAA,OAAO,IAAS,CAAC;AAClB,SAAA;AAAM,aAAA;AACL,YAAA,YAAY,CACV,IAAI,KAAK,SAAS,EAClB,kDAAkD,CACnD,CAAC;AACF,YAAA,OAAO,IAAI,CAAC;AACb,SAAA;KACF;AACF,CAAA;MAEY,KAAK,CAAA;IAKhB,WAAqB,CAAA,SAAoB,EAAW,SAAsB,EAAA;QAArD,IAAS,CAAA,SAAA,GAAT,SAAS,CAAW;QAAW,IAAS,CAAA,SAAA,GAAT,SAAS,CAAa;QACxE,IAAI,CAAC,eAAe,GAAG,IAAI,cAAc,CAAC,SAAS,CAAC,CAAC;KACtD;AAED,IAAA,KAAK,CACH,SAA8B,EAC9B,KAA0B,EAC1B,KAAc,EAAA;QAEd,IAAI;;;;YAIF,OAAO,IAAI,KAAK,CACd,IAAI,CAAC,SAAS,EACd,KAAK,CAAC,IAAI,CAAC,SAAS,EAAE,KAAK,CAAC,SAAmB,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC,CAChE,CAAC;AACH,SAAA;AAAC,QAAA,OAAO,CAAC,EAAE;YACV,MAAM,mBAAmB,CACvB,CAAU,EACV,qBAAqB,EACrB,YAAY,CACb,CAAC;AACH,SAAA;KACF;IAED,OAAO,CACL,SAA8B,EAC9B,YAAqC,EAAA;QAErC,IAAI;;;;YAIF,OAAO,IAAI,KAAK,CACd,IAAI,CAAC,SAAS,EACd,KAAK,CAAC,IAAI,CAAC,SAAS,EAAE,OAAO,CAAC,SAAmB,EAAE,YAAY,CAAC,CAAC,CAClE,CAAC;AACH,SAAA;AAAC,QAAA,OAAO,CAAC,EAAE;YACV,MAAM,mBAAmB,CACvB,CAAU,EACV,qBAAqB,EACrB,YAAY,CACb,CAAC;AACH,SAAA;KACF;AAED,IAAA,KAAK,CAAC,CAAS,EAAA;QACb,IAAI;AACF,YAAA,OAAO,IAAI,KAAK,CAAI,IAAI,CAAC,SAAS,EAAE,KAAK,CAAC,IAAI,CAAC,SAAS,EAAE,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACtE,SAAA;AAAC,QAAA,OAAO,CAAC,EAAE;YACV,MAAM,mBAAmB,CAAC,CAAU,EAAE,SAAS,EAAE,eAAe,CAAC,CAAC;AACnE,SAAA;KACF;AAED,IAAA,WAAW,CAAC,CAAS,EAAA;QACnB,IAAI;AACF,YAAA,OAAO,IAAI,KAAK,CACd,IAAI,CAAC,SAAS,EACd,KAAK,CAAC,IAAI,CAAC,SAAS,EAAE,WAAW,CAAC,CAAC,CAAC,CAAC,CACtC,CAAC;AACH,SAAA;AAAC,QAAA,OAAO,CAAC,EAAE;YACV,MAAM,mBAAmB,CACvB,CAAU,EACV,eAAe,EACf,qBAAqB,CACtB,CAAC;AACH,SAAA;KACF;IAED,OAAO,CAAC,GAAG,IAAW,EAAA;QACpB,IAAI;YACF,OAAO,IAAI,KAAK,CAAC,IAAI,CAAC,SAAS,EAAE,KAAK,CAAC,IAAI,CAAC,SAAS,EAAE,OAAO,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;AAC3E,SAAA;AAAC,QAAA,OAAO,CAAC,EAAE;YACV,MAAM,mBAAmB,CAAC,CAAU,EAAE,WAAW,EAAE,iBAAiB,CAAC,CAAC;AACvE,SAAA;KACF;IAED,UAAU,CAAC,GAAG,IAAW,EAAA;QACvB,IAAI;YACF,OAAO,IAAI,KAAK,CACd,IAAI,CAAC,SAAS,EACd,KAAK,CAAC,IAAI,CAAC,SAAS,EAAE,UAAU,CAAC,GAAG,IAAI,CAAC,CAAC,CAC3C,CAAC;AACH,SAAA;AAAC,QAAA,OAAO,CAAC,EAAE;YACV,MAAM,mBAAmB,CACvB,CAAU,EACV,cAAc,EACd,oBAAoB,CACrB,CAAC;AACH,SAAA;KACF;IAED,SAAS,CAAC,GAAG,IAAW,EAAA;QACtB,IAAI;YACF,OAAO,IAAI,KAAK,CACd,IAAI,CAAC,SAAS,EACd,KAAK,CAAC,IAAI,CAAC,SAAS,EAAE,SAAS,CAAC,GAAG,IAAI,CAAC,CAAC,CAC1C,CAAC;AACH,SAAA;AAAC,QAAA,OAAO,CAAC,EAAE;YACV,MAAM,mBAAmB,CAAC,CAAU,EAAE,aAAa,EAAE,mBAAmB,CAAC,CAAC;AAC3E,SAAA;KACF;IAED,KAAK,CAAC,GAAG,IAAW,EAAA;QAClB,IAAI;YACF,OAAO,IAAI,KAAK,CAAC,IAAI,CAAC,SAAS,EAAE,KAAK,CAAC,IAAI,CAAC,SAAS,EAAE,KAAK,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;AACzE,SAAA;AAAC,QAAA,OAAO,CAAC,EAAE;YACV,MAAM,mBAAmB,CAAC,CAAU,EAAE,SAAS,EAAE,eAAe,CAAC,CAAC;AACnE,SAAA;KACF;AAED,IAAA,OAAO,CAAC,KAAqB,EAAA;QAC3B,OAAO,UAAU,CAAC,IAAI,CAAC,SAAS,EAAG,KAAkB,CAAC,SAAS,CAAC,CAAC;KAClE;AAED,IAAA,GAAG,CAAC,OAA0B,EAAA;AAC5B,QAAA,IAAI,KAAmC,CAAC;QACxC,IAAI,CAAA,OAAO,KAAA,IAAA,IAAP,OAAO,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAP,OAAO,CAAE,MAAM,MAAK,OAAO,EAAE;AAC/B,YAAA,KAAK,GAAG,gBAAgB,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;AAC1C,SAAA;aAAM,IAAI,CAAA,OAAO,KAAA,IAAA,IAAP,OAAO,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAP,OAAO,CAAE,MAAM,MAAK,QAAQ,EAAE;AACvC,YAAA,KAAK,GAAG,iBAAiB,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;AAC3C,SAAA;AAAM,aAAA;AACL,YAAA,KAAK,GAAG,OAAO,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;AACjC,SAAA;AACD,QAAA,OAAO,KAAK,CAAC,IAAI,CACf,MAAM,IACJ,IAAI,aAAa,CACf,IAAI,CAAC,SAAS,EACd,IAAIG,eAAgB,CAClB,IAAI,CAAC,SAAS,CAAC,SAAS,EACxB,IAAI,CAAC,eAAe,EACpB,IAAI,CAAC,SAAS,EACd,MAAM,CAAC,SAAS,CACjB,CACF,CACJ,CAAC;KACH;IAmBD,UAAU,CAAC,GAAG,IAAe,EAAA;AAC3B,QAAA,MAAM,OAAO,GAAG,sBAAsB,CAAC,IAAI,CAAC,CAAC;AAC7C,QAAA,MAAM,QAAQ,GAAG,YAAY,CAC3B,IAAI,EACJ,IAAI,IACF,IAAI,aAAa,CACf,IAAI,CAAC,SAAS,EACd,IAAIA,eAAgB,CAClB,IAAI,CAAC,SAAS,CAAC,SAAS,EACxB,IAAI,CAAC,eAAe,EACpB,IAAI,CAAC,SAAS,EACd,IAAI,CAAC,SAAS,CACf,CACF,CACJ,CAAC;QACF,OAAO,UAAU,CAAC,IAAI,CAAC,SAAS,EAAE,OAAO,EAAE,QAAQ,CAAC,CAAC;KACtD;AAID,IAAA,aAAa,CACX,SAAiD,EAAA;AAEjD,QAAA,OAAO,IAAI,KAAK,CACd,IAAI,CAAC,SAAS,EACd,SAAS;AACP,cAAE,IAAI,CAAC,SAAS,CAAC,aAAa,CAC1B,sBAAsB,CAAC,WAAW,CAAC,IAAI,CAAC,SAAS,EAAE,SAAS,CAAC,CAC9D;cACA,IAAI,CAAC,SAAS,CAAC,aAAa,CAAC,IAAI,CAAiB,CACxD,CAAC;KACH;AACF,CAAA;MAEY,cAAc,CAAA;IAGzB,WACmB,CAAA,UAAqB,EAC7B,SAA+B,EAAA;QADvB,IAAU,CAAA,UAAA,GAAV,UAAU,CAAW;QAC7B,IAAS,CAAA,SAAA,GAAT,SAAS,CAAsB;KACtC;AAEJ,IAAA,IAAI,IAAI,GAAA;AACN,QAAA,OAAO,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC;KAC5B;AAED,IAAA,IAAI,GAAG,GAAA;AACL,QAAA,OAAO,IAAI,qBAAqB,CAAI,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC;KAC1E;AAED,IAAA,IAAI,QAAQ,GAAA;AACV,QAAA,OAAO,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC;KAChC;AAED,IAAA,IAAI,QAAQ,GAAA;AACV,QAAA,OAAO,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC;KAChC;AACF,CAAA;MAEY,aAAa,CAAA;IAGxB,WACW,CAAA,UAAqB,EACrB,SAA8B,EAAA;QAD9B,IAAU,CAAA,UAAA,GAAV,UAAU,CAAW;QACrB,IAAS,CAAA,SAAA,GAAT,SAAS,CAAqB;KACrC;AAEJ,IAAA,IAAI,KAAK,GAAA;AACP,QAAA,OAAO,IAAI,KAAK,CAAC,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;KACzD;AAED,IAAA,IAAI,QAAQ,GAAA;AACV,QAAA,OAAO,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC;KAChC;AAED,IAAA,IAAI,IAAI,GAAA;AACN,QAAA,OAAO,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC;KAC5B;AAED,IAAA,IAAI,KAAK,GAAA;AACP,QAAA,OAAO,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC;KAC7B;AAED,IAAA,IAAI,IAAI,GAAA;QACN,OAAO,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,GAAG,CAC5B,GAAG,IAAI,IAAI,qBAAqB,CAAI,IAAI,CAAC,UAAU,EAAE,GAAG,CAAC,CAC1D,CAAC;KACH;AAED,IAAA,UAAU,CACR,OAAqC,EAAA;QAErC,OAAO,IAAI,CAAC,SAAS;aAClB,UAAU,CAAC,OAAO,CAAC;AACnB,aAAA,GAAG,CAAC,SAAS,IAAI,IAAI,cAAc,CAAI,IAAI,CAAC,UAAU,EAAE,SAAS,CAAC,CAAC,CAAC;KACxE;IAED,OAAO,CACL,QAAoD,EACpD,OAAiB,EAAA;AAEjB,QAAA,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,QAAQ,IAAG;AAChC,YAAA,QAAQ,CAAC,IAAI,CACX,OAAO,EACP,IAAI,qBAAqB,CAAC,IAAI,CAAC,UAAU,EAAE,QAAQ,CAAC,CACrD,CAAC;AACJ,SAAC,CAAC,CAAC;KACJ;AAED,IAAA,OAAO,CAAC,KAAuB,EAAA;QAC7B,OAAO,aAAa,CAAC,IAAI,CAAC,SAAS,EAAE,KAAK,CAAC,SAAS,CAAC,CAAC;KACvD;AACF,CAAA;AAEK,MAAO,mBACX,SAAQ,KAAQ,CAAA;IAGhB,WACW,CAAA,SAAoB,EACpB,SAAoC,EAAA;AAE7C,QAAA,KAAK,CAAC,SAAS,EAAE,SAAS,CAAC,CAAC;QAHnB,IAAS,CAAA,SAAA,GAAT,SAAS,CAAW;QACpB,IAAS,CAAA,SAAA,GAAT,SAAS,CAA2B;KAG9C;AAED,IAAA,IAAI,EAAE,GAAA;AACJ,QAAA,OAAO,IAAI,CAAC,SAAS,CAAC,EAAE,CAAC;KAC1B;AAED,IAAA,IAAI,IAAI,GAAA;AACN,QAAA,OAAO,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC;KAC5B;AAED,IAAA,IAAI,MAAM,GAAA;AACR,QAAA,MAAM,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC;AACrC,QAAA,OAAO,MAAM,GAAG,IAAI,iBAAiB,CAAC,IAAI,CAAC,SAAS,EAAE,MAAM,CAAC,GAAG,IAAI,CAAC;KACtE;AAED,IAAA,GAAG,CAAC,YAAqB,EAAA;QACvB,IAAI;YACF,IAAI,YAAY,KAAK,SAAS,EAAE;;;;AAI9B,gBAAA,OAAO,IAAI,iBAAiB,CAAC,IAAI,CAAC,SAAS,EAAE,GAAG,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC;AACnE,aAAA;AAAM,iBAAA;AACL,gBAAA,OAAO,IAAI,iBAAiB,CAC1B,IAAI,CAAC,SAAS,EACd,GAAG,CAAC,IAAI,CAAC,SAAS,EAAE,YAAY,CAAC,CAClC,CAAC;AACH,aAAA;AACF,SAAA;AAAC,QAAA,OAAO,CAAC,EAAE;YACV,MAAM,mBAAmB,CACvB,CAAU,EACV,OAAO,EACP,2BAA2B,CAC5B,CAAC;AACH,SAAA;KACF;AAED,IAAA,GAAG,CAAC,IAAO,EAAA;QACT,OAAO,MAAM,CAAC,IAAI,CAAC,SAAS,EAAE,IAAyB,CAAC,CAAC,IAAI,CAC3D,MAAM,IAAI,IAAI,iBAAiB,CAAC,IAAI,CAAC,SAAS,EAAE,MAAM,CAAC,CACxD,CAAC;KACH;AAED,IAAA,OAAO,CAAC,KAA6B,EAAA;QACnC,OAAO,QAAQ,CAAC,IAAI,CAAC,SAAS,EAAE,KAAK,CAAC,SAAS,CAAC,CAAC;KAClD;AAMD,IAAA,aAAa,CACX,SAAiD,EAAA;AAEjD,QAAA,OAAO,IAAI,mBAAmB,CAC5B,IAAI,CAAC,SAAS,EACd,SAAS;AACP,cAAE,IAAI,CAAC,SAAS,CAAC,aAAa,CAC1B,sBAAsB,CAAC,WAAW,CAAC,IAAI,CAAC,SAAS,EAAE,SAAS,CAAC,CAC9D;cACA,IAAI,CAAC,SAAS,CAAC,aAAa,CAAC,IAAI,CAA+B,CACtE,CAAC;KACH;AACF,CAAA;AAED,SAAS,aAAa,CACpB,WAAuC,EAAA;AAEvC,IAAA,OAAO,KAAK,CAA0B,WAAW,EAAED,mBAAoB,CAAC,CAAC;AAC3E;;AC5zCA;;;;;;;;;;;;;;;AAeG;AASH;AACA;AACA;AAEA;;;;AAIG;MACU,SAAS,CAAA;AAEpB;;;;;AAKG;AACH,IAAA,WAAA,CAAY,GAAG,UAAoB,EAAA;QACjC,IAAI,CAAC,SAAS,GAAG,IAAIE,WAAY,CAAC,GAAG,UAAU,CAAC,CAAC;KAClD;AAED,IAAA,OAAO,UAAU,GAAA;AACf;;;;;AAKG;QACH,OAAO,IAAI,SAAS,CAACC,UAAiB,CAAC,QAAQ,EAAE,CAAC,eAAe,EAAE,CAAC,CAAC;KACtE;AAED,IAAA,OAAO,CAAC,KAAsB,EAAA;AAC5B,QAAA,KAAK,GAAG,kBAAkB,CAAC,KAAK,CAAC,CAAC;AAElC,QAAA,IAAI,EAAE,KAAK,YAAYD,WAAY,CAAC,EAAE;AACpC,YAAA,OAAO,KAAK,CAAC;AACd,SAAA;AACD,QAAA,OAAO,IAAI,CAAC,SAAS,CAAC,aAAa,CAAC,OAAO,CAAC,KAAK,CAAC,aAAa,CAAC,CAAC;KAClE;AACF;;AC/DD;;;;;;;;;;;;;;;AAeG;MAaU,UAAU,CAAA;AA+BrB,IAAA,WAAA,CAAqB,SAAsB,EAAA;QAAtB,IAAS,CAAA,SAAA,GAAT,SAAS,CAAa;KAAI;AA9B/C,IAAA,OAAO,eAAe,GAAA;AACpB,QAAA,MAAM,QAAQ,GAAG,eAAe,EAAE,CAAC;AACnC,QAAA,QAAQ,CAAC,WAAW,GAAG,4BAA4B,CAAC;AACpD,QAAA,OAAO,IAAI,UAAU,CAAC,QAAQ,CAAC,CAAC;KACjC;AAED,IAAA,OAAO,MAAM,GAAA;AACX,QAAA,MAAM,QAAQ,GAAG,WAAW,EAAE,CAAC;AAC/B,QAAA,QAAQ,CAAC,WAAW,GAAG,mBAAmB,CAAC;AAC3C,QAAA,OAAO,IAAI,UAAU,CAAC,QAAQ,CAAC,CAAC;KACjC;AAED,IAAA,OAAO,UAAU,CAAC,GAAG,QAAmB,EAAA;AACtC,QAAA,MAAM,QAAQ,GAAG,UAAU,CAAC,GAAG,QAAQ,CAAC,CAAC;AACzC,QAAA,QAAQ,CAAC,WAAW,GAAG,uBAAuB,CAAC;AAC/C,QAAA,OAAO,IAAI,UAAU,CAAC,QAAQ,CAAC,CAAC;KACjC;AAED,IAAA,OAAO,WAAW,CAAC,GAAG,QAAmB,EAAA;AACvC,QAAA,MAAM,QAAQ,GAAG,WAAW,CAAC,GAAG,QAAQ,CAAC,CAAC;AAC1C,QAAA,QAAQ,CAAC,WAAW,GAAG,wBAAwB,CAAC;AAChD,QAAA,OAAO,IAAI,UAAU,CAAC,QAAQ,CAAC,CAAC;KACjC;IAED,OAAO,SAAS,CAAC,CAAS,EAAA;AACxB,QAAA,MAAM,QAAQ,GAAG,SAAS,CAAC,CAAC,CAAC,CAAC;AAC9B,QAAA,QAAQ,CAAC,WAAW,GAAG,sBAAsB,CAAC;AAC9C,QAAA,OAAO,IAAI,UAAU,CAAC,QAAQ,CAAC,CAAC;KACjC;AAID,IAAA,OAAO,CAAC,KAAiB,EAAA;QACvB,OAAO,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC;KAChD;AACF;;AChED;;;;;;;;;;;;;;;AAeG;AA8BH,MAAM,kBAAkB,GAAG;IACzB,SAAS;IACT,QAAQ;IACR,SAAS;IACT,IAAI;IACJ,WAAW;IACX,UAAU;IACV,iBAAiB;IACjB,gBAAgB;IAChB,KAAK;IACL,qBAAqB;IACrB,aAAa;IACb,mBAAmB;IACnB,SAAS;IACT,UAAU;IACV,WAAW;IACX,oBAAoB;CACrB,CAAC;AAEF;;;;;;AAMG;AACa,SAAA,oBAAoB,CAClC,QAA2B,EAC3B,gBAGc,EAAA;AAEb,IAAA,QAA+B,CAAC,QAAQ,CAAC,iBAAiB,CACzD,IAAI,SAAS,CACX,kBAAkB,EAClB,SAAS,IAAG;QACV,MAAM,GAAG,GAAG,SAAS,CAAC,WAAW,CAAC,YAAY,CAAC,CAAC,YAAY,EAAG,CAAC;QAChE,MAAM,YAAY,GAAG,SAAS,CAAC,WAAW,CAAC,WAAW,CAAC,CAAC,YAAY,EAAG,CAAC;AACxE,QAAA,OAAO,gBAAgB,CAAC,GAAG,EAAE,YAAY,CAAC,CAAC;KAC5C,EACD,QAAgC,CACjC,CAAC,eAAe,CAAM,MAAA,CAAA,MAAA,CAAA,EAAA,EAAA,kBAAkB,CAAG,CAAA,CAC7C,CAAC;AACJ;;ACzFA;;;;;;;;;;;;;;;AAeG;AAWH;;;AAGG;AACG,SAAU,iBAAiB,CAAC,QAA2B,EAAA;IAC3D,oBAAoB,CAClB,QAAQ,EACR,CAAC,GAAG,EAAE,YAAY,KAChB,IAAI,SAAS,CAAC,GAAG,EAAE,YAAY,EAAE,IAAI,4BAA4B,EAAE,CAAC,CACvE,CAAC;IACF,QAAQ,CAAC,eAAe,CAAC,IAAI,EAAE,OAAO,EAAE,MAAM,CAAC,CAAC;AAClD,CAAC;AAED,iBAAiB,CAAC,QAAwC,CAAC;;;;"}