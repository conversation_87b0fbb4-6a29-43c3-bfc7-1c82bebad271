{"version": 3, "file": "index.node.cjs.js", "sources": ["../src/serializer.ts", "../src/constants.ts", "../src/error.ts", "../src/context.ts", "../src/service.ts", "../src/config.ts", "../src/api.ts", "../src/index.node.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nconst LONG_TYPE = 'type.googleapis.com/google.protobuf.Int64Value';\nconst UNSIGNED_LONG_TYPE = 'type.googleapis.com/google.protobuf.UInt64Value';\n\nfunction mapValues(\n  // { [k: string]: unknown } is no longer a wildcard assignment target after typescript 3.5\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  o: { [key: string]: any },\n  f: (arg0: unknown) => unknown\n): object {\n  const result: { [key: string]: unknown } = {};\n  for (const key in o) {\n    if (o.hasOwnProperty(key)) {\n      result[key] = f(o[key]);\n    }\n  }\n  return result;\n}\n\n/**\n * Takes data and encodes it in a JSON-friendly way, such that types such as\n * Date are preserved.\n * @internal\n * @param data - Data to encode.\n */\nexport function encode(data: unknown): unknown {\n  if (data == null) {\n    return null;\n  }\n  if (data instanceof Number) {\n    data = data.valueOf();\n  }\n  if (typeof data === 'number' && isFinite(data)) {\n    // Any number in JS is safe to put directly in JSON and parse as a double\n    // without any loss of precision.\n    return data;\n  }\n  if (data === true || data === false) {\n    return data;\n  }\n  if (Object.prototype.toString.call(data) === '[object String]') {\n    return data;\n  }\n  if (data instanceof Date) {\n    return data.toISOString();\n  }\n  if (Array.isArray(data)) {\n    return data.map(x => encode(x));\n  }\n  if (typeof data === 'function' || typeof data === 'object') {\n    return mapValues(data!, x => encode(x));\n  }\n  // If we got this far, the data is not encodable.\n  throw new Error('Data cannot be encoded in JSON: ' + data);\n}\n\n/**\n * Takes data that's been encoded in a JSON-friendly form and returns a form\n * with richer datatypes, such as Dates, etc.\n * @internal\n * @param json - JSON to convert.\n */\nexport function decode(json: unknown): unknown {\n  if (json == null) {\n    return json;\n  }\n  if ((json as { [key: string]: unknown })['@type']) {\n    switch ((json as { [key: string]: unknown })['@type']) {\n      case LONG_TYPE:\n      // Fall through and handle this the same as unsigned.\n      case UNSIGNED_LONG_TYPE: {\n        // Technically, this could work return a valid number for malformed\n        // data if there was a number followed by garbage. But it's just not\n        // worth all the extra code to detect that case.\n        const value = Number((json as { [key: string]: unknown })['value']);\n        if (isNaN(value)) {\n          throw new Error('Data cannot be decoded from JSON: ' + json);\n        }\n        return value;\n      }\n      default: {\n        throw new Error('Data cannot be decoded from JSON: ' + json);\n      }\n    }\n  }\n  if (Array.isArray(json)) {\n    return json.map(x => decode(x));\n  }\n  if (typeof json === 'function' || typeof json === 'object') {\n    return mapValues(json!, x => decode(x));\n  }\n  // Anything else is safe to return.\n  return json;\n}\n", "/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n/**\n * Type constant for Firebase Functions.\n */\nexport const FUNCTIONS_TYPE = 'functions';\n", "/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { FunctionsErrorCodeCore as FunctionsErrorCode } from './public-types';\nimport { decode } from './serializer';\nimport { HttpResponseBody } from './service';\nimport { FirebaseError } from '@firebase/util';\nimport { FUNCTIONS_TYPE } from './constants';\n\n/**\n * Standard error codes for different ways a request can fail, as defined by:\n * https://github.com/googleapis/googleapis/blob/master/google/rpc/code.proto\n *\n * This map is used primarily to convert from a backend error code string to\n * a client SDK error code string, and make sure it's in the supported set.\n */\nconst errorCodeMap: { [name: string]: FunctionsErrorCode } = {\n  OK: 'ok',\n  CANCELLED: 'cancelled',\n  UNKNOWN: 'unknown',\n  INVALID_ARGUMENT: 'invalid-argument',\n  DEADLINE_EXCEEDED: 'deadline-exceeded',\n  NOT_FOUND: 'not-found',\n  ALREADY_EXISTS: 'already-exists',\n  PERMISSION_DENIED: 'permission-denied',\n  UNAUTHENTICATED: 'unauthenticated',\n  RESOURCE_EXHAUSTED: 'resource-exhausted',\n  FAILED_PRECONDITION: 'failed-precondition',\n  ABORTED: 'aborted',\n  OUT_OF_RANGE: 'out-of-range',\n  UNIMPLEMENTED: 'unimplemented',\n  INTERNAL: 'internal',\n  UNAVAILABLE: 'unavailable',\n  DATA_LOSS: 'data-loss'\n};\n\n/**\n * An explicit error that can be thrown from a handler to send an error to the\n * client that called the function.\n */\nexport class FunctionsError extends FirebaseError {\n  constructor(\n    /**\n     * A standard error code that will be returned to the client. This also\n     * determines the HTTP status code of the response, as defined in code.proto.\n     */\n    code: FunctionsErrorCode,\n    message?: string,\n    /**\n     * Extra data to be converted to JSON and included in the error response.\n     */\n    readonly details?: unknown\n  ) {\n    super(`${FUNCTIONS_TYPE}/${code}`, message || '');\n  }\n}\n\n/**\n * Takes an HTTP status code and returns the corresponding ErrorCode.\n * This is the standard HTTP status code -> error mapping defined in:\n * https://github.com/googleapis/googleapis/blob/master/google/rpc/code.proto\n *\n * @param status An HTTP status code.\n * @return The corresponding ErrorCode, or ErrorCode.UNKNOWN if none.\n */\nfunction codeForHTTPStatus(status: number): FunctionsErrorCode {\n  // Make sure any successful status is OK.\n  if (status >= 200 && status < 300) {\n    return 'ok';\n  }\n  switch (status) {\n    case 0:\n      // This can happen if the server returns 500.\n      return 'internal';\n    case 400:\n      return 'invalid-argument';\n    case 401:\n      return 'unauthenticated';\n    case 403:\n      return 'permission-denied';\n    case 404:\n      return 'not-found';\n    case 409:\n      return 'aborted';\n    case 429:\n      return 'resource-exhausted';\n    case 499:\n      return 'cancelled';\n    case 500:\n      return 'internal';\n    case 501:\n      return 'unimplemented';\n    case 503:\n      return 'unavailable';\n    case 504:\n      return 'deadline-exceeded';\n    default: // ignore\n  }\n  return 'unknown';\n}\n\n/**\n * Takes an HTTP response and returns the corresponding Error, if any.\n */\nexport function _errorForResponse(\n  status: number,\n  bodyJSON: HttpResponseBody | null\n): Error | null {\n  let code = codeForHTTPStatus(status);\n\n  // Start with reasonable defaults from the status code.\n  let description: string = code;\n\n  let details: unknown = undefined;\n\n  // Then look through the body for explicit details.\n  try {\n    const errorJSON = bodyJSON && bodyJSON.error;\n    if (errorJSON) {\n      const status = errorJSON.status;\n      if (typeof status === 'string') {\n        if (!errorCodeMap[status]) {\n          // They must've included an unknown error code in the body.\n          return new FunctionsError('internal', 'internal');\n        }\n        code = errorCodeMap[status];\n\n        // TODO(klimt): Add better default descriptions for error enums.\n        // The default description needs to be updated for the new code.\n        description = status;\n      }\n\n      const message = errorJSON.message;\n      if (typeof message === 'string') {\n        description = message;\n      }\n\n      details = errorJSON.details;\n      if (details !== undefined) {\n        details = decode(details);\n      }\n    }\n  } catch (e) {\n    // If we couldn't parse explicit error data, that's fine.\n  }\n\n  if (code === 'ok') {\n    // Technically, there's an edge case where a developer could explicitly\n    // return an error code of OK, and we will treat it as success, but that\n    // seems reasonable.\n    return null;\n  }\n\n  return new FunctionsError(code, description, details);\n}\n", "/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { Provider } from '@firebase/component';\nimport {\n  AppCheckInternalComponentName,\n  FirebaseAppCheckInternal\n} from '@firebase/app-check-interop-types';\nimport {\n  MessagingInternal,\n  MessagingInternalComponentName\n} from '@firebase/messaging-interop-types';\nimport {\n  FirebaseAuthInternal,\n  FirebaseAuthInternalName\n} from '@firebase/auth-interop-types';\n\n/**\n * The metadata that should be supplied with function calls.\n * @internal\n */\nexport interface Context {\n  authToken?: string;\n  messagingToken?: string;\n  appCheckToken: string | null;\n}\n\n/**\n * Helper class to get metadata that should be included with a function call.\n * @internal\n */\nexport class ContextProvider {\n  private auth: FirebaseAuthInternal | null = null;\n  private messaging: MessagingInternal | null = null;\n  private appCheck: FirebaseAppCheckInternal | null = null;\n  constructor(\n    authProvider: Provider<FirebaseAuthInternalName>,\n    messagingProvider: Provider<MessagingInternalComponentName>,\n    appCheckProvider: Provider<AppCheckInternalComponentName>\n  ) {\n    this.auth = authProvider.getImmediate({ optional: true });\n    this.messaging = messagingProvider.getImmediate({\n      optional: true\n    });\n\n    if (!this.auth) {\n      authProvider.get().then(\n        auth => (this.auth = auth),\n        () => {\n          /* get() never rejects */\n        }\n      );\n    }\n\n    if (!this.messaging) {\n      messagingProvider.get().then(\n        messaging => (this.messaging = messaging),\n        () => {\n          /* get() never rejects */\n        }\n      );\n    }\n\n    if (!this.appCheck) {\n      appCheckProvider.get().then(\n        appCheck => (this.appCheck = appCheck),\n        () => {\n          /* get() never rejects */\n        }\n      );\n    }\n  }\n\n  async getAuthToken(): Promise<string | undefined> {\n    if (!this.auth) {\n      return undefined;\n    }\n\n    try {\n      const token = await this.auth.getToken();\n      return token?.accessToken;\n    } catch (e) {\n      // If there's any error when trying to get the auth token, leave it off.\n      return undefined;\n    }\n  }\n\n  async getMessagingToken(): Promise<string | undefined> {\n    if (\n      !this.messaging ||\n      !('Notification' in self) ||\n      Notification.permission !== 'granted'\n    ) {\n      return undefined;\n    }\n\n    try {\n      return await this.messaging.getToken();\n    } catch (e) {\n      // We don't warn on this, because it usually means messaging isn't set up.\n      // console.warn('Failed to retrieve instance id token.', e);\n\n      // If there's any error when trying to get the token, leave it off.\n      return undefined;\n    }\n  }\n\n  async getAppCheckToken(\n    limitedUseAppCheckTokens?: boolean\n  ): Promise<string | null> {\n    if (this.appCheck) {\n      const result = limitedUseAppCheckTokens\n        ? await this.appCheck.getLimitedUseToken()\n        : await this.appCheck.getToken();\n      if (result.error) {\n        // Do not send the App Check header to the functions endpoint if\n        // there was an error from the App Check exchange endpoint. The App\n        // Check SDK will already have logged the error to console.\n        return null;\n      }\n      return result.token;\n    }\n    return null;\n  }\n\n  async getContext(limitedUseAppCheckTokens?: boolean): Promise<Context> {\n    const authToken = await this.getAuthToken();\n    const messagingToken = await this.getMessagingToken();\n    const appCheckToken = await this.getAppCheckToken(limitedUseAppCheckTokens);\n    return { authToken, messagingToken, appCheckToken };\n  }\n}\n", "/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { FirebaseApp, _FirebaseService } from '@firebase/app';\nimport {\n  HttpsCallable,\n  HttpsCallableResult,\n  HttpsCallableOptions\n} from './public-types';\nimport { _errorForResponse, FunctionsError } from './error';\nimport { ContextProvider } from './context';\nimport { encode, decode } from './serializer';\nimport { Provider } from '@firebase/component';\nimport { FirebaseAuthInternalName } from '@firebase/auth-interop-types';\nimport { MessagingInternalComponentName } from '@firebase/messaging-interop-types';\nimport { AppCheckInternalComponentName } from '@firebase/app-check-interop-types';\n\nexport const DEFAULT_REGION = 'us-central1';\n\n/**\n * The response to an http request.\n */\ninterface HttpResponse {\n  status: number;\n  json: HttpResponseBody | null;\n}\n/**\n * Describes the shape of the HttpResponse body.\n * It makes functions that would otherwise take {} able to access the\n * possible elements in the body more easily\n */\nexport interface HttpResponseBody {\n  data?: unknown;\n  result?: unknown;\n  error?: {\n    message?: unknown;\n    status?: unknown;\n    details?: unknown;\n  };\n}\n\ninterface CancellablePromise<T> {\n  promise: Promise<T>;\n  cancel: () => void;\n}\n\n/**\n * Returns a Promise that will be rejected after the given duration.\n * The error will be of type FunctionsError.\n *\n * @param millis Number of milliseconds to wait before rejecting.\n */\nfunction failAfter(millis: number): CancellablePromise<never> {\n  // Node timers and browser timers are fundamentally incompatible, but we\n  // don't care about the value here\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  let timer: any | null = null;\n  return {\n    promise: new Promise((_, reject) => {\n      timer = setTimeout(() => {\n        reject(new FunctionsError('deadline-exceeded', 'deadline-exceeded'));\n      }, millis);\n    }),\n    cancel: () => {\n      if (timer) {\n        clearTimeout(timer);\n      }\n    }\n  };\n}\n\n/**\n * The main class for the Firebase Functions SDK.\n * @internal\n */\nexport class FunctionsService implements _FirebaseService {\n  readonly contextProvider: ContextProvider;\n  emulatorOrigin: string | null = null;\n  cancelAllRequests: Promise<void>;\n  deleteService!: () => Promise<void>;\n  region: string;\n  customDomain: string | null;\n\n  /**\n   * Creates a new Functions service for the given app.\n   * @param app - The FirebaseApp to use.\n   */\n  constructor(\n    readonly app: FirebaseApp,\n    authProvider: Provider<FirebaseAuthInternalName>,\n    messagingProvider: Provider<MessagingInternalComponentName>,\n    appCheckProvider: Provider<AppCheckInternalComponentName>,\n    regionOrCustomDomain: string = DEFAULT_REGION,\n    readonly fetchImpl: typeof fetch\n  ) {\n    this.contextProvider = new ContextProvider(\n      authProvider,\n      messagingProvider,\n      appCheckProvider\n    );\n    // Cancels all ongoing requests when resolved.\n    this.cancelAllRequests = new Promise(resolve => {\n      this.deleteService = () => {\n        return Promise.resolve(resolve());\n      };\n    });\n\n    // Resolve the region or custom domain overload by attempting to parse it.\n    try {\n      const url = new URL(regionOrCustomDomain);\n      this.customDomain =\n        url.origin + (url.pathname === '/' ? '' : url.pathname);\n      this.region = DEFAULT_REGION;\n    } catch (e) {\n      this.customDomain = null;\n      this.region = regionOrCustomDomain;\n    }\n  }\n\n  _delete(): Promise<void> {\n    return this.deleteService();\n  }\n\n  /**\n   * Returns the URL for a callable with the given name.\n   * @param name - The name of the callable.\n   * @internal\n   */\n  _url(name: string): string {\n    const projectId = this.app.options.projectId;\n    if (this.emulatorOrigin !== null) {\n      const origin = this.emulatorOrigin;\n      return `${origin}/${projectId}/${this.region}/${name}`;\n    }\n\n    if (this.customDomain !== null) {\n      return `${this.customDomain}/${name}`;\n    }\n\n    return `https://${this.region}-${projectId}.cloudfunctions.net/${name}`;\n  }\n}\n\n/**\n * Modify this instance to communicate with the Cloud Functions emulator.\n *\n * Note: this must be called before this instance has been used to do any operations.\n *\n * @param host The emulator host (ex: localhost)\n * @param port The emulator port (ex: 5001)\n * @public\n */\nexport function connectFunctionsEmulator(\n  functionsInstance: FunctionsService,\n  host: string,\n  port: number\n): void {\n  functionsInstance.emulatorOrigin = `http://${host}:${port}`;\n}\n\n/**\n * Returns a reference to the callable https trigger with the given name.\n * @param name - The name of the trigger.\n * @public\n */\nexport function httpsCallable<RequestData, ResponseData>(\n  functionsInstance: FunctionsService,\n  name: string,\n  options?: HttpsCallableOptions\n): HttpsCallable<RequestData, ResponseData> {\n  return (data => {\n    return call(functionsInstance, name, data, options || {});\n  }) as HttpsCallable<RequestData, ResponseData>;\n}\n\n/**\n * Returns a reference to the callable https trigger with the given url.\n * @param url - The url of the trigger.\n * @public\n */\nexport function httpsCallableFromURL<RequestData, ResponseData>(\n  functionsInstance: FunctionsService,\n  url: string,\n  options?: HttpsCallableOptions\n): HttpsCallable<RequestData, ResponseData> {\n  return (data => {\n    return callAtURL(functionsInstance, url, data, options || {});\n  }) as HttpsCallable<RequestData, ResponseData>;\n}\n\n/**\n * Does an HTTP POST and returns the completed response.\n * @param url The url to post to.\n * @param body The JSON body of the post.\n * @param headers The HTTP headers to include in the request.\n * @return A Promise that will succeed when the request finishes.\n */\nasync function postJSON(\n  url: string,\n  body: unknown,\n  headers: { [key: string]: string },\n  fetchImpl: typeof fetch\n): Promise<HttpResponse> {\n  headers['Content-Type'] = 'application/json';\n\n  let response: Response;\n  try {\n    response = await fetchImpl(url, {\n      method: 'POST',\n      body: JSON.stringify(body),\n      headers\n    });\n  } catch (e) {\n    // This could be an unhandled error on the backend, or it could be a\n    // network error. There's no way to know, since an unhandled error on the\n    // backend will fail to set the proper CORS header, and thus will be\n    // treated as a network error by fetch.\n    return {\n      status: 0,\n      json: null\n    };\n  }\n  let json: HttpResponseBody | null = null;\n  try {\n    json = await response.json();\n  } catch (e) {\n    // If we fail to parse JSON, it will fail the same as an empty body.\n  }\n  return {\n    status: response.status,\n    json\n  };\n}\n\n/**\n * Calls a callable function asynchronously and returns the result.\n * @param name The name of the callable trigger.\n * @param data The data to pass as params to the function.s\n */\nfunction call(\n  functionsInstance: FunctionsService,\n  name: string,\n  data: unknown,\n  options: HttpsCallableOptions\n): Promise<HttpsCallableResult> {\n  const url = functionsInstance._url(name);\n  return callAtURL(functionsInstance, url, data, options);\n}\n\n/**\n * Calls a callable function asynchronously and returns the result.\n * @param url The url of the callable trigger.\n * @param data The data to pass as params to the function.s\n */\nasync function callAtURL(\n  functionsInstance: FunctionsService,\n  url: string,\n  data: unknown,\n  options: HttpsCallableOptions\n): Promise<HttpsCallableResult> {\n  // Encode any special types, such as dates, in the input data.\n  data = encode(data);\n  const body = { data };\n\n  // Add a header for the authToken.\n  const headers: { [key: string]: string } = {};\n  const context = await functionsInstance.contextProvider.getContext(\n    options.limitedUseAppCheckTokens\n  );\n  if (context.authToken) {\n    headers['Authorization'] = 'Bearer ' + context.authToken;\n  }\n  if (context.messagingToken) {\n    headers['Firebase-Instance-ID-Token'] = context.messagingToken;\n  }\n  if (context.appCheckToken !== null) {\n    headers['X-Firebase-AppCheck'] = context.appCheckToken;\n  }\n\n  // Default timeout to 70s, but let the options override it.\n  const timeout = options.timeout || 70000;\n\n  const failAfterHandle = failAfter(timeout);\n  const response = await Promise.race([\n    postJSON(url, body, headers, functionsInstance.fetchImpl),\n    failAfterHandle.promise,\n    functionsInstance.cancelAllRequests\n  ]);\n\n  // Always clear the failAfter timeout\n  failAfterHandle.cancel();\n\n  // If service was deleted, interrupted response throws an error.\n  if (!response) {\n    throw new FunctionsError(\n      'cancelled',\n      'Firebase Functions instance was deleted.'\n    );\n  }\n\n  // Check for an error status, regardless of http status.\n  const error = _errorForResponse(response.status, response.json);\n  if (error) {\n    throw error;\n  }\n\n  if (!response.json) {\n    throw new FunctionsError('internal', 'Response is not valid JSON object.');\n  }\n\n  let responseData = response.json.data;\n  // TODO(klimt): For right now, allow \"result\" instead of \"data\", for\n  // backwards compatibility.\n  if (typeof responseData === 'undefined') {\n    responseData = response.json.result;\n  }\n  if (typeof responseData === 'undefined') {\n    // Consider the response malformed.\n    throw new FunctionsError('internal', 'Response is missing data field.');\n  }\n\n  // Decode any special types, such as dates, in the returned data.\n  const decodedData = decode(responseData);\n\n  return { data: decodedData };\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { _registerComponent, registerVersion } from '@firebase/app';\nimport { FunctionsService } from './service';\nimport {\n  Component,\n  ComponentType,\n  ComponentContainer,\n  InstanceFactory\n} from '@firebase/component';\nimport { FUNCTIONS_TYPE } from './constants';\nimport { FirebaseAuthInternalName } from '@firebase/auth-interop-types';\nimport { AppCheckInternalComponentName } from '@firebase/app-check-interop-types';\nimport { MessagingInternalComponentName } from '@firebase/messaging-interop-types';\nimport { name, version } from '../package.json';\n\nconst AUTH_INTERNAL_NAME: FirebaseAuthInternalName = 'auth-internal';\nconst APP_CHECK_INTERNAL_NAME: AppCheckInternalComponentName =\n  'app-check-internal';\nconst MESSAGING_INTERNAL_NAME: MessagingInternalComponentName =\n  'messaging-internal';\n\nexport function registerFunctions(\n  fetchImpl: typeof fetch,\n  variant?: string\n): void {\n  const factory: InstanceFactory<'functions'> = (\n    container: ComponentContainer,\n    { instanceIdentifier: regionOrCustomDomain }\n  ) => {\n    // Dependencies\n    const app = container.getProvider('app').getImmediate();\n    const authProvider = container.getProvider(AUTH_INTERNAL_NAME);\n    const messagingProvider = container.getProvider(MESSAGING_INTERNAL_NAME);\n    const appCheckProvider = container.getProvider(APP_CHECK_INTERNAL_NAME);\n\n    // eslint-disable-next-line @typescript-eslint/no-explicit-any\n    return new FunctionsService(\n      app,\n      authProvider,\n      messagingProvider,\n      appCheckProvider,\n      regionOrCustomDomain,\n      fetchImpl\n    );\n  };\n\n  _registerComponent(\n    new Component(\n      FUNCTIONS_TYPE,\n      factory,\n      ComponentType.PUBLIC\n    ).setMultipleInstances(true)\n  );\n\n  registerVersion(name, version, variant);\n  // BUILD_TARGET will be replaced by values like esm5, esm2017, cjs5, etc during the compilation\n  registerVersion(name, version, '__BUILD_TARGET__');\n}\n", "/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { _getProvider, FirebaseApp, getApp } from '@firebase/app';\nimport { FUNCTIONS_TYPE } from './constants';\n\nimport { Provider } from '@firebase/component';\nimport { Functions, HttpsCallableOptions, HttpsCallable } from './public-types';\nimport {\n  FunctionsService,\n  DEFAULT_REGION,\n  connectFunctionsEmulator as _connectFunctionsEmulator,\n  httpsCallable as _httpsCallable,\n  httpsCallableFromURL as _httpsCallableFromURL\n} from './service';\nimport {\n  getModularInstance,\n  getDefaultEmulatorHostnameAndPort\n} from '@firebase/util';\n\nexport * from './public-types';\n\n/**\n * Returns a {@link Functions} instance for the given app.\n * @param app - The {@link @firebase/app#FirebaseApp} to use.\n * @param regionOrCustomDomain - one of:\n *   a) The region the callable functions are located in (ex: us-central1)\n *   b) A custom domain hosting the callable functions (ex: https://mydomain.com)\n * @public\n */\nexport function getFunctions(\n  app: FirebaseApp = getApp(),\n  regionOrCustomDomain: string = DEFAULT_REGION\n): Functions {\n  // Dependencies\n  const functionsProvider: Provider<'functions'> = _getProvider(\n    getModularInstance(app),\n    FUNCTIONS_TYPE\n  );\n  const functionsInstance = functionsProvider.getImmediate({\n    identifier: regionOrCustomDomain\n  });\n  const emulator = getDefaultEmulatorHostnameAndPort('functions');\n  if (emulator) {\n    connectFunctionsEmulator(functionsInstance, ...emulator);\n  }\n  return functionsInstance;\n}\n\n/**\n * Modify this instance to communicate with the Cloud Functions emulator.\n *\n * Note: this must be called before this instance has been used to do any operations.\n *\n * @param host - The emulator host (ex: localhost)\n * @param port - The emulator port (ex: 5001)\n * @public\n */\nexport function connectFunctionsEmulator(\n  functionsInstance: Functions,\n  host: string,\n  port: number\n): void {\n  _connectFunctionsEmulator(\n    getModularInstance<FunctionsService>(functionsInstance as FunctionsService),\n    host,\n    port\n  );\n}\n\n/**\n * Returns a reference to the callable HTTPS trigger with the given name.\n * @param name - The name of the trigger.\n * @public\n */\nexport function httpsCallable<RequestData = unknown, ResponseData = unknown>(\n  functionsInstance: Functions,\n  name: string,\n  options?: HttpsCallableOptions\n): HttpsCallable<RequestData, ResponseData> {\n  return _httpsCallable<RequestData, ResponseData>(\n    getModularInstance<FunctionsService>(functionsInstance as FunctionsService),\n    name,\n    options\n  );\n}\n\n/**\n * Returns a reference to the callable HTTPS trigger with the specified url.\n * @param url - The url of the trigger.\n * @public\n */\nexport function httpsCallableFromURL<\n  RequestData = unknown,\n  ResponseData = unknown\n>(\n  functionsInstance: Functions,\n  url: string,\n  options?: HttpsCallableOptions\n): HttpsCallable<RequestData, ResponseData> {\n  return _httpsCallableFromURL<RequestData, ResponseData>(\n    getModularInstance<FunctionsService>(functionsInstance as FunctionsService),\n    url,\n    options\n  );\n}\n", "/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nimport { registerFunctions } from './config';\nimport { fetch as undiciFetch } from 'undici';\n\nexport * from './api';\n\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\nregisterFunctions(undiciFetch as any, 'node');\n"], "names": ["__extends", "FirebaseError", "connectFunctionsEmulator", "httpsCallable", "httpsCallableFromURL", "_registerComponent", "Component", "registerVersion", "app", "getApp", "_get<PERSON><PERSON><PERSON>", "getModularInstance", "getDefaultEmulatorHostnameAndPort", "__spread<PERSON><PERSON>y", "_connectFunctionsEmulator", "_httpsCallable", "_httpsCallableFromURL", "undiciFetch"], "mappings": ";;;;;;;;;;AAAA;;;;;;;;;;;;;;;AAeG;AACH,IAAM,SAAS,GAAG,gDAAgD,CAAC;AACnE,IAAM,kBAAkB,GAAG,iDAAiD,CAAC;AAE7E,SAAS,SAAS;AAChB;AACA;AACA,CAAyB,EACzB,CAA6B,EAAA;IAE7B,IAAM,MAAM,GAA+B,EAAE,CAAC;AAC9C,IAAA,KAAK,IAAM,GAAG,IAAI,CAAC,EAAE;AACnB,QAAA,IAAI,CAAC,CAAC,cAAc,CAAC,GAAG,CAAC,EAAE;YACzB,MAAM,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;AACzB,SAAA;AACF,KAAA;AACD,IAAA,OAAO,MAAM,CAAC;AAChB,CAAC;AAED;;;;;AAKG;AACG,SAAU,MAAM,CAAC,IAAa,EAAA;IAClC,IAAI,IAAI,IAAI,IAAI,EAAE;AAChB,QAAA,OAAO,IAAI,CAAC;AACb,KAAA;IACD,IAAI,IAAI,YAAY,MAAM,EAAE;AAC1B,QAAA,IAAI,GAAG,IAAI,CAAC,OAAO,EAAE,CAAC;AACvB,KAAA;IACD,IAAI,OAAO,IAAI,KAAK,QAAQ,IAAI,QAAQ,CAAC,IAAI,CAAC,EAAE;;;AAG9C,QAAA,OAAO,IAAI,CAAC;AACb,KAAA;AACD,IAAA,IAAI,IAAI,KAAK,IAAI,IAAI,IAAI,KAAK,KAAK,EAAE;AACnC,QAAA,OAAO,IAAI,CAAC;AACb,KAAA;AACD,IAAA,IAAI,MAAM,CAAC,SAAS,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,iBAAiB,EAAE;AAC9D,QAAA,OAAO,IAAI,CAAC;AACb,KAAA;IACD,IAAI,IAAI,YAAY,IAAI,EAAE;AACxB,QAAA,OAAO,IAAI,CAAC,WAAW,EAAE,CAAC;AAC3B,KAAA;AACD,IAAA,IAAI,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE;AACvB,QAAA,OAAO,IAAI,CAAC,GAAG,CAAC,UAAA,CAAC,EAAA,EAAI,OAAA,MAAM,CAAC,CAAC,CAAC,CAAT,EAAS,CAAC,CAAC;AACjC,KAAA;IACD,IAAI,OAAO,IAAI,KAAK,UAAU,IAAI,OAAO,IAAI,KAAK,QAAQ,EAAE;AAC1D,QAAA,OAAO,SAAS,CAAC,IAAK,EAAE,UAAA,CAAC,EAAA,EAAI,OAAA,MAAM,CAAC,CAAC,CAAC,CAAT,EAAS,CAAC,CAAC;AACzC,KAAA;;AAED,IAAA,MAAM,IAAI,KAAK,CAAC,kCAAkC,GAAG,IAAI,CAAC,CAAC;AAC7D,CAAC;AAED;;;;;AAKG;AACG,SAAU,MAAM,CAAC,IAAa,EAAA;IAClC,IAAI,IAAI,IAAI,IAAI,EAAE;AAChB,QAAA,OAAO,IAAI,CAAC;AACb,KAAA;AACD,IAAA,IAAK,IAAmC,CAAC,OAAO,CAAC,EAAE;AACjD,QAAA,QAAS,IAAmC,CAAC,OAAO,CAAC;AACnD,YAAA,KAAK,SAAS,CAAC;;YAEf,KAAK,kBAAkB,EAAE;;;;gBAIvB,IAAM,KAAK,GAAG,MAAM,CAAE,IAAmC,CAAC,OAAO,CAAC,CAAC,CAAC;AACpE,gBAAA,IAAI,KAAK,CAAC,KAAK,CAAC,EAAE;AAChB,oBAAA,MAAM,IAAI,KAAK,CAAC,oCAAoC,GAAG,IAAI,CAAC,CAAC;AAC9D,iBAAA;AACD,gBAAA,OAAO,KAAK,CAAC;AACd,aAAA;AACD,YAAA,SAAS;AACP,gBAAA,MAAM,IAAI,KAAK,CAAC,oCAAoC,GAAG,IAAI,CAAC,CAAC;AAC9D,aAAA;AACF,SAAA;AACF,KAAA;AACD,IAAA,IAAI,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE;AACvB,QAAA,OAAO,IAAI,CAAC,GAAG,CAAC,UAAA,CAAC,EAAA,EAAI,OAAA,MAAM,CAAC,CAAC,CAAC,CAAT,EAAS,CAAC,CAAC;AACjC,KAAA;IACD,IAAI,OAAO,IAAI,KAAK,UAAU,IAAI,OAAO,IAAI,KAAK,QAAQ,EAAE;AAC1D,QAAA,OAAO,SAAS,CAAC,IAAK,EAAE,UAAA,CAAC,EAAA,EAAI,OAAA,MAAM,CAAC,CAAC,CAAC,CAAT,EAAS,CAAC,CAAC;AACzC,KAAA;;AAED,IAAA,OAAO,IAAI,CAAC;AACd;;AC5GA;;;;;;;;;;;;;;;AAeG;AAEH;;AAEG;AACI,IAAM,cAAc,GAAG,WAAW;;ACpBzC;;;;;;;;;;;;;;;AAeG;AAQH;;;;;;AAMG;AACH,IAAM,YAAY,GAA2C;AAC3D,IAAA,EAAE,EAAE,IAAI;AACR,IAAA,SAAS,EAAE,WAAW;AACtB,IAAA,OAAO,EAAE,SAAS;AAClB,IAAA,gBAAgB,EAAE,kBAAkB;AACpC,IAAA,iBAAiB,EAAE,mBAAmB;AACtC,IAAA,SAAS,EAAE,WAAW;AACtB,IAAA,cAAc,EAAE,gBAAgB;AAChC,IAAA,iBAAiB,EAAE,mBAAmB;AACtC,IAAA,eAAe,EAAE,iBAAiB;AAClC,IAAA,kBAAkB,EAAE,oBAAoB;AACxC,IAAA,mBAAmB,EAAE,qBAAqB;AAC1C,IAAA,OAAO,EAAE,SAAS;AAClB,IAAA,YAAY,EAAE,cAAc;AAC5B,IAAA,aAAa,EAAE,eAAe;AAC9B,IAAA,QAAQ,EAAE,UAAU;AACpB,IAAA,WAAW,EAAE,aAAa;AAC1B,IAAA,SAAS,EAAE,WAAW;CACvB,CAAC;AAEF;;;AAGG;AACH,IAAA,cAAA,kBAAA,UAAA,MAAA,EAAA;IAAoCA,eAAa,CAAA,cAAA,EAAA,MAAA,CAAA,CAAA;AAC/C,IAAA,SAAA,cAAA;AACE;;;AAGG;AACH,IAAA,IAAwB,EACxB,OAAgB;AAChB;;AAEG;IACM,OAAiB,EAAA;QAV5B,IAYE,KAAA,GAAA,MAAA,CAAA,IAAA,CAAA,IAAA,EAAM,EAAG,CAAA,MAAA,CAAA,cAAc,EAAI,GAAA,CAAA,CAAA,MAAA,CAAA,IAAI,CAAE,EAAE,OAAO,IAAI,EAAE,CAAC,IAClD,IAAA,CAAA;QAHU,KAAO,CAAA,OAAA,GAAP,OAAO,CAAU;;KAG3B;IACH,OAAC,cAAA,CAAA;AAAD,CAfA,CAAoCC,kBAAa,CAehD,CAAA,CAAA;AAED;;;;;;;AAOG;AACH,SAAS,iBAAiB,CAAC,MAAc,EAAA;;AAEvC,IAAA,IAAI,MAAM,IAAI,GAAG,IAAI,MAAM,GAAG,GAAG,EAAE;AACjC,QAAA,OAAO,IAAI,CAAC;AACb,KAAA;AACD,IAAA,QAAQ,MAAM;AACZ,QAAA,KAAK,CAAC;;AAEJ,YAAA,OAAO,UAAU,CAAC;AACpB,QAAA,KAAK,GAAG;AACN,YAAA,OAAO,kBAAkB,CAAC;AAC5B,QAAA,KAAK,GAAG;AACN,YAAA,OAAO,iBAAiB,CAAC;AAC3B,QAAA,KAAK,GAAG;AACN,YAAA,OAAO,mBAAmB,CAAC;AAC7B,QAAA,KAAK,GAAG;AACN,YAAA,OAAO,WAAW,CAAC;AACrB,QAAA,KAAK,GAAG;AACN,YAAA,OAAO,SAAS,CAAC;AACnB,QAAA,KAAK,GAAG;AACN,YAAA,OAAO,oBAAoB,CAAC;AAC9B,QAAA,KAAK,GAAG;AACN,YAAA,OAAO,WAAW,CAAC;AACrB,QAAA,KAAK,GAAG;AACN,YAAA,OAAO,UAAU,CAAC;AACpB,QAAA,KAAK,GAAG;AACN,YAAA,OAAO,eAAe,CAAC;AACzB,QAAA,KAAK,GAAG;AACN,YAAA,OAAO,aAAa,CAAC;AACvB,QAAA,KAAK,GAAG;AACN,YAAA,OAAO,mBAAmB,CAAC;AAE9B,KAAA;AACD,IAAA,OAAO,SAAS,CAAC;AACnB,CAAC;AAED;;AAEG;AACa,SAAA,iBAAiB,CAC/B,MAAc,EACd,QAAiC,EAAA;AAEjC,IAAA,IAAI,IAAI,GAAG,iBAAiB,CAAC,MAAM,CAAC,CAAC;;IAGrC,IAAI,WAAW,GAAW,IAAI,CAAC;IAE/B,IAAI,OAAO,GAAY,SAAS,CAAC;;IAGjC,IAAI;AACF,QAAA,IAAM,SAAS,GAAG,QAAQ,IAAI,QAAQ,CAAC,KAAK,CAAC;AAC7C,QAAA,IAAI,SAAS,EAAE;AACb,YAAA,IAAM,QAAM,GAAG,SAAS,CAAC,MAAM,CAAC;AAChC,YAAA,IAAI,OAAO,QAAM,KAAK,QAAQ,EAAE;AAC9B,gBAAA,IAAI,CAAC,YAAY,CAAC,QAAM,CAAC,EAAE;;AAEzB,oBAAA,OAAO,IAAI,cAAc,CAAC,UAAU,EAAE,UAAU,CAAC,CAAC;AACnD,iBAAA;AACD,gBAAA,IAAI,GAAG,YAAY,CAAC,QAAM,CAAC,CAAC;;;gBAI5B,WAAW,GAAG,QAAM,CAAC;AACtB,aAAA;AAED,YAAA,IAAM,OAAO,GAAG,SAAS,CAAC,OAAO,CAAC;AAClC,YAAA,IAAI,OAAO,OAAO,KAAK,QAAQ,EAAE;gBAC/B,WAAW,GAAG,OAAO,CAAC;AACvB,aAAA;AAED,YAAA,OAAO,GAAG,SAAS,CAAC,OAAO,CAAC;YAC5B,IAAI,OAAO,KAAK,SAAS,EAAE;AACzB,gBAAA,OAAO,GAAG,MAAM,CAAC,OAAO,CAAC,CAAC;AAC3B,aAAA;AACF,SAAA;AACF,KAAA;AAAC,IAAA,OAAO,CAAC,EAAE;;AAEX,KAAA;IAED,IAAI,IAAI,KAAK,IAAI,EAAE;;;;AAIjB,QAAA,OAAO,IAAI,CAAC;AACb,KAAA;IAED,OAAO,IAAI,cAAc,CAAC,IAAI,EAAE,WAAW,EAAE,OAAO,CAAC,CAAC;AACxD;;ACxKA;;;;;;;;;;;;;;;AAeG;AA0BH;;;AAGG;AACH,IAAA,eAAA,kBAAA,YAAA;AAIE,IAAA,SAAA,eAAA,CACE,YAAgD,EAChD,iBAA2D,EAC3D,gBAAyD,EAAA;QAH3D,IAoCC,KAAA,GAAA,IAAA,CAAA;QAvCO,IAAI,CAAA,IAAA,GAAgC,IAAI,CAAC;QACzC,IAAS,CAAA,SAAA,GAA6B,IAAI,CAAC;QAC3C,IAAQ,CAAA,QAAA,GAAoC,IAAI,CAAC;AAMvD,QAAA,IAAI,CAAC,IAAI,GAAG,YAAY,CAAC,YAAY,CAAC,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC,CAAC;AAC1D,QAAA,IAAI,CAAC,SAAS,GAAG,iBAAiB,CAAC,YAAY,CAAC;AAC9C,YAAA,QAAQ,EAAE,IAAI;AACf,SAAA,CAAC,CAAC;AAEH,QAAA,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE;AACd,YAAA,YAAY,CAAC,GAAG,EAAE,CAAC,IAAI,CACrB,UAAA,IAAI,EAAA,EAAI,QAAC,KAAI,CAAC,IAAI,GAAG,IAAI,EAAC,EAAA,EAC1B,YAAA;;AAEA,aAAC,CACF,CAAC;AACH,SAAA;AAED,QAAA,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;AACnB,YAAA,iBAAiB,CAAC,GAAG,EAAE,CAAC,IAAI,CAC1B,UAAA,SAAS,EAAA,EAAI,QAAC,KAAI,CAAC,SAAS,GAAG,SAAS,EAAC,EAAA,EACzC,YAAA;;AAEA,aAAC,CACF,CAAC;AACH,SAAA;AAED,QAAA,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;AAClB,YAAA,gBAAgB,CAAC,GAAG,EAAE,CAAC,IAAI,CACzB,UAAA,QAAQ,EAAA,EAAI,QAAC,KAAI,CAAC,QAAQ,GAAG,QAAQ,EAAC,EAAA,EACtC,YAAA;;AAEA,aAAC,CACF,CAAC;AACH,SAAA;KACF;AAEK,IAAA,eAAA,CAAA,SAAA,CAAA,YAAY,GAAlB,YAAA;;;;;;AACE,wBAAA,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE;AACd,4BAAA,OAAA,CAAA,CAAA,aAAO,SAAS,CAAC,CAAA;AAClB,yBAAA;;;;AAGe,wBAAA,OAAA,CAAA,CAAA,YAAM,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAA,CAAA;;AAAlC,wBAAA,KAAK,GAAG,EAA0B,CAAA,IAAA,EAAA,CAAA;AACxC,wBAAA,OAAA,CAAA,CAAA,aAAO,KAAK,KAAL,IAAA,IAAA,KAAK,uBAAL,KAAK,CAAE,WAAW,CAAC,CAAA;;;;AAG1B,wBAAA,OAAA,CAAA,CAAA,aAAO,SAAS,CAAC,CAAA;;;;;AAEpB,KAAA,CAAA;AAEK,IAAA,eAAA,CAAA,SAAA,CAAA,iBAAiB,GAAvB,YAAA;;;;;wBACE,IACE,CAAC,IAAI,CAAC,SAAS;AACf,4BAAA,EAAE,cAAc,IAAI,IAAI,CAAC;AACzB,4BAAA,YAAY,CAAC,UAAU,KAAK,SAAS,EACrC;AACA,4BAAA,OAAA,CAAA,CAAA,aAAO,SAAS,CAAC,CAAA;AAClB,yBAAA;;;;AAGQ,wBAAA,OAAA,CAAA,CAAA,YAAM,IAAI,CAAC,SAAS,CAAC,QAAQ,EAAE,CAAA,CAAA;AAAtC,oBAAA,KAAA,CAAA,EAAA,OAAA,CAAA,CAAA,aAAO,SAA+B,CAAC,CAAA;;;;;;AAMvC,wBAAA,OAAA,CAAA,CAAA,aAAO,SAAS,CAAC,CAAA;;;;;AAEpB,KAAA,CAAA;IAEK,eAAgB,CAAA,SAAA,CAAA,gBAAA,GAAtB,UACE,wBAAkC,EAAA;;;;;;6BAE9B,IAAI,CAAC,QAAQ,EAAb,OAAa,CAAA,CAAA,YAAA,CAAA,CAAA,CAAA;AACA,wBAAA,IAAA,CAAA,wBAAwB,EAAxB,OAAwB,CAAA,CAAA,YAAA,CAAA,CAAA,CAAA;AACnC,wBAAA,OAAA,CAAA,CAAA,YAAM,IAAI,CAAC,QAAQ,CAAC,kBAAkB,EAAE,CAAA,CAAA;;AAAxC,wBAAA,EAAA,GAAA,SAAwC,CAAA;;AACxC,oBAAA,KAAA,CAAA,EAAA,OAAA,CAAA,CAAA,YAAM,IAAI,CAAC,QAAQ,CAAC,QAAQ,EAAE,CAAA,CAAA;;AAA9B,wBAAA,EAAA,GAAA,SAA8B,CAAA;;;AAF5B,wBAAA,MAAM,GAEsB,EAAA,CAAA;wBAClC,IAAI,MAAM,CAAC,KAAK,EAAE;;;;AAIhB,4BAAA,OAAA,CAAA,CAAA,aAAO,IAAI,CAAC,CAAA;AACb,yBAAA;wBACD,OAAO,CAAA,CAAA,aAAA,MAAM,CAAC,KAAK,CAAC,CAAA;AAEtB,oBAAA,KAAA,CAAA,EAAA,OAAA,CAAA,CAAA,aAAO,IAAI,CAAC,CAAA;;;;AACb,KAAA,CAAA;IAEK,eAAU,CAAA,SAAA,CAAA,UAAA,GAAhB,UAAiB,wBAAkC,EAAA;;;;;AAC/B,oBAAA,KAAA,CAAA,EAAA,OAAA,CAAA,CAAA,YAAM,IAAI,CAAC,YAAY,EAAE,CAAA,CAAA;;AAArC,wBAAA,SAAS,GAAG,EAAyB,CAAA,IAAA,EAAA,CAAA;AACpB,wBAAA,OAAA,CAAA,CAAA,YAAM,IAAI,CAAC,iBAAiB,EAAE,CAAA,CAAA;;AAA/C,wBAAA,cAAc,GAAG,EAA8B,CAAA,IAAA,EAAA,CAAA;AAC/B,wBAAA,OAAA,CAAA,CAAA,YAAM,IAAI,CAAC,gBAAgB,CAAC,wBAAwB,CAAC,CAAA,CAAA;;AAArE,wBAAA,aAAa,GAAG,EAAqD,CAAA,IAAA,EAAA,CAAA;wBAC3E,OAAO,CAAA,CAAA,aAAA,EAAE,SAAS,EAAA,SAAA,EAAE,cAAc,gBAAA,EAAE,aAAa,EAAA,aAAA,EAAE,CAAC,CAAA;;;;AACrD,KAAA,CAAA;IACH,OAAC,eAAA,CAAA;AAAD,CAAC,EAAA,CAAA;;ACjJD;;;;;;;;;;;;;;;AAeG;AAgBI,IAAM,cAAc,GAAG,aAAa,CAAC;AA6B5C;;;;;AAKG;AACH,SAAS,SAAS,CAAC,MAAc,EAAA;;;;IAI/B,IAAI,KAAK,GAAe,IAAI,CAAC;IAC7B,OAAO;AACL,QAAA,OAAO,EAAE,IAAI,OAAO,CAAC,UAAC,CAAC,EAAE,MAAM,EAAA;YAC7B,KAAK,GAAG,UAAU,CAAC,YAAA;gBACjB,MAAM,CAAC,IAAI,cAAc,CAAC,mBAAmB,EAAE,mBAAmB,CAAC,CAAC,CAAC;aACtE,EAAE,MAAM,CAAC,CAAC;AACb,SAAC,CAAC;AACF,QAAA,MAAM,EAAE,YAAA;AACN,YAAA,IAAI,KAAK,EAAE;gBACT,YAAY,CAAC,KAAK,CAAC,CAAC;AACrB,aAAA;SACF;KACF,CAAC;AACJ,CAAC;AAED;;;AAGG;AACH,IAAA,gBAAA,kBAAA,YAAA;AAQE;;;AAGG;IACH,SACW,gBAAA,CAAA,GAAgB,EACzB,YAAgD,EAChD,iBAA2D,EAC3D,gBAAyD,EACzD,oBAA6C,EACpC,SAAuB,EAAA;AADhC,QAAA,IAAA,oBAAA,KAAA,KAAA,CAAA,EAAA,EAAA,oBAA6C,GAAA,cAAA,CAAA,EAAA;QAL/C,IA8BC,KAAA,GAAA,IAAA,CAAA;QA7BU,IAAG,CAAA,GAAA,GAAH,GAAG,CAAa;QAKhB,IAAS,CAAA,SAAA,GAAT,SAAS,CAAc;QAhBlC,IAAc,CAAA,cAAA,GAAkB,IAAI,CAAC;AAkBnC,QAAA,IAAI,CAAC,eAAe,GAAG,IAAI,eAAe,CACxC,YAAY,EACZ,iBAAiB,EACjB,gBAAgB,CACjB,CAAC;;AAEF,QAAA,IAAI,CAAC,iBAAiB,GAAG,IAAI,OAAO,CAAC,UAAA,OAAO,EAAA;YAC1C,KAAI,CAAC,aAAa,GAAG,YAAA;AACnB,gBAAA,OAAO,OAAO,CAAC,OAAO,CAAC,OAAO,EAAE,CAAC,CAAC;AACpC,aAAC,CAAC;AACJ,SAAC,CAAC,CAAC;;QAGH,IAAI;AACF,YAAA,IAAM,GAAG,GAAG,IAAI,GAAG,CAAC,oBAAoB,CAAC,CAAC;AAC1C,YAAA,IAAI,CAAC,YAAY;gBACf,GAAG,CAAC,MAAM,IAAI,GAAG,CAAC,QAAQ,KAAK,GAAG,GAAG,EAAE,GAAG,GAAG,CAAC,QAAQ,CAAC,CAAC;AAC1D,YAAA,IAAI,CAAC,MAAM,GAAG,cAAc,CAAC;AAC9B,SAAA;AAAC,QAAA,OAAO,CAAC,EAAE;AACV,YAAA,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC;AACzB,YAAA,IAAI,CAAC,MAAM,GAAG,oBAAoB,CAAC;AACpC,SAAA;KACF;AAED,IAAA,gBAAA,CAAA,SAAA,CAAA,OAAO,GAAP,YAAA;AACE,QAAA,OAAO,IAAI,CAAC,aAAa,EAAE,CAAC;KAC7B,CAAA;AAED;;;;AAIG;IACH,gBAAI,CAAA,SAAA,CAAA,IAAA,GAAJ,UAAK,IAAY,EAAA;QACf,IAAM,SAAS,GAAG,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,SAAS,CAAC;AAC7C,QAAA,IAAI,IAAI,CAAC,cAAc,KAAK,IAAI,EAAE;AAChC,YAAA,IAAM,QAAM,GAAG,IAAI,CAAC,cAAc,CAAC;YACnC,OAAO,EAAA,CAAA,MAAA,CAAG,QAAM,EAAA,GAAA,CAAA,CAAA,MAAA,CAAI,SAAS,EAAA,GAAA,CAAA,CAAA,MAAA,CAAI,IAAI,CAAC,MAAM,EAAA,GAAA,CAAA,CAAA,MAAA,CAAI,IAAI,CAAE,CAAC;AACxD,SAAA;AAED,QAAA,IAAI,IAAI,CAAC,YAAY,KAAK,IAAI,EAAE;AAC9B,YAAA,OAAO,UAAG,IAAI,CAAC,YAAY,EAAI,GAAA,CAAA,CAAA,MAAA,CAAA,IAAI,CAAE,CAAC;AACvC,SAAA;QAED,OAAO,UAAA,CAAA,MAAA,CAAW,IAAI,CAAC,MAAM,cAAI,SAAS,EAAA,sBAAA,CAAA,CAAA,MAAA,CAAuB,IAAI,CAAE,CAAC;KACzE,CAAA;IACH,OAAC,gBAAA,CAAA;AAAD,CAAC,EAAA,CAAA,CAAA;AAED;;;;;;;;AAQG;SACaC,0BAAwB,CACtC,iBAAmC,EACnC,IAAY,EACZ,IAAY,EAAA;IAEZ,iBAAiB,CAAC,cAAc,GAAG,SAAA,CAAA,MAAA,CAAU,IAAI,EAAI,GAAA,CAAA,CAAA,MAAA,CAAA,IAAI,CAAE,CAAC;AAC9D,CAAC;AAED;;;;AAIG;SACaC,eAAa,CAC3B,iBAAmC,EACnC,IAAY,EACZ,OAA8B,EAAA;IAE9B,QAAQ,UAAA,IAAI,EAAA;AACV,QAAA,OAAO,IAAI,CAAC,iBAAiB,EAAE,IAAI,EAAE,IAAI,EAAE,OAAO,IAAI,EAAE,CAAC,CAAC;AAC5D,KAAC,EAA8C;AACjD,CAAC;AAED;;;;AAIG;SACaC,sBAAoB,CAClC,iBAAmC,EACnC,GAAW,EACX,OAA8B,EAAA;IAE9B,QAAQ,UAAA,IAAI,EAAA;AACV,QAAA,OAAO,SAAS,CAAC,iBAAiB,EAAE,GAAG,EAAE,IAAI,EAAE,OAAO,IAAI,EAAE,CAAC,CAAC;AAChE,KAAC,EAA8C;AACjD,CAAC;AAED;;;;;;AAMG;AACH,SAAe,QAAQ,CACrB,GAAW,EACX,IAAa,EACb,OAAkC,EAClC,SAAuB,EAAA;;;;;;AAEvB,oBAAA,OAAO,CAAC,cAAc,CAAC,GAAG,kBAAkB,CAAC;;;;oBAIhC,OAAM,CAAA,CAAA,YAAA,SAAS,CAAC,GAAG,EAAE;AAC9B,4BAAA,MAAM,EAAE,MAAM;AACd,4BAAA,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC;AAC1B,4BAAA,OAAO,EAAA,OAAA;AACR,yBAAA,CAAC,CAAA,CAAA;;oBAJF,QAAQ,GAAG,SAIT,CAAC;;;;;;;;oBAMH,OAAO,CAAA,CAAA,aAAA;AACL,4BAAA,MAAM,EAAE,CAAC;AACT,4BAAA,IAAI,EAAE,IAAI;yBACX,CAAC,CAAA;;oBAEA,IAAI,GAA4B,IAAI,CAAC;;;;AAEhC,oBAAA,OAAA,CAAA,CAAA,YAAM,QAAQ,CAAC,IAAI,EAAE,CAAA,CAAA;;oBAA5B,IAAI,GAAG,SAAqB,CAAC;;;;;wBAI/B,OAAO,CAAA,CAAA,aAAA;wBACL,MAAM,EAAE,QAAQ,CAAC,MAAM;AACvB,wBAAA,IAAI,EAAA,IAAA;qBACL,CAAC,CAAA;;;;AACH,CAAA;AAED;;;;AAIG;AACH,SAAS,IAAI,CACX,iBAAmC,EACnC,IAAY,EACZ,IAAa,EACb,OAA6B,EAAA;IAE7B,IAAM,GAAG,GAAG,iBAAiB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IACzC,OAAO,SAAS,CAAC,iBAAiB,EAAE,GAAG,EAAE,IAAI,EAAE,OAAO,CAAC,CAAC;AAC1D,CAAC;AAED;;;;AAIG;AACH,SAAe,SAAS,CACtB,iBAAmC,EACnC,GAAW,EACX,IAAa,EACb,OAA6B,EAAA;;;;;;;AAG7B,oBAAA,IAAI,GAAG,MAAM,CAAC,IAAI,CAAC,CAAC;AACd,oBAAA,IAAI,GAAG,EAAE,IAAI,EAAA,IAAA,EAAE,CAAC;oBAGhB,OAAO,GAA8B,EAAE,CAAC;oBAC9B,OAAM,CAAA,CAAA,YAAA,iBAAiB,CAAC,eAAe,CAAC,UAAU,CAChE,OAAO,CAAC,wBAAwB,CACjC,CAAA,CAAA;;AAFK,oBAAA,OAAO,GAAG,EAEf,CAAA,IAAA,EAAA,CAAA;oBACD,IAAI,OAAO,CAAC,SAAS,EAAE;wBACrB,OAAO,CAAC,eAAe,CAAC,GAAG,SAAS,GAAG,OAAO,CAAC,SAAS,CAAC;AAC1D,qBAAA;oBACD,IAAI,OAAO,CAAC,cAAc,EAAE;AAC1B,wBAAA,OAAO,CAAC,4BAA4B,CAAC,GAAG,OAAO,CAAC,cAAc,CAAC;AAChE,qBAAA;AACD,oBAAA,IAAI,OAAO,CAAC,aAAa,KAAK,IAAI,EAAE;AAClC,wBAAA,OAAO,CAAC,qBAAqB,CAAC,GAAG,OAAO,CAAC,aAAa,CAAC;AACxD,qBAAA;AAGK,oBAAA,OAAO,GAAG,OAAO,CAAC,OAAO,IAAI,KAAK,CAAC;AAEnC,oBAAA,eAAe,GAAG,SAAS,CAAC,OAAO,CAAC,CAAC;oBAC1B,OAAM,CAAA,CAAA,YAAA,OAAO,CAAC,IAAI,CAAC;4BAClC,QAAQ,CAAC,GAAG,EAAE,IAAI,EAAE,OAAO,EAAE,iBAAiB,CAAC,SAAS,CAAC;AACzD,4BAAA,eAAe,CAAC,OAAO;AACvB,4BAAA,iBAAiB,CAAC,iBAAiB;AACpC,yBAAA,CAAC,CAAA,CAAA;;AAJI,oBAAA,QAAQ,GAAG,EAIf,CAAA,IAAA,EAAA,CAAA;;oBAGF,eAAe,CAAC,MAAM,EAAE,CAAC;;oBAGzB,IAAI,CAAC,QAAQ,EAAE;AACb,wBAAA,MAAM,IAAI,cAAc,CACtB,WAAW,EACX,0CAA0C,CAC3C,CAAC;AACH,qBAAA;oBAGK,KAAK,GAAG,iBAAiB,CAAC,QAAQ,CAAC,MAAM,EAAE,QAAQ,CAAC,IAAI,CAAC,CAAC;AAChE,oBAAA,IAAI,KAAK,EAAE;AACT,wBAAA,MAAM,KAAK,CAAC;AACb,qBAAA;AAED,oBAAA,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE;AAClB,wBAAA,MAAM,IAAI,cAAc,CAAC,UAAU,EAAE,oCAAoC,CAAC,CAAC;AAC5E,qBAAA;AAEG,oBAAA,YAAY,GAAG,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC;;;AAGtC,oBAAA,IAAI,OAAO,YAAY,KAAK,WAAW,EAAE;AACvC,wBAAA,YAAY,GAAG,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC;AACrC,qBAAA;AACD,oBAAA,IAAI,OAAO,YAAY,KAAK,WAAW,EAAE;;AAEvC,wBAAA,MAAM,IAAI,cAAc,CAAC,UAAU,EAAE,iCAAiC,CAAC,CAAC;AACzE,qBAAA;AAGK,oBAAA,WAAW,GAAG,MAAM,CAAC,YAAY,CAAC,CAAC;AAEzC,oBAAA,OAAA,CAAA,CAAA,aAAO,EAAE,IAAI,EAAE,WAAW,EAAE,CAAC,CAAA;;;;AAC9B;;;;;ACnVD;;;;;;;;;;;;;;;AAeG;AAgBH,IAAM,kBAAkB,GAA6B,eAAe,CAAC;AACrE,IAAM,uBAAuB,GAC3B,oBAAoB,CAAC;AACvB,IAAM,uBAAuB,GAC3B,oBAAoB,CAAC;AAEP,SAAA,iBAAiB,CAC/B,SAAuB,EACvB,OAAgB,EAAA;AAEhB,IAAA,IAAM,OAAO,GAAiC,UAC5C,SAA6B,EAC7B,EAA4C,EAAA;AAAtB,QAAA,IAAA,oBAAoB,GAAA,EAAA,CAAA,kBAAA,CAAA;;QAG1C,IAAM,GAAG,GAAG,SAAS,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC,YAAY,EAAE,CAAC;QACxD,IAAM,YAAY,GAAG,SAAS,CAAC,WAAW,CAAC,kBAAkB,CAAC,CAAC;QAC/D,IAAM,iBAAiB,GAAG,SAAS,CAAC,WAAW,CAAC,uBAAuB,CAAC,CAAC;QACzE,IAAM,gBAAgB,GAAG,SAAS,CAAC,WAAW,CAAC,uBAAuB,CAAC,CAAC;;AAGxE,QAAA,OAAO,IAAI,gBAAgB,CACzB,GAAG,EACH,YAAY,EACZ,iBAAiB,EACjB,gBAAgB,EAChB,oBAAoB,EACpB,SAAS,CACV,CAAC;AACJ,KAAC,CAAC;AAEF,IAAAC,sBAAkB,CAChB,IAAIC,mBAAS,CACX,cAAc,EACd,OAAO,EAER,QAAA,4BAAA,CAAC,oBAAoB,CAAC,IAAI,CAAC,CAC7B,CAAC;AAEF,IAAAC,mBAAe,CAAC,IAAI,EAAE,OAAO,EAAE,OAAO,CAAC,CAAC;;AAExC,IAAAA,mBAAe,CAAC,IAAI,EAAE,OAAO,EAAE,MAAkB,CAAC,CAAC;AACrD;;ACzEA;;;;;;;;;;;;;;;AAeG;AAqBH;;;;;;;AAOG;AACa,SAAA,YAAY,CAC1BC,KAA2B,EAC3B,oBAA6C,EAAA;IAD7C,IAAAA,KAAA,KAAA,KAAA,CAAA,EAAA,EAAAA,KAAmB,GAAAC,UAAM,EAAE,CAAA,EAAA;AAC3B,IAAA,IAAA,oBAAA,KAAA,KAAA,CAAA,EAAA,EAAA,oBAA6C,GAAA,cAAA,CAAA,EAAA;;IAG7C,IAAM,iBAAiB,GAA0BC,gBAAY,CAC3DC,uBAAkB,CAACH,KAAG,CAAC,EACvB,cAAc,CACf,CAAC;AACF,IAAA,IAAM,iBAAiB,GAAG,iBAAiB,CAAC,YAAY,CAAC;AACvD,QAAA,UAAU,EAAE,oBAAoB;AACjC,KAAA,CAAC,CAAC;AACH,IAAA,IAAM,QAAQ,GAAGI,sCAAiC,CAAC,WAAW,CAAC,CAAC;AAChE,IAAA,IAAI,QAAQ,EAAE;AACZ,QAAA,wBAAwB,CAAC,KAAA,CAAA,KAAA,CAAA,EAAAC,mBAAA,CAAA,CAAA,iBAAiB,CAAK,EAAA,QAAQ,EAAE,KAAA,CAAA,CAAA,CAAA;AAC1D,KAAA;AACD,IAAA,OAAO,iBAAiB,CAAC;AAC3B,CAAC;AAED;;;;;;;;AAQG;SACa,wBAAwB,CACtC,iBAA4B,EAC5B,IAAY,EACZ,IAAY,EAAA;IAEZC,0BAAyB,CACvBH,uBAAkB,CAAmB,iBAAqC,CAAC,EAC3E,IAAI,EACJ,IAAI,CACL,CAAC;AACJ,CAAC;AAED;;;;AAIG;SACa,aAAa,CAC3B,iBAA4B,EAC5B,IAAY,EACZ,OAA8B,EAAA;IAE9B,OAAOI,eAAc,CACnBJ,uBAAkB,CAAmB,iBAAqC,CAAC,EAC3E,IAAI,EACJ,OAAO,CACR,CAAC;AACJ,CAAC;AAED;;;;AAIG;SACa,oBAAoB,CAIlC,iBAA4B,EAC5B,GAAW,EACX,OAA8B,EAAA;IAE9B,OAAOK,sBAAqB,CAC1BL,uBAAkB,CAAmB,iBAAqC,CAAC,EAC3E,GAAG,EACH,OAAO,CACR,CAAC;AACJ;;ACvHA;;;;;;;;;;;;;;;AAeG;AAMH;AACA,iBAAiB,CAACM,YAAkB,EAAE,MAAM,CAAC;;;;;;;"}