<template>
  <div
    v-if="block"
    :class="['preview-block', `preview-block-${block.type}`]"
    :style="block.styles"
    @click.stop="handleClick"
  >
    <!-- Label Block -->
    <span v-if="block.type === 'label'">{{ block.content || 'Label' }}</span>

    <!-- But<PERSON> Block -->
    <button v-else-if="block.type === 'button'" @click.stop="triggerEvent('onClick')">
      {{ block.content || 'Button' }}
    </button>

    <!-- Input Block -->
    <input
      v-else-if="block.type === 'input'"
      :type="block.inputType || 'text'"
      :placeholder="block.placeholder || 'Input'"
      :value="inputValue"
      @input="updateInputValue"
      @change="triggerEvent('onChange')"
    />

    <!-- Image Display Block -->
    <img
        v-else-if="block.type === 'image_display'"
        :src="block.src || 'https://via.placeholder.com/150'"
        :alt="block.altText || 'Image preview'"
        class="preview-image"
    />

    <!-- Container Block (Recursive) -->
    <div v-else-if="block.type === 'container'" class="preview-container-children">
      <BlockRenderer
        v-for="childBlock in block.children"
        :key="childBlock.id"
        :block="childBlock"
        @block-event="emitBlockEvent"
      />
    </div>

    <!-- Fallback for unknown block types -->
    <span v-else>Unknown block type: {{ block.type }}</span>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue';

const props = defineProps({
  block: {
    type: Object,
    required: true,
  },
});

const emit = defineEmits(['block-event']);

// Specific state for input blocks
const inputValue = ref(props.block.type === 'input' ? (props.block.defaultValue || '') : '');

function updateInputValue(event) {
  if (props.block.type === 'input') {
    inputValue.value = event.target.value;
  }
}

function handleClick() {
  // General click for any block, could be used for selection in the future
  // console.log('Block clicked:', props.block.id);
  // For now, only buttons have specific onClick logic via triggerEvent
}

function triggerEvent(eventName) {
  // console.log(`Event triggered: ${eventName} on block ${props.block.id}`);
  const eventDetails = {
    blockId: props.block.id,
    blockType: props.block.type,
    eventName: eventName,
    // value: eventName === 'onChange' && props.block.type === 'input' ? inputValue.value : undefined,
    // For onChange, we might want to pass the value. For onClick, it's just the event.
  };
  if (props.block.events && props.block.events[eventName]) {
    eventDetails.handlerFunction = props.block.events[eventName];
  }
  emit('block-event', eventDetails);
}

// To allow nested components to bubble up events
function emitBlockEvent(eventDetails) {
  emit('block-event', eventDetails);
}

</script>

<style scoped>
.preview-block {
  padding: 8px;
  margin: 5px;
  border: 1px solid #ddd;
  border-radius: 4px;
  background-color: #f9f9f9;
  box-sizing: border-box;
  /* Add some basic styling to make blocks visible */
  min-height: 30px;
  display: flex; /* Helps with alignment for some elements */
  flex-direction: column; /* Default for containers */
  align-items: flex-start; /* Align items to the start by default */
}

.preview-block-label {
  background-color: #e9f5ff;
  border-color: #cce0ff;
}

.preview-block-button {
  background-color: #e2e3e5;
  border-color: #d6d8db;
  align-items: center; /* Center button text */
}
.preview-block-button button {
  padding: 5px 10px;
  background-color: #007bff;
  color: white;
  border: none;
  border-radius: 3px;
  cursor: pointer;
}
.preview-block-button button:hover {
  background-color: #0056b3;
}

.preview-block-input {
  background-color: #fff;
  border-color: #ccc;
}
.preview-block-input input {
  padding: 5px;
  border: 1px solid #ccc;
  border-radius: 3px;
  width: 100%; /* Make input take full width of its block */
  box-sizing: border-box;
}

.preview-block-image_display {
    padding: 0; /* Remove padding for images to avoid double borders if image has one */
}
.preview-image {
    max-width: 100%;
    height: auto;
    display: block;
    border-radius: 4px; /* Match block radius */
}

.preview-block-container {
  background-color: #f0f0f0;
  border-color: #c0c0c0;
  padding: 10px; /* More padding for containers */
}
.preview-container-children {
  width: 100%;
}

/* Add more specific styles as needed */
</style>
