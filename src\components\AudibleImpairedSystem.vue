<template>
  <div class="bg-white dark:bg-gray-800 shadow-lg rounded-lg p-4 max-h-60 overflow-y-auto">
    <div class="flex justify-between items-center mb-3">
      <h3 class="font-semibold text-gray-900 dark:text-white">Live Transcription</h3>
      <div class="flex items-center space-x-2">
        <select
          v-model="selectedLanguage"
          class="text-sm border border-gray-300 dark:border-gray-600 rounded px-2 py-1 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
        >
          <option value="en-US">English</option>
          <option value="es-ES">Spanish</option>
          <option value="fr-FR">French</option>
          <option value="de-DE">German</option>
          <option value="zh-CN">Chinese</option>
          <option value="ja-JP">Japanese</option>
          <option value="ko-KR">Korean</option>
          <option value="hi-IN">Hindi</option>
        </select>
        <button
          @click="toggleTranscription"
          class="p-1 rounded"
          :class="isTranscribing ? 'bg-red-100 text-red-600 dark:bg-red-900 dark:text-red-300' : 'bg-green-100 text-green-600 dark:bg-green-900 dark:text-green-300'"
        >
          <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path v-if="isTranscribing" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            <path v-if="isTranscribing" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 10a1 1 0 011-1h4a1 1 0 011 1v4a1 1 0 01-1 1h-4a1 1 0 01-1-1v-4z" />
            <path v-else stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15.536 8.464a5 5 0 010 7.072m2.828-9.9a9 9 0 010 12.728M5.586 15H4a1 1 0 01-1-1v-4a1 1 0 011-1h1.586l4.707-4.707C10.923 3.663 12 4.109 12 5v14c0 .891-1.077 1.337-1.707.707L5.586 15z" />
          </svg>
        </button>
      </div>
    </div>

    <div class="space-y-2">
      <div
        v-for="(transcript, index) in transcripts"
        :key="index"
        class="p-2 rounded-lg mb-4"
        :class="transcript.userId === currentUserId ? 'bg-blue-50 dark:bg-blue-900' : 'bg-gray-50 dark:bg-gray-700'"
      >
        <div class="flex items-start">
          <div class="font-semibold text-sm text-gray-900 dark:text-white">
            {{ transcript.userName }}:
          </div>
          <div class="ml-2 text-sm text-gray-700 dark:text-gray-300">
            {{ transcript.text }}
          </div>
        </div>
        <div class="text-xs text-gray-500 dark:text-gray-400 mt-1">
          {{ formatTime(transcript.timestamp) }}
        </div>

        <!-- Translated version (simulated) -->
        <div v-if="transcript.language && transcript.language !== selectedLanguage" class="mt-4 p-2 bg-gray-100 dark:bg-gray-600 rounded">
          <div class="text-xs text-gray-500 dark:text-gray-400 mb-1">
            Translated to {{ getLanguageName(selectedLanguage) }}:
          </div>
          <div class="text-sm text-gray-700 dark:text-gray-300">
            {{ getTranslatedText(transcript.text, transcript.language, selectedLanguage) }}
          </div>
        </div>
      </div>

      <div v-if="isTranscribing && currentTranscript" class="p-2 rounded-lg bg-yellow-50 dark:bg-yellow-900 animate-pulse">
        <div class="flex items-start">
          <div class="font-semibold text-sm text-gray-900 dark:text-white">
            {{ currentSpeaker }}:
          </div>
          <div class="ml-2 text-sm text-gray-700 dark:text-gray-300">
            {{ currentTranscript }}
          </div>
        </div>
      </div>

      <div v-if="transcripts.length === 0 && !currentTranscript" class="text-center text-gray-500 dark:text-gray-400 py-4">
        No transcriptions yet. Start speaking to see transcriptions.
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onBeforeUnmount, watch, defineProps } from 'vue'
import { db } from '../firebase/config'
import { collection, addDoc, onSnapshot, serverTimestamp, query, orderBy, limit } from 'firebase/firestore'

const props = defineProps({
  meetingId: {
    type: String,
    required: true
  },
  currentUserId: {
    type: String,
    required: true
  },
  currentUserName: {
    type: String,
    required: true
  },
  localStream: {
    type: MediaStream,
    required: true
  }
})

// State
const isTranscribing = ref(false)
const selectedLanguage = ref('en-US')
const transcripts = ref([])
const currentTranscript = ref('')
const currentSpeaker = ref('')
let recognition = null
let transcriptsUnsubscribe = null

// Initialize speech recognition
const initSpeechRecognition = () => {
  try {
    // Check if browser supports speech recognition
    if (!('webkitSpeechRecognition' in window) && !('SpeechRecognition' in window)) {
      console.error('Speech recognition not supported in this browser')
      return false
    }

    // Create speech recognition instance
    const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition
    recognition = new SpeechRecognition()

    // Configure recognition
    recognition.continuous = true
    recognition.interimResults = true
    recognition.lang = selectedLanguage.value

    // Set up event handlers
    recognition.onresult = handleRecognitionResult
    recognition.onerror = handleRecognitionError
    recognition.onend = handleRecognitionEnd

    return true
  } catch (error) {
    console.error('Error initializing speech recognition:', error)
    return false
  }
}

// Handle recognition results
const handleRecognitionResult = (event) => {
  const result = event.results[event.results.length - 1]
  const transcript = result[0].transcript

  // Update current transcript
  currentTranscript.value = transcript
  currentSpeaker.value = props.currentUserName

  // If this is a final result, save it to Firestore
  if (result.isFinal) {
    saveTranscript(transcript)
    currentTranscript.value = ''
  }
}

// Handle recognition errors
const handleRecognitionError = (event) => {
  console.error('Speech recognition error:', event.error)

  if (isTranscribing.value) {
    // Try to restart recognition
    try {
      recognition.stop()
      setTimeout(() => {
        if (isTranscribing.value) {
          recognition.start()
        }
      }, 1000)
    } catch (error) {
      console.error('Error restarting speech recognition:', error)
    }
  }
}

// Handle recognition end
const handleRecognitionEnd = () => {
  // Restart recognition if still transcribing
  if (isTranscribing.value) {
    try {
      recognition.start()
    } catch (error) {
      console.error('Error restarting speech recognition:', error)
      isTranscribing.value = false
    }
  }
}

// Save transcript to Firestore
const saveTranscript = async (text) => {
  try {
    await addDoc(collection(db, 'meetings', props.meetingId, 'transcripts'), {
      userId: props.currentUserId,
      userName: props.currentUserName,
      text,
      language: selectedLanguage.value,
      timestamp: serverTimestamp()
    })
  } catch (error) {
    console.error('Error saving transcript:', error)
  }
}

// Toggle transcription
const toggleTranscription = () => {
  if (!recognition && !initSpeechRecognition()) {
    alert('Speech recognition is not supported in your browser')
    return
  }

  isTranscribing.value = !isTranscribing.value

  if (isTranscribing.value) {
    try {
      recognition.lang = selectedLanguage.value
      recognition.start()
    } catch (error) {
      console.error('Error starting speech recognition:', error)
      isTranscribing.value = false
    }
  } else {
    try {
      recognition.stop()
      currentTranscript.value = ''
    } catch (error) {
      console.error('Error stopping speech recognition:', error)
    }
  }
}

// Format timestamp
const formatTime = (timestamp) => {
  if (!timestamp) return ''

  const date = timestamp.toDate ? timestamp.toDate() : new Date(timestamp)
  return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit', second: '2-digit' })
}

// Get language name from code
const getLanguageName = (langCode) => {
  const languages = {
    'en-US': 'English',
    'es-ES': 'Spanish',
    'fr-FR': 'French',
    'de-DE': 'German',
    'zh-CN': 'Chinese',
    'ja-JP': 'Japanese',
    'ko-KR': 'Korean',
    'hi-IN': 'Hindi'
  }

  return languages[langCode] || langCode
}

// Simulate translation (in a real app, this would use a translation API)
const getTranslatedText = (text, fromLang, toLang) => {
  // This is just a simulation - in a real app, you would use a translation API
  return `[Translated from ${getLanguageName(fromLang)}] ${text}`
}

// Listen for language changes
watch(selectedLanguage, (newLanguage) => {
  if (recognition && isTranscribing.value) {
    try {
      // Stop current recognition
      recognition.stop()
      
      // Wait for the stop event before starting again
      recognition.onend = () => {
        recognition.lang = newLanguage
        recognition.start()
      }
    } catch (error) {
      console.error('Error updating recognition language:', error)
    }
  }
})

// Listen for transcripts
const listenForTranscripts = () => {
  const transcriptsRef = collection(db, 'meetings', props.meetingId, 'transcripts')
  const transcriptsQuery = query(transcriptsRef, orderBy('timestamp', 'desc'), limit(50))

  transcriptsUnsubscribe = onSnapshot(transcriptsQuery, (snapshot) => {
    const newTranscripts = []

    snapshot.forEach(doc => {
      newTranscripts.push({
        id: doc.id,
        ...doc.data()
      })
    })

    // Reverse to show oldest first
    transcripts.value = newTranscripts.reverse()
  })
}

// Initialize component
onMounted(() => {
  initSpeechRecognition()
  listenForTranscripts()
})

// Clean up
onBeforeUnmount(() => {
  if (recognition) {
    try {
      recognition.stop()
    } catch (error) {
      console.error('Error stopping speech recognition:', error)
    }
  }

  if (transcriptsUnsubscribe) {
    transcriptsUnsubscribe()
  }
})
</script>
