<template>
  <MeetingInvitePopup
    v-if="showInvitePopup"
    :visible="showInvitePopup"
    :inviteLink="inviteLink"
    :meetingId="meetingId"
    :meetingName="meetingName"
    :hostName="userName"
    :linkSharingPolicy="linkSharingPolicy"
    @close="showInvitePopup = false"
    @linkShared="onLinkShared"
  />
  <UsernamePromptPopup
    v-if="showUsernamePrompt"
    :visible="showUsernamePrompt"
    :meetingInfo="{
      name: meetingName,
      hostName: getHostName(),
      participantCount: meetingStore.participants.length
    }"
    :sharedBy="route.query.sharedBy"
    :joinViaLink="!!route.query.joinViaLink"
    @submit="onUsernameSubmit"
    @cancel="onUsernameCancel"
  />
  <div v-if="meetingConfigStore.isLoading || meetingStore.connectionStatus === 'connecting'" class="h-screen flex items-center justify-center bg-gray-100 dark:bg-gray-900">
    <div class="text-center">
      <svg class="mx-auto h-12 w-12 text-primary animate-spin" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
        <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
        <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
      </svg>
      <p class="mt-4 text-lg font-medium text-gray-700 dark:text-gray-300">
        {{ meetingStore.connectionStatus === 'connecting' ? 'Connecting to meeting...' : 'Loading meeting details...' }}
      </p>
    </div>
  </div>
  <div v-else-if="meetingConfigStore.error" class="h-screen flex items-center justify-center bg-gray-100 dark:bg-gray-900">
     <div class="text-center p-6 bg-white dark:bg-gray-800 rounded-lg shadow-xl">
        <i class="fas fa-exclamation-triangle text-4xl text-red-500 mb-4"></i>
        <h2 class="text-2xl font-semibold text-gray-900 dark:text-white mb-2">Error Loading Meeting</h2>
        <p class="text-gray-600 dark:text-gray-300 mb-6">{{ meetingConfigStore.error }}</p>
        <button @click="router.push('/')" class="btn btn-primary">Go to Homepage</button>
    </div>
  </div>
  <div v-else class="h-screen flex flex-col bg-gray-100 dark:bg-gray-900">
    <!-- Meeting header -->
    <div class="sticky top-0 z-30 bg-white dark:bg-gray-800 shadow-md px-2 sm:px-4 py-2 flex flex-col sm:flex-row justify-between items-center gap-2 border-b border-gray-200 dark:border-gray-700">
      <div class="flex items-center">
        <!-- Meeting Name from Store -->
        <h1 class="text-xl font-semibold text-gray-900 dark:text-white">{{ meetingName }}</h1>
        <div class="ml-4 px-2 py-1 bg-gray-100 dark:bg-gray-700 rounded text-sm text-gray-600 dark:text-gray-300">
          <!-- Meeting ID from Store/Route -->
          {{ meetingId }}
          <button
            @click="copyMeetingId"
            class="ml-1 text-primary hover:text-primary-dark"
            title="Copy meeting ID"
          >
            <i class="fas fa-copy"></i>
          </button>
        </div>
        <div v-if="isAdmin" class="ml-4 flex items-center gap-2">
          <label class="text-xs text-gray-600 dark:text-gray-300">Who can share invite?</label>
          <select v-model="linkSharingPolicy" @change="updateLinkSharingPolicy" class="text-xs p-1 rounded border bg-gray-50 dark:bg-gray-700">
            <option value="host">Host Only</option>
            <option value="all">All Members</option>
          </select>
        </div>
      </div>
      <div v-if="isAdmin && joinTracking.length" class="w-full mt-2">
        <div class="text-xs text-gray-600 dark:text-gray-300 font-semibold mb-1">Join Link Activity</div>
        <ul class="text-xs max-h-24 overflow-y-auto">
          <li v-for="(ev, idx) in joinTracking" :key="idx" class="mb-1">
            <span class="font-bold">{{ getParticipantName(ev.sharedBy) }}</span> shared link &rarr; <span class="font-bold">{{ ev.joinedName }}</span> joined ({{ formatTime(ev.joinedAt) }})
          </li>
        </ul>
      </div>
      <div class="flex items-center">
        <span class="text-sm text-gray-600 dark:text-gray-300 mr-2">
          <!-- Participants count from meetingStore -->
          {{ meetingStore.participants.length }} participant{{ meetingStore.participants.length !== 1 ? 's' : '' }}
        </span>
        <button
          @click="showLeaveMeetingDialog"
          class="btn btn-outline text-red-600 hover:bg-red-50 dark:hover:bg-red-900 dark:text-red-400"
        >
          Leave
        </button>
      </div>
    </div>

    <!-- Meeting content -->
    <div class="flex-1 flex overflow-hidden">
      <!-- Video grid -->
      <div class="flex-1 p-4 overflow-auto">
        <!-- Meeting Title -->
        <div class="text-center mb-6">
          <h1 class="text-2xl font-bold text-gray-900 dark:text-white">
            {{ meetingName || 'MVP DEVELOPMENT MEETING' }}
          </h1>
        </div>

        <!-- Main video layout: Host on left, participants grid on right -->
        <div class="flex flex-col lg:flex-row gap-6 lg:gap-8 h-full max-h-[600px]">
          <!-- Host video section (left side) -->
          <div class="flex-shrink-0 w-full lg:w-2/5">
            <div class="relative bg-black rounded-2xl overflow-hidden shadow-xl h-full min-h-[300px] lg:min-h-[400px] border-2 border-gray-300 dark:border-gray-600">
              <!-- Host video or avatar -->
              <video
                v-if="!meetingStore.isCameraOff"
                ref="localVideo"
                :muted="true"
                autoplay
                playsinline
                class="w-full h-full object-cover"
              ></video>
              <div v-else class="w-full h-full flex flex-col items-center justify-center bg-black">
                <!-- Host avatar with broadcasting icon -->
                <div class="relative mb-6">
                  <svg class="w-24 h-24 text-white" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z"/>
                  </svg>
                  <!-- Broadcasting waves -->
                  <div class="absolute inset-0 flex items-center justify-center">
                    <div class="absolute w-32 h-32 border-2 border-white opacity-30 rounded-full animate-ping"></div>
                    <div class="absolute w-40 h-40 border-2 border-white opacity-20 rounded-full animate-ping" style="animation-delay: 0.5s;"></div>
                    <div class="absolute w-48 h-48 border-2 border-white opacity-10 rounded-full animate-ping" style="animation-delay: 1s;"></div>
                  </div>
                </div>
              </div>
              <!-- Mic status indicator -->
              <div v-if="meetingStore.isMicMuted" class="absolute top-4 right-4 bg-red-600 rounded-full p-2">
                <i class="fas fa-microphone-slash text-white"></i>
              </div>
            </div>
            <!-- Host name and status -->
            <div class="text-center mt-4">
              <div class="text-xl font-bold text-gray-900 dark:text-white uppercase tracking-wide">
                {{ userName || 'KATHLEEN JOHNSON' }}
              </div>
              <div class="text-base font-bold text-gray-900 dark:text-white mt-1">
                HOST
              </div>
            </div>
          </div>

          <!-- Participants grid (right side) -->
          <div class="flex-1">
            <div class="grid grid-cols-2 sm:grid-cols-3 gap-3 lg:gap-4 h-full auto-rows-fr">
              <!-- Remote participants -->
              <div
                v-for="(stream, peerId) in meetingStore.remoteStreams"
                :key="peerId"
                class="flex flex-col"
              >
                <div class="relative bg-black rounded-xl overflow-hidden shadow-lg aspect-square border border-gray-300 dark:border-gray-600">
                  <video
                    v-if="!isParticipantVideoOff(peerId)"
                    :ref="el => { if (el) remoteVideoRefs[peerId] = el; if (el && stream) el.srcObject = stream; }"
                    autoplay
                    playsinline
                    class="w-full h-full object-cover"
                  ></video>
                  <div v-else class="w-full h-full flex items-center justify-center bg-gray-800">
                    <svg class="w-16 h-16 text-gray-400" fill="currentColor" viewBox="0 0 24 24">
                      <path d="M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z"/>
                    </svg>
                  </div>
                  <!-- Mute indicator -->
                  <div v-if="isParticipantMuted(peerId)" class="absolute top-2 right-2 bg-red-600 rounded-full p-1.5">
                    <i class="fas fa-microphone-slash text-white text-xs"></i>
                  </div>
                  <!-- Admin controls -->
                  <div v-if="isAdmin" class="absolute top-2 left-2">
                    <button
                      @click="showRemoveParticipantDialog(peerId)"
                      class="bg-red-600 text-white p-1.5 rounded-full hover:bg-red-700 text-xs"
                      title="Remove participant"
                    >
                      <i class="fas fa-user-times"></i>
                    </button>
                  </div>
                </div>
                <!-- Participant name -->
                <div class="text-center mt-2">
                  <div class="text-sm font-semibold text-gray-900 dark:text-white truncate">
                    {{ getParticipantName(peerId) }}
                  </div>
                </div>
              </div>

              <!-- Empty slots for up to 9 participants -->
              <div
                v-for="n in Math.max(0, 9 - Object.keys(meetingStore.remoteStreams).length)"
                :key="`empty-${n}`"
                class="flex flex-col"
              >
                <div class="relative bg-gray-800 rounded-xl overflow-hidden shadow-lg aspect-square border border-gray-300 dark:border-gray-600 flex items-center justify-center">
                  <svg class="w-12 h-12 text-gray-500" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z"/>
                  </svg>
                </div>
                <div class="text-center mt-2">
                  <div class="text-sm font-semibold text-gray-900 dark:text-white">
                    &lt;name&gt;
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Dynamic Extension Rendering Area -->
        <div v-if="activeMeetingExtensions.length > 0" class="mt-4">
            <h3 class="text-lg font-semibold text-gray-800 dark:text-white mb-2">Active Extensions</h3>
            <div v-for="ext in activeMeetingExtensions" :key="ext.id" class="p-2 border rounded mb-2 bg-gray-50 dark:bg-gray-800">
                <component
                    :is="resolveExtensionComponent(ext.entryPoint || ext.id)"
                    :config="ext.config"
                    :meetingId="meetingId"
                    :userId="userId"
                    :isAdmin="isAdmin"
                    :meetingContext="{ meetingId, userId, userName: userName.value, meetingConfigStore, meetingStore }"
                     />
                 <!-- Fallback if component can't be resolved -->
                <div v-if="!resolveExtensionComponent(ext.entryPoint || ext.id)">
                    <p class="font-semibold">{{ ext.name }}</p>
                    <p class="text-sm text-red-500">Cannot load UI for this extension (entry point: {{ext.entryPoint || 'not defined'}}).</p>
                </div>
            </div>
        </div>

      </div>

      <!-- Join Requests Panel (Admin only) - Logic remains similar -->
      <div
        v-if="isAdmin && showJoinRequestsPanel && joinRequests.length > 0"
        class="w-80 bg-white dark:bg-gray-800 shadow-lg flex flex-col border-l border-gray-200 dark:border-gray-700"
      >
        <div class="p-3 border-b border-gray-200 dark:border-gray-700 flex justify-between items-center">
          <h2 class="font-semibold text-gray-900 dark:text-white">Join Requests</h2>
          <button @click="toggleJoinRequestsPanel" class="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200">
            <i class="fas fa-times"></i>
          </button>
        </div>
        <div class="flex-1 p-3 overflow-y-auto">
          <div v-for="request in joinRequests" :key="request.id" class="mb-4 p-3 bg-gray-100 dark:bg-gray-700 rounded-lg">
            <div class="font-semibold mb-1 text-gray-900 dark:text-white">{{ request.userName }}</div>
            <div class="text-sm text-gray-600 dark:text-gray-300 mb-2">Requested at {{ formatTime(request.timestamp) }}</div>
            <div class="flex space-x-2">
              <button @click="approveJoinRequest(request.id)" class="btn btn-primary text-sm py-1 px-3">Approve</button>
              <button @click="denyJoinRequest(request.id)" class="btn btn-outline text-sm py-1 px-3">Deny</button>
            </div>
          </div>
        </div>
      </div>

      <!-- Breakout Rooms Panel - Now driven by activeCoreFeatures.breakoutRooms -->
      <div v-if="activeCoreFeatures.breakoutRooms" class="w-80 bg-white dark:bg-gray-800 shadow-lg flex flex-col border-l border-gray-200 dark:border-gray-700">
        <div class="p-3 border-b border-gray-200 dark:border-gray-700 flex justify-between items-center">
          <h2 class="font-semibold text-gray-900 dark:text-white">Breakout Rooms</h2>
          <button @click="meetingConfigStore.toggleFeature('breakoutRooms')" class="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200">
            <i class="fas fa-times"></i>
          </button>
        </div>
        <div class="flex-1">
          <BreakoutRooms :meetingId="meetingId" :userId="userId" :isHost="isAdmin" :participants="meetingStore.participants" />
        </div>
      </div>

      <!-- Virtual Background Panel - Driven by activeCoreFeatures.virtualBackground -->
      <div v-if="activeCoreFeatures.virtualBackground" class="w-80 bg-white dark:bg-gray-800 shadow-lg flex flex-col border-l border-gray-200 dark:border-gray-700">
        <div class="p-3 border-b border-gray-200 dark:border-gray-700 flex justify-between items-center">
          <h2 class="font-semibold text-gray-900 dark:text-white">Virtual Background</h2>
          <button @click="meetingConfigStore.toggleFeature('virtualBackground')" class="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200">
            <i class="fas fa-times"></i>
          </button>
        </div>
        <div class="flex-1">
          <VirtualBackgroundSelector :stream="meetingStore.localStream" :currentBackground="'none'" @apply="applyVirtualBackground" @cancel="meetingConfigStore.toggleFeature('virtualBackground')" />
        </div>
      </div>

      <!-- Whiteboard Panel - Driven by activeCoreFeatures.whiteboard -->
      <div v-if="activeCoreFeatures.whiteboard" class="w-80 bg-white dark:bg-gray-800 shadow-lg flex flex-col border-l border-gray-200 dark:border-gray-700">
        <div class="p-3 border-b border-gray-200 dark:border-gray-700 flex justify-between items-center">
          <h2 class="font-semibold text-gray-900 dark:text-white">Whiteboard</h2>
          <button @click="toggleWhiteboardFeature" class="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200">
            <i class="fas fa-times"></i>
          </button>
        </div>
        <div class="flex-1">
          <Whiteboard :meetingId="meetingId" :userId="userId" :isHost="isAdmin" />
        </div>
      </div>

      <!-- Link Sharing Panel -->
      <LinkSharingPanel
        v-if="showLinkSharingPanel"
        :activities="meetingConfigStore.getLinkSharingActivities"
        :linkSharingPolicy="linkSharingPolicy"
        :participants="meetingStore.participants"
        @close="showLinkSharingPanel = false"
        @updatePolicy="updateLinkSharingPolicy"
        @showInvite="showInvitePopup = true"
      />

      <!-- Chat panel - Driven by local isChatOpen, but feature itself by activeCoreFeatures.chat -->
      <div v-if="isChatOpen && activeCoreFeatures.chat" class="w-80 bg-white dark:bg-gray-800 shadow-lg flex flex-col border-l border-gray-200 dark:border-gray-700">
        <div class="p-3 border-b border-gray-200 dark:border-gray-700 flex justify-between items-center">
          <h2 class="font-semibold text-gray-900 dark:text-white">Chat</h2>
          <button @click="isChatOpen = false" class="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200">
            <i class="fas fa-times"></i>
          </button>
        </div>
        <div class="flex-1 p-3 overflow-y-auto" ref="chatMessagesContainer">
          <!-- Messages from meetingStore -->
          <div v-for="message in meetingStore.messages" :key="message.id" class="mb-3">
            <div class="flex items-start">
              <div class="flex-1 bg-gray-100 dark:bg-gray-700 rounded-lg px-3 py-2 text-sm"
                   :class="{ 'bg-primary-light text-white': message.userId === userId, 'ml-auto': message.userId === userId }">
                <div class="font-semibold mb-1">{{ message.userId === userId ? 'You' : message.userName }}</div>
                <div>{{ message.message }}</div>
              </div>
            </div>
            <div class="text-xs text-gray-500 dark:text-gray-400 mt-1" :class="{ 'text-right': message.userId === userId }">
              {{ formatTime(message.timestamp) }}
            </div>
          </div>
        </div>
        <div class="p-3 border-t border-gray-200 dark:border-gray-700">
          <form @submit.prevent="sendChatMessage" class="flex">
            <input v-model="chatMessage" type="text" placeholder="Type a message..." class="input flex-1 mr-2" />
            <button type="submit" class="btn btn-primary" :disabled="!chatMessage.trim()">Send</button>
          </form>
        </div>
      </div>
    </div>

    <!-- Reactions Display - Driven by activeCoreFeatures.reactions -->
    <ReactionDisplay v-if="activeCoreFeatures.reactions" :meetingId="meetingId" />

    <!-- Audible Impaired System - Driven by activeCoreFeatures.audibleImpairedSystem -->
    <div v-if="activeCoreFeatures.audibleImpairedSystem" class="border-t border-gray-200 dark:border-gray-700">
      <AudibleImpairedSystem :meetingId="meetingId" :currentUserId="userId" :currentUserName="userName.value" :localStream="meetingStore.localStream" />
    </div>

    <!-- Recording Controls - Driven by activeCoreFeatures.recording -->
    <div v-if="activeCoreFeatures.recording" class="border-t border-gray-200 dark:border-gray-700 p-2">
      <RecordingControls :meetingId="meetingId" :userId="userId" :stream="meetingStore.localStream"
                         @recording-started="meetingStore.isRecording = true"
                         @recording-stopped="meetingStore.isRecording = false" />
    </div>

    <!-- Reaction Selector - Driven by activeCoreFeatures.reactions -->
    <div v-if="activeCoreFeatures.reactions" class="fixed bottom-24 left-1/2 transform -translate-x-1/2 z-10">
      <ReactionSelector :meetingId="meetingId" :userId="userId" :userName="userName.value" />
    </div>

    <!-- Meeting controls - Props now use meetingStore and meetingConfigStore values -->
    <MeetingControls
      :isMicMuted="meetingStore.isMicMuted"
      :isCameraOff="meetingStore.isCameraOff"
      :isScreenSharing="meetingStore.isScreenSharing"
      :isChatOpen="isChatOpen"
      :isChatEnabled="activeCoreFeatures.chat"
      :isAISEnabled="activeCoreFeatures.audibleImpairedSystem"
      :isReactionsEnabled="activeCoreFeatures.reactions"
      :isBreakoutRoomsEnabled="activeCoreFeatures.breakoutRooms"
      :isVirtualBackgroundEnabled="activeCoreFeatures.virtualBackground"
      :isWhiteboardEnabled="isWhiteboardFeatureEnabled"
      :isRecordingEnabled="isRecordingFeatureEnabled"
      :isRecordingActive="meetingStore.isRecording"
      :hasJoinRequests="isAdmin && joinRequests.length > 0"
      :showJoinRequestsPanel="showJoinRequestsPanel"
      :joinRequestsCount="joinRequests.length"
      :isAdmin="isAdmin"
      :canShareLink="meetingConfigStore.canUserShareLink(userId)"
      :linkSharingPolicy="linkSharingPolicy"
      @toggle-mic="toggleMic"
      @toggle-camera="toggleCamera"
      @toggle-screen="toggleScreenSharing"
      @toggle-chat="isChatOpen = !isChatOpen"
      @toggle-ais="meetingConfigStore.toggleFeature('audibleImpairedSystem')"
      @toggle-reactions="meetingConfigStore.toggleFeature('reactions')"
      @toggle-breakout-rooms="meetingConfigStore.toggleFeature('breakoutRooms')"
      @toggle-virtual-background="meetingConfigStore.toggleFeature('virtualBackground')"
      @toggle-whiteboard="toggleWhiteboardFeature"
      @toggle-recording="meetingConfigStore.toggleFeature('recording')"
      @toggle-join-requests="toggleJoinRequestsPanel"
      @show-invite="showInvitePopup = true"
      @show-link-sharing="showLinkSharingPanel = true"
      @leave-meeting="showLeaveMeetingDialog"
    />
  </div>

  <!-- Leave Meeting Dialog -->
  <div v-if="showLeaveDialog" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6 max-w-md w-full">
      <h2 class="text-xl font-semibold mb-4 text-gray-900 dark:text-white">Leave Meeting</h2>
      <p class="mb-4 text-gray-700 dark:text-gray-300">Please provide a reason for leaving the meeting:</p>
      <textarea v-model="leaveReason" class="w-full p-2 border border-gray-300 dark:border-gray-600 rounded mb-4 bg-white dark:bg-gray-700 text-gray-900 dark:text-white" placeholder="Reason for leaving (optional)" rows="3"></textarea>
      <div class="flex justify-end space-x-2">
        <button @click="showLeaveDialog = false" class="px-4 py-2 bg-gray-200 dark:bg-gray-700 text-gray-800 dark:text-gray-200 rounded hover:bg-gray-300 dark:hover:bg-gray-600">Cancel</button>
        <button @click="leaveMeetingWithReason" class="px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700">Leave Meeting</button>
      </div>
    </div>
  </div>

  <!-- Remove Participant Dialog (Admin only) -->
  <div v-if="showRemoveDialog" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6 max-w-md w-full">
      <h2 class="text-xl font-semibold mb-4 text-gray-900 dark:text-white">Remove Participant</h2>
      <p class="mb-4 text-gray-700 dark:text-gray-300">
        You are about to remove <span class="font-semibold">{{ participantToRemove ? getParticipantName(participantToRemove) : '' }}</span> from the meeting.
      </p>
      <p class="mb-4 text-gray-700 dark:text-gray-300">Please provide a reason (visible only to the host):</p>
      <textarea v-model="removeReason" class="w-full p-2 border border-gray-300 dark:border-gray-600 rounded mb-4 bg-white dark:bg-gray-700 text-gray-900 dark:text-white" placeholder="Reason for removal (optional)" rows="3"></textarea>
      <div class="flex justify-end space-x-2">
        <button @click="showRemoveDialog = false" class="px-4 py-2 bg-gray-200 dark:bg-gray-700 text-gray-800 dark:text-gray-200 rounded hover:bg-gray-300 dark:hover:bg-gray-600">Cancel</button>
        <button @click="removeParticipantWithReason" class="px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700">Remove</button>
      </div>
    </div>
  </div>

  <!-- Notification Container -->
  <NotificationContainer @action="handleNotificationAction" />
</template>

<script setup>
import { ref, computed, onMounted, onBeforeUnmount, nextTick, watch } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { useMeetingStore } from '../store/meetingStore'
import { useMeetingConfigStore } from '../store/meetingConfigStore'
import { useAuthStore } from '../store/authStore'
import { sendMessage, listenForMessages, listenForParticipants, listenForLinkSharingActivities } from '../firebase/meetings'
import { db } from '../firebase/config'
import { collection, doc, onSnapshot, updateDoc, deleteDoc, setDoc, serverTimestamp } from 'firebase/firestore'
import { WebRTCHandler } from '../utils/webrtc'
import { VirtualBackgroundProcessor } from '../utils/virtualBackground'
import MeetingInvitePopup from '../components/MeetingInvitePopup.vue'
import UsernamePromptPopup from '../components/UsernamePromptPopup.vue'
import MeetingControls from '../components/MeetingControls.vue'
import ChatMessage from '../components/ChatMessage.vue'
import VirtualBackgroundSelector from '../components/VirtualBackgroundSelector.vue'
import LinkSharingPanel from '../components/LinkSharingPanel.vue'
import NotificationContainer from '../components/NotificationContainer.vue'
import { useNotifications } from '../composables/useNotifications'

// Router and route
const router = useRouter()
const route = useRoute()

// Stores
const meetingStore = useMeetingStore()
const meetingConfigStore = useMeetingConfigStore()
const authStore = useAuthStore()

// Notifications
const {
  showLinkShared,
  showUserJoinedViaLink,
  showInvitePrompt,
  showLinkSharingPolicyChanged
} = useNotifications()

// Meeting ID from route params
const meetingId = computed(() => route.params.id)

// User identification - prioritize authStore, fallback to localStorage
const userId = computed(() => {
  if (authStore.user?.id) return authStore.user.id
  return localStorage.getItem('userId') || `guest_${Date.now()}`
})

const userName = computed(() => {
  if (authStore.user?.name) return authStore.user.name
  return localStorage.getItem('userName') || 'Guest User'
})

// Meeting name from store or fallback
const meetingName = computed(() => meetingConfigStore.getMeetingName || `Meeting ${meetingId.value}`)

// Link sharing policy
const linkSharingPolicy = computed(() => meetingConfigStore.linkSharingPolicy)

// Auto-show invite popup when host is alone
const isHostAlone = computed(() => {
  return isAdmin.value && meetingStore.participants.length <= 1
})

// Watch for when host becomes alone and show invite popup
watch(isHostAlone, (alone) => {
  if (alone && !showInvitePopup.value && !showUsernamePrompt.value) {
    // Show invite notification after a short delay
    setTimeout(() => {
      if (isHostAlone.value && !showInvitePopup.value) {
        showInvitePrompt(1); // 1 participant (just the host)
      }
    }, 3000) // 3 second delay
  }
})

// Watch for link sharing policy changes
watch(() => meetingConfigStore.linkSharingPolicy, (newPolicy, oldPolicy) => {
  if (oldPolicy && newPolicy !== oldPolicy) {
    showLinkSharingPolicyChanged(newPolicy, getHostName());
  }
})

  // Invite/join popup logic
  const showInvitePopup = ref(false)
  const showUsernamePrompt = ref(false)

  // Generate shareable link with tracking parameters
  const generateShareableLink = (sharedBy = null) => {
    const baseUrl = `${window.location.origin}/meeting/${meetingId.value}`
    const params = new URLSearchParams()

    params.set('joinViaLink', 'true')
    if (sharedBy) {
      params.set('sharedBy', sharedBy)
    }
    params.set('timestamp', Date.now().toString())

    return `${baseUrl}?${params.toString()}`
  }

  const inviteLink = computed(() => generateShareableLink(userId.value))
  const joinTracking = computed(() => meetingConfigStore.getJoinTracking)

  function updateLinkSharingPolicy(policy) {
    meetingConfigStore.updateLinkSharingPolicy(policy)
  }

const isAdmin = computed(() => meetingConfigStore.isHost(userId.value))
/** @type {import('vue').ComputedRef<Object<String, Boolean>>} Object tracking active core features. */
const activeCoreFeatures = computed(() => meetingConfigStore.activeFeatures)
/** @type {import('vue').ComputedRef<Array<Object>>} Array of active extensions in the meeting. */
const activeMeetingExtensions = computed(() => meetingConfigStore.getAllActiveExtensions)


// Video elements refs for direct DOM manipulation (e.g., attaching streams).
const localVideo = ref(null)
const remoteVideoRefs = ref({}) // Still needed for remote streams

// WebRTC - meetingStore will hold localStream, remoteStreams, mic/cam status
const webrtcHandler = ref(null) // WebRTCHandler instance

// UI state for features - driven by meetingConfigStore or local component state if not in configStore
const isChatOpen = ref(false) // Example of local UI state not necessarily in configStore for all users
const showJoinRequestsPanel = ref(false)
const showLinkSharingPanel = ref(false)



// States for features that might have their own panels/visibility toggles
// These will be controlled by meetingConfigStore.activeFeatures eventually
// const showAIS = ref(false) // Replaced by activeCoreFeatures.value.audibleImpairedSystem
// const showBreakoutRooms = ref(false) // Replaced by activeCoreFeatures.value.breakoutRooms
// const showReactions = ref(false) // Replaced by activeCoreFeatures.value.reactions
// const showVirtualBackground = ref(false) // Replaced by activeCoreFeatures.value.virtualBackground
// const showWhiteboard = ref(false) // Replaced by activeCoreFeatures.value.whiteboard
// const showRecording = ref(false) // Replaced by activeCoreFeatures.value.recording
// const isRecording = ref(false) // This could be part of a recording feature's specific state

// Component-specific UI states (dialogs, etc.)
const chatMessage = ref('')
const chatMessagesContainer = ref(null) // For scrolling chat
const joinRequests = ref([]) // Still fetched directly for admin for now

const showRemoveDialog = ref(false)
const participantToRemove = ref(null)
const removeReason = ref('')
const showLeaveDialog = ref(false)
const leaveReason = ref('')

// Unsubscribe functions for Firebase listeners not handled by stores
let joinRequestsUnsubscribe = null
let messagesUnsubscribe = null; // From meetingStore or direct
let participantsUnsubscribe = null; // From meetingStore or direct
let linkSharingActivitiesUnsubscribe = null; // For link sharing activities


// Initialize the meeting
// Check if user is joining via shared link and needs username prompt
const checkJoinViaLink = () => {
  const joinViaLink = route.query.joinViaLink
  const sharedBy = route.query.sharedBy
  const hasUsername = localStorage.getItem('userName')

  // If joining via link and no username, show prompt
  if (joinViaLink && !hasUsername) {
    showUsernamePrompt.value = true
    return true
  }

  // If joining via link with username, record the join
  if (joinViaLink && hasUsername && sharedBy) {
    recordJoinViaSharedLink(sharedBy, hasUsername)
  }

  return false
}

const recordJoinViaSharedLink = async (sharedBy, username) => {
  try {
    await meetingConfigStore.recordJoinViaLink({
      sharedBy,
      joinedName: username,
      joinedBy: userId.value,
      timestamp: new Date().toISOString()
    })
  } catch (error) {
    console.error('Failed to record join via link:', error)
  }
}

onMounted(async () => {
  // Check if user needs to enter username first
  if (checkJoinViaLink()) {
    return // Don't initialize meeting until username is provided
  }

  if (!userId.value) {
    // This case should ideally be handled by a route guard redirecting to login/home
    alert("User ID not found. Please ensure you are logged in or have provided a name.");
    router.push('/');
    return;
  }
  // Ensure userId and userName are in localStorage if not using full auth
  if (!localStorage.getItem('userId')) localStorage.setItem('userId', userId.value);
  if (userName.value && !localStorage.getItem('userName')) localStorage.setItem('userName', userName.value);


  try {
    await initializeMeeting();
  } catch (error) {
    console.error('Error in onMounted:', error);
  }
});

const handleBeforeUnload = () => {
  if (webrtcHandler.value) {
    webrtcHandler.value.leaveMeeting(); // Signals other peers
  }
  // Consider if meetingConfigStore.resetMeetingConfig() or meetingStore.reset() should be called here
  // or if it's better handled by onBeforeUnmount for graceful component cleanup.
};

onBeforeUnmount(() => {
  window.removeEventListener('beforeunload', handleBeforeUnload);

  if (webrtcHandler.value) {
    webrtcHandler.value.leaveMeeting();
  }
  if (meetingStore.localStream) {
    meetingStore.localStream.getTracks().forEach(track => track.stop());
  }

  meetingStore.reset();
  meetingConfigStore.resetMeetingConfig(); // Resets features, extensions, listeners for this meeting

  if (messagesUnsubscribe) messagesUnsubscribe();
  if (participantsUnsubscribe) participantsUnsubscribe();
  if (joinRequestsUnsubscribe) joinRequestsUnsubscribe();
  if (linkSharingActivitiesUnsubscribe) linkSharingActivitiesUnsubscribe();
});


// WebRTC related stream handling already done via callbacks to meetingStore

const handleParticipantJoined = (participant) => {
  console.log('Participant joined (WebRTC):', participant);
  // Firebase listener for participants subcollection is the source of truth for participant list UI
};

const handleParticipantLeft = (participantId) => {
  console.log('Participant left (WebRTC):', participantId);
  // Firebase listener for participants subcollection handles UI updates
};

// Initialize meeting after username is provided
const initializeMeeting = async () => {
  try {
    meetingStore.setMeetingId(meetingId.value);
    await meetingConfigStore.loadMeetingConfiguration(meetingId.value, userId.value)

    if (meetingConfigStore.error) {
      alert(`Error loading meeting: ${meetingConfigStore.error}`)
      router.push('/')
      return
    }

    // Initialize WebRTC Handler
    webrtcHandler.value = new WebRTCHandler(
      meetingId.value,
      userId.value,
      (peerId, stream) => meetingStore.addRemoteStream(peerId, stream),
      (peerId) => meetingStore.removeRemoteStream(peerId),
      handleParticipantJoined,
      handleParticipantLeft
    )

    const stream = await webrtcHandler.value.initLocalStream();
    meetingStore.setLocalStream(stream);

    if (localVideo.value && meetingStore.localStream) {
      localVideo.value.srcObject = meetingStore.localStream;
    }

    await webrtcHandler.value.joinMeeting();
    meetingStore.setConnectionStatus('connected');

    // Listen for messages
    messagesUnsubscribe = listenForMessages(meetingId.value, (newMessages) => {
      meetingStore.messages = newMessages;
      scrollChatToBottom();
    });

    // Listen for participants
    participantsUnsubscribe = listenForParticipants(
      meetingId.value,
      (newParticipants) => {
        meetingStore.participants = newParticipants;
      },
      (joinData) => {
        if (isAdmin.value) {
          console.log('User joined via shared link:', joinData);

          // Show notification for host
          const sharedByName = getParticipantName(joinData.participantData.sharedBy) || 'Unknown';
          showUserJoinedViaLink(
            joinData.participantData.userName || 'Unknown User',
            sharedByName
          );
        }
      }
    );

    if (isAdmin.value) {
      listenForJoinRequests();

      // Listen for link sharing activities (host only)
      linkSharingActivitiesUnsubscribe = listenForLinkSharingActivities(meetingId.value, (activities) => {
        // Update the store with new activities
        meetingConfigStore.linkSharingActivities = activities;
      });
    }

    window.addEventListener('beforeunload', handleBeforeUnload);
  } catch (error) {
    console.error('Error initializing meeting:', error);
    meetingStore.setConnectionStatus('disconnected');
    alert(`Failed to join the meeting: ${error.message}. Please try again.`);
    router.push('/');
  }
}

// Username prompt handlers
const onUsernameSubmit = async (data) => {
  if (typeof data === 'string') {
    // Legacy support
    localStorage.setItem('userName', data);
    console.log('Username set:', data);
  } else {
    // New format with tracking data
    localStorage.setItem('userName', data.username);
    console.log('Username set with tracking:', data);

    // Record the join via link
    if (data.joinMethod === 'shared_link') {
      await meetingConfigStore.recordJoinViaLink({
        sharedBy: data.sharedBy || 'unknown',
        joinedName: data.username,
        joinedBy: userId.value,
        timestamp: data.timestamp
      });
    }
  }

  // Hide username prompt and initialize meeting
  showUsernamePrompt.value = false;
  await initializeMeeting();
};

const onUsernameCancel = () => {
  // Redirect back to home or handle cancellation
  router.push('/');
};

// Link sharing handlers
const onLinkShared = async (shareData) => {
  await meetingConfigStore.recordLinkSharingActivity({
    type: 'link_shared',
    sharedBy: userId.value,
    method: shareData.method,
    timestamp: shareData.timestamp
  });

  // Show notification to other participants (if host)
  if (isAdmin.value) {
    showLinkShared(userName.value, shareData.method);
  }

  console.log('Link shared via:', shareData.method);
};

const getHostName = () => {
  const hostId = meetingConfigStore.meetingSettings.hostUserId;
  const host = meetingStore.participants.find(p => p.userId === hostId || p.id === hostId);
  return host?.userName || 'Host';
};

const getParticipantName = (participantId) => {
  const participant = meetingStore.participants.find(p => p.id === participantId || p.userId === participantId);
  return participant?.userName || 'Unknown User';
};

// Notification action handler
const handleNotificationAction = (action) => {
  switch (action.action) {
    case 'show_invite':
      showInvitePopup.value = true;
      break;
    case 'dismiss':
      // Already handled by notification removal
      break;
    default:
      console.log('Unknown notification action:', action);
  }
};

// Media Toggles - now actions in meetingStore
const toggleMic = () => {
  meetingStore.toggleMic();
  // Optionally, sync this state to Firebase for other participants to see mute status
  // This would likely involve updating the user's document in the 'participants' subcollection
  // e.g., updateDoc(doc(db, 'meetings', meetingId.value, 'participants', userId.value), { audioMuted: meetingStore.isMicMuted });
};

const toggleCamera = () => {
  meetingStore.toggleCamera();
  // Similar to toggleMic, sync videoMuted state to Firebase
  // e.g., updateDoc(doc(db, 'meetings', meetingId.value, 'participants', userId.value), { videoMuted: meetingStore.isCameraOff });
};

const toggleScreenSharing = async () => {
  try {
    // Screen sharing state is more complex, WebRTCHandler manages it
    const isCurrentlySharing = meetingStore.isScreenSharing;
    await webrtcHandler.value.toggleScreenSharing(!isCurrentlySharing);
    meetingStore.toggleScreenSharing(); // Update store state AFTER successful operation
  } catch (error) {
    console.error('Error toggling screen sharing:', error);
    if (meetingStore.isScreenSharing) meetingStore.toggleScreenSharing(); // Revert store state on error
  }
};

// Chat
const sendChatMessage = async () => {
  if (!chatMessage.value.trim()) return;
  try {
    await sendMessage(
      meetingId.value,
      userId.value,
      userName.value,
      chatMessage.value
    );
    chatMessage.value = '';
  } catch (error) {
    console.error('Error sending message:', error);
  }
};

const copyMeetingId = () => {
  navigator.clipboard.writeText(meetingId.value)
    .then(() => alert('Meeting ID copied to clipboard'))
    .catch(err => console.error('Failed to copy meeting ID:', err));
};

// Participant Info Getters (using meetingStore.participants)

const isParticipantMuted = (participantId) => {
  const participant = meetingStore.participants.find(p => p.id === participantId || p.userId === participantId);
  return participant?.audioMuted || false; // Assuming 'audioMuted' field in participant data
};

const isParticipantVideoOff = (participantId) => {
  const participant = meetingStore.participants.find(p => p.id === participantId || p.userId === participantId);
  return participant?.videoMuted || false; // Assuming 'videoMuted' field in participant data
};


// Virtual Background - This logic would interact with meetingStore.localStream
// and potentially a feature flag from meetingConfigStore.activeFeatures.virtualBackground
const applyVirtualBackground = async (backgroundSettings) => {
  if (!meetingConfigStore.isFeatureActive('virtualBackground') || !meetingStore.localStream) return;
  try {
    const videoTrack = meetingStore.localStream.getVideoTracks()[0];
    if (!videoTrack) return;

    const processor = new VirtualBackgroundProcessor(); // Consider making this a singleton or managed instance
    const processedTrack = await processor.initialize(videoTrack);

    if (processedTrack) {
      processor.setBackground(backgroundSettings.url);
      const newStream = new MediaStream([processedTrack, ...meetingStore.localStream.getAudioTracks()]);
      meetingStore.setLocalStream(newStream); // Update stream in store

      if (localVideo.value) localVideo.value.srcObject = newStream;
      if (webrtcHandler.value) await webrtcHandler.value.updateVideoTrack(processedTrack);
    }
    // showVirtualBackground.value = false; // Panel visibility now controlled by activeFeatures
    await meetingConfigStore.toggleFeature('virtualBackground'); // Example: to close panel
  } catch (error) {
    console.error('Error applying virtual background:', error);
    alert('Failed to apply virtual background.');
  }
};

// Participant Removal (Admin)
const showRemoveParticipantDialog = (pId) => {
  participantToRemove.value = pId;
  removeReason.value = '';
  showRemoveDialog.value = true;
};

const removeParticipantWithReason = async () => {
  if (!isAdmin.value || !participantToRemove.value) return;
  try {
    if (webrtcHandler.value) {
      // This would involve signaling the specific peer to disconnect AND removing them from Firebase participants
      // For now, we'll focus on Firebase removal, WebRTCHandler would need a method.
      // await webrtcHandler.value.kickParticipant(participantToRemove.value);

      // Remove from Firebase participants list (this will trigger updates for others)
      await deleteDoc(doc(db, 'meetings', meetingId.value, 'participants', participantToRemove.value));

      const reasonText = removeReason.value.trim() ? ` (Reason: ${removeReason.value})` : '';
      await sendMessage(
        meetingId.value, 'system', 'System',
        `${getParticipantName(participantToRemove.value)} was removed by the host${reasonText}`
      );
      showRemoveDialog.value = false;
      participantToRemove.value = null;
      removeReason.value = '';
    }
  } catch (error) {
    console.error('Error removing participant:', error);
    alert('Failed to remove participant.');
  }
};

// Leave Meeting
const showLeaveMeetingDialog = () => {
  leaveReason.value = '';
  showLeaveDialog.value = true;
};

const leaveMeetingWithReason = async () => {
  try {
    if (leaveReason.value.trim()) {
      await sendMessage(
        meetingId.value, 'system', 'System',
        `${userName.value} left the meeting (Reason: ${leaveReason.value})`
      );
    }
    // Actual cleanup (WebRTC, store resets) is in onBeforeUnmount
    router.push('/');
  } catch (error) {
    console.error('Error leaving meeting with reason:', error);
    router.push('/'); // Leave anyway
  }
};


const formatTime = (timestamp) => {
  if (!timestamp) return '';
  const date = timestamp.toDate ? timestamp.toDate() : new Date(timestamp);
  return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
};

const scrollChatToBottom = () => {
  nextTick(() => {
    if (chatMessagesContainer.value) {
      chatMessagesContainer.value.scrollTop = chatMessagesContainer.value.scrollHeight;
    }
  });
};

watch(() => meetingStore.messages, scrollChatToBottom, { deep: true, immediate: true });


// Join Requests (Admin) - Stays largely the same for now, uses direct Firebase listeners
const listenForJoinRequests = () => {
  const joinRequestsRef = collection(db, 'meetings', meetingId.value, 'joinRequests');
  joinRequestsUnsubscribe = onSnapshot(joinRequestsRef, (snapshot) => {
    joinRequests.value = snapshot.docs.map(d => ({ id: d.id, ...d.data() }));
    if (joinRequests.value.length > 0 && !showJoinRequestsPanel.value) {
      // alert(`You have ${joinRequests.value.length} new join request(s).`);
      // showJoinRequestsPanel.value = true; // Auto-open panel or show badge
    }
  });
};

const approveJoinRequest = async (requestId) => {
  try {
    const request = joinRequests.value.find(r => r.id === requestId);
    if (!request) return;

    await updateDoc(doc(db, 'meetings', meetingId.value, 'joinRequests', requestId), {
      status: 'approved', approvedAt: serverTimestamp(), approvedBy: userId.value
    });

    // Add to participants subcollection (this is the key step for them to actually join)
    await setDoc(doc(db, 'meetings', meetingId.value, 'participants', request.userId), {
      userId: request.userId, userName: request.userName, joined: serverTimestamp(), approved: true, isAdmin: false
    });

    await sendMessage(meetingId.value, 'system', 'System', `${request.userName} joined the meeting.`);
    await deleteDoc(doc(db, 'meetings', meetingId.value, 'joinRequests', requestId));
  } catch (error) {
    console.error('Error approving join request:', error);
    alert('Failed to approve join request.');
  }
};

const denyJoinRequest = async (requestId) => {
  try {
    await updateDoc(doc(db, 'meetings', meetingId.value, 'joinRequests', requestId), {
      status: 'denied', deniedAt: serverTimestamp(), deniedBy: userId.value
    });
    await deleteDoc(doc(db, 'meetings', meetingId.value, 'joinRequests', requestId));
  } catch (error) {
    console.error('Error denying join request:', error);
    alert('Failed to deny join request.');
  }
};

const toggleJoinRequestsPanel = () => {
  showJoinRequestsPanel.value = !showJoinRequestsPanel.value;
};

// Computed properties for MeetingControls based on meetingConfigStore
const isRecordingFeatureEnabled = computed(() => meetingConfigStore.isFeatureActive('recording'));
const isWhiteboardFeatureEnabled = computed(() => meetingConfigStore.isFeatureActive('whiteboard'));
// ... and so on for other features

// Example: Toggling a feature using meetingConfigStore
const toggleWhiteboardFeature = async () => {
  if (!isAdmin.value) return; // Only host can toggle features
  await meetingConfigStore.toggleFeature('whiteboard');
};

/**
 * Resolves the component to be rendered for a given extension.
 * This is a placeholder and needs a robust implementation for a true dynamic extension system.
 * It might involve:
 * 1. Extensions registering their components globally during an "installation" phase.
 * 2. A mapping maintained in `extensionsStore` from an extension ID/entryPoint to its component.
 * 3. Dynamic imports: `defineAsyncComponent(() => import(\`../extensions/\${extensionId}/\${entryPoint}.vue\`))`
 *    (Requires extensions to be bundled with the app or follow a strict, known directory structure).
 * 4. For truly third-party extensions, loading pre-compiled JS bundles and their exposed components.
 *
 * @param {string} entryPoint - The entry point identifier for the extension's UI component (e.g., component name or path).
 * @returns {Object|null} The Vue component definition or null if not resolved.
 */
const resolveExtensionComponent = (entryPoint) => {
  // This will need a robust mechanism. For now, a placeholder.
  // Could involve dynamic imports or a global registration system for extension components.
  // e.g., return defineAsyncComponent(() => import(`../extensions/${entryPoint}.vue`));
  if (entryPoint) {
    console.warn(`Dynamic component resolution for entry point "${entryPoint}" not yet fully implemented. Attempting placeholder lookup.`);
    // Example: try to look up globally registered component by entryPoint as name
    // const component = getCurrentInstance()?.appContext.components[entryPoint];
    // if (component) return component;
  }
  console.warn(`Component for entry point "${entryPoint}" could not be resolved.`);
  return null;
}
}
</script>