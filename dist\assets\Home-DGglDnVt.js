import{u as O,r as u,c as R,o as $,a as i,b as t,d as I,e as L,f as z,w,g as P,h as C,i as p,v as _,j as V,F as A,k as q,l as T,m as o,n as b,t as M,p as W}from"./index-2cGdSAEs.js";import{n as U,c as Y,a as H}from"./meetings-DGXsHbUS.js";import{u as G}from"./meetingConfigStore-extFw0sc.js";import{u as Q}from"./extensionsStore-DVoXHZtn.js";const X={class:"w-full px-2 sm:px-4 py-6 max-w-3xl mx-auto flex flex-col gap-8"},Z={class:"text-center mb-8"},K={class:"flex flex-wrap justify-center gap-4 mt-6"},ee={class:"bg-white dark:bg-gray-800 rounded-lg shadow p-4 sm:p-6 mb-8 flex flex-col gap-4"},te={class:"mb-4"},se={class:"mb-4"},ae={class:"mb-4"},oe={class:"flex items-center"},le={class:"mb-6"},ne={class:"grid grid-cols-1 sm:grid-cols-2 gap-2"},ie=["id","onUpdate:modelValue"],re=["for"],de={class:"mb-6"},ue={key:0,class:"text-center py-4"},me={key:1,class:"text-center py-4"},ge={key:2,class:"grid grid-cols-1 sm:grid-cols-2 gap-2"},ce=["onClick"],pe={class:"font-semibold text-gray-800 dark:text-white"},xe={class:"text-xs text-gray-500 dark:text-gray-400"},ve={class:"text-sm text-gray-600 dark:text-gray-300"},ye=["disabled"],be={key:0},fe={key:1},ke={class:"bg-white dark:bg-gray-800 rounded-lg shadow p-4 sm:p-6 flex flex-col gap-4"},he={class:"mb-4"},we={class:"mb-4"},_e=["disabled"],Me={key:0},Ee={key:1},je={__name:"Home",setup(Se){const F=T(),a=G(),m=Q(),x=O(),E=u(a.meetingSettings.name||"My Awesome Meeting"),l=u(localStorage.getItem("userName")||""),c=u(""),S=u(a.meetingSettings.requireApproval||!1),g=u({...a.activeFeatures}),v=u([]),f=u(!1),y=u(!1),N=R(()=>m.getMarketplaceExtensions);$(async()=>{var n;m.extensions.length===0&&!m.isLoading&&await m.fetchExtensions(),!l.value&&((n=x.user)!=null&&n.name)&&(l.value=x.user.name),j.value.forEach(e=>{typeof g.value[e.key]>"u"&&(g.value[e.key]=a.activeFeatures[e.key]===void 0?!1:a.activeFeatures[e.key])})});const B=n=>{const e=v.value.indexOf(n);e>-1?v.value.splice(e,1):v.value.push(n)},D=async()=>{var n;if(!l.value.trim()){alert("Please enter your name.");return}try{f.value=!0;let e=((n=x.user)==null?void 0:n.id)||localStorage.getItem("userId");e||(e=U(),localStorage.setItem("userId",e)),localStorage.setItem("userName",l.value);const r={name:E.value||"TheMeet Meeting",requireApproval:S.value};a.initializeNewMeeting(r,e),Object.keys(g.value).forEach(d=>{Object.prototype.hasOwnProperty.call(a.activeFeatures,d),a.activeFeatures[d]=g.value[d]});const s=v.value.map(d=>{const h=m.getExtensionById(d);return h?{id:h.id,name:h.name,config:h.defaultConfig||{}}:null}).filter(d=>d!==null);a.activeExtensions=s;const k=await Y(e,a.meetingSettings.name,a.meetingSettings.requireApproval,a.activeFeatures,a.activeExtensions);F.push(`/meeting/${k}`)}catch(e){console.error("Error creating meeting:",e),alert(`Failed to create meeting: ${e.message}. Please try again.`)}finally{f.value=!1}},J=async()=>{var n;if(!(!c.value.trim()||!l.value.trim()))try{if(y.value=!0,!await H(c.value)){alert("Meeting not found or has ended."),y.value=!1;return}let r=((n=x.user)==null?void 0:n.id)||localStorage.getItem("userId");r||(r=U(),localStorage.setItem("userId",r)),localStorage.setItem("userName",l.value),F.push(`/meeting/${c.value}`)}catch(e){console.error("Error joining meeting:",e),alert("Failed to join meeting. Please try again.")}finally{y.value=!1}},j=u([{key:"recording",label:"Enable Recording"},{key:"whiteboard",label:"Enable Whiteboard"},{key:"breakoutRooms",label:"Enable Breakout Rooms"},{key:"virtualBackground",label:"Enable Virtual Backgrounds"},{key:"audibleImpairedSystem",label:"Enable Audible Impaired System"},{key:"reactions",label:"Enable Reactions"}]);return(n,e)=>{const r=P("router-link");return o(),i("div",X,[t("div",Z,[e[9]||(e[9]=t("h1",{class:"text-4xl font-bold text-gray-900 dark:text-white mb-4"},"Welcome to TheMeet",-1)),e[10]||(e[10]=t("p",{class:"text-xl text-gray-600 dark:text-gray-300 mb-6"}," A modern web app for hosting seamless video meetings ",-1)),t("div",K,[I(r,{to:"/join",class:"btn btn-outline"},{default:w(()=>e[5]||(e[5]=[t("i",{class:"fas fa-sign-in-alt mr-2"},null,-1),b(" Join Meeting ")])),_:1,__:[5]}),I(r,{to:"/marketplace",class:"btn btn-outline"},{default:w(()=>e[6]||(e[6]=[t("i",{class:"fas fa-store mr-2"},null,-1),b(" Marketplace ")])),_:1,__:[6]}),I(r,{to:"/extensions",class:"btn btn-outline"},{default:w(()=>e[7]||(e[7]=[t("i",{class:"fas fa-puzzle-piece mr-2"},null,-1),b(" Extensions ")])),_:1,__:[7]}),C(x).isDev?(o(),L(r,{key:0,to:"/dashboard",class:"btn btn-outline"},{default:w(()=>e[8]||(e[8]=[t("i",{class:"fas fa-tachometer-alt mr-2"},null,-1),b(" Dashboard ")])),_:1,__:[8]})):z("",!0)])]),t("div",ee,[e[18]||(e[18]=t("h2",{class:"text-2xl font-semibold text-gray-900 dark:text-white mb-4"},"Start a New Meeting",-1)),t("div",te,[e[11]||(e[11]=t("label",{for:"meetingName",class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"}," Meeting Name (optional) ",-1)),p(t("input",{id:"meetingName","onUpdate:modelValue":e[0]||(e[0]=s=>E.value=s),type:"text",placeholder:"My Awesome Meeting",class:"input"},null,512),[[_,E.value]])]),t("div",se,[e[12]||(e[12]=t("label",{for:"userName",class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"}," Your Name ",-1)),p(t("input",{id:"userName","onUpdate:modelValue":e[1]||(e[1]=s=>l.value=s),type:"text",placeholder:"John Doe",class:"input",required:""},null,512),[[_,l.value]])]),t("div",ae,[t("div",oe,[p(t("input",{id:"requireApproval","onUpdate:modelValue":e[2]||(e[2]=s=>S.value=s),type:"checkbox",class:"h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded"},null,512),[[V,S.value]]),e[13]||(e[13]=t("label",{for:"requireApproval",class:"ml-2 block text-sm text-gray-700 dark:text-gray-300"}," Require approval for participants to join ",-1))])]),t("div",le,[e[14]||(e[14]=t("h3",{class:"text-lg font-medium text-gray-900 dark:text-white mb-2"},"Core Features",-1)),t("div",ne,[(o(!0),i(A,null,q(j.value,s=>(o(),i("div",{key:s.key,class:"flex items-center"},[p(t("input",{id:`feature-${s.key}`,"onUpdate:modelValue":k=>g.value[s.key]=k,type:"checkbox",class:"h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded"},null,8,ie),[[V,g.value[s.key]]]),t("label",{for:`feature-${s.key}`,class:"ml-2 block text-sm text-gray-700 dark:text-gray-300"},M(s.label),9,re)]))),128))])]),t("div",de,[e[17]||(e[17]=t("h3",{class:"text-lg font-medium text-gray-900 dark:text-white mb-2"},"Select Extensions (Optional)",-1)),C(m).isLoading&&N.value.length===0?(o(),i("div",ue,e[15]||(e[15]=[t("p",{class:"text-gray-500 dark:text-gray-400"},"Loading extensions...",-1)]))):!C(m).isLoading&&N.value.length===0?(o(),i("div",me,e[16]||(e[16]=[t("p",{class:"text-gray-500 dark:text-gray-400"},"No extensions currently available.",-1)]))):(o(),i("div",ge,[(o(!0),i(A,null,q(N.value,s=>(o(),i("div",{key:s.id,onClick:k=>B(s.id),class:W(["extension-card",{selected:v.value.includes(s.id)}])},[t("h4",pe,[b(M(s.name)+" ",1),t("span",xe,"v"+M(s.version),1)]),t("p",ve,M(s.description),1)],10,ce))),128))]))]),t("button",{onClick:D,class:"w-full py-3 px-4 rounded bg-blue-600 text-white font-semibold text-lg shadow hover:bg-blue-700 transition disabled:opacity-60 disabled:cursor-not-allowed",disabled:!l.value.trim()||f.value},[f.value?(o(),i("span",be,"Creating...")):(o(),i("span",fe,"Start Meeting Now"))],8,ye)]),t("div",ke,[e[21]||(e[21]=t("h2",{class:"text-2xl font-semibold text-gray-900 dark:text-white mb-4"},"Join a Meeting",-1)),t("div",he,[e[19]||(e[19]=t("label",{for:"meetingId",class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"}," Meeting ID ",-1)),p(t("input",{id:"meetingId","onUpdate:modelValue":e[3]||(e[3]=s=>c.value=s),type:"text",placeholder:"Enter meeting ID",class:"input",required:""},null,512),[[_,c.value]])]),t("div",we,[e[20]||(e[20]=t("label",{for:"joinUserName",class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"}," Your Name ",-1)),p(t("input",{id:"joinUserName","onUpdate:modelValue":e[4]||(e[4]=s=>l.value=s),type:"text",placeholder:"John Doe",class:"input w-full py-2 pl-10 text-sm text-gray-700 dark:text-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-gray-600 focus:border-transparent",required:""},null,512),[[_,l.value]])]),t("button",{onClick:J,class:"w-full py-3 px-4 rounded bg-gray-700 text-white font-semibold text-lg shadow hover:bg-gray-800 transition disabled:opacity-60 disabled:cursor-not-allowed",disabled:!c.value.trim()||!l.value.trim()||y.value},[y.value?(o(),i("span",Me,"Joining...")):(o(),i("span",Ee,"Join Meeting"))],8,_e)])])}}};export{je as default};
