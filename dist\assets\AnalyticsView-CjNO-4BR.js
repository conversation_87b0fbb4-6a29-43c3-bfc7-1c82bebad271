import{_ as x,c as y,r as m,f as h,I as b,g as i,h as s,x as v,t as n,H as u,A as g,L as w,m as l,p as f}from"./index-DX9maXWO.js";import{u as k}from"./extensionsStore-PEOhYxsJ.js";const E={class:"analytics-container"},D={key:0,class:"loading-message"},V={key:1,class:"error-message"},A={key:2,class:"analytics-content"},S={class:"analytics-header"},B={class:"stats-grid"},I={class:"stat-card total-downloads"},L={class:"stat-value"},N={class:"stat-card last-updated"},C={class:"stat-value-small"},T={class:"stat-card version-info"},U={class:"stat-value-small"},H={class:"actions-footer"},P={__name:"AnalyticsView",setup($){const _=b(),d=y(),r=k(),t=m(null),c=m(!0);h(async()=>{var o;c.value=!0;const e=_.params.id;r.extensions.length===0&&await r.fetchExtensions();const a=r.getExtensionById(e);a&&d.user&&a.dev_metadata.author_id===d.user.id?t.value=a:(t.value=null,console.warn(`User ${(o=d.user)==null?void 0:o.id} not authorized for extension ${e} or extension not found.`)),c.value=!1});function p(e){if(!e)return"N/A";const a={year:"numeric",month:"short",day:"numeric"};return new Date(e).toLocaleDateString(void 0,a)}return(e,a)=>{const o=g("router-link");return l(),i("div",E,[c.value?(l(),i("div",D,"Loading analytics data...")):t.value?(l(),i("div",A,[s("div",S,[s("h1",null,'Analytics for "'+n(t.value.name)+'"',1),v(o,{to:{name:"Dashboard"},class:"back-button"},{default:u(()=>a[0]||(a[0]=[s("i",{class:"fas fa-arrow-left"},null,-1),f(" Back to Dashboard ")])),_:1,__:[0]})]),s("div",B,[s("div",I,[a[1]||(a[1]=s("div",{class:"stat-icon"},[s("i",{class:"fas fa-users"})],-1)),s("div",L,n(t.value.downloads),1),a[2]||(a[2]=s("div",{class:"stat-label"},"Total Installs",-1))]),s("div",N,[a[3]||(a[3]=s("div",{class:"stat-icon"},[s("i",{class:"fas fa-clock"})],-1)),s("div",C,n(p(t.value.dev_metadata.updated_at)),1),a[4]||(a[4]=s("div",{class:"stat-label"},"Last Updated",-1))]),s("div",T,[a[5]||(a[5]=s("div",{class:"stat-icon"},[s("i",{class:"fas fa-code-branch"})],-1)),s("div",U,"v"+n(t.value.version),1),a[6]||(a[6]=s("div",{class:"stat-label"},"Current Version",-1))]),a[7]||(a[7]=w('<div class="stat-card chart-placeholder" data-v-07148a07><div class="stat-icon" data-v-07148a07><i class="fas fa-chart-bar" data-v-07148a07></i></div><div class="stat-value-small" data-v-07148a07>Downloads Over Time</div><div class="stat-label" data-v-07148a07>(Chart coming soon)</div></div><div class="stat-card chart-placeholder" data-v-07148a07><div class="stat-icon" data-v-07148a07><i class="fas fa-map-marked-alt" data-v-07148a07></i></div><div class="stat-value-small" data-v-07148a07>Usage Heatmap</div><div class="stat-label" data-v-07148a07>(Feature coming soon)</div></div>',2))]),s("div",H,[v(o,{to:{name:"ExtensionEditor",params:{id:t.value.id}},class:"action-btn edit-btn"},{default:u(()=>a[8]||(a[8]=[s("i",{class:"fas fa-edit"},null,-1),f(" Edit Extension ")])),_:1,__:[8]},8,["to"]),v(o,{to:{name:"ExtensionDetail",params:{id:t.value.id}},class:"action-btn view-btn"},{default:u(()=>a[9]||(a[9]=[s("i",{class:"fas fa-eye"},null,-1),f(" View Public Page ")])),_:1,__:[9]},8,["to"])])])):(l(),i("div",V," Extension not found or you do not have permission to view its analytics. "))])}}},M=x(P,[["__scopeId","data-v-07148a07"]]);export{M as default};
