<template>
  <div class="p-4 sm:p-8 max-w-6xl mx-auto">
    <h1 class="text-3xl sm:text-4xl font-bold mb-2 text-center text-gray-800 dark:text-white">Extension Marketplace</h1>
    <p class="mb-6 text-center text-gray-600 dark:text-gray-300 text-base sm:text-lg">Discover and install extensions built by TheMeet community.</p>

    <div v-if="extensionsStore.isLoading" class="text-center text-lg py-8 text-gray-500">Loading extensions...</div>
    <div v-if="extensionsStore.error" class="text-center text-lg py-8 text-red-500">Error fetching extensions: {{ extensionsStore.error }}</div>

    <div v-if="!extensionsStore.isLoading && !extensionsStore.error" class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
      <div v-for="ext in extensionsStore.getMarketplaceExtensions" :key="ext.id"
        class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-100 dark:border-gray-700 flex flex-col transition-transform duration-150 hover:scale-[1.02] hover:shadow-lg focus-within:scale-[1.02] focus-within:shadow-lg">
        <img :src="ext.dev_metadata.author_pic || 'https://via.placeholder.com/40'" :alt="ext.dev_metadata.author_name"
          class="w-12 h-12 rounded-full mt-5 ml-5 mb-0 object-cover border border-gray-200 dark:border-gray-600" />
        <div class="flex flex-col flex-1 px-5 pb-5 pt-2">
          <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-1">{{ ext.name }}
            <span class="ml-2 px-2 py-0.5 rounded bg-gray-100 dark:bg-gray-700 text-xs text-gray-600 dark:text-gray-300 align-middle">v{{ ext.version }}</span>
          </h3>
          <p class="text-gray-600 dark:text-gray-300 text-sm mb-2 flex-1">{{ ext.description }}</p>
          <div class="flex flex-wrap gap-2 mb-3">
            <span v-for="tag in ext.tags" :key="tag" class="bg-blue-50 dark:bg-blue-900 text-blue-600 dark:text-blue-200 px-2 py-0.5 rounded-full text-xs">{{ tag }}</span>
          </div>
          <div class="flex justify-between items-center border-t border-gray-100 dark:border-gray-700 pt-3 mt-auto">
            <span class="text-xs text-gray-500 flex items-center"><i class="fas fa-download mr-1"></i>{{ ext.downloads }} downloads</span>
            <router-link :to="{ name: 'ExtensionDetail', params: { id: ext.id } }"
              class="inline-block bg-blue-600 hover:bg-blue-700 text-white text-xs font-medium px-4 py-2 rounded transition-colors duration-150 focus:outline-none focus:ring-2 focus:ring-blue-400 focus:ring-offset-2">View Details</router-link>
          </div>
        </div>
      </div>
      <div v-if="extensionsStore.getMarketplaceExtensions.length === 0" class="col-span-full text-center text-gray-400 py-8">
        No extensions found in the marketplace yet.
      </div>
    </div>
  </div>
</template>


<script setup>
import { onMounted } from 'vue';
import { useExtensionsStore } from '../../store/extensionsStore';

const extensionsStore = useExtensionsStore();

onMounted(() => {
  if (extensionsStore.extensions.length === 0) { // Fetch only if not already populated
    extensionsStore.fetchExtensions();
  }
});
</script>

