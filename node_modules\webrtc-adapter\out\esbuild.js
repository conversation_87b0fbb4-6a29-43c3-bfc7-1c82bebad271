"use strict";(()=>{var P=(e,t)=>()=>(t||e((t={exports:{}}).exports,t),t.exports);var x=P(E=>{"use strict";Object.defineProperty(E,"__esModule",{value:!0});var J=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(e){return typeof e}:function(e){return e&&typeof Symbol=="function"&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e};E.extractVersion=F;E.wrapPeerConnectionEvent=Te;E.disableLog=Pe;E.disableWarnings=_e;E.log=Ee;E.deprecated=Me;E.detectBrowser=be;E.compactObject=ee;E.walkStats=N;E.filterStats=ke;function Re(e,t,r){return t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}var Z=!0,w=!0;function F(e,t,r){var n=e.match(t);return n&&n.length>=r&&parseInt(n[r],10)}function Te(e,t,r){if(!!e.RTCPeerConnection){var n=e.RTCPeerConnection.prototype,i=n.addEventListener;n.addEventListener=function(u,c){if(u!==t)return i.apply(this,arguments);var p=function(o){var d=r(o);d&&(c.handleEvent?c.handleEvent(d):c(d))};return this._eventMap=this._eventMap||{},this._eventMap[t]||(this._eventMap[t]=new Map),this._eventMap[t].set(c,p),i.apply(this,[u,p])};var a=n.removeEventListener;n.removeEventListener=function(u,c){if(u!==t||!this._eventMap||!this._eventMap[t])return a.apply(this,arguments);if(!this._eventMap[t].has(c))return a.apply(this,arguments);var p=this._eventMap[t].get(c);return this._eventMap[t].delete(c),this._eventMap[t].size===0&&delete this._eventMap[t],Object.keys(this._eventMap).length===0&&delete this._eventMap,a.apply(this,[u,p])},Object.defineProperty(n,"on"+t,{get:function(){return this["_on"+t]},set:function(c){this["_on"+t]&&(this.removeEventListener(t,this["_on"+t]),delete this["_on"+t]),c&&this.addEventListener(t,this["_on"+t]=c)},enumerable:!0,configurable:!0})}}function Pe(e){return typeof e!="boolean"?new Error("Argument type: "+(typeof e>"u"?"undefined":J(e))+". Please use a boolean."):(Z=e,e?"adapter.js logging disabled":"adapter.js logging enabled")}function _e(e){return typeof e!="boolean"?new Error("Argument type: "+(typeof e>"u"?"undefined":J(e))+". Please use a boolean."):(w=!e,"adapter.js deprecation warnings "+(e?"disabled":"enabled"))}function Ee(){if((typeof window>"u"?"undefined":J(window))==="object"){if(Z)return;typeof console<"u"&&typeof console.log=="function"&&console.log.apply(console,arguments)}}function Me(e,t){!w||console.warn(e+" is deprecated, please use "+t+" instead.")}function be(e){var t={browser:null,version:null};if(typeof e>"u"||!e.navigator)return t.browser="Not a browser.",t;var r=e.navigator;if(r.mozGetUserMedia)t.browser="firefox",t.version=F(r.userAgent,/Firefox\/(\d+)\./,1);else if(r.webkitGetUserMedia||e.isSecureContext===!1&&e.webkitRTCPeerConnection&&!e.RTCIceGatherer)t.browser="chrome",t.version=F(r.userAgent,/Chrom(e|ium)\/(\d+)\./,2);else if(e.RTCPeerConnection&&r.userAgent.match(/AppleWebKit\/(\d+)\./))t.browser="safari",t.version=F(r.userAgent,/AppleWebKit\/(\d+)\./,1),t.supportsUnifiedPlan=e.RTCRtpTransceiver&&"currentDirection"in e.RTCRtpTransceiver.prototype;else return t.browser="Not a supported browser.",t;return t}function Q(e){return Object.prototype.toString.call(e)==="[object Object]"}function ee(e){return Q(e)?Object.keys(e).reduce(function(t,r){var n=Q(e[r]),i=n?ee(e[r]):e[r],a=n&&!Object.keys(i).length;return i===void 0||a?t:Object.assign(t,Re({},r,i))},{}):e}function N(e,t,r){!t||r.has(t.id)||(r.set(t.id,t),Object.keys(t).forEach(function(n){n.endsWith("Id")?N(e,e.get(t[n]),r):n.endsWith("Ids")&&t[n].forEach(function(i){N(e,e.get(i),r)})}))}function ke(e,t,r){var n=r?"outbound-rtp":"inbound-rtp",i=new Map;if(t===null)return i;var a=[];return e.forEach(function(u){u.type==="track"&&u.trackIdentifier===t.id&&a.push(u)}),a.forEach(function(u){e.forEach(function(c){c.type===n&&c.trackId===u.id&&N(e,c,i)})}),i}});var re=P(j=>{"use strict";Object.defineProperty(j,"__esModule",{value:!0});var I=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(e){return typeof e}:function(e){return e&&typeof Symbol=="function"&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e};j.shimGetUserMedia=Ie;var xe=x(),De=Oe(xe);function Oe(e){if(e&&e.__esModule)return e;var t={};if(e!=null)for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r]);return t.default=e,t}var te=De.log;function Ie(e,t){var r=e&&e.navigator;if(!!r.mediaDevices){var n=function(s){if((typeof s>"u"?"undefined":I(s))!=="object"||s.mandatory||s.optional)return s;var o={};return Object.keys(s).forEach(function(d){if(!(d==="require"||d==="advanced"||d==="mediaSource")){var m=I(s[d])==="object"?s[d]:{ideal:s[d]};m.exact!==void 0&&typeof m.exact=="number"&&(m.min=m.max=m.exact);var l=function(y,g){return y?y+g.charAt(0).toUpperCase()+g.slice(1):g==="deviceId"?"sourceId":g};if(m.ideal!==void 0){o.optional=o.optional||[];var v={};typeof m.ideal=="number"?(v[l("min",d)]=m.ideal,o.optional.push(v),v={},v[l("max",d)]=m.ideal,o.optional.push(v)):(v[l("",d)]=m.ideal,o.optional.push(v))}m.exact!==void 0&&typeof m.exact!="number"?(o.mandatory=o.mandatory||{},o.mandatory[l("",d)]=m.exact):["min","max"].forEach(function(h){m[h]!==void 0&&(o.mandatory=o.mandatory||{},o.mandatory[l(h,d)]=m[h])})}}),s.advanced&&(o.optional=(o.optional||[]).concat(s.advanced)),o},i=function(s,o){if(t.version>=61)return o(s);if(s=JSON.parse(JSON.stringify(s)),s&&I(s.audio)==="object"){var d=function(y,g,U){g in y&&!(U in y)&&(y[U]=y[g],delete y[g])};s=JSON.parse(JSON.stringify(s)),d(s.audio,"autoGainControl","googAutoGainControl"),d(s.audio,"noiseSuppression","googNoiseSuppression"),s.audio=n(s.audio)}if(s&&I(s.video)==="object"){var m=s.video.facingMode;m=m&&((typeof m>"u"?"undefined":I(m))==="object"?m:{ideal:m});var l=t.version<66;if(m&&(m.exact==="user"||m.exact==="environment"||m.ideal==="user"||m.ideal==="environment")&&!(r.mediaDevices.getSupportedConstraints&&r.mediaDevices.getSupportedConstraints().facingMode&&!l)){delete s.video.facingMode;var v=void 0;if(m.exact==="environment"||m.ideal==="environment"?v=["back","rear"]:(m.exact==="user"||m.ideal==="user")&&(v=["front"]),v)return r.mediaDevices.enumerateDevices().then(function(h){h=h.filter(function(g){return g.kind==="videoinput"});var y=h.find(function(g){return v.some(function(U){return g.label.toLowerCase().includes(U)})});return!y&&h.length&&v.includes("back")&&(y=h[h.length-1]),y&&(s.video.deviceId=m.exact?{exact:y.deviceId}:{ideal:y.deviceId}),s.video=n(s.video),te("chrome: "+JSON.stringify(s)),o(s)})}s.video=n(s.video)}return te("chrome: "+JSON.stringify(s)),o(s)},a=function(s){return t.version>=64?s:{name:{PermissionDeniedError:"NotAllowedError",PermissionDismissedError:"NotAllowedError",InvalidStateError:"NotAllowedError",DevicesNotFoundError:"NotFoundError",ConstraintNotSatisfiedError:"OverconstrainedError",TrackStartError:"NotReadableError",MediaDeviceFailedDueToShutdown:"NotAllowedError",MediaDeviceKillSwitchOn:"NotAllowedError",TabCaptureError:"AbortError",ScreenCaptureError:"AbortError",DeviceCaptureError:"AbortError"}[s.name]||s.name,message:s.message,constraint:s.constraint||s.constraintName,toString:function(){return this.name+(this.message&&": ")+this.message}}},u=function(s,o,d){i(s,function(m){r.webkitGetUserMedia(m,o,function(l){d&&d(a(l))})})};if(r.getUserMedia=u.bind(r),r.mediaDevices.getUserMedia){var c=r.mediaDevices.getUserMedia.bind(r.mediaDevices);r.mediaDevices.getUserMedia=function(p){return i(p,function(s){return c(s).then(function(o){if(s.audio&&!o.getAudioTracks().length||s.video&&!o.getVideoTracks().length)throw o.getTracks().forEach(function(d){d.stop()}),new DOMException("","NotFoundError");return o},function(o){return Promise.reject(a(o))})})}}}}});var ne=P(B=>{"use strict";Object.defineProperty(B,"__esModule",{value:!0});B.shimGetDisplayMedia=Le;function Le(e,t){if(!(e.navigator.mediaDevices&&"getDisplayMedia"in e.navigator.mediaDevices)&&!!e.navigator.mediaDevices){if(typeof t!="function"){console.error("shimGetDisplayMedia: getSourceId argument is not a function");return}e.navigator.mediaDevices.getDisplayMedia=function(n){return t(n).then(function(i){var a=n.video&&n.video.width,u=n.video&&n.video.height,c=n.video&&n.video.frameRate;return n.video={mandatory:{chromeMediaSource:"desktop",chromeMediaSourceId:i,maxFrameRate:c||3}},a&&(n.video.mandatory.maxWidth=a),u&&(n.video.mandatory.maxHeight=u),e.navigator.mediaDevices.getUserMedia(n)})}}}});var ie=P(K=>{"use strict";Object.defineProperty(K,"__esModule",{value:!0});K.shimSelectAudioOutput=Ae;function Ae(e){"HTMLMediaElement"in e&&"setSinkId"in e.HTMLMediaElement.prototype&&(!(e.navigator&&e.navigator.mediaDevices)||!e.navigator.mediaDevices.enumerateDevices||e.navigator.mediaDevices.selectAudioOutput||(e.navigator.mediaDevices.selectAudioOutput=function(){return e.navigator.mediaDevices.enumerateDevices().then(function(t){return t.filter(function(r){return r.kind==="audiooutput"})})}))}});var oe=P(S=>{"use strict";Object.defineProperty(S,"__esModule",{value:!0});S.shimSelectAudioOutput=S.shimGetDisplayMedia=S.shimGetUserMedia=void 0;var z=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(e){return typeof e}:function(e){return e&&typeof Symbol=="function"&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},Ge=re();Object.defineProperty(S,"shimGetUserMedia",{enumerable:!0,get:function(){return Ge.shimGetUserMedia}});var Ue=ne();Object.defineProperty(S,"shimGetDisplayMedia",{enumerable:!0,get:function(){return Ue.shimGetDisplayMedia}});var Fe=ie();Object.defineProperty(S,"shimSelectAudioOutput",{enumerable:!0,get:function(){return Fe.shimSelectAudioOutput}});S.shimMediaStream=qe;S.shimOnTrack=Ve;S.shimGetSendersWithDtmf=Je;S.shimGetStats=je;S.shimSenderReceiverGetStats=Be;S.shimAddTrackRemoveTrackWithNative=se;S.shimAddTrackRemoveTrack=Ke;S.shimPeerConnection=We;S.fixNegotiationNeeded=He;var Ne=x(),L=ze(Ne);function ze(e){if(e&&e.__esModule)return e;var t={};if(e!=null)for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r]);return t.default=e,t}function ae(e,t,r){return t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function qe(e){e.MediaStream=e.MediaStream||e.webkitMediaStream}function Ve(e){if((typeof e>"u"?"undefined":z(e))==="object"&&e.RTCPeerConnection&&!("ontrack"in e.RTCPeerConnection.prototype)){Object.defineProperty(e.RTCPeerConnection.prototype,"ontrack",{get:function(){return this._ontrack},set:function(n){this._ontrack&&this.removeEventListener("track",this._ontrack),this.addEventListener("track",this._ontrack=n)},enumerable:!0,configurable:!0});var t=e.RTCPeerConnection.prototype.setRemoteDescription;e.RTCPeerConnection.prototype.setRemoteDescription=function(){var n=this;return this._ontrackpoly||(this._ontrackpoly=function(i){i.stream.addEventListener("addtrack",function(a){var u=void 0;e.RTCPeerConnection.prototype.getReceivers?u=n.getReceivers().find(function(p){return p.track&&p.track.id===a.track.id}):u={track:a.track};var c=new Event("track");c.track=a.track,c.receiver=u,c.transceiver={receiver:u},c.streams=[i.stream],n.dispatchEvent(c)}),i.stream.getTracks().forEach(function(a){var u=void 0;e.RTCPeerConnection.prototype.getReceivers?u=n.getReceivers().find(function(p){return p.track&&p.track.id===a.id}):u={track:a};var c=new Event("track");c.track=a,c.receiver=u,c.transceiver={receiver:u},c.streams=[i.stream],n.dispatchEvent(c)})},this.addEventListener("addstream",this._ontrackpoly)),t.apply(this,arguments)}}else L.wrapPeerConnectionEvent(e,"track",function(r){return r.transceiver||Object.defineProperty(r,"transceiver",{value:{receiver:r.receiver}}),r})}function Je(e){if((typeof e>"u"?"undefined":z(e))==="object"&&e.RTCPeerConnection&&!("getSenders"in e.RTCPeerConnection.prototype)&&"createDTMFSender"in e.RTCPeerConnection.prototype){var t=function(p,s){return{track:s,get dtmf(){return this._dtmf===void 0&&(s.kind==="audio"?this._dtmf=p.createDTMFSender(s):this._dtmf=null),this._dtmf},_pc:p}};if(!e.RTCPeerConnection.prototype.getSenders){e.RTCPeerConnection.prototype.getSenders=function(){return this._senders=this._senders||[],this._senders.slice()};var r=e.RTCPeerConnection.prototype.addTrack;e.RTCPeerConnection.prototype.addTrack=function(p,s){var o=r.apply(this,arguments);return o||(o=t(this,p),this._senders.push(o)),o};var n=e.RTCPeerConnection.prototype.removeTrack;e.RTCPeerConnection.prototype.removeTrack=function(p){n.apply(this,arguments);var s=this._senders.indexOf(p);s!==-1&&this._senders.splice(s,1)}}var i=e.RTCPeerConnection.prototype.addStream;e.RTCPeerConnection.prototype.addStream=function(p){var s=this;this._senders=this._senders||[],i.apply(this,[p]),p.getTracks().forEach(function(o){s._senders.push(t(s,o))})};var a=e.RTCPeerConnection.prototype.removeStream;e.RTCPeerConnection.prototype.removeStream=function(p){var s=this;this._senders=this._senders||[],a.apply(this,[p]),p.getTracks().forEach(function(o){var d=s._senders.find(function(m){return m.track===o});d&&s._senders.splice(s._senders.indexOf(d),1)})}}else if((typeof e>"u"?"undefined":z(e))==="object"&&e.RTCPeerConnection&&"getSenders"in e.RTCPeerConnection.prototype&&"createDTMFSender"in e.RTCPeerConnection.prototype&&e.RTCRtpSender&&!("dtmf"in e.RTCRtpSender.prototype)){var u=e.RTCPeerConnection.prototype.getSenders;e.RTCPeerConnection.prototype.getSenders=function(){var p=this,s=u.apply(this,[]);return s.forEach(function(o){return o._pc=p}),s},Object.defineProperty(e.RTCRtpSender.prototype,"dtmf",{get:function(){return this._dtmf===void 0&&(this.track.kind==="audio"?this._dtmf=this._pc.createDTMFSender(this.track):this._dtmf=null),this._dtmf}})}}function je(e){if(!!e.RTCPeerConnection){var t=e.RTCPeerConnection.prototype.getStats;e.RTCPeerConnection.prototype.getStats=function(){var n=this,i=Array.prototype.slice.call(arguments),a=i[0],u=i[1],c=i[2];if(arguments.length>0&&typeof a=="function")return t.apply(this,arguments);if(t.length===0&&(arguments.length===0||typeof a!="function"))return t.apply(this,[]);var p=function(m){var l={},v=m.result();return v.forEach(function(h){var y={id:h.id,timestamp:h.timestamp,type:{localcandidate:"local-candidate",remotecandidate:"remote-candidate"}[h.type]||h.type};h.names().forEach(function(g){y[g]=h.stat(g)}),l[y.id]=y}),l},s=function(m){return new Map(Object.keys(m).map(function(l){return[l,m[l]]}))};if(arguments.length>=2){var o=function(m){u(s(p(m)))};return t.apply(this,[o,a])}return new Promise(function(d,m){t.apply(n,[function(l){d(s(p(l)))},m])}).then(u,c)}}}function Be(e){if(!!((typeof e>"u"?"undefined":z(e))==="object"&&e.RTCPeerConnection&&e.RTCRtpSender&&e.RTCRtpReceiver)){if(!("getStats"in e.RTCRtpSender.prototype)){var t=e.RTCPeerConnection.prototype.getSenders;t&&(e.RTCPeerConnection.prototype.getSenders=function(){var u=this,c=t.apply(this,[]);return c.forEach(function(p){return p._pc=u}),c});var r=e.RTCPeerConnection.prototype.addTrack;r&&(e.RTCPeerConnection.prototype.addTrack=function(){var u=r.apply(this,arguments);return u._pc=this,u}),e.RTCRtpSender.prototype.getStats=function(){var u=this;return this._pc.getStats().then(function(c){return L.filterStats(c,u.track,!0)})}}if(!("getStats"in e.RTCRtpReceiver.prototype)){var n=e.RTCPeerConnection.prototype.getReceivers;n&&(e.RTCPeerConnection.prototype.getReceivers=function(){var u=this,c=n.apply(this,[]);return c.forEach(function(p){return p._pc=u}),c}),L.wrapPeerConnectionEvent(e,"track",function(a){return a.receiver._pc=a.srcElement,a}),e.RTCRtpReceiver.prototype.getStats=function(){var u=this;return this._pc.getStats().then(function(c){return L.filterStats(c,u.track,!1)})}}if("getStats"in e.RTCRtpSender.prototype&&"getStats"in e.RTCRtpReceiver.prototype){var i=e.RTCPeerConnection.prototype.getStats;e.RTCPeerConnection.prototype.getStats=function(){if(arguments.length>0&&arguments[0]instanceof e.MediaStreamTrack){var u=arguments[0],c=void 0,p=void 0,s=void 0;return this.getSenders().forEach(function(o){o.track===u&&(c?s=!0:c=o)}),this.getReceivers().forEach(function(o){return o.track===u&&(p?s=!0:p=o),o.track===u}),s||c&&p?Promise.reject(new DOMException("There are more than one sender or receiver for the track.","InvalidAccessError")):c?c.getStats():p?p.getStats():Promise.reject(new DOMException("There is no sender or receiver for the track.","InvalidAccessError"))}return i.apply(this,arguments)}}}}function se(e){e.RTCPeerConnection.prototype.getLocalStreams=function(){var u=this;return this._shimmedLocalStreams=this._shimmedLocalStreams||{},Object.keys(this._shimmedLocalStreams).map(function(c){return u._shimmedLocalStreams[c][0]})};var t=e.RTCPeerConnection.prototype.addTrack;e.RTCPeerConnection.prototype.addTrack=function(u,c){if(!c)return t.apply(this,arguments);this._shimmedLocalStreams=this._shimmedLocalStreams||{};var p=t.apply(this,arguments);return this._shimmedLocalStreams[c.id]?this._shimmedLocalStreams[c.id].indexOf(p)===-1&&this._shimmedLocalStreams[c.id].push(p):this._shimmedLocalStreams[c.id]=[c,p],p};var r=e.RTCPeerConnection.prototype.addStream;e.RTCPeerConnection.prototype.addStream=function(u){var c=this;this._shimmedLocalStreams=this._shimmedLocalStreams||{},u.getTracks().forEach(function(o){var d=c.getSenders().find(function(m){return m.track===o});if(d)throw new DOMException("Track already exists.","InvalidAccessError")});var p=this.getSenders();r.apply(this,arguments);var s=this.getSenders().filter(function(o){return p.indexOf(o)===-1});this._shimmedLocalStreams[u.id]=[u].concat(s)};var n=e.RTCPeerConnection.prototype.removeStream;e.RTCPeerConnection.prototype.removeStream=function(u){return this._shimmedLocalStreams=this._shimmedLocalStreams||{},delete this._shimmedLocalStreams[u.id],n.apply(this,arguments)};var i=e.RTCPeerConnection.prototype.removeTrack;e.RTCPeerConnection.prototype.removeTrack=function(u){var c=this;return this._shimmedLocalStreams=this._shimmedLocalStreams||{},u&&Object.keys(this._shimmedLocalStreams).forEach(function(p){var s=c._shimmedLocalStreams[p].indexOf(u);s!==-1&&c._shimmedLocalStreams[p].splice(s,1),c._shimmedLocalStreams[p].length===1&&delete c._shimmedLocalStreams[p]}),i.apply(this,arguments)}}function Ke(e,t){if(!e.RTCPeerConnection)return;if(e.RTCPeerConnection.prototype.addTrack&&t.version>=65)return se(e);var r=e.RTCPeerConnection.prototype.getLocalStreams;e.RTCPeerConnection.prototype.getLocalStreams=function(){var o=this,d=r.apply(this);return this._reverseStreams=this._reverseStreams||{},d.map(function(m){return o._reverseStreams[m.id]})};var n=e.RTCPeerConnection.prototype.addStream;e.RTCPeerConnection.prototype.addStream=function(o){var d=this;if(this._streams=this._streams||{},this._reverseStreams=this._reverseStreams||{},o.getTracks().forEach(function(l){var v=d.getSenders().find(function(h){return h.track===l});if(v)throw new DOMException("Track already exists.","InvalidAccessError")}),!this._reverseStreams[o.id]){var m=new e.MediaStream(o.getTracks());this._streams[o.id]=m,this._reverseStreams[m.id]=o,o=m}n.apply(this,[o])};var i=e.RTCPeerConnection.prototype.removeStream;e.RTCPeerConnection.prototype.removeStream=function(o){this._streams=this._streams||{},this._reverseStreams=this._reverseStreams||{},i.apply(this,[this._streams[o.id]||o]),delete this._reverseStreams[this._streams[o.id]?this._streams[o.id].id:o.id],delete this._streams[o.id]},e.RTCPeerConnection.prototype.addTrack=function(o,d){var m=this;if(this.signalingState==="closed")throw new DOMException("The RTCPeerConnection's signalingState is 'closed'.","InvalidStateError");var l=[].slice.call(arguments,1);if(l.length!==1||!l[0].getTracks().find(function(g){return g===o}))throw new DOMException("The adapter.js addTrack polyfill only supports a single  stream which is associated with the specified track.","NotSupportedError");var v=this.getSenders().find(function(g){return g.track===o});if(v)throw new DOMException("Track already exists.","InvalidAccessError");this._streams=this._streams||{},this._reverseStreams=this._reverseStreams||{};var h=this._streams[d.id];if(h)h.addTrack(o),Promise.resolve().then(function(){m.dispatchEvent(new Event("negotiationneeded"))});else{var y=new e.MediaStream([o]);this._streams[d.id]=y,this._reverseStreams[y.id]=d,this.addStream(y)}return this.getSenders().find(function(g){return g.track===o})};function a(s,o){var d=o.sdp;return Object.keys(s._reverseStreams||[]).forEach(function(m){var l=s._reverseStreams[m],v=s._streams[l.id];d=d.replace(new RegExp(v.id,"g"),l.id)}),new RTCSessionDescription({type:o.type,sdp:d})}function u(s,o){var d=o.sdp;return Object.keys(s._reverseStreams||[]).forEach(function(m){var l=s._reverseStreams[m],v=s._streams[l.id];d=d.replace(new RegExp(l.id,"g"),v.id)}),new RTCSessionDescription({type:o.type,sdp:d})}["createOffer","createAnswer"].forEach(function(s){var o=e.RTCPeerConnection.prototype[s],d=ae({},s,function(){var m=this,l=arguments,v=arguments.length&&typeof arguments[0]=="function";return v?o.apply(this,[function(h){var y=a(m,h);l[0].apply(null,[y])},function(h){l[1]&&l[1].apply(null,h)},arguments[2]]):o.apply(this,arguments).then(function(h){return a(m,h)})});e.RTCPeerConnection.prototype[s]=d[s]});var c=e.RTCPeerConnection.prototype.setLocalDescription;e.RTCPeerConnection.prototype.setLocalDescription=function(){return!arguments.length||!arguments[0].type?c.apply(this,arguments):(arguments[0]=u(this,arguments[0]),c.apply(this,arguments))};var p=Object.getOwnPropertyDescriptor(e.RTCPeerConnection.prototype,"localDescription");Object.defineProperty(e.RTCPeerConnection.prototype,"localDescription",{get:function(){var o=p.get.apply(this);return o.type===""?o:a(this,o)}}),e.RTCPeerConnection.prototype.removeTrack=function(o){var d=this;if(this.signalingState==="closed")throw new DOMException("The RTCPeerConnection's signalingState is 'closed'.","InvalidStateError");if(!o._pc)throw new DOMException("Argument 1 of RTCPeerConnection.removeTrack does not implement interface RTCRtpSender.","TypeError");var m=o._pc===this;if(!m)throw new DOMException("Sender was not created by this connection.","InvalidAccessError");this._streams=this._streams||{};var l=void 0;Object.keys(this._streams).forEach(function(v){var h=d._streams[v].getTracks().find(function(y){return o.track===y});h&&(l=d._streams[v])}),l&&(l.getTracks().length===1?this.removeStream(this._reverseStreams[l.id]):l.removeTrack(o.track),this.dispatchEvent(new Event("negotiationneeded")))}}function We(e,t){!e.RTCPeerConnection&&e.webkitRTCPeerConnection&&(e.RTCPeerConnection=e.webkitRTCPeerConnection),!!e.RTCPeerConnection&&t.version<53&&["setLocalDescription","setRemoteDescription","addIceCandidate"].forEach(function(r){var n=e.RTCPeerConnection.prototype[r],i=ae({},r,function(){return arguments[0]=new(r==="addIceCandidate"?e.RTCIceCandidate:e.RTCSessionDescription)(arguments[0]),n.apply(this,arguments)});e.RTCPeerConnection.prototype[r]=i[r]})}function He(e,t){L.wrapPeerConnectionEvent(e,"negotiationneeded",function(r){var n=r.target;if(!((t.version<72||n.getConfiguration&&n.getConfiguration().sdpSemantics==="plan-b")&&n.signalingState!=="stable"))return r})}});var ce=P(H=>{"use strict";Object.defineProperty(H,"__esModule",{value:!0});var W=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(e){return typeof e}:function(e){return e&&typeof Symbol=="function"&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e};H.shimGetUserMedia=Qe;var Xe=x(),Ye=$e(Xe);function $e(e){if(e&&e.__esModule)return e;var t={};if(e!=null)for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r]);return t.default=e,t}function Qe(e,t){var r=e&&e.navigator,n=e&&e.MediaStreamTrack;if(r.getUserMedia=function(p,s,o){Ye.deprecated("navigator.getUserMedia","navigator.mediaDevices.getUserMedia"),r.mediaDevices.getUserMedia(p).then(s,o)},!(t.version>55&&"autoGainControl"in r.mediaDevices.getSupportedConstraints())){var i=function(s,o,d){o in s&&!(d in s)&&(s[d]=s[o],delete s[o])},a=r.mediaDevices.getUserMedia.bind(r.mediaDevices);if(r.mediaDevices.getUserMedia=function(p){return(typeof p>"u"?"undefined":W(p))==="object"&&W(p.audio)==="object"&&(p=JSON.parse(JSON.stringify(p)),i(p.audio,"autoGainControl","mozAutoGainControl"),i(p.audio,"noiseSuppression","mozNoiseSuppression")),a(p)},n&&n.prototype.getSettings){var u=n.prototype.getSettings;n.prototype.getSettings=function(){var p=u.apply(this,arguments);return i(p,"mozAutoGainControl","autoGainControl"),i(p,"mozNoiseSuppression","noiseSuppression"),p}}if(n&&n.prototype.applyConstraints){var c=n.prototype.applyConstraints;n.prototype.applyConstraints=function(p){return this.kind==="audio"&&(typeof p>"u"?"undefined":W(p))==="object"&&(p=JSON.parse(JSON.stringify(p)),i(p,"autoGainControl","mozAutoGainControl"),i(p,"noiseSuppression","mozNoiseSuppression")),c.apply(this,[p])}}}}});var ue=P(X=>{"use strict";Object.defineProperty(X,"__esModule",{value:!0});X.shimGetDisplayMedia=Ze;function Ze(e,t){e.navigator.mediaDevices&&"getDisplayMedia"in e.navigator.mediaDevices||!e.navigator.mediaDevices||(e.navigator.mediaDevices.getDisplayMedia=function(n){if(!(n&&n.video)){var i=new DOMException("getDisplayMedia without video constraints is undefined");return i.name="NotFoundError",i.code=8,Promise.reject(i)}return n.video===!0?n.video={mediaSource:t}:n.video.mediaSource=t,e.navigator.mediaDevices.getUserMedia(n)})}});var fe=P(R=>{"use strict";Object.defineProperty(R,"__esModule",{value:!0});R.shimGetDisplayMedia=R.shimGetUserMedia=void 0;var D=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(e){return typeof e}:function(e){return e&&typeof Symbol=="function"&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},we=ce();Object.defineProperty(R,"shimGetUserMedia",{enumerable:!0,get:function(){return we.shimGetUserMedia}});var et=ue();Object.defineProperty(R,"shimGetDisplayMedia",{enumerable:!0,get:function(){return et.shimGetDisplayMedia}});R.shimOnTrack=it;R.shimPeerConnection=at;R.shimSenderGetStats=st;R.shimReceiverGetStats=ot;R.shimRemoveStream=ct;R.shimRTCDataChannel=ut;R.shimAddTransceiver=pt;R.shimGetParameters=ft;R.shimCreateOffer=dt;R.shimCreateAnswer=mt;var tt=x(),pe=rt(tt);function rt(e){if(e&&e.__esModule)return e;var t={};if(e!=null)for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r]);return t.default=e,t}function nt(e,t,r){return t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function it(e){(typeof e>"u"?"undefined":D(e))==="object"&&e.RTCTrackEvent&&"receiver"in e.RTCTrackEvent.prototype&&!("transceiver"in e.RTCTrackEvent.prototype)&&Object.defineProperty(e.RTCTrackEvent.prototype,"transceiver",{get:function(){return{receiver:this.receiver}}})}function at(e,t){if(!((typeof e>"u"?"undefined":D(e))!=="object"||!(e.RTCPeerConnection||e.mozRTCPeerConnection))){!e.RTCPeerConnection&&e.mozRTCPeerConnection&&(e.RTCPeerConnection=e.mozRTCPeerConnection),t.version<53&&["setLocalDescription","setRemoteDescription","addIceCandidate"].forEach(function(i){var a=e.RTCPeerConnection.prototype[i],u=nt({},i,function(){return arguments[0]=new(i==="addIceCandidate"?e.RTCIceCandidate:e.RTCSessionDescription)(arguments[0]),a.apply(this,arguments)});e.RTCPeerConnection.prototype[i]=u[i]});var r={inboundrtp:"inbound-rtp",outboundrtp:"outbound-rtp",candidatepair:"candidate-pair",localcandidate:"local-candidate",remotecandidate:"remote-candidate"},n=e.RTCPeerConnection.prototype.getStats;e.RTCPeerConnection.prototype.getStats=function(){var a=Array.prototype.slice.call(arguments),u=a[0],c=a[1],p=a[2];return n.apply(this,[u||null]).then(function(s){if(t.version<53&&!c)try{s.forEach(function(o){o.type=r[o.type]||o.type})}catch(o){if(o.name!=="TypeError")throw o;s.forEach(function(d,m){s.set(m,Object.assign({},d,{type:r[d.type]||d.type}))})}return s}).then(c,p)}}}function st(e){if(!!((typeof e>"u"?"undefined":D(e))==="object"&&e.RTCPeerConnection&&e.RTCRtpSender)&&!(e.RTCRtpSender&&"getStats"in e.RTCRtpSender.prototype)){var t=e.RTCPeerConnection.prototype.getSenders;t&&(e.RTCPeerConnection.prototype.getSenders=function(){var i=this,a=t.apply(this,[]);return a.forEach(function(u){return u._pc=i}),a});var r=e.RTCPeerConnection.prototype.addTrack;r&&(e.RTCPeerConnection.prototype.addTrack=function(){var i=r.apply(this,arguments);return i._pc=this,i}),e.RTCRtpSender.prototype.getStats=function(){return this.track?this._pc.getStats(this.track):Promise.resolve(new Map)}}}function ot(e){if(!!((typeof e>"u"?"undefined":D(e))==="object"&&e.RTCPeerConnection&&e.RTCRtpSender)&&!(e.RTCRtpSender&&"getStats"in e.RTCRtpReceiver.prototype)){var t=e.RTCPeerConnection.prototype.getReceivers;t&&(e.RTCPeerConnection.prototype.getReceivers=function(){var n=this,i=t.apply(this,[]);return i.forEach(function(a){return a._pc=n}),i}),pe.wrapPeerConnectionEvent(e,"track",function(r){return r.receiver._pc=r.srcElement,r}),e.RTCRtpReceiver.prototype.getStats=function(){return this._pc.getStats(this.track)}}}function ct(e){!e.RTCPeerConnection||"removeStream"in e.RTCPeerConnection.prototype||(e.RTCPeerConnection.prototype.removeStream=function(r){var n=this;pe.deprecated("removeStream","removeTrack"),this.getSenders().forEach(function(i){i.track&&r.getTracks().includes(i.track)&&n.removeTrack(i)})})}function ut(e){e.DataChannel&&!e.RTCDataChannel&&(e.RTCDataChannel=e.DataChannel)}function pt(e){if(!!((typeof e>"u"?"undefined":D(e))==="object"&&e.RTCPeerConnection)){var t=e.RTCPeerConnection.prototype.addTransceiver;t&&(e.RTCPeerConnection.prototype.addTransceiver=function(){this.setParametersPromises=[];var n=arguments[1],i=n&&"sendEncodings"in n;i&&n.sendEncodings.forEach(function(p){if("rid"in p){var s=/^[a-z0-9]{0,16}$/i;if(!s.test(p.rid))throw new TypeError("Invalid RID value provided.")}if("scaleResolutionDownBy"in p&&!(parseFloat(p.scaleResolutionDownBy)>=1))throw new RangeError("scale_resolution_down_by must be >= 1.0");if("maxFramerate"in p&&!(parseFloat(p.maxFramerate)>=0))throw new RangeError("max_framerate must be >= 0.0")});var a=t.apply(this,arguments);if(i){var u=a.sender,c=u.getParameters();(!("encodings"in c)||c.encodings.length===1&&Object.keys(c.encodings[0]).length===0)&&(c.encodings=n.sendEncodings,u.sendEncodings=n.sendEncodings,this.setParametersPromises.push(u.setParameters(c).then(function(){delete u.sendEncodings}).catch(function(){delete u.sendEncodings})))}return a})}}function ft(e){if(!!((typeof e>"u"?"undefined":D(e))==="object"&&e.RTCRtpSender)){var t=e.RTCRtpSender.prototype.getParameters;t&&(e.RTCRtpSender.prototype.getParameters=function(){var n=t.apply(this,arguments);return"encodings"in n||(n.encodings=[].concat(this.sendEncodings||[{}])),n})}}function dt(e){if(!!((typeof e>"u"?"undefined":D(e))==="object"&&e.RTCPeerConnection)){var t=e.RTCPeerConnection.prototype.createOffer;e.RTCPeerConnection.prototype.createOffer=function(){var n=this,i=arguments;return this.setParametersPromises&&this.setParametersPromises.length?Promise.all(this.setParametersPromises).then(function(){return t.apply(n,i)}).finally(function(){n.setParametersPromises=[]}):t.apply(this,arguments)}}}function mt(e){if(!!((typeof e>"u"?"undefined":D(e))==="object"&&e.RTCPeerConnection)){var t=e.RTCPeerConnection.prototype.createAnswer;e.RTCPeerConnection.prototype.createAnswer=function(){var n=this,i=arguments;return this.setParametersPromises&&this.setParametersPromises.length?Promise.all(this.setParametersPromises).then(function(){return t.apply(n,i)}).finally(function(){n.setParametersPromises=[]}):t.apply(this,arguments)}}}});var le=P(M=>{"use strict";Object.defineProperty(M,"__esModule",{value:!0});var A=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(e){return typeof e}:function(e){return e&&typeof Symbol=="function"&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e};M.shimLocalStreamsAPI=vt;M.shimRemoteStreamsAPI=yt;M.shimCallbacksAPI=gt;M.shimGetUserMedia=Ct;M.shimConstraints=me;M.shimRTCIceServerUrls=St;M.shimTrackEventTransceiver=Rt;M.shimCreateOfferLegacy=Tt;M.shimAudioContext=Pt;var lt=x(),de=ht(lt);function ht(e){if(e&&e.__esModule)return e;var t={};if(e!=null)for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r]);return t.default=e,t}function vt(e){if(!((typeof e>"u"?"undefined":A(e))!=="object"||!e.RTCPeerConnection)){if("getLocalStreams"in e.RTCPeerConnection.prototype||(e.RTCPeerConnection.prototype.getLocalStreams=function(){return this._localStreams||(this._localStreams=[]),this._localStreams}),!("addStream"in e.RTCPeerConnection.prototype)){var t=e.RTCPeerConnection.prototype.addTrack;e.RTCPeerConnection.prototype.addStream=function(n){var i=this;this._localStreams||(this._localStreams=[]),this._localStreams.includes(n)||this._localStreams.push(n),n.getAudioTracks().forEach(function(a){return t.call(i,a,n)}),n.getVideoTracks().forEach(function(a){return t.call(i,a,n)})},e.RTCPeerConnection.prototype.addTrack=function(n){for(var i=this,a=arguments.length,u=Array(a>1?a-1:0),c=1;c<a;c++)u[c-1]=arguments[c];return u&&u.forEach(function(p){i._localStreams?i._localStreams.includes(p)||i._localStreams.push(p):i._localStreams=[p]}),t.apply(this,arguments)}}"removeStream"in e.RTCPeerConnection.prototype||(e.RTCPeerConnection.prototype.removeStream=function(n){var i=this;this._localStreams||(this._localStreams=[]);var a=this._localStreams.indexOf(n);if(a!==-1){this._localStreams.splice(a,1);var u=n.getTracks();this.getSenders().forEach(function(c){u.includes(c.track)&&i.removeTrack(c)})}})}}function yt(e){if(!((typeof e>"u"?"undefined":A(e))!=="object"||!e.RTCPeerConnection)&&("getRemoteStreams"in e.RTCPeerConnection.prototype||(e.RTCPeerConnection.prototype.getRemoteStreams=function(){return this._remoteStreams?this._remoteStreams:[]}),!("onaddstream"in e.RTCPeerConnection.prototype))){Object.defineProperty(e.RTCPeerConnection.prototype,"onaddstream",{get:function(){return this._onaddstream},set:function(n){var i=this;this._onaddstream&&(this.removeEventListener("addstream",this._onaddstream),this.removeEventListener("track",this._onaddstreampoly)),this.addEventListener("addstream",this._onaddstream=n),this.addEventListener("track",this._onaddstreampoly=function(a){a.streams.forEach(function(u){if(i._remoteStreams||(i._remoteStreams=[]),!i._remoteStreams.includes(u)){i._remoteStreams.push(u);var c=new Event("addstream");c.stream=u,i.dispatchEvent(c)}})})}});var t=e.RTCPeerConnection.prototype.setRemoteDescription;e.RTCPeerConnection.prototype.setRemoteDescription=function(){var n=this;return this._onaddstreampoly||this.addEventListener("track",this._onaddstreampoly=function(i){i.streams.forEach(function(a){if(n._remoteStreams||(n._remoteStreams=[]),!(n._remoteStreams.indexOf(a)>=0)){n._remoteStreams.push(a);var u=new Event("addstream");u.stream=a,n.dispatchEvent(u)}})}),t.apply(n,arguments)}}}function gt(e){if(!((typeof e>"u"?"undefined":A(e))!=="object"||!e.RTCPeerConnection)){var t=e.RTCPeerConnection.prototype,r=t.createOffer,n=t.createAnswer,i=t.setLocalDescription,a=t.setRemoteDescription,u=t.addIceCandidate;t.createOffer=function(s,o){var d=arguments.length>=2?arguments[2]:arguments[0],m=r.apply(this,[d]);return o?(m.then(s,o),Promise.resolve()):m},t.createAnswer=function(s,o){var d=arguments.length>=2?arguments[2]:arguments[0],m=n.apply(this,[d]);return o?(m.then(s,o),Promise.resolve()):m};var c=function(s,o,d){var m=i.apply(this,[s]);return d?(m.then(o,d),Promise.resolve()):m};t.setLocalDescription=c,c=function(s,o,d){var m=a.apply(this,[s]);return d?(m.then(o,d),Promise.resolve()):m},t.setRemoteDescription=c,c=function(s,o,d){var m=u.apply(this,[s]);return d?(m.then(o,d),Promise.resolve()):m},t.addIceCandidate=c}}function Ct(e){var t=e&&e.navigator;if(t.mediaDevices&&t.mediaDevices.getUserMedia){var r=t.mediaDevices,n=r.getUserMedia.bind(r);t.mediaDevices.getUserMedia=function(i){return n(me(i))}}!t.getUserMedia&&t.mediaDevices&&t.mediaDevices.getUserMedia&&(t.getUserMedia=function(a,u,c){t.mediaDevices.getUserMedia(a).then(u,c)}.bind(t))}function me(e){return e&&e.video!==void 0?Object.assign({},e,{video:de.compactObject(e.video)}):e}function St(e){if(!!e.RTCPeerConnection){var t=e.RTCPeerConnection;e.RTCPeerConnection=function(n,i){if(n&&n.iceServers){for(var a=[],u=0;u<n.iceServers.length;u++){var c=n.iceServers[u];!c.hasOwnProperty("urls")&&c.hasOwnProperty("url")?(de.deprecated("RTCIceServer.url","RTCIceServer.urls"),c=JSON.parse(JSON.stringify(c)),c.urls=c.url,delete c.url,a.push(c)):a.push(n.iceServers[u])}n.iceServers=a}return new t(n,i)},e.RTCPeerConnection.prototype=t.prototype,"generateCertificate"in t&&Object.defineProperty(e.RTCPeerConnection,"generateCertificate",{get:function(){return t.generateCertificate}})}}function Rt(e){(typeof e>"u"?"undefined":A(e))==="object"&&e.RTCTrackEvent&&"receiver"in e.RTCTrackEvent.prototype&&!("transceiver"in e.RTCTrackEvent.prototype)&&Object.defineProperty(e.RTCTrackEvent.prototype,"transceiver",{get:function(){return{receiver:this.receiver}}})}function Tt(e){var t=e.RTCPeerConnection.prototype.createOffer;e.RTCPeerConnection.prototype.createOffer=function(n){if(n){typeof n.offerToReceiveAudio<"u"&&(n.offerToReceiveAudio=!!n.offerToReceiveAudio);var i=this.getTransceivers().find(function(u){return u.receiver.track.kind==="audio"});n.offerToReceiveAudio===!1&&i?i.direction==="sendrecv"?i.setDirection?i.setDirection("sendonly"):i.direction="sendonly":i.direction==="recvonly"&&(i.setDirection?i.setDirection("inactive"):i.direction="inactive"):n.offerToReceiveAudio===!0&&!i&&this.addTransceiver("audio",{direction:"recvonly"}),typeof n.offerToReceiveVideo<"u"&&(n.offerToReceiveVideo=!!n.offerToReceiveVideo);var a=this.getTransceivers().find(function(u){return u.receiver.track.kind==="video"});n.offerToReceiveVideo===!1&&a?a.direction==="sendrecv"?a.setDirection?a.setDirection("sendonly"):a.direction="sendonly":a.direction==="recvonly"&&(a.setDirection?a.setDirection("inactive"):a.direction="inactive"):n.offerToReceiveVideo===!0&&!a&&this.addTransceiver("video",{direction:"recvonly"})}return t.apply(this,arguments)}}function Pt(e){(typeof e>"u"?"undefined":A(e))!=="object"||e.AudioContext||(e.AudioContext=e.webkitAudioContext)}});var Y=P((rr,q)=>{"use strict";var he=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(e){return typeof e}:function(e){return e&&typeof Symbol=="function"&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},f={};f.generateIdentifier=function(){return Math.random().toString(36).substr(2,10)};f.localCName=f.generateIdentifier();f.splitLines=function(e){return e.trim().split(`
`).map(function(t){return t.trim()})};f.splitSections=function(e){var t=e.split(`
m=`);return t.map(function(r,n){return(n>0?"m="+r:r).trim()+`\r
`})};f.getDescription=function(e){var t=f.splitSections(e);return t&&t[0]};f.getMediaSections=function(e){var t=f.splitSections(e);return t.shift(),t};f.matchPrefix=function(e,t){return f.splitLines(e).filter(function(r){return r.indexOf(t)===0})};f.parseCandidate=function(e){var t=void 0;e.indexOf("a=candidate:")===0?t=e.substring(12).split(" "):t=e.substring(10).split(" ");for(var r={foundation:t[0],component:{1:"rtp",2:"rtcp"}[t[1]]||t[1],protocol:t[2].toLowerCase(),priority:parseInt(t[3],10),ip:t[4],address:t[4],port:parseInt(t[5],10),type:t[7]},n=8;n<t.length;n+=2)switch(t[n]){case"raddr":r.relatedAddress=t[n+1];break;case"rport":r.relatedPort=parseInt(t[n+1],10);break;case"tcptype":r.tcpType=t[n+1];break;case"ufrag":r.ufrag=t[n+1],r.usernameFragment=t[n+1];break;default:r[t[n]]===void 0&&(r[t[n]]=t[n+1]);break}return r};f.writeCandidate=function(e){var t=[];t.push(e.foundation);var r=e.component;r==="rtp"?t.push(1):r==="rtcp"?t.push(2):t.push(r),t.push(e.protocol.toUpperCase()),t.push(e.priority),t.push(e.address||e.ip),t.push(e.port);var n=e.type;return t.push("typ"),t.push(n),n!=="host"&&e.relatedAddress&&e.relatedPort&&(t.push("raddr"),t.push(e.relatedAddress),t.push("rport"),t.push(e.relatedPort)),e.tcpType&&e.protocol.toLowerCase()==="tcp"&&(t.push("tcptype"),t.push(e.tcpType)),(e.usernameFragment||e.ufrag)&&(t.push("ufrag"),t.push(e.usernameFragment||e.ufrag)),"candidate:"+t.join(" ")};f.parseIceOptions=function(e){return e.substr(14).split(" ")};f.parseRtpMap=function(e){var t=e.substr(9).split(" "),r={payloadType:parseInt(t.shift(),10)};return t=t[0].split("/"),r.name=t[0],r.clockRate=parseInt(t[1],10),r.channels=t.length===3?parseInt(t[2],10):1,r.numChannels=r.channels,r};f.writeRtpMap=function(e){var t=e.payloadType;e.preferredPayloadType!==void 0&&(t=e.preferredPayloadType);var r=e.channels||e.numChannels||1;return"a=rtpmap:"+t+" "+e.name+"/"+e.clockRate+(r!==1?"/"+r:"")+`\r
`};f.parseExtmap=function(e){var t=e.substr(9).split(" ");return{id:parseInt(t[0],10),direction:t[0].indexOf("/")>0?t[0].split("/")[1]:"sendrecv",uri:t[1]}};f.writeExtmap=function(e){return"a=extmap:"+(e.id||e.preferredId)+(e.direction&&e.direction!=="sendrecv"?"/"+e.direction:"")+" "+e.uri+`\r
`};f.parseFmtp=function(e){for(var t={},r=void 0,n=e.substr(e.indexOf(" ")+1).split(";"),i=0;i<n.length;i++)r=n[i].trim().split("="),t[r[0].trim()]=r[1];return t};f.writeFmtp=function(e){var t="",r=e.payloadType;if(e.preferredPayloadType!==void 0&&(r=e.preferredPayloadType),e.parameters&&Object.keys(e.parameters).length){var n=[];Object.keys(e.parameters).forEach(function(i){e.parameters[i]!==void 0?n.push(i+"="+e.parameters[i]):n.push(i)}),t+="a=fmtp:"+r+" "+n.join(";")+`\r
`}return t};f.parseRtcpFb=function(e){var t=e.substr(e.indexOf(" ")+1).split(" ");return{type:t.shift(),parameter:t.join(" ")}};f.writeRtcpFb=function(e){var t="",r=e.payloadType;return e.preferredPayloadType!==void 0&&(r=e.preferredPayloadType),e.rtcpFeedback&&e.rtcpFeedback.length&&e.rtcpFeedback.forEach(function(n){t+="a=rtcp-fb:"+r+" "+n.type+(n.parameter&&n.parameter.length?" "+n.parameter:"")+`\r
`}),t};f.parseSsrcMedia=function(e){var t=e.indexOf(" "),r={ssrc:parseInt(e.substr(7,t-7),10)},n=e.indexOf(":",t);return n>-1?(r.attribute=e.substr(t+1,n-t-1),r.value=e.substr(n+1)):r.attribute=e.substr(t+1),r};f.parseSsrcGroup=function(e){var t=e.substr(13).split(" ");return{semantics:t.shift(),ssrcs:t.map(function(r){return parseInt(r,10)})}};f.getMid=function(e){var t=f.matchPrefix(e,"a=mid:")[0];if(t)return t.substr(6)};f.parseFingerprint=function(e){var t=e.substr(14).split(" ");return{algorithm:t[0].toLowerCase(),value:t[1].toUpperCase()}};f.getDtlsParameters=function(e,t){var r=f.matchPrefix(e+t,"a=fingerprint:");return{role:"auto",fingerprints:r.map(f.parseFingerprint)}};f.writeDtlsParameters=function(e,t){var r="a=setup:"+t+`\r
`;return e.fingerprints.forEach(function(n){r+="a=fingerprint:"+n.algorithm+" "+n.value+`\r
`}),r};f.parseCryptoLine=function(e){var t=e.substr(9).split(" ");return{tag:parseInt(t[0],10),cryptoSuite:t[1],keyParams:t[2],sessionParams:t.slice(3)}};f.writeCryptoLine=function(e){return"a=crypto:"+e.tag+" "+e.cryptoSuite+" "+(he(e.keyParams)==="object"?f.writeCryptoKeyParams(e.keyParams):e.keyParams)+(e.sessionParams?" "+e.sessionParams.join(" "):"")+`\r
`};f.parseCryptoKeyParams=function(e){if(e.indexOf("inline:")!==0)return null;var t=e.substr(7).split("|");return{keyMethod:"inline",keySalt:t[0],lifeTime:t[1],mkiValue:t[2]?t[2].split(":")[0]:void 0,mkiLength:t[2]?t[2].split(":")[1]:void 0}};f.writeCryptoKeyParams=function(e){return e.keyMethod+":"+e.keySalt+(e.lifeTime?"|"+e.lifeTime:"")+(e.mkiValue&&e.mkiLength?"|"+e.mkiValue+":"+e.mkiLength:"")};f.getCryptoParameters=function(e,t){var r=f.matchPrefix(e+t,"a=crypto:");return r.map(f.parseCryptoLine)};f.getIceParameters=function(e,t){var r=f.matchPrefix(e+t,"a=ice-ufrag:")[0],n=f.matchPrefix(e+t,"a=ice-pwd:")[0];return r&&n?{usernameFragment:r.substr(12),password:n.substr(10)}:null};f.writeIceParameters=function(e){var t="a=ice-ufrag:"+e.usernameFragment+`\r
a=ice-pwd:`+e.password+`\r
`;return e.iceLite&&(t+=`a=ice-lite\r
`),t};f.parseRtpParameters=function(e){for(var t={codecs:[],headerExtensions:[],fecMechanisms:[],rtcp:[]},r=f.splitLines(e),n=r[0].split(" "),i=3;i<n.length;i++){var a=n[i],u=f.matchPrefix(e,"a=rtpmap:"+a+" ")[0];if(u){var c=f.parseRtpMap(u),p=f.matchPrefix(e,"a=fmtp:"+a+" ");switch(c.parameters=p.length?f.parseFmtp(p[0]):{},c.rtcpFeedback=f.matchPrefix(e,"a=rtcp-fb:"+a+" ").map(f.parseRtcpFb),t.codecs.push(c),c.name.toUpperCase()){case"RED":case"ULPFEC":t.fecMechanisms.push(c.name.toUpperCase());break;default:break}}}return f.matchPrefix(e,"a=extmap:").forEach(function(s){t.headerExtensions.push(f.parseExtmap(s))}),t};f.writeRtpDescription=function(e,t){var r="";r+="m="+e+" ",r+=t.codecs.length>0?"9":"0",r+=" UDP/TLS/RTP/SAVPF ",r+=t.codecs.map(function(i){return i.preferredPayloadType!==void 0?i.preferredPayloadType:i.payloadType}).join(" ")+`\r
`,r+=`c=IN IP4 0.0.0.0\r
`,r+=`a=rtcp:9 IN IP4 0.0.0.0\r
`,t.codecs.forEach(function(i){r+=f.writeRtpMap(i),r+=f.writeFmtp(i),r+=f.writeRtcpFb(i)});var n=0;return t.codecs.forEach(function(i){i.maxptime>n&&(n=i.maxptime)}),n>0&&(r+="a=maxptime:"+n+`\r
`),t.headerExtensions&&t.headerExtensions.forEach(function(i){r+=f.writeExtmap(i)}),r};f.parseRtpEncodingParameters=function(e){var t=[],r=f.parseRtpParameters(e),n=r.fecMechanisms.indexOf("RED")!==-1,i=r.fecMechanisms.indexOf("ULPFEC")!==-1,a=f.matchPrefix(e,"a=ssrc:").map(function(o){return f.parseSsrcMedia(o)}).filter(function(o){return o.attribute==="cname"}),u=a.length>0&&a[0].ssrc,c=void 0,p=f.matchPrefix(e,"a=ssrc-group:FID").map(function(o){var d=o.substr(17).split(" ");return d.map(function(m){return parseInt(m,10)})});p.length>0&&p[0].length>1&&p[0][0]===u&&(c=p[0][1]),r.codecs.forEach(function(o){if(o.name.toUpperCase()==="RTX"&&o.parameters.apt){var d={ssrc:u,codecPayloadType:parseInt(o.parameters.apt,10)};u&&c&&(d.rtx={ssrc:c}),t.push(d),n&&(d=JSON.parse(JSON.stringify(d)),d.fec={ssrc:u,mechanism:i?"red+ulpfec":"red"},t.push(d))}}),t.length===0&&u&&t.push({ssrc:u});var s=f.matchPrefix(e,"b=");return s.length&&(s[0].indexOf("b=TIAS:")===0?s=parseInt(s[0].substr(7),10):s[0].indexOf("b=AS:")===0?s=parseInt(s[0].substr(5),10)*1e3*.95-50*40*8:s=void 0,t.forEach(function(o){o.maxBitrate=s})),t};f.parseRtcpParameters=function(e){var t={},r=f.matchPrefix(e,"a=ssrc:").map(function(a){return f.parseSsrcMedia(a)}).filter(function(a){return a.attribute==="cname"})[0];r&&(t.cname=r.value,t.ssrc=r.ssrc);var n=f.matchPrefix(e,"a=rtcp-rsize");t.reducedSize=n.length>0,t.compound=n.length===0;var i=f.matchPrefix(e,"a=rtcp-mux");return t.mux=i.length>0,t};f.writeRtcpParameters=function(e){var t="";return e.reducedSize&&(t+=`a=rtcp-rsize\r
`),e.mux&&(t+=`a=rtcp-mux\r
`),e.ssrc!==void 0&&e.cname&&(t+="a=ssrc:"+e.ssrc+" cname:"+e.cname+`\r
`),t};f.parseMsid=function(e){var t=void 0,r=f.matchPrefix(e,"a=msid:");if(r.length===1)return t=r[0].substr(7).split(" "),{stream:t[0],track:t[1]};var n=f.matchPrefix(e,"a=ssrc:").map(function(i){return f.parseSsrcMedia(i)}).filter(function(i){return i.attribute==="msid"});if(n.length>0)return t=n[0].value.split(" "),{stream:t[0],track:t[1]}};f.parseSctpDescription=function(e){var t=f.parseMLine(e),r=f.matchPrefix(e,"a=max-message-size:"),n=void 0;r.length>0&&(n=parseInt(r[0].substr(19),10)),isNaN(n)&&(n=65536);var i=f.matchPrefix(e,"a=sctp-port:");if(i.length>0)return{port:parseInt(i[0].substr(12),10),protocol:t.fmt,maxMessageSize:n};var a=f.matchPrefix(e,"a=sctpmap:");if(a.length>0){var u=a[0].substr(10).split(" ");return{port:parseInt(u[0],10),protocol:u[1],maxMessageSize:n}}};f.writeSctpDescription=function(e,t){var r=[];return e.protocol!=="DTLS/SCTP"?r=["m="+e.kind+" 9 "+e.protocol+" "+t.protocol+`\r
`,`c=IN IP4 0.0.0.0\r
`,"a=sctp-port:"+t.port+`\r
`]:r=["m="+e.kind+" 9 "+e.protocol+" "+t.port+`\r
`,`c=IN IP4 0.0.0.0\r
`,"a=sctpmap:"+t.port+" "+t.protocol+` 65535\r
`],t.maxMessageSize!==void 0&&r.push("a=max-message-size:"+t.maxMessageSize+`\r
`),r.join("")};f.generateSessionId=function(){return Math.random().toString().substr(2,21)};f.writeSessionBoilerplate=function(e,t,r){var n=void 0,i=t!==void 0?t:2;e?n=e:n=f.generateSessionId();var a=r||"thisisadapterortc";return`v=0\r
o=`+a+" "+n+" "+i+` IN IP4 127.0.0.1\r
s=-\r
t=0 0\r
`};f.getDirection=function(e,t){for(var r=f.splitLines(e),n=0;n<r.length;n++)switch(r[n]){case"a=sendrecv":case"a=sendonly":case"a=recvonly":case"a=inactive":return r[n].substr(2);default:}return t?f.getDirection(t):"sendrecv"};f.getKind=function(e){var t=f.splitLines(e),r=t[0].split(" ");return r[0].substr(2)};f.isRejected=function(e){return e.split(" ",2)[1]==="0"};f.parseMLine=function(e){var t=f.splitLines(e),r=t[0].substr(2).split(" ");return{kind:r[0],port:parseInt(r[1],10),protocol:r[2],fmt:r.slice(3).join(" ")}};f.parseOLine=function(e){var t=f.matchPrefix(e,"o=")[0],r=t.substr(2).split(" ");return{username:r[0],sessionId:r[1],sessionVersion:parseInt(r[2],10),netType:r[3],addressType:r[4],address:r[5]}};f.isValidSDP=function(e){if(typeof e!="string"||e.length===0)return!1;for(var t=f.splitLines(e),r=0;r<t.length;r++)if(t[r].length<2||t[r].charAt(1)!=="=")return!1;return!0};(typeof q>"u"?"undefined":he(q))==="object"&&(q.exports=f)});var ge=P(k=>{"use strict";Object.defineProperty(k,"__esModule",{value:!0});var ve=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(e){return typeof e}:function(e){return e&&typeof Symbol=="function"&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e};k.shimRTCIceCandidate=kt;k.shimMaxMessageSize=xt;k.shimSendThrowTypeError=Dt;k.shimConnectionState=Ot;k.removeExtmapAllowMixed=It;k.shimAddIceCandidateNullOrEmpty=Lt;k.shimParameterlessSetLocalDescription=At;var _t=Y(),V=bt(_t),Et=x(),ye=Mt(Et);function Mt(e){if(e&&e.__esModule)return e;var t={};if(e!=null)for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r]);return t.default=e,t}function bt(e){return e&&e.__esModule?e:{default:e}}function kt(e){if(!(!e.RTCIceCandidate||e.RTCIceCandidate&&"foundation"in e.RTCIceCandidate.prototype)){var t=e.RTCIceCandidate;e.RTCIceCandidate=function(n){if((typeof n>"u"?"undefined":ve(n))==="object"&&n.candidate&&n.candidate.indexOf("a=")===0&&(n=JSON.parse(JSON.stringify(n)),n.candidate=n.candidate.substr(2)),n.candidate&&n.candidate.length){var i=new t(n),a=V.default.parseCandidate(n.candidate),u=Object.assign(i,a);return u.toJSON=function(){return{candidate:u.candidate,sdpMid:u.sdpMid,sdpMLineIndex:u.sdpMLineIndex,usernameFragment:u.usernameFragment}},u}return new t(n)},e.RTCIceCandidate.prototype=t.prototype,ye.wrapPeerConnectionEvent(e,"icecandidate",function(r){return r.candidate&&Object.defineProperty(r,"candidate",{value:new e.RTCIceCandidate(r.candidate),writable:"false"}),r})}}function xt(e,t){if(!!e.RTCPeerConnection){"sctp"in e.RTCPeerConnection.prototype||Object.defineProperty(e.RTCPeerConnection.prototype,"sctp",{get:function(){return typeof this._sctp>"u"?null:this._sctp}});var r=function(p){if(!p||!p.sdp)return!1;var s=V.default.splitSections(p.sdp);return s.shift(),s.some(function(o){var d=V.default.parseMLine(o);return d&&d.kind==="application"&&d.protocol.indexOf("SCTP")!==-1})},n=function(p){var s=p.sdp.match(/mozilla...THIS_IS_SDPARTA-(\d+)/);if(s===null||s.length<2)return-1;var o=parseInt(s[1],10);return o!==o?-1:o},i=function(p){var s=65536;return t.browser==="firefox"&&(t.version<57?p===-1?s=16384:s=2147483637:t.version<60?s=t.version===57?65535:65536:s=2147483637),s},a=function(p,s){var o=65536;t.browser==="firefox"&&t.version===57&&(o=65535);var d=V.default.matchPrefix(p.sdp,"a=max-message-size:");return d.length>0?o=parseInt(d[0].substr(19),10):t.browser==="firefox"&&s!==-1&&(o=2147483637),o},u=e.RTCPeerConnection.prototype.setRemoteDescription;e.RTCPeerConnection.prototype.setRemoteDescription=function(){if(this._sctp=null,t.browser==="chrome"&&t.version>=76){var p=this.getConfiguration(),s=p.sdpSemantics;s==="plan-b"&&Object.defineProperty(this,"sctp",{get:function(){return typeof this._sctp>"u"?null:this._sctp},enumerable:!0,configurable:!0})}if(r(arguments[0])){var o=n(arguments[0]),d=i(o),m=a(arguments[0],o),l=void 0;d===0&&m===0?l=Number.POSITIVE_INFINITY:d===0||m===0?l=Math.max(d,m):l=Math.min(d,m);var v={};Object.defineProperty(v,"maxMessageSize",{get:function(){return l}}),this._sctp=v}return u.apply(this,arguments)}}}function Dt(e){if(!(e.RTCPeerConnection&&"createDataChannel"in e.RTCPeerConnection.prototype))return;function t(n,i){var a=n.send;n.send=function(){var c=arguments[0],p=c.length||c.size||c.byteLength;if(n.readyState==="open"&&i.sctp&&p>i.sctp.maxMessageSize)throw new TypeError("Message too large (can send a maximum of "+i.sctp.maxMessageSize+" bytes)");return a.apply(n,arguments)}}var r=e.RTCPeerConnection.prototype.createDataChannel;e.RTCPeerConnection.prototype.createDataChannel=function(){var i=r.apply(this,arguments);return t(i,this),i},ye.wrapPeerConnectionEvent(e,"datachannel",function(n){return t(n.channel,n.target),n})}function Ot(e){if(!(!e.RTCPeerConnection||"connectionState"in e.RTCPeerConnection.prototype)){var t=e.RTCPeerConnection.prototype;Object.defineProperty(t,"connectionState",{get:function(){return{completed:"connected",checking:"connecting"}[this.iceConnectionState]||this.iceConnectionState},enumerable:!0,configurable:!0}),Object.defineProperty(t,"onconnectionstatechange",{get:function(){return this._onconnectionstatechange||null},set:function(n){this._onconnectionstatechange&&(this.removeEventListener("connectionstatechange",this._onconnectionstatechange),delete this._onconnectionstatechange),n&&this.addEventListener("connectionstatechange",this._onconnectionstatechange=n)},enumerable:!0,configurable:!0}),["setLocalDescription","setRemoteDescription"].forEach(function(r){var n=t[r];t[r]=function(){return this._connectionstatechangepoly||(this._connectionstatechangepoly=function(i){var a=i.target;if(a._lastConnectionState!==a.connectionState){a._lastConnectionState=a.connectionState;var u=new Event("connectionstatechange",i);a.dispatchEvent(u)}return i},this.addEventListener("iceconnectionstatechange",this._connectionstatechangepoly)),n.apply(this,arguments)}})}}function It(e,t){if(!!e.RTCPeerConnection&&!(t.browser==="chrome"&&t.version>=71)&&!(t.browser==="safari"&&t.version>=605)){var r=e.RTCPeerConnection.prototype.setRemoteDescription;e.RTCPeerConnection.prototype.setRemoteDescription=function(i){if(i&&i.sdp&&i.sdp.indexOf(`
a=extmap-allow-mixed`)!==-1){var a=i.sdp.split(`
`).filter(function(u){return u.trim()!=="a=extmap-allow-mixed"}).join(`
`);e.RTCSessionDescription&&i instanceof e.RTCSessionDescription?arguments[0]=new e.RTCSessionDescription({type:i.type,sdp:a}):i.sdp=a}return r.apply(this,arguments)}}}function Lt(e,t){if(!!(e.RTCPeerConnection&&e.RTCPeerConnection.prototype)){var r=e.RTCPeerConnection.prototype.addIceCandidate;!r||r.length===0||(e.RTCPeerConnection.prototype.addIceCandidate=function(){return arguments[0]?(t.browser==="chrome"&&t.version<78||t.browser==="firefox"&&t.version<68||t.browser==="safari")&&arguments[0]&&arguments[0].candidate===""?Promise.resolve():r.apply(this,arguments):(arguments[1]&&arguments[1].apply(null),Promise.resolve())})}}function At(e,t){if(!!(e.RTCPeerConnection&&e.RTCPeerConnection.prototype)){var r=e.RTCPeerConnection.prototype.setLocalDescription;!r||r.length===0||(e.RTCPeerConnection.prototype.setLocalDescription=function(){var i=this,a=arguments[0]||{};if((typeof a>"u"?"undefined":ve(a))!=="object"||a.type&&a.sdp)return r.apply(this,arguments);if(a={type:a.type,sdp:a.sdp},!a.type)switch(this.signalingState){case"stable":case"have-local-offer":case"have-remote-pranswer":a.type="offer";break;default:a.type="answer";break}if(a.sdp||a.type!=="offer"&&a.type!=="answer")return r.apply(this,[a]);var u=a.type==="offer"?this.createOffer:this.createAnswer;return u.apply(this).then(function(c){return r.apply(i,[c])})})}}});var Ce=P($=>{"use strict";Object.defineProperty($,"__esModule",{value:!0});$.adapterFactory=Jt;var Gt=x(),G=O(Gt),Ut=oe(),_=O(Ut),Ft=fe(),T=O(Ft),Nt=le(),b=O(Nt),zt=ge(),C=O(zt),qt=Y(),Vt=O(qt);function O(e){if(e&&e.__esModule)return e;var t={};if(e!=null)for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r]);return t.default=e,t}function Jt(){var e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},t=e.window,r=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{shimChrome:!0,shimFirefox:!0,shimSafari:!0},n=G.log,i=G.detectBrowser(t),a={browserDetails:i,commonShim:C,extractVersion:G.extractVersion,disableLog:G.disableLog,disableWarnings:G.disableWarnings,sdp:Vt};switch(i.browser){case"chrome":if(!_||!_.shimPeerConnection||!r.shimChrome)return n("Chrome shim is not included in this adapter release."),a;if(i.version===null)return n("Chrome shim can not determine version, not shimming."),a;n("adapter.js shimming chrome."),a.browserShim=_,C.shimAddIceCandidateNullOrEmpty(t,i),C.shimParameterlessSetLocalDescription(t,i),_.shimGetUserMedia(t,i),_.shimMediaStream(t,i),_.shimPeerConnection(t,i),_.shimOnTrack(t,i),_.shimAddTrackRemoveTrack(t,i),_.shimGetSendersWithDtmf(t,i),_.shimGetStats(t,i),_.shimSenderReceiverGetStats(t,i),_.fixNegotiationNeeded(t,i),_.shimSelectAudioOutput(t,i),C.shimRTCIceCandidate(t,i),C.shimConnectionState(t,i),C.shimMaxMessageSize(t,i),C.shimSendThrowTypeError(t,i),C.removeExtmapAllowMixed(t,i);break;case"firefox":if(!T||!T.shimPeerConnection||!r.shimFirefox)return n("Firefox shim is not included in this adapter release."),a;n("adapter.js shimming firefox."),a.browserShim=T,C.shimAddIceCandidateNullOrEmpty(t,i),C.shimParameterlessSetLocalDescription(t,i),T.shimGetUserMedia(t,i),T.shimPeerConnection(t,i),T.shimOnTrack(t,i),T.shimRemoveStream(t,i),T.shimSenderGetStats(t,i),T.shimReceiverGetStats(t,i),T.shimRTCDataChannel(t,i),T.shimAddTransceiver(t,i),T.shimGetParameters(t,i),T.shimCreateOffer(t,i),T.shimCreateAnswer(t,i),C.shimRTCIceCandidate(t,i),C.shimConnectionState(t,i),C.shimMaxMessageSize(t,i),C.shimSendThrowTypeError(t,i);break;case"safari":if(!b||!r.shimSafari)return n("Safari shim is not included in this adapter release."),a;n("adapter.js shimming safari."),a.browserShim=b,C.shimAddIceCandidateNullOrEmpty(t,i),C.shimParameterlessSetLocalDescription(t,i),b.shimRTCIceServerUrls(t,i),b.shimCreateOfferLegacy(t,i),b.shimCallbacksAPI(t,i),b.shimLocalStreamsAPI(t,i),b.shimRemoteStreamsAPI(t,i),b.shimTrackEventTransceiver(t,i),b.shimGetUserMedia(t,i),b.shimAudioContext(t,i),C.shimRTCIceCandidate(t,i),C.shimMaxMessageSize(t,i),C.shimSendThrowTypeError(t,i),C.removeExtmapAllowMixed(t,i);break;default:n("Unsupported browser!");break}return a}});var Kt=P((ar,Se)=>{var jt=Ce(),Bt=(0,jt.adapterFactory)({window:typeof window>"u"?void 0:window});Se.exports=Bt});"use strict";Kt();})();
