{"version": 3, "file": "index.js", "sources": ["../../src/platform_react_native/persistence/react_native.ts", "../../index.rn.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { Persistence, ReactNativeAsyncStorage } from '../../model/public_types';\n\nimport {\n  PersistenceInternal,\n  PersistenceType,\n  PersistenceValue,\n  STORAGE_AVAILABLE_KEY,\n  StorageEventListener\n} from '../../core/persistence';\n\n/**\n * Returns a persistence object that wraps `AsyncStorage` imported from\n * `react-native` or `@react-native-community/async-storage`, and can\n * be used in the persistence dependency field in {@link initializeAuth}.\n *\n * @public\n */\nexport function getReactNativePersistence(\n  storage: ReactNativeAsyncStorage\n): Persistence {\n  // In the _getInstance() implementation (see src/core/persistence/index.ts),\n  // we expect each \"externs.Persistence\" object passed to us by the user to\n  // be able to be instantiated (as a class) using \"new\". That function also\n  // expects the constructor to be empty. Since ReactNativeStorage requires the\n  // underlying storage layer, we need to be able to create subclasses\n  // (closures, essentially) that have the storage layer but empty constructor.\n  return class implements PersistenceInternal {\n    static type: 'LOCAL' = 'LOCAL';\n    readonly type: PersistenceType = PersistenceType.LOCAL;\n\n    async _isAvailable(): Promise<boolean> {\n      try {\n        if (!storage) {\n          return false;\n        }\n        await storage.setItem(STORAGE_AVAILABLE_KEY, '1');\n        await storage.removeItem(STORAGE_AVAILABLE_KEY);\n        return true;\n      } catch {\n        return false;\n      }\n    }\n\n    _set(key: string, value: PersistenceValue): Promise<void> {\n      return storage.setItem(key, JSON.stringify(value));\n    }\n\n    async _get<T extends PersistenceValue>(key: string): Promise<T | null> {\n      const json = await storage.getItem(key);\n      return json ? JSON.parse(json) : null;\n    }\n\n    _remove(key: string): Promise<void> {\n      return storage.removeItem(key);\n    }\n\n    _addListener(_key: string, _listener: StorageEventListener): void {\n      // Listeners are not supported for React Native storage.\n      return;\n    }\n\n    _removeListener(_key: string, _listener: StorageEventListener): void {\n      // Listeners are not supported for React Native storage.\n      return;\n    }\n  };\n}\n", "/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n/**\n * This is the file that people using React Native will actually import. You\n * should only include this file if you have something specific about your\n * implementation that mandates having a separate entrypoint. Otherwise you can\n * just use index.ts\n */\n\nimport { FirebaseApp, getApp, _getProvider } from '@firebase/app';\nimport { Auth, Dependencies } from './src/model/public_types';\n\nimport { initializeAuth as initializeAuthOriginal } from './src';\nimport { registerAuth } from './src/core/auth/register';\nimport { ClientPlatform } from './src/core/util/version';\nimport { _logWarn } from './src/core/util/log';\n\n// Core functionality shared by all clients\nexport * from './index.shared';\n\n// Export some Phone symbols\n// providers\nexport { PhoneAuthProvider } from './src/platform_browser/providers/phone';\n\n// strategies\nexport {\n  signInWithPhoneNumber,\n  linkWithPhoneNumber,\n  reauthenticateWithPhoneNumber,\n  updatePhoneNumber\n} from './src/platform_browser/strategies/phone';\n\n// MFA\nexport { PhoneMultiFactorGenerator } from './src/platform_browser/mfa/assertions/phone';\nexport {\n  TotpMultiFactorGenerator,\n  TotpSecret\n} from './src/mfa/assertions/totp';\n\nexport { getReactNativePersistence } from './src/platform_react_native/persistence/react_native';\n\nconst NO_PERSISTENCE_WARNING = `\nYou are initializing Firebase Auth for React Native without providing\nAsyncStorage. Auth state will default to memory persistence and will not\npersist between sessions. In order to persist auth state, install the package\n\"@react-native-async-storage/async-storage\" and provide it to\ninitializeAuth:\n\nimport { initializeAuth, getReactNativePersistence } from 'firebase/auth';\nimport ReactNativeAsyncStorage from '@react-native-async-storage/async-storage';\nconst auth = initializeAuth(app, {\n  persistence: getReactNativePersistence(ReactNativeAsyncStorage)\n});\n`;\n\nexport function getAuth(app: FirebaseApp = getApp()): Auth {\n  const provider = _getProvider(app, 'auth');\n\n  if (provider.isInitialized()) {\n    return provider.getImmediate();\n  }\n\n  // Only warn if getAuth() is called before initializeAuth()\n  _logWarn(NO_PERSISTENCE_WARNING);\n\n  return initializeAuthOriginal(app);\n}\n\n/**\n * Wrapper around base `initializeAuth()` for RN users only, which\n * shows the warning message if no persistence is provided.\n * Double-checked potential collision with `export * from './index.shared'`\n * as `./index.shared` also exports `initializeAuth()`, and the final\n * bundle does correctly export only this `initializeAuth()` function\n * and not the one from index.shared.\n */\nexport function initializeAuth(app: FirebaseApp, deps?: Dependencies): Auth {\n  if (!deps?.persistence) {\n    _logWarn(NO_PERSISTENCE_WARNING);\n  }\n  return initializeAuthOriginal(app, deps);\n}\n\nregisterAuth(ClientPlatform.REACT_NATIVE);\n"], "names": ["STORAGE_AVAILABLE_KEY", "app", "getApp", "_get<PERSON><PERSON><PERSON>", "_logWarn", "initializeAuthOriginal", "registerAuth"], "mappings": ";;;;;;;;;;;AAAA;;;;;;;;;;;;;;;AAeG;AAYH;;;;;;AAMG;AACG,SAAU,yBAAyB,CACvC,OAAgC,EAAA;;;;;;;;AAQhC,IAAA,OAAA,EAAA,kBAAA,YAAA;AAAO,YAAA,SAAA,OAAA,GAAA;AAEI,gBAAA,IAAA,CAAA,IAAI,GAA0C,OAAA,6BAAA;aAqCxD;AAnCO,YAAA,OAAA,CAAA,SAAA,CAAA,YAAY,GAAlB,YAAA;;;;;;gCAEI,IAAI,CAAC,OAAO,EAAE;AACZ,oCAAA,OAAA,CAAA,CAAA,aAAO,KAAK,CAAC,CAAA;AACd,iCAAA;gCACD,OAAM,CAAA,CAAA,YAAA,OAAO,CAAC,OAAO,CAACA,2BAAqB,EAAE,GAAG,CAAC,CAAA,CAAA;;AAAjD,gCAAA,EAAA,CAAA,IAAA,EAAiD,CAAC;AAClD,gCAAA,OAAA,CAAA,CAAA,YAAM,OAAO,CAAC,UAAU,CAACA,2BAAqB,CAAC,CAAA,CAAA;;AAA/C,gCAAA,EAAA,CAAA,IAAA,EAA+C,CAAC;AAChD,gCAAA,OAAA,CAAA,CAAA,aAAO,IAAI,CAAC,CAAA;;;AAEZ,gCAAA,OAAA,CAAA,CAAA,aAAO,KAAK,CAAC,CAAA;;;;;AAEhB,aAAA,CAAA;AAED,YAAA,OAAA,CAAA,SAAA,CAAA,IAAI,GAAJ,UAAK,GAAW,EAAE,KAAuB,EAAA;AACvC,gBAAA,OAAO,OAAO,CAAC,OAAO,CAAC,GAAG,EAAE,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,CAAC;aACpD,CAAA;YAEK,OAAI,CAAA,SAAA,CAAA,IAAA,GAAV,UAAuC,GAAW,EAAA;;;;;AACnC,4BAAA,KAAA,CAAA,EAAA,OAAA,CAAA,CAAA,YAAM,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,CAAA,CAAA;;AAAjC,gCAAA,IAAI,GAAG,EAA0B,CAAA,IAAA,EAAA,CAAA;AACvC,gCAAA,OAAA,CAAA,CAAA,aAAO,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,CAAA;;;;AACvC,aAAA,CAAA;YAED,OAAO,CAAA,SAAA,CAAA,OAAA,GAAP,UAAQ,GAAW,EAAA;AACjB,gBAAA,OAAO,OAAO,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC;aAChC,CAAA;AAED,YAAA,OAAA,CAAA,SAAA,CAAA,YAAY,GAAZ,UAAa,IAAY,EAAE,SAA+B,EAAA;;gBAExD,OAAO;aACR,CAAA;AAED,YAAA,OAAA,CAAA,SAAA,CAAA,eAAe,GAAf,UAAgB,IAAY,EAAE,SAA+B,EAAA;;gBAE3D,OAAO;aACR,CAAA;YACH,OAAC,OAAA,CAAA;AAAD,SAAC,EAvCM,CAAA;AACE,QAAA,EAAA,CAAA,IAAI,GAAY,OAAQ;AAsC/B,QAAA,EAAA,CAAA;AACJ;;ACnFA;;;;;;;;;;;;;;;AAeG;AAyCH,IAAM,sBAAsB,GAAG,kkBAY9B,CAAC;AAEI,SAAU,OAAO,CAACC,KAA2B,EAAA;IAA3B,IAAAA,KAAA,KAAA,KAAA,CAAA,EAAA,EAAAA,KAAmB,GAAAC,UAAM,EAAE,CAAA,EAAA;IACjD,IAAM,QAAQ,GAAGC,gBAAY,CAACF,KAAG,EAAE,MAAM,CAAC,CAAC;AAE3C,IAAA,IAAI,QAAQ,CAAC,aAAa,EAAE,EAAE;AAC5B,QAAA,OAAO,QAAQ,CAAC,YAAY,EAAE,CAAC;AAChC,KAAA;;IAGDG,cAAQ,CAAC,sBAAsB,CAAC,CAAC;AAEjC,IAAA,OAAOC,oBAAsB,CAACJ,KAAG,CAAC,CAAC;AACrC,CAAC;AAED;;;;;;;AAOG;AACa,SAAA,cAAc,CAAC,GAAgB,EAAE,IAAmB,EAAA;IAClE,IAAI,EAAC,IAAI,KAAJ,IAAA,IAAA,IAAI,uBAAJ,IAAI,CAAE,WAAW,CAAA,EAAE;QACtBG,cAAQ,CAAC,sBAAsB,CAAC,CAAC;AAClC,KAAA;AACD,IAAA,OAAOC,oBAAsB,CAAC,GAAG,EAAE,IAAI,CAAC,CAAC;AAC3C,CAAC;AAEDC,kBAAY,iDAA6B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;"}