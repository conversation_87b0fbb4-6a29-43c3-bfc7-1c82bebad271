import{_ as B,r as c,M as I,o as N,E as S,a,f as v,h as _,n as o,d as x,w,g as L,b as e,t as l,F as m,k as p,l as V,m as n,e as C}from"./index-2cGdSAEs.js";import{u as D}from"./extensionsStore-DVoXHZtn.js";import{B as M}from"./BlockRenderer-DTQXxKLs.js";const P={class:"extension-detail-container"},F={key:0,class:"loading-message"},R={key:1,class:"not-found-message"},U={key:2,class:"extension-content"},$={class:"header"},j=["src","alt"],J={class:"version-tag-large"},O={class:"author"},A={class:"description-full"},T={class:"tags-detail"},q={class:"actions-bar"},z={class:"downloads-detail"},G={class:"details-layout"},H={class:"preview-section"},K={class:"preview-box"},Q={key:1},W={class:"event-debugger-preview"},X={key:0},Y={key:1},Z={class:"json-view-section"},ee={class:"json-code-block"},se={__name:"ExtensionDetailView",setup(te){const f=I();V();const i=D(),h=c(f.params.id),s=c(null),r=c([]);async function k(){i.extensions.length===0&&await i.fetchExtensions(),s.value=i.getExtensionById(h.value)}N(k),S(()=>f.params.id,u=>{u&&(h.value=u,k(),r.value=[])});async function y(){if(!s.value)return;if(console.log(`Installing extension: ${s.value.name}`),await i.incrementDownloadCount(s.value.id)){const t=i.getExtensionById(s.value.id);t&&(s.value={...t}),alert(`${s.value.name} installed successfully! (Simulated)`)}else alert(`Failed to install ${s.value.name}. (Simulated)`)}function E(u){r.value.unshift({eventName:u.eventName,blockId:u.blockId}),r.value.length>5&&r.value.pop()}return(u,t)=>{const g=L("router-link");return n(),a("div",P,[_(i).isLoading?(n(),a("div",F,"Loading extension details...")):v("",!0),!_(i).isLoading&&!s.value?(n(),a("div",R,[t[1]||(t[1]=o(" Extension not found. ")),x(g,{to:{name:"Marketplace"}},{default:w(()=>t[0]||(t[0]=[o("Back to Marketplace")])),_:1,__:[0]})])):v("",!0),s.value&&!_(i).isLoading?(n(),a("div",U,[e("div",$,[e("img",{src:s.value.dev_metadata.author_pic||"https://via.placeholder.com/50",alt:s.value.dev_metadata.author_name,class:"dev-avatar-large"},null,8,j),e("div",null,[e("h1",null,[o(l(s.value.name)+" ",1),e("span",J,"v"+l(s.value.version),1)]),e("p",O,[t[2]||(t[2]=o(" By ")),x(g,{to:{name:"UserProfile",params:{username:s.value.dev_metadata.author_id}}},{default:w(()=>[o(l(s.value.dev_metadata.author_name||"Unknown Developer"),1)]),_:1},8,["to"])])])]),e("p",A,l(s.value.description),1),e("div",T,[(n(!0),a(m,null,p(s.value.tags,d=>(n(),a("span",{key:d,class:"tag"},l(d),1))),128))]),e("div",q,[e("button",{onClick:y,class:"install-button"},t[3]||(t[3]=[e("i",{class:"fas fa-download"},null,-1),o(" Install Extension ")])),e("p",z,[t[4]||(t[4]=e("i",{class:"fas fa-users"},null,-1)),o(" "+l(s.value.downloads)+" Installs ",1)])]),e("div",G,[e("div",H,[t[6]||(t[6]=e("h3",null,[e("i",{class:"fas fa-eye"}),o(" Live Preview")],-1)),e("div",K,[s.value.ui_blocks&&s.value.ui_blocks.length>0?(n(!0),a(m,{key:0},p(s.value.ui_blocks,d=>(n(),C(M,{key:d.id,block:d,onBlockEvent:E},null,8,["block"]))),128)):(n(),a("p",Q,"No UI preview available for this extension."))]),e("div",W,[t[5]||(t[5]=e("h4",null,"Preview Events:",-1)),r.value.length>0?(n(),a("ul",X,[(n(!0),a(m,null,p(r.value,(d,b)=>(n(),a("li",{key:b},l(d.eventName)+" on '"+l(d.blockId)+"' ",1))),128))])):(n(),a("p",Y,"Interact with the preview to see events here."))])]),e("div",Z,[t[7]||(t[7]=e("h3",null,[e("i",{class:"fas fa-code"}),o(" Extension JSON")],-1)),e("pre",ee,l(JSON.stringify(s.value,null,2)),1)])])])):v("",!0)])}}},le=B(se,[["__scopeId","data-v-36600090"]]);export{le as default};
