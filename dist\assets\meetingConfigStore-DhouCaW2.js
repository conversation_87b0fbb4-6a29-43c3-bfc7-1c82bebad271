import{q as l,x as c,y as g,Q as d,z as f}from"./index-oz5Qducj.js";import{u as h}from"./extensionsStore-C2g9n1A4.js";const u={recording:!1,whiteboard:!1,breakoutRooms:!1,chat:!0,reactions:!0,virtualBackground:!1,audibleImpairedSystem:!1},b=l("meetingConfig",{state:()=>({currentMeetingId:null,meetingSettings:{name:"TheMeet Meeting",requireApproval:!1,hostUserId:null},activeFeatures:{...u},activeExtensions:[],linkSharingPolicy:"host",joinTracking:[],linkSharingActivities:[],isLoading:!1,error:null,_firebaseUnsubscribe:null}),getters:{isFeatureActive:e=>i=>!!e.activeFeatures[i],getExtensionConfig:e=>i=>{const t=e.activeExtensions.find(s=>s.id===i);return t?t.config:null},getAllActiveExtensions:e=>e.activeExtensions,getMeetingName:e=>e.meetingSettings.name,isHost:e=>i=>e.meetingSettings.hostUserId===i,getLinkSharingPolicy:e=>e.linkSharingPolicy,getJoinTracking:e=>e.joinTracking,getLinkSharingActivities:e=>e.linkSharingActivities,canUserShareLink:e=>i=>e.linkSharingPolicy==="all"?!0:e.meetingSettings.hostUserId===i},actions:{initializeNewMeeting(e,i){this.meetingSettings={...this.meetingSettings,...e,hostUserId:i}},async loadMeetingConfiguration(e,i){this._firebaseUnsubscribe&&(this._firebaseUnsubscribe(),this._firebaseUnsubscribe=null),this.currentMeetingId=e,this.isLoading=!0,this.error=null;const t=c(g,"meetings",e);this._firebaseUnsubscribe=f(t,s=>{if(s.exists()){const n=s.data();this.meetingSettings={name:n.meetingName||"TheMeet Meeting",requireApproval:n.requireApproval||!1,hostUserId:n.createdBy},this.activeFeatures={...u,...n.features||{}};const r=h();this.activeExtensions=(n.extensions||[]).map(a=>{const o=r.getExtensionById(a.id);return o?{...o,config:a.config||{}}:{id:a.id,name:a.name||"Unknown Extension",config:a.config||{},manifestNotFound:!0}}),this.linkSharingPolicy=n.linkSharingPolicy||"host",this.joinTracking=n.joinTracking||[],this.linkSharingActivities=n.linkSharingActivities||[],this.isLoading=!1}else this.error="Meeting not found or you do not have access.",this.isLoading=!1,console.error(`Meeting with ID ${e} not found.`)},s=>{this.error=`Error loading meeting: ${s.message}`,this.isLoading=!1,console.error("Firebase snapshot error:",s)})},async _updateFirebaseMeeting(e){if(!this.currentMeetingId){console.error("No currentMeetingId set, cannot update Firebase."),this.error="Cannot update: No active meeting.";return}try{const i=c(g,"meetings",this.currentMeetingId);await d(i,e)}catch(i){console.error("Error updating Firebase meeting:",i),this.error=`Failed to update meeting setting: ${i.message}`}},async toggleFeature(e){if(typeof this.activeFeatures[e]>"u"){console.warn(`Feature ${e} is not defined in activeFeatures state.`);return}const i=!this.activeFeatures[e];this.activeFeatures[e]=i,await this._updateFirebaseMeeting({[`features.${e}`]:i})},async addExtensionToMeeting(e){const t=h().getExtensionById(e);if(!t){this.error=`Extension manifest for ${e} not found.`,console.error(this.error);return}if(this.activeExtensions.some(r=>r.id===e)){console.warn(`Extension ${e} is already active.`);return}const s={...t,config:t.defaultConfig||{}};this.activeExtensions.push(s);const n=this.activeExtensions.map(r=>({id:r.id,name:r.name,config:r.config||{}}));await this._updateFirebaseMeeting({extensions:n})},async removeExtensionFromMeeting(e){this.activeExtensions=this.activeExtensions.filter(t=>t.id!==e);const i=this.activeExtensions.map(t=>({id:t.id,name:t.name,config:t.config||{}}));await this._updateFirebaseMeeting({extensions:i})},async updateExtensionMeetingConfig(e,i){const t=this.activeExtensions.findIndex(n=>n.id===e);if(t===-1){this.error=`Cannot update config: Extension ${e} not active.`,console.error(this.error);return}this.activeExtensions[t].config={...this.activeExtensions[t].config,...i};const s=this.activeExtensions.map(n=>({id:n.id,name:n.name,config:n.config||{}}));await this._updateFirebaseMeeting({extensions:s})},setMeetingHost(e){this.meetingSettings.hostUserId=e},async updateLinkSharingPolicy(e){e!=="host"&&e!=="all"||(this.linkSharingPolicy=e,await this._updateFirebaseMeeting({linkSharingPolicy:e}))},async recordLinkSharingActivity(e){const i={...e,id:`${e.type}_${Date.now()}_${Math.random().toString(36).substring(2,11)}`,timestamp:e.timestamp||new Date().toISOString()};this.linkSharingActivities.push(i),await this._updateFirebaseMeeting({linkSharingActivities:this.linkSharingActivities})},async recordJoinViaLink(e){this.joinTracking.push(e),await this.recordLinkSharingActivity({type:"user_joined",sharedBy:e.sharedBy,joinedName:e.joinedName,joinedBy:e.joinedBy,timestamp:e.timestamp}),await this._updateFirebaseMeeting({joinTracking:this.joinTracking})},async logJoinEvent(e,i,t){const s={sharedBy:e,joinedBy:i,joinedName:t,joinedAt:Date.now()};this.joinTracking.push(s),await this._updateFirebaseMeeting({joinTracking:this.joinTracking})},resetMeetingConfig(){this._firebaseUnsubscribe&&(this._firebaseUnsubscribe(),this._firebaseUnsubscribe=null),this.currentMeetingId=null,this.meetingSettings={name:"TheMeet Meeting",requireApproval:!1,hostUserId:null},this.activeFeatures={recording:!1,whiteboard:!1,breakoutRooms:!1,chat:!0,reactions:!0,virtualBackground:!1,audibleImpairedSystem:!1},this.activeExtensions=[],this.linkSharingPolicy="host",this.joinTracking=[],this.linkSharingActivities=[],this.isLoading=!1,this.error=null}}});export{b as u};
