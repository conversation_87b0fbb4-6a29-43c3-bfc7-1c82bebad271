<template>
  <div class="analytics-container">
    <div v-if="isLoading" class="loading-message">Loading analytics data...</div>
    <div v-else-if="!extension" class="error-message">
      Extension not found or you do not have permission to view its analytics.
    </div>
    <div v-else class="analytics-content">
      <div class="analytics-header">
        <h1>Analytics for "{{ extension.name }}"</h1>
        <router-link :to="{ name: 'Dashboard' }" class="back-button">
          <i class="fas fa-arrow-left"></i> Back to Dashboard
        </router-link>
      </div>

      <div class="stats-grid">
        <div class="stat-card total-downloads">
          <div class="stat-icon"><i class="fas fa-users"></i></div>
          <div class="stat-value">{{ extension.downloads }}</div>
          <div class="stat-label">Total Installs</div>
        </div>

        <div class="stat-card last-updated">
          <div class="stat-icon"><i class="fas fa-clock"></i></div>
          <div class="stat-value-small">{{ formatDate(extension.dev_metadata.updated_at) }}</div>
          <div class="stat-label">Last Updated</div>
        </div>

        <div class="stat-card version-info">
          <div class="stat-icon"><i class="fas fa-code-branch"></i></div>
          <div class="stat-value-small">v{{ extension.version }}</div>
          <div class="stat-label">Current Version</div>
        </div>

        <!-- Placeholder for future charts/graphs -->
        <div class="stat-card chart-placeholder">
          <div class="stat-icon"><i class="fas fa-chart-bar"></i></div>
          <div class="stat-value-small">Downloads Over Time</div>
          <div class="stat-label">(Chart coming soon)</div>
        </div>
         <div class="stat-card chart-placeholder">
          <div class="stat-icon"><i class="fas fa-map-marked-alt"></i></div>
          <div class="stat-value-small">Usage Heatmap</div>
          <div class="stat-label">(Feature coming soon)</div>
        </div>
      </div>

      <div class="actions-footer">
        <router-link :to="{ name: 'ExtensionEditor', params: { id: extension.id } }" class="action-btn edit-btn">
          <i class="fas fa-edit"></i> Edit Extension
        </router-link>
        <router-link :to="{ name: 'ExtensionDetail', params: { id: extension.id } }" class="action-btn view-btn">
          <i class="fas fa-eye"></i> View Public Page
        </router-link>
      </div>

    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue';
import { useRoute } from 'vue-router';
import { useAuthStore } from '../../store/authStore';
import { useExtensionsStore } from '../../store/extensionsStore';

const route = useRoute();
const authStore = useAuthStore();
const extensionsStore = useExtensionsStore();

const extension = ref(null);
const isLoading = ref(true);

onMounted(async () => {
  isLoading.value = true;
  const extensionId = route.params.id;
  if (extensionsStore.extensions.length === 0) {
    await extensionsStore.fetchExtensions();
  }

  const fetchedExtension = extensionsStore.getExtensionById(extensionId);

  // Security check: Ensure the logged-in user is the author of the extension
  if (fetchedExtension && authStore.user && fetchedExtension.dev_metadata.author_id === authStore.user.id) {
    extension.value = fetchedExtension;
  } else {
    extension.value = null; // Clear if not authorized or not found
    console.warn(`User ${authStore.user?.id} not authorized for extension ${extensionId} or extension not found.`);
  }
  isLoading.value = false;
});

function formatDate(dateString) {
  if (!dateString) return 'N/A';
  const options = { year: 'numeric', month: 'short', day: 'numeric' };
  return new Date(dateString).toLocaleDateString(undefined, options);
}
</script>

<style scoped>
.analytics-container {
  padding: 20px;
  max-width: 900px;
  margin: 0 auto;
}

.loading-message, .error-message {
  text-align: center;
  font-size: 1.2em;
  padding: 30px;
  color: #777;
}
.error-message {
  color: #dc3545;
}

.analytics-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 25px;
}
.analytics-header h1 {
  font-size: 1.8em; /* Slightly smaller than dashboard */
  color: #333;
  margin: 0;
}
.back-button {
  background-color: #6c757d;
  color: white;
  padding: 8px 12px;
  border-radius: 5px;
  text-decoration: none;
  font-size: 0.9em;
  display: inline-flex;
  align-items: center;
  gap: 6px;
}
.back-button:hover {
  background-color: #5a6268;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
}

.stat-card {
  background-color: #fff;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  padding: 20px;
  text-align: center;
  box-shadow: 0 1px 3px rgba(0,0,0,0.05);
}
.stat-icon {
  font-size: 2.5em;
  margin-bottom: 10px;
  color: #007bff;
}
.stat-card.total-downloads .stat-icon {
  color: #28a745; /* Green for downloads */
}
.stat-card.last-updated .stat-icon {
  color: #ffc107; /* Yellow for time */
}
.stat-card.version-info .stat-icon {
    color: #17a2b8; /* Teal for version */
}
.stat-card.chart-placeholder .stat-icon {
    color: #6c757d; /* Grey for placeholders */
}


.stat-value {
  font-size: 2.8em;
  font-weight: bold;
  color: #333;
  margin-bottom: 5px;
}
.stat-value-small {
  font-size: 1.4em; /* Smaller for text values like dates/versions */
  font-weight: 500;
  color: #444;
  margin-bottom: 5px;
  min-height: 1.4em; /* Ensure consistent height */
}

.stat-label {
  font-size: 0.95em;
  color: #777;
}

.chart-placeholder .stat-label {
    font-style: italic;
}

.actions-footer {
  margin-top: 30px;
  padding-top: 20px;
  border-top: 1px solid #eee;
  display: flex;
  gap: 15px;
  justify-content: flex-start;
}
.action-btn { /* Reusing from dashboard for consistency */
  padding: 10px 15px;
  text-decoration: none;
  border-radius: 5px;
  font-size: 1em;
  display: inline-flex;
  align-items: center;
  gap: 6px;
  transition: opacity 0.2s;
}
.action-btn:hover {
  opacity: 0.8;
}
.edit-btn {
  background-color: #ffc107;
  color: #333;
}
.view-btn {
  background-color: #6c757d;
  color: white;
}

</style>
