{"version": 3, "sources": ["../../@vue/devtools-api/lib/esm/env.js", "../../@vue/devtools-api/lib/esm/const.js", "../../@vue/devtools-api/lib/esm/time.js", "../../@vue/devtools-api/lib/esm/proxy.js", "../../@vue/devtools-api/lib/esm/index.js"], "sourcesContent": ["export function getDevtoolsGlobalHook() {\r\n    return getTarget().__VUE_DEVTOOLS_GLOBAL_HOOK__;\r\n}\r\nexport function getTarget() {\r\n    // @ts-expect-error navigator and windows are not available in all environments\r\n    return (typeof navigator !== 'undefined' && typeof window !== 'undefined')\r\n        ? window\r\n        : typeof globalThis !== 'undefined'\r\n            ? globalThis\r\n            : {};\r\n}\r\nexport const isProxyAvailable = typeof Proxy === 'function';\r\n", "export const HOOK_SETUP = 'devtools-plugin:setup';\r\nexport const HOOK_PLUGIN_SETTINGS_SET = 'plugin:settings:set';\r\n", "let supported;\r\nlet perf;\r\nexport function isPerformanceSupported() {\r\n    var _a;\r\n    if (supported !== undefined) {\r\n        return supported;\r\n    }\r\n    if (typeof window !== 'undefined' && window.performance) {\r\n        supported = true;\r\n        perf = window.performance;\r\n    }\r\n    else if (typeof globalThis !== 'undefined' && ((_a = globalThis.perf_hooks) === null || _a === void 0 ? void 0 : _a.performance)) {\r\n        supported = true;\r\n        perf = globalThis.perf_hooks.performance;\r\n    }\r\n    else {\r\n        supported = false;\r\n    }\r\n    return supported;\r\n}\r\nexport function now() {\r\n    return isPerformanceSupported() ? perf.now() : Date.now();\r\n}\r\n", "import { HOOK_PLUGIN_SETTINGS_SET } from './const.js';\r\nimport { now } from './time.js';\r\nexport class ApiProxy {\r\n    constructor(plugin, hook) {\r\n        this.target = null;\r\n        this.targetQueue = [];\r\n        this.onQueue = [];\r\n        this.plugin = plugin;\r\n        this.hook = hook;\r\n        const defaultSettings = {};\r\n        if (plugin.settings) {\r\n            for (const id in plugin.settings) {\r\n                const item = plugin.settings[id];\r\n                defaultSettings[id] = item.defaultValue;\r\n            }\r\n        }\r\n        const localSettingsSaveId = `__vue-devtools-plugin-settings__${plugin.id}`;\r\n        let currentSettings = Object.assign({}, defaultSettings);\r\n        try {\r\n            const raw = localStorage.getItem(localSettingsSaveId);\r\n            const data = JSON.parse(raw);\r\n            Object.assign(currentSettings, data);\r\n        }\r\n        catch (e) {\r\n            // noop\r\n        }\r\n        this.fallbacks = {\r\n            getSettings() {\r\n                return currentSettings;\r\n            },\r\n            setSettings(value) {\r\n                try {\r\n                    localStorage.setItem(localSettingsSaveId, JSON.stringify(value));\r\n                }\r\n                catch (e) {\r\n                    // noop\r\n                }\r\n                currentSettings = value;\r\n            },\r\n            now() {\r\n                return now();\r\n            },\r\n        };\r\n        if (hook) {\r\n            hook.on(HOOK_PLUGIN_SETTINGS_SET, (pluginId, value) => {\r\n                if (pluginId === this.plugin.id) {\r\n                    this.fallbacks.setSettings(value);\r\n                }\r\n            });\r\n        }\r\n        this.proxiedOn = new Proxy({}, {\r\n            get: (_target, prop) => {\r\n                if (this.target) {\r\n                    return this.target.on[prop];\r\n                }\r\n                else {\r\n                    return (...args) => {\r\n                        this.onQueue.push({\r\n                            method: prop,\r\n                            args,\r\n                        });\r\n                    };\r\n                }\r\n            },\r\n        });\r\n        this.proxiedTarget = new Proxy({}, {\r\n            get: (_target, prop) => {\r\n                if (this.target) {\r\n                    return this.target[prop];\r\n                }\r\n                else if (prop === 'on') {\r\n                    return this.proxiedOn;\r\n                }\r\n                else if (Object.keys(this.fallbacks).includes(prop)) {\r\n                    return (...args) => {\r\n                        this.targetQueue.push({\r\n                            method: prop,\r\n                            args,\r\n                            resolve: () => { },\r\n                        });\r\n                        return this.fallbacks[prop](...args);\r\n                    };\r\n                }\r\n                else {\r\n                    return (...args) => {\r\n                        return new Promise((resolve) => {\r\n                            this.targetQueue.push({\r\n                                method: prop,\r\n                                args,\r\n                                resolve,\r\n                            });\r\n                        });\r\n                    };\r\n                }\r\n            },\r\n        });\r\n    }\r\n    async setRealTarget(target) {\r\n        this.target = target;\r\n        for (const item of this.onQueue) {\r\n            this.target.on[item.method](...item.args);\r\n        }\r\n        for (const item of this.targetQueue) {\r\n            item.resolve(await this.target[item.method](...item.args));\r\n        }\r\n    }\r\n}\r\n", "import { getDevtoolsGlobalHook, getTarget, isProxyAvailable } from './env.js';\r\nimport { HOOK_SETUP } from './const.js';\r\nimport { ApiProxy } from './proxy.js';\r\nexport * from './api/index.js';\r\nexport * from './plugin.js';\r\nexport * from './time.js';\r\nexport function setupDevtoolsPlugin(pluginDescriptor, setupFn) {\r\n    const descriptor = pluginDescriptor;\r\n    const target = getTarget();\r\n    const hook = getDevtoolsGlobalHook();\r\n    const enableProxy = isProxyAvailable && descriptor.enableEarlyProxy;\r\n    if (hook && (target.__VUE_DEVTOOLS_PLUGIN_API_AVAILABLE__ || !enableProxy)) {\r\n        hook.emit(HOOK_SETUP, pluginDescriptor, setupFn);\r\n    }\r\n    else {\r\n        const proxy = enableProxy ? new ApiProxy(descriptor, hook) : null;\r\n        const list = target.__VUE_DEVTOOLS_PLUGINS__ = target.__VUE_DEVTOOLS_PLUGINS__ || [];\r\n        list.push({\r\n            pluginDescriptor: descriptor,\r\n            setupFn,\r\n            proxy,\r\n        });\r\n        if (proxy) {\r\n            setupFn(proxy.proxiedTarget);\r\n        }\r\n    }\r\n}\r\n"], "mappings": ";AAAO,SAAS,wBAAwB;AACpC,SAAO,UAAU,EAAE;AACvB;AACO,SAAS,YAAY;AAExB,SAAQ,OAAO,cAAc,eAAe,OAAO,WAAW,cACxD,SACA,OAAO,eAAe,cAClB,aACA,CAAC;AACf;AACO,IAAM,mBAAmB,OAAO,UAAU;;;ACX1C,IAAM,aAAa;AACnB,IAAM,2BAA2B;;;ACDxC,IAAI;AACJ,IAAI;AACG,SAAS,yBAAyB;AACrC,MAAI;AACJ,MAAI,cAAc,QAAW;AACzB,WAAO;AAAA,EACX;AACA,MAAI,OAAO,WAAW,eAAe,OAAO,aAAa;AACrD,gBAAY;AACZ,WAAO,OAAO;AAAA,EAClB,WACS,OAAO,eAAe,iBAAiB,KAAK,WAAW,gBAAgB,QAAQ,OAAO,SAAS,SAAS,GAAG,cAAc;AAC9H,gBAAY;AACZ,WAAO,WAAW,WAAW;AAAA,EACjC,OACK;AACD,gBAAY;AAAA,EAChB;AACA,SAAO;AACX;AACO,SAAS,MAAM;AAClB,SAAO,uBAAuB,IAAI,KAAK,IAAI,IAAI,KAAK,IAAI;AAC5D;;;ACpBO,IAAM,WAAN,MAAe;AAAA,EAClB,YAAY,QAAQ,MAAM;AACtB,SAAK,SAAS;AACd,SAAK,cAAc,CAAC;AACpB,SAAK,UAAU,CAAC;AAChB,SAAK,SAAS;AACd,SAAK,OAAO;AACZ,UAAM,kBAAkB,CAAC;AACzB,QAAI,OAAO,UAAU;AACjB,iBAAW,MAAM,OAAO,UAAU;AAC9B,cAAM,OAAO,OAAO,SAAS,EAAE;AAC/B,wBAAgB,EAAE,IAAI,KAAK;AAAA,MAC/B;AAAA,IACJ;AACA,UAAM,sBAAsB,mCAAmC,OAAO,EAAE;AACxE,QAAI,kBAAkB,OAAO,OAAO,CAAC,GAAG,eAAe;AACvD,QAAI;AACA,YAAM,MAAM,aAAa,QAAQ,mBAAmB;AACpD,YAAM,OAAO,KAAK,MAAM,GAAG;AAC3B,aAAO,OAAO,iBAAiB,IAAI;AAAA,IACvC,SACO,GAAG;AAAA,IAEV;AACA,SAAK,YAAY;AAAA,MACb,cAAc;AACV,eAAO;AAAA,MACX;AAAA,MACA,YAAY,OAAO;AACf,YAAI;AACA,uBAAa,QAAQ,qBAAqB,KAAK,UAAU,KAAK,CAAC;AAAA,QACnE,SACO,GAAG;AAAA,QAEV;AACA,0BAAkB;AAAA,MACtB;AAAA,MACA,MAAM;AACF,eAAO,IAAI;AAAA,MACf;AAAA,IACJ;AACA,QAAI,MAAM;AACN,WAAK,GAAG,0BAA0B,CAAC,UAAU,UAAU;AACnD,YAAI,aAAa,KAAK,OAAO,IAAI;AAC7B,eAAK,UAAU,YAAY,KAAK;AAAA,QACpC;AAAA,MACJ,CAAC;AAAA,IACL;AACA,SAAK,YAAY,IAAI,MAAM,CAAC,GAAG;AAAA,MAC3B,KAAK,CAAC,SAAS,SAAS;AACpB,YAAI,KAAK,QAAQ;AACb,iBAAO,KAAK,OAAO,GAAG,IAAI;AAAA,QAC9B,OACK;AACD,iBAAO,IAAI,SAAS;AAChB,iBAAK,QAAQ,KAAK;AAAA,cACd,QAAQ;AAAA,cACR;AAAA,YACJ,CAAC;AAAA,UACL;AAAA,QACJ;AAAA,MACJ;AAAA,IACJ,CAAC;AACD,SAAK,gBAAgB,IAAI,MAAM,CAAC,GAAG;AAAA,MAC/B,KAAK,CAAC,SAAS,SAAS;AACpB,YAAI,KAAK,QAAQ;AACb,iBAAO,KAAK,OAAO,IAAI;AAAA,QAC3B,WACS,SAAS,MAAM;AACpB,iBAAO,KAAK;AAAA,QAChB,WACS,OAAO,KAAK,KAAK,SAAS,EAAE,SAAS,IAAI,GAAG;AACjD,iBAAO,IAAI,SAAS;AAChB,iBAAK,YAAY,KAAK;AAAA,cAClB,QAAQ;AAAA,cACR;AAAA,cACA,SAAS,MAAM;AAAA,cAAE;AAAA,YACrB,CAAC;AACD,mBAAO,KAAK,UAAU,IAAI,EAAE,GAAG,IAAI;AAAA,UACvC;AAAA,QACJ,OACK;AACD,iBAAO,IAAI,SAAS;AAChB,mBAAO,IAAI,QAAQ,CAAC,YAAY;AAC5B,mBAAK,YAAY,KAAK;AAAA,gBAClB,QAAQ;AAAA,gBACR;AAAA,gBACA;AAAA,cACJ,CAAC;AAAA,YACL,CAAC;AAAA,UACL;AAAA,QACJ;AAAA,MACJ;AAAA,IACJ,CAAC;AAAA,EACL;AAAA,EACA,MAAM,cAAc,QAAQ;AACxB,SAAK,SAAS;AACd,eAAW,QAAQ,KAAK,SAAS;AAC7B,WAAK,OAAO,GAAG,KAAK,MAAM,EAAE,GAAG,KAAK,IAAI;AAAA,IAC5C;AACA,eAAW,QAAQ,KAAK,aAAa;AACjC,WAAK,QAAQ,MAAM,KAAK,OAAO,KAAK,MAAM,EAAE,GAAG,KAAK,IAAI,CAAC;AAAA,IAC7D;AAAA,EACJ;AACJ;;;ACpGO,SAAS,oBAAoB,kBAAkB,SAAS;AAC3D,QAAM,aAAa;AACnB,QAAM,SAAS,UAAU;AACzB,QAAM,OAAO,sBAAsB;AACnC,QAAM,cAAc,oBAAoB,WAAW;AACnD,MAAI,SAAS,OAAO,yCAAyC,CAAC,cAAc;AACxE,SAAK,KAAK,YAAY,kBAAkB,OAAO;AAAA,EACnD,OACK;AACD,UAAM,QAAQ,cAAc,IAAI,SAAS,YAAY,IAAI,IAAI;AAC7D,UAAM,OAAO,OAAO,2BAA2B,OAAO,4BAA4B,CAAC;AACnF,SAAK,KAAK;AAAA,MACN,kBAAkB;AAAA,MAClB;AAAA,MACA;AAAA,IACJ,CAAC;AACD,QAAI,OAAO;AACP,cAAQ,MAAM,aAAa;AAAA,IAC/B;AAAA,EACJ;AACJ;", "names": []}