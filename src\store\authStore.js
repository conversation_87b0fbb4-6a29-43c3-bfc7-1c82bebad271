import { defineStore } from 'pinia';
import { db, auth } from '../firebase/config';
import { doc, setDoc } from 'firebase/firestore';
import { GoogleAuthProvider, signInWithPopup, signInWithEmailAndPassword, signOut } from 'firebase/auth';

export const useAuthStore = defineStore('auth', {
  state: () => ({
    // For simulation: null if not logged in, or an object like { id: 'dev1', role: 'dev' } or { id: 'user1', role: 'user' }
    user: null,
    intendedRoute: null, // To store the route the user was trying to access before being redirected to login
  }),
  getters: {
    isLoggedIn: (state) => !!state.user,
    isDev: (state) => state.user?.role === 'dev',
  },
  actions: {
    async loginWithGoogle() {
      try {
        console.log('Attempting Google login...');
        const provider = new GoogleAuthProvider();
        const result = await signInWithPopup(auth, provider);
        const user = result.user;
        this.user = {
          id: user.uid,
          name: user.displayName,
          email: user.email,
          photoURL: user.photoURL,
          provider: 'google',
          role: 'user', // Default role; can be updated later
        };
        await setDoc(doc(db, 'users', this.user.id), {
          id: this.user.id,
          name: this.user.name,
          email: this.user.email,
          photoURL: this.user.photoURL,
          provider: this.user.provider,
          role: this.user.role,
          createdAt: new Date().toISOString(),
        }, { merge: true });
        console.log('Google login successful:', this.user);
      } catch (error) {
        console.error('Google login failed:', error);
        throw error;
      }
    },
    async loginWithEmail(email, password) {
      const result = await signInWithEmailAndPassword(auth, email, password);
      const user = result.user;
      this.user = {
        id: user.uid,
        name: user.displayName || user.email,
        email: user.email,
        photoURL: user.photoURL,
        provider: 'email',
        role: 'user',
      };
      await setDoc(doc(db, 'users', this.user.id), {
        id: this.user.id,
        name: this.user.name,
        email: this.user.email,
        photoURL: this.user.photoURL,
        provider: this.user.provider,
        role: this.user.role,
        createdAt: new Date().toISOString(),
      }, { merge: true });
    },
    async logout() {
      await signOut(auth);
      this.user = null;
      this.intendedRoute = null;
    },
    setIntendedRoute(route) {
      this.intendedRoute = route;
    },
    clearIntendedRoute() {
      const path = this.intendedRoute;
      this.intendedRoute = null;
      return path;
    }
  },
});
