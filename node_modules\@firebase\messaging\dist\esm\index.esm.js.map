{"version": 3, "file": "index.esm.js", "sources": ["../../src/util/constants.ts", "../../src/interfaces/internal-message-payload.ts", "../../src/helpers/array-base64-translator.ts", "../../src/helpers/migrate-old-database.ts", "../../src/internals/idb-manager.ts", "../../src/util/errors.ts", "../../src/internals/requests.ts", "../../src/internals/token-manager.ts", "../../src/helpers/externalizePayload.ts", "../../src/helpers/is-console-message.ts", "../../src/helpers/logToFirelog.ts", "../../src/helpers/extract-app-config.ts", "../../src/messaging-service.ts", "../../src/helpers/registerDefaultSw.ts", "../../src/helpers/updateSwReg.ts", "../../src/helpers/updateVapidKey.ts", "../../src/api/getToken.ts", "../../src/helpers/logToScion.ts", "../../src/listeners/window-listener.ts", "../../src/helpers/register.ts", "../../src/api/isSupported.ts", "../../src/api/deleteToken.ts", "../../src/api/onMessage.ts", "../../src/api.ts", "../../src/index.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nexport const DEFAULT_SW_PATH = '/firebase-messaging-sw.js';\nexport const DEFAULT_SW_SCOPE = '/firebase-cloud-messaging-push-scope';\n\nexport const DEFAULT_VAPID_KEY =\n  'BDOU99-h67HcA6JeFXHbSNMu7e2yNNu3RzoMj8TM4W88jITfq7ZmPvIM1Iv-4_l2LxQcYwhqby2xGpWwzjfAnG4';\n\nexport const ENDPOINT = 'https://fcmregistrations.googleapis.com/v1';\n\n/** Key of FCM Payload in Notification's data field. */\nexport const FCM_MSG = 'FCM_MSG';\n\nexport const CONSOLE_CAMPAIGN_ID = 'google.c.a.c_id';\nexport const CONSOLE_CAMPAIGN_NAME = 'google.c.a.c_l';\nexport const CONSOLE_CAMPAIGN_TIME = 'google.c.a.ts';\n/** Set to '1' if Analytics is enabled for the campaign */\nexport const CONSOLE_CAMPAIGN_ANALYTICS_ENABLED = 'google.c.a.e';\nexport const TAG = 'FirebaseMessaging: ';\nexport const MAX_NUMBER_OF_EVENTS_PER_LOG_REQUEST = 1000;\nexport const MAX_RETRIES = 3;\nexport const LOG_INTERVAL_IN_MS = 86400000; //24 hour\nexport const DEFAULT_BACKOFF_TIME_MS = 5000;\n\n// FCM log source name registered at Firelog: 'FCM_CLIENT_EVENT_LOGGING'. It uniquely identifies\n// FCM's logging configuration.\nexport const FCM_LOG_SOURCE = 1249;\n\n// Defined as in proto/messaging_event.proto. Neglecting fields that are supported.\nexport const SDK_PLATFORM_WEB = 3;\nexport const EVENT_MESSAGE_DELIVERED = 1;\n\nexport enum MessageType {\n  DATA_MESSAGE = 1,\n  DISPLAY_NOTIFICATION = 3\n}\n", "/**\n * @license\n * Copyright 2018 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\"); you may not use this file except\n * in compliance with the License. You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under the License\n * is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express\n * or implied. See the License for the specific language governing permissions and limitations under\n * the License.\n */\n\nimport {\n  CONSOLE_CAMPAIGN_ANALYTICS_ENABLED,\n  CONSOLE_CAMPAIGN_ID,\n  CONSOLE_CAMPAIGN_NAME,\n  CONSOLE_CAMPAIGN_TIME\n} from '../util/constants';\n\nexport interface MessagePayloadInternal {\n  notification?: NotificationPayloadInternal;\n  data?: unknown;\n  fcmOptions?: FcmOptionsInternal;\n  messageType?: MessageType;\n  isFirebaseMessaging?: boolean;\n  from: string;\n  fcmMessageId: string;\n  productId: number;\n  // eslint-disable-next-line camelcase\n  collapse_key: string;\n}\n\nexport interface NotificationPayloadInternal extends NotificationOptions {\n  title: string;\n  // Supported in the Legacy Send API.\n  // See:https://firebase.google.com/docs/cloud-messaging/xmpp-server-ref.\n  // eslint-disable-next-line camelcase\n  click_action?: string;\n  icon?: string;\n}\n\n// Defined in\n// https://firebase.google.com/docs/reference/fcm/rest/v1/projects.messages#webpushfcmoptions. Note\n// that the keys are sent to the clients in snake cases which we need to convert to camel so it can\n// be exposed as a type to match the Firebase API convention.\nexport interface FcmOptionsInternal {\n  link?: string;\n\n  // eslint-disable-next-line camelcase\n  analytics_label?: string;\n}\n\nexport enum MessageType {\n  PUSH_RECEIVED = 'push-received',\n  NOTIFICATION_CLICKED = 'notification-clicked'\n}\n\n/** Additional data of a message sent from the FN Console. */\nexport interface ConsoleMessageData {\n  [CONSOLE_CAMPAIGN_ID]: string;\n  [CONSOLE_CAMPAIGN_TIME]: string;\n  [CONSOLE_CAMPAIGN_NAME]?: string;\n  [CONSOLE_CAMPAIGN_ANALYTICS_ENABLED]?: '1';\n}\n", "/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nexport function arrayToBase64(array: Uint8Array | ArrayBuffer): string {\n  const uint8Array = new Uint8Array(array);\n  const base64String = btoa(String.fromCharCode(...uint8Array));\n  return base64String.replace(/=/g, '').replace(/\\+/g, '-').replace(/\\//g, '_');\n}\n\nexport function base64ToArray(base64String: string): Uint8Array {\n  const padding = '='.repeat((4 - (base64String.length % 4)) % 4);\n  const base64 = (base64String + padding)\n    .replace(/\\-/g, '+')\n    .replace(/_/g, '/');\n\n  const rawData = atob(base64);\n  const outputArray = new Uint8Array(rawData.length);\n\n  for (let i = 0; i < rawData.length; ++i) {\n    outputArray[i] = rawData.charCodeAt(i);\n  }\n  return outputArray;\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { deleteDB, openDB } from 'idb';\n\nimport { TokenDetails } from '../interfaces/token-details';\nimport { arrayToBase64 } from './array-base64-translator';\n\n// https://github.com/firebase/firebase-js-sdk/blob/7857c212f944a2a9eb421fd4cb7370181bc034b5/packages/messaging/src/interfaces/token-details.ts\nexport interface V2TokenDetails {\n  fcmToken: string;\n  swScope: string;\n  vapidKey: string | Uint8Array;\n  subscription: PushSubscription;\n  fcmSenderId: string;\n  fcmPushSet: string;\n  createTime?: number;\n  endpoint?: string;\n  auth?: string;\n  p256dh?: string;\n}\n\n// https://github.com/firebase/firebase-js-sdk/blob/6b5b15ce4ea3df5df5df8a8b33a4e41e249c7715/packages/messaging/src/interfaces/token-details.ts\nexport interface V3TokenDetails {\n  fcmToken: string;\n  swScope: string;\n  vapidKey: Uint8Array;\n  fcmSenderId: string;\n  fcmPushSet: string;\n  endpoint: string;\n  auth: ArrayBuffer;\n  p256dh: ArrayBuffer;\n  createTime: number;\n}\n\n// https://github.com/firebase/firebase-js-sdk/blob/9567dba664732f681fa7fe60f5b7032bb1daf4c9/packages/messaging/src/interfaces/token-details.ts\nexport interface V4TokenDetails {\n  fcmToken: string;\n  swScope: string;\n  vapidKey: Uint8Array;\n  fcmSenderId: string;\n  endpoint: string;\n  auth: ArrayBufferLike;\n  p256dh: ArrayBufferLike;\n  createTime: number;\n}\n\nconst OLD_DB_NAME = 'fcm_token_details_db';\n/**\n * The last DB version of 'fcm_token_details_db' was 4. This is one higher, so that the upgrade\n * callback is called for all versions of the old DB.\n */\nconst OLD_DB_VERSION = 5;\nconst OLD_OBJECT_STORE_NAME = 'fcm_token_object_Store';\n\nexport async function migrateOldDatabase(\n  senderId: string\n): Promise<TokenDetails | null> {\n  if ('databases' in indexedDB) {\n    // indexedDb.databases() is an IndexedDB v3 API and does not exist in all browsers. TODO: Remove\n    // typecast when it lands in TS types.\n    const databases = await (\n      indexedDB as {\n        databases(): Promise<Array<{ name: string; version: number }>>;\n      }\n    ).databases();\n    const dbNames = databases.map(db => db.name);\n\n    if (!dbNames.includes(OLD_DB_NAME)) {\n      // old DB didn't exist, no need to open.\n      return null;\n    }\n  }\n\n  let tokenDetails: TokenDetails | null = null;\n\n  const db = await openDB(OLD_DB_NAME, OLD_DB_VERSION, {\n    upgrade: async (db, oldVersion, newVersion, upgradeTransaction) => {\n      if (oldVersion < 2) {\n        // Database too old, skip migration.\n        return;\n      }\n\n      if (!db.objectStoreNames.contains(OLD_OBJECT_STORE_NAME)) {\n        // Database did not exist. Nothing to do.\n        return;\n      }\n\n      const objectStore = upgradeTransaction.objectStore(OLD_OBJECT_STORE_NAME);\n      const value = await objectStore.index('fcmSenderId').get(senderId);\n      await objectStore.clear();\n\n      if (!value) {\n        // No entry in the database, nothing to migrate.\n        return;\n      }\n\n      if (oldVersion === 2) {\n        const oldDetails = value as V2TokenDetails;\n\n        if (!oldDetails.auth || !oldDetails.p256dh || !oldDetails.endpoint) {\n          return;\n        }\n\n        tokenDetails = {\n          token: oldDetails.fcmToken,\n          createTime: oldDetails.createTime ?? Date.now(),\n          subscriptionOptions: {\n            auth: oldDetails.auth,\n            p256dh: oldDetails.p256dh,\n            endpoint: oldDetails.endpoint,\n            swScope: oldDetails.swScope,\n            vapidKey:\n              typeof oldDetails.vapidKey === 'string'\n                ? oldDetails.vapidKey\n                : arrayToBase64(oldDetails.vapidKey)\n          }\n        };\n      } else if (oldVersion === 3) {\n        const oldDetails = value as V3TokenDetails;\n\n        tokenDetails = {\n          token: oldDetails.fcmToken,\n          createTime: oldDetails.createTime,\n          subscriptionOptions: {\n            auth: arrayToBase64(oldDetails.auth),\n            p256dh: arrayToBase64(oldDetails.p256dh),\n            endpoint: oldDetails.endpoint,\n            swScope: oldDetails.swScope,\n            vapidKey: arrayToBase64(oldDetails.vapidKey)\n          }\n        };\n      } else if (oldVersion === 4) {\n        const oldDetails = value as V4TokenDetails;\n\n        tokenDetails = {\n          token: oldDetails.fcmToken,\n          createTime: oldDetails.createTime,\n          subscriptionOptions: {\n            auth: arrayToBase64(oldDetails.auth),\n            p256dh: arrayToBase64(oldDetails.p256dh),\n            endpoint: oldDetails.endpoint,\n            swScope: oldDetails.swScope,\n            vapidKey: arrayToBase64(oldDetails.vapidKey)\n          }\n        };\n      }\n    }\n  });\n  db.close();\n\n  // Delete all old databases.\n  await deleteDB(OLD_DB_NAME);\n  await deleteDB('fcm_vapid_details_db');\n  await deleteDB('undefined');\n\n  return checkTokenDetails(tokenDetails) ? tokenDetails : null;\n}\n\nfunction checkTokenDetails(\n  tokenDetails: TokenDetails | null\n): tokenDetails is TokenDetails {\n  if (!tokenDetails || !tokenDetails.subscriptionOptions) {\n    return false;\n  }\n  const { subscriptionOptions } = tokenDetails;\n  return (\n    typeof tokenDetails.createTime === 'number' &&\n    tokenDetails.createTime > 0 &&\n    typeof tokenDetails.token === 'string' &&\n    tokenDetails.token.length > 0 &&\n    typeof subscriptionOptions.auth === 'string' &&\n    subscriptionOptions.auth.length > 0 &&\n    typeof subscriptionOptions.p256dh === 'string' &&\n    subscriptionOptions.p256dh.length > 0 &&\n    typeof subscriptionOptions.endpoint === 'string' &&\n    subscriptionOptions.endpoint.length > 0 &&\n    typeof subscriptionOptions.swScope === 'string' &&\n    subscriptionOptions.swScope.length > 0 &&\n    typeof subscriptionOptions.vapidKey === 'string' &&\n    subscriptionOptions.vapidKey.length > 0\n  );\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { DBSchema, IDBPDatabase, deleteDB, openDB } from 'idb';\n\nimport { FirebaseInternalDependencies } from '../interfaces/internal-dependencies';\nimport { TokenDetails } from '../interfaces/token-details';\nimport { migrateOldDatabase } from '../helpers/migrate-old-database';\n\n// Exported for tests.\nexport const DATABASE_NAME = 'firebase-messaging-database';\nconst DATABASE_VERSION = 1;\nconst OBJECT_STORE_NAME = 'firebase-messaging-store';\n\ninterface MessagingDB extends DBSchema {\n  'firebase-messaging-store': {\n    key: string;\n    value: TokenDetails;\n  };\n}\n\nlet dbPromise: Promise<IDBPDatabase<MessagingDB>> | null = null;\nfunction getDbPromise(): Promise<IDBPDatabase<MessagingDB>> {\n  if (!dbPromise) {\n    dbPromise = openDB(DATABASE_NAME, DATABASE_VERSION, {\n      upgrade: (upgradeDb, oldVersion) => {\n        // We don't use 'break' in this switch statement, the fall-through behavior is what we want,\n        // because if there are multiple versions between the old version and the current version, we\n        // want ALL the migrations that correspond to those versions to run, not only the last one.\n        // eslint-disable-next-line default-case\n        switch (oldVersion) {\n          case 0:\n            upgradeDb.createObjectStore(OBJECT_STORE_NAME);\n        }\n      }\n    });\n  }\n  return dbPromise;\n}\n\n/** Gets record(s) from the objectStore that match the given key. */\nexport async function dbGet(\n  firebaseDependencies: FirebaseInternalDependencies\n): Promise<TokenDetails | undefined> {\n  const key = getKey(firebaseDependencies);\n  const db = await getDbPromise();\n  const tokenDetails = (await db\n    .transaction(OBJECT_STORE_NAME)\n    .objectStore(OBJECT_STORE_NAME)\n    .get(key)) as TokenDetails;\n\n  if (tokenDetails) {\n    return tokenDetails;\n  } else {\n    // Check if there is a tokenDetails object in the old DB.\n    const oldTokenDetails = await migrateOldDatabase(\n      firebaseDependencies.appConfig.senderId\n    );\n    if (oldTokenDetails) {\n      await dbSet(firebaseDependencies, oldTokenDetails);\n      return oldTokenDetails;\n    }\n  }\n}\n\n/** Assigns or overwrites the record for the given key with the given value. */\nexport async function dbSet(\n  firebaseDependencies: FirebaseInternalDependencies,\n  tokenDetails: TokenDetails\n): Promise<TokenDetails> {\n  const key = getKey(firebaseDependencies);\n  const db = await getDbPromise();\n  const tx = db.transaction(OBJECT_STORE_NAME, 'readwrite');\n  await tx.objectStore(OBJECT_STORE_NAME).put(tokenDetails, key);\n  await tx.done;\n  return tokenDetails;\n}\n\n/** Removes record(s) from the objectStore that match the given key. */\nexport async function dbRemove(\n  firebaseDependencies: FirebaseInternalDependencies\n): Promise<void> {\n  const key = getKey(firebaseDependencies);\n  const db = await getDbPromise();\n  const tx = db.transaction(OBJECT_STORE_NAME, 'readwrite');\n  await tx.objectStore(OBJECT_STORE_NAME).delete(key);\n  await tx.done;\n}\n\n/** Deletes the DB. Useful for tests. */\nexport async function dbDelete(): Promise<void> {\n  if (dbPromise) {\n    (await dbPromise).close();\n    await deleteDB(DATABASE_NAME);\n    dbPromise = null;\n  }\n}\n\nfunction getKey({ appConfig }: FirebaseInternalDependencies): string {\n  return appConfig.appId;\n}\n", "/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { ErrorFactory, ErrorMap } from '@firebase/util';\n\nexport const enum ErrorCode {\n  MISSING_APP_CONFIG_VALUES = 'missing-app-config-values',\n  AVAILABLE_IN_WINDOW = 'only-available-in-window',\n  AVAILABLE_IN_SW = 'only-available-in-sw',\n  PERMISSION_DEFAULT = 'permission-default',\n  PERMISSION_BLOCKED = 'permission-blocked',\n  UNSUPPORTED_BROWSER = 'unsupported-browser',\n  INDEXED_DB_UNSUPPORTED = 'indexed-db-unsupported',\n  FAILED_DEFAULT_REGISTRATION = 'failed-service-worker-registration',\n  TOKEN_SUBSCRIBE_FAILED = 'token-subscribe-failed',\n  TOKEN_SUBSCRIBE_NO_TOKEN = 'token-subscribe-no-token',\n  TOKEN_UNSUBSCRIBE_FAILED = 'token-unsubscribe-failed',\n  TOKEN_UPDATE_FAILED = 'token-update-failed',\n  TOKEN_UPDATE_NO_TOKEN = 'token-update-no-token',\n  INVALID_BG_HANDLER = 'invalid-bg-handler',\n  USE_SW_AFTER_GET_TOKEN = 'use-sw-after-get-token',\n  INVALID_SW_REGISTRATION = 'invalid-sw-registration',\n  USE_VAPID_KEY_AFTER_GET_TOKEN = 'use-vapid-key-after-get-token',\n  INVALID_VAPID_KEY = 'invalid-vapid-key'\n}\n\nexport const ERROR_MAP: ErrorMap<ErrorCode> = {\n  [ErrorCode.MISSING_APP_CONFIG_VALUES]:\n    'Missing App configuration value: \"{$valueName}\"',\n  [ErrorCode.AVAILABLE_IN_WINDOW]:\n    'This method is available in a Window context.',\n  [ErrorCode.AVAILABLE_IN_SW]:\n    'This method is available in a service worker context.',\n  [ErrorCode.PERMISSION_DEFAULT]:\n    'The notification permission was not granted and dismissed instead.',\n  [ErrorCode.PERMISSION_BLOCKED]:\n    'The notification permission was not granted and blocked instead.',\n  [ErrorCode.UNSUPPORTED_BROWSER]:\n    \"This browser doesn't support the API's required to use the Firebase SDK.\",\n  [ErrorCode.INDEXED_DB_UNSUPPORTED]:\n    \"This browser doesn't support indexedDb.open() (ex. Safari iFrame, Firefox Private Browsing, etc)\",\n  [ErrorCode.FAILED_DEFAULT_REGISTRATION]:\n    'We are unable to register the default service worker. {$browserErrorMessage}',\n  [ErrorCode.TOKEN_SUBSCRIBE_FAILED]:\n    'A problem occurred while subscribing the user to FCM: {$errorInfo}',\n  [ErrorCode.TOKEN_SUBSCRIBE_NO_TOKEN]:\n    'FCM returned no token when subscribing the user to push.',\n  [ErrorCode.TOKEN_UNSUBSCRIBE_FAILED]:\n    'A problem occurred while unsubscribing the ' +\n    'user from FCM: {$errorInfo}',\n  [ErrorCode.TOKEN_UPDATE_FAILED]:\n    'A problem occurred while updating the user from FCM: {$errorInfo}',\n  [ErrorCode.TOKEN_UPDATE_NO_TOKEN]:\n    'FCM returned no token when updating the user to push.',\n  [ErrorCode.USE_SW_AFTER_GET_TOKEN]:\n    'The useServiceWorker() method may only be called once and must be ' +\n    'called before calling getToken() to ensure your service worker is used.',\n  [ErrorCode.INVALID_SW_REGISTRATION]:\n    'The input to useServiceWorker() must be a ServiceWorkerRegistration.',\n  [ErrorCode.INVALID_BG_HANDLER]:\n    'The input to setBackgroundMessageHandler() must be a function.',\n  [ErrorCode.INVALID_VAPID_KEY]: 'The public VAPID key must be a string.',\n  [ErrorCode.USE_VAPID_KEY_AFTER_GET_TOKEN]:\n    'The usePublicVapidKey() method may only be called once and must be ' +\n    'called before calling getToken() to ensure your VAPID key is used.'\n};\n\ninterface ErrorParams {\n  [ErrorCode.MISSING_APP_CONFIG_VALUES]: {\n    valueName: string;\n  };\n  [ErrorCode.FAILED_DEFAULT_REGISTRATION]: { browserErrorMessage: string };\n  [ErrorCode.TOKEN_SUBSCRIBE_FAILED]: { errorInfo: string };\n  [ErrorCode.TOKEN_UNSUBSCRIBE_FAILED]: { errorInfo: string };\n  [ErrorCode.TOKEN_UPDATE_FAILED]: { errorInfo: string };\n}\n\nexport const ERROR_FACTORY = new ErrorFactory<ErrorCode, ErrorParams>(\n  'messaging',\n  'Messaging',\n  ERROR_MAP\n);\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { DEFAULT_VAPID_KEY, ENDPOINT } from '../util/constants';\nimport { ERROR_FACTORY, ErrorCode } from '../util/errors';\nimport { SubscriptionOptions, TokenDetails } from '../interfaces/token-details';\n\nimport { AppConfig } from '../interfaces/app-config';\nimport { FirebaseInternalDependencies } from '../interfaces/internal-dependencies';\n\nexport interface ApiResponse {\n  token?: string;\n  error?: { message: string };\n}\n\nexport interface ApiRequestBody {\n  web: {\n    endpoint: string;\n    p256dh: string;\n    auth: string;\n    applicationPubKey?: string;\n  };\n}\n\nexport async function requestGetToken(\n  firebaseDependencies: FirebaseInternalDependencies,\n  subscriptionOptions: SubscriptionOptions\n): Promise<string> {\n  const headers = await getHeaders(firebaseDependencies);\n  const body = getBody(subscriptionOptions);\n\n  const subscribeOptions = {\n    method: 'POST',\n    headers,\n    body: JSON.stringify(body)\n  };\n\n  let responseData: ApiResponse;\n  try {\n    const response = await fetch(\n      getEndpoint(firebaseDependencies.appConfig),\n      subscribeOptions\n    );\n    responseData = await response.json();\n  } catch (err) {\n    throw ERROR_FACTORY.create(ErrorCode.TOKEN_SUBSCRIBE_FAILED, {\n      errorInfo: (err as Error)?.toString()\n    });\n  }\n\n  if (responseData.error) {\n    const message = responseData.error.message;\n    throw ERROR_FACTORY.create(ErrorCode.TOKEN_SUBSCRIBE_FAILED, {\n      errorInfo: message\n    });\n  }\n\n  if (!responseData.token) {\n    throw ERROR_FACTORY.create(ErrorCode.TOKEN_SUBSCRIBE_NO_TOKEN);\n  }\n\n  return responseData.token;\n}\n\nexport async function requestUpdateToken(\n  firebaseDependencies: FirebaseInternalDependencies,\n  tokenDetails: TokenDetails\n): Promise<string> {\n  const headers = await getHeaders(firebaseDependencies);\n  const body = getBody(tokenDetails.subscriptionOptions!);\n\n  const updateOptions = {\n    method: 'PATCH',\n    headers,\n    body: JSON.stringify(body)\n  };\n\n  let responseData: ApiResponse;\n  try {\n    const response = await fetch(\n      `${getEndpoint(firebaseDependencies.appConfig)}/${tokenDetails.token}`,\n      updateOptions\n    );\n    responseData = await response.json();\n  } catch (err) {\n    throw ERROR_FACTORY.create(ErrorCode.TOKEN_UPDATE_FAILED, {\n      errorInfo: (err as Error)?.toString()\n    });\n  }\n\n  if (responseData.error) {\n    const message = responseData.error.message;\n    throw ERROR_FACTORY.create(ErrorCode.TOKEN_UPDATE_FAILED, {\n      errorInfo: message\n    });\n  }\n\n  if (!responseData.token) {\n    throw ERROR_FACTORY.create(ErrorCode.TOKEN_UPDATE_NO_TOKEN);\n  }\n\n  return responseData.token;\n}\n\nexport async function requestDeleteToken(\n  firebaseDependencies: FirebaseInternalDependencies,\n  token: string\n): Promise<void> {\n  const headers = await getHeaders(firebaseDependencies);\n\n  const unsubscribeOptions = {\n    method: 'DELETE',\n    headers\n  };\n\n  try {\n    const response = await fetch(\n      `${getEndpoint(firebaseDependencies.appConfig)}/${token}`,\n      unsubscribeOptions\n    );\n    const responseData: ApiResponse = await response.json();\n    if (responseData.error) {\n      const message = responseData.error.message;\n      throw ERROR_FACTORY.create(ErrorCode.TOKEN_UNSUBSCRIBE_FAILED, {\n        errorInfo: message\n      });\n    }\n  } catch (err) {\n    throw ERROR_FACTORY.create(ErrorCode.TOKEN_UNSUBSCRIBE_FAILED, {\n      errorInfo: (err as Error)?.toString()\n    });\n  }\n}\n\nfunction getEndpoint({ projectId }: AppConfig): string {\n  return `${ENDPOINT}/projects/${projectId!}/registrations`;\n}\n\nasync function getHeaders({\n  appConfig,\n  installations\n}: FirebaseInternalDependencies): Promise<Headers> {\n  const authToken = await installations.getToken();\n\n  return new Headers({\n    'Content-Type': 'application/json',\n    Accept: 'application/json',\n    'x-goog-api-key': appConfig.apiKey!,\n    'x-goog-firebase-installations-auth': `FIS ${authToken}`\n  });\n}\n\nfunction getBody({\n  p256dh,\n  auth,\n  endpoint,\n  vapidKey\n}: SubscriptionOptions): ApiRequestBody {\n  const body: ApiRequestBody = {\n    web: {\n      endpoint,\n      auth,\n      p256dh\n    }\n  };\n\n  if (vapidKey !== DEFAULT_VAPID_KEY) {\n    body.web.applicationPubKey = vapidKey;\n  }\n\n  return body;\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { SubscriptionOptions, TokenDetails } from '../interfaces/token-details';\nimport {\n  arrayToBase64,\n  base64ToArray\n} from '../helpers/array-base64-translator';\nimport { dbGet, dbRemove, dbSet } from './idb-manager';\nimport {\n  requestDeleteToken,\n  requestGetToken,\n  requestUpdateToken\n} from './requests';\n\nimport { FirebaseInternalDependencies } from '../interfaces/internal-dependencies';\nimport { MessagingService } from '../messaging-service';\n\n// UpdateRegistration will be called once every week.\nconst TOKEN_EXPIRATION_MS = 7 * 24 * 60 * 60 * 1000; // 7 days\n\nexport async function getTokenInternal(\n  messaging: MessagingService\n): Promise<string> {\n  const pushSubscription = await getPushSubscription(\n    messaging.swRegistration!,\n    messaging.vapidKey!\n  );\n\n  const subscriptionOptions: SubscriptionOptions = {\n    vapidKey: messaging.vapidKey!,\n    swScope: messaging.swRegistration!.scope,\n    endpoint: pushSubscription.endpoint,\n    auth: arrayToBase64(pushSubscription.getKey('auth')!),\n    p256dh: arrayToBase64(pushSubscription.getKey('p256dh')!)\n  };\n\n  const tokenDetails = await dbGet(messaging.firebaseDependencies);\n  if (!tokenDetails) {\n    // No token, get a new one.\n    return getNewToken(messaging.firebaseDependencies, subscriptionOptions);\n  } else if (\n    !isTokenValid(tokenDetails.subscriptionOptions!, subscriptionOptions)\n  ) {\n    // Invalid token, get a new one.\n    try {\n      await requestDeleteToken(\n        messaging.firebaseDependencies!,\n        tokenDetails.token\n      );\n    } catch (e) {\n      // Suppress errors because of #2364\n      console.warn(e);\n    }\n\n    return getNewToken(messaging.firebaseDependencies!, subscriptionOptions);\n  } else if (Date.now() >= tokenDetails.createTime + TOKEN_EXPIRATION_MS) {\n    // Weekly token refresh\n    return updateToken(messaging, {\n      token: tokenDetails.token,\n      createTime: Date.now(),\n      subscriptionOptions\n    });\n  } else {\n    // Valid token, nothing to do.\n    return tokenDetails.token;\n  }\n}\n\n/**\n * This method deletes the token from the database, unsubscribes the token from FCM, and unregisters\n * the push subscription if it exists.\n */\nexport async function deleteTokenInternal(\n  messaging: MessagingService\n): Promise<boolean> {\n  const tokenDetails = await dbGet(messaging.firebaseDependencies);\n  if (tokenDetails) {\n    await requestDeleteToken(\n      messaging.firebaseDependencies,\n      tokenDetails.token\n    );\n    await dbRemove(messaging.firebaseDependencies);\n  }\n\n  // Unsubscribe from the push subscription.\n  const pushSubscription =\n    await messaging.swRegistration!.pushManager.getSubscription();\n  if (pushSubscription) {\n    return pushSubscription.unsubscribe();\n  }\n\n  // If there's no SW, consider it a success.\n  return true;\n}\n\nasync function updateToken(\n  messaging: MessagingService,\n  tokenDetails: TokenDetails\n): Promise<string> {\n  try {\n    const updatedToken = await requestUpdateToken(\n      messaging.firebaseDependencies,\n      tokenDetails\n    );\n\n    const updatedTokenDetails: TokenDetails = {\n      ...tokenDetails,\n      token: updatedToken,\n      createTime: Date.now()\n    };\n\n    await dbSet(messaging.firebaseDependencies, updatedTokenDetails);\n    return updatedToken;\n  } catch (e) {\n    throw e;\n  }\n}\n\nasync function getNewToken(\n  firebaseDependencies: FirebaseInternalDependencies,\n  subscriptionOptions: SubscriptionOptions\n): Promise<string> {\n  const token = await requestGetToken(\n    firebaseDependencies,\n    subscriptionOptions\n  );\n  const tokenDetails: TokenDetails = {\n    token,\n    createTime: Date.now(),\n    subscriptionOptions\n  };\n  await dbSet(firebaseDependencies, tokenDetails);\n  return tokenDetails.token;\n}\n\n/**\n * Gets a PushSubscription for the current user.\n */\nasync function getPushSubscription(\n  swRegistration: ServiceWorkerRegistration,\n  vapidKey: string\n): Promise<PushSubscription> {\n  const subscription = await swRegistration.pushManager.getSubscription();\n  if (subscription) {\n    return subscription;\n  }\n\n  return swRegistration.pushManager.subscribe({\n    userVisibleOnly: true,\n    // Chrome <= 75 doesn't support base64-encoded VAPID key. For backward compatibility, VAPID key\n    // submitted to pushManager#subscribe must be of type Uint8Array.\n    applicationServerKey: base64ToArray(vapidKey)\n  });\n}\n\n/**\n * Checks if the saved tokenDetails object matches the configuration provided.\n */\nfunction isTokenValid(\n  dbOptions: SubscriptionOptions,\n  currentOptions: SubscriptionOptions\n): boolean {\n  const isVapidKeyEqual = currentOptions.vapidKey === dbOptions.vapidKey;\n  const isEndpointEqual = currentOptions.endpoint === dbOptions.endpoint;\n  const isAuthEqual = currentOptions.auth === dbOptions.auth;\n  const isP256dhEqual = currentOptions.p256dh === dbOptions.p256dh;\n\n  return isVapidKeyEqual && isEndpointEqual && isAuthEqual && isP256dhEqual;\n}\n", "/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { MessagePayload } from '../interfaces/public-types';\nimport { MessagePayloadInternal } from '../interfaces/internal-message-payload';\n\nexport function externalizePayload(\n  internalPayload: MessagePayloadInternal\n): MessagePayload {\n  const payload: MessagePayload = {\n    from: internalPayload.from,\n    // eslint-disable-next-line camelcase\n    collapseKey: internalPayload.collapse_key,\n    // eslint-disable-next-line camelcase\n    messageId: internalPayload.fcmMessageId\n  } as MessagePayload;\n\n  propagateNotificationPayload(payload, internalPayload);\n  propagateDataPayload(payload, internalPayload);\n  propagateFcmOptions(payload, internalPayload);\n\n  return payload;\n}\n\nfunction propagateNotificationPayload(\n  payload: MessagePayload,\n  messagePayloadInternal: MessagePayloadInternal\n): void {\n  if (!messagePayloadInternal.notification) {\n    return;\n  }\n\n  payload.notification = {};\n\n  const title = messagePayloadInternal.notification!.title;\n  if (!!title) {\n    payload.notification!.title = title;\n  }\n\n  const body = messagePayloadInternal.notification!.body;\n  if (!!body) {\n    payload.notification!.body = body;\n  }\n\n  const image = messagePayloadInternal.notification!.image;\n  if (!!image) {\n    payload.notification!.image = image;\n  }\n\n  const icon = messagePayloadInternal.notification!.icon;\n  if (!!icon) {\n    payload.notification!.icon = icon;\n  }\n}\n\nfunction propagateDataPayload(\n  payload: MessagePayload,\n  messagePayloadInternal: MessagePayloadInternal\n): void {\n  if (!messagePayloadInternal.data) {\n    return;\n  }\n\n  payload.data = messagePayloadInternal.data as { [key: string]: string };\n}\n\nfunction propagateFcmOptions(\n  payload: MessagePayload,\n  messagePayloadInternal: MessagePayloadInternal\n): void {\n  // fcmOptions.link value is written into notification.click_action. see more in b/232072111\n  if (\n    !messagePayloadInternal.fcmOptions &&\n    !messagePayloadInternal.notification?.click_action\n  ) {\n    return;\n  }\n\n  payload.fcmOptions = {};\n\n  const link =\n    messagePayloadInternal.fcmOptions?.link ??\n    messagePayloadInternal.notification?.click_action;\n\n  if (!!link) {\n    payload.fcmOptions!.link = link;\n  }\n\n  // eslint-disable-next-line camelcase\n  const analyticsLabel = messagePayloadInternal.fcmOptions?.analytics_label;\n  if (!!analyticsLabel) {\n    payload.fcmOptions!.analyticsLabel = analyticsLabel;\n  }\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { CONSOLE_CAMPAIGN_ID } from '../util/constants';\nimport { ConsoleMessageData } from '../interfaces/internal-message-payload';\n\nexport function isConsoleMessage(data: unknown): data is ConsoleMessageData {\n  // This message has a campaign ID, meaning it was sent using the Firebase Console.\n  return typeof data === 'object' && !!data && CONSOLE_CAMPAIGN_ID in data;\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {\n  DEFAULT_BACKOFF_TIME_MS,\n  EVENT_MESSAGE_DELIVERED,\n  FCM_LOG_SOURCE,\n  LOG_INTERVAL_IN_MS,\n  MAX_NUMBER_OF_EVENTS_PER_LOG_REQUEST,\n  MAX_RETRIES,\n  MessageType,\n  SDK_PLATFORM_WEB\n} from '../util/constants';\nimport {\n  FcmEvent,\n  LogEvent,\n  LogRequest,\n  LogResponse,\n  ComplianceData\n} from '../interfaces/logging-types';\n\nimport { MessagePayloadInternal } from '../interfaces/internal-message-payload';\nimport { MessagingService } from '../messaging-service';\n\nconst LOG_ENDPOINT = 'https://play.google.com/log?format=json_proto3';\n\nconst FCM_TRANSPORT_KEY = _mergeStrings(\n  'AzSCbw63g1R0nCw85jG8',\n  'Iaya3yLKwmgvh7cF0q4'\n);\n\nexport function startLoggingService(messaging: MessagingService): void {\n  if (!messaging.isLogServiceStarted) {\n    _processQueue(messaging, LOG_INTERVAL_IN_MS);\n    messaging.isLogServiceStarted = true;\n  }\n}\n\n/**\n *\n * @param messaging the messaging instance.\n * @param offsetInMs this method execute after `offsetInMs` elapsed .\n */\nexport function _processQueue(\n  messaging: MessagingService,\n  offsetInMs: number\n): void {\n  setTimeout(async () => {\n    if (!messaging.deliveryMetricsExportedToBigQueryEnabled) {\n      // flush events and terminate logging service\n      messaging.logEvents = [];\n      messaging.isLogServiceStarted = false;\n\n      return;\n    }\n\n    if (!messaging.logEvents.length) {\n      return _processQueue(messaging, LOG_INTERVAL_IN_MS);\n    }\n\n    await _dispatchLogEvents(messaging);\n  }, offsetInMs);\n}\n\nexport async function _dispatchLogEvents(\n  messaging: MessagingService\n): Promise<void> {\n  for (\n    let i = 0, n = messaging.logEvents.length;\n    i < n;\n    i += MAX_NUMBER_OF_EVENTS_PER_LOG_REQUEST\n  ) {\n    const logRequest = _createLogRequest(\n      messaging.logEvents.slice(i, i + MAX_NUMBER_OF_EVENTS_PER_LOG_REQUEST)\n    );\n\n    let retryCount = 0,\n      response = {} as Response;\n\n    do {\n      try {\n        response = await fetch(\n          LOG_ENDPOINT.concat('&key=', FCM_TRANSPORT_KEY),\n          {\n            method: 'POST',\n            body: JSON.stringify(logRequest)\n          }\n        );\n\n        // don't retry on 200s or non retriable errors\n        if (response.ok || (!response.ok && !isRetriableError(response))) {\n          break;\n        }\n\n        if (!response.ok && isRetriableError(response)) {\n          // rethrow to retry with quota\n          throw new Error(\n            'a retriable Non-200 code is returned in fetch to Firelog endpoint. Retry'\n          );\n        }\n      } catch (error) {\n        const isLastAttempt = retryCount === MAX_RETRIES;\n        if (isLastAttempt) {\n          // existing the do-while interactive retry logic because retry quota has reached.\n          break;\n        }\n      }\n\n      let delayInMs: number;\n      try {\n        delayInMs = Number(\n          ((await response.json()) as LogResponse).nextRequestWaitMillis\n        );\n      } catch (e) {\n        delayInMs = DEFAULT_BACKOFF_TIME_MS;\n      }\n\n      await new Promise(resolve => setTimeout(resolve, delayInMs));\n\n      retryCount++;\n    } while (retryCount < MAX_RETRIES);\n  }\n\n  messaging.logEvents = [];\n  // schedule for next logging\n  _processQueue(messaging, LOG_INTERVAL_IN_MS);\n}\n\nfunction isRetriableError(response: Response): boolean {\n  const httpStatus = response.status;\n\n  return (\n    httpStatus === 429 ||\n    httpStatus === 500 ||\n    httpStatus === 503 ||\n    httpStatus === 504\n  );\n}\n\nexport async function stageLog(\n  messaging: MessagingService,\n  internalPayload: MessagePayloadInternal\n): Promise<void> {\n  const fcmEvent = createFcmEvent(\n    internalPayload,\n    await messaging.firebaseDependencies.installations.getId()\n  );\n\n  createAndEnqueueLogEvent(messaging, fcmEvent, internalPayload.productId);\n}\n\nfunction createFcmEvent(\n  internalPayload: MessagePayloadInternal,\n  fid: string\n): FcmEvent {\n  const fcmEvent = {} as FcmEvent;\n\n  /* eslint-disable camelcase */\n  // some fields should always be non-null. Still check to ensure.\n  if (!!internalPayload.from) {\n    fcmEvent.project_number = internalPayload.from;\n  }\n\n  if (!!internalPayload.fcmMessageId) {\n    fcmEvent.message_id = internalPayload.fcmMessageId;\n  }\n\n  fcmEvent.instance_id = fid;\n\n  if (!!internalPayload.notification) {\n    fcmEvent.message_type = MessageType.DISPLAY_NOTIFICATION.toString();\n  } else {\n    fcmEvent.message_type = MessageType.DATA_MESSAGE.toString();\n  }\n\n  fcmEvent.sdk_platform = SDK_PLATFORM_WEB.toString();\n  fcmEvent.package_name = self.origin.replace(/(^\\w+:|^)\\/\\//, '');\n\n  if (!!internalPayload.collapse_key) {\n    fcmEvent.collapse_key = internalPayload.collapse_key;\n  }\n\n  fcmEvent.event = EVENT_MESSAGE_DELIVERED.toString();\n\n  if (!!internalPayload.fcmOptions?.analytics_label) {\n    fcmEvent.analytics_label = internalPayload.fcmOptions?.analytics_label;\n  }\n\n  /* eslint-enable camelcase */\n  return fcmEvent;\n}\n\nfunction createAndEnqueueLogEvent(\n  messaging: MessagingService,\n  fcmEvent: FcmEvent,\n  productId: number\n): void {\n  const logEvent = {} as LogEvent;\n\n  /* eslint-disable camelcase */\n  logEvent.event_time_ms = Math.floor(Date.now()).toString();\n  logEvent.source_extension_json_proto3 = JSON.stringify({\n    messaging_client_event: fcmEvent\n  });\n\n  if (!!productId) {\n    logEvent.compliance_data = buildComplianceData(productId);\n  }\n  // eslint-disable-next-line camelcase\n\n  messaging.logEvents.push(logEvent);\n}\n\nfunction buildComplianceData(productId: number): ComplianceData {\n  const complianceData: ComplianceData = {\n    privacy_context: {\n      prequest: {\n        origin_associated_product_id: productId\n      }\n    }\n  };\n\n  return complianceData;\n}\n\nexport function _createLogRequest(logEventQueue: LogEvent[]): LogRequest {\n  const logRequest = {} as LogRequest;\n\n  /* eslint-disable camelcase */\n  logRequest.log_source = FCM_LOG_SOURCE.toString();\n  logRequest.log_event = logEventQueue;\n  /* eslint-enable camelcase */\n\n  return logRequest;\n}\n\nexport function _mergeStrings(s1: string, s2: string): string {\n  const resultArray = [];\n  for (let i = 0; i < s1.length; i++) {\n    resultArray.push(s1.charAt(i));\n    if (i < s2.length) {\n      resultArray.push(s2.charAt(i));\n    }\n  }\n\n  return resultArray.join('');\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { ERROR_FACTORY, ErrorCode } from '../util/errors';\nimport { FirebaseApp, FirebaseOptions } from '@firebase/app';\n\nimport { AppConfig } from '../interfaces/app-config';\nimport { FirebaseError } from '@firebase/util';\n\nexport function extractAppConfig(app: FirebaseApp): AppConfig {\n  if (!app || !app.options) {\n    throw getMissingValueError('App Configuration Object');\n  }\n\n  if (!app.name) {\n    throw getMissingValueError('App Name');\n  }\n\n  // Required app config keys\n  const configKeys: ReadonlyArray<keyof FirebaseOptions> = [\n    'projectId',\n    'apiKey',\n    'appId',\n    'messagingSenderId'\n  ];\n\n  const { options } = app;\n  for (const keyName of configKeys) {\n    if (!options[keyName]) {\n      throw getMissingValueError(keyName);\n    }\n  }\n\n  return {\n    appName: app.name,\n    projectId: options.projectId!,\n    apiKey: options.apiKey!,\n    appId: options.appId!,\n    senderId: options.messagingSenderId!\n  };\n}\n\nfunction getMissingValueError(valueName: string): FirebaseError {\n  return ERROR_FACTORY.create(ErrorCode.MISSING_APP_CONFIG_VALUES, {\n    valueName\n  });\n}\n", "/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { FirebaseApp, _FirebaseService } from '@firebase/app';\nimport { MessagePayload, NextFn, Observer } from './interfaces/public-types';\n\nimport { FirebaseAnalyticsInternalName } from '@firebase/analytics-interop-types';\nimport { FirebaseInternalDependencies } from './interfaces/internal-dependencies';\nimport { LogEvent } from './interfaces/logging-types';\nimport { Provider } from '@firebase/component';\nimport { _FirebaseInstallationsInternal } from '@firebase/installations';\nimport { extractAppConfig } from './helpers/extract-app-config';\n\nexport class MessagingService implements _FirebaseService {\n  readonly app!: FirebaseApp;\n  readonly firebaseDependencies!: FirebaseInternalDependencies;\n\n  swRegistration?: ServiceWorkerRegistration;\n  vapidKey?: string;\n  // logging is only done with end user consent. Default to false.\n  deliveryMetricsExportedToBigQueryEnabled: boolean = false;\n\n  onBackgroundMessageHandler:\n    | NextFn<MessagePayload>\n    | Observer<MessagePayload>\n    | null = null;\n\n  onMessageHandler: NextFn<MessagePayload> | Observer<MessagePayload> | null =\n    null;\n\n  logEvents: LogEvent[] = [];\n  isLogServiceStarted: boolean = false;\n\n  constructor(\n    app: FirebaseApp,\n    installations: _FirebaseInstallationsInternal,\n    analyticsProvider: Provider<FirebaseAnalyticsInternalName>\n  ) {\n    const appConfig = extractAppConfig(app);\n\n    this.firebaseDependencies = {\n      app,\n      appConfig,\n      installations,\n      analyticsProvider\n    };\n  }\n\n  _delete(): Promise<void> {\n    return Promise.resolve();\n  }\n}\n", "/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { DEFAULT_SW_PATH, DEFAULT_SW_SCOPE } from '../util/constants';\nimport { ERROR_FACTORY, ErrorCode } from '../util/errors';\n\nimport { MessagingService } from '../messaging-service';\n\nexport async function registerDefaultSw(\n  messaging: MessagingService\n): Promise<void> {\n  try {\n    messaging.swRegistration = await navigator.serviceWorker.register(\n      DEFAULT_SW_PATH,\n      {\n        scope: DEFAULT_SW_SCOPE\n      }\n    );\n\n    // The timing when browser updates sw when sw has an update is unreliable from experiment. It\n    // leads to version conflict when the SDK upgrades to a newer version in the main page, but sw\n    // is stuck with the old version. For example,\n    // https://github.com/firebase/firebase-js-sdk/issues/2590 The following line reliably updates\n    // sw if there was an update.\n    messaging.swRegistration.update().catch(() => {\n      /* it is non blocking and we don't care if it failed */\n    });\n  } catch (e) {\n    throw ERROR_FACTORY.create(ErrorCode.FAILED_DEFAULT_REGISTRATION, {\n      browserErrorMessage: (e as Error)?.message\n    });\n  }\n}\n", "/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { ERROR_FACTORY, ErrorCode } from '../util/errors';\n\nimport { MessagingService } from '../messaging-service';\nimport { registerDefaultSw } from './registerDefaultSw';\n\nexport async function updateSwReg(\n  messaging: MessagingService,\n  swRegistration?: ServiceWorkerRegistration | undefined\n): Promise<void> {\n  if (!swRegistration && !messaging.swRegistration) {\n    await registerDefaultSw(messaging);\n  }\n\n  if (!swRegistration && !!messaging.swRegistration) {\n    return;\n  }\n\n  if (!(swRegistration instanceof ServiceWorkerRegistration)) {\n    throw ERROR_FACTORY.create(ErrorCode.INVALID_SW_REGISTRATION);\n  }\n\n  messaging.swRegistration = swRegistration;\n}\n", "/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { DEFAULT_VAPID_KEY } from '../util/constants';\nimport { MessagingService } from '../messaging-service';\n\nexport async function updateVapidKey(\n  messaging: MessagingService,\n  vapidKey?: string | undefined\n): Promise<void> {\n  if (!!vapidKey) {\n    messaging.vapidKey = vapidKey;\n  } else if (!messaging.vapidKey) {\n    messaging.vapidKey = DEFAULT_VAPID_KEY;\n  }\n}\n", "/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { ERROR_FACTORY, ErrorCode } from '../util/errors';\n\nimport { MessagingService } from '../messaging-service';\nimport { getTokenInternal } from '../internals/token-manager';\nimport { updateSwReg } from '../helpers/updateSwReg';\nimport { updateVapidKey } from '../helpers/updateVapidKey';\nimport { GetTokenOptions } from '../interfaces/public-types';\n\nexport async function getToken(\n  messaging: MessagingService,\n  options?: GetTokenOptions\n): Promise<string> {\n  if (!navigator) {\n    throw ERROR_FACTORY.create(ErrorCode.AVAILABLE_IN_WINDOW);\n  }\n\n  if (Notification.permission === 'default') {\n    await Notification.requestPermission();\n  }\n\n  if (Notification.permission !== 'granted') {\n    throw ERROR_FACTORY.create(ErrorCode.PERMISSION_BLOCKED);\n  }\n\n  await updateVapidKey(messaging, options?.vapidKey);\n  await updateSwReg(messaging, options?.serviceWorkerRegistration);\n\n  return getTokenInternal(messaging);\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {\n  CONSOLE_CAMPAIGN_ID,\n  CONSOLE_CAMPAIGN_NAME,\n  CONSOLE_CAMPAIGN_TIME\n} from '../util/constants';\nimport {\n  ConsoleMessageData,\n  MessageType\n} from '../interfaces/internal-message-payload';\n\nimport { MessagingService } from '../messaging-service';\n\nexport async function logToScion(\n  messaging: MessagingService,\n  messageType: MessageType,\n  data: ConsoleMessageData\n): Promise<void> {\n  const eventType = getEventType(messageType);\n  const analytics =\n    await messaging.firebaseDependencies.analyticsProvider.get();\n  analytics.logEvent(eventType, {\n    /* eslint-disable camelcase */\n    message_id: data[CONSOLE_CAMPAIGN_ID],\n    message_name: data[CONSOLE_CAMPAIGN_NAME],\n    message_time: data[CONSOLE_CAMPAIGN_TIME],\n    message_device_time: Math.floor(Date.now() / 1000)\n    /* eslint-enable camelcase */\n  });\n}\n\nfunction getEventType(messageType: MessageType): string {\n  switch (messageType) {\n    case MessageType.NOTIFICATION_CLICKED:\n      return 'notification_open';\n    case MessageType.PUSH_RECEIVED:\n      return 'notification_foreground';\n    default:\n      throw new Error();\n  }\n}\n", "/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {\n  MessagePayloadInternal,\n  MessageType\n} from '../interfaces/internal-message-payload';\n\nimport { CONSOLE_CAMPAIGN_ANALYTICS_ENABLED } from '../util/constants';\nimport { MessagingService } from '../messaging-service';\nimport { externalizePayload } from '../helpers/externalizePayload';\nimport { isConsoleMessage } from '../helpers/is-console-message';\nimport { logToScion } from '../helpers/logToScion';\n\nexport async function messageEventListener(\n  messaging: MessagingService,\n  event: MessageEvent\n): Promise<void> {\n  const internalPayload = event.data as MessagePayloadInternal;\n\n  if (!internalPayload.isFirebaseMessaging) {\n    return;\n  }\n\n  if (\n    messaging.onMessageHandler &&\n    internalPayload.messageType === MessageType.PUSH_RECEIVED\n  ) {\n    if (typeof messaging.onMessageHandler === 'function') {\n      messaging.onMessageHandler(externalizePayload(internalPayload));\n    } else {\n      messaging.onMessageHandler.next(externalizePayload(internalPayload));\n    }\n  }\n\n  // Log to Scion if applicable\n  const dataPayload = internalPayload.data;\n  if (\n    isConsoleMessage(dataPayload) &&\n    dataPayload[CONSOLE_CAMPAIGN_ANALYTICS_ENABLED] === '1'\n  ) {\n    await logToScion(messaging, internalPayload.messageType!, dataPayload);\n  }\n}\n", "/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {\n  Component,\n  ComponentContainer,\n  ComponentType,\n  InstanceFactory\n} from '@firebase/component';\nimport {\n  onNotificationClick,\n  onPush,\n  onSubChange\n} from '../listeners/sw-listeners';\n\nimport { GetTokenOptions } from '../interfaces/public-types';\nimport { MessagingInternal } from '@firebase/messaging-interop-types';\nimport { MessagingService } from '../messaging-service';\nimport { ServiceWorkerGlobalScope } from '../util/sw-types';\nimport { _registerComponent, registerVersion } from '@firebase/app';\nimport { getToken } from '../api/getToken';\nimport { messageEventListener } from '../listeners/window-listener';\n\nimport { name, version } from '../../package.json';\n\nconst WindowMessagingFactory: InstanceFactory<'messaging'> = (\n  container: ComponentContainer\n) => {\n  const messaging = new MessagingService(\n    container.getProvider('app').getImmediate(),\n    container.getProvider('installations-internal').getImmediate(),\n    container.getProvider('analytics-internal')\n  );\n\n  navigator.serviceWorker.addEventListener('message', e =>\n    messageEventListener(messaging as MessagingService, e)\n  );\n\n  return messaging;\n};\n\nconst WindowMessagingInternalFactory: InstanceFactory<'messaging-internal'> = (\n  container: ComponentContainer\n) => {\n  const messaging = container\n    .getProvider('messaging')\n    .getImmediate() as MessagingService;\n\n  const messagingInternal: MessagingInternal = {\n    getToken: (options?: GetTokenOptions) => getToken(messaging, options)\n  };\n\n  return messagingInternal;\n};\n\ndeclare const self: ServiceWorkerGlobalScope;\nconst SwMessagingFactory: InstanceFactory<'messaging'> = (\n  container: ComponentContainer\n) => {\n  const messaging = new MessagingService(\n    container.getProvider('app').getImmediate(),\n    container.getProvider('installations-internal').getImmediate(),\n    container.getProvider('analytics-internal')\n  );\n\n  self.addEventListener('push', e => {\n    e.waitUntil(onPush(e, messaging as MessagingService));\n  });\n  self.addEventListener('pushsubscriptionchange', e => {\n    e.waitUntil(onSubChange(e, messaging as MessagingService));\n  });\n  self.addEventListener('notificationclick', e => {\n    e.waitUntil(onNotificationClick(e));\n  });\n\n  return messaging;\n};\n\nexport function registerMessagingInWindow(): void {\n  _registerComponent(\n    new Component('messaging', WindowMessagingFactory, ComponentType.PUBLIC)\n  );\n\n  _registerComponent(\n    new Component(\n      'messaging-internal',\n      WindowMessagingInternalFactory,\n      ComponentType.PRIVATE\n    )\n  );\n\n  registerVersion(name, version);\n  // BUILD_TARGET will be replaced by values like esm5, esm2017, cjs5, etc during the compilation\n  registerVersion(name, version, '__BUILD_TARGET__');\n}\n\n/**\n * The messaging instance registered in sw is named differently than that of in client. This is\n * because both `registerMessagingInWindow` and `registerMessagingInSw` would be called in\n * `messaging-compat` and component with the same name can only be registered once.\n */\nexport function registerMessagingInSw(): void {\n  _registerComponent(\n    new Component('messaging-sw', SwMessagingFactory, ComponentType.PUBLIC)\n  );\n}\n", "/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {\n  areCookiesEnabled,\n  isIndexedDBAvailable,\n  validateIndexedDBOpenable\n} from '@firebase/util';\n\n/**\n * Checks if all required APIs exist in the browser.\n * @returns a Promise that resolves to a boolean.\n *\n * @public\n */\nexport async function isWindowSupported(): Promise<boolean> {\n  try {\n    // This throws if open() is unsupported, so adding it to the conditional\n    // statement below can cause an uncaught error.\n    await validateIndexedDBOpenable();\n  } catch (e) {\n    return false;\n  }\n  // firebase-js-sdk/issues/2393 reveals that idb#open in Safari iframe and Firefox private browsing\n  // might be prohibited to run. In these contexts, an error would be thrown during the messaging\n  // instantiating phase, informing the developers to import/call isSupported for special handling.\n  return (\n    typeof window !== 'undefined' &&\n    isIndexedDBAvailable() &&\n    areCookiesEnabled() &&\n    'serviceWorker' in navigator &&\n    'PushManager' in window &&\n    'Notification' in window &&\n    'fetch' in window &&\n    ServiceWorkerRegistration.prototype.hasOwnProperty('showNotification') &&\n    PushSubscription.prototype.hasOwnProperty('getKey')\n  );\n}\n\n/**\n * Checks whether all required APIs exist within SW Context\n * @returns a Promise that resolves to a boolean.\n *\n * @public\n */\nexport async function isSwSupported(): Promise<boolean> {\n  // firebase-js-sdk/issues/2393 reveals that idb#open in Safari iframe and Firefox private browsing\n  // might be prohibited to run. In these contexts, an error would be thrown during the messaging\n  // instantiating phase, informing the developers to import/call isSupported for special handling.\n  return (\n    isIndexedDBAvailable() &&\n    (await validateIndexedDBOpenable()) &&\n    'PushManager' in self &&\n    'Notification' in self &&\n    ServiceWorkerRegistration.prototype.hasOwnProperty('showNotification') &&\n    PushSubscription.prototype.hasOwnProperty('getKey')\n  );\n}\n", "/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { ERROR_FACTORY, ErrorCode } from '../util/errors';\n\nimport { MessagingService } from '../messaging-service';\nimport { deleteTokenInternal } from '../internals/token-manager';\nimport { registerDefaultSw } from '../helpers/registerDefaultSw';\n\nexport async function deleteToken(\n  messaging: MessagingService\n): Promise<boolean> {\n  if (!navigator) {\n    throw ERROR_FACTORY.create(ErrorCode.AVAILABLE_IN_WINDOW);\n  }\n\n  if (!messaging.swRegistration) {\n    await registerDefaultSw(messaging);\n  }\n\n  return deleteTokenInternal(messaging);\n}\n", "/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { ERROR_FACTORY, ErrorCode } from '../util/errors';\n\nimport {\n  MessagePayload,\n  NextFn,\n  Observer,\n  Unsubscribe\n} from '../interfaces/public-types';\nimport { MessagingService } from '../messaging-service';\n\nexport function onMessage(\n  messaging: MessagingService,\n  nextOrObserver: NextFn<MessagePayload> | Observer<MessagePayload>\n): Unsubscribe {\n  if (!navigator) {\n    throw ERROR_FACTORY.create(ErrorCode.AVAILABLE_IN_WINDOW);\n  }\n\n  messaging.onMessageHandler = nextOrObserver;\n\n  return () => {\n    messaging.onMessageHandler = null;\n  };\n}\n", "/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { ERROR_FACTORY, ErrorCode } from './util/errors';\nimport { FirebaseApp, _getProvider, getApp } from '@firebase/app';\nimport {\n  GetTokenOptions,\n  MessagePayload,\n  Messaging\n} from './interfaces/public-types';\nimport {\n  NextFn,\n  Observer,\n  Unsubscribe,\n  getModularInstance\n} from '@firebase/util';\nimport { isSwSupported, isWindowSupported } from './api/isSupported';\n\nimport { MessagingService } from './messaging-service';\nimport { deleteToken as _deleteToken } from './api/deleteToken';\nimport { getToken as _getToken } from './api/getToken';\nimport { onBackgroundMessage as _onBackgroundMessage } from './api/onBackgroundMessage';\nimport { onMessage as _onMessage } from './api/onMessage';\nimport { _setDeliveryMetricsExportedToBigQueryEnabled } from './api/setDeliveryMetricsExportedToBigQueryEnabled';\n\n/**\n * Retrieves a Firebase Cloud Messaging instance.\n *\n * @returns The Firebase Cloud Messaging instance associated with the provided firebase app.\n *\n * @public\n */\nexport function getMessagingInWindow(app: FirebaseApp = getApp()): Messaging {\n  // Conscious decision to make this async check non-blocking during the messaging instance\n  // initialization phase for performance consideration. An error would be thrown latter for\n  // developer's information. Developers can then choose to import and call `isSupported` for\n  // special handling.\n  isWindowSupported().then(\n    isSupported => {\n      // If `isWindowSupported()` resolved, but returned false.\n      if (!isSupported) {\n        throw ERROR_FACTORY.create(ErrorCode.UNSUPPORTED_BROWSER);\n      }\n    },\n    _ => {\n      // If `isWindowSupported()` rejected.\n      throw ERROR_FACTORY.create(ErrorCode.INDEXED_DB_UNSUPPORTED);\n    }\n  );\n  return _getProvider(getModularInstance(app), 'messaging').getImmediate();\n}\n\n/**\n * Retrieves a Firebase Cloud Messaging instance.\n *\n * @returns The Firebase Cloud Messaging instance associated with the provided firebase app.\n *\n * @public\n */\nexport function getMessagingInSw(app: FirebaseApp = getApp()): Messaging {\n  // Conscious decision to make this async check non-blocking during the messaging instance\n  // initialization phase for performance consideration. An error would be thrown latter for\n  // developer's information. Developers can then choose to import and call `isSupported` for\n  // special handling.\n  isSwSupported().then(\n    isSupported => {\n      // If `isSwSupported()` resolved, but returned false.\n      if (!isSupported) {\n        throw ERROR_FACTORY.create(ErrorCode.UNSUPPORTED_BROWSER);\n      }\n    },\n    _ => {\n      // If `isSwSupported()` rejected.\n      throw ERROR_FACTORY.create(ErrorCode.INDEXED_DB_UNSUPPORTED);\n    }\n  );\n  return _getProvider(getModularInstance(app), 'messaging-sw').getImmediate();\n}\n\n/**\n * Subscribes the {@link Messaging} instance to push notifications. Returns a Firebase Cloud\n * Messaging registration token that can be used to send push messages to that {@link Messaging}\n * instance.\n *\n * If notification permission isn't already granted, this method asks the user for permission. The\n * returned promise rejects if the user does not allow the app to show notifications.\n *\n * @param messaging - The {@link Messaging} instance.\n * @param options - Provides an optional vapid key and an optional service worker registration.\n *\n * @returns The promise resolves with an FCM registration token.\n *\n * @public\n */\nexport async function getToken(\n  messaging: Messaging,\n  options?: GetTokenOptions\n): Promise<string> {\n  messaging = getModularInstance(messaging);\n  return _getToken(messaging as MessagingService, options);\n}\n\n/**\n * Deletes the registration token associated with this {@link Messaging} instance and unsubscribes\n * the {@link Messaging} instance from the push subscription.\n *\n * @param messaging - The {@link Messaging} instance.\n *\n * @returns The promise resolves when the token has been successfully deleted.\n *\n * @public\n */\nexport function deleteToken(messaging: Messaging): Promise<boolean> {\n  messaging = getModularInstance(messaging);\n  return _deleteToken(messaging as MessagingService);\n}\n\n/**\n * When a push message is received and the user is currently on a page for your origin, the\n * message is passed to the page and an `onMessage()` event is dispatched with the payload of\n * the push message.\n *\n *\n * @param messaging - The {@link Messaging} instance.\n * @param nextOrObserver - This function, or observer object with `next` defined,\n *     is called when a message is received and the user is currently viewing your page.\n * @returns To stop listening for messages execute this returned function.\n *\n * @public\n */\nexport function onMessage(\n  messaging: Messaging,\n  nextOrObserver: NextFn<MessagePayload> | Observer<MessagePayload>\n): Unsubscribe {\n  messaging = getModularInstance(messaging);\n  return _onMessage(messaging as MessagingService, nextOrObserver);\n}\n\n/**\n * Called when a message is received while the app is in the background. An app is considered to be\n * in the background if no active window is displayed.\n *\n * @param messaging - The {@link Messaging} instance.\n * @param nextOrObserver - This function, or observer object with `next` defined, is called when a\n * message is received and the app is currently in the background.\n *\n * @returns To stop listening for messages execute this returned function\n *\n * @public\n */\nexport function onBackgroundMessage(\n  messaging: Messaging,\n  nextOrObserver: NextFn<MessagePayload> | Observer<MessagePayload>\n): Unsubscribe {\n  messaging = getModularInstance(messaging);\n  return _onBackgroundMessage(messaging as MessagingService, nextOrObserver);\n}\n\n/**\n * Enables or disables Firebase Cloud Messaging message delivery metrics export to BigQuery. By\n * default, message delivery metrics are not exported to BigQuery. Use this method to enable or\n * disable the export at runtime.\n *\n * @param messaging - The `FirebaseMessaging` instance.\n * @param enable - Whether Firebase Cloud Messaging should export message delivery metrics to\n * BigQuery.\n *\n * @public\n */\nexport function experimentalSetDeliveryMetricsExportedToBigQueryEnabled(\n  messaging: Messaging,\n  enable: boolean\n): void {\n  messaging = getModularInstance(messaging);\n  return _setDeliveryMetricsExportedToBigQueryEnabled(messaging, enable);\n}\n", "/**\n * The Firebase Cloud Messaging Web SDK.\n * This SDK does not work in a Node.js environment.\n *\n * @packageDocumentation\n */\n\n/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport '@firebase/installations';\n\nimport { Messaging } from './interfaces/public-types';\nimport { registerMessagingInWindow } from './helpers/register';\n\nexport {\n  getToken,\n  deleteToken,\n  onMessage,\n  getMessagingInWindow as getMessaging\n} from './api';\nexport { isWindowSupported as isSupported } from './api/isSupported';\nexport * from './interfaces/public-types';\n\ndeclare module '@firebase/component' {\n  interface NameServiceMapping {\n    'messaging': Messaging;\n  }\n}\n\nregisterMessagingInWindow();\n"], "names": ["MessageType", "getToken", "deleteToken", "onMessage", "_getToken", "_deleteToken", "_onMessage"], "mappings": ";;;;;;;AAAA;;;;;;;;;;;;;;;AAeG;AAEI,IAAM,eAAe,GAAG,2BAA2B,CAAC;AACpD,IAAM,gBAAgB,GAAG,sCAAsC,CAAC;AAEhE,IAAM,iBAAiB,GAC5B,yFAAyF,CAAC;AAErF,IAAM,QAAQ,GAAG,4CAA4C,CAAC;AAK9D,IAAM,mBAAmB,GAAG,iBAAiB,CAAC;AAC9C,IAAM,qBAAqB,GAAG,gBAAgB,CAAC;AAC/C,IAAM,qBAAqB,GAAG,eAAe,CAAC;AACrD;AACO,IAAM,kCAAkC,GAAG,cAAc,CAAC;AAejE,IAAYA,aAGX,CAAA;AAHD,CAAA,UAAY,WAAW,EAAA;AACrB,IAAA,WAAA,CAAA,WAAA,CAAA,cAAA,CAAA,GAAA,CAAA,CAAA,GAAA,cAAgB,CAAA;AAChB,IAAA,WAAA,CAAA,WAAA,CAAA,sBAAA,CAAA,GAAA,CAAA,CAAA,GAAA,sBAAwB,CAAA;AAC1B,CAAC,EAHWA,aAAW,KAAXA,aAAW,GAGtB,EAAA,CAAA,CAAA;;AClDD;;;;;;;;;;;;;AAaG;AA0CH,IAAY,WAGX,CAAA;AAHD,CAAA,UAAY,WAAW,EAAA;AACrB,IAAA,WAAA,CAAA,eAAA,CAAA,GAAA,eAA+B,CAAA;AAC/B,IAAA,WAAA,CAAA,sBAAA,CAAA,GAAA,sBAA6C,CAAA;AAC/C,CAAC,EAHW,WAAW,KAAX,WAAW,GAGtB,EAAA,CAAA,CAAA;;AC1DD;;;;;;;;;;;;;;;AAeG;AAEG,SAAU,aAAa,CAAC,KAA+B,EAAA;AAC3D,IAAA,IAAM,UAAU,GAAG,IAAI,UAAU,CAAC,KAAK,CAAC,CAAC;AACzC,IAAA,IAAM,YAAY,GAAG,IAAI,CAAC,MAAM,CAAC,YAAY,CAAA,KAAA,CAAnB,MAAM,EAAA,aAAA,CAAA,EAAA,EAAA,MAAA,CAAiB,UAAU,CAAA,EAAA,KAAA,CAAA,CAAA,CAAE,CAAC;IAC9D,OAAO,YAAY,CAAC,OAAO,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC,OAAO,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC,OAAO,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC;AAChF,CAAC;AAEK,SAAU,aAAa,CAAC,YAAoB,EAAA;IAChD,IAAM,OAAO,GAAG,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,YAAY,CAAC,MAAM,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC;AAChE,IAAA,IAAM,MAAM,GAAG,CAAC,YAAY,GAAG,OAAO;AACnC,SAAA,OAAO,CAAC,KAAK,EAAE,GAAG,CAAC;AACnB,SAAA,OAAO,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC;AAEtB,IAAA,IAAM,OAAO,GAAG,IAAI,CAAC,MAAM,CAAC,CAAC;IAC7B,IAAM,WAAW,GAAG,IAAI,UAAU,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;AAEnD,IAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,OAAO,CAAC,MAAM,EAAE,EAAE,CAAC,EAAE;QACvC,WAAW,CAAC,CAAC,CAAC,GAAG,OAAO,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;AACxC,KAAA;AACD,IAAA,OAAO,WAAW,CAAC;AACrB;;ACpCA;;;;;;;;;;;;;;;AAeG;AA8CH,IAAM,WAAW,GAAG,sBAAsB,CAAC;AAC3C;;;AAGG;AACH,IAAM,cAAc,GAAG,CAAC,CAAC;AACzB,IAAM,qBAAqB,GAAG,wBAAwB,CAAC;AAEjD,SAAgB,kBAAkB,CACtC,QAAgB,EAAA;;;;;;;AAEZ,oBAAA,IAAA,EAAA,WAAW,IAAI,SAAS,CAAA,EAAxB,OAAwB,CAAA,CAAA,YAAA,CAAA,CAAA,CAAA;AAGR,oBAAA,OAAA,CAAA,CAAA,YAChB,SAGD,CAAC,SAAS,EAAE,CAAA,CAAA;;AAJP,oBAAA,SAAS,GAAG,EAIL,CAAA,IAAA,EAAA,CAAA;AACP,oBAAA,OAAO,GAAG,SAAS,CAAC,GAAG,CAAC,UAAA,EAAE,EAAI,EAAA,OAAA,EAAE,CAAC,IAAI,CAAP,EAAO,CAAC,CAAC;AAE7C,oBAAA,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,WAAW,CAAC,EAAE;;AAElC,wBAAA,OAAA,CAAA,CAAA,aAAO,IAAI,CAAC,CAAA;AACb,qBAAA;;;oBAGC,YAAY,GAAwB,IAAI,CAAC;AAElC,oBAAA,OAAA,CAAA,CAAA,YAAM,MAAM,CAAC,WAAW,EAAE,cAAc,EAAE;4BACnD,OAAO,EAAE,UAAO,EAAE,EAAE,UAAU,EAAE,UAAU,EAAE,kBAAkB,EAAA,EAAA,OAAA,SAAA,CAAA,KAAA,EAAA,KAAA,CAAA,EAAA,KAAA,CAAA,EAAA,YAAA;;;;;;4CAC5D,IAAI,UAAU,GAAG,CAAC,EAAE;;gDAElB,OAAO,CAAA,CAAA,YAAA,CAAA;AACR,6CAAA;4CAED,IAAI,CAAC,EAAE,CAAC,gBAAgB,CAAC,QAAQ,CAAC,qBAAqB,CAAC,EAAE;;gDAExD,OAAO,CAAA,CAAA,YAAA,CAAA;AACR,6CAAA;AAEK,4CAAA,WAAW,GAAG,kBAAkB,CAAC,WAAW,CAAC,qBAAqB,CAAC,CAAC;4CAC5D,OAAM,CAAA,CAAA,YAAA,WAAW,CAAC,KAAK,CAAC,aAAa,CAAC,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAA,CAAA;;AAA5D,4CAAA,KAAK,GAAG,EAAoD,CAAA,IAAA,EAAA,CAAA;AAClE,4CAAA,OAAA,CAAA,CAAA,YAAM,WAAW,CAAC,KAAK,EAAE,CAAA,CAAA;;AAAzB,4CAAA,EAAA,CAAA,IAAA,EAAyB,CAAC;4CAE1B,IAAI,CAAC,KAAK,EAAE;;gDAEV,OAAO,CAAA,CAAA,YAAA,CAAA;AACR,6CAAA;4CAED,IAAI,UAAU,KAAK,CAAC,EAAE;gDACd,UAAU,GAAG,KAAuB,CAAC;AAE3C,gDAAA,IAAI,CAAC,UAAU,CAAC,IAAI,IAAI,CAAC,UAAU,CAAC,MAAM,IAAI,CAAC,UAAU,CAAC,QAAQ,EAAE;oDAClE,OAAO,CAAA,CAAA,YAAA,CAAA;AACR,iDAAA;AAED,gDAAA,YAAY,GAAG;oDACb,KAAK,EAAE,UAAU,CAAC,QAAQ;oDAC1B,UAAU,EAAE,MAAA,UAAU,CAAC,UAAU,MAAI,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,EAAA,GAAA,IAAI,CAAC,GAAG,EAAE;AAC/C,oDAAA,mBAAmB,EAAE;wDACnB,IAAI,EAAE,UAAU,CAAC,IAAI;wDACrB,MAAM,EAAE,UAAU,CAAC,MAAM;wDACzB,QAAQ,EAAE,UAAU,CAAC,QAAQ;wDAC7B,OAAO,EAAE,UAAU,CAAC,OAAO;AAC3B,wDAAA,QAAQ,EACN,OAAO,UAAU,CAAC,QAAQ,KAAK,QAAQ;8DACnC,UAAU,CAAC,QAAQ;AACrB,8DAAE,aAAa,CAAC,UAAU,CAAC,QAAQ,CAAC;AACzC,qDAAA;iDACF,CAAC;AACH,6CAAA;iDAAM,IAAI,UAAU,KAAK,CAAC,EAAE;gDACrB,UAAU,GAAG,KAAuB,CAAC;AAE3C,gDAAA,YAAY,GAAG;oDACb,KAAK,EAAE,UAAU,CAAC,QAAQ;oDAC1B,UAAU,EAAE,UAAU,CAAC,UAAU;AACjC,oDAAA,mBAAmB,EAAE;AACnB,wDAAA,IAAI,EAAE,aAAa,CAAC,UAAU,CAAC,IAAI,CAAC;AACpC,wDAAA,MAAM,EAAE,aAAa,CAAC,UAAU,CAAC,MAAM,CAAC;wDACxC,QAAQ,EAAE,UAAU,CAAC,QAAQ;wDAC7B,OAAO,EAAE,UAAU,CAAC,OAAO;AAC3B,wDAAA,QAAQ,EAAE,aAAa,CAAC,UAAU,CAAC,QAAQ,CAAC;AAC7C,qDAAA;iDACF,CAAC;AACH,6CAAA;iDAAM,IAAI,UAAU,KAAK,CAAC,EAAE;gDACrB,UAAU,GAAG,KAAuB,CAAC;AAE3C,gDAAA,YAAY,GAAG;oDACb,KAAK,EAAE,UAAU,CAAC,QAAQ;oDAC1B,UAAU,EAAE,UAAU,CAAC,UAAU;AACjC,oDAAA,mBAAmB,EAAE;AACnB,wDAAA,IAAI,EAAE,aAAa,CAAC,UAAU,CAAC,IAAI,CAAC;AACpC,wDAAA,MAAM,EAAE,aAAa,CAAC,UAAU,CAAC,MAAM,CAAC;wDACxC,QAAQ,EAAE,UAAU,CAAC,QAAQ;wDAC7B,OAAO,EAAE,UAAU,CAAC,OAAO;AAC3B,wDAAA,QAAQ,EAAE,aAAa,CAAC,UAAU,CAAC,QAAQ,CAAC;AAC7C,qDAAA;iDACF,CAAC;AACH,6CAAA;;;;AACF,6BAAA,CAAA,CAAA,EAAA;AACF,yBAAA,CAAC,CAAA,CAAA;;AAxEI,oBAAA,EAAE,GAAG,EAwET,CAAA,IAAA,EAAA,CAAA;oBACF,EAAE,CAAC,KAAK,EAAE,CAAC;;AAGX,oBAAA,OAAA,CAAA,CAAA,YAAM,QAAQ,CAAC,WAAW,CAAC,CAAA,CAAA;;;AAA3B,oBAAA,EAAA,CAAA,IAAA,EAA2B,CAAC;AAC5B,oBAAA,OAAA,CAAA,CAAA,YAAM,QAAQ,CAAC,sBAAsB,CAAC,CAAA,CAAA;;AAAtC,oBAAA,EAAA,CAAA,IAAA,EAAsC,CAAC;AACvC,oBAAA,OAAA,CAAA,CAAA,YAAM,QAAQ,CAAC,WAAW,CAAC,CAAA,CAAA;;AAA3B,oBAAA,EAAA,CAAA,IAAA,EAA2B,CAAC;AAE5B,oBAAA,OAAA,CAAA,CAAA,aAAO,iBAAiB,CAAC,YAAY,CAAC,GAAG,YAAY,GAAG,IAAI,CAAC,CAAA;;;;AAC9D,CAAA;AAED,SAAS,iBAAiB,CACxB,YAAiC,EAAA;AAEjC,IAAA,IAAI,CAAC,YAAY,IAAI,CAAC,YAAY,CAAC,mBAAmB,EAAE;AACtD,QAAA,OAAO,KAAK,CAAC;AACd,KAAA;AACO,IAAA,IAAA,mBAAmB,GAAK,YAAY,CAAA,mBAAjB,CAAkB;AAC7C,IAAA,QACE,OAAO,YAAY,CAAC,UAAU,KAAK,QAAQ;QAC3C,YAAY,CAAC,UAAU,GAAG,CAAC;AAC3B,QAAA,OAAO,YAAY,CAAC,KAAK,KAAK,QAAQ;AACtC,QAAA,YAAY,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC;AAC7B,QAAA,OAAO,mBAAmB,CAAC,IAAI,KAAK,QAAQ;AAC5C,QAAA,mBAAmB,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC;AACnC,QAAA,OAAO,mBAAmB,CAAC,MAAM,KAAK,QAAQ;AAC9C,QAAA,mBAAmB,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC;AACrC,QAAA,OAAO,mBAAmB,CAAC,QAAQ,KAAK,QAAQ;AAChD,QAAA,mBAAmB,CAAC,QAAQ,CAAC,MAAM,GAAG,CAAC;AACvC,QAAA,OAAO,mBAAmB,CAAC,OAAO,KAAK,QAAQ;AAC/C,QAAA,mBAAmB,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC;AACtC,QAAA,OAAO,mBAAmB,CAAC,QAAQ,KAAK,QAAQ;AAChD,QAAA,mBAAmB,CAAC,QAAQ,CAAC,MAAM,GAAG,CAAC,EACvC;AACJ;;ACpMA;;;;;;;;;;;;;;;AAeG;AAQH;AACO,IAAM,aAAa,GAAG,6BAA6B,CAAC;AAC3D,IAAM,gBAAgB,GAAG,CAAC,CAAC;AAC3B,IAAM,iBAAiB,GAAG,0BAA0B,CAAC;AASrD,IAAI,SAAS,GAA8C,IAAI,CAAC;AAChE,SAAS,YAAY,GAAA;IACnB,IAAI,CAAC,SAAS,EAAE;AACd,QAAA,SAAS,GAAG,MAAM,CAAC,aAAa,EAAE,gBAAgB,EAAE;AAClD,YAAA,OAAO,EAAE,UAAC,SAAS,EAAE,UAAU,EAAA;;;;;AAK7B,gBAAA,QAAQ,UAAU;AAChB,oBAAA,KAAK,CAAC;AACJ,wBAAA,SAAS,CAAC,iBAAiB,CAAC,iBAAiB,CAAC,CAAC;AAClD,iBAAA;aACF;AACF,SAAA,CAAC,CAAC;AACJ,KAAA;AACD,IAAA,OAAO,SAAS,CAAC;AACnB,CAAC;AAED;AACM,SAAgB,KAAK,CACzB,oBAAkD,EAAA;;;;;;AAE5C,oBAAA,GAAG,GAAG,MAAM,CAAC,oBAAoB,CAAC,CAAC;oBAC9B,OAAM,CAAA,CAAA,YAAA,YAAY,EAAE,CAAA,CAAA;;AAAzB,oBAAA,EAAE,GAAG,EAAoB,CAAA,IAAA,EAAA,CAAA;AACT,oBAAA,OAAA,CAAA,CAAA,YAAM,EAAE;6BAC3B,WAAW,CAAC,iBAAiB,CAAC;6BAC9B,WAAW,CAAC,iBAAiB,CAAC;6BAC9B,GAAG,CAAC,GAAG,CAAC,CAAA,CAAA;;oBAHL,YAAY,IAAI,EAAA,CAAA,IAAA,EAGX,CAAiB,CAAA;AAExB,oBAAA,IAAA,CAAA,YAAY,EAAZ,OAAY,CAAA,CAAA,YAAA,CAAA,CAAA,CAAA;AACd,oBAAA,OAAA,CAAA,CAAA,aAAO,YAAY,CAAC,CAAA;wBAGI,OAAM,CAAA,CAAA,YAAA,kBAAkB,CAC9C,oBAAoB,CAAC,SAAS,CAAC,QAAQ,CACxC,CAAA,CAAA;;AAFK,oBAAA,eAAe,GAAG,EAEvB,CAAA,IAAA,EAAA,CAAA;AACG,oBAAA,IAAA,CAAA,eAAe,EAAf,OAAe,CAAA,CAAA,YAAA,CAAA,CAAA,CAAA;AACjB,oBAAA,OAAA,CAAA,CAAA,YAAM,KAAK,CAAC,oBAAoB,EAAE,eAAe,CAAC,CAAA,CAAA;;AAAlD,oBAAA,EAAA,CAAA,IAAA,EAAkD,CAAC;AACnD,oBAAA,OAAA,CAAA,CAAA,aAAO,eAAe,CAAC,CAAA;;;;;AAG5B,CAAA;AAED;AACsB,SAAA,KAAK,CACzB,oBAAkD,EAClD,YAA0B,EAAA;;;;;;AAEpB,oBAAA,GAAG,GAAG,MAAM,CAAC,oBAAoB,CAAC,CAAC;oBAC9B,OAAM,CAAA,CAAA,YAAA,YAAY,EAAE,CAAA,CAAA;;AAAzB,oBAAA,EAAE,GAAG,EAAoB,CAAA,IAAA,EAAA,CAAA;oBACzB,EAAE,GAAG,EAAE,CAAC,WAAW,CAAC,iBAAiB,EAAE,WAAW,CAAC,CAAC;AAC1D,oBAAA,OAAA,CAAA,CAAA,YAAM,EAAE,CAAC,WAAW,CAAC,iBAAiB,CAAC,CAAC,GAAG,CAAC,YAAY,EAAE,GAAG,CAAC,CAAA,CAAA;;AAA9D,oBAAA,EAAA,CAAA,IAAA,EAA8D,CAAC;oBAC/D,OAAM,CAAA,CAAA,YAAA,EAAE,CAAC,IAAI,CAAA,CAAA;;AAAb,oBAAA,EAAA,CAAA,IAAA,EAAa,CAAC;AACd,oBAAA,OAAA,CAAA,CAAA,aAAO,YAAY,CAAC,CAAA;;;;AACrB,CAAA;AAED;AACM,SAAgB,QAAQ,CAC5B,oBAAkD,EAAA;;;;;;AAE5C,oBAAA,GAAG,GAAG,MAAM,CAAC,oBAAoB,CAAC,CAAC;oBAC9B,OAAM,CAAA,CAAA,YAAA,YAAY,EAAE,CAAA,CAAA;;AAAzB,oBAAA,EAAE,GAAG,EAAoB,CAAA,IAAA,EAAA,CAAA;oBACzB,EAAE,GAAG,EAAE,CAAC,WAAW,CAAC,iBAAiB,EAAE,WAAW,CAAC,CAAC;oBAC1D,OAAM,CAAA,CAAA,YAAA,EAAE,CAAC,WAAW,CAAC,iBAAiB,CAAC,CAAC,MAAM,CAAC,GAAG,CAAC,CAAA,CAAA;;AAAnD,oBAAA,EAAA,CAAA,IAAA,EAAmD,CAAC;oBACpD,OAAM,CAAA,CAAA,YAAA,EAAE,CAAC,IAAI,CAAA,CAAA;;AAAb,oBAAA,EAAA,CAAA,IAAA,EAAa,CAAC;;;;;AACf,CAAA;AAWD,SAAS,MAAM,CAAC,EAA2C,EAAA;AAAzC,IAAA,IAAA,SAAS,GAAA,EAAA,CAAA,SAAA,CAAA;IACzB,OAAO,SAAS,CAAC,KAAK,CAAC;AACzB;;AClHA;;;;;;;;;;;;;;;AAeG;;AAyBI,IAAM,SAAS,IAAA,EAAA,GAAA,EAAA;AACpB,IAAA,EAAA,CAAA,2BAAA,2CAAA,GACE,iDAAiD;AACnD,IAAA,EAAA,CAAA,0BAAA,qCAAA,GACE,+CAA+C;AACjD,IAAA,EAAA,CAAA,sBAAA,iCAAA,GACE,uDAAuD;AACzD,IAAA,EAAA,CAAA,oBAAA,oCAAA,GACE,oEAAoE;AACtE,IAAA,EAAA,CAAA,oBAAA,oCAAA,GACE,kEAAkE;AACpE,IAAA,EAAA,CAAA,qBAAA,qCAAA,GACE,0EAA0E;AAC5E,IAAA,EAAA,CAAA,wBAAA,wCAAA,GACE,kGAAkG;AACpG,IAAA,EAAA,CAAA,oCAAA,6CAAA,GACE,8EAA8E;AAChF,IAAA,EAAA,CAAA,wBAAA,wCAAA,GACE,oEAAoE;AACtE,IAAA,EAAA,CAAA,0BAAA,0CAAA,GACE,0DAA0D;AAC5D,IAAA,EAAA,CAAA,0BAAA,0CAAA,GACE,6CAA6C;QAC7C,6BAA6B;AAC/B,IAAA,EAAA,CAAA,qBAAA,qCAAA,GACE,mEAAmE;AACrE,IAAA,EAAA,CAAA,uBAAA,uCAAA,GACE,uDAAuD;AACzD,IAAA,EAAA,CAAA,wBAAA,wCAAA,GACE,oEAAoE;QACpE,yEAAyE;AAC3E,IAAA,EAAA,CAAA,yBAAA,yCAAA,GACE,sEAAsE;AACxE,IAAA,EAAA,CAAA,oBAAA,oCAAA,GACE,gEAAgE;AAClE,IAAA,EAAA,CAAA,mBAAA,mCAAA,GAA+B,wCAAwC;AACvE,IAAA,EAAA,CAAA,+BAAA,+CAAA,GACE,qEAAqE;QACrE,oEAAoE;OACvE,CAAC;AAYK,IAAM,aAAa,GAAG,IAAI,YAAY,CAC3C,WAAW,EACX,WAAW,EACX,SAAS,CACV;;AC/FD;;;;;;;;;;;;;;;AAeG;AAuBmB,SAAA,eAAe,CACnC,oBAAkD,EAClD,mBAAwC,EAAA;;;;;AAExB,gBAAA,KAAA,CAAA,EAAA,OAAA,CAAA,CAAA,YAAM,UAAU,CAAC,oBAAoB,CAAC,CAAA,CAAA;;AAAhD,oBAAA,OAAO,GAAG,EAAsC,CAAA,IAAA,EAAA,CAAA;AAChD,oBAAA,IAAI,GAAG,OAAO,CAAC,mBAAmB,CAAC,CAAC;AAEpC,oBAAA,gBAAgB,GAAG;AACvB,wBAAA,MAAM,EAAE,MAAM;AACd,wBAAA,OAAO,EAAA,OAAA;AACP,wBAAA,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC;qBAC3B,CAAC;;;;oBAIiB,OAAM,CAAA,CAAA,YAAA,KAAK,CAC1B,WAAW,CAAC,oBAAoB,CAAC,SAAS,CAAC,EAC3C,gBAAgB,CACjB,CAAA,CAAA;;AAHK,oBAAA,QAAQ,GAAG,EAGhB,CAAA,IAAA,EAAA,CAAA;AACc,oBAAA,OAAA,CAAA,CAAA,YAAM,QAAQ,CAAC,IAAI,EAAE,CAAA,CAAA;;oBAApC,YAAY,GAAG,SAAqB,CAAC;;;;oBAErC,MAAM,aAAa,CAAC,MAAM,CAAmC,wBAAA,yCAAA;wBAC3D,SAAS,EAAG,KAAa,KAAb,IAAA,IAAA,KAAG,uBAAH,KAAG,CAAY,QAAQ,EAAE;AACtC,qBAAA,CAAC,CAAC;;oBAGL,IAAI,YAAY,CAAC,KAAK,EAAE;AAChB,wBAAA,OAAO,GAAG,YAAY,CAAC,KAAK,CAAC,OAAO,CAAC;wBAC3C,MAAM,aAAa,CAAC,MAAM,CAAmC,wBAAA,yCAAA;AAC3D,4BAAA,SAAS,EAAE,OAAO;AACnB,yBAAA,CAAC,CAAC;AACJ,qBAAA;AAED,oBAAA,IAAI,CAAC,YAAY,CAAC,KAAK,EAAE;AACvB,wBAAA,MAAM,aAAa,CAAC,MAAM,CAAA,0BAAA,0CAAoC,CAAC;AAChE,qBAAA;oBAED,OAAO,CAAA,CAAA,aAAA,YAAY,CAAC,KAAK,CAAC,CAAA;;;;AAC3B,CAAA;AAEqB,SAAA,kBAAkB,CACtC,oBAAkD,EAClD,YAA0B,EAAA;;;;;AAEV,gBAAA,KAAA,CAAA,EAAA,OAAA,CAAA,CAAA,YAAM,UAAU,CAAC,oBAAoB,CAAC,CAAA,CAAA;;AAAhD,oBAAA,OAAO,GAAG,EAAsC,CAAA,IAAA,EAAA,CAAA;AAChD,oBAAA,IAAI,GAAG,OAAO,CAAC,YAAY,CAAC,mBAAoB,CAAC,CAAC;AAElD,oBAAA,aAAa,GAAG;AACpB,wBAAA,MAAM,EAAE,OAAO;AACf,wBAAA,OAAO,EAAA,OAAA;AACP,wBAAA,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC;qBAC3B,CAAC;;;;AAIiB,oBAAA,OAAA,CAAA,CAAA,YAAM,KAAK,CAC1B,EAAA,CAAA,MAAA,CAAG,WAAW,CAAC,oBAAoB,CAAC,SAAS,CAAC,EAAA,GAAA,CAAA,CAAA,MAAA,CAAI,YAAY,CAAC,KAAK,CAAE,EACtE,aAAa,CACd,CAAA,CAAA;;AAHK,oBAAA,QAAQ,GAAG,EAGhB,CAAA,IAAA,EAAA,CAAA;AACc,oBAAA,OAAA,CAAA,CAAA,YAAM,QAAQ,CAAC,IAAI,EAAE,CAAA,CAAA;;oBAApC,YAAY,GAAG,SAAqB,CAAC;;;;oBAErC,MAAM,aAAa,CAAC,MAAM,CAAgC,qBAAA,sCAAA;wBACxD,SAAS,EAAG,KAAa,KAAb,IAAA,IAAA,KAAG,uBAAH,KAAG,CAAY,QAAQ,EAAE;AACtC,qBAAA,CAAC,CAAC;;oBAGL,IAAI,YAAY,CAAC,KAAK,EAAE;AAChB,wBAAA,OAAO,GAAG,YAAY,CAAC,KAAK,CAAC,OAAO,CAAC;wBAC3C,MAAM,aAAa,CAAC,MAAM,CAAgC,qBAAA,sCAAA;AACxD,4BAAA,SAAS,EAAE,OAAO;AACnB,yBAAA,CAAC,CAAC;AACJ,qBAAA;AAED,oBAAA,IAAI,CAAC,YAAY,CAAC,KAAK,EAAE;AACvB,wBAAA,MAAM,aAAa,CAAC,MAAM,CAAA,uBAAA,uCAAiC,CAAC;AAC7D,qBAAA;oBAED,OAAO,CAAA,CAAA,aAAA,YAAY,CAAC,KAAK,CAAC,CAAA;;;;AAC3B,CAAA;AAEqB,SAAA,kBAAkB,CACtC,oBAAkD,EAClD,KAAa,EAAA;;;;;AAEG,gBAAA,KAAA,CAAA,EAAA,OAAA,CAAA,CAAA,YAAM,UAAU,CAAC,oBAAoB,CAAC,CAAA,CAAA;;AAAhD,oBAAA,OAAO,GAAG,EAAsC,CAAA,IAAA,EAAA,CAAA;AAEhD,oBAAA,kBAAkB,GAAG;AACzB,wBAAA,MAAM,EAAE,QAAQ;AAChB,wBAAA,OAAO,EAAA,OAAA;qBACR,CAAC;;;;AAGiB,oBAAA,OAAA,CAAA,CAAA,YAAM,KAAK,CAC1B,EAAG,CAAA,MAAA,CAAA,WAAW,CAAC,oBAAoB,CAAC,SAAS,CAAC,cAAI,KAAK,CAAE,EACzD,kBAAkB,CACnB,CAAA,CAAA;;AAHK,oBAAA,QAAQ,GAAG,EAGhB,CAAA,IAAA,EAAA,CAAA;AACiC,oBAAA,OAAA,CAAA,CAAA,YAAM,QAAQ,CAAC,IAAI,EAAE,CAAA,CAAA;;AAAjD,oBAAA,YAAY,GAAgB,EAAqB,CAAA,IAAA,EAAA,CAAA;oBACvD,IAAI,YAAY,CAAC,KAAK,EAAE;AAChB,wBAAA,OAAO,GAAG,YAAY,CAAC,KAAK,CAAC,OAAO,CAAC;wBAC3C,MAAM,aAAa,CAAC,MAAM,CAAqC,0BAAA,2CAAA;AAC7D,4BAAA,SAAS,EAAE,OAAO;AACnB,yBAAA,CAAC,CAAC;AACJ,qBAAA;;;;oBAED,MAAM,aAAa,CAAC,MAAM,CAAqC,0BAAA,2CAAA;wBAC7D,SAAS,EAAG,KAAa,KAAb,IAAA,IAAA,KAAG,uBAAH,KAAG,CAAY,QAAQ,EAAE;AACtC,qBAAA,CAAC,CAAC;;;;;AAEN,CAAA;AAED,SAAS,WAAW,CAAC,EAAwB,EAAA;AAAtB,IAAA,IAAA,SAAS,GAAA,EAAA,CAAA,SAAA,CAAA;AAC9B,IAAA,OAAO,EAAG,CAAA,MAAA,CAAA,QAAQ,EAAa,YAAA,CAAA,CAAA,MAAA,CAAA,SAAU,mBAAgB,CAAC;AAC5D,CAAC;AAED,SAAe,UAAU,CAAC,EAGK,EAAA;QAF7B,SAAS,GAAA,EAAA,CAAA,SAAA,EACT,aAAa,GAAA,EAAA,CAAA,aAAA,CAAA;;;;;AAEK,gBAAA,KAAA,CAAA,EAAA,OAAA,CAAA,CAAA,YAAM,aAAa,CAAC,QAAQ,EAAE,CAAA,CAAA;;AAA1C,oBAAA,SAAS,GAAG,EAA8B,CAAA,IAAA,EAAA,CAAA;oBAEhD,OAAO,CAAA,CAAA,aAAA,IAAI,OAAO,CAAC;AACjB,4BAAA,cAAc,EAAE,kBAAkB;AAClC,4BAAA,MAAM,EAAE,kBAAkB;4BAC1B,gBAAgB,EAAE,SAAS,CAAC,MAAO;4BACnC,oCAAoC,EAAE,MAAO,CAAA,MAAA,CAAA,SAAS,CAAE;AACzD,yBAAA,CAAC,CAAC,CAAA;;;;AACJ,CAAA;AAED,SAAS,OAAO,CAAC,EAKK,EAAA;QAJpB,MAAM,GAAA,EAAA,CAAA,MAAA,EACN,IAAI,GAAA,EAAA,CAAA,IAAA,EACJ,QAAQ,GAAA,EAAA,CAAA,QAAA,EACR,QAAQ,GAAA,EAAA,CAAA,QAAA,CAAA;AAER,IAAA,IAAM,IAAI,GAAmB;AAC3B,QAAA,GAAG,EAAE;AACH,YAAA,QAAQ,EAAA,QAAA;AACR,YAAA,IAAI,EAAA,IAAA;AACJ,YAAA,MAAM,EAAA,MAAA;AACP,SAAA;KACF,CAAC;IAEF,IAAI,QAAQ,KAAK,iBAAiB,EAAE;AAClC,QAAA,IAAI,CAAC,GAAG,CAAC,iBAAiB,GAAG,QAAQ,CAAC;AACvC,KAAA;AAED,IAAA,OAAO,IAAI,CAAC;AACd;;ACzLA;;;;;;;;;;;;;;;AAeG;AAiBH;AACA,IAAM,mBAAmB,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC;AAE9C,SAAgB,gBAAgB,CACpC,SAA2B,EAAA;;;;;wBAEF,OAAM,CAAA,CAAA,YAAA,mBAAmB,CAChD,SAAS,CAAC,cAAe,EACzB,SAAS,CAAC,QAAS,CACpB,CAAA,CAAA;;AAHK,oBAAA,gBAAgB,GAAG,EAGxB,CAAA,IAAA,EAAA,CAAA;AAEK,oBAAA,mBAAmB,GAAwB;wBAC/C,QAAQ,EAAE,SAAS,CAAC,QAAS;AAC7B,wBAAA,OAAO,EAAE,SAAS,CAAC,cAAe,CAAC,KAAK;wBACxC,QAAQ,EAAE,gBAAgB,CAAC,QAAQ;wBACnC,IAAI,EAAE,aAAa,CAAC,gBAAgB,CAAC,MAAM,CAAC,MAAM,CAAE,CAAC;wBACrD,MAAM,EAAE,aAAa,CAAC,gBAAgB,CAAC,MAAM,CAAC,QAAQ,CAAE,CAAC;qBAC1D,CAAC;AAEmB,oBAAA,OAAA,CAAA,CAAA,YAAM,KAAK,CAAC,SAAS,CAAC,oBAAoB,CAAC,CAAA,CAAA;;AAA1D,oBAAA,YAAY,GAAG,EAA2C,CAAA,IAAA,EAAA,CAAA;yBAC5D,CAAC,YAAY,EAAb,OAAa,CAAA,CAAA,YAAA,CAAA,CAAA,CAAA;;oBAEf,OAAO,CAAA,CAAA,aAAA,WAAW,CAAC,SAAS,CAAC,oBAAoB,EAAE,mBAAmB,CAAC,CAAC,CAAA;;yBAExE,CAAC,YAAY,CAAC,YAAY,CAAC,mBAAoB,EAAE,mBAAmB,CAAC,EAArE,OAAqE,CAAA,CAAA,YAAA,CAAA,CAAA,CAAA;;;;oBAInE,OAAM,CAAA,CAAA,YAAA,kBAAkB,CACtB,SAAS,CAAC,oBAAqB,EAC/B,YAAY,CAAC,KAAK,CACnB,CAAA,CAAA;;AAHD,oBAAA,EAAA,CAAA,IAAA,EAGC,CAAC;;;;;AAGF,oBAAA,OAAO,CAAC,IAAI,CAAC,GAAC,CAAC,CAAC;;wBAGlB,OAAO,CAAA,CAAA,aAAA,WAAW,CAAC,SAAS,CAAC,oBAAqB,EAAE,mBAAmB,CAAC,CAAC,CAAA;;oBACpE,IAAI,IAAI,CAAC,GAAG,EAAE,IAAI,YAAY,CAAC,UAAU,GAAG,mBAAmB,EAAE;;wBAEtE,OAAO,CAAA,CAAA,aAAA,WAAW,CAAC,SAAS,EAAE;gCAC5B,KAAK,EAAE,YAAY,CAAC,KAAK;AACzB,gCAAA,UAAU,EAAE,IAAI,CAAC,GAAG,EAAE;AACtB,gCAAA,mBAAmB,EAAA,mBAAA;AACpB,6BAAA,CAAC,CAAC,CAAA;AACJ,qBAAA;AAAM,yBAAA;;wBAEL,OAAO,CAAA,CAAA,aAAA,YAAY,CAAC,KAAK,CAAC,CAAA;AAC3B,qBAAA;;;;;AACF,CAAA;AAED;;;AAGG;AACG,SAAgB,mBAAmB,CACvC,SAA2B,EAAA;;;;;AAEN,gBAAA,KAAA,CAAA,EAAA,OAAA,CAAA,CAAA,YAAM,KAAK,CAAC,SAAS,CAAC,oBAAoB,CAAC,CAAA,CAAA;;AAA1D,oBAAA,YAAY,GAAG,EAA2C,CAAA,IAAA,EAAA,CAAA;AAC5D,oBAAA,IAAA,CAAA,YAAY,EAAZ,OAAY,CAAA,CAAA,YAAA,CAAA,CAAA,CAAA;oBACd,OAAM,CAAA,CAAA,YAAA,kBAAkB,CACtB,SAAS,CAAC,oBAAoB,EAC9B,YAAY,CAAC,KAAK,CACnB,CAAA,CAAA;;AAHD,oBAAA,EAAA,CAAA,IAAA,EAGC,CAAC;AACF,oBAAA,OAAA,CAAA,CAAA,YAAM,QAAQ,CAAC,SAAS,CAAC,oBAAoB,CAAC,CAAA,CAAA;;AAA9C,oBAAA,EAAA,CAAA,IAAA,EAA8C,CAAC;;wBAK/C,OAAM,CAAA,CAAA,YAAA,SAAS,CAAC,cAAe,CAAC,WAAW,CAAC,eAAe,EAAE,CAAA,CAAA;;AADzD,oBAAA,gBAAgB,GACpB,EAA6D,CAAA,IAAA,EAAA,CAAA;AAC/D,oBAAA,IAAI,gBAAgB,EAAE;AACpB,wBAAA,OAAA,CAAA,CAAA,aAAO,gBAAgB,CAAC,WAAW,EAAE,CAAC,CAAA;AACvC,qBAAA;;AAGD,oBAAA,OAAA,CAAA,CAAA,aAAO,IAAI,CAAC,CAAA;;;;AACb,CAAA;AAED,SAAe,WAAW,CACxB,SAA2B,EAC3B,YAA0B,EAAA;;;;;;;oBAGH,OAAM,CAAA,CAAA,YAAA,kBAAkB,CAC3C,SAAS,CAAC,oBAAoB,EAC9B,YAAY,CACb,CAAA,CAAA;;AAHK,oBAAA,YAAY,GAAG,EAGpB,CAAA,IAAA,EAAA,CAAA;AAEK,oBAAA,mBAAmB,GACpB,QAAA,CAAA,QAAA,CAAA,EAAA,EAAA,YAAY,CACf,EAAA,EAAA,KAAK,EAAE,YAAY,EACnB,UAAU,EAAE,IAAI,CAAC,GAAG,EAAE,GACvB,CAAC;oBAEF,OAAM,CAAA,CAAA,YAAA,KAAK,CAAC,SAAS,CAAC,oBAAoB,EAAE,mBAAmB,CAAC,CAAA,CAAA;;AAAhE,oBAAA,EAAA,CAAA,IAAA,EAAgE,CAAC;AACjE,oBAAA,OAAA,CAAA,CAAA,aAAO,YAAY,CAAC,CAAA;;;AAEpB,oBAAA,MAAM,GAAC,CAAC;;;;;AAEX,CAAA;AAED,SAAe,WAAW,CACxB,oBAAkD,EAClD,mBAAwC,EAAA;;;;;AAE1B,gBAAA,KAAA,CAAA,EAAA,OAAA,CAAA,CAAA,YAAM,eAAe,CACjC,oBAAoB,EACpB,mBAAmB,CACpB,CAAA,CAAA;;AAHK,oBAAA,KAAK,GAAG,EAGb,CAAA,IAAA,EAAA,CAAA;AACK,oBAAA,YAAY,GAAiB;AACjC,wBAAA,KAAK,EAAA,KAAA;AACL,wBAAA,UAAU,EAAE,IAAI,CAAC,GAAG,EAAE;AACtB,wBAAA,mBAAmB,EAAA,mBAAA;qBACpB,CAAC;AACF,oBAAA,OAAA,CAAA,CAAA,YAAM,KAAK,CAAC,oBAAoB,EAAE,YAAY,CAAC,CAAA,CAAA;;AAA/C,oBAAA,EAAA,CAAA,IAAA,EAA+C,CAAC;oBAChD,OAAO,CAAA,CAAA,aAAA,YAAY,CAAC,KAAK,CAAC,CAAA;;;;AAC3B,CAAA;AAED;;AAEG;AACH,SAAe,mBAAmB,CAChC,cAAyC,EACzC,QAAgB,EAAA;;;;;AAEK,gBAAA,KAAA,CAAA,EAAA,OAAA,CAAA,CAAA,YAAM,cAAc,CAAC,WAAW,CAAC,eAAe,EAAE,CAAA,CAAA;;AAAjE,oBAAA,YAAY,GAAG,EAAkD,CAAA,IAAA,EAAA,CAAA;AACvE,oBAAA,IAAI,YAAY,EAAE;AAChB,wBAAA,OAAA,CAAA,CAAA,aAAO,YAAY,CAAC,CAAA;AACrB,qBAAA;AAED,oBAAA,OAAA,CAAA,CAAA,aAAO,cAAc,CAAC,WAAW,CAAC,SAAS,CAAC;AAC1C,4BAAA,eAAe,EAAE,IAAI;;;AAGrB,4BAAA,oBAAoB,EAAE,aAAa,CAAC,QAAQ,CAAC;AAC9C,yBAAA,CAAC,CAAC,CAAA;;;;AACJ,CAAA;AAED;;AAEG;AACH,SAAS,YAAY,CACnB,SAA8B,EAC9B,cAAmC,EAAA;IAEnC,IAAM,eAAe,GAAG,cAAc,CAAC,QAAQ,KAAK,SAAS,CAAC,QAAQ,CAAC;IACvE,IAAM,eAAe,GAAG,cAAc,CAAC,QAAQ,KAAK,SAAS,CAAC,QAAQ,CAAC;IACvE,IAAM,WAAW,GAAG,cAAc,CAAC,IAAI,KAAK,SAAS,CAAC,IAAI,CAAC;IAC3D,IAAM,aAAa,GAAG,cAAc,CAAC,MAAM,KAAK,SAAS,CAAC,MAAM,CAAC;AAEjE,IAAA,OAAO,eAAe,IAAI,eAAe,IAAI,WAAW,IAAI,aAAa,CAAC;AAC5E;;ACvLA;;;;;;;;;;;;;;;AAeG;AAKG,SAAU,kBAAkB,CAChC,eAAuC,EAAA;AAEvC,IAAA,IAAM,OAAO,GAAmB;QAC9B,IAAI,EAAE,eAAe,CAAC,IAAI;;QAE1B,WAAW,EAAE,eAAe,CAAC,YAAY;;QAEzC,SAAS,EAAE,eAAe,CAAC,YAAY;KACtB,CAAC;AAEpB,IAAA,4BAA4B,CAAC,OAAO,EAAE,eAAe,CAAC,CAAC;AACvD,IAAA,oBAAoB,CAAC,OAAO,EAAE,eAAe,CAAC,CAAC;AAC/C,IAAA,mBAAmB,CAAC,OAAO,EAAE,eAAe,CAAC,CAAC;AAE9C,IAAA,OAAO,OAAO,CAAC;AACjB,CAAC;AAED,SAAS,4BAA4B,CACnC,OAAuB,EACvB,sBAA8C,EAAA;AAE9C,IAAA,IAAI,CAAC,sBAAsB,CAAC,YAAY,EAAE;QACxC,OAAO;AACR,KAAA;AAED,IAAA,OAAO,CAAC,YAAY,GAAG,EAAE,CAAC;AAE1B,IAAA,IAAM,KAAK,GAAG,sBAAsB,CAAC,YAAa,CAAC,KAAK,CAAC;IACzD,IAAI,CAAC,CAAC,KAAK,EAAE;AACX,QAAA,OAAO,CAAC,YAAa,CAAC,KAAK,GAAG,KAAK,CAAC;AACrC,KAAA;AAED,IAAA,IAAM,IAAI,GAAG,sBAAsB,CAAC,YAAa,CAAC,IAAI,CAAC;IACvD,IAAI,CAAC,CAAC,IAAI,EAAE;AACV,QAAA,OAAO,CAAC,YAAa,CAAC,IAAI,GAAG,IAAI,CAAC;AACnC,KAAA;AAED,IAAA,IAAM,KAAK,GAAG,sBAAsB,CAAC,YAAa,CAAC,KAAK,CAAC;IACzD,IAAI,CAAC,CAAC,KAAK,EAAE;AACX,QAAA,OAAO,CAAC,YAAa,CAAC,KAAK,GAAG,KAAK,CAAC;AACrC,KAAA;AAED,IAAA,IAAM,IAAI,GAAG,sBAAsB,CAAC,YAAa,CAAC,IAAI,CAAC;IACvD,IAAI,CAAC,CAAC,IAAI,EAAE;AACV,QAAA,OAAO,CAAC,YAAa,CAAC,IAAI,GAAG,IAAI,CAAC;AACnC,KAAA;AACH,CAAC;AAED,SAAS,oBAAoB,CAC3B,OAAuB,EACvB,sBAA8C,EAAA;AAE9C,IAAA,IAAI,CAAC,sBAAsB,CAAC,IAAI,EAAE;QAChC,OAAO;AACR,KAAA;AAED,IAAA,OAAO,CAAC,IAAI,GAAG,sBAAsB,CAAC,IAAiC,CAAC;AAC1E,CAAC;AAED,SAAS,mBAAmB,CAC1B,OAAuB,EACvB,sBAA8C,EAAA;;;IAG9C,IACE,CAAC,sBAAsB,CAAC,UAAU;QAClC,EAAC,MAAA,sBAAsB,CAAC,YAAY,MAAE,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,YAAY,CAAA,EAClD;QACA,OAAO;AACR,KAAA;AAED,IAAA,OAAO,CAAC,UAAU,GAAG,EAAE,CAAC;AAExB,IAAA,IAAM,IAAI,GACR,CAAA,EAAA,GAAA,CAAA,EAAA,GAAA,sBAAsB,CAAC,UAAU,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAE,IAAI,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,EAAA,GACvC,MAAA,sBAAsB,CAAC,YAAY,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAE,YAAY,CAAC;IAEpD,IAAI,CAAC,CAAC,IAAI,EAAE;AACV,QAAA,OAAO,CAAC,UAAW,CAAC,IAAI,GAAG,IAAI,CAAC;AACjC,KAAA;;IAGD,IAAM,cAAc,GAAG,CAAA,EAAA,GAAA,sBAAsB,CAAC,UAAU,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAE,eAAe,CAAC;IAC1E,IAAI,CAAC,CAAC,cAAc,EAAE;AACpB,QAAA,OAAO,CAAC,UAAW,CAAC,cAAc,GAAG,cAAc,CAAC;AACrD,KAAA;AACH;;AC3GA;;;;;;;;;;;;;;;AAeG;AAKG,SAAU,gBAAgB,CAAC,IAAa,EAAA;;AAE5C,IAAA,OAAO,OAAO,IAAI,KAAK,QAAQ,IAAI,CAAC,CAAC,IAAI,IAAI,mBAAmB,IAAI,IAAI,CAAC;AAC3E;;ACvBA;;;;;;;;;;;;;;;AAeG;AAyBuB,aAAa,CACrC,sBAAsB,EACtB,qBAAqB,EACrB;AA+Mc,SAAA,aAAa,CAAC,EAAU,EAAE,EAAU,EAAA;IAClD,IAAM,WAAW,GAAG,EAAE,CAAC;AACvB,IAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;QAClC,WAAW,CAAC,IAAI,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;AAC/B,QAAA,IAAI,CAAC,GAAG,EAAE,CAAC,MAAM,EAAE;YACjB,WAAW,CAAC,IAAI,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;AAChC,SAAA;AACF,KAAA;AAED,IAAA,OAAO,WAAW,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;AAC9B;;ACpQA;;;;;;;;;;;;;;;AAeG;AAQG,SAAU,gBAAgB,CAAC,GAAgB,EAAA;;AAC/C,IAAA,IAAI,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,OAAO,EAAE;AACxB,QAAA,MAAM,oBAAoB,CAAC,0BAA0B,CAAC,CAAC;AACxD,KAAA;AAED,IAAA,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE;AACb,QAAA,MAAM,oBAAoB,CAAC,UAAU,CAAC,CAAC;AACxC,KAAA;;AAGD,IAAA,IAAM,UAAU,GAAyC;QACvD,WAAW;QACX,QAAQ;QACR,OAAO;QACP,mBAAmB;KACpB,CAAC;AAEM,IAAA,IAAA,OAAO,GAAK,GAAG,CAAA,OAAR,CAAS;;AACxB,QAAA,KAAsB,IAAA,YAAA,GAAA,QAAA,CAAA,UAAU,CAAA,sCAAA,EAAE,CAAA,cAAA,CAAA,IAAA,EAAA,cAAA,GAAA,YAAA,CAAA,IAAA,EAAA,EAAA;AAA7B,YAAA,IAAM,OAAO,GAAA,cAAA,CAAA,KAAA,CAAA;AAChB,YAAA,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE;AACrB,gBAAA,MAAM,oBAAoB,CAAC,OAAO,CAAC,CAAC;AACrC,aAAA;AACF,SAAA;;;;;;;;;IAED,OAAO;QACL,OAAO,EAAE,GAAG,CAAC,IAAI;QACjB,SAAS,EAAE,OAAO,CAAC,SAAU;QAC7B,MAAM,EAAE,OAAO,CAAC,MAAO;QACvB,KAAK,EAAE,OAAO,CAAC,KAAM;QACrB,QAAQ,EAAE,OAAO,CAAC,iBAAkB;KACrC,CAAC;AACJ,CAAC;AAED,SAAS,oBAAoB,CAAC,SAAiB,EAAA;IAC7C,OAAO,aAAa,CAAC,MAAM,CAAsC,2BAAA,4CAAA;AAC/D,QAAA,SAAS,EAAA,SAAA;AACV,KAAA,CAAC,CAAC;AACL;;AC5DA;;;;;;;;;;;;;;;AAeG;AAYH,IAAA,gBAAA,kBAAA,YAAA;AAoBE,IAAA,SAAA,gBAAA,CACE,GAAgB,EAChB,aAA6C,EAC7C,iBAA0D,EAAA;;QAhB5D,IAAwC,CAAA,wCAAA,GAAY,KAAK,CAAC;QAE1D,IAA0B,CAAA,0BAAA,GAGf,IAAI,CAAC;QAEhB,IAAgB,CAAA,gBAAA,GACd,IAAI,CAAC;QAEP,IAAS,CAAA,SAAA,GAAe,EAAE,CAAC;QAC3B,IAAmB,CAAA,mBAAA,GAAY,KAAK,CAAC;AAOnC,QAAA,IAAM,SAAS,GAAG,gBAAgB,CAAC,GAAG,CAAC,CAAC;QAExC,IAAI,CAAC,oBAAoB,GAAG;AAC1B,YAAA,GAAG,EAAA,GAAA;AACH,YAAA,SAAS,EAAA,SAAA;AACT,YAAA,aAAa,EAAA,aAAA;AACb,YAAA,iBAAiB,EAAA,iBAAA;SAClB,CAAC;KACH;AAED,IAAA,gBAAA,CAAA,SAAA,CAAA,OAAO,GAAP,YAAA;AACE,QAAA,OAAO,OAAO,CAAC,OAAO,EAAE,CAAC;KAC1B,CAAA;IACH,OAAC,gBAAA,CAAA;AAAD,CAAC,EAAA,CAAA;;ACjED;;;;;;;;;;;;;;;AAeG;AAOG,SAAgB,iBAAiB,CACrC,SAA2B,EAAA;;;;;;;AAGzB,oBAAA,EAAA,GAAA,SAAS,CAAA;AAAkB,oBAAA,OAAA,CAAA,CAAA,YAAM,SAAS,CAAC,aAAa,CAAC,QAAQ,CAC/D,eAAe,EACf;AACE,4BAAA,KAAK,EAAE,gBAAgB;AACxB,yBAAA,CACF,CAAA,CAAA;;oBALD,EAAU,CAAA,cAAc,GAAG,EAAA,CAAA,IAAA,EAK1B,CAAC;;;;;;AAOF,oBAAA,SAAS,CAAC,cAAc,CAAC,MAAM,EAAE,CAAC,KAAK,CAAC,YAAA;;AAExC,qBAAC,CAAC,CAAC;;;;oBAEH,MAAM,aAAa,CAAC,MAAM,CAAwC,oCAAA,8CAAA;AAChE,wBAAA,mBAAmB,EAAG,GAAW,KAAA,IAAA,IAAX,GAAC,KAAD,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,GAAC,CAAY,OAAO;AAC3C,qBAAA,CAAC,CAAC;;;;;AAEN;;AC9CD;;;;;;;;;;;;;;;AAeG;AAOmB,SAAA,WAAW,CAC/B,SAA2B,EAC3B,cAAsD,EAAA;;;;;0BAElD,CAAC,cAAc,IAAI,CAAC,SAAS,CAAC,cAAc,CAAA,EAA5C,OAA4C,CAAA,CAAA,YAAA,CAAA,CAAA,CAAA;AAC9C,oBAAA,OAAA,CAAA,CAAA,YAAM,iBAAiB,CAAC,SAAS,CAAC,CAAA,CAAA;;AAAlC,oBAAA,EAAA,CAAA,IAAA,EAAkC,CAAC;;;oBAGrC,IAAI,CAAC,cAAc,IAAI,CAAC,CAAC,SAAS,CAAC,cAAc,EAAE;wBACjD,OAAO,CAAA,CAAA,YAAA,CAAA;AACR,qBAAA;AAED,oBAAA,IAAI,EAAE,cAAc,YAAY,yBAAyB,CAAC,EAAE;AAC1D,wBAAA,MAAM,aAAa,CAAC,MAAM,CAAA,yBAAA,yCAAmC,CAAC;AAC/D,qBAAA;AAED,oBAAA,SAAS,CAAC,cAAc,GAAG,cAAc,CAAC;;;;;AAC3C;;ACvCD;;;;;;;;;;;;;;;AAeG;AAKmB,SAAA,cAAc,CAClC,SAA2B,EAC3B,QAA6B,EAAA;;;YAE7B,IAAI,CAAC,CAAC,QAAQ,EAAE;AACd,gBAAA,SAAS,CAAC,QAAQ,GAAG,QAAQ,CAAC;AAC/B,aAAA;AAAM,iBAAA,IAAI,CAAC,SAAS,CAAC,QAAQ,EAAE;AAC9B,gBAAA,SAAS,CAAC,QAAQ,GAAG,iBAAiB,CAAC;AACxC,aAAA;;;;AACF;;AC7BD;;;;;;;;;;;;;;;AAeG;AAUmB,SAAAC,UAAQ,CAC5B,SAA2B,EAC3B,OAAyB,EAAA;;;;;oBAEzB,IAAI,CAAC,SAAS,EAAE;AACd,wBAAA,MAAM,aAAa,CAAC,MAAM,CAAA,0BAAA,qCAA+B,CAAC;AAC3D,qBAAA;AAEG,oBAAA,IAAA,EAAA,YAAY,CAAC,UAAU,KAAK,SAAS,CAAA,EAArC,OAAqC,CAAA,CAAA,YAAA,CAAA,CAAA,CAAA;AACvC,oBAAA,OAAA,CAAA,CAAA,YAAM,YAAY,CAAC,iBAAiB,EAAE,CAAA,CAAA;;AAAtC,oBAAA,EAAA,CAAA,IAAA,EAAsC,CAAC;;;AAGzC,oBAAA,IAAI,YAAY,CAAC,UAAU,KAAK,SAAS,EAAE;AACzC,wBAAA,MAAM,aAAa,CAAC,MAAM,CAAA,oBAAA,oCAA8B,CAAC;AAC1D,qBAAA;AAED,oBAAA,OAAA,CAAA,CAAA,YAAM,cAAc,CAAC,SAAS,EAAE,OAAO,KAAA,IAAA,IAAP,OAAO,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAP,OAAO,CAAE,QAAQ,CAAC,CAAA,CAAA;;AAAlD,oBAAA,EAAA,CAAA,IAAA,EAAkD,CAAC;AACnD,oBAAA,OAAA,CAAA,CAAA,YAAM,WAAW,CAAC,SAAS,EAAE,OAAO,KAAA,IAAA,IAAP,OAAO,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAP,OAAO,CAAE,yBAAyB,CAAC,CAAA,CAAA;;AAAhE,oBAAA,EAAA,CAAA,IAAA,EAAgE,CAAC;AAEjE,oBAAA,OAAA,CAAA,CAAA,aAAO,gBAAgB,CAAC,SAAS,CAAC,CAAC,CAAA;;;;AACpC;;AC7CD;;;;;;;;;;;;;;;AAeG;SAcmB,UAAU,CAC9B,SAA2B,EAC3B,WAAwB,EACxB,IAAwB,EAAA;;;;;;AAElB,oBAAA,SAAS,GAAG,YAAY,CAAC,WAAW,CAAC,CAAC;oBAE1C,OAAM,CAAA,CAAA,YAAA,SAAS,CAAC,oBAAoB,CAAC,iBAAiB,CAAC,GAAG,EAAE,CAAA,CAAA;;AADxD,oBAAA,SAAS,GACb,EAA4D,CAAA,IAAA,EAAA,CAAA;AAC9D,oBAAA,SAAS,CAAC,QAAQ,CAAC,SAAS,EAAE;;AAE5B,wBAAA,UAAU,EAAE,IAAI,CAAC,mBAAmB,CAAC;AACrC,wBAAA,YAAY,EAAE,IAAI,CAAC,qBAAqB,CAAC;AACzC,wBAAA,YAAY,EAAE,IAAI,CAAC,qBAAqB,CAAC;wBACzC,mBAAmB,EAAE,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC;;AAEnD,qBAAA,CAAC,CAAC;;;;;AACJ,CAAA;AAED,SAAS,YAAY,CAAC,WAAwB,EAAA;AAC5C,IAAA,QAAQ,WAAW;QACjB,KAAK,WAAW,CAAC,oBAAoB;AACnC,YAAA,OAAO,mBAAmB,CAAC;QAC7B,KAAK,WAAW,CAAC,aAAa;AAC5B,YAAA,OAAO,yBAAyB,CAAC;AACnC,QAAA;YACE,MAAM,IAAI,KAAK,EAAE,CAAC;AACrB,KAAA;AACH;;ACxDA;;;;;;;;;;;;;;;AAeG;AAamB,SAAA,oBAAoB,CACxC,SAA2B,EAC3B,KAAmB,EAAA;;;;;;AAEb,oBAAA,eAAe,GAAG,KAAK,CAAC,IAA8B,CAAC;AAE7D,oBAAA,IAAI,CAAC,eAAe,CAAC,mBAAmB,EAAE;wBACxC,OAAO,CAAA,CAAA,YAAA,CAAA;AACR,qBAAA;oBAED,IACE,SAAS,CAAC,gBAAgB;AAC1B,wBAAA,eAAe,CAAC,WAAW,KAAK,WAAW,CAAC,aAAa,EACzD;AACA,wBAAA,IAAI,OAAO,SAAS,CAAC,gBAAgB,KAAK,UAAU,EAAE;4BACpD,SAAS,CAAC,gBAAgB,CAAC,kBAAkB,CAAC,eAAe,CAAC,CAAC,CAAC;AACjE,yBAAA;AAAM,6BAAA;4BACL,SAAS,CAAC,gBAAgB,CAAC,IAAI,CAAC,kBAAkB,CAAC,eAAe,CAAC,CAAC,CAAC;AACtE,yBAAA;AACF,qBAAA;AAGK,oBAAA,WAAW,GAAG,eAAe,CAAC,IAAI,CAAC;0BAEvC,gBAAgB,CAAC,WAAW,CAAC;AAC7B,wBAAA,WAAW,CAAC,kCAAkC,CAAC,KAAK,GAAG,CAAA,EADvD,OACuD,CAAA,CAAA,YAAA,CAAA,CAAA,CAAA;oBAEvD,OAAM,CAAA,CAAA,YAAA,UAAU,CAAC,SAAS,EAAE,eAAe,CAAC,WAAY,EAAE,WAAW,CAAC,CAAA,CAAA;;AAAtE,oBAAA,EAAA,CAAA,IAAA,EAAsE,CAAC;;;;;;AAE1E;;;;;ACzDD;;;;;;;;;;;;;;;AAeG;AAwBH,IAAM,sBAAsB,GAAiC,UAC3D,SAA6B,EAAA;AAE7B,IAAA,IAAM,SAAS,GAAG,IAAI,gBAAgB,CACpC,SAAS,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC,YAAY,EAAE,EAC3C,SAAS,CAAC,WAAW,CAAC,wBAAwB,CAAC,CAAC,YAAY,EAAE,EAC9D,SAAS,CAAC,WAAW,CAAC,oBAAoB,CAAC,CAC5C,CAAC;IAEF,SAAS,CAAC,aAAa,CAAC,gBAAgB,CAAC,SAAS,EAAE,UAAA,CAAC,EAAA;AACnD,QAAA,OAAA,oBAAoB,CAAC,SAA6B,EAAE,CAAC,CAAC,CAAA;AAAtD,KAAsD,CACvD,CAAC;AAEF,IAAA,OAAO,SAAS,CAAC;AACnB,CAAC,CAAC;AAEF,IAAM,8BAA8B,GAA0C,UAC5E,SAA6B,EAAA;IAE7B,IAAM,SAAS,GAAG,SAAS;SACxB,WAAW,CAAC,WAAW,CAAC;AACxB,SAAA,YAAY,EAAsB,CAAC;AAEtC,IAAA,IAAM,iBAAiB,GAAsB;AAC3C,QAAA,QAAQ,EAAE,UAAC,OAAyB,EAAA,EAAK,OAAAA,UAAQ,CAAC,SAAS,EAAE,OAAO,CAAC,CAAA,EAAA;KACtE,CAAC;AAEF,IAAA,OAAO,iBAAiB,CAAC;AAC3B,CAAC,CAAC;SAyBc,yBAAyB,GAAA;IACvC,kBAAkB,CAChB,IAAI,SAAS,CAAC,WAAW,EAAE,sBAAsB,EAAuB,QAAA,4BAAA,CACzE,CAAC;IAEF,kBAAkB,CAChB,IAAI,SAAS,CACX,oBAAoB,EACpB,8BAA8B,EAE/B,SAAA,6BAAA,CACF,CAAC;AAEF,IAAA,eAAe,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;;AAE/B,IAAA,eAAe,CAAC,IAAI,EAAE,OAAO,EAAE,MAAkB,CAAC,CAAC;AACrD;;AC5GA;;;;;;;;;;;;;;;AAeG;AAQH;;;;;AAKG;SACmB,iBAAiB,GAAA;;;;;;;;oBAInC,OAAM,CAAA,CAAA,YAAA,yBAAyB,EAAE,CAAA,CAAA;;;;AAAjC,oBAAA,EAAA,CAAA,IAAA,EAAiC,CAAC;;;;AAElC,oBAAA,OAAA,CAAA,CAAA,aAAO,KAAK,CAAC,CAAA;;;;;AAKf,gBAAA,OAAA,CAAA,CAAA,cACE,OAAO,MAAM,KAAK,WAAW;AAC7B,wBAAA,oBAAoB,EAAE;AACtB,wBAAA,iBAAiB,EAAE;AACnB,wBAAA,eAAe,IAAI,SAAS;AAC5B,wBAAA,aAAa,IAAI,MAAM;AACvB,wBAAA,cAAc,IAAI,MAAM;AACxB,wBAAA,OAAO,IAAI,MAAM;AACjB,wBAAA,yBAAyB,CAAC,SAAS,CAAC,cAAc,CAAC,kBAAkB,CAAC;wBACtE,gBAAgB,CAAC,SAAS,CAAC,cAAc,CAAC,QAAQ,CAAC,EACnD,CAAA;;;;AACH;;ACnDD;;;;;;;;;;;;;;;AAeG;AAQG,SAAgBC,aAAW,CAC/B,SAA2B,EAAA;;;;;oBAE3B,IAAI,CAAC,SAAS,EAAE;AACd,wBAAA,MAAM,aAAa,CAAC,MAAM,CAAA,0BAAA,qCAA+B,CAAC;AAC3D,qBAAA;AAEG,oBAAA,IAAA,CAAA,CAAC,SAAS,CAAC,cAAc,EAAzB,OAAyB,CAAA,CAAA,YAAA,CAAA,CAAA,CAAA;AAC3B,oBAAA,OAAA,CAAA,CAAA,YAAM,iBAAiB,CAAC,SAAS,CAAC,CAAA,CAAA;;AAAlC,oBAAA,EAAA,CAAA,IAAA,EAAkC,CAAC;;AAGrC,gBAAA,KAAA,CAAA,EAAA,OAAA,CAAA,CAAA,aAAO,mBAAmB,CAAC,SAAS,CAAC,CAAC,CAAA;;;;AACvC;;ACnCD;;;;;;;;;;;;;;;AAeG;AAYa,SAAAC,WAAS,CACvB,SAA2B,EAC3B,cAAiE,EAAA;IAEjE,IAAI,CAAC,SAAS,EAAE;AACd,QAAA,MAAM,aAAa,CAAC,MAAM,CAAA,0BAAA,qCAA+B,CAAC;AAC3D,KAAA;AAED,IAAA,SAAS,CAAC,gBAAgB,GAAG,cAAc,CAAC;IAE5C,OAAO,YAAA;AACL,QAAA,SAAS,CAAC,gBAAgB,GAAG,IAAI,CAAC;AACpC,KAAC,CAAC;AACJ;;ACxCA;;;;;;;;;;;;;;;AAeG;AAwBH;;;;;;AAMG;AACG,SAAU,oBAAoB,CAAC,GAA2B,EAAA;IAA3B,IAAA,GAAA,KAAA,KAAA,CAAA,EAAA,EAAA,GAAmB,GAAA,MAAM,EAAE,CAAA,EAAA;;;;;AAK9D,IAAA,iBAAiB,EAAE,CAAC,IAAI,CACtB,UAAA,WAAW,EAAA;;QAET,IAAI,CAAC,WAAW,EAAE;AAChB,YAAA,MAAM,aAAa,CAAC,MAAM,CAAA,qBAAA,qCAA+B,CAAC;AAC3D,SAAA;KACF,EACD,UAAA,CAAC,EAAA;;AAEC,QAAA,MAAM,aAAa,CAAC,MAAM,CAAA,wBAAA,wCAAkC,CAAC;AAC/D,KAAC,CACF,CAAC;AACF,IAAA,OAAO,YAAY,CAAC,kBAAkB,CAAC,GAAG,CAAC,EAAE,WAAW,CAAC,CAAC,YAAY,EAAE,CAAC;AAC3E,CAAC;AA6BD;;;;;;;;;;;;;;AAcG;AACmB,SAAA,QAAQ,CAC5B,SAAoB,EACpB,OAAyB,EAAA;;;AAEzB,YAAA,SAAS,GAAG,kBAAkB,CAAC,SAAS,CAAC,CAAC;AAC1C,YAAA,OAAA,CAAA,CAAA,aAAOC,UAAS,CAAC,SAA6B,EAAE,OAAO,CAAC,CAAC,CAAA;;;AAC1D,CAAA;AAED;;;;;;;;;AASG;AACG,SAAU,WAAW,CAAC,SAAoB,EAAA;AAC9C,IAAA,SAAS,GAAG,kBAAkB,CAAC,SAAS,CAAC,CAAC;AAC1C,IAAA,OAAOC,aAAY,CAAC,SAA6B,CAAC,CAAC;AACrD,CAAC;AAED;;;;;;;;;;;;AAYG;AACa,SAAA,SAAS,CACvB,SAAoB,EACpB,cAAiE,EAAA;AAEjE,IAAA,SAAS,GAAG,kBAAkB,CAAC,SAAS,CAAC,CAAC;AAC1C,IAAA,OAAOC,WAAU,CAAC,SAA6B,EAAE,cAAc,CAAC,CAAC;AACnE;;ACtJA;;;;;AAKG;AAuCH,yBAAyB,EAAE;;;;"}