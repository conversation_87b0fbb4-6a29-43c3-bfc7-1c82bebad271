<template>
  <div class="extension-detail-container">
    <div v-if="extensionsStore.isLoading" class="loading-message">Loading extension details...</div>
    <div v-if="!extensionsStore.isLoading && !extension" class="not-found-message">
      Extension not found.
      <router-link :to="{ name: 'Marketplace' }">Back to Marketplace</router-link>
    </div>

    <div v-if="extension && !extensionsStore.isLoading" class="extension-content">
      <div class="header">
        <img :src="extension.dev_metadata.author_pic || 'https://via.placeholder.com/50'" :alt="extension.dev_metadata.author_name" class="dev-avatar-large">
        <div>
          <h1>{{ extension.name }} <span class="version-tag-large">v{{ extension.version }}</span></h1>
          <p class="author">
            By <router-link :to="{ name: 'UserProfile', params: { username: extension.dev_metadata.author_id } }">{{ extension.dev_metadata.author_name || 'Unknown Developer' }}</router-link>
          </p>
        </div>
      </div>

      <p class="description-full">{{ extension.description }}</p>

      <div class="tags-detail">
        <span v-for="tag in extension.tags" :key="tag" class="tag">{{ tag }}</span>
      </div>

      <div class="actions-bar">
        <button @click="installExtension" class="install-button">
          <i class="fas fa-download"></i> Install Extension
        </button>
        <p class="downloads-detail">
          <i class="fas fa-users"></i> {{ extension.downloads }} Installs
        </p>
      </div>

      <div class="details-layout">
        <div class="preview-section">
          <h3><i class="fas fa-eye"></i> Live Preview</h3>
          <div class="preview-box">
            <BlockRenderer
              v-if="extension.ui_blocks && extension.ui_blocks.length > 0"
              v-for="block in extension.ui_blocks"
              :key="block.id"
              :block="block"
              @block-event="handlePreviewEvent"
            />
            <p v-else>No UI preview available for this extension.</p>
          </div>
          <div class="event-debugger-preview">
            <h4>Preview Events:</h4>
            <ul v-if="previewEventLog.length > 0">
              <li v-for="(event, index) in previewEventLog" :key="index">
                {{ event.eventName }} on '{{ event.blockId }}'
              </li>
            </ul>
            <p v-else>Interact with the preview to see events here.</p>
          </div>
        </div>

        <div class="json-view-section">
          <h3><i class="fas fa-code"></i> Extension JSON</h3>
          <pre class="json-code-block">{{ JSON.stringify(extension, null, 2) }}</pre>
        </div>
      </div>

      <!-- Placeholder for future comments section -->
      <!-- <div class="comments-section">
        <h3>Comments</h3>
        <p>Comments feature coming soon.</p>
      </div> -->

    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { useExtensionsStore } from '../../store/extensionsStore';
import BlockRenderer from '../../components/extensions/preview/BlockRenderer.vue'; // Re-use renderer

const route = useRoute();
const router = useRouter();
const extensionsStore = useExtensionsStore();

const extensionId = ref(route.params.id);
const extension = ref(null);
const previewEventLog = ref([]);

async function fetchAndSetExtension() {
  if (extensionsStore.extensions.length === 0) {
    await extensionsStore.fetchExtensions(); // Ensure extensions are loaded
  }
  extension.value = extensionsStore.getExtensionById(extensionId.value);
}

onMounted(fetchAndSetExtension);

// Watch for route changes if user navigates between extension detail pages
watch(() => route.params.id, (newId) => {
  if (newId) {
    extensionId.value = newId;
    fetchAndSetExtension();
    previewEventLog.value = []; // Reset preview log for new extension
  }
});

async function installExtension() {
  if (!extension.value) return;

  // Simulate installation process
  console.log(`Installing extension: ${extension.value.name}`);
  const success = await extensionsStore.incrementDownloadCount(extension.value.id);
  if (success) {
    // Update local extension data to reflect new download count immediately
    // This is because the store getter getExtensionById might return a non-reactive copy or
    // we want to ensure this specific instance is updated if it's a deep copy.
    const updatedExtension = extensionsStore.getExtensionById(extension.value.id);
    if (updatedExtension) {
        extension.value = { ...updatedExtension }; // Make it reactive
    }
    alert(`${extension.value.name} installed successfully! (Simulated)`);
  } else {
    alert(`Failed to install ${extension.value.name}. (Simulated)`);
  }
}

function handlePreviewEvent(eventDetails) {
  previewEventLog.value.unshift({ eventName: eventDetails.eventName, blockId: eventDetails.blockId });
  if (previewEventLog.value.length > 5) { // Keep log short
    previewEventLog.value.pop();
  }
}

</script>

<style scoped>
.extension-detail-container {
  padding: 20px;
  max-width: 1000px;
  margin: 0 auto;
}

.loading-message, .not-found-message {
  text-align: center;
  font-size: 1.2em;
  padding: 30px;
  color: #777;
}
.not-found-message a {
  display: block;
  margin-top: 15px;
}

.extension-content h1 {
  font-size: 2.2em;
  margin-bottom: 0.1em;
  color: #333;
}
.version-tag-large {
  font-size: 0.6em;
  background-color: #e0e0e0;
  color: #666;
  padding: 3px 7px;
  border-radius: 4px;
  vertical-align: middle;
  margin-left: 8px;
}

.header {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
}
.dev-avatar-large {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  margin-right: 15px;
  border: 2px solid #eee;
}
.author {
  font-size: 1em;
  color: #555;
  margin-bottom: 15px;
}
.author a {
  color: #007bff;
  text-decoration: none;
}
.author a:hover {
  text-decoration: underline;
}

.description-full {
  font-size: 1.1em;
  line-height: 1.6;
  color: #444;
  margin-bottom: 20px;
}

.tags-detail {
  margin-bottom: 20px;
}
.tags-detail .tag { /* Reusing marketplace tag style, could be componentized */
  background-color: #eef2f7;
  color: #3498db;
  padding: 4px 10px;
  border-radius: 15px;
  font-size: 0.9em;
  margin-right: 8px;
  margin-bottom: 8px;
  display: inline-block;
}

.actions-bar {
  display: flex;
  align-items: center;
  gap: 20px;
  padding: 15px;
  background-color: #f9f9f9;
  border-radius: 8px;
  margin-bottom: 25px;
}
.install-button {
  background-color: #28a745;
  color: white;
  padding: 12px 25px;
  border: none;
  border-radius: 5px;
  font-size: 1.1em;
  cursor: pointer;
  transition: background-color 0.2s;
}
.install-button:hover {
  background-color: #218838;
}
.install-button .fa-download {
  margin-right: 8px;
}
.downloads-detail {
  font-size: 1em;
  color: #555;
}
.downloads-detail .fa-users {
  margin-right: 5px;
}


.details-layout {
  display: grid;
  grid-template-columns: 1fr 1fr; /* Two equal columns */
  gap: 25px;
  margin-top: 20px;
}

.preview-section, .json-view-section {
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  padding: 15px;
  background-color: #fff;
}

.preview-section h3, .json-view-section h3 {
  font-size: 1.3em;
  color: #333;
  margin-top: 0;
  margin-bottom: 15px;
  border-bottom: 1px solid #eee;
  padding-bottom: 10px;
}
.preview-section h3 .fa-eye, .json-view-section h3 .fa-code {
  margin-right: 8px;
  color: #007bff;
}

.preview-box {
  border: 1px dashed #ccc;
  padding: 10px;
  min-height: 150px;
  background-color: #fdfdfd;
  overflow: auto; /* If content is large */
}
.event-debugger-preview {
  margin-top: 15px;
  font-size: 0.9em;
}
.event-debugger-preview h4 {
  font-size: 1em;
  margin-bottom: 5px;
}
.event-debugger-preview ul {
  list-style-type: none;
  padding-left: 0;
  max-height: 100px;
  overflow-y: auto;
  font-family: monospace;
  font-size: 0.9em;
  color: #555;
}


.json-code-block {
  background-color: #2d2d2d; /* Dark background for code */
  color: #f8f8f2; /* Light text */
  padding: 15px;
  border-radius: 5px;
  overflow-x: auto; /* Scroll for long lines */
  max-height: 400px; /* Limit height and make it scrollable */
  font-family: 'Courier New', Courier, monospace;
  font-size: 0.9em;
  line-height: 1.5;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .details-layout {
    grid-template-columns: 1fr; /* Stack columns on smaller screens */
  }
  .header {
    flex-direction: column;
    align-items: flex-start;
  }
  .dev-avatar-large {
    margin-bottom: 10px;
  }
}

</style>
