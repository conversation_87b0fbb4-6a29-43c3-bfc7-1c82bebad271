{"version": 3, "file": "internal.js", "sources": ["../../src/api/authentication/mfa.ts", "../../src/platform_browser/recaptcha/recaptcha_mock.ts", "../../src/platform_browser/recaptcha/recaptcha_loader.ts", "../../src/platform_browser/recaptcha/recaptcha_verifier.ts", "../../src/platform_browser/strategies/phone.ts", "../../src/platform_browser/providers/phone.ts", "../../src/platform_browser/strategies/popup.ts", "../../src/core/util/validate_origin.ts", "../../src/platform_browser/iframe/gapi.ts", "../../src/platform_browser/iframe/iframe.ts", "../../src/platform_browser/util/popup.ts", "../../src/platform_browser/popup_redirect.ts", "../../src/mfa/mfa_assertion.ts", "../../src/platform_browser/mfa/assertions/phone.ts", "../../src/mfa/assertions/totp.ts", "../../src/platform_browser/index.ts", "../../internal/index.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {\n  _performApiRequest,\n  Endpoint,\n  HttpMethod,\n  _addTidIfNecessary\n} from '../index';\nimport { Auth } from '../../model/public_types';\nimport { IdTokenResponse } from '../../model/id_token';\nimport { MfaEnrollment } from '../account_management/mfa';\nimport { SignInWithIdpResponse } from './idp';\nimport {\n  SignInWithPhoneNumberRequest,\n  SignInWithPhoneNumberResponse\n} from './sms';\n\nexport interface FinalizeMfaResponse {\n  idToken: string;\n  refreshToken: string;\n}\n\n/**\n * @internal\n */\nexport interface IdTokenMfaResponse extends IdTokenResponse {\n  mfaPendingCredential?: string;\n  mfaInfo?: MfaEnrollment[];\n}\n\nexport interface StartPhoneMfaSignInRequest {\n  mfaPendingCredential: string;\n  mfaEnrollmentId: string;\n  phoneSignInInfo: {\n    recaptchaToken: string;\n  };\n  tenantId?: string;\n}\n\nexport interface StartPhoneMfaSignInResponse {\n  phoneResponseInfo: {\n    sessionInfo: string;\n  };\n}\n\nexport function startSignInPhoneMfa(\n  auth: Auth,\n  request: StartPhoneMfaSignInRequest\n): Promise<StartPhoneMfaSignInResponse> {\n  return _performApiRequest<\n    StartPhoneMfaSignInRequest,\n    StartPhoneMfaSignInResponse\n  >(\n    auth,\n    HttpMethod.POST,\n    Endpoint.START_MFA_SIGN_IN,\n    _addTidIfNecessary(auth, request)\n  );\n}\n\nexport interface FinalizePhoneMfaSignInRequest {\n  mfaPendingCredential: string;\n  phoneVerificationInfo: SignInWithPhoneNumberRequest;\n  tenantId?: string;\n}\n\n// TOTP MFA Sign in only has a finalize phase. Phone MFA has a start phase to initiate sending an\n// SMS and a finalize phase to complete sign in. With TOTP, the user already has the OTP in the\n// TOTP/Authenticator app.\nexport interface FinalizeTotpMfaSignInRequest {\n  mfaPendingCredential: string;\n  totpVerificationInfo: { verificationCode: string };\n  tenantId?: string;\n  mfaEnrollmentId: string;\n}\n\nexport interface FinalizePhoneMfaSignInResponse extends FinalizeMfaResponse {}\n\nexport interface FinalizeTotpMfaSignInResponse extends FinalizeMfaResponse {}\n\nexport function finalizeSignInPhoneMfa(\n  auth: Auth,\n  request: FinalizePhoneMfaSignInRequest\n): Promise<FinalizePhoneMfaSignInResponse> {\n  return _performApiRequest<\n    FinalizePhoneMfaSignInRequest,\n    FinalizePhoneMfaSignInResponse\n  >(\n    auth,\n    HttpMethod.POST,\n    Endpoint.FINALIZE_MFA_SIGN_IN,\n    _addTidIfNecessary(auth, request)\n  );\n}\n\nexport function finalizeSignInTotpMfa(\n  auth: Auth,\n  request: FinalizeTotpMfaSignInRequest\n): Promise<FinalizeTotpMfaSignInResponse> {\n  return _performApiRequest<\n    FinalizeTotpMfaSignInRequest,\n    FinalizeTotpMfaSignInResponse\n  >(\n    auth,\n    HttpMethod.POST,\n    Endpoint.FINALIZE_MFA_SIGN_IN,\n    _addTidIfNecessary(auth, request)\n  );\n}\n\n/**\n * @internal\n */\nexport type PhoneOrOauthTokenResponse =\n  | SignInWithPhoneNumberResponse\n  | SignInWithIdpResponse\n  | IdTokenResponse;\n", "/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { AuthErrorCode } from '../../core/errors';\nimport { _assert } from '../../core/util/assert';\nimport { AuthInternal } from '../../model/auth';\nimport { RecaptchaParameters } from '../../model/public_types';\nimport {\n  Recaptcha,\n  GreCAPTCHATopLevel,\n  GreCAPTCHARenderOption,\n  GreCAPTCHA\n} from './recaptcha';\n\nexport const _SOLVE_TIME_MS = 500;\nexport const _EXPIRATION_TIME_MS = 60_000;\nexport const _WIDGET_ID_START = 1_000_000_000_000;\n\nexport interface Widget {\n  getResponse: () => string | null;\n  delete: () => void;\n  execute: () => void;\n}\n\nexport class MockReCaptcha implements Recaptcha {\n  private counter = _WIDGET_ID_START;\n  _widgets = new Map<number, Widget>();\n\n  constructor(private readonly auth: AuthInternal) {}\n\n  render(\n    container: string | HTMLElement,\n    parameters?: RecaptchaParameters\n  ): number {\n    const id = this.counter;\n    this._widgets.set(\n      id,\n      new MockWidget(container, this.auth.name, parameters || {})\n    );\n    this.counter++;\n    return id;\n  }\n\n  reset(optWidgetId?: number): void {\n    const id = optWidgetId || _WIDGET_ID_START;\n    void this._widgets.get(id)?.delete();\n    this._widgets.delete(id);\n  }\n\n  getResponse(optWidgetId?: number): string {\n    const id = optWidgetId || _WIDGET_ID_START;\n    return this._widgets.get(id)?.getResponse() || '';\n  }\n\n  async execute(optWidgetId?: number | string): Promise<string> {\n    const id: number = (optWidgetId as number) || _WIDGET_ID_START;\n    void this._widgets.get(id)?.execute();\n    return '';\n  }\n}\n\nexport class MockGreCAPTCHATopLevel implements GreCAPTCHATopLevel {\n  enterprise: GreCAPTCHA = new MockGreCAPTCHA();\n  ready(callback: () => void): void {\n    callback();\n  }\n\n  execute(\n    // eslint-disable-next-line @typescript-eslint/no-unused-vars\n    _siteKey: string,\n    _options: { action: string }\n  ): Promise<string> {\n    return Promise.resolve('token');\n  }\n  render(\n    // eslint-disable-next-line @typescript-eslint/no-unused-vars\n    _container: string | HTMLElement,\n    _parameters: GreCAPTCHARenderOption\n  ): string {\n    return '';\n  }\n}\n\nexport class MockGreCAPTCHA implements GreCAPTCHA {\n  ready(callback: () => void): void {\n    callback();\n  }\n\n  execute(\n    // eslint-disable-next-line @typescript-eslint/no-unused-vars\n    _siteKey: string,\n    _options: { action: string }\n  ): Promise<string> {\n    return Promise.resolve('token');\n  }\n  render(\n    // eslint-disable-next-line @typescript-eslint/no-unused-vars\n    _container: string | HTMLElement,\n    _parameters: GreCAPTCHARenderOption\n  ): string {\n    return '';\n  }\n}\n\nexport class MockWidget {\n  private readonly container: HTMLElement;\n  private readonly isVisible: boolean;\n  private timerId: number | null = null;\n  private deleted = false;\n  private responseToken: string | null = null;\n  private readonly clickHandler = (): void => {\n    this.execute();\n  };\n\n  constructor(\n    containerOrId: string | HTMLElement,\n    appName: string,\n    private readonly params: RecaptchaParameters\n  ) {\n    const container =\n      typeof containerOrId === 'string'\n        ? document.getElementById(containerOrId)\n        : containerOrId;\n    _assert(container, AuthErrorCode.ARGUMENT_ERROR, { appName });\n\n    this.container = container;\n    this.isVisible = this.params.size !== 'invisible';\n    if (this.isVisible) {\n      this.execute();\n    } else {\n      this.container.addEventListener('click', this.clickHandler);\n    }\n  }\n\n  getResponse(): string | null {\n    this.checkIfDeleted();\n    return this.responseToken;\n  }\n\n  delete(): void {\n    this.checkIfDeleted();\n    this.deleted = true;\n    if (this.timerId) {\n      clearTimeout(this.timerId);\n      this.timerId = null;\n    }\n    this.container.removeEventListener('click', this.clickHandler);\n  }\n\n  execute(): void {\n    this.checkIfDeleted();\n    if (this.timerId) {\n      return;\n    }\n\n    this.timerId = window.setTimeout(() => {\n      this.responseToken = generateRandomAlphaNumericString(50);\n      const { callback, 'expired-callback': expiredCallback } = this.params;\n      if (callback) {\n        try {\n          callback(this.responseToken);\n        } catch (e) {}\n      }\n\n      this.timerId = window.setTimeout(() => {\n        this.timerId = null;\n        this.responseToken = null;\n        if (expiredCallback) {\n          try {\n            expiredCallback();\n          } catch (e) {}\n        }\n\n        if (this.isVisible) {\n          this.execute();\n        }\n      }, _EXPIRATION_TIME_MS);\n    }, _SOLVE_TIME_MS);\n  }\n\n  private checkIfDeleted(): void {\n    if (this.deleted) {\n      throw new Error('reCAPTCHA mock was already deleted!');\n    }\n  }\n}\n\nfunction generateRandomAlphaNumericString(len: number): string {\n  const chars = [];\n  const allowedChars =\n    '1234567890abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ';\n  for (let i = 0; i < len; i++) {\n    chars.push(\n      allowedChars.charAt(Math.floor(Math.random() * allowedChars.length))\n    );\n  }\n  return chars.join('');\n}\n", "/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { querystring } from '@firebase/util';\n\nimport { AuthErrorCode } from '../../core/errors';\nimport { _assert, _createError } from '../../core/util/assert';\nimport { Delay } from '../../core/util/delay';\nimport { AuthInternal } from '../../model/auth';\nimport { _window } from '../auth_window';\nimport * as jsHelpers from '../load_js';\nimport { Recaptcha, isV2 } from './recaptcha';\nimport { MockReCaptcha } from './recaptcha_mock';\n\n// ReCaptch<PERSON> will load using the same callback, so the callback function needs\n// to be kept around\nexport const _JSLOAD_CALLBACK = jsHelpers._generateCallbackName('rcb');\nconst NETWORK_TIMEOUT_DELAY = new Delay(30000, 60000);\n\n/**\n * We need to mark this interface as internal explicitly to exclude it in the public typings, because\n * it references AuthInternal which has a circular dependency with UserInternal.\n *\n * @internal\n */\nexport interface ReCaptchaLoader {\n  load(auth: AuthInternal, hl?: string): Promise<Recaptcha>;\n  clearedOneInstance(): void;\n}\n\n/**\n * Loader for the GReCaptcha library. There should only ever be one of this.\n */\nexport class ReCaptchaLoaderImpl implements ReCaptchaLoader {\n  private hostLanguage = '';\n  private counter = 0;\n  /**\n   * Check for `render()` method. `window.grecaptcha` will exist if the Enterprise\n   * version of the ReCAPTCHA script was loaded by someone else (e.g. App Check) but\n   * `window.grecaptcha.render()` will not. Another load will add it.\n   */\n  private readonly librarySeparatelyLoaded = !!_window().grecaptcha?.render;\n\n  load(auth: AuthInternal, hl = ''): Promise<Recaptcha> {\n    _assert(isHostLanguageValid(hl), auth, AuthErrorCode.ARGUMENT_ERROR);\n\n    if (this.shouldResolveImmediately(hl) && isV2(_window().grecaptcha)) {\n      return Promise.resolve(_window().grecaptcha! as Recaptcha);\n    }\n    return new Promise<Recaptcha>((resolve, reject) => {\n      const networkTimeout = _window().setTimeout(() => {\n        reject(_createError(auth, AuthErrorCode.NETWORK_REQUEST_FAILED));\n      }, NETWORK_TIMEOUT_DELAY.get());\n\n      _window()[_JSLOAD_CALLBACK] = () => {\n        _window().clearTimeout(networkTimeout);\n        delete _window()[_JSLOAD_CALLBACK];\n\n        const recaptcha = _window().grecaptcha as Recaptcha;\n\n        if (!recaptcha || !isV2(recaptcha)) {\n          reject(_createError(auth, AuthErrorCode.INTERNAL_ERROR));\n          return;\n        }\n\n        // Wrap the recaptcha render function so that we know if the developer has\n        // called it separately\n        const render = recaptcha.render;\n        recaptcha.render = (container, params) => {\n          const widgetId = render(container, params);\n          this.counter++;\n          return widgetId;\n        };\n\n        this.hostLanguage = hl;\n        resolve(recaptcha);\n      };\n\n      const url = `${jsHelpers._recaptchaV2ScriptUrl()}?${querystring({\n        onload: _JSLOAD_CALLBACK,\n        render: 'explicit',\n        hl\n      })}`;\n\n      jsHelpers._loadJS(url).catch(() => {\n        clearTimeout(networkTimeout);\n        reject(_createError(auth, AuthErrorCode.INTERNAL_ERROR));\n      });\n    });\n  }\n\n  clearedOneInstance(): void {\n    this.counter--;\n  }\n\n  private shouldResolveImmediately(hl: string): boolean {\n    // We can resolve immediately if:\n    //   • grecaptcha is already defined AND (\n    //     1. the requested language codes are the same OR\n    //     2. there exists already a ReCaptcha on the page\n    //     3. the library was already loaded by the app\n    // In cases (2) and (3), we _can't_ reload as it would break the recaptchas\n    // that are already in the page\n    return (\n      !!_window().grecaptcha?.render &&\n      (hl === this.hostLanguage ||\n        this.counter > 0 ||\n        this.librarySeparatelyLoaded)\n    );\n  }\n}\n\nfunction isHostLanguageValid(hl: string): boolean {\n  return hl.length <= 6 && /^\\s*[a-zA-Z0-9\\-]*\\s*$/.test(hl);\n}\n\nexport class MockReCaptchaLoaderImpl implements ReCaptchaLoader {\n  async load(auth: AuthInternal): Promise<Recaptcha> {\n    return new MockReCaptcha(auth);\n  }\n\n  clearedOneInstance(): void {}\n}\n", "/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { Auth, RecaptchaParameters } from '../../model/public_types';\nimport { getRecaptchaParams } from '../../api/authentication/recaptcha';\nimport { _castAuth } from '../../core/auth/auth_impl';\nimport { AuthErrorCode } from '../../core/errors';\nimport { _assert } from '../../core/util/assert';\nimport { _isHttpOrHttps } from '../../core/util/location';\nimport { ApplicationVerifierInternal } from '../../model/application_verifier';\nimport { AuthInternal } from '../../model/auth';\nimport { _window } from '../auth_window';\nimport { _isWorker } from '../util/worker';\nimport { Recaptcha } from './recaptcha';\nimport {\n  MockReCaptchaLoaderImpl,\n  ReCaptchaLoader,\n  ReCaptchaLoaderImpl\n} from './recaptcha_loader';\n\nexport const RECAPTCHA_VERIFIER_TYPE = 'recaptcha';\n\nconst DEFAULT_PARAMS: RecaptchaParameters = {\n  theme: 'light',\n  type: 'image'\n};\n\ntype TokenCallback = (token: string) => void;\n\n/**\n * An {@link https://www.google.com/recaptcha/ | reCAPTCHA}-based application verifier.\n *\n * @remarks\n * `RecaptchaVerifier` does not work in a Node.js environment.\n *\n * @public\n */\nexport class RecaptchaVerifier implements ApplicationVerifierInternal {\n  /**\n   * The application verifier type.\n   *\n   * @remarks\n   * For a reCAPTCHA verifier, this is 'recaptcha'.\n   */\n  readonly type = RECAPTCHA_VERIFIER_TYPE;\n  private destroyed = false;\n  private widgetId: number | null = null;\n  private readonly container: HTMLElement;\n  private readonly isInvisible: boolean;\n  private readonly tokenChangeListeners = new Set<TokenCallback>();\n  private renderPromise: Promise<number> | null = null;\n  private readonly auth: AuthInternal;\n\n  /** @internal */\n  readonly _recaptchaLoader: ReCaptchaLoader;\n  private recaptcha: Recaptcha | null = null;\n\n  /**\n   * @param authExtern - The corresponding Firebase {@link Auth} instance.\n   *\n   * @param containerOrId - The reCAPTCHA container parameter.\n   *\n   * @remarks\n   * This has different meaning depending on whether the reCAPTCHA is hidden or visible. For a\n   * visible reCAPTCHA the container must be empty. If a string is used, it has to correspond to\n   * an element ID. The corresponding element must also must be in the DOM at the time of\n   * initialization.\n   *\n   * @param parameters - The optional reCAPTCHA parameters.\n   *\n   * @remarks\n   * Check the reCAPTCHA docs for a comprehensive list. All parameters are accepted except for\n   * the sitekey. Firebase Auth backend provisions a reCAPTCHA for each project and will\n   * configure this upon rendering. For an invisible reCAPTCHA, a size key must have the value\n   * 'invisible'.\n   */\n  constructor(\n    authExtern: Auth,\n    containerOrId: HTMLElement | string,\n    private readonly parameters: RecaptchaParameters = {\n      ...DEFAULT_PARAMS\n    }\n  ) {\n    this.auth = _castAuth(authExtern);\n    this.isInvisible = this.parameters.size === 'invisible';\n    _assert(\n      typeof document !== 'undefined',\n      this.auth,\n      AuthErrorCode.OPERATION_NOT_SUPPORTED\n    );\n    const container =\n      typeof containerOrId === 'string'\n        ? document.getElementById(containerOrId)\n        : containerOrId;\n    _assert(container, this.auth, AuthErrorCode.ARGUMENT_ERROR);\n\n    this.container = container;\n    this.parameters.callback = this.makeTokenCallback(this.parameters.callback);\n\n    this._recaptchaLoader = this.auth.settings.appVerificationDisabledForTesting\n      ? new MockReCaptchaLoaderImpl()\n      : new ReCaptchaLoaderImpl();\n\n    this.validateStartingState();\n    // TODO: Figure out if sdk version is needed\n  }\n\n  /**\n   * Waits for the user to solve the reCAPTCHA and resolves with the reCAPTCHA token.\n   *\n   * @returns A Promise for the reCAPTCHA token.\n   */\n  async verify(): Promise<string> {\n    this.assertNotDestroyed();\n    const id = await this.render();\n    const recaptcha = this.getAssertedRecaptcha();\n\n    const response = recaptcha.getResponse(id);\n    if (response) {\n      return response;\n    }\n\n    return new Promise<string>(resolve => {\n      const tokenChange = (token: string): void => {\n        if (!token) {\n          return; // Ignore token expirations.\n        }\n        this.tokenChangeListeners.delete(tokenChange);\n        resolve(token);\n      };\n\n      this.tokenChangeListeners.add(tokenChange);\n      if (this.isInvisible) {\n        recaptcha.execute(id);\n      }\n    });\n  }\n\n  /**\n   * Renders the reCAPTCHA widget on the page.\n   *\n   * @returns A Promise that resolves with the reCAPTCHA widget ID.\n   */\n  render(): Promise<number> {\n    try {\n      this.assertNotDestroyed();\n    } catch (e) {\n      // This method returns a promise. Since it's not async (we want to return the\n      // _same_ promise if rendering is still occurring), the API surface should\n      // reject with the error rather than just throw\n      return Promise.reject(e);\n    }\n\n    if (this.renderPromise) {\n      return this.renderPromise;\n    }\n\n    this.renderPromise = this.makeRenderPromise().catch(e => {\n      this.renderPromise = null;\n      throw e;\n    });\n\n    return this.renderPromise;\n  }\n\n  /** @internal */\n  _reset(): void {\n    this.assertNotDestroyed();\n    if (this.widgetId !== null) {\n      this.getAssertedRecaptcha().reset(this.widgetId);\n    }\n  }\n\n  /**\n   * Clears the reCAPTCHA widget from the page and destroys the instance.\n   */\n  clear(): void {\n    this.assertNotDestroyed();\n    this.destroyed = true;\n    this._recaptchaLoader.clearedOneInstance();\n    if (!this.isInvisible) {\n      this.container.childNodes.forEach(node => {\n        this.container.removeChild(node);\n      });\n    }\n  }\n\n  private validateStartingState(): void {\n    _assert(!this.parameters.sitekey, this.auth, AuthErrorCode.ARGUMENT_ERROR);\n    _assert(\n      this.isInvisible || !this.container.hasChildNodes(),\n      this.auth,\n      AuthErrorCode.ARGUMENT_ERROR\n    );\n    _assert(\n      typeof document !== 'undefined',\n      this.auth,\n      AuthErrorCode.OPERATION_NOT_SUPPORTED\n    );\n  }\n\n  private makeTokenCallback(\n    existing: TokenCallback | string | undefined\n  ): TokenCallback {\n    return token => {\n      this.tokenChangeListeners.forEach(listener => listener(token));\n      if (typeof existing === 'function') {\n        existing(token);\n      } else if (typeof existing === 'string') {\n        const globalFunc = _window()[existing];\n        if (typeof globalFunc === 'function') {\n          globalFunc(token);\n        }\n      }\n    };\n  }\n\n  private assertNotDestroyed(): void {\n    _assert(!this.destroyed, this.auth, AuthErrorCode.INTERNAL_ERROR);\n  }\n\n  private async makeRenderPromise(): Promise<number> {\n    await this.init();\n    if (!this.widgetId) {\n      let container = this.container;\n      if (!this.isInvisible) {\n        const guaranteedEmpty = document.createElement('div');\n        container.appendChild(guaranteedEmpty);\n        container = guaranteedEmpty;\n      }\n\n      this.widgetId = this.getAssertedRecaptcha().render(\n        container,\n        this.parameters\n      );\n    }\n\n    return this.widgetId;\n  }\n\n  private async init(): Promise<void> {\n    _assert(\n      _isHttpOrHttps() && !_isWorker(),\n      this.auth,\n      AuthErrorCode.INTERNAL_ERROR\n    );\n\n    await domReady();\n    this.recaptcha = await this._recaptchaLoader.load(\n      this.auth,\n      this.auth.languageCode || undefined\n    );\n\n    const siteKey = await getRecaptchaParams(this.auth);\n    _assert(siteKey, this.auth, AuthErrorCode.INTERNAL_ERROR);\n    this.parameters.sitekey = siteKey;\n  }\n\n  private getAssertedRecaptcha(): Recaptcha {\n    _assert(this.recaptcha, this.auth, AuthErrorCode.INTERNAL_ERROR);\n    return this.recaptcha;\n  }\n}\n\nfunction domReady(): Promise<void> {\n  let resolver: (() => void) | null = null;\n  return new Promise<void>(resolve => {\n    if (document.readyState === 'complete') {\n      resolve();\n      return;\n    }\n\n    // Document not ready, wait for load before resolving.\n    // Save resolver, so we can remove listener in case it was externally\n    // cancelled.\n    resolver = () => resolve();\n    window.addEventListener('load', resolver);\n  }).catch(e => {\n    if (resolver) {\n      window.removeEventListener('load', resolver);\n    }\n\n    throw e;\n  });\n}\n", "/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {\n  ApplicationVerifier,\n  Auth,\n  ConfirmationResult,\n  PhoneInfoOptions,\n  User,\n  UserCredential\n} from '../../model/public_types';\n\nimport { startEnrollPhoneMfa } from '../../api/account_management/mfa';\nimport { startSignInPhoneMfa } from '../../api/authentication/mfa';\nimport { sendPhoneVerificationCode } from '../../api/authentication/sms';\nimport { ApplicationVerifierInternal } from '../../model/application_verifier';\nimport { PhoneAuthCredential } from '../../core/credentials/phone';\nimport { AuthErrorCode } from '../../core/errors';\nimport { _assertLinkedStatus, _link } from '../../core/user/link_unlink';\nimport {\n  _assert,\n  _serverAppCurrentUserOperationNotSupportedError\n} from '../../core/util/assert';\nimport { AuthInternal } from '../../model/auth';\nimport {\n  linkWithCredential,\n  reauthenticateWithCredential,\n  signInWithCredential\n} from '../../core/strategies/credential';\nimport {\n  MultiFactorSessionImpl,\n  MultiFactorSessionType\n} from '../../mfa/mfa_session';\nimport { UserInternal } from '../../model/user';\nimport { RECAPTCHA_VERIFIER_TYPE } from '../recaptcha/recaptcha_verifier';\nimport { _castAuth } from '../../core/auth/auth_impl';\nimport { getModularInstance } from '@firebase/util';\nimport { ProviderId } from '../../model/enums';\nimport { _isFirebaseServerApp } from '@firebase/app';\n\ninterface OnConfirmationCallback {\n  (credential: PhoneAuthCredential): Promise<UserCredential>;\n}\n\nclass ConfirmationResultImpl implements ConfirmationResult {\n  constructor(\n    readonly verificationId: string,\n    private readonly onConfirmation: OnConfirmationCallback\n  ) {}\n\n  confirm(verificationCode: string): Promise<UserCredential> {\n    const authCredential = PhoneAuthCredential._fromVerification(\n      this.verificationId,\n      verificationCode\n    );\n    return this.onConfirmation(authCredential);\n  }\n}\n\n/**\n * Asynchronously signs in using a phone number.\n *\n * @remarks\n * This method sends a code via SMS to the given\n * phone number, and returns a {@link ConfirmationResult}. After the user\n * provides the code sent to their phone, call {@link ConfirmationResult.confirm}\n * with the code to sign the user in.\n *\n * For abuse prevention, this method also requires a {@link ApplicationVerifier}.\n * This SDK includes a reCAPTCHA-based implementation, {@link RecaptchaVerifier}.\n * This function can work on other platforms that do not support the\n * {@link RecaptchaVerifier} (like React Native), but you need to use a\n * third-party {@link ApplicationVerifier} implementation.\n *\n * This method does not work in a Node.js environment or with {@link Auth} instances created with a\n * {@link @firebase/app#FirebaseServerApp}.\n *\n * @example\n * ```javascript\n * // 'recaptcha-container' is the ID of an element in the DOM.\n * const applicationVerifier = new firebase.auth.RecaptchaVerifier('recaptcha-container');\n * const confirmationResult = await signInWithPhoneNumber(auth, phoneNumber, applicationVerifier);\n * // Obtain a verificationCode from the user.\n * const credential = await confirmationResult.confirm(verificationCode);\n * ```\n *\n * @param auth - The {@link Auth} instance.\n * @param phoneNumber - The user's phone number in E.164 format (e.g. +***********).\n * @param appVerifier - The {@link ApplicationVerifier}.\n *\n * @public\n */\nexport async function signInWithPhoneNumber(\n  auth: Auth,\n  phoneNumber: string,\n  appVerifier: ApplicationVerifier\n): Promise<ConfirmationResult> {\n  if (_isFirebaseServerApp(auth.app)) {\n    return Promise.reject(\n      _serverAppCurrentUserOperationNotSupportedError(auth)\n    );\n  }\n  const authInternal = _castAuth(auth);\n  const verificationId = await _verifyPhoneNumber(\n    authInternal,\n    phoneNumber,\n    getModularInstance(appVerifier as ApplicationVerifierInternal)\n  );\n  return new ConfirmationResultImpl(verificationId, cred =>\n    signInWithCredential(authInternal, cred)\n  );\n}\n\n/**\n * Links the user account with the given phone number.\n *\n * @remarks\n * This method does not work in a Node.js environment.\n *\n * @param user - The user.\n * @param phoneNumber - The user's phone number in E.164 format (e.g. +***********).\n * @param appVerifier - The {@link ApplicationVerifier}.\n *\n * @public\n */\nexport async function linkWithPhoneNumber(\n  user: User,\n  phoneNumber: string,\n  appVerifier: ApplicationVerifier\n): Promise<ConfirmationResult> {\n  const userInternal = getModularInstance(user) as UserInternal;\n  await _assertLinkedStatus(false, userInternal, ProviderId.PHONE);\n  const verificationId = await _verifyPhoneNumber(\n    userInternal.auth,\n    phoneNumber,\n    getModularInstance(appVerifier as ApplicationVerifierInternal)\n  );\n  return new ConfirmationResultImpl(verificationId, cred =>\n    linkWithCredential(userInternal, cred)\n  );\n}\n\n/**\n * Re-authenticates a user using a fresh phone credential.\n *\n * @remarks\n * Use before operations such as {@link updatePassword} that require tokens from recent sign-in attempts.\n *\n * This method does not work in a Node.js environment or on any {@link User} signed in by\n * {@link Auth} instances created with a {@link @firebase/app#FirebaseServerApp}.\n *\n * @param user - The user.\n * @param phoneNumber - The user's phone number in E.164 format (e.g. +***********).\n * @param appVerifier - The {@link ApplicationVerifier}.\n *\n * @public\n */\nexport async function reauthenticateWithPhoneNumber(\n  user: User,\n  phoneNumber: string,\n  appVerifier: ApplicationVerifier\n): Promise<ConfirmationResult> {\n  const userInternal = getModularInstance(user) as UserInternal;\n  if (_isFirebaseServerApp(userInternal.auth.app)) {\n    return Promise.reject(\n      _serverAppCurrentUserOperationNotSupportedError(userInternal.auth)\n    );\n  }\n  const verificationId = await _verifyPhoneNumber(\n    userInternal.auth,\n    phoneNumber,\n    getModularInstance(appVerifier as ApplicationVerifierInternal)\n  );\n  return new ConfirmationResultImpl(verificationId, cred =>\n    reauthenticateWithCredential(userInternal, cred)\n  );\n}\n\n/**\n * Returns a verification ID to be used in conjunction with the SMS code that is sent.\n *\n */\nexport async function _verifyPhoneNumber(\n  auth: AuthInternal,\n  options: PhoneInfoOptions | string,\n  verifier: ApplicationVerifierInternal\n): Promise<string> {\n  const recaptchaToken = await verifier.verify();\n\n  try {\n    _assert(\n      typeof recaptchaToken === 'string',\n      auth,\n      AuthErrorCode.ARGUMENT_ERROR\n    );\n    _assert(\n      verifier.type === RECAPTCHA_VERIFIER_TYPE,\n      auth,\n      AuthErrorCode.ARGUMENT_ERROR\n    );\n\n    let phoneInfoOptions: PhoneInfoOptions;\n\n    if (typeof options === 'string') {\n      phoneInfoOptions = {\n        phoneNumber: options\n      };\n    } else {\n      phoneInfoOptions = options;\n    }\n\n    if ('session' in phoneInfoOptions) {\n      const session = phoneInfoOptions.session as MultiFactorSessionImpl;\n\n      if ('phoneNumber' in phoneInfoOptions) {\n        _assert(\n          session.type === MultiFactorSessionType.ENROLL,\n          auth,\n          AuthErrorCode.INTERNAL_ERROR\n        );\n        const response = await startEnrollPhoneMfa(auth, {\n          idToken: session.credential,\n          phoneEnrollmentInfo: {\n            phoneNumber: phoneInfoOptions.phoneNumber,\n            recaptchaToken\n          }\n        });\n        return response.phoneSessionInfo.sessionInfo;\n      } else {\n        _assert(\n          session.type === MultiFactorSessionType.SIGN_IN,\n          auth,\n          AuthErrorCode.INTERNAL_ERROR\n        );\n        const mfaEnrollmentId =\n          phoneInfoOptions.multiFactorHint?.uid ||\n          phoneInfoOptions.multiFactorUid;\n        _assert(mfaEnrollmentId, auth, AuthErrorCode.MISSING_MFA_INFO);\n        const response = await startSignInPhoneMfa(auth, {\n          mfaPendingCredential: session.credential,\n          mfaEnrollmentId,\n          phoneSignInInfo: {\n            recaptchaToken\n          }\n        });\n        return response.phoneResponseInfo.sessionInfo;\n      }\n    } else {\n      const { sessionInfo } = await sendPhoneVerificationCode(auth, {\n        phoneNumber: phoneInfoOptions.phoneNumber,\n        recaptchaToken\n      });\n      return sessionInfo;\n    }\n  } finally {\n    verifier._reset();\n  }\n}\n\n/**\n * Updates the user's phone number.\n *\n * @remarks\n * This method does not work in a Node.js environment or on any {@link User} signed in by\n * {@link Auth} instances created with a {@link @firebase/app#FirebaseServerApp}.\n *\n * @example\n * ```\n * // 'recaptcha-container' is the ID of an element in the DOM.\n * const applicationVerifier = new RecaptchaVerifier('recaptcha-container');\n * const provider = new PhoneAuthProvider(auth);\n * const verificationId = await provider.verifyPhoneNumber('+***********', applicationVerifier);\n * // Obtain the verificationCode from the user.\n * const phoneCredential = PhoneAuthProvider.credential(verificationId, verificationCode);\n * await updatePhoneNumber(user, phoneCredential);\n * ```\n *\n * @param user - The user.\n * @param credential - A credential authenticating the new phone number.\n *\n * @public\n */\nexport async function updatePhoneNumber(\n  user: User,\n  credential: PhoneAuthCredential\n): Promise<void> {\n  const userInternal = getModularInstance(user) as UserInternal;\n  if (_isFirebaseServerApp(userInternal.auth.app)) {\n    return Promise.reject(\n      _serverAppCurrentUserOperationNotSupportedError(userInternal.auth)\n    );\n  }\n  await _link(userInternal, credential);\n}\n", "/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {\n  Auth,\n  PhoneInfoOptions,\n  ApplicationVerifier,\n  UserCredential\n} from '../../model/public_types';\n\nimport { SignInWithPhoneNumberResponse } from '../../api/authentication/sms';\nimport { ApplicationVerifierInternal as ApplicationVerifierInternal } from '../../model/application_verifier';\nimport { AuthInternal as AuthInternal } from '../../model/auth';\nimport { UserCredentialInternal as UserCredentialInternal } from '../../model/user';\nimport { PhoneAuthCredential } from '../../core/credentials/phone';\nimport { _verifyPhoneNumber } from '../strategies/phone';\nimport { _castAuth } from '../../core/auth/auth_impl';\nimport { AuthCredential } from '../../core';\nimport { FirebaseError, getModularInstance } from '@firebase/util';\nimport { TaggedWithTokenResponse } from '../../model/id_token';\nimport { ProviderId, SignInMethod } from '../../model/enums';\n\n/**\n * Provider for generating an {@link PhoneAuthCredential}.\n *\n * @remarks\n * `PhoneAuthProvider` does not work in a Node.js environment.\n *\n * @example\n * ```javascript\n * // 'recaptcha-container' is the ID of an element in the DOM.\n * const applicationVerifier = new RecaptchaVerifier('recaptcha-container');\n * const provider = new PhoneAuthProvider(auth);\n * const verificationId = await provider.verifyPhoneNumber('+***********', applicationVerifier);\n * // Obtain the verificationCode from the user.\n * const phoneCredential = PhoneAuthProvider.credential(verificationId, verificationCode);\n * const userCredential = await signInWithCredential(auth, phoneCredential);\n * ```\n *\n * @public\n */\nexport class PhoneAuthProvider {\n  /** Always set to {@link ProviderId}.PHONE. */\n  static readonly PROVIDER_ID: 'phone' = ProviderId.PHONE;\n  /** Always set to {@link SignInMethod}.PHONE. */\n  static readonly PHONE_SIGN_IN_METHOD: 'phone' = SignInMethod.PHONE;\n\n  /** Always set to {@link ProviderId}.PHONE. */\n  readonly providerId = PhoneAuthProvider.PROVIDER_ID;\n  private readonly auth: AuthInternal;\n\n  /**\n   * @param auth - The Firebase {@link Auth} instance in which sign-ins should occur.\n   *\n   */\n  constructor(auth: Auth) {\n    this.auth = _castAuth(auth);\n  }\n\n  /**\n   *\n   * Starts a phone number authentication flow by sending a verification code to the given phone\n   * number.\n   *\n   * @example\n   * ```javascript\n   * const provider = new PhoneAuthProvider(auth);\n   * const verificationId = await provider.verifyPhoneNumber(phoneNumber, applicationVerifier);\n   * // Obtain verificationCode from the user.\n   * const authCredential = PhoneAuthProvider.credential(verificationId, verificationCode);\n   * const userCredential = await signInWithCredential(auth, authCredential);\n   * ```\n   *\n   * @example\n   * An alternative flow is provided using the `signInWithPhoneNumber` method.\n   * ```javascript\n   * const confirmationResult = signInWithPhoneNumber(auth, phoneNumber, applicationVerifier);\n   * // Obtain verificationCode from the user.\n   * const userCredential = confirmationResult.confirm(verificationCode);\n   * ```\n   *\n   * @param phoneInfoOptions - The user's {@link PhoneInfoOptions}. The phone number should be in\n   * E.164 format (e.g. +***********).\n   * @param applicationVerifier - For abuse prevention, this method also requires a\n   * {@link ApplicationVerifier}. This SDK includes a reCAPTCHA-based implementation,\n   * {@link RecaptchaVerifier}.\n   *\n   * @returns A Promise for a verification ID that can be passed to\n   * {@link PhoneAuthProvider.credential} to identify this flow..\n   */\n  verifyPhoneNumber(\n    phoneOptions: PhoneInfoOptions | string,\n    applicationVerifier: ApplicationVerifier\n  ): Promise<string> {\n    return _verifyPhoneNumber(\n      this.auth,\n      phoneOptions,\n      getModularInstance(applicationVerifier as ApplicationVerifierInternal)\n    );\n  }\n\n  /**\n   * Creates a phone auth credential, given the verification ID from\n   * {@link PhoneAuthProvider.verifyPhoneNumber} and the code that was sent to the user's\n   * mobile device.\n   *\n   * @example\n   * ```javascript\n   * const provider = new PhoneAuthProvider(auth);\n   * const verificationId = provider.verifyPhoneNumber(phoneNumber, applicationVerifier);\n   * // Obtain verificationCode from the user.\n   * const authCredential = PhoneAuthProvider.credential(verificationId, verificationCode);\n   * const userCredential = signInWithCredential(auth, authCredential);\n   * ```\n   *\n   * @example\n   * An alternative flow is provided using the `signInWithPhoneNumber` method.\n   * ```javascript\n   * const confirmationResult = await signInWithPhoneNumber(auth, phoneNumber, applicationVerifier);\n   * // Obtain verificationCode from the user.\n   * const userCredential = await confirmationResult.confirm(verificationCode);\n   * ```\n   *\n   * @param verificationId - The verification ID returned from {@link PhoneAuthProvider.verifyPhoneNumber}.\n   * @param verificationCode - The verification code sent to the user's mobile device.\n   *\n   * @returns The auth provider credential.\n   */\n  static credential(\n    verificationId: string,\n    verificationCode: string\n  ): PhoneAuthCredential {\n    return PhoneAuthCredential._fromVerification(\n      verificationId,\n      verificationCode\n    );\n  }\n\n  /**\n   * Generates an {@link AuthCredential} from a {@link UserCredential}.\n   * @param userCredential - The user credential.\n   */\n  static credentialFromResult(\n    userCredential: UserCredential\n  ): AuthCredential | null {\n    const credential = userCredential as UserCredentialInternal;\n    return PhoneAuthProvider.credentialFromTaggedObject(credential);\n  }\n\n  /**\n   * Returns an {@link AuthCredential} when passed an error.\n   *\n   * @remarks\n   *\n   * This method works for errors like\n   * `auth/account-exists-with-different-credentials`. This is useful for\n   * recovering when attempting to set a user's phone number but the number\n   * in question is already tied to another account. For example, the following\n   * code tries to update the current user's phone number, and if that\n   * fails, links the user with the account associated with that number:\n   *\n   * ```js\n   * const provider = new PhoneAuthProvider(auth);\n   * const verificationId = await provider.verifyPhoneNumber(number, verifier);\n   * try {\n   *   const code = ''; // Prompt the user for the verification code\n   *   await updatePhoneNumber(\n   *       auth.currentUser,\n   *       PhoneAuthProvider.credential(verificationId, code));\n   * } catch (e) {\n   *   if ((e as FirebaseError)?.code === 'auth/account-exists-with-different-credential') {\n   *     const cred = PhoneAuthProvider.credentialFromError(e);\n   *     await linkWithCredential(auth.currentUser, cred);\n   *   }\n   * }\n   *\n   * // At this point, auth.currentUser.phoneNumber === number.\n   * ```\n   *\n   * @param error - The error to generate a credential from.\n   */\n  static credentialFromError(error: FirebaseError): AuthCredential | null {\n    return PhoneAuthProvider.credentialFromTaggedObject(\n      (error.customData || {}) as TaggedWithTokenResponse\n    );\n  }\n\n  private static credentialFromTaggedObject({\n    _tokenResponse: tokenResponse\n  }: TaggedWithTokenResponse): AuthCredential | null {\n    if (!tokenResponse) {\n      return null;\n    }\n    const { phoneNumber, temporaryProof } =\n      tokenResponse as SignInWithPhoneNumberResponse;\n    if (phoneNumber && temporaryProof) {\n      return PhoneAuthCredential._fromTokenResponse(\n        phoneNumber,\n        temporaryProof\n      );\n    }\n    return null;\n  }\n}\n", "/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {\n  Auth,\n  AuthProvider,\n  PopupRedirectResolver,\n  User,\n  UserCredential\n} from '../../model/public_types';\n\nimport { _castAuth } from '../../core/auth/auth_impl';\nimport { AuthErrorCode } from '../../core/errors';\nimport {\n  _assert,\n  debugAssert,\n  _createError,\n  _assertInstanceOf\n} from '../../core/util/assert';\nimport { Delay } from '../../core/util/delay';\nimport { _generateEventId } from '../../core/util/event_id';\nimport { AuthInternal } from '../../model/auth';\nimport {\n  AuthEventType,\n  PopupRedirectResolverInternal\n} from '../../model/popup_redirect';\nimport { UserInternal } from '../../model/user';\nimport { _withDefaultResolver } from '../../core/util/resolver';\nimport { AuthPopup } from '../util/popup';\nimport { AbstractPopupRedirectOperation } from '../../core/strategies/abstract_popup_redirect_operation';\nimport { FederatedAuthProvider } from '../../core/providers/federated';\nimport { getModularInstance } from '@firebase/util';\nimport { _isFirebaseServerApp } from '@firebase/app';\n\n/*\n * The event timeout is the same on mobile and desktop, no need for Delay. Set this to 8s since\n * blocking functions can take upto 7s to complete sign in, as documented in:\n * https://cloud.google.com/identity-platform/docs/blocking-functions#understanding_blocking_functions\n * https://firebase.google.com/docs/auth/extend-with-blocking-functions#understanding_blocking_functions\n */\nexport const enum _Timeout {\n  AUTH_EVENT = 8000\n}\nexport const _POLL_WINDOW_CLOSE_TIMEOUT = new Delay(2000, 10000);\n\n/**\n * Authenticates a Firebase client using a popup-based OAuth authentication flow.\n *\n * @remarks\n * If succeeds, returns the signed in user along with the provider's credential. If sign in was\n * unsuccessful, returns an error object containing additional information about the error.\n *\n * This method does not work in a Node.js environment or with {@link Auth} instances created with a\n * {@link @firebase/app#FirebaseServerApp}.\n *\n * @example\n * ```javascript\n * // Sign in using a popup.\n * const provider = new FacebookAuthProvider();\n * const result = await signInWithPopup(auth, provider);\n *\n * // The signed-in user info.\n * const user = result.user;\n * // This gives you a Facebook Access Token.\n * const credential = provider.credentialFromResult(auth, result);\n * const token = credential.accessToken;\n * ```\n *\n * @param auth - The {@link Auth} instance.\n * @param provider - The provider to authenticate. The provider has to be an {@link OAuthProvider}.\n * Non-OAuth providers like {@link EmailAuthProvider} will throw an error.\n * @param resolver - An instance of {@link PopupRedirectResolver}, optional\n * if already supplied to {@link initializeAuth} or provided by {@link getAuth}.\n *\n * @public\n */\nexport async function signInWithPopup(\n  auth: Auth,\n  provider: AuthProvider,\n  resolver?: PopupRedirectResolver\n): Promise<UserCredential> {\n  if (_isFirebaseServerApp(auth.app)) {\n    return Promise.reject(\n      _createError(auth, AuthErrorCode.OPERATION_NOT_SUPPORTED)\n    );\n  }\n  const authInternal = _castAuth(auth);\n  _assertInstanceOf(auth, provider, FederatedAuthProvider);\n  const resolverInternal = _withDefaultResolver(authInternal, resolver);\n  const action = new PopupOperation(\n    authInternal,\n    AuthEventType.SIGN_IN_VIA_POPUP,\n    provider,\n    resolverInternal\n  );\n  return action.executeNotNull();\n}\n\n/**\n * Reauthenticates the current user with the specified {@link OAuthProvider} using a pop-up based\n * OAuth flow.\n *\n * @remarks\n * If the reauthentication is successful, the returned result will contain the user and the\n * provider's credential.\n *\n * This method does not work in a Node.js environment or on any {@link User} signed in by\n * {@link Auth} instances created with a {@link @firebase/app#FirebaseServerApp}.\n *\n * @example\n * ```javascript\n * // Sign in using a popup.\n * const provider = new FacebookAuthProvider();\n * const result = await signInWithPopup(auth, provider);\n * // Reauthenticate using a popup.\n * await reauthenticateWithPopup(result.user, provider);\n * ```\n *\n * @param user - The user.\n * @param provider - The provider to authenticate. The provider has to be an {@link OAuthProvider}.\n * Non-OAuth providers like {@link EmailAuthProvider} will throw an error.\n * @param resolver - An instance of {@link PopupRedirectResolver}, optional\n * if already supplied to {@link initializeAuth} or provided by {@link getAuth}.\n *\n * @public\n */\nexport async function reauthenticateWithPopup(\n  user: User,\n  provider: AuthProvider,\n  resolver?: PopupRedirectResolver\n): Promise<UserCredential> {\n  const userInternal = getModularInstance(user) as UserInternal;\n  if (_isFirebaseServerApp(userInternal.auth.app)) {\n    return Promise.reject(\n      _createError(userInternal.auth, AuthErrorCode.OPERATION_NOT_SUPPORTED)\n    );\n  }\n  _assertInstanceOf(userInternal.auth, provider, FederatedAuthProvider);\n  const resolverInternal = _withDefaultResolver(userInternal.auth, resolver);\n  const action = new PopupOperation(\n    userInternal.auth,\n    AuthEventType.REAUTH_VIA_POPUP,\n    provider,\n    resolverInternal,\n    userInternal\n  );\n  return action.executeNotNull();\n}\n\n/**\n * Links the authenticated provider to the user account using a pop-up based OAuth flow.\n *\n * @remarks\n * If the linking is successful, the returned result will contain the user and the provider's credential.\n *\n * This method does not work in a Node.js environment.\n *\n * @example\n * ```javascript\n * // Sign in using some other provider.\n * const result = await signInWithEmailAndPassword(auth, email, password);\n * // Link using a popup.\n * const provider = new FacebookAuthProvider();\n * await linkWithPopup(result.user, provider);\n * ```\n *\n * @param user - The user.\n * @param provider - The provider to authenticate. The provider has to be an {@link OAuthProvider}.\n * Non-OAuth providers like {@link EmailAuthProvider} will throw an error.\n * @param resolver - An instance of {@link PopupRedirectResolver}, optional\n * if already supplied to {@link initializeAuth} or provided by {@link getAuth}.\n *\n * @public\n */\nexport async function linkWithPopup(\n  user: User,\n  provider: AuthProvider,\n  resolver?: PopupRedirectResolver\n): Promise<UserCredential> {\n  const userInternal = getModularInstance(user) as UserInternal;\n  _assertInstanceOf(userInternal.auth, provider, FederatedAuthProvider);\n  const resolverInternal = _withDefaultResolver(userInternal.auth, resolver);\n\n  const action = new PopupOperation(\n    userInternal.auth,\n    AuthEventType.LINK_VIA_POPUP,\n    provider,\n    resolverInternal,\n    userInternal\n  );\n  return action.executeNotNull();\n}\n\n/**\n * Popup event manager. Handles the popup's entire lifecycle; listens to auth\n * events\n *\n */\nclass PopupOperation extends AbstractPopupRedirectOperation {\n  // Only one popup is ever shown at once. The lifecycle of the current popup\n  // can be managed / cancelled by the constructor.\n  private static currentPopupAction: PopupOperation | null = null;\n  private authWindow: AuthPopup | null = null;\n  private pollId: number | null = null;\n\n  constructor(\n    auth: AuthInternal,\n    filter: AuthEventType,\n    private readonly provider: AuthProvider,\n    resolver: PopupRedirectResolverInternal,\n    user?: UserInternal\n  ) {\n    super(auth, filter, resolver, user);\n    if (PopupOperation.currentPopupAction) {\n      PopupOperation.currentPopupAction.cancel();\n    }\n\n    PopupOperation.currentPopupAction = this;\n  }\n\n  async executeNotNull(): Promise<UserCredential> {\n    const result = await this.execute();\n    _assert(result, this.auth, AuthErrorCode.INTERNAL_ERROR);\n    return result;\n  }\n\n  async onExecution(): Promise<void> {\n    debugAssert(\n      this.filter.length === 1,\n      'Popup operations only handle one event'\n    );\n    const eventId = _generateEventId();\n    this.authWindow = await this.resolver._openPopup(\n      this.auth,\n      this.provider,\n      this.filter[0], // There's always one, see constructor\n      eventId\n    );\n    this.authWindow.associatedEvent = eventId;\n\n    // Check for web storage support and origin validation _after_ the popup is\n    // loaded. These operations are slow (~1 second or so) Rather than\n    // waiting on them before opening the window, optimistically open the popup\n    // and check for storage support at the same time. If storage support is\n    // not available, this will cause the whole thing to reject properly. It\n    // will also close the popup, but since the promise has already rejected,\n    // the popup closed by user poll will reject into the void.\n    this.resolver._originValidation(this.auth).catch(e => {\n      this.reject(e);\n    });\n\n    this.resolver._isIframeWebStorageSupported(this.auth, isSupported => {\n      if (!isSupported) {\n        this.reject(\n          _createError(this.auth, AuthErrorCode.WEB_STORAGE_UNSUPPORTED)\n        );\n      }\n    });\n\n    // Handle user closure. Notice this does *not* use await\n    this.pollUserCancellation();\n  }\n\n  get eventId(): string | null {\n    return this.authWindow?.associatedEvent || null;\n  }\n\n  cancel(): void {\n    this.reject(_createError(this.auth, AuthErrorCode.EXPIRED_POPUP_REQUEST));\n  }\n\n  cleanUp(): void {\n    if (this.authWindow) {\n      this.authWindow.close();\n    }\n\n    if (this.pollId) {\n      window.clearTimeout(this.pollId);\n    }\n\n    this.authWindow = null;\n    this.pollId = null;\n    PopupOperation.currentPopupAction = null;\n  }\n\n  private pollUserCancellation(): void {\n    const poll = (): void => {\n      if (this.authWindow?.window?.closed) {\n        // Make sure that there is sufficient time for whatever action to\n        // complete. The window could have closed but the sign in network\n        // call could still be in flight. This is specifically true for\n        // Firefox or if the opener is in an iframe, in which case the oauth\n        // helper closes the popup.\n        this.pollId = window.setTimeout(() => {\n          this.pollId = null;\n          this.reject(\n            _createError(this.auth, AuthErrorCode.POPUP_CLOSED_BY_USER)\n          );\n        }, _Timeout.AUTH_EVENT);\n        return;\n      }\n\n      this.pollId = window.setTimeout(poll, _POLL_WINDOW_CLOSE_TIMEOUT.get());\n    };\n\n    poll();\n  }\n}\n", "/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { _getProjectConfig } from '../../api/project_config/get_project_config';\nimport { AuthInternal } from '../../model/auth';\nimport { AuthErrorCode } from '../errors';\nimport { _fail } from './assert';\nimport { _getCurrentUrl } from './location';\n\nconst IP_ADDRESS_REGEX = /^\\d{1,3}\\.\\d{1,3}\\.\\d{1,3}\\.\\d{1,3}$/;\nconst HTTP_REGEX = /^https?/;\n\nexport async function _validateOrigin(auth: AuthInternal): Promise<void> {\n  // Skip origin validation if we are in an emulated environment\n  if (auth.config.emulator) {\n    return;\n  }\n\n  const { authorizedDomains } = await _getProjectConfig(auth);\n\n  for (const domain of authorizedDomains) {\n    try {\n      if (matchDomain(domain)) {\n        return;\n      }\n    } catch {\n      // Do nothing if there's a URL error; just continue searching\n    }\n  }\n\n  // In the old SDK, this error also provides helpful messages.\n  _fail(auth, AuthErrorCode.INVALID_ORIGIN);\n}\n\nfunction matchDomain(expected: string): boolean {\n  const currentUrl = _getCurrentUrl();\n  const { protocol, hostname } = new URL(currentUrl);\n  if (expected.startsWith('chrome-extension://')) {\n    const ceUrl = new URL(expected);\n\n    if (ceUrl.hostname === '' && hostname === '') {\n      // For some reason we're not parsing chrome URLs properly\n      return (\n        protocol === 'chrome-extension:' &&\n        expected.replace('chrome-extension://', '') ===\n          currentUrl.replace('chrome-extension://', '')\n      );\n    }\n\n    return protocol === 'chrome-extension:' && ceUrl.hostname === hostname;\n  }\n\n  if (!HTTP_REGEX.test(protocol)) {\n    return false;\n  }\n\n  if (IP_ADDRESS_REGEX.test(expected)) {\n    // The domain has to be exactly equal to the pattern, as an IP domain will\n    // only contain the IP, no extra character.\n    return hostname === expected;\n  }\n\n  // Dots in pattern should be escaped.\n  const escapedDomainPattern = expected.replace(/\\./g, '\\\\.');\n  // Non ip address domains.\n  // domain.com = *.domain.com OR domain.com\n  const re = new RegExp(\n    '^(.+\\\\.' + escapedDomainPattern + '|' + escapedDomainPattern + ')$',\n    'i'\n  );\n  return re.test(hostname);\n}\n", "/**\n * @license\n * Copyright 2020 Google LLC.\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { AuthErrorCode } from '../../core/errors';\nimport { _createError } from '../../core/util/assert';\nimport { Delay } from '../../core/util/delay';\nimport { AuthInternal } from '../../model/auth';\nimport { _window } from '../auth_window';\nimport * as js from '../load_js';\n\nconst NETWORK_TIMEOUT = new Delay(30000, 60000);\n\n/**\n * Reset unloaded GApi modules. If gapi.load fails due to a network error,\n * it will stop working after a retrial. This is a hack to fix this issue.\n */\nfunction resetUnloadedGapiModules(): void {\n  // Clear last failed gapi.load state to force next gapi.load to first\n  // load the failed gapi.iframes module.\n  // Get gapix.beacon context.\n  const beacon = _window().___jsl;\n  // Get current hint.\n  if (beacon?.H) {\n    // Get gapi hint.\n    for (const hint of Object.keys(beacon.H)) {\n      // Requested modules.\n      beacon.H[hint].r = beacon.H[hint].r || [];\n      // Loaded modules.\n      beacon.H[hint].L = beacon.H[hint].L || [];\n      // Set requested modules to a copy of the loaded modules.\n      beacon.H[hint].r = [...beacon.H[hint].L];\n      // Clear pending callbacks.\n      if (beacon.CP) {\n        for (let i = 0; i < beacon.CP.length; i++) {\n          // Remove all failed pending callbacks.\n          beacon.CP[i] = null;\n        }\n      }\n    }\n  }\n}\n\nfunction loadGapi(auth: AuthInternal): Promise<gapi.iframes.Context> {\n  return new Promise<gapi.iframes.Context>((resolve, reject) => {\n    // Function to run when gapi.load is ready.\n    function loadGapiIframe(): void {\n      // The developer may have tried to previously run gapi.load and failed.\n      // Run this to fix that.\n      resetUnloadedGapiModules();\n      gapi.load('gapi.iframes', {\n        callback: () => {\n          resolve(gapi.iframes.getContext());\n        },\n        ontimeout: () => {\n          // The above reset may be sufficient, but having this reset after\n          // failure ensures that if the developer calls gapi.load after the\n          // connection is re-established and before another attempt to embed\n          // the iframe, it would work and would not be broken because of our\n          // failed attempt.\n          // Timeout when gapi.iframes.Iframe not loaded.\n          resetUnloadedGapiModules();\n          reject(_createError(auth, AuthErrorCode.NETWORK_REQUEST_FAILED));\n        },\n        timeout: NETWORK_TIMEOUT.get()\n      });\n    }\n\n    if (_window().gapi?.iframes?.Iframe) {\n      // If gapi.iframes.Iframe available, resolve.\n      resolve(gapi.iframes.getContext());\n    } else if (!!_window().gapi?.load) {\n      // Gapi loader ready, load gapi.iframes.\n      loadGapiIframe();\n    } else {\n      // Create a new iframe callback when this is called so as not to overwrite\n      // any previous defined callback. This happens if this method is called\n      // multiple times in parallel and could result in the later callback\n      // overwriting the previous one. This would end up with a iframe\n      // timeout.\n      const cbName = js._generateCallbackName('iframefcb');\n      // GApi loader not available, dynamically load platform.js.\n      _window()[cbName] = () => {\n        // GApi loader should be ready.\n        if (!!gapi.load) {\n          loadGapiIframe();\n        } else {\n          // Gapi loader failed, throw error.\n          reject(_createError(auth, AuthErrorCode.NETWORK_REQUEST_FAILED));\n        }\n      };\n      // Load GApi loader.\n      return js\n        ._loadJS(`${js._gapiScriptUrl()}?onload=${cbName}`)\n        .catch(e => reject(e));\n    }\n  }).catch(error => {\n    // Reset cached promise to allow for retrial.\n    cachedGApiLoader = null;\n    throw error;\n  });\n}\n\nlet cachedGApiLoader: Promise<gapi.iframes.Context> | null = null;\nexport function _loadGapi(auth: AuthInternal): Promise<gapi.iframes.Context> {\n  cachedGApiLoader = cachedGApiLoader || loadGapi(auth);\n  return cachedGApiLoader;\n}\n\nexport function _resetLoader(): void {\n  cachedGApiLoader = null;\n}\n", "/**\n * @license\n * Copyright 2020 Google LLC.\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { SDK_VERSION } from '@firebase/app';\nimport { querystring } from '@firebase/util';\nimport { DefaultConfig } from '../../../internal';\n\nimport { AuthErrorCode } from '../../core/errors';\nimport { _assert, _createError } from '../../core/util/assert';\nimport { Delay } from '../../core/util/delay';\nimport { _emulatorUrl } from '../../core/util/emulator';\nimport { AuthInternal } from '../../model/auth';\nimport { _window } from '../auth_window';\nimport * as gapiLoader from './gapi';\n\nconst PING_TIMEOUT = new Delay(5000, 15000);\nconst IFRAME_PATH = '__/auth/iframe';\nconst EMULATED_IFRAME_PATH = 'emulator/auth/iframe';\n\nconst IFRAME_ATTRIBUTES = {\n  style: {\n    position: 'absolute',\n    top: '-100px',\n    width: '1px',\n    height: '1px'\n  },\n  'aria-hidden': 'true',\n  tabindex: '-1'\n};\n\n// Map from apiHost to endpoint ID for passing into iframe. In current SDK, apiHost can be set to\n// anything (not from a list of endpoints with IDs as in legacy), so this is the closest we can get.\nconst EID_FROM_APIHOST = new Map([\n  [DefaultConfig.API_HOST, 'p'], // production\n  ['staging-identitytoolkit.sandbox.googleapis.com', 's'], // staging\n  ['test-identitytoolkit.sandbox.googleapis.com', 't'] // test\n]);\n\nfunction getIframeUrl(auth: AuthInternal): string {\n  const config = auth.config;\n  _assert(config.authDomain, auth, AuthErrorCode.MISSING_AUTH_DOMAIN);\n  const url = config.emulator\n    ? _emulatorUrl(config, EMULATED_IFRAME_PATH)\n    : `https://${auth.config.authDomain}/${IFRAME_PATH}`;\n\n  const params: Record<string, string> = {\n    apiKey: config.apiKey,\n    appName: auth.name,\n    v: SDK_VERSION\n  };\n  const eid = EID_FROM_APIHOST.get(auth.config.apiHost);\n  if (eid) {\n    params.eid = eid;\n  }\n  const frameworks = auth._getFrameworks();\n  if (frameworks.length) {\n    params.fw = frameworks.join(',');\n  }\n  return `${url}?${querystring(params).slice(1)}`;\n}\n\nexport async function _openIframe(\n  auth: AuthInternal\n): Promise<gapi.iframes.Iframe> {\n  const context = await gapiLoader._loadGapi(auth);\n  const gapi = _window().gapi;\n  _assert(gapi, auth, AuthErrorCode.INTERNAL_ERROR);\n  return context.open(\n    {\n      where: document.body,\n      url: getIframeUrl(auth),\n      messageHandlersFilter: gapi.iframes.CROSS_ORIGIN_IFRAMES_FILTER,\n      attributes: IFRAME_ATTRIBUTES,\n      dontclear: true\n    },\n    (iframe: gapi.iframes.Iframe) =>\n      new Promise(async (resolve, reject) => {\n        await iframe.restyle({\n          // Prevent iframe from closing on mouse out.\n          setHideOnLeave: false\n        });\n\n        const networkError = _createError(\n          auth,\n          AuthErrorCode.NETWORK_REQUEST_FAILED\n        );\n        // Confirm iframe is correctly loaded.\n        // To fallback on failure, set a timeout.\n        const networkErrorTimer = _window().setTimeout(() => {\n          reject(networkError);\n        }, PING_TIMEOUT.get());\n        // Clear timer and resolve pending iframe ready promise.\n        function clearTimerAndResolve(): void {\n          _window().clearTimeout(networkErrorTimer);\n          resolve(iframe);\n        }\n        // This returns an IThenable. However the reject part does not call\n        // when the iframe is not loaded.\n        iframe.ping(clearTimerAndResolve).then(clearTimerAndResolve, () => {\n          reject(networkError);\n        });\n      })\n  );\n}\n", "/**\n * @license\n * Copyright 2020 Google LLC.\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { getUA } from '@firebase/util';\n\nimport { AuthErrorCode } from '../../core/errors';\nimport { _assert } from '../../core/util/assert';\nimport {\n  _isChromeIOS,\n  _isFirefox,\n  _isIOSStandalone\n} from '../../core/util/browser';\nimport { AuthInternal } from '../../model/auth';\n\nconst BASE_POPUP_OPTIONS = {\n  location: 'yes',\n  resizable: 'yes',\n  statusbar: 'yes',\n  toolbar: 'no'\n};\n\nconst DEFAULT_WIDTH = 500;\nconst DEFAULT_HEIGHT = 600;\nconst TARGET_BLANK = '_blank';\n\nconst FIREFOX_EMPTY_URL = 'http://localhost';\n\nexport class AuthPopup {\n  associatedEvent: string | null = null;\n\n  constructor(readonly window: Window | null) {}\n\n  close(): void {\n    if (this.window) {\n      try {\n        this.window.close();\n      } catch (e) {}\n    }\n  }\n}\n\nexport function _open(\n  auth: AuthInternal,\n  url?: string,\n  name?: string,\n  width = DEFAULT_WIDTH,\n  height = DEFAULT_HEIGHT\n): AuthPopup {\n  const top = Math.max((window.screen.availHeight - height) / 2, 0).toString();\n  const left = Math.max((window.screen.availWidth - width) / 2, 0).toString();\n  let target = '';\n\n  const options: { [key: string]: string } = {\n    ...BASE_POPUP_OPTIONS,\n    width: width.toString(),\n    height: height.toString(),\n    top,\n    left\n  };\n\n  // Chrome iOS 7 and 8 is returning an undefined popup win when target is\n  // specified, even though the popup is not necessarily blocked.\n  const ua = getUA().toLowerCase();\n\n  if (name) {\n    target = _isChromeIOS(ua) ? TARGET_BLANK : name;\n  }\n\n  if (_isFirefox(ua)) {\n    // Firefox complains when invalid URLs are popped out. Hacky way to bypass.\n    url = url || FIREFOX_EMPTY_URL;\n    // Firefox disables by default scrolling on popup windows, which can create\n    // issues when the user has many Google accounts, for instance.\n    options.scrollbars = 'yes';\n  }\n\n  const optionsString = Object.entries(options).reduce(\n    (accum, [key, value]) => `${accum}${key}=${value},`,\n    ''\n  );\n\n  if (_isIOSStandalone(ua) && target !== '_self') {\n    openAsNewWindowIOS(url || '', target);\n    return new AuthPopup(null);\n  }\n\n  // about:blank getting sanitized causing browsers like IE/Edge to display\n  // brief error message before redirecting to handler.\n  const newWin = window.open(url || '', target, optionsString);\n  _assert(newWin, auth, AuthErrorCode.POPUP_BLOCKED);\n\n  // Flaky on IE edge, encapsulate with a try and catch.\n  try {\n    newWin.focus();\n  } catch (e) {}\n\n  return new AuthPopup(newWin);\n}\n\nfunction openAsNewWindowIOS(url: string, target: string): void {\n  const el = document.createElement('a');\n  el.href = url;\n  el.target = target;\n  const click = document.createEvent('MouseEvent');\n  click.initMouseEvent(\n    'click',\n    true,\n    true,\n    window,\n    1,\n    0,\n    0,\n    0,\n    0,\n    false,\n    false,\n    false,\n    false,\n    1,\n    null\n  );\n  el.dispatchEvent(click);\n}\n", "/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { AuthProvider, PopupRedirectResolver } from '../model/public_types';\n\nimport { AuthEventManager } from '../core/auth/auth_event_manager';\nimport { AuthErrorCode } from '../core/errors';\nimport { _assert, debugAssert, _fail } from '../core/util/assert';\nimport { _generateEventId } from '../core/util/event_id';\nimport { _getCurrentUrl } from '../core/util/location';\nimport { _validateOrigin } from '../core/util/validate_origin';\nimport { AuthInternal } from '../model/auth';\nimport {\n  AuthEventType,\n  EventManager,\n  GapiAuthEvent,\n  GapiOutcome,\n  PopupRedirectResolverInternal\n} from '../model/popup_redirect';\nimport { _setWindowLocation } from './auth_window';\nimport { _openIframe } from './iframe/iframe';\nimport { browserSessionPersistence } from './persistence/session_storage';\nimport { _open, AuthPopup } from './util/popup';\nimport { _getRedirectResult } from './strategies/redirect';\nimport { _getRedirectUrl } from '../core/util/handler';\nimport { _isIOS, _isMobileBrowser, _isSafari } from '../core/util/browser';\nimport { _overrideRedirectResult } from '../core/strategies/redirect';\n\n/**\n * The special web storage event\n *\n */\nconst WEB_STORAGE_SUPPORT_KEY = 'webStorageSupport';\n\ninterface WebStorageSupportMessage extends gapi.iframes.Message {\n  [index: number]: Record<string, boolean>;\n}\n\ninterface ManagerOrPromise {\n  manager?: EventManager;\n  promise?: Promise<EventManager>;\n}\n\nclass BrowserPopupRedirectResolver implements PopupRedirectResolverInternal {\n  private readonly eventManagers: Record<string, ManagerOrPromise> = {};\n  private readonly iframes: Record<string, gapi.iframes.Iframe> = {};\n  private readonly originValidationPromises: Record<string, Promise<void>> = {};\n\n  readonly _redirectPersistence = browserSessionPersistence;\n\n  // Wrapping in async even though we don't await anywhere in order\n  // to make sure errors are raised as promise rejections\n  async _openPopup(\n    auth: AuthInternal,\n    provider: AuthProvider,\n    authType: AuthEventType,\n    eventId?: string\n  ): Promise<AuthPopup> {\n    debugAssert(\n      this.eventManagers[auth._key()]?.manager,\n      '_initialize() not called before _openPopup()'\n    );\n\n    const url = await _getRedirectUrl(\n      auth,\n      provider,\n      authType,\n      _getCurrentUrl(),\n      eventId\n    );\n    return _open(auth, url, _generateEventId());\n  }\n\n  async _openRedirect(\n    auth: AuthInternal,\n    provider: AuthProvider,\n    authType: AuthEventType,\n    eventId?: string\n  ): Promise<never> {\n    await this._originValidation(auth);\n    const url = await _getRedirectUrl(\n      auth,\n      provider,\n      authType,\n      _getCurrentUrl(),\n      eventId\n    );\n    _setWindowLocation(url);\n    return new Promise(() => {});\n  }\n\n  _initialize(auth: AuthInternal): Promise<EventManager> {\n    const key = auth._key();\n    if (this.eventManagers[key]) {\n      const { manager, promise } = this.eventManagers[key];\n      if (manager) {\n        return Promise.resolve(manager);\n      } else {\n        debugAssert(promise, 'If manager is not set, promise should be');\n        return promise;\n      }\n    }\n\n    const promise = this.initAndGetManager(auth);\n    this.eventManagers[key] = { promise };\n\n    // If the promise is rejected, the key should be removed so that the\n    // operation can be retried later.\n    promise.catch(() => {\n      delete this.eventManagers[key];\n    });\n\n    return promise;\n  }\n\n  private async initAndGetManager(auth: AuthInternal): Promise<EventManager> {\n    const iframe = await _openIframe(auth);\n    const manager = new AuthEventManager(auth);\n    iframe.register<GapiAuthEvent>(\n      'authEvent',\n      (iframeEvent: GapiAuthEvent | null) => {\n        _assert(iframeEvent?.authEvent, auth, AuthErrorCode.INVALID_AUTH_EVENT);\n        // TODO: Consider splitting redirect and popup events earlier on\n\n        const handled = manager.onEvent(iframeEvent.authEvent);\n        return { status: handled ? GapiOutcome.ACK : GapiOutcome.ERROR };\n      },\n      gapi.iframes.CROSS_ORIGIN_IFRAMES_FILTER\n    );\n\n    this.eventManagers[auth._key()] = { manager };\n    this.iframes[auth._key()] = iframe;\n    return manager;\n  }\n\n  _isIframeWebStorageSupported(\n    auth: AuthInternal,\n    cb: (supported: boolean) => unknown\n  ): void {\n    const iframe = this.iframes[auth._key()];\n    iframe.send<gapi.iframes.Message, WebStorageSupportMessage>(\n      WEB_STORAGE_SUPPORT_KEY,\n      { type: WEB_STORAGE_SUPPORT_KEY },\n      result => {\n        const isSupported = result?.[0]?.[WEB_STORAGE_SUPPORT_KEY];\n        if (isSupported !== undefined) {\n          cb(!!isSupported);\n        }\n\n        _fail(auth, AuthErrorCode.INTERNAL_ERROR);\n      },\n      gapi.iframes.CROSS_ORIGIN_IFRAMES_FILTER\n    );\n  }\n\n  _originValidation(auth: AuthInternal): Promise<void> {\n    const key = auth._key();\n    if (!this.originValidationPromises[key]) {\n      this.originValidationPromises[key] = _validateOrigin(auth);\n    }\n\n    return this.originValidationPromises[key];\n  }\n\n  get _shouldInitProactively(): boolean {\n    // Mobile browsers and Safari need to optimistically initialize\n    return _isMobileBrowser() || _isSafari() || _isIOS();\n  }\n\n  _completeRedirectFn = _getRedirectResult;\n\n  _overrideRedirectResult = _overrideRedirectResult;\n}\n\n/**\n * An implementation of {@link PopupRedirectResolver} suitable for browser\n * based applications.\n *\n * @remarks\n * This method does not work in a Node.js environment.\n *\n * @public\n */\nexport const browserPopupRedirectResolver: PopupRedirectResolver =\n  BrowserPopupRedirectResolver;\n", "/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nimport { FactorId, MultiFactorAssertion } from '../model/public_types';\nimport { debugFail } from '../core/util/assert';\nimport { MultiFactorSessionImpl, MultiFactorSessionType } from './mfa_session';\nimport { FinalizeMfaResponse } from '../api/authentication/mfa';\nimport { AuthInternal } from '../model/auth';\n\nexport abstract class MultiFactorAssertionImpl implements MultiFactorAssertion {\n  protected constructor(readonly factorId: FactorId) {}\n\n  _process(\n    auth: AuthInternal,\n    session: MultiFactorSessionImpl,\n    displayName?: string | null\n  ): Promise<FinalizeMfaResponse> {\n    switch (session.type) {\n      case MultiFactorSessionType.ENROLL:\n        return this._finalizeEnroll(auth, session.credential, displayName);\n      case MultiFactorSessionType.SIGN_IN:\n        return this._finalizeSignIn(auth, session.credential);\n      default:\n        return debugFail('unexpected MultiFactorSessionType');\n    }\n  }\n\n  abstract _finalizeEnroll(\n    auth: AuthInternal,\n    idToken: string,\n    displayName?: string | null\n  ): Promise<FinalizeMfaResponse>;\n  abstract _finalizeSignIn(\n    auth: AuthInternal,\n    mfaPendingCredential: string\n  ): Promise<FinalizeMfaResponse>;\n}\n", "/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nimport {\n  FactorId,\n  PhoneMultiFactorAssertion\n} from '../../../model/public_types';\n\nimport { MultiFactorAssertionImpl } from '../../../mfa/mfa_assertion';\nimport { AuthInternal } from '../../../model/auth';\nimport { finalizeEnrollPhoneMfa } from '../../../api/account_management/mfa';\nimport { PhoneAuthCredential } from '../../../core/credentials/phone';\nimport {\n  finalizeSignInPhoneMfa,\n  FinalizeMfaResponse\n} from '../../../api/authentication/mfa';\n\n/**\n * {@inheritdoc PhoneMultiFactorAssertion}\n *\n * @public\n */\nexport class PhoneMultiFactorAssertionImpl\n  extends MultiFactorAssertionImpl\n  implements PhoneMultiFactorAssertion\n{\n  private constructor(private readonly credential: PhoneAuthCredential) {\n    super(FactorId.PHONE);\n  }\n\n  /** @internal */\n  static _fromCredential(\n    credential: PhoneAuthCredential\n  ): PhoneMultiFactorAssertionImpl {\n    return new PhoneMultiFactorAssertionImpl(credential);\n  }\n\n  /** @internal */\n  _finalizeEnroll(\n    auth: AuthInternal,\n    idToken: string,\n    displayName?: string | null\n  ): Promise<FinalizeMfaResponse> {\n    return finalizeEnrollPhoneMfa(auth, {\n      idToken,\n      displayName,\n      phoneVerificationInfo: this.credential._makeVerificationRequest()\n    });\n  }\n\n  /** @internal */\n  _finalizeSignIn(\n    auth: AuthInternal,\n    mfaPendingCredential: string\n  ): Promise<FinalizeMfaResponse> {\n    return finalizeSignInPhoneMfa(auth, {\n      mfaPendingCredential,\n      phoneVerificationInfo: this.credential._makeVerificationRequest()\n    });\n  }\n}\n\n/**\n * Provider for generating a {@link PhoneMultiFactorAssertion}.\n *\n * @public\n */\nexport class PhoneMultiFactorGenerator {\n  private constructor() {}\n\n  /**\n   * Provides a {@link PhoneMultiFactorAssertion} to confirm ownership of the phone second factor.\n   *\n   * @remarks\n   * This method does not work in a Node.js environment.\n   *\n   * @param phoneAuthCredential - A credential provided by {@link PhoneAuthProvider.credential}.\n   * @returns A {@link PhoneMultiFactorAssertion} which can be used with\n   * {@link MultiFactorResolver.resolveSignIn}\n   */\n  static assertion(credential: PhoneAuthCredential): PhoneMultiFactorAssertion {\n    return PhoneMultiFactorAssertionImpl._fromCredential(credential);\n  }\n\n  /**\n   * The identifier of the phone second factor: `phone`.\n   */\n  static FACTOR_ID = 'phone';\n}\n", "/**\n * @license\n * Copyright 2022 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nimport {\n  TotpMultiFactorAssertion,\n  MultiFactorSession,\n  FactorId\n} from '../../model/public_types';\nimport { AuthInternal } from '../../model/auth';\nimport {\n  finalizeEnrollTotpMfa,\n  startEnrollTotpMfa,\n  StartTotpMfaEnrollmentResponse,\n  TotpVerificationInfo\n} from '../../api/account_management/mfa';\nimport {\n  FinalizeMfaResponse,\n  finalizeSignInTotpMfa\n} from '../../api/authentication/mfa';\nimport { MultiFactorAssertionImpl } from '../../mfa/mfa_assertion';\nimport { MultiFactorSessionImpl } from '../mfa_session';\nimport { AuthErrorCode } from '../../core/errors';\nimport { _assert } from '../../core/util/assert';\n\n/**\n * Provider for generating a {@link TotpMultiFactorAssertion}.\n *\n * @public\n */\nexport class TotpMultiFactorGenerator {\n  /**\n   * Provides a {@link TotpMultiFactorAssertion} to confirm ownership of\n   * the TOTP (time-based one-time password) second factor.\n   * This assertion is used to complete enrollment in TOTP second factor.\n   *\n   * @param secret A {@link TotpSecret} containing the shared secret key and other TOTP parameters.\n   * @param oneTimePassword One-time password from TOTP App.\n   * @returns A {@link TotpMultiFactorAssertion} which can be used with\n   * {@link MultiFactorUser.enroll}.\n   */\n  static assertionForEnrollment(\n    secret: TotpSecret,\n    oneTimePassword: string\n  ): TotpMultiFactorAssertion {\n    return TotpMultiFactorAssertionImpl._fromSecret(secret, oneTimePassword);\n  }\n\n  /**\n   * Provides a {@link TotpMultiFactorAssertion} to confirm ownership of the TOTP second factor.\n   * This assertion is used to complete signIn with TOTP as the second factor.\n   *\n   * @param enrollmentId identifies the enrolled TOTP second factor.\n   * @param oneTimePassword One-time password from TOTP App.\n   * @returns A {@link TotpMultiFactorAssertion} which can be used with\n   * {@link MultiFactorResolver.resolveSignIn}.\n   */\n  static assertionForSignIn(\n    enrollmentId: string,\n    oneTimePassword: string\n  ): TotpMultiFactorAssertion {\n    return TotpMultiFactorAssertionImpl._fromEnrollmentId(\n      enrollmentId,\n      oneTimePassword\n    );\n  }\n\n  /**\n   * Returns a promise to {@link TotpSecret} which contains the TOTP shared secret key and other parameters.\n   * Creates a TOTP secret as part of enrolling a TOTP second factor.\n   * Used for generating a QR code URL or inputting into a TOTP app.\n   * This method uses the auth instance corresponding to the user in the multiFactorSession.\n   *\n   * @param session The {@link MultiFactorSession} that the user is part of.\n   * @returns A promise to {@link TotpSecret}.\n   */\n  static async generateSecret(\n    session: MultiFactorSession\n  ): Promise<TotpSecret> {\n    const mfaSession = session as MultiFactorSessionImpl;\n    _assert(\n      typeof mfaSession.user?.auth !== 'undefined',\n      AuthErrorCode.INTERNAL_ERROR\n    );\n    const response = await startEnrollTotpMfa(mfaSession.user.auth, {\n      idToken: mfaSession.credential,\n      totpEnrollmentInfo: {}\n    });\n    return TotpSecret._fromStartTotpMfaEnrollmentResponse(\n      response,\n      mfaSession.user.auth\n    );\n  }\n\n  /**\n   * The identifier of the TOTP second factor: `totp`.\n   */\n  static FACTOR_ID: 'totp' = FactorId.TOTP;\n}\n\nexport class TotpMultiFactorAssertionImpl\n  extends MultiFactorAssertionImpl\n  implements TotpMultiFactorAssertion\n{\n  constructor(\n    readonly otp: string,\n    readonly enrollmentId?: string,\n    readonly secret?: TotpSecret\n  ) {\n    super(FactorId.TOTP);\n  }\n\n  /** @internal */\n  static _fromSecret(\n    secret: TotpSecret,\n    otp: string\n  ): TotpMultiFactorAssertionImpl {\n    return new TotpMultiFactorAssertionImpl(otp, undefined, secret);\n  }\n\n  /** @internal */\n  static _fromEnrollmentId(\n    enrollmentId: string,\n    otp: string\n  ): TotpMultiFactorAssertionImpl {\n    return new TotpMultiFactorAssertionImpl(otp, enrollmentId);\n  }\n\n  /** @internal */\n  async _finalizeEnroll(\n    auth: AuthInternal,\n    idToken: string,\n    displayName?: string | null\n  ): Promise<FinalizeMfaResponse> {\n    _assert(\n      typeof this.secret !== 'undefined',\n      auth,\n      AuthErrorCode.ARGUMENT_ERROR\n    );\n    return finalizeEnrollTotpMfa(auth, {\n      idToken,\n      displayName,\n      totpVerificationInfo: this.secret._makeTotpVerificationInfo(this.otp)\n    });\n  }\n\n  /** @internal */\n  async _finalizeSignIn(\n    auth: AuthInternal,\n    mfaPendingCredential: string\n  ): Promise<FinalizeMfaResponse> {\n    _assert(\n      this.enrollmentId !== undefined && this.otp !== undefined,\n      auth,\n      AuthErrorCode.ARGUMENT_ERROR\n    );\n    const totpVerificationInfo = { verificationCode: this.otp };\n    return finalizeSignInTotpMfa(auth, {\n      mfaPendingCredential,\n      mfaEnrollmentId: this.enrollmentId,\n      totpVerificationInfo\n    });\n  }\n}\n\n/**\n * Provider for generating a {@link TotpMultiFactorAssertion}.\n *\n * Stores the shared secret key and other parameters to generate time-based OTPs.\n * Implements methods to retrieve the shared secret key and generate a QR code URL.\n * @public\n */\nexport class TotpSecret {\n  /**\n   * Shared secret key/seed used for enrolling in TOTP MFA and generating OTPs.\n   */\n  readonly secretKey: string;\n  /**\n   * Hashing algorithm used.\n   */\n  readonly hashingAlgorithm: string;\n  /**\n   * Length of the one-time passwords to be generated.\n   */\n  readonly codeLength: number;\n  /**\n   * The interval (in seconds) when the OTP codes should change.\n   */\n  readonly codeIntervalSeconds: number;\n  /**\n   * The timestamp (UTC string) by which TOTP enrollment should be completed.\n   */\n  // This can be used by callers to show a countdown of when to enter OTP code by.\n  readonly enrollmentCompletionDeadline: string;\n\n  // The public members are declared outside the constructor so the docs can be generated.\n  private constructor(\n    secretKey: string,\n    hashingAlgorithm: string,\n    codeLength: number,\n    codeIntervalSeconds: number,\n    enrollmentCompletionDeadline: string,\n    private readonly sessionInfo: string,\n    private readonly auth: AuthInternal\n  ) {\n    this.secretKey = secretKey;\n    this.hashingAlgorithm = hashingAlgorithm;\n    this.codeLength = codeLength;\n    this.codeIntervalSeconds = codeIntervalSeconds;\n    this.enrollmentCompletionDeadline = enrollmentCompletionDeadline;\n  }\n\n  /** @internal */\n  static _fromStartTotpMfaEnrollmentResponse(\n    response: StartTotpMfaEnrollmentResponse,\n    auth: AuthInternal\n  ): TotpSecret {\n    return new TotpSecret(\n      response.totpSessionInfo.sharedSecretKey,\n      response.totpSessionInfo.hashingAlgorithm,\n      response.totpSessionInfo.verificationCodeLength,\n      response.totpSessionInfo.periodSec,\n      new Date(response.totpSessionInfo.finalizeEnrollmentTime).toUTCString(),\n      response.totpSessionInfo.sessionInfo,\n      auth\n    );\n  }\n\n  /** @internal */\n  _makeTotpVerificationInfo(otp: string): TotpVerificationInfo {\n    return { sessionInfo: this.sessionInfo, verificationCode: otp };\n  }\n\n  /**\n   * Returns a QR code URL as described in\n   * https://github.com/google/google-authenticator/wiki/Key-Uri-Format\n   * This can be displayed to the user as a QR code to be scanned into a TOTP app like Google Authenticator.\n   * If the optional parameters are unspecified, an accountName of <userEmail> and issuer of <firebaseAppName> are used.\n   *\n   * @param accountName the name of the account/app along with a user identifier.\n   * @param issuer issuer of the TOTP (likely the app name).\n   * @returns A QR code URL string.\n   */\n  generateQrCodeUrl(accountName?: string, issuer?: string): string {\n    let useDefaults = false;\n    if (_isEmptyString(accountName) || _isEmptyString(issuer)) {\n      useDefaults = true;\n    }\n    if (useDefaults) {\n      if (_isEmptyString(accountName)) {\n        accountName = this.auth.currentUser?.email || 'unknownuser';\n      }\n      if (_isEmptyString(issuer)) {\n        issuer = this.auth.name;\n      }\n    }\n    return `otpauth://totp/${issuer}:${accountName}?secret=${this.secretKey}&issuer=${issuer}&algorithm=${this.hashingAlgorithm}&digits=${this.codeLength}`;\n  }\n}\n\n/** @internal */\nfunction _isEmptyString(input?: string): boolean {\n  return typeof input === 'undefined' || input?.length === 0;\n}\n", "/**\n * @license\n * Copyright 2021 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { FirebaseApp, getApp, _getProvider } from '@firebase/app';\n\nimport {\n  initializeAuth,\n  beforeAuthStateChanged,\n  onIdTokenChanged,\n  connectAuthEmulator\n} from '..';\nimport { registerAuth } from '../core/auth/register';\nimport { ClientPlatform } from '../core/util/version';\nimport { browserLocalPersistence } from './persistence/local_storage';\nimport { browserSessionPersistence } from './persistence/session_storage';\nimport { indexedDBLocalPersistence } from './persistence/indexed_db';\nimport { browserPopupRedirectResolver } from './popup_redirect';\nimport { Auth, User } from '../model/public_types';\nimport { getDefaultEmulatorHost, getExperimentalSetting } from '@firebase/util';\nimport { _setExternalJSProvider } from './load_js';\nimport { _createError } from '../core/util/assert';\nimport { AuthErrorCode } from '../core/errors';\n\nconst DEFAULT_ID_TOKEN_MAX_AGE = 5 * 60;\nconst authIdTokenMaxAge =\n  getExperimentalSetting('authIdTokenMaxAge') || DEFAULT_ID_TOKEN_MAX_AGE;\n\nlet lastPostedIdToken: string | undefined | null = null;\n\nconst mintCookieFactory = (url: string) => async (user: User | null) => {\n  const idTokenResult = user && (await user.getIdTokenResult());\n  const idTokenAge =\n    idTokenResult &&\n    (new Date().getTime() - Date.parse(idTokenResult.issuedAtTime)) / 1_000;\n  if (idTokenAge && idTokenAge > authIdTokenMaxAge) {\n    return;\n  }\n  // Specifically trip null => undefined when logged out, to delete any existing cookie\n  const idToken = idTokenResult?.token;\n  if (lastPostedIdToken === idToken) {\n    return;\n  }\n  lastPostedIdToken = idToken;\n  await fetch(url, {\n    method: idToken ? 'POST' : 'DELETE',\n    headers: idToken\n      ? {\n          'Authorization': `Bearer ${idToken}`\n        }\n      : {}\n  });\n};\n\n/**\n * Returns the Auth instance associated with the provided {@link @firebase/app#FirebaseApp}.\n * If no instance exists, initializes an Auth instance with platform-specific default dependencies.\n *\n * @param app - The Firebase App.\n *\n * @public\n */\nexport function getAuth(app: FirebaseApp = getApp()): Auth {\n  const provider = _getProvider(app, 'auth');\n\n  if (provider.isInitialized()) {\n    return provider.getImmediate();\n  }\n\n  const auth = initializeAuth(app, {\n    popupRedirectResolver: browserPopupRedirectResolver,\n    persistence: [\n      indexedDBLocalPersistence,\n      browserLocalPersistence,\n      browserSessionPersistence\n    ]\n  });\n\n  const authTokenSyncPath = getExperimentalSetting('authTokenSyncURL');\n  // Only do the Cookie exchange in a secure context\n  if (\n    authTokenSyncPath &&\n    typeof isSecureContext === 'boolean' &&\n    isSecureContext\n  ) {\n    // Don't allow urls (XSS possibility), only paths on the same domain\n    const authTokenSyncUrl = new URL(authTokenSyncPath, location.origin);\n    if (location.origin === authTokenSyncUrl.origin) {\n      const mintCookie = mintCookieFactory(authTokenSyncUrl.toString());\n      beforeAuthStateChanged(auth, mintCookie, () =>\n        mintCookie(auth.currentUser)\n      );\n      onIdTokenChanged(auth, user => mintCookie(user));\n    }\n  }\n\n  const authEmulatorHost = getDefaultEmulatorHost('auth');\n  if (authEmulatorHost) {\n    connectAuthEmulator(auth, `http://${authEmulatorHost}`);\n  }\n\n  return auth;\n}\n\nfunction getScriptParentElement(): HTMLDocument | HTMLHeadElement {\n  return document.getElementsByTagName('head')?.[0] ?? document;\n}\n\n_setExternalJSProvider({\n  loadJS(url: string): Promise<Event> {\n    // TODO: consider adding timeout support & cancellation\n    return new Promise((resolve, reject) => {\n      const el = document.createElement('script');\n      el.setAttribute('src', url);\n      el.onload = resolve;\n      el.onerror = e => {\n        const error = _createError(AuthErrorCode.INTERNAL_ERROR);\n        error.customData = e as unknown as Record<string, unknown>;\n        reject(error);\n      };\n      el.type = 'text/javascript';\n      el.charset = 'UTF-8';\n      getScriptParentElement().appendChild(el);\n    });\n  },\n\n  gapiScript: 'https://apis.google.com/js/api.js',\n  recaptchaV2Script: 'https://www.google.com/recaptcha/api.js',\n  recaptchaEnterpriseScript:\n    'https://www.google.com/recaptcha/enterprise.js?render='\n});\n\nregisterAuth(ClientPlatform.BROWSER);\n", "/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { _castAuth } from '../src/core/auth/auth_impl';\nimport { Auth } from '../src/model/public_types';\n\n/**\n * This interface is intended only for use by @firebase/auth-compat, do not use directly\n */\nexport * from '../index';\n\nexport { SignInWithIdpResponse } from '../src/api/authentication/idp';\nexport { AuthErrorCode } from '../src/core/errors';\nexport { PersistenceInternal } from '../src/core/persistence';\nexport { _persistenceKeyName } from '../src/core/persistence/persistence_user_manager';\nexport { UserImpl } from '../src/core/user/user_impl';\nexport { _getInstance } from '../src/core/util/instantiator';\nexport {\n  PopupRedirectResolverInternal,\n  EventManager,\n  AuthEventType\n} from '../src/model/popup_redirect';\nexport { UserCredentialInternal, UserParameters } from '../src/model/user';\nexport { AuthInternal, ConfigInternal } from '../src/model/auth';\nexport { DefaultConfig, AuthImpl, _castAuth } from '../src/core/auth/auth_impl';\n\nexport { ClientPlatform, _getClientVersion } from '../src/core/util/version';\n\nexport { _generateEventId } from '../src/core/util/event_id';\nexport { TaggedWithTokenResponse } from '../src/model/id_token';\nexport { _fail, _assert } from '../src/core/util/assert';\nexport { AuthPopup } from '../src/platform_browser/util/popup';\nexport { _getRedirectResult } from '../src/platform_browser/strategies/redirect';\nexport { _overrideRedirectResult } from '../src/core/strategies/redirect';\nexport { cordovaPopupRedirectResolver } from '../src/platform_cordova/popup_redirect/popup_redirect';\nexport { FetchProvider } from '../src/core/util/fetch_provider';\nexport { SAMLAuthCredential } from '../src/core/credentials/saml';\n\n// This function should only be called by frameworks (e.g. FirebaseUI-web) to log their usage.\n// It is not intended for direct use by developer apps. NO jsdoc here to intentionally leave it out\n// of autogenerated documentation pages to reduce accidental misuse.\nexport function addFrameworkForLogging(auth: Auth, framework: string): void {\n  _castAuth(auth)._logFramework(framework);\n}\n"], "names": ["jsHelpers._generateCallbackName", "jsHelpers._recaptchaV2ScriptUrl", "jsHelpers._loadJS", "js._generateCallbackName", "js\r\n                ._loadJS", "js._gapiScriptUrl", "gapiLoader._loadGapi"], "mappings": ";;;;;;;;AAAA;;;;;;;;;;;;;;;AAeG;AA6Ca,SAAA,mBAAmB,CACjC,IAAU,EACV,OAAmC,EAAA;IAEnC,OAAO,kBAAkB,CAIvB,IAAI,EAGJ,MAAA,wBAAA,8BAAA,mCAAA,kBAAkB,CAAC,IAAI,EAAE,OAAO,CAAC,CAClC,CAAC;AACJ,CAAC;AAsBe,SAAA,sBAAsB,CACpC,IAAU,EACV,OAAsC,EAAA;IAEtC,OAAO,kBAAkB,CAIvB,IAAI,EAGJ,MAAA,wBAAA,iCAAA,sCAAA,kBAAkB,CAAC,IAAI,EAAE,OAAO,CAAC,CAClC,CAAC;AACJ,CAAC;AAEe,SAAA,qBAAqB,CACnC,IAAU,EACV,OAAqC,EAAA;IAErC,OAAO,kBAAkB,CAIvB,IAAI,EAGJ,MAAA,wBAAA,iCAAA,sCAAA,kBAAkB,CAAC,IAAI,EAAE,OAAO,CAAC,CAClC,CAAC;AACJ;;AC3HA;;;;;;;;;;;;;;;AAeG;AAaI,IAAM,cAAc,GAAG,GAAG,CAAC;AAC3B,IAAM,mBAAmB,GAAG,KAAM,CAAC;AACnC,IAAM,gBAAgB,GAAG,aAAiB,CAAC;AAQlD,IAAA,aAAA,kBAAA,YAAA;AAIE,IAAA,SAAA,aAAA,CAA6B,IAAkB,EAAA;QAAlB,IAAI,CAAA,IAAA,GAAJ,IAAI,CAAc;QAHvC,IAAO,CAAA,OAAA,GAAG,gBAAgB,CAAC;AACnC,QAAA,IAAA,CAAA,QAAQ,GAAG,IAAI,GAAG,EAAkB,CAAC;KAEc;AAEnD,IAAA,aAAA,CAAA,SAAA,CAAA,MAAM,GAAN,UACE,SAA+B,EAC/B,UAAgC,EAAA;AAEhC,QAAA,IAAM,EAAE,GAAG,IAAI,CAAC,OAAO,CAAC;QACxB,IAAI,CAAC,QAAQ,CAAC,GAAG,CACf,EAAE,EACF,IAAI,UAAU,CAAC,SAAS,EAAE,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,UAAU,IAAI,EAAE,CAAC,CAC5D,CAAC;QACF,IAAI,CAAC,OAAO,EAAE,CAAC;AACf,QAAA,OAAO,EAAE,CAAC;KACX,CAAA;IAED,aAAK,CAAA,SAAA,CAAA,KAAA,GAAL,UAAM,WAAoB,EAAA;;AACxB,QAAA,IAAM,EAAE,GAAG,WAAW,IAAI,gBAAgB,CAAC;AAC3C,QAAA,MAAK,CAAA,EAAA,GAAA,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE,CAAC,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAE,MAAM,EAAE,CAAA,CAAC;AACrC,QAAA,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC;KAC1B,CAAA;IAED,aAAW,CAAA,SAAA,CAAA,WAAA,GAAX,UAAY,WAAoB,EAAA;;AAC9B,QAAA,IAAM,EAAE,GAAG,WAAW,IAAI,gBAAgB,CAAC;AAC3C,QAAA,OAAO,CAAA,CAAA,EAAA,GAAA,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE,CAAC,MAAE,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,WAAW,EAAE,KAAI,EAAE,CAAC;KACnD,CAAA;IAEK,aAAO,CAAA,SAAA,CAAA,OAAA,GAAb,UAAc,WAA6B,EAAA;;;;;AACnC,gBAAA,EAAE,GAAY,WAAsB,IAAI,gBAAgB,CAAC;AAC/D,gBAAA,MAAK,CAAA,EAAA,GAAA,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE,CAAC,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAE,OAAO,EAAE,CAAA,CAAC;AACtC,gBAAA,OAAA,CAAA,CAAA,aAAO,EAAE,CAAC,CAAA;;;AACX,KAAA,CAAA;IACH,OAAC,aAAA,CAAA;AAAD,CAAC,EAAA,CAAA,CAAA;AA6CD,IAAA,UAAA,kBAAA,YAAA;AAUE,IAAA,SAAA,UAAA,CACE,aAAmC,EACnC,OAAe,EACE,MAA2B,EAAA;QAH9C,IAkBC,KAAA,GAAA,IAAA,CAAA;QAfkB,IAAM,CAAA,MAAA,GAAN,MAAM,CAAqB;QAVtC,IAAO,CAAA,OAAA,GAAkB,IAAI,CAAC;QAC9B,IAAO,CAAA,OAAA,GAAG,KAAK,CAAC;QAChB,IAAa,CAAA,aAAA,GAAkB,IAAI,CAAC;AAC3B,QAAA,IAAA,CAAA,YAAY,GAAG,YAAA;YAC9B,KAAI,CAAC,OAAO,EAAE,CAAC;AACjB,SAAC,CAAC;AAOA,QAAA,IAAM,SAAS,GACb,OAAO,aAAa,KAAK,QAAQ;AAC/B,cAAE,QAAQ,CAAC,cAAc,CAAC,aAAa,CAAC;cACtC,aAAa,CAAC;QACpB,OAAO,CAAC,SAAS,EAAgC,gBAAA,qCAAA,EAAE,OAAO,EAAA,OAAA,EAAE,CAAC,CAAC;AAE9D,QAAA,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;QAC3B,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,MAAM,CAAC,IAAI,KAAK,WAAW,CAAC;QAClD,IAAI,IAAI,CAAC,SAAS,EAAE;YAClB,IAAI,CAAC,OAAO,EAAE,CAAC;AAChB,SAAA;AAAM,aAAA;YACL,IAAI,CAAC,SAAS,CAAC,gBAAgB,CAAC,OAAO,EAAE,IAAI,CAAC,YAAY,CAAC,CAAC;AAC7D,SAAA;KACF;AAED,IAAA,UAAA,CAAA,SAAA,CAAA,WAAW,GAAX,YAAA;QACE,IAAI,CAAC,cAAc,EAAE,CAAC;QACtB,OAAO,IAAI,CAAC,aAAa,CAAC;KAC3B,CAAA;AAED,IAAA,UAAA,CAAA,SAAA,CAAA,MAAM,GAAN,YAAA;QACE,IAAI,CAAC,cAAc,EAAE,CAAC;AACtB,QAAA,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;QACpB,IAAI,IAAI,CAAC,OAAO,EAAE;AAChB,YAAA,YAAY,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;AAC3B,YAAA,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;AACrB,SAAA;QACD,IAAI,CAAC,SAAS,CAAC,mBAAmB,CAAC,OAAO,EAAE,IAAI,CAAC,YAAY,CAAC,CAAC;KAChE,CAAA;AAED,IAAA,UAAA,CAAA,SAAA,CAAA,OAAO,GAAP,YAAA;QAAA,IA6BC,KAAA,GAAA,IAAA,CAAA;QA5BC,IAAI,CAAC,cAAc,EAAE,CAAC;QACtB,IAAI,IAAI,CAAC,OAAO,EAAE;YAChB,OAAO;AACR,SAAA;AAED,QAAA,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC,UAAU,CAAC,YAAA;AAC/B,YAAA,KAAI,CAAC,aAAa,GAAG,gCAAgC,CAAC,EAAE,CAAC,CAAC;YACpD,IAAA,EAAA,GAAoD,KAAI,CAAC,MAAM,EAA7D,QAAQ,GAAA,EAAA,CAAA,QAAA,EAAsB,eAAe,GAAA,EAAA,CAAA,kBAAA,CAAgB,CAAC;AACtE,YAAA,IAAI,QAAQ,EAAE;gBACZ,IAAI;AACF,oBAAA,QAAQ,CAAC,KAAI,CAAC,aAAa,CAAC,CAAC;AAC9B,iBAAA;gBAAC,OAAO,CAAC,EAAE,GAAE;AACf,aAAA;AAED,YAAA,KAAI,CAAC,OAAO,GAAG,MAAM,CAAC,UAAU,CAAC,YAAA;AAC/B,gBAAA,KAAI,CAAC,OAAO,GAAG,IAAI,CAAC;AACpB,gBAAA,KAAI,CAAC,aAAa,GAAG,IAAI,CAAC;AAC1B,gBAAA,IAAI,eAAe,EAAE;oBACnB,IAAI;AACF,wBAAA,eAAe,EAAE,CAAC;AACnB,qBAAA;oBAAC,OAAO,CAAC,EAAE,GAAE;AACf,iBAAA;gBAED,IAAI,KAAI,CAAC,SAAS,EAAE;oBAClB,KAAI,CAAC,OAAO,EAAE,CAAC;AAChB,iBAAA;aACF,EAAE,mBAAmB,CAAC,CAAC;SACzB,EAAE,cAAc,CAAC,CAAC;KACpB,CAAA;AAEO,IAAA,UAAA,CAAA,SAAA,CAAA,cAAc,GAAtB,YAAA;QACE,IAAI,IAAI,CAAC,OAAO,EAAE;AAChB,YAAA,MAAM,IAAI,KAAK,CAAC,qCAAqC,CAAC,CAAC;AACxD,SAAA;KACF,CAAA;IACH,OAAC,UAAA,CAAA;AAAD,CAAC,EAAA,CAAA,CAAA;AAED,SAAS,gCAAgC,CAAC,GAAW,EAAA;IACnD,IAAM,KAAK,GAAG,EAAE,CAAC;IACjB,IAAM,YAAY,GAChB,gEAAgE,CAAC;IACnE,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,EAAE,EAAE;QAC5B,KAAK,CAAC,IAAI,CACR,YAAY,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,YAAY,CAAC,MAAM,CAAC,CAAC,CACrE,CAAC;AACH,KAAA;AACD,IAAA,OAAO,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;AACxB;;ACnNA;;;;;;;;;;;;;;;AAeG;AAaH;AACA;AACO,IAAM,gBAAgB,GAAGA,qBAA+B,CAAC,KAAK,CAAC,CAAC;AACvE,IAAM,qBAAqB,GAAG,IAAI,KAAK,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;AAatD;;AAEG;AACH,IAAA,mBAAA,kBAAA,YAAA;AAAA,IAAA,SAAA,mBAAA,GAAA;;QACU,IAAY,CAAA,YAAA,GAAG,EAAE,CAAC;QAClB,IAAO,CAAA,OAAA,GAAG,CAAC,CAAC;AACpB;;;;AAIG;AACc,QAAA,IAAA,CAAA,uBAAuB,GAAG,CAAC,EAAC,CAAA,EAAA,GAAA,OAAO,EAAE,CAAC,UAAU,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAE,MAAM,CAAA,CAAC;KAqE3E;AAnEC,IAAA,mBAAA,CAAA,SAAA,CAAA,IAAI,GAAJ,UAAK,IAAkB,EAAE,EAAO,EAAA;QAAhC,IA8CC,KAAA,GAAA,IAAA,CAAA;AA9CwB,QAAA,IAAA,EAAA,KAAA,KAAA,CAAA,EAAA,EAAA,EAAO,GAAA,EAAA,CAAA,EAAA;QAC9B,OAAO,CAAC,mBAAmB,CAAC,EAAE,CAAC,EAAE,IAAI,sDAA+B,CAAC;AAErE,QAAA,IAAI,IAAI,CAAC,wBAAwB,CAAC,EAAE,CAAC,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC,UAAU,CAAC,EAAE;YACnE,OAAO,OAAO,CAAC,OAAO,CAAC,OAAO,EAAE,CAAC,UAAwB,CAAC,CAAC;AAC5D,SAAA;AACD,QAAA,OAAO,IAAI,OAAO,CAAY,UAAC,OAAO,EAAE,MAAM,EAAA;AAC5C,YAAA,IAAM,cAAc,GAAG,OAAO,EAAE,CAAC,UAAU,CAAC,YAAA;AAC1C,gBAAA,MAAM,CAAC,YAAY,CAAC,IAAI,EAAA,wBAAA,4CAAuC,CAAC,CAAC;AACnE,aAAC,EAAE,qBAAqB,CAAC,GAAG,EAAE,CAAC,CAAC;AAEhC,YAAA,OAAO,EAAE,CAAC,gBAAgB,CAAC,GAAG,YAAA;AAC5B,gBAAA,OAAO,EAAE,CAAC,YAAY,CAAC,cAAc,CAAC,CAAC;AACvC,gBAAA,OAAO,OAAO,EAAE,CAAC,gBAAgB,CAAC,CAAC;AAEnC,gBAAA,IAAM,SAAS,GAAG,OAAO,EAAE,CAAC,UAAuB,CAAC;gBAEpD,IAAI,CAAC,SAAS,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,EAAE;AAClC,oBAAA,MAAM,CAAC,YAAY,CAAC,IAAI,EAAA,gBAAA,oCAA+B,CAAC,CAAC;oBACzD,OAAO;AACR,iBAAA;;;AAID,gBAAA,IAAM,MAAM,GAAG,SAAS,CAAC,MAAM,CAAC;AAChC,gBAAA,SAAS,CAAC,MAAM,GAAG,UAAC,SAAS,EAAE,MAAM,EAAA;oBACnC,IAAM,QAAQ,GAAG,MAAM,CAAC,SAAS,EAAE,MAAM,CAAC,CAAC;oBAC3C,KAAI,CAAC,OAAO,EAAE,CAAC;AACf,oBAAA,OAAO,QAAQ,CAAC;AAClB,iBAAC,CAAC;AAEF,gBAAA,KAAI,CAAC,YAAY,GAAG,EAAE,CAAC;gBACvB,OAAO,CAAC,SAAS,CAAC,CAAC;AACrB,aAAC,CAAC;YAEF,IAAM,GAAG,GAAG,EAAG,CAAA,MAAA,CAAAC,qBAA+B,EAAE,EAAI,GAAA,CAAA,CAAA,MAAA,CAAA,WAAW,CAAC;AAC9D,gBAAA,MAAM,EAAE,gBAAgB;AACxB,gBAAA,MAAM,EAAE,UAAU;AAClB,gBAAA,EAAE,EAAA,EAAA;AACH,aAAA,CAAC,CAAE,CAAC;AAEL,YAAAC,OAAiB,CAAC,GAAG,CAAC,CAAC,KAAK,CAAC,YAAA;gBAC3B,YAAY,CAAC,cAAc,CAAC,CAAC;AAC7B,gBAAA,MAAM,CAAC,YAAY,CAAC,IAAI,EAAA,gBAAA,oCAA+B,CAAC,CAAC;AAC3D,aAAC,CAAC,CAAC;AACL,SAAC,CAAC,CAAC;KACJ,CAAA;AAED,IAAA,mBAAA,CAAA,SAAA,CAAA,kBAAkB,GAAlB,YAAA;QACE,IAAI,CAAC,OAAO,EAAE,CAAC;KAChB,CAAA;IAEO,mBAAwB,CAAA,SAAA,CAAA,wBAAA,GAAhC,UAAiC,EAAU,EAAA;;;;;;;;;QAQzC,QACE,CAAC,EAAC,CAAA,EAAA,GAAA,OAAO,EAAE,CAAC,UAAU,MAAE,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,MAAM,CAAA;AAC9B,aAAC,EAAE,KAAK,IAAI,CAAC,YAAY;gBACvB,IAAI,CAAC,OAAO,GAAG,CAAC;AAChB,gBAAA,IAAI,CAAC,uBAAuB,CAAC,EAC/B;KACH,CAAA;IACH,OAAC,mBAAA,CAAA;AAAD,CAAC,EAAA,CAAA,CAAA;AAED,SAAS,mBAAmB,CAAC,EAAU,EAAA;AACrC,IAAA,OAAO,EAAE,CAAC,MAAM,IAAI,CAAC,IAAI,wBAAwB,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;AAC7D,CAAC;AAED,IAAA,uBAAA,kBAAA,YAAA;AAAA,IAAA,SAAA,uBAAA,GAAA;KAMC;IALO,uBAAI,CAAA,SAAA,CAAA,IAAA,GAAV,UAAW,IAAkB,EAAA;;;AAC3B,gBAAA,OAAA,CAAA,CAAA,aAAO,IAAI,aAAa,CAAC,IAAI,CAAC,CAAC,CAAA;;;AAChC,KAAA,CAAA;IAED,uBAAkB,CAAA,SAAA,CAAA,kBAAA,GAAlB,eAA6B,CAAA;IAC/B,OAAC,uBAAA,CAAA;AAAD,CAAC,EAAA,CAAA;;ACxID;;;;;;;;;;;;;;;AAeG;AAmBI,IAAM,uBAAuB,GAAG,WAAW,CAAC;AAEnD,IAAM,cAAc,GAAwB;AAC1C,IAAA,KAAK,EAAE,OAAO;AACd,IAAA,IAAI,EAAE,OAAO;CACd,CAAC;AAIF;;;;;;;AAOG;AACH,IAAA,iBAAA,kBAAA,YAAA;AAoBE;;;;;;;;;;;;;;;;;;AAkBG;AACH,IAAA,SAAA,iBAAA,CACE,UAAgB,EAChB,aAAmC,EAClB,UAEhB,EAAA;QAFgB,IAAA,UAAA,KAAA,KAAA,CAAA,EAAA,EAAA,UACZ,GAAA,QAAA,CAAA,EAAA,EAAA,cAAc,CAClB,CAAA,EAAA;QAFgB,IAAU,CAAA,UAAA,GAAV,UAAU,CAE1B;AA3CH;;;;;AAKG;QACM,IAAI,CAAA,IAAA,GAAG,uBAAuB,CAAC;QAChC,IAAS,CAAA,SAAA,GAAG,KAAK,CAAC;QAClB,IAAQ,CAAA,QAAA,GAAkB,IAAI,CAAC;AAGtB,QAAA,IAAA,CAAA,oBAAoB,GAAG,IAAI,GAAG,EAAiB,CAAC;QACzD,IAAa,CAAA,aAAA,GAA2B,IAAI,CAAC;QAK7C,IAAS,CAAA,SAAA,GAAqB,IAAI,CAAC;AA4BzC,QAAA,IAAI,CAAC,IAAI,GAAG,SAAS,CAAC,UAAU,CAAC,CAAC;QAClC,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,UAAU,CAAC,IAAI,KAAK,WAAW,CAAC;QACxD,OAAO,CACL,OAAO,QAAQ,KAAK,WAAW,EAC/B,IAAI,CAAC,IAAI,EAAA,6CAAA,6CAEV,CAAC;AACF,QAAA,IAAM,SAAS,GACb,OAAO,aAAa,KAAK,QAAQ;AAC/B,cAAE,QAAQ,CAAC,cAAc,CAAC,aAAa,CAAC;cACtC,aAAa,CAAC;AACpB,QAAA,OAAO,CAAC,SAAS,EAAE,IAAI,CAAC,IAAI,sDAA+B,CAAC;AAE5D,QAAA,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;AAC3B,QAAA,IAAI,CAAC,UAAU,CAAC,QAAQ,GAAG,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC;QAE5E,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,iCAAiC;cACxE,IAAI,uBAAuB,EAAE;AAC/B,cAAE,IAAI,mBAAmB,EAAE,CAAC;QAE9B,IAAI,CAAC,qBAAqB,EAAE,CAAC;;KAE9B;AAED;;;;AAIG;AACG,IAAA,iBAAA,CAAA,SAAA,CAAA,MAAM,GAAZ,YAAA;;;;;;;wBACE,IAAI,CAAC,kBAAkB,EAAE,CAAC;AACf,wBAAA,OAAA,CAAA,CAAA,YAAM,IAAI,CAAC,MAAM,EAAE,CAAA,CAAA;;AAAxB,wBAAA,EAAE,GAAG,EAAmB,CAAA,IAAA,EAAA,CAAA;AACxB,wBAAA,SAAS,GAAG,IAAI,CAAC,oBAAoB,EAAE,CAAC;AAExC,wBAAA,QAAQ,GAAG,SAAS,CAAC,WAAW,CAAC,EAAE,CAAC,CAAC;AAC3C,wBAAA,IAAI,QAAQ,EAAE;AACZ,4BAAA,OAAA,CAAA,CAAA,aAAO,QAAQ,CAAC,CAAA;AACjB,yBAAA;AAED,wBAAA,OAAA,CAAA,CAAA,aAAO,IAAI,OAAO,CAAS,UAAA,OAAO,EAAA;gCAChC,IAAM,WAAW,GAAG,UAAC,KAAa,EAAA;oCAChC,IAAI,CAAC,KAAK,EAAE;AACV,wCAAA,OAAO;AACR,qCAAA;AACD,oCAAA,KAAI,CAAC,oBAAoB,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC;oCAC9C,OAAO,CAAC,KAAK,CAAC,CAAC;AACjB,iCAAC,CAAC;AAEF,gCAAA,KAAI,CAAC,oBAAoB,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC;gCAC3C,IAAI,KAAI,CAAC,WAAW,EAAE;AACpB,oCAAA,SAAS,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;AACvB,iCAAA;AACH,6BAAC,CAAC,CAAC,CAAA;;;;AACJ,KAAA,CAAA;AAED;;;;AAIG;AACH,IAAA,iBAAA,CAAA,SAAA,CAAA,MAAM,GAAN,YAAA;QAAA,IAoBC,KAAA,GAAA,IAAA,CAAA;QAnBC,IAAI;YACF,IAAI,CAAC,kBAAkB,EAAE,CAAC;AAC3B,SAAA;AAAC,QAAA,OAAO,CAAC,EAAE;;;;AAIV,YAAA,OAAO,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;AAC1B,SAAA;QAED,IAAI,IAAI,CAAC,aAAa,EAAE;YACtB,OAAO,IAAI,CAAC,aAAa,CAAC;AAC3B,SAAA;QAED,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,iBAAiB,EAAE,CAAC,KAAK,CAAC,UAAA,CAAC,EAAA;AACnD,YAAA,KAAI,CAAC,aAAa,GAAG,IAAI,CAAC;AAC1B,YAAA,MAAM,CAAC,CAAC;AACV,SAAC,CAAC,CAAC;QAEH,OAAO,IAAI,CAAC,aAAa,CAAC;KAC3B,CAAA;;AAGD,IAAA,iBAAA,CAAA,SAAA,CAAA,MAAM,GAAN,YAAA;QACE,IAAI,CAAC,kBAAkB,EAAE,CAAC;AAC1B,QAAA,IAAI,IAAI,CAAC,QAAQ,KAAK,IAAI,EAAE;YAC1B,IAAI,CAAC,oBAAoB,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;AAClD,SAAA;KACF,CAAA;AAED;;AAEG;AACH,IAAA,iBAAA,CAAA,SAAA,CAAA,KAAK,GAAL,YAAA;QAAA,IASC,KAAA,GAAA,IAAA,CAAA;QARC,IAAI,CAAC,kBAAkB,EAAE,CAAC;AAC1B,QAAA,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;AACtB,QAAA,IAAI,CAAC,gBAAgB,CAAC,kBAAkB,EAAE,CAAC;AAC3C,QAAA,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE;YACrB,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC,OAAO,CAAC,UAAA,IAAI,EAAA;AACpC,gBAAA,KAAI,CAAC,SAAS,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;AACnC,aAAC,CAAC,CAAC;AACJ,SAAA;KACF,CAAA;AAEO,IAAA,iBAAA,CAAA,SAAA,CAAA,qBAAqB,GAA7B,YAAA;AACE,QAAA,OAAO,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,OAAO,EAAE,IAAI,CAAC,IAAI,EAAA,gBAAA,oCAA+B,CAAC;AAC3E,QAAA,OAAO,CACL,IAAI,CAAC,WAAW,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,aAAa,EAAE,EACnD,IAAI,CAAC,IAAI,sDAEV,CAAC;QACF,OAAO,CACL,OAAO,QAAQ,KAAK,WAAW,EAC/B,IAAI,CAAC,IAAI,EAAA,6CAAA,6CAEV,CAAC;KACH,CAAA;IAEO,iBAAiB,CAAA,SAAA,CAAA,iBAAA,GAAzB,UACE,QAA4C,EAAA;QAD9C,IAcC,KAAA,GAAA,IAAA,CAAA;AAXC,QAAA,OAAO,UAAA,KAAK,EAAA;AACV,YAAA,KAAI,CAAC,oBAAoB,CAAC,OAAO,CAAC,UAAA,QAAQ,EAAA,EAAI,OAAA,QAAQ,CAAC,KAAK,CAAC,CAAf,EAAe,CAAC,CAAC;AAC/D,YAAA,IAAI,OAAO,QAAQ,KAAK,UAAU,EAAE;gBAClC,QAAQ,CAAC,KAAK,CAAC,CAAC;AACjB,aAAA;AAAM,iBAAA,IAAI,OAAO,QAAQ,KAAK,QAAQ,EAAE;AACvC,gBAAA,IAAM,UAAU,GAAG,OAAO,EAAE,CAAC,QAAQ,CAAC,CAAC;AACvC,gBAAA,IAAI,OAAO,UAAU,KAAK,UAAU,EAAE;oBACpC,UAAU,CAAC,KAAK,CAAC,CAAC;AACnB,iBAAA;AACF,aAAA;AACH,SAAC,CAAC;KACH,CAAA;AAEO,IAAA,iBAAA,CAAA,SAAA,CAAA,kBAAkB,GAA1B,YAAA;QACE,OAAO,CAAC,CAAC,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,IAAI,EAAA,gBAAA,oCAA+B,CAAC;KACnE,CAAA;AAEa,IAAA,iBAAA,CAAA,SAAA,CAAA,iBAAiB,GAA/B,YAAA;;;;;AACE,oBAAA,KAAA,CAAA,EAAA,OAAA,CAAA,CAAA,YAAM,IAAI,CAAC,IAAI,EAAE,CAAA,CAAA;;AAAjB,wBAAA,EAAA,CAAA,IAAA,EAAiB,CAAC;AAClB,wBAAA,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;AACd,4BAAA,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC;AAC/B,4BAAA,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE;AACf,gCAAA,eAAe,GAAG,QAAQ,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;AACtD,gCAAA,SAAS,CAAC,WAAW,CAAC,eAAe,CAAC,CAAC;gCACvC,SAAS,GAAG,eAAe,CAAC;AAC7B,6BAAA;AAED,4BAAA,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,oBAAoB,EAAE,CAAC,MAAM,CAChD,SAAS,EACT,IAAI,CAAC,UAAU,CAChB,CAAC;AACH,yBAAA;wBAED,OAAO,CAAA,CAAA,aAAA,IAAI,CAAC,QAAQ,CAAC,CAAA;;;;AACtB,KAAA,CAAA;AAEa,IAAA,iBAAA,CAAA,SAAA,CAAA,IAAI,GAAlB,YAAA;;;;;;AACE,wBAAA,OAAO,CACL,cAAc,EAAE,IAAI,CAAC,SAAS,EAAE,EAChC,IAAI,CAAC,IAAI,EAAA,gBAAA,oCAEV,CAAC;wBAEF,OAAM,CAAA,CAAA,YAAA,QAAQ,EAAE,CAAA,CAAA;;AAAhB,wBAAA,EAAA,CAAA,IAAA,EAAgB,CAAC;AACjB,wBAAA,EAAA,GAAA,IAAI,CAAA;AAAa,wBAAA,OAAA,CAAA,CAAA,YAAM,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAC/C,IAAI,CAAC,IAAI,EACT,IAAI,CAAC,IAAI,CAAC,YAAY,IAAI,SAAS,CACpC,CAAA,CAAA;;wBAHD,EAAK,CAAA,SAAS,GAAG,EAAA,CAAA,IAAA,EAGhB,CAAC;AAEc,wBAAA,OAAA,CAAA,CAAA,YAAM,kBAAkB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA,CAAA;;AAA7C,wBAAA,OAAO,GAAG,EAAmC,CAAA,IAAA,EAAA,CAAA;AACnD,wBAAA,OAAO,CAAC,OAAO,EAAE,IAAI,CAAC,IAAI,sDAA+B,CAAC;AAC1D,wBAAA,IAAI,CAAC,UAAU,CAAC,OAAO,GAAG,OAAO,CAAC;;;;;AACnC,KAAA,CAAA;AAEO,IAAA,iBAAA,CAAA,SAAA,CAAA,oBAAoB,GAA5B,YAAA;QACE,OAAO,CAAC,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,IAAI,EAAA,gBAAA,oCAA+B,CAAC;QACjE,OAAO,IAAI,CAAC,SAAS,CAAC;KACvB,CAAA;IACH,OAAC,iBAAA,CAAA;AAAD,CAAC,EAAA,EAAA;AAED,SAAS,QAAQ,GAAA;IACf,IAAI,QAAQ,GAAwB,IAAI,CAAC;AACzC,IAAA,OAAO,IAAI,OAAO,CAAO,UAAA,OAAO,EAAA;AAC9B,QAAA,IAAI,QAAQ,CAAC,UAAU,KAAK,UAAU,EAAE;AACtC,YAAA,OAAO,EAAE,CAAC;YACV,OAAO;AACR,SAAA;;;;AAKD,QAAA,QAAQ,GAAG,YAAM,EAAA,OAAA,OAAO,EAAE,CAAA,EAAA,CAAC;AAC3B,QAAA,MAAM,CAAC,gBAAgB,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC;AAC5C,KAAC,CAAC,CAAC,KAAK,CAAC,UAAA,CAAC,EAAA;AACR,QAAA,IAAI,QAAQ,EAAE;AACZ,YAAA,MAAM,CAAC,mBAAmB,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC;AAC9C,SAAA;AAED,QAAA,MAAM,CAAC,CAAC;AACV,KAAC,CAAC,CAAC;AACL;;AC1SA;;;;;;;;;;;;;;;AAeG;AA2CH,IAAA,sBAAA,kBAAA,YAAA;IACE,SACW,sBAAA,CAAA,cAAsB,EACd,cAAsC,EAAA;QAD9C,IAAc,CAAA,cAAA,GAAd,cAAc,CAAQ;QACd,IAAc,CAAA,cAAA,GAAd,cAAc,CAAwB;KACrD;IAEJ,sBAAO,CAAA,SAAA,CAAA,OAAA,GAAP,UAAQ,gBAAwB,EAAA;AAC9B,QAAA,IAAM,cAAc,GAAG,mBAAmB,CAAC,iBAAiB,CAC1D,IAAI,CAAC,cAAc,EACnB,gBAAgB,CACjB,CAAC;AACF,QAAA,OAAO,IAAI,CAAC,cAAc,CAAC,cAAc,CAAC,CAAC;KAC5C,CAAA;IACH,OAAC,sBAAA,CAAA;AAAD,CAAC,EAAA,CAAA,CAAA;AAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAgCG;SACmB,qBAAqB,CACzC,IAAU,EACV,WAAmB,EACnB,WAAgC,EAAA;;;;;;AAEhC,oBAAA,IAAI,oBAAoB,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE;wBAClC,OAAO,CAAA,CAAA,aAAA,OAAO,CAAC,MAAM,CACnB,+CAA+C,CAAC,IAAI,CAAC,CACtD,CAAC,CAAA;AACH,qBAAA;AACK,oBAAA,YAAY,GAAG,SAAS,CAAC,IAAI,CAAC,CAAC;oBACd,OAAM,CAAA,CAAA,YAAA,kBAAkB,CAC7C,YAAY,EACZ,WAAW,EACX,kBAAkB,CAAC,WAA0C,CAAC,CAC/D,CAAA,CAAA;;AAJK,oBAAA,cAAc,GAAG,EAItB,CAAA,IAAA,EAAA,CAAA;AACD,oBAAA,OAAA,CAAA,CAAA,aAAO,IAAI,sBAAsB,CAAC,cAAc,EAAE,UAAA,IAAI,EAAA;AACpD,4BAAA,OAAA,oBAAoB,CAAC,YAAY,EAAE,IAAI,CAAC,CAAA;AAAxC,yBAAwC,CACzC,CAAC,CAAA;;;;AACH,CAAA;AAED;;;;;;;;;;;AAWG;SACmB,mBAAmB,CACvC,IAAU,EACV,WAAmB,EACnB,WAAgC,EAAA;;;;;;AAE1B,oBAAA,YAAY,GAAG,kBAAkB,CAAC,IAAI,CAAiB,CAAC;AAC9D,oBAAA,OAAA,CAAA,CAAA,YAAM,mBAAmB,CAAC,KAAK,EAAE,YAAY,iCAAmB,CAAA,CAAA;;AAAhE,oBAAA,EAAA,CAAA,IAAA,EAAgE,CAAC;AAC1C,oBAAA,OAAA,CAAA,CAAA,YAAM,kBAAkB,CAC7C,YAAY,CAAC,IAAI,EACjB,WAAW,EACX,kBAAkB,CAAC,WAA0C,CAAC,CAC/D,CAAA,CAAA;;AAJK,oBAAA,cAAc,GAAG,EAItB,CAAA,IAAA,EAAA,CAAA;AACD,oBAAA,OAAA,CAAA,CAAA,aAAO,IAAI,sBAAsB,CAAC,cAAc,EAAE,UAAA,IAAI,EAAA;AACpD,4BAAA,OAAA,kBAAkB,CAAC,YAAY,EAAE,IAAI,CAAC,CAAA;AAAtC,yBAAsC,CACvC,CAAC,CAAA;;;;AACH,CAAA;AAED;;;;;;;;;;;;;;AAcG;SACmB,6BAA6B,CACjD,IAAU,EACV,WAAmB,EACnB,WAAgC,EAAA;;;;;;AAE1B,oBAAA,YAAY,GAAG,kBAAkB,CAAC,IAAI,CAAiB,CAAC;oBAC9D,IAAI,oBAAoB,CAAC,YAAY,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE;wBAC/C,OAAO,CAAA,CAAA,aAAA,OAAO,CAAC,MAAM,CACnB,+CAA+C,CAAC,YAAY,CAAC,IAAI,CAAC,CACnE,CAAC,CAAA;AACH,qBAAA;AACsB,oBAAA,OAAA,CAAA,CAAA,YAAM,kBAAkB,CAC7C,YAAY,CAAC,IAAI,EACjB,WAAW,EACX,kBAAkB,CAAC,WAA0C,CAAC,CAC/D,CAAA,CAAA;;AAJK,oBAAA,cAAc,GAAG,EAItB,CAAA,IAAA,EAAA,CAAA;AACD,oBAAA,OAAA,CAAA,CAAA,aAAO,IAAI,sBAAsB,CAAC,cAAc,EAAE,UAAA,IAAI,EAAA;AACpD,4BAAA,OAAA,4BAA4B,CAAC,YAAY,EAAE,IAAI,CAAC,CAAA;AAAhD,yBAAgD,CACjD,CAAC,CAAA;;;;AACH,CAAA;AAED;;;AAGG;SACmB,kBAAkB,CACtC,IAAkB,EAClB,OAAkC,EAClC,QAAqC,EAAA;;;;;;AAEd,gBAAA,KAAA,CAAA,EAAA,OAAA,CAAA,CAAA,YAAM,QAAQ,CAAC,MAAM,EAAE,CAAA,CAAA;;AAAxC,oBAAA,cAAc,GAAG,EAAuB,CAAA,IAAA,EAAA,CAAA;;;;oBAG5C,OAAO,CACL,OAAO,cAAc,KAAK,QAAQ,EAClC,IAAI,sDAEL,CAAC;oBACF,OAAO,CACL,QAAQ,CAAC,IAAI,KAAK,uBAAuB,EACzC,IAAI,EAAA,gBAAA,oCAEL,CAAC;AAEE,oBAAA,gBAAgB,SAAkB,CAAC;AAEvC,oBAAA,IAAI,OAAO,OAAO,KAAK,QAAQ,EAAE;AAC/B,wBAAA,gBAAgB,GAAG;AACjB,4BAAA,WAAW,EAAE,OAAO;yBACrB,CAAC;AACH,qBAAA;AAAM,yBAAA;wBACL,gBAAgB,GAAG,OAAO,CAAC;AAC5B,qBAAA;AAEG,oBAAA,IAAA,EAAA,SAAS,IAAI,gBAAgB,CAAA,EAA7B,OAA6B,CAAA,CAAA,YAAA,CAAA,CAAA,CAAA;AACzB,oBAAA,OAAO,GAAG,gBAAgB,CAAC,OAAiC,CAAC;AAE/D,oBAAA,IAAA,EAAA,aAAa,IAAI,gBAAgB,CAAA,EAAjC,OAAiC,CAAA,CAAA,YAAA,CAAA,CAAA,CAAA;oBACnC,OAAO,CACL,OAAO,CAAC,IAAI,mDACZ,IAAI,sDAEL,CAAC;oBACe,OAAM,CAAA,CAAA,YAAA,mBAAmB,CAAC,IAAI,EAAE;4BAC/C,OAAO,EAAE,OAAO,CAAC,UAAU;AAC3B,4BAAA,mBAAmB,EAAE;gCACnB,WAAW,EAAE,gBAAgB,CAAC,WAAW;AACzC,gCAAA,cAAc,EAAA,cAAA;AACf,6BAAA;AACF,yBAAA,CAAC,CAAA,CAAA;;AANI,oBAAA,QAAQ,GAAG,EAMf,CAAA,IAAA,EAAA,CAAA;AACF,oBAAA,OAAA,CAAA,CAAA,aAAO,QAAQ,CAAC,gBAAgB,CAAC,WAAW,CAAC,CAAA;;oBAE7C,OAAO,CACL,OAAO,CAAC,IAAI,oDACZ,IAAI,sDAEL,CAAC;AACI,oBAAA,eAAe,GACnB,CAAA,CAAA,EAAA,GAAA,gBAAgB,CAAC,eAAe,0CAAE,GAAG;wBACrC,gBAAgB,CAAC,cAAc,CAAC;AAClC,oBAAA,OAAO,CAAC,eAAe,EAAE,IAAI,mEAAiC,CAAC;oBAC9C,OAAM,CAAA,CAAA,YAAA,mBAAmB,CAAC,IAAI,EAAE;4BAC/C,oBAAoB,EAAE,OAAO,CAAC,UAAU;AACxC,4BAAA,eAAe,EAAA,eAAA;AACf,4BAAA,eAAe,EAAE;AACf,gCAAA,cAAc,EAAA,cAAA;AACf,6BAAA;AACF,yBAAA,CAAC,CAAA,CAAA;;AANI,oBAAA,QAAQ,GAAG,EAMf,CAAA,IAAA,EAAA,CAAA;AACF,oBAAA,OAAA,CAAA,CAAA,aAAO,QAAQ,CAAC,iBAAiB,CAAC,WAAW,CAAC,CAAA;;wBAGxB,OAAM,CAAA,CAAA,YAAA,yBAAyB,CAAC,IAAI,EAAE;wBAC5D,WAAW,EAAE,gBAAgB,CAAC,WAAW;AACzC,wBAAA,cAAc,EAAA,cAAA;AACf,qBAAA,CAAC,CAAA,CAAA;;AAHM,oBAAA,WAAW,GAAK,CAAA,EAGtB,CAAA,IAAA,EAAA,EAHiB,WAAA,CAAA;AAInB,oBAAA,OAAA,CAAA,CAAA,aAAO,WAAW,CAAC,CAAA;;;oBAGrB,QAAQ,CAAC,MAAM,EAAE,CAAC;;;;;;AAErB,CAAA;AAED;;;;;;;;;;;;;;;;;;;;;;AAsBG;AACmB,SAAA,iBAAiB,CACrC,IAAU,EACV,UAA+B,EAAA;;;;;;AAEzB,oBAAA,YAAY,GAAG,kBAAkB,CAAC,IAAI,CAAiB,CAAC;oBAC9D,IAAI,oBAAoB,CAAC,YAAY,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE;wBAC/C,OAAO,CAAA,CAAA,aAAA,OAAO,CAAC,MAAM,CACnB,+CAA+C,CAAC,YAAY,CAAC,IAAI,CAAC,CACnE,CAAC,CAAA;AACH,qBAAA;AACD,oBAAA,OAAA,CAAA,CAAA,YAAM,KAAK,CAAC,YAAY,EAAE,UAAU,CAAC,CAAA,CAAA;;AAArC,oBAAA,EAAA,CAAA,IAAA,EAAqC,CAAC;;;;;AACvC;;ACnTD;;;;;;;;;;;;;;;AAeG;AAqBH;;;;;;;;;;;;;;;;;;AAkBG;AACH,IAAA,iBAAA,kBAAA,YAAA;AAUE;;;AAGG;AACH,IAAA,SAAA,iBAAA,CAAY,IAAU,EAAA;;AAPb,QAAA,IAAA,CAAA,UAAU,GAAG,iBAAiB,CAAC,WAAW,CAAC;AAQlD,QAAA,IAAI,CAAC,IAAI,GAAG,SAAS,CAAC,IAAI,CAAC,CAAC;KAC7B;AAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA8BG;AACH,IAAA,iBAAA,CAAA,SAAA,CAAA,iBAAiB,GAAjB,UACE,YAAuC,EACvC,mBAAwC,EAAA;AAExC,QAAA,OAAO,kBAAkB,CACvB,IAAI,CAAC,IAAI,EACT,YAAY,EACZ,kBAAkB,CAAC,mBAAkD,CAAC,CACvE,CAAC;KACH,CAAA;AAED;;;;;;;;;;;;;;;;;;;;;;;;;;AA0BG;AACI,IAAA,iBAAA,CAAA,UAAU,GAAjB,UACE,cAAsB,EACtB,gBAAwB,EAAA;QAExB,OAAO,mBAAmB,CAAC,iBAAiB,CAC1C,cAAc,EACd,gBAAgB,CACjB,CAAC;KACH,CAAA;AAED;;;AAGG;IACI,iBAAoB,CAAA,oBAAA,GAA3B,UACE,cAA8B,EAAA;QAE9B,IAAM,UAAU,GAAG,cAAwC,CAAC;AAC5D,QAAA,OAAO,iBAAiB,CAAC,0BAA0B,CAAC,UAAU,CAAC,CAAC;KACjE,CAAA;AAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA+BG;IACI,iBAAmB,CAAA,mBAAA,GAA1B,UAA2B,KAAoB,EAAA;AAC7C,QAAA,OAAO,iBAAiB,CAAC,0BAA0B,EAChD,KAAK,CAAC,UAAU,IAAI,EAAE,EACxB,CAAC;KACH,CAAA;IAEc,iBAA0B,CAAA,0BAAA,GAAzC,UAA0C,EAEhB,EAAA;AADR,QAAA,IAAA,aAAa,GAAA,EAAA,CAAA,cAAA,CAAA;QAE7B,IAAI,CAAC,aAAa,EAAE;AAClB,YAAA,OAAO,IAAI,CAAC;AACb,SAAA;QACK,IAAA,EAAA,GACJ,aAA8C,EADxC,WAAW,iBAAA,EAAE,cAAc,oBACa,CAAC;QACjD,IAAI,WAAW,IAAI,cAAc,EAAE;YACjC,OAAO,mBAAmB,CAAC,kBAAkB,CAC3C,WAAW,EACX,cAAc,CACf,CAAC;AACH,SAAA;AACD,QAAA,OAAO,IAAI,CAAC;KACb,CAAA;;AA/Je,IAAA,iBAAA,CAAA,WAAW,GAA6B,OAAA,wBAAA;;AAExC,IAAA,iBAAA,CAAA,oBAAoB,GAA+B,OAAA,0BAAA;IA8JrE,OAAC,iBAAA,CAAA;AAAA,CAlKD,EAkKC;;ACzND;;;;;;;;;;;;;;;AAeG;AA0CI,IAAM,0BAA0B,GAAG,IAAI,KAAK,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;AAEjE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA8BG;SACmB,eAAe,CACnC,IAAU,EACV,QAAsB,EACtB,QAAgC,EAAA;;;;AAEhC,YAAA,IAAI,oBAAoB,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE;gBAClC,OAAO,CAAA,CAAA,aAAA,OAAO,CAAC,MAAM,CACnB,YAAY,CAAC,IAAI,EAAwC,6CAAA,6CAAA,CAC1D,CAAC,CAAA;AACH,aAAA;AACK,YAAA,YAAY,GAAG,SAAS,CAAC,IAAI,CAAC,CAAC;AACrC,YAAA,iBAAiB,CAAC,IAAI,EAAE,QAAQ,EAAE,qBAAqB,CAAC,CAAC;AACnD,YAAA,gBAAgB,GAAG,oBAAoB,CAAC,YAAY,EAAE,QAAQ,CAAC,CAAC;YAChE,MAAM,GAAG,IAAI,cAAc,CAC/B,YAAY,0DAEZ,QAAQ,EACR,gBAAgB,CACjB,CAAC;AACF,YAAA,OAAA,CAAA,CAAA,aAAO,MAAM,CAAC,cAAc,EAAE,CAAC,CAAA;;;AAChC,CAAA;AAED;;;;;;;;;;;;;;;;;;;;;;;;;;;AA2BG;SACmB,uBAAuB,CAC3C,IAAU,EACV,QAAsB,EACtB,QAAgC,EAAA;;;;AAE1B,YAAA,YAAY,GAAG,kBAAkB,CAAC,IAAI,CAAiB,CAAC;YAC9D,IAAI,oBAAoB,CAAC,YAAY,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE;gBAC/C,OAAO,CAAA,CAAA,aAAA,OAAO,CAAC,MAAM,CACnB,YAAY,CAAC,YAAY,CAAC,IAAI,EAAwC,6CAAA,6CAAA,CACvE,CAAC,CAAA;AACH,aAAA;YACD,iBAAiB,CAAC,YAAY,CAAC,IAAI,EAAE,QAAQ,EAAE,qBAAqB,CAAC,CAAC;YAChE,gBAAgB,GAAG,oBAAoB,CAAC,YAAY,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;AACrE,YAAA,MAAM,GAAG,IAAI,cAAc,CAC/B,YAAY,CAAC,IAAI,EAEjB,gBAAA,uCAAA,QAAQ,EACR,gBAAgB,EAChB,YAAY,CACb,CAAC;AACF,YAAA,OAAA,CAAA,CAAA,aAAO,MAAM,CAAC,cAAc,EAAE,CAAC,CAAA;;;AAChC,CAAA;AAED;;;;;;;;;;;;;;;;;;;;;;;;AAwBG;SACmB,aAAa,CACjC,IAAU,EACV,QAAsB,EACtB,QAAgC,EAAA;;;;AAE1B,YAAA,YAAY,GAAG,kBAAkB,CAAC,IAAI,CAAiB,CAAC;YAC9D,iBAAiB,CAAC,YAAY,CAAC,IAAI,EAAE,QAAQ,EAAE,qBAAqB,CAAC,CAAC;YAChE,gBAAgB,GAAG,oBAAoB,CAAC,YAAY,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;AAErE,YAAA,MAAM,GAAG,IAAI,cAAc,CAC/B,YAAY,CAAC,IAAI,EAEjB,cAAA,qCAAA,QAAQ,EACR,gBAAgB,EAChB,YAAY,CACb,CAAC;AACF,YAAA,OAAA,CAAA,CAAA,aAAO,MAAM,CAAC,cAAc,EAAE,CAAC,CAAA;;;AAChC,CAAA;AAED;;;;AAIG;AACH,IAAA,cAAA,kBAAA,UAAA,MAAA,EAAA;IAA6B,SAA8B,CAAA,cAAA,EAAA,MAAA,CAAA,CAAA;IAOzD,SACE,cAAA,CAAA,IAAkB,EAClB,MAAqB,EACJ,QAAsB,EACvC,QAAuC,EACvC,IAAmB,EAAA;QALrB,IAOE,KAAA,GAAA,MAAA,CAAA,IAAA,CAAA,IAAA,EAAM,IAAI,EAAE,MAAM,EAAE,QAAQ,EAAE,IAAI,CAAC,IAMpC,IAAA,CAAA;QAVkB,KAAQ,CAAA,QAAA,GAAR,QAAQ,CAAc;QANjC,KAAU,CAAA,UAAA,GAAqB,IAAI,CAAC;QACpC,KAAM,CAAA,MAAA,GAAkB,IAAI,CAAC;QAUnC,IAAI,cAAc,CAAC,kBAAkB,EAAE;AACrC,YAAA,cAAc,CAAC,kBAAkB,CAAC,MAAM,EAAE,CAAC;AAC5C,SAAA;AAED,QAAA,cAAc,CAAC,kBAAkB,GAAG,KAAI,CAAC;;KAC1C;AAEK,IAAA,cAAA,CAAA,SAAA,CAAA,cAAc,GAApB,YAAA;;;;;AACiB,oBAAA,KAAA,CAAA,EAAA,OAAA,CAAA,CAAA,YAAM,IAAI,CAAC,OAAO,EAAE,CAAA,CAAA;;AAA7B,wBAAA,MAAM,GAAG,EAAoB,CAAA,IAAA,EAAA,CAAA;AACnC,wBAAA,OAAO,CAAC,MAAM,EAAE,IAAI,CAAC,IAAI,sDAA+B,CAAC;AACzD,wBAAA,OAAA,CAAA,CAAA,aAAO,MAAM,CAAC,CAAA;;;;AACf,KAAA,CAAA;AAEK,IAAA,cAAA,CAAA,SAAA,CAAA,WAAW,GAAjB,YAAA;;;;;;;wBACE,WAAW,CACT,IAAI,CAAC,MAAM,CAAC,MAAM,KAAK,CAAC,EACxB,wCAAwC,CACzC,CAAC;wBACI,OAAO,GAAG,gBAAgB,EAAE,CAAC;AACnC,wBAAA,EAAA,GAAA,IAAI,CAAA;wBAAc,OAAM,CAAA,CAAA,YAAA,IAAI,CAAC,QAAQ,CAAC,UAAU,CAC9C,IAAI,CAAC,IAAI,EACT,IAAI,CAAC,QAAQ,EACb,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC;AACd,4BAAA,OAAO,CACR,CAAA,CAAA;;wBALD,EAAK,CAAA,UAAU,GAAG,EAAA,CAAA,IAAA,EAKjB,CAAC;AACF,wBAAA,IAAI,CAAC,UAAU,CAAC,eAAe,GAAG,OAAO,CAAC;;;;;;;;AAS1C,wBAAA,IAAI,CAAC,QAAQ,CAAC,iBAAiB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,KAAK,CAAC,UAAA,CAAC,EAAA;AAChD,4BAAA,KAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;AACjB,yBAAC,CAAC,CAAC;wBAEH,IAAI,CAAC,QAAQ,CAAC,4BAA4B,CAAC,IAAI,CAAC,IAAI,EAAE,UAAA,WAAW,EAAA;4BAC/D,IAAI,CAAC,WAAW,EAAE;gCAChB,KAAI,CAAC,MAAM,CACT,YAAY,CAAC,KAAI,CAAC,IAAI,EAAwC,yBAAA,6CAAA,CAC/D,CAAC;AACH,6BAAA;AACH,yBAAC,CAAC,CAAC;;wBAGH,IAAI,CAAC,oBAAoB,EAAE,CAAC;;;;;AAC7B,KAAA,CAAA;AAED,IAAA,MAAA,CAAA,cAAA,CAAI,cAAO,CAAA,SAAA,EAAA,SAAA,EAAA;AAAX,QAAA,GAAA,EAAA,YAAA;;YACE,OAAO,CAAA,MAAA,IAAI,CAAC,UAAU,MAAE,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,eAAe,KAAI,IAAI,CAAC;SACjD;;;AAAA,KAAA,CAAA,CAAA;AAED,IAAA,cAAA,CAAA,SAAA,CAAA,MAAM,GAAN,YAAA;QACE,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,IAAI,CAAC,IAAI,EAAsC,yBAAA,2CAAA,CAAC,CAAC;KAC3E,CAAA;AAED,IAAA,cAAA,CAAA,SAAA,CAAA,OAAO,GAAP,YAAA;QACE,IAAI,IAAI,CAAC,UAAU,EAAE;AACnB,YAAA,IAAI,CAAC,UAAU,CAAC,KAAK,EAAE,CAAC;AACzB,SAAA;QAED,IAAI,IAAI,CAAC,MAAM,EAAE;AACf,YAAA,MAAM,CAAC,YAAY,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;AAClC,SAAA;AAED,QAAA,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;AACvB,QAAA,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC;AACnB,QAAA,cAAc,CAAC,kBAAkB,GAAG,IAAI,CAAC;KAC1C,CAAA;AAEO,IAAA,cAAA,CAAA,SAAA,CAAA,oBAAoB,GAA5B,YAAA;QAAA,IAqBC,KAAA,GAAA,IAAA,CAAA;AApBC,QAAA,IAAM,IAAI,GAAG,YAAA;;YACX,IAAI,CAAA,EAAA,GAAA,MAAA,KAAI,CAAC,UAAU,MAAE,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,MAAM,MAAE,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,MAAM,EAAE;;;;;;AAMnC,gBAAA,KAAI,CAAC,MAAM,GAAG,MAAM,CAAC,UAAU,CAAC,YAAA;AAC9B,oBAAA,KAAI,CAAC,MAAM,GAAG,IAAI,CAAC;oBACnB,KAAI,CAAC,MAAM,CACT,YAAY,CAAC,KAAI,CAAC,IAAI,EAAqC,sBAAA,0CAAA,CAC5D,CAAC;AACJ,iBAAC,iCAAsB,CAAC;gBACxB,OAAO;AACR,aAAA;AAED,YAAA,KAAI,CAAC,MAAM,GAAG,MAAM,CAAC,UAAU,CAAC,IAAI,EAAE,0BAA0B,CAAC,GAAG,EAAE,CAAC,CAAC;AAC1E,SAAC,CAAC;AAEF,QAAA,IAAI,EAAE,CAAC;KACR,CAAA;;;IAzGc,cAAkB,CAAA,kBAAA,GAA0B,IAAI,CAAC;IA0GlE,OAAC,cAAA,CAAA;CAAA,CA7G4B,8BAA8B,CA6G1D,CAAA;;ACjUD;;;;;;;;;;;;;;;AAeG;AAQH,IAAM,gBAAgB,GAAG,sCAAsC,CAAC;AAChE,IAAM,UAAU,GAAG,SAAS,CAAC;AAEvB,SAAgB,eAAe,CAAC,IAAkB,EAAA;;;;;;;AAEtD,oBAAA,IAAI,IAAI,CAAC,MAAM,CAAC,QAAQ,EAAE;wBACxB,OAAO,CAAA,CAAA,YAAA,CAAA;AACR,qBAAA;AAE6B,oBAAA,OAAA,CAAA,CAAA,YAAM,iBAAiB,CAAC,IAAI,CAAC,CAAA,CAAA;;AAAnD,oBAAA,iBAAiB,GAAK,CAAA,EAA6B,CAAA,IAAA,EAAA,EAAlC,iBAAA,CAAA;AAEzB,oBAAA,KAAA,EAAA,GAAA,CAAsC,EAAjB,mBAAiB,GAAA,iBAAA,EAAjB,EAAiB,GAAA,mBAAA,CAAA,MAAA,EAAjB,IAAiB,EAAE;wBAA7B,MAAM,GAAA,mBAAA,CAAA,EAAA,CAAA,CAAA;wBACf,IAAI;AACF,4BAAA,IAAI,WAAW,CAAC,MAAM,CAAC,EAAE;gCACvB,OAAO,CAAA,CAAA,YAAA,CAAA;AACR,6BAAA;AACF,yBAAA;wBAAC,OAAM,EAAA,EAAA;;AAEP,yBAAA;AACF,qBAAA;;oBAGD,KAAK,CAAC,IAAI,EAAA,qBAAA,oCAA+B,CAAC;;;;;AAC3C,CAAA;AAED,SAAS,WAAW,CAAC,QAAgB,EAAA;AACnC,IAAA,IAAM,UAAU,GAAG,cAAc,EAAE,CAAC;AAC9B,IAAA,IAAA,EAAyB,GAAA,IAAI,GAAG,CAAC,UAAU,CAAC,EAA1C,QAAQ,GAAA,EAAA,CAAA,QAAA,EAAE,QAAQ,GAAA,EAAA,CAAA,QAAwB,CAAC;AACnD,IAAA,IAAI,QAAQ,CAAC,UAAU,CAAC,qBAAqB,CAAC,EAAE;AAC9C,QAAA,IAAM,KAAK,GAAG,IAAI,GAAG,CAAC,QAAQ,CAAC,CAAC;QAEhC,IAAI,KAAK,CAAC,QAAQ,KAAK,EAAE,IAAI,QAAQ,KAAK,EAAE,EAAE;;YAE5C,QACE,QAAQ,KAAK,mBAAmB;AAChC,gBAAA,QAAQ,CAAC,OAAO,CAAC,qBAAqB,EAAE,EAAE,CAAC;oBACzC,UAAU,CAAC,OAAO,CAAC,qBAAqB,EAAE,EAAE,CAAC,EAC/C;AACH,SAAA;QAED,OAAO,QAAQ,KAAK,mBAAmB,IAAI,KAAK,CAAC,QAAQ,KAAK,QAAQ,CAAC;AACxE,KAAA;AAED,IAAA,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE;AAC9B,QAAA,OAAO,KAAK,CAAC;AACd,KAAA;AAED,IAAA,IAAI,gBAAgB,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE;;;QAGnC,OAAO,QAAQ,KAAK,QAAQ,CAAC;AAC9B,KAAA;;IAGD,IAAM,oBAAoB,GAAG,QAAQ,CAAC,OAAO,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;;;AAG5D,IAAA,IAAM,EAAE,GAAG,IAAI,MAAM,CACnB,SAAS,GAAG,oBAAoB,GAAG,GAAG,GAAG,oBAAoB,GAAG,IAAI,EACpE,GAAG,CACJ,CAAC;AACF,IAAA,OAAO,EAAE,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;AAC3B;;ACrFA;;;;;;;;;;;;;;;AAeG;AASH,IAAM,eAAe,GAAG,IAAI,KAAK,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;AAEhD;;;AAGG;AACH,SAAS,wBAAwB,GAAA;;;;AAI/B,IAAA,IAAM,MAAM,GAAG,OAAO,EAAE,CAAC,MAAM,CAAC;;AAEhC,IAAA,IAAI,MAAM,KAAN,IAAA,IAAA,MAAM,uBAAN,MAAM,CAAE,CAAC,EAAE;;AAEb,QAAA,KAAmB,IAAqB,EAAA,GAAA,CAAA,EAArB,EAAA,GAAA,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,EAArB,EAAqB,GAAA,EAAA,CAAA,MAAA,EAArB,IAAqB,EAAE;AAArC,YAAA,IAAM,IAAI,GAAA,EAAA,CAAA,EAAA,CAAA,CAAA;;AAEb,YAAA,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC;;AAE1C,YAAA,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC;;AAE1C,YAAA,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,GAAA,aAAA,CAAA,EAAA,EAAO,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,OAAC,CAAC;;YAEzC,IAAI,MAAM,CAAC,EAAE,EAAE;AACb,gBAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,CAAC,EAAE,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;;AAEzC,oBAAA,MAAM,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC;AACrB,iBAAA;AACF,aAAA;AACF,SAAA;AACF,KAAA;AACH,CAAC;AAED,SAAS,QAAQ,CAAC,IAAkB,EAAA;AAClC,IAAA,OAAO,IAAI,OAAO,CAAuB,UAAC,OAAO,EAAE,MAAM,EAAA;;;AAEvD,QAAA,SAAS,cAAc,GAAA;;;AAGrB,YAAA,wBAAwB,EAAE,CAAC;AAC3B,YAAA,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE;AACxB,gBAAA,QAAQ,EAAE,YAAA;oBACR,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,UAAU,EAAE,CAAC,CAAC;iBACpC;AACD,gBAAA,SAAS,EAAE,YAAA;;;;;;;AAOT,oBAAA,wBAAwB,EAAE,CAAC;AAC3B,oBAAA,MAAM,CAAC,YAAY,CAAC,IAAI,EAAA,wBAAA,4CAAuC,CAAC,CAAC;iBAClE;AACD,gBAAA,OAAO,EAAE,eAAe,CAAC,GAAG,EAAE;AAC/B,aAAA,CAAC,CAAC;SACJ;QAED,IAAI,CAAA,EAAA,GAAA,CAAA,EAAA,GAAA,OAAO,EAAE,CAAC,IAAI,MAAE,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,OAAO,MAAE,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,MAAM,EAAE;;YAEnC,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,UAAU,EAAE,CAAC,CAAC;AACpC,SAAA;aAAM,IAAI,CAAC,EAAC,CAAA,EAAA,GAAA,OAAO,EAAE,CAAC,IAAI,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAE,IAAI,CAAA,EAAE;;AAEjC,YAAA,cAAc,EAAE,CAAC;AAClB,SAAA;AAAM,aAAA;;;;;;YAML,IAAM,MAAM,GAAGC,qBAAwB,CAAC,WAAW,CAAC,CAAC;;AAErD,YAAA,OAAO,EAAE,CAAC,MAAM,CAAC,GAAG,YAAA;;AAElB,gBAAA,IAAI,CAAC,CAAC,IAAI,CAAC,IAAI,EAAE;AACf,oBAAA,cAAc,EAAE,CAAC;AAClB,iBAAA;AAAM,qBAAA;;AAEL,oBAAA,MAAM,CAAC,YAAY,CAAC,IAAI,EAAA,wBAAA,4CAAuC,CAAC,CAAC;AAClE,iBAAA;AACH,aAAC,CAAC;;AAEF,YAAA,OAAOC,OACG,CAAC,UAAGC,cAAiB,EAAE,EAAA,UAAA,CAAA,CAAA,MAAA,CAAW,MAAM,CAAE,CAAC;AAClD,iBAAA,KAAK,CAAC,UAAA,CAAC,EAAA,EAAI,OAAA,MAAM,CAAC,CAAC,CAAC,CAAA,EAAA,CAAC,CAAC;AAC1B,SAAA;AACH,KAAC,CAAC,CAAC,KAAK,CAAC,UAAA,KAAK,EAAA;;QAEZ,gBAAgB,GAAG,IAAI,CAAC;AACxB,QAAA,MAAM,KAAK,CAAC;AACd,KAAC,CAAC,CAAC;AACL,CAAC;AAED,IAAI,gBAAgB,GAAyC,IAAI,CAAC;AAC5D,SAAU,SAAS,CAAC,IAAkB,EAAA;AAC1C,IAAA,gBAAgB,GAAG,gBAAgB,IAAI,QAAQ,CAAC,IAAI,CAAC,CAAC;AACtD,IAAA,OAAO,gBAAgB,CAAC;AAC1B;;ACxHA;;;;;;;;;;;;;;;AAeG;AAcH,IAAM,YAAY,GAAG,IAAI,KAAK,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;AAC5C,IAAM,WAAW,GAAG,gBAAgB,CAAC;AACrC,IAAM,oBAAoB,GAAG,sBAAsB,CAAC;AAEpD,IAAM,iBAAiB,GAAG;AACxB,IAAA,KAAK,EAAE;AACL,QAAA,QAAQ,EAAE,UAAU;AACpB,QAAA,GAAG,EAAE,QAAQ;AACb,QAAA,KAAK,EAAE,KAAK;AACZ,QAAA,MAAM,EAAE,KAAK;AACd,KAAA;AACD,IAAA,aAAa,EAAE,MAAM;AACrB,IAAA,QAAQ,EAAE,IAAI;CACf,CAAC;AAEF;AACA;AACA,IAAM,gBAAgB,GAAG,IAAI,GAAG,CAAC;AAC/B,IAAA,CAAA,gCAAA,+BAAyB,GAAG,CAAC;IAC7B,CAAC,gDAAgD,EAAE,GAAG,CAAC;AACvD,IAAA,CAAC,6CAA6C,EAAE,GAAG,CAAC;AACrD,CAAA,CAAC,CAAC;AAEH,SAAS,YAAY,CAAC,IAAkB,EAAA;AACtC,IAAA,IAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;AAC3B,IAAA,OAAO,CAAC,MAAM,CAAC,UAAU,EAAE,IAAI,wEAAoC,CAAC;AACpE,IAAA,IAAM,GAAG,GAAG,MAAM,CAAC,QAAQ;AACzB,UAAE,YAAY,CAAC,MAAM,EAAE,oBAAoB,CAAC;UAC1C,UAAW,CAAA,MAAA,CAAA,IAAI,CAAC,MAAM,CAAC,UAAU,EAAA,GAAA,CAAA,CAAA,MAAA,CAAI,WAAW,CAAE,CAAC;AAEvD,IAAA,IAAM,MAAM,GAA2B;QACrC,MAAM,EAAE,MAAM,CAAC,MAAM;QACrB,OAAO,EAAE,IAAI,CAAC,IAAI;AAClB,QAAA,CAAC,EAAE,WAAW;KACf,CAAC;AACF,IAAA,IAAM,GAAG,GAAG,gBAAgB,CAAC,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;AACtD,IAAA,IAAI,GAAG,EAAE;AACP,QAAA,MAAM,CAAC,GAAG,GAAG,GAAG,CAAC;AAClB,KAAA;AACD,IAAA,IAAM,UAAU,GAAG,IAAI,CAAC,cAAc,EAAE,CAAC;IACzC,IAAI,UAAU,CAAC,MAAM,EAAE;QACrB,MAAM,CAAC,EAAE,GAAG,UAAU,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;AAClC,KAAA;AACD,IAAA,OAAO,EAAG,CAAA,MAAA,CAAA,GAAG,EAAI,GAAA,CAAA,CAAA,MAAA,CAAA,WAAW,CAAC,MAAM,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAE,CAAC;AAClD,CAAC;AAEK,SAAgB,WAAW,CAC/B,IAAkB,EAAA;;;;;;AAEF,gBAAA,KAAA,CAAA,EAAA,OAAA,CAAA,CAAA,YAAMC,SAAoB,CAAC,IAAI,CAAC,CAAA,CAAA;;AAA1C,oBAAA,OAAO,GAAG,EAAgC,CAAA,IAAA,EAAA,CAAA;AAC1C,oBAAA,IAAI,GAAG,OAAO,EAAE,CAAC,IAAI,CAAC;AAC5B,oBAAA,OAAO,CAAC,IAAI,EAAE,IAAI,sDAA+B,CAAC;oBAClD,OAAO,CAAA,CAAA,aAAA,OAAO,CAAC,IAAI,CACjB;4BACE,KAAK,EAAE,QAAQ,CAAC,IAAI;AACpB,4BAAA,GAAG,EAAE,YAAY,CAAC,IAAI,CAAC;AACvB,4BAAA,qBAAqB,EAAE,IAAI,CAAC,OAAO,CAAC,2BAA2B;AAC/D,4BAAA,UAAU,EAAE,iBAAiB;AAC7B,4BAAA,SAAS,EAAE,IAAI;AAChB,yBAAA,EACD,UAAC,MAA2B,EAAA;AAC1B,4BAAA,OAAA,IAAI,OAAO,CAAC,UAAO,OAAO,EAAE,MAAM,EAAA,EAAA,OAAA,SAAA,CAAA,KAAA,EAAA,KAAA,CAAA,EAAA,KAAA,CAAA,EAAA,YAAA;;AAgBhC,gCAAA,SAAS,oBAAoB,GAAA;AAC3B,oCAAA,OAAO,EAAE,CAAC,YAAY,CAAC,iBAAiB,CAAC,CAAC;oCAC1C,OAAO,CAAC,MAAM,CAAC,CAAC;iCACjB;;;;gDAlBD,OAAM,CAAA,CAAA,YAAA,MAAM,CAAC,OAAO,CAAC;;AAEnB,gDAAA,cAAc,EAAE,KAAK;AACtB,6CAAA,CAAC,CAAA,CAAA;;AAHF,4CAAA,EAAA,CAAA,IAAA,EAGE,CAAC;AAEG,4CAAA,YAAY,GAAG,YAAY,CAC/B,IAAI,sEAEL,CAAC;AAGI,4CAAA,iBAAiB,GAAG,OAAO,EAAE,CAAC,UAAU,CAAC,YAAA;gDAC7C,MAAM,CAAC,YAAY,CAAC,CAAC;AACvB,6CAAC,EAAE,YAAY,CAAC,GAAG,EAAE,CAAC,CAAC;;;4CAQvB,MAAM,CAAC,IAAI,CAAC,oBAAoB,CAAC,CAAC,IAAI,CAAC,oBAAoB,EAAE,YAAA;gDAC3D,MAAM,CAAC,YAAY,CAAC,CAAC;AACvB,6CAAC,CAAC,CAAC;;;;iCACJ,CAAC,CAAA;AAzBF,yBAyBE,CACL,CAAC,CAAA;;;;AACH;;ACrHD;;;;;;;;;;;;;;;AAeG;AAaH,IAAM,kBAAkB,GAAG;AACzB,IAAA,QAAQ,EAAE,KAAK;AACf,IAAA,SAAS,EAAE,KAAK;AAChB,IAAA,SAAS,EAAE,KAAK;AAChB,IAAA,OAAO,EAAE,IAAI;CACd,CAAC;AAEF,IAAM,aAAa,GAAG,GAAG,CAAC;AAC1B,IAAM,cAAc,GAAG,GAAG,CAAC;AAC3B,IAAM,YAAY,GAAG,QAAQ,CAAC;AAE9B,IAAM,iBAAiB,GAAG,kBAAkB,CAAC;AAE7C,IAAA,SAAA,kBAAA,YAAA;AAGE,IAAA,SAAA,SAAA,CAAqB,MAAqB,EAAA;QAArB,IAAM,CAAA,MAAA,GAAN,MAAM,CAAe;QAF1C,IAAe,CAAA,eAAA,GAAkB,IAAI,CAAC;KAEQ;AAE9C,IAAA,SAAA,CAAA,SAAA,CAAA,KAAK,GAAL,YAAA;QACE,IAAI,IAAI,CAAC,MAAM,EAAE;YACf,IAAI;AACF,gBAAA,IAAI,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC;AACrB,aAAA;YAAC,OAAO,CAAC,EAAE,GAAE;AACf,SAAA;KACF,CAAA;IACH,OAAC,SAAA,CAAA;AAAD,CAAC,EAAA,EAAA;AAEK,SAAU,KAAK,CACnB,IAAkB,EAClB,GAAY,EACZ,IAAa,EACb,KAAqB,EACrB,MAAuB,EAAA;AADvB,IAAA,IAAA,KAAA,KAAA,KAAA,CAAA,EAAA,EAAA,KAAqB,GAAA,aAAA,CAAA,EAAA;AACrB,IAAA,IAAA,MAAA,KAAA,KAAA,CAAA,EAAA,EAAA,MAAuB,GAAA,cAAA,CAAA,EAAA;IAEvB,IAAM,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,MAAM,CAAC,MAAM,CAAC,WAAW,GAAG,MAAM,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC,QAAQ,EAAE,CAAC;IAC7E,IAAM,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,MAAM,CAAC,MAAM,CAAC,UAAU,GAAG,KAAK,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC,QAAQ,EAAE,CAAC;IAC5E,IAAI,MAAM,GAAG,EAAE,CAAC;IAEhB,IAAM,OAAO,GACR,QAAA,CAAA,QAAA,CAAA,EAAA,EAAA,kBAAkB,CACrB,EAAA,EAAA,KAAK,EAAE,KAAK,CAAC,QAAQ,EAAE,EACvB,MAAM,EAAE,MAAM,CAAC,QAAQ,EAAE,EACzB,GAAG,KAAA,EACH,IAAI,EAAA,IAAA,EAAA,CACL,CAAC;;;AAIF,IAAA,IAAM,EAAE,GAAG,KAAK,EAAE,CAAC,WAAW,EAAE,CAAC;AAEjC,IAAA,IAAI,IAAI,EAAE;AACR,QAAA,MAAM,GAAG,YAAY,CAAC,EAAE,CAAC,GAAG,YAAY,GAAG,IAAI,CAAC;AACjD,KAAA;AAED,IAAA,IAAI,UAAU,CAAC,EAAE,CAAC,EAAE;;AAElB,QAAA,GAAG,GAAG,GAAG,IAAI,iBAAiB,CAAC;;;AAG/B,QAAA,OAAO,CAAC,UAAU,GAAG,KAAK,CAAC;AAC5B,KAAA;AAED,IAAA,IAAM,aAAa,GAAG,MAAM,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,MAAM,CAClD,UAAC,KAAK,EAAE,EAAY,EAAA;YAAX,GAAG,GAAA,EAAA,CAAA,CAAA,CAAA,EAAE,KAAK,GAAA,EAAA,CAAA,CAAA,CAAA,CAAA;AAAM,QAAA,OAAA,UAAG,KAAK,CAAA,CAAA,MAAA,CAAG,GAAG,EAAA,GAAA,CAAA,CAAA,MAAA,CAAI,KAAK,EAAG,GAAA,CAAA,CAAA;KAAA,EACnD,EAAE,CACH,CAAC;IAEF,IAAI,gBAAgB,CAAC,EAAE,CAAC,IAAI,MAAM,KAAK,OAAO,EAAE;AAC9C,QAAA,kBAAkB,CAAC,GAAG,IAAI,EAAE,EAAE,MAAM,CAAC,CAAC;AACtC,QAAA,OAAO,IAAI,SAAS,CAAC,IAAI,CAAC,CAAC;AAC5B,KAAA;;;AAID,IAAA,IAAM,MAAM,GAAG,MAAM,CAAC,IAAI,CAAC,GAAG,IAAI,EAAE,EAAE,MAAM,EAAE,aAAa,CAAC,CAAC;AAC7D,IAAA,OAAO,CAAC,MAAM,EAAE,IAAI,oDAA8B,CAAC;;IAGnD,IAAI;QACF,MAAM,CAAC,KAAK,EAAE,CAAC;AAChB,KAAA;IAAC,OAAO,CAAC,EAAE,GAAE;AAEd,IAAA,OAAO,IAAI,SAAS,CAAC,MAAM,CAAC,CAAC;AAC/B,CAAC;AAED,SAAS,kBAAkB,CAAC,GAAW,EAAE,MAAc,EAAA;IACrD,IAAM,EAAE,GAAG,QAAQ,CAAC,aAAa,CAAC,GAAG,CAAC,CAAC;AACvC,IAAA,EAAE,CAAC,IAAI,GAAG,GAAG,CAAC;AACd,IAAA,EAAE,CAAC,MAAM,GAAG,MAAM,CAAC;IACnB,IAAM,KAAK,GAAG,QAAQ,CAAC,WAAW,CAAC,YAAY,CAAC,CAAC;AACjD,IAAA,KAAK,CAAC,cAAc,CAClB,OAAO,EACP,IAAI,EACJ,IAAI,EACJ,MAAM,EACN,CAAC,EACD,CAAC,EACD,CAAC,EACD,CAAC,EACD,CAAC,EACD,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,CAAC,EACD,IAAI,CACL,CAAC;AACF,IAAA,EAAE,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;AAC1B;;ACxIA;;;;;;;;;;;;;;;AAeG;AA2BH;;;AAGG;AACH,IAAM,uBAAuB,GAAG,mBAAmB,CAAC;AAWpD,IAAA,4BAAA,kBAAA,YAAA;AAAA,IAAA,SAAA,4BAAA,GAAA;QACmB,IAAa,CAAA,aAAA,GAAqC,EAAE,CAAC;QACrD,IAAO,CAAA,OAAA,GAAwC,EAAE,CAAC;QAClD,IAAwB,CAAA,wBAAA,GAAkC,EAAE,CAAC;QAErE,IAAoB,CAAA,oBAAA,GAAG,yBAAyB,CAAC;QAyH1D,IAAmB,CAAA,mBAAA,GAAG,kBAAkB,CAAC;QAEzC,IAAuB,CAAA,uBAAA,GAAG,uBAAuB,CAAC;KACnD;;;IAxHO,4BAAU,CAAA,SAAA,CAAA,UAAA,GAAhB,UACE,IAAkB,EAClB,QAAsB,EACtB,QAAuB,EACvB,OAAgB,EAAA;;;;;;;AAEhB,wBAAA,WAAW,CACT,CAAA,EAAA,GAAA,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC,MAAE,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,OAAO,EACxC,8CAA8C,CAC/C,CAAC;AAEU,wBAAA,OAAA,CAAA,CAAA,YAAM,eAAe,CAC/B,IAAI,EACJ,QAAQ,EACR,QAAQ,EACR,cAAc,EAAE,EAChB,OAAO,CACR,CAAA,CAAA;;AANK,wBAAA,GAAG,GAAG,EAMX,CAAA,IAAA,EAAA,CAAA;wBACD,OAAO,CAAA,CAAA,aAAA,KAAK,CAAC,IAAI,EAAE,GAAG,EAAE,gBAAgB,EAAE,CAAC,CAAC,CAAA;;;;AAC7C,KAAA,CAAA;IAEK,4BAAa,CAAA,SAAA,CAAA,aAAA,GAAnB,UACE,IAAkB,EAClB,QAAsB,EACtB,QAAuB,EACvB,OAAgB,EAAA;;;;;AAEhB,oBAAA,KAAA,CAAA,EAAA,OAAA,CAAA,CAAA,YAAM,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAA,CAAA;;AAAlC,wBAAA,EAAA,CAAA,IAAA,EAAkC,CAAC;AACvB,wBAAA,OAAA,CAAA,CAAA,YAAM,eAAe,CAC/B,IAAI,EACJ,QAAQ,EACR,QAAQ,EACR,cAAc,EAAE,EAChB,OAAO,CACR,CAAA,CAAA;;AANK,wBAAA,GAAG,GAAG,EAMX,CAAA,IAAA,EAAA,CAAA;wBACD,kBAAkB,CAAC,GAAG,CAAC,CAAC;AACxB,wBAAA,OAAA,CAAA,CAAA,aAAO,IAAI,OAAO,CAAC,YAAO,GAAC,CAAC,CAAC,CAAA;;;;AAC9B,KAAA,CAAA;IAED,4BAAW,CAAA,SAAA,CAAA,WAAA,GAAX,UAAY,IAAkB,EAAA;QAA9B,IAsBC,KAAA,GAAA,IAAA,CAAA;AArBC,QAAA,IAAM,GAAG,GAAG,IAAI,CAAC,IAAI,EAAE,CAAC;AACxB,QAAA,IAAI,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,EAAE;AACrB,YAAA,IAAA,EAAuB,GAAA,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,EAA5C,OAAO,GAAA,EAAA,CAAA,OAAA,EAAE,SAAO,aAA4B,CAAC;AACrD,YAAA,IAAI,OAAO,EAAE;AACX,gBAAA,OAAO,OAAO,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;AACjC,aAAA;AAAM,iBAAA;AACL,gBAAA,WAAW,CAAC,SAAO,EAAE,0CAA0C,CAAC,CAAC;AACjE,gBAAA,OAAO,SAAO,CAAC;AAChB,aAAA;AACF,SAAA;QAED,IAAM,OAAO,GAAG,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC;QAC7C,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,GAAG,EAAE,OAAO,EAAA,OAAA,EAAE,CAAC;;;QAItC,OAAO,CAAC,KAAK,CAAC,YAAA;AACZ,YAAA,OAAO,KAAI,CAAC,aAAa,CAAC,GAAG,CAAC,CAAC;AACjC,SAAC,CAAC,CAAC;AAEH,QAAA,OAAO,OAAO,CAAC;KAChB,CAAA;IAEa,4BAAiB,CAAA,SAAA,CAAA,iBAAA,GAA/B,UAAgC,IAAkB,EAAA;;;;;AACjC,oBAAA,KAAA,CAAA,EAAA,OAAA,CAAA,CAAA,YAAM,WAAW,CAAC,IAAI,CAAC,CAAA,CAAA;;AAAhC,wBAAA,MAAM,GAAG,EAAuB,CAAA,IAAA,EAAA,CAAA;AAChC,wBAAA,OAAO,GAAG,IAAI,gBAAgB,CAAC,IAAI,CAAC,CAAC;AAC3C,wBAAA,MAAM,CAAC,QAAQ,CACb,WAAW,EACX,UAAC,WAAiC,EAAA;4BAChC,OAAO,CAAC,WAAW,KAAA,IAAA,IAAX,WAAW,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAX,WAAW,CAAE,SAAS,EAAE,IAAI,EAAA,oBAAA,wCAAmC,CAAC;;4BAGxE,IAAM,OAAO,GAAG,OAAO,CAAC,OAAO,CAAC,WAAW,CAAC,SAAS,CAAC,CAAC;4BACvD,OAAO,EAAE,MAAM,EAAE,OAAO,GAAmB,KAAA,yBAAmB,OAAA,0BAAE,CAAC;AACnE,yBAAC,EACD,IAAI,CAAC,OAAO,CAAC,2BAA2B,CACzC,CAAC;AAEF,wBAAA,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC,GAAG,EAAE,OAAO,EAAA,OAAA,EAAE,CAAC;wBAC9C,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC,GAAG,MAAM,CAAC;AACnC,wBAAA,OAAA,CAAA,CAAA,aAAO,OAAO,CAAC,CAAA;;;;AAChB,KAAA,CAAA;AAED,IAAA,4BAAA,CAAA,SAAA,CAAA,4BAA4B,GAA5B,UACE,IAAkB,EAClB,EAAmC,EAAA;QAEnC,IAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC,CAAC;AACzC,QAAA,MAAM,CAAC,IAAI,CACT,uBAAuB,EACvB,EAAE,IAAI,EAAE,uBAAuB,EAAE,EACjC,UAAA,MAAM,EAAA;;AACJ,YAAA,IAAM,WAAW,GAAG,CAAA,EAAA,GAAA,MAAM,aAAN,MAAM,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAN,MAAM,CAAG,CAAC,CAAC,MAAG,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,uBAAuB,CAAC,CAAC;YAC3D,IAAI,WAAW,KAAK,SAAS,EAAE;AAC7B,gBAAA,EAAE,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC;AACnB,aAAA;YAED,KAAK,CAAC,IAAI,EAAA,gBAAA,oCAA+B,CAAC;AAC5C,SAAC,EACD,IAAI,CAAC,OAAO,CAAC,2BAA2B,CACzC,CAAC;KACH,CAAA;IAED,4BAAiB,CAAA,SAAA,CAAA,iBAAA,GAAjB,UAAkB,IAAkB,EAAA;AAClC,QAAA,IAAM,GAAG,GAAG,IAAI,CAAC,IAAI,EAAE,CAAC;AACxB,QAAA,IAAI,CAAC,IAAI,CAAC,wBAAwB,CAAC,GAAG,CAAC,EAAE;YACvC,IAAI,CAAC,wBAAwB,CAAC,GAAG,CAAC,GAAG,eAAe,CAAC,IAAI,CAAC,CAAC;AAC5D,SAAA;AAED,QAAA,OAAO,IAAI,CAAC,wBAAwB,CAAC,GAAG,CAAC,CAAC;KAC3C,CAAA;AAED,IAAA,MAAA,CAAA,cAAA,CAAI,4BAAsB,CAAA,SAAA,EAAA,wBAAA,EAAA;AAA1B,QAAA,GAAA,EAAA,YAAA;;YAEE,OAAO,gBAAgB,EAAE,IAAI,SAAS,EAAE,IAAI,MAAM,EAAE,CAAC;SACtD;;;AAAA,KAAA,CAAA,CAAA;IAKH,OAAC,4BAAA,CAAA;AAAD,CAAC,EAAA,CAAA,CAAA;AAED;;;;;;;;AAQG;AACI,IAAM,4BAA4B,GACvC;;AChLF,IAAA,wBAAA,kBAAA,YAAA;AACE,IAAA,SAAA,wBAAA,CAA+B,QAAkB,EAAA;QAAlB,IAAQ,CAAA,QAAA,GAAR,QAAQ,CAAU;KAAI;AAErD,IAAA,wBAAA,CAAA,SAAA,CAAA,QAAQ,GAAR,UACE,IAAkB,EAClB,OAA+B,EAC/B,WAA2B,EAAA;QAE3B,QAAQ,OAAO,CAAC,IAAI;AAClB,YAAA,KAAA,QAAA;AACE,gBAAA,OAAO,IAAI,CAAC,eAAe,CAAC,IAAI,EAAE,OAAO,CAAC,UAAU,EAAE,WAAW,CAAC,CAAC;AACrE,YAAA,KAAA,QAAA;gBACE,OAAO,IAAI,CAAC,eAAe,CAAC,IAAI,EAAE,OAAO,CAAC,UAAU,CAAC,CAAC;AACxD,YAAA;AACE,gBAAA,OAAO,SAAS,CAAC,mCAAmC,CAAC,CAAC;AACzD,SAAA;KACF,CAAA;IAWH,OAAC,wBAAA,CAAA;AAAD,CAAC,EAAA,CAAA;;ACnBD;;;;AAIG;AACH,IAAA,6BAAA,kBAAA,UAAA,MAAA,EAAA;IACU,SAAwB,CAAA,6BAAA,EAAA,MAAA,CAAA,CAAA;AAGhC,IAAA,SAAA,6BAAA,CAAqC,UAA+B,EAAA;AAApE,QAAA,IAAA,KAAA,GACE,+CAAqB,IACtB,IAAA,CAAA;QAFoC,KAAU,CAAA,UAAA,GAAV,UAAU,CAAqB;;KAEnE;;IAGM,6BAAe,CAAA,eAAA,GAAtB,UACE,UAA+B,EAAA;AAE/B,QAAA,OAAO,IAAI,6BAA6B,CAAC,UAAU,CAAC,CAAC;KACtD,CAAA;;AAGD,IAAA,6BAAA,CAAA,SAAA,CAAA,eAAe,GAAf,UACE,IAAkB,EAClB,OAAe,EACf,WAA2B,EAAA;QAE3B,OAAO,sBAAsB,CAAC,IAAI,EAAE;AAClC,YAAA,OAAO,EAAA,OAAA;AACP,YAAA,WAAW,EAAA,WAAA;AACX,YAAA,qBAAqB,EAAE,IAAI,CAAC,UAAU,CAAC,wBAAwB,EAAE;AAClE,SAAA,CAAC,CAAC;KACJ,CAAA;;AAGD,IAAA,6BAAA,CAAA,SAAA,CAAA,eAAe,GAAf,UACE,IAAkB,EAClB,oBAA4B,EAAA;QAE5B,OAAO,sBAAsB,CAAC,IAAI,EAAE;AAClC,YAAA,oBAAoB,EAAA,oBAAA;AACpB,YAAA,qBAAqB,EAAE,IAAI,CAAC,UAAU,CAAC,wBAAwB,EAAE;AAClE,SAAA,CAAC,CAAC;KACJ,CAAA;IACH,OAAC,6BAAA,CAAA;AAAD,CAtCA,CACU,wBAAwB,CAqCjC,CAAA,CAAA;AAED;;;;AAIG;AACH,IAAA,yBAAA,kBAAA,YAAA;AACE,IAAA,SAAA,yBAAA,GAAA;KAAwB;AAExB;;;;;;;;;AASG;IACI,yBAAS,CAAA,SAAA,GAAhB,UAAiB,UAA+B,EAAA;AAC9C,QAAA,OAAO,6BAA6B,CAAC,eAAe,CAAC,UAAU,CAAC,CAAC;KAClE,CAAA;AAED;;AAEG;IACI,yBAAS,CAAA,SAAA,GAAG,OAAO,CAAC;IAC7B,OAAC,yBAAA,CAAA;AAAA,CArBD,EAqBC;;AChED;;;;AAIG;AACH,IAAA,wBAAA,kBAAA,YAAA;AAAA,IAAA,SAAA,wBAAA,GAAA;KAoEC;AAnEC;;;;;;;;;AASG;AACI,IAAA,wBAAA,CAAA,sBAAsB,GAA7B,UACE,MAAkB,EAClB,eAAuB,EAAA;QAEvB,OAAO,4BAA4B,CAAC,WAAW,CAAC,MAAM,EAAE,eAAe,CAAC,CAAC;KAC1E,CAAA;AAED;;;;;;;;AAQG;AACI,IAAA,wBAAA,CAAA,kBAAkB,GAAzB,UACE,YAAoB,EACpB,eAAuB,EAAA;QAEvB,OAAO,4BAA4B,CAAC,iBAAiB,CACnD,YAAY,EACZ,eAAe,CAChB,CAAC;KACH,CAAA;AAED;;;;;;;;AAQG;IACU,wBAAc,CAAA,cAAA,GAA3B,UACE,OAA2B,EAAA;;;;;;;wBAErB,UAAU,GAAG,OAAiC,CAAC;AACrD,wBAAA,OAAO,CACL,QAAO,CAAA,EAAA,GAAA,UAAU,CAAC,IAAI,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAE,IAAI,CAAA,KAAK,WAAW,sDAE7C,CAAC;AACe,wBAAA,OAAA,CAAA,CAAA,YAAM,kBAAkB,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,EAAE;gCAC9D,OAAO,EAAE,UAAU,CAAC,UAAU;AAC9B,gCAAA,kBAAkB,EAAE,EAAE;AACvB,6BAAA,CAAC,CAAA,CAAA;;AAHI,wBAAA,QAAQ,GAAG,EAGf,CAAA,IAAA,EAAA,CAAA;AACF,wBAAA,OAAA,CAAA,CAAA,aAAO,UAAU,CAAC,mCAAmC,CACnD,QAAQ,EACR,UAAU,CAAC,IAAI,CAAC,IAAI,CACrB,CAAC,CAAA;;;;AACH,KAAA,CAAA;AAED;;AAEG;AACI,IAAA,wBAAA,CAAA,SAAS,GAAyB,MAAA,qBAAA;IAC3C,OAAC,wBAAA,CAAA;AAAA,CApED,EAoEC,EAAA;AAED,IAAA,4BAAA,kBAAA,UAAA,MAAA,EAAA;IACU,SAAwB,CAAA,4BAAA,EAAA,MAAA,CAAA,CAAA;AAGhC,IAAA,SAAA,4BAAA,CACW,GAAW,EACX,YAAqB,EACrB,MAAmB,EAAA;AAH9B,QAAA,IAAA,KAAA,GAKE,6CAAoB,IACrB,IAAA,CAAA;QALU,KAAG,CAAA,GAAA,GAAH,GAAG,CAAQ;QACX,KAAY,CAAA,YAAA,GAAZ,YAAY,CAAS;QACrB,KAAM,CAAA,MAAA,GAAN,MAAM,CAAa;;KAG7B;;AAGM,IAAA,4BAAA,CAAA,WAAW,GAAlB,UACE,MAAkB,EAClB,GAAW,EAAA;QAEX,OAAO,IAAI,4BAA4B,CAAC,GAAG,EAAE,SAAS,EAAE,MAAM,CAAC,CAAC;KACjE,CAAA;;AAGM,IAAA,4BAAA,CAAA,iBAAiB,GAAxB,UACE,YAAoB,EACpB,GAAW,EAAA;AAEX,QAAA,OAAO,IAAI,4BAA4B,CAAC,GAAG,EAAE,YAAY,CAAC,CAAC;KAC5D,CAAA;;AAGK,IAAA,4BAAA,CAAA,SAAA,CAAA,eAAe,GAArB,UACE,IAAkB,EAClB,OAAe,EACf,WAA2B,EAAA;;;gBAE3B,OAAO,CACL,OAAO,IAAI,CAAC,MAAM,KAAK,WAAW,EAClC,IAAI,EAAA,gBAAA,oCAEL,CAAC;gBACF,OAAO,CAAA,CAAA,aAAA,qBAAqB,CAAC,IAAI,EAAE;AACjC,wBAAA,OAAO,EAAA,OAAA;AACP,wBAAA,WAAW,EAAA,WAAA;wBACX,oBAAoB,EAAE,IAAI,CAAC,MAAM,CAAC,yBAAyB,CAAC,IAAI,CAAC,GAAG,CAAC;AACtE,qBAAA,CAAC,CAAC,CAAA;;;AACJ,KAAA,CAAA;;AAGK,IAAA,4BAAA,CAAA,SAAA,CAAA,eAAe,GAArB,UACE,IAAkB,EAClB,oBAA4B,EAAA;;;;AAE5B,gBAAA,OAAO,CACL,IAAI,CAAC,YAAY,KAAK,SAAS,IAAI,IAAI,CAAC,GAAG,KAAK,SAAS,EACzD,IAAI,sDAEL,CAAC;gBACI,oBAAoB,GAAG,EAAE,gBAAgB,EAAE,IAAI,CAAC,GAAG,EAAE,CAAC;gBAC5D,OAAO,CAAA,CAAA,aAAA,qBAAqB,CAAC,IAAI,EAAE;AACjC,wBAAA,oBAAoB,EAAA,oBAAA;wBACpB,eAAe,EAAE,IAAI,CAAC,YAAY;AAClC,wBAAA,oBAAoB,EAAA,oBAAA;AACrB,qBAAA,CAAC,CAAC,CAAA;;;AACJ,KAAA,CAAA;IACH,OAAC,4BAAA,CAAA;AAAD,CA/DA,CACU,wBAAwB,CA8DjC,CAAA,CAAA;AAED;;;;;;AAMG;AACH,IAAA,UAAA,kBAAA,YAAA;;AAwBE,IAAA,SAAA,UAAA,CACE,SAAiB,EACjB,gBAAwB,EACxB,UAAkB,EAClB,mBAA2B,EAC3B,4BAAoC,EACnB,WAAmB,EACnB,IAAkB,EAAA;QADlB,IAAW,CAAA,WAAA,GAAX,WAAW,CAAQ;QACnB,IAAI,CAAA,IAAA,GAAJ,IAAI,CAAc;AAEnC,QAAA,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;AAC3B,QAAA,IAAI,CAAC,gBAAgB,GAAG,gBAAgB,CAAC;AACzC,QAAA,IAAI,CAAC,UAAU,GAAG,UAAU,CAAC;AAC7B,QAAA,IAAI,CAAC,mBAAmB,GAAG,mBAAmB,CAAC;AAC/C,QAAA,IAAI,CAAC,4BAA4B,GAAG,4BAA4B,CAAC;KAClE;;AAGM,IAAA,UAAA,CAAA,mCAAmC,GAA1C,UACE,QAAwC,EACxC,IAAkB,EAAA;QAElB,OAAO,IAAI,UAAU,CACnB,QAAQ,CAAC,eAAe,CAAC,eAAe,EACxC,QAAQ,CAAC,eAAe,CAAC,gBAAgB,EACzC,QAAQ,CAAC,eAAe,CAAC,sBAAsB,EAC/C,QAAQ,CAAC,eAAe,CAAC,SAAS,EAClC,IAAI,IAAI,CAAC,QAAQ,CAAC,eAAe,CAAC,sBAAsB,CAAC,CAAC,WAAW,EAAE,EACvE,QAAQ,CAAC,eAAe,CAAC,WAAW,EACpC,IAAI,CACL,CAAC;KACH,CAAA;;IAGD,UAAyB,CAAA,SAAA,CAAA,yBAAA,GAAzB,UAA0B,GAAW,EAAA;QACnC,OAAO,EAAE,WAAW,EAAE,IAAI,CAAC,WAAW,EAAE,gBAAgB,EAAE,GAAG,EAAE,CAAC;KACjE,CAAA;AAED;;;;;;;;;AASG;AACH,IAAA,UAAA,CAAA,SAAA,CAAA,iBAAiB,GAAjB,UAAkB,WAAoB,EAAE,MAAe,EAAA;;QACrD,IAAI,WAAW,GAAG,KAAK,CAAC;QACxB,IAAI,cAAc,CAAC,WAAW,CAAC,IAAI,cAAc,CAAC,MAAM,CAAC,EAAE;YACzD,WAAW,GAAG,IAAI,CAAC;AACpB,SAAA;AACD,QAAA,IAAI,WAAW,EAAE;AACf,YAAA,IAAI,cAAc,CAAC,WAAW,CAAC,EAAE;AAC/B,gBAAA,WAAW,GAAG,CAAA,CAAA,EAAA,GAAA,IAAI,CAAC,IAAI,CAAC,WAAW,MAAE,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,KAAK,KAAI,aAAa,CAAC;AAC7D,aAAA;AACD,YAAA,IAAI,cAAc,CAAC,MAAM,CAAC,EAAE;AAC1B,gBAAA,MAAM,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC;AACzB,aAAA;AACF,SAAA;AACD,QAAA,OAAO,yBAAkB,MAAM,EAAA,GAAA,CAAA,CAAA,MAAA,CAAI,WAAW,EAAW,UAAA,CAAA,CAAA,MAAA,CAAA,IAAI,CAAC,SAAS,EAAA,UAAA,CAAA,CAAA,MAAA,CAAW,MAAM,EAAc,aAAA,CAAA,CAAA,MAAA,CAAA,IAAI,CAAC,gBAAgB,EAAA,UAAA,CAAA,CAAA,MAAA,CAAW,IAAI,CAAC,UAAU,CAAE,CAAC;KACzJ,CAAA;IACH,OAAC,UAAA,CAAA;AAAD,CAAC,EAAA,EAAA;AAED;AACA,SAAS,cAAc,CAAC,KAAc,EAAA;AACpC,IAAA,OAAO,OAAO,KAAK,KAAK,WAAW,IAAI,CAAA,KAAK,KAAL,IAAA,IAAA,KAAK,uBAAL,KAAK,CAAE,MAAM,MAAK,CAAC,CAAC;AAC7D;;ACnRA;;;;;;;;;;;;;;;AAeG;AAsBH,IAAM,wBAAwB,GAAG,CAAC,GAAG,EAAE,CAAC;AACxC,IAAM,iBAAiB,GACrB,sBAAsB,CAAC,mBAAmB,CAAC,IAAI,wBAAwB,CAAC;AAE1E,IAAI,iBAAiB,GAA8B,IAAI,CAAC;AAExD,IAAM,iBAAiB,GAAG,UAAC,GAAW,EAAK,EAAA,OAAA,UAAO,IAAiB,EAAA,EAAA,OAAA,SAAA,CAAA,KAAA,CAAA,EAAA,KAAA,CAAA,EAAA,KAAA,CAAA,EAAA,YAAA;;;;;AAC3C,gBAAA,EAAA,GAAA,IAAI,CAAA;yBAAJ,OAAI,CAAA,CAAA,YAAA,CAAA,CAAA,CAAA;AAAK,gBAAA,OAAA,CAAA,CAAA,YAAM,IAAI,CAAC,gBAAgB,EAAE,CAAA,CAAA;;gBAA9B,EAAA,IAAC,EAA6B,CAAA,IAAA,EAAA,CAAC,CAAA;;;AAAvD,gBAAA,aAAa,GAA0C,EAAA,CAAA;AACvD,gBAAA,UAAU,GACd,aAAa;AACb,oBAAA,CAAC,IAAI,IAAI,EAAE,CAAC,OAAO,EAAE,GAAG,IAAI,CAAC,KAAK,CAAC,aAAa,CAAC,YAAY,CAAC,IAAI,IAAK,CAAC;AAC1E,gBAAA,IAAI,UAAU,IAAI,UAAU,GAAG,iBAAiB,EAAE;oBAChD,OAAO,CAAA,CAAA,YAAA,CAAA;AACR,iBAAA;gBAEK,OAAO,GAAG,aAAa,KAAb,IAAA,IAAA,aAAa,uBAAb,aAAa,CAAE,KAAK,CAAC;gBACrC,IAAI,iBAAiB,KAAK,OAAO,EAAE;oBACjC,OAAO,CAAA,CAAA,YAAA,CAAA;AACR,iBAAA;gBACD,iBAAiB,GAAG,OAAO,CAAC;gBAC5B,OAAM,CAAA,CAAA,YAAA,KAAK,CAAC,GAAG,EAAE;wBACf,MAAM,EAAE,OAAO,GAAG,MAAM,GAAG,QAAQ;AACnC,wBAAA,OAAO,EAAE,OAAO;AACd,8BAAE;gCACE,eAAe,EAAE,SAAU,CAAA,MAAA,CAAA,OAAO,CAAE;AACrC,6BAAA;AACH,8BAAE,EAAE;AACP,qBAAA,CAAC,CAAA,CAAA;;AAPF,gBAAA,EAAA,CAAA,IAAA,EAOE,CAAC;;;;AACJ,CAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAC;AAEF;;;;;;;AAOG;AACG,SAAU,OAAO,CAAC,GAA2B,EAAA;IAA3B,IAAA,GAAA,KAAA,KAAA,CAAA,EAAA,EAAA,GAAmB,GAAA,MAAM,EAAE,CAAA,EAAA;IACjD,IAAM,QAAQ,GAAG,YAAY,CAAC,GAAG,EAAE,MAAM,CAAC,CAAC;AAE3C,IAAA,IAAI,QAAQ,CAAC,aAAa,EAAE,EAAE;AAC5B,QAAA,OAAO,QAAQ,CAAC,YAAY,EAAE,CAAC;AAChC,KAAA;AAED,IAAA,IAAM,IAAI,GAAG,cAAc,CAAC,GAAG,EAAE;AAC/B,QAAA,qBAAqB,EAAE,4BAA4B;AACnD,QAAA,WAAW,EAAE;YACX,yBAAyB;YACzB,uBAAuB;YACvB,yBAAyB;AAC1B,SAAA;AACF,KAAA,CAAC,CAAC;AAEH,IAAA,IAAM,iBAAiB,GAAG,sBAAsB,CAAC,kBAAkB,CAAC,CAAC;;AAErE,IAAA,IACE,iBAAiB;QACjB,OAAO,eAAe,KAAK,SAAS;AACpC,QAAA,eAAe,EACf;;QAEA,IAAM,gBAAgB,GAAG,IAAI,GAAG,CAAC,iBAAiB,EAAE,QAAQ,CAAC,MAAM,CAAC,CAAC;AACrE,QAAA,IAAI,QAAQ,CAAC,MAAM,KAAK,gBAAgB,CAAC,MAAM,EAAE;YAC/C,IAAM,YAAU,GAAG,iBAAiB,CAAC,gBAAgB,CAAC,QAAQ,EAAE,CAAC,CAAC;AAClE,YAAA,sBAAsB,CAAC,IAAI,EAAE,YAAU,EAAE,YAAA;AACvC,gBAAA,OAAA,YAAU,CAAC,IAAI,CAAC,WAAW,CAAC,CAAA;AAA5B,aAA4B,CAC7B,CAAC;AACF,YAAA,gBAAgB,CAAC,IAAI,EAAE,UAAA,IAAI,EAAI,EAAA,OAAA,YAAU,CAAC,IAAI,CAAC,CAAhB,EAAgB,CAAC,CAAC;AAClD,SAAA;AACF,KAAA;AAED,IAAA,IAAM,gBAAgB,GAAG,sBAAsB,CAAC,MAAM,CAAC,CAAC;AACxD,IAAA,IAAI,gBAAgB,EAAE;AACpB,QAAA,mBAAmB,CAAC,IAAI,EAAE,iBAAU,gBAAgB,CAAE,CAAC,CAAC;AACzD,KAAA;AAED,IAAA,OAAO,IAAI,CAAC;AACd,CAAC;AAED,SAAS,sBAAsB,GAAA;;AAC7B,IAAA,OAAO,CAAA,EAAA,GAAA,CAAA,EAAA,GAAA,QAAQ,CAAC,oBAAoB,CAAC,MAAM,CAAC,MAAG,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,CAAC,CAAC,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,EAAA,GAAI,QAAQ,CAAC;AAChE,CAAC;AAED,sBAAsB,CAAC;IACrB,MAAM,EAAN,UAAO,GAAW,EAAA;;AAEhB,QAAA,OAAO,IAAI,OAAO,CAAC,UAAC,OAAO,EAAE,MAAM,EAAA;YACjC,IAAM,EAAE,GAAG,QAAQ,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC;AAC5C,YAAA,EAAE,CAAC,YAAY,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC;AAC5B,YAAA,EAAE,CAAC,MAAM,GAAG,OAAO,CAAC;AACpB,YAAA,EAAE,CAAC,OAAO,GAAG,UAAA,CAAC,EAAA;AACZ,gBAAA,IAAM,KAAK,GAAG,YAAY,CAAA,gBAAA,oCAA8B,CAAC;AACzD,gBAAA,KAAK,CAAC,UAAU,GAAG,CAAuC,CAAC;gBAC3D,MAAM,CAAC,KAAK,CAAC,CAAC;AAChB,aAAC,CAAC;AACF,YAAA,EAAE,CAAC,IAAI,GAAG,iBAAiB,CAAC;AAC5B,YAAA,EAAE,CAAC,OAAO,GAAG,OAAO,CAAC;AACrB,YAAA,sBAAsB,EAAE,CAAC,WAAW,CAAC,EAAE,CAAC,CAAC;AAC3C,SAAC,CAAC,CAAC;KACJ;AAED,IAAA,UAAU,EAAE,mCAAmC;AAC/C,IAAA,iBAAiB,EAAE,yCAAyC;AAC5D,IAAA,yBAAyB,EACvB,wDAAwD;AAC3D,CAAA,CAAC,CAAC;AAEH,YAAY,wCAAwB;;ACjJpC;;;;;;;;;;;;;;;AAeG;AAqCH;AACA;AACA;AACgB,SAAA,sBAAsB,CAAC,IAAU,EAAE,SAAiB,EAAA;IAClE,SAAS,CAAC,IAAI,CAAC,CAAC,aAAa,CAAC,SAAS,CAAC,CAAC;AAC3C;;;;"}