<template>
  <div class="w-full px-2 sm:px-4 py-6 max-w-3xl mx-auto flex flex-col gap-8">
    
      <div class="text-center mb-8">
        <h1 class="text-4xl font-bold text-gray-900 dark:text-white mb-4">Welcome to TheMeet</h1>
        <p class="text-xl text-gray-600 dark:text-gray-300 mb-6">
          A modern web app for hosting seamless video meetings
        </p>

        <!-- Quick Navigation -->
        <div class="flex flex-wrap justify-center gap-4 mt-6">
          <router-link to="/join" class="btn btn-outline">
            <i class="fas fa-sign-in-alt mr-2"></i>
            Join Meeting
          </router-link>
          <router-link to="/marketplace" class="btn btn-outline">
            <i class="fas fa-store mr-2"></i>
            Marketplace
          </router-link>
          <router-link to="/extensions" class="btn btn-outline">
            <i class="fas fa-puzzle-piece mr-2"></i>
            Extensions
          </router-link>
          <router-link v-if="authStore.isDev" to="/dashboard" class="btn btn-outline">
            <i class="fas fa-tachometer-alt mr-2"></i>
            Dashboard
          </router-link>
        </div>
      </div>

      <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-4 sm:p-6 mb-8 flex flex-col gap-4">
        <h2 class="text-2xl font-semibold text-gray-900 dark:text-white mb-4">Start a New Meeting</h2>

        <!-- Meeting Name -->
        <div class="mb-4">
          <label for="meetingName" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
            Meeting Name (optional)
          </label>
          <input
            id="meetingName"
            v-model="meetingName"
            type="text"
            placeholder="My Awesome Meeting"
            class="input"
          />
        </div>

        <!-- User Name -->
        <div class="mb-4">
          <label for="userName" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
            Your Name
          </label>
          <input
            id="userName"
            v-model="userName"
            type="text"
            placeholder="John Doe"
            class="input"
            required
          />
        </div>

        <!-- Require Approval -->
        <div class="mb-4">
          <div class="flex items-center">
            <input
              id="requireApproval"
              v-model="requireApproval"
              type="checkbox"
              class="h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded"
            />
            <label for="requireApproval" class="ml-2 block text-sm text-gray-700 dark:text-gray-300">
              Require approval for participants to join
            </label>
          </div>
        </div>

        <!-- Core Features Selection -->
        <div class="mb-6">
          <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">Core Features</h3>
          <div class="grid grid-cols-1 sm:grid-cols-2 gap-2">
            <div v-for="feature in coreFeaturesOptions" :key="feature.key" class="flex items-center">
              <input
                :id="`feature-${feature.key}`"
                v-model="selectedFeatures[feature.key]"
                type="checkbox"
                class="h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded"
              />
              <label :for="`feature-${feature.key}`" class="ml-2 block text-sm text-gray-700 dark:text-gray-300">
                {{ feature.label }}
              </label>
            </div>
          </div>
        </div>

        <!-- Extensions Selection -->
        <div class="mb-6">
          <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">Select Extensions (Optional)</h3>
          <div v-if="extensionsStore.isLoading && availableExtensions.length === 0" class="text-center py-4">
            <p class="text-gray-500 dark:text-gray-400">Loading extensions...</p>
            <!-- Optional: Add a spinner SVG or component here -->
          </div>
          <div v-else-if="!extensionsStore.isLoading && availableExtensions.length === 0" class="text-center py-4">
            <p class="text-gray-500 dark:text-gray-400">No extensions currently available.</p>
          </div>
          <div v-else class="grid grid-cols-1 sm:grid-cols-2 gap-2">
            <div
              v-for="ext in availableExtensions"
              :key="ext.id"
              @click="toggleExtensionSelection(ext.id)"
              :class="['extension-card', { 'selected': selectedExtensions.includes(ext.id) }]"
            >
              <h4 class="font-semibold text-gray-800 dark:text-white">{{ ext.name }} <span class="text-xs text-gray-500 dark:text-gray-400">v{{ext.version}}</span></h4>
              <p class="text-sm text-gray-600 dark:text-gray-300">{{ ext.description }}</p>
            </div>
          </div>
        </div>

        <button
          @click="createNewMeeting"
          class="w-full py-3 px-4 rounded bg-blue-600 text-white font-semibold text-lg shadow hover:bg-blue-700 transition disabled:opacity-60 disabled:cursor-not-allowed"
          :disabled="!userName.trim() || isCreating"
        >
          <span v-if="isCreating">Creating...</span>
          <span v-else>Start Meeting Now</span>
        </button>
      </div>

      <!-- Join Meeting Section (remains largely the same) -->
      <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-4 sm:p-6 flex flex-col gap-4">
        <h2 class="text-2xl font-semibold text-gray-900 dark:text-white mb-4">Join a Meeting</h2>
        <div class="mb-4">
          <label for="meetingId" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
            Meeting ID
          </label>
          <input
            id="meetingId"
            v-model="joinMeetingId"
            type="text"
            placeholder="Enter meeting ID"
            class="input"
            required
          />
        </div>
        <div class="mb-4">
          <label for="joinUserName" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
            Your Name
          </label>
          <input
            id="joinUserName"
            v-model="userName"
            type="text"
            placeholder="John Doe"
            class="input w-full py-2 pl-10 text-sm text-gray-700 dark:text-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-gray-600 focus:border-transparent"
            required
          />
        </div>
        <button
          @click="joinExistingMeeting"
          class="w-full py-3 px-4 rounded bg-gray-700 text-white font-semibold text-lg shadow hover:bg-gray-800 transition disabled:opacity-60 disabled:cursor-not-allowed"
          :disabled="!joinMeetingId.trim() || !userName.trim() || isJoining"
        >
          <span v-if="isJoining">Joining...</span>
          <span v-else>Join Meeting</span>
        </button>
      </div>
    </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { createMeeting, checkMeeting } from '../firebase/meetings' // Will need updates
import { useMeetingConfigStore } from '../store/meetingConfigStore'
import { useExtensionsStore } from '../store/extensionsStore'
import { useAuthStore } from '../store/authStore' // Assuming authStore provides user ID
import { nanoid } from 'nanoid'

const router = useRouter()
const meetingConfigStore = useMeetingConfigStore()
const extensionsStore = useExtensionsStore()
const authStore = useAuthStore()

// Form data for creating a new meeting
const meetingName = ref(meetingConfigStore.meetingSettings.name || 'My Awesome Meeting')
const userName = ref(localStorage.getItem('userName') || '') // Persist user name locally for convenience
const joinMeetingId = ref('') // For joining an existing meeting
const requireApproval = ref(meetingConfigStore.meetingSettings.requireApproval || false)

// Reactive state for user's selection of core features for the new meeting.
// Initialized with defaults from meetingConfigStore.activeFeatures.
const selectedFeatures = ref({ ...meetingConfigStore.activeFeatures })

// Reactive state storing IDs of extensions selected by the user for the new meeting.
const selectedExtensions = ref([])

// Loading states
const isCreating = ref(false)
const isJoining = ref(false)

// Available extensions for selection
const availableExtensions = computed(() => extensionsStore.getMarketplaceExtensions)

onMounted(async () => {
  // No need to call fetchExtensions if already loading or loaded by default
  if (extensionsStore.extensions.length === 0 && !extensionsStore.isLoading) {
    await extensionsStore.fetchExtensions();
  }
  // Initialize userName if not already set (e.g. from authStore if integrated)
  if (!userName.value && authStore.user?.name) {
    userName.value = authStore.user.name;
  }
   // Ensure selectedFeatures is initialized with all keys from coreFeaturesOptions
  coreFeaturesOptions.value.forEach(opt => {
    if (typeof selectedFeatures.value[opt.key] === 'undefined') {
      // Initialize with default from config store, or false if not defined there
      selectedFeatures.value[opt.key] = meetingConfigStore.activeFeatures[opt.key] === undefined
        ? false
        : meetingConfigStore.activeFeatures[opt.key];
    }
  });
})

const toggleExtensionSelection = (extId) => {
  const index = selectedExtensions.value.indexOf(extId);
  if (index > -1) {
    selectedExtensions.value.splice(index, 1);
  } else {
    selectedExtensions.value.push(extId);
  }
};

// Create a new meeting
const createNewMeeting = async () => {
  if (!userName.value.trim()) {
    alert('Please enter your name.')
    return
  }

  try {
    isCreating.value = true

    let userId = authStore.user?.id || localStorage.getItem('userId');
    if (!userId) {
      userId = nanoid();
      localStorage.setItem('userId', userId);
    }
    localStorage.setItem('userName', userName.value);

    // Prepare meeting settings from the form
    const meetingSettings = {
      name: meetingName.value || 'TheMeet Meeting',
      requireApproval: requireApproval.value,
    }
    meetingConfigStore.initializeNewMeeting(meetingSettings, userId)

    // Update activeFeatures in store based on selection
    Object.keys(selectedFeatures.value).forEach(featureKey => {
      // Ensure the featureKey exists in the store's activeFeatures before assigning
      if (Object.prototype.hasOwnProperty.call(meetingConfigStore.activeFeatures, featureKey)) {
         meetingConfigStore.activeFeatures[featureKey] = selectedFeatures.value[featureKey];
      } else {
        // If featureKey from UI selection isn't in store's definition, add it.
        // This could happen if coreFeaturesOptions has items not initially in meetingConfigStore.activeFeatures
        meetingConfigStore.activeFeatures[featureKey] = selectedFeatures.value[featureKey];
      }
    });

    // Prepare selected extensions configuration
    const extensionsForMeeting = selectedExtensions.value.map(extId => {
      const manifest = extensionsStore.getExtensionById(extId)
      return manifest ? { id: manifest.id, name: manifest.name, config: manifest.defaultConfig || {} } : null
    }).filter(ext => ext !== null)
    meetingConfigStore.activeExtensions = extensionsForMeeting;


    // Create the meeting in Firebase, now passing the full config
    const meetingId = await createMeeting(
      userId,
      meetingConfigStore.meetingSettings.name,
      meetingConfigStore.meetingSettings.requireApproval,
      meetingConfigStore.activeFeatures, // Pass selected features
      meetingConfigStore.activeExtensions // Pass selected extensions
    )

    // Navigate to the meeting room
    router.push(`/meeting/${meetingId}`)
  } catch (error) {
    console.error('Error creating meeting:', error)
    alert(`Failed to create meeting: ${error.message}. Please try again.`)
  } finally {
    isCreating.value = false
  }
}

// Join an existing meeting
const joinExistingMeeting = async () => {
  if (!joinMeetingId.value.trim() || !userName.value.trim()) return

  try {
    isJoining.value = true

    const meetingExists = await checkMeeting(joinMeetingId.value)

    if (!meetingExists) {
      alert('Meeting not found or has ended.')
      isJoining.value = false;
      return
    }

    let userId = authStore.user?.id || localStorage.getItem('userId');
    if (!userId) {
      userId = nanoid();
      localStorage.setItem('userId', userId);
    }
    localStorage.setItem('userName', userName.value);

    // When joining, the meetingConfigStore will be populated in Meeting.vue
    // by loading the configuration from Firebase.

    router.push(`/meeting/${joinMeetingId.value}`)
  } catch (error) {
    console.error('Error joining meeting:', error)
    alert('Failed to join meeting. Please try again.')
  } finally {
    isJoining.value = false
  }
}

const coreFeaturesOptions = ref([
  { key: 'recording', label: 'Enable Recording' },
  { key: 'whiteboard', label: 'Enable Whiteboard' },
  { key: 'breakoutRooms', label: 'Enable Breakout Rooms' },
  { key: 'virtualBackground', label: 'Enable Virtual Backgrounds' },
  { key: 'audibleImpairedSystem', label: 'Enable Audible Impaired System' },
  { key: 'reactions', label: 'Enable Reactions' },
  // Ensure 'chat' is here if you want a UI toggle, though it's often default true
  // { key: 'chat', label: 'Enable Chat' },
]);

</script>

