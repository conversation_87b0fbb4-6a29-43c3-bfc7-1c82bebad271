{"hash": "5cbaa4fc", "configHash": "b5ff6d4b", "lockfileHash": "59f3d637", "browserHash": "83d94958", "optimized": {"@mediapipe/selfie_segmentation": {"src": "../../@mediapipe/selfie_segmentation/selfie_segmentation.js", "file": "@mediapipe_selfie_segmentation.js", "fileHash": "21350604", "needsInterop": true}, "firebase/app": {"src": "../../firebase/app/dist/esm/index.esm.js", "file": "firebase_app.js", "fileHash": "b47b4bef", "needsInterop": false}, "firebase/auth": {"src": "../../firebase/auth/dist/esm/index.esm.js", "file": "firebase_auth.js", "fileHash": "b0a0c9fd", "needsInterop": false}, "firebase/firestore": {"src": "../../firebase/firestore/dist/esm/index.esm.js", "file": "firebase_firestore.js", "fileHash": "55b91261", "needsInterop": false}, "nanoid": {"src": "../../nanoid/index.browser.js", "file": "nanoid.js", "fileHash": "48e8b965", "needsInterop": false}, "pinia": {"src": "../../pinia/dist/pinia.mjs", "file": "pinia.js", "fileHash": "5b9b16ff", "needsInterop": false}, "vue": {"src": "../../vue/dist/vue.runtime.esm-bundler.js", "file": "vue.js", "fileHash": "0d8212e8", "needsInterop": false}, "vue-router": {"src": "../../vue-router/dist/vue-router.mjs", "file": "vue-router.js", "fileHash": "c367d74e", "needsInterop": false}, "webrtc-adapter": {"src": "../../webrtc-adapter/src/js/adapter_core.js", "file": "webrtc-adapter.js", "fileHash": "7451d39c", "needsInterop": false}}, "chunks": {"chunk-IPXPKJG4": {"file": "chunk-IPXPKJG4.js"}, "chunk-VID4RN2V": {"file": "chunk-VID4RN2V.js"}, "chunk-5DF355KD": {"file": "chunk-5DF355KD.js"}, "chunk-G3PMV62Z": {"file": "chunk-G3PMV62Z.js"}}}