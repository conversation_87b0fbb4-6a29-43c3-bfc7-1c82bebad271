<template>
  <div class="extension-builder">
    <div v-if="!authStore.isLoggedIn || !authStore.isDev" class="builder-auth-guard">
  <h2>Developer Login Required</h2>
  <p>Only logged-in developers can create extensions. Please log in:</p>
  <button @click="loginWithGoogle" class="login-btn dev">Login with Google</button>
  <form @submit.prevent="loginWithEmail" class="email-login-form">
    <input v-model="email" type="email" placeholder="Email" required />
    <input v-model="password" type="password" placeholder="Password" required />
    <button type="submit" class="login-btn user">Login with Email</button>
  </form>
  <p class="login-note">Use Google or email/password to log in. Only developers can create extensions.</p>
  <div v-if="loginError" class="login-error">{{ loginError }}</div>
  <div v-if="authStore.isLoggedIn && !authStore.isDev" class="not-dev-msg">
    <p>You are logged in as a non-developer. Only developers can create extensions.</p>
    <button @click="authStore.logout" class="logout-btn">Logout</button>
  </div>
</div>
    <div v-else>
    <div class="builder-toolbar">
      <h2>Extension Builder <span v-if="extensionIdFromRoute" class="extension-id-display">(Editing ID: {{ extensionIdFromRoute }})</span></h2>
      <div class="toolbar-actions">
        <button
          @click="handleSaveExtension"
          :disabled="!canSaveChanges"
          class="action-button save-button"
          title="Save changes to this extension"
        >
          <i class="fas fa-save"></i> Save Extension
        </button>
        <button @click="currentMode = 'code'" :class="{ active: currentMode === 'code' }" class="mode-toggle">Code Mode</button>
        <button @click="currentMode = 'nocode'" :class="{ active: currentMode === 'nocode' }" class="mode-toggle">No-Code Mode</button>
      </div>
    </div>

    <div v-if="saveStatus" :class="['save-status-toast', saveStatus.type]">
      {{ saveStatus.message }}
    </div>

    <div class="builder-main-area">
      <!-- Main Content Area -->
      <div class="builder-content">
        <div v-if="currentMode === 'code'" class="code-mode-ide">
          <h3>JSON IDE</h3>
          <p>Edit the extension JSON directly. Validation errors will appear below.</p>
          <textarea
            v-model="jsonInput"
            @input="validateJsonInput"
            placeholder="Enter your extension JSON here..."
            rows="20"
          ></textarea>
          <div v-if="validationResult && !validationResult.isValid" class="validation-errors">
            <h4>Validation Errors:</h4>
            <ul v-if="validationResult.parseError">
              <li>Parse Error: {{ validationResult.parseError }}</li>
            </ul>
            <ul v-if="validationResult.errors">
              <li v-for="(error, index) in validationResult.errors" :key="index">
                {{ error.instancePath || 'JSON root' }}: {{ error.message }}
              </li>
            </ul>
          </div>
           <div v-if="validationResult && validationResult.isValid && jsonInput.trim() !== ''" class="validation-success">
            <p>JSON is valid according to the schema!</p>
          </div>
        </div>
        <div v-if="currentMode === 'nocode'" class="nocode-mode-visual-generator">
  <h3>Visual JSON Generator (No-Code)</h3>
  <div class="nocode-layout">
    <div class="nocode-canvas-area">
      <p>Add UI blocks to your extension:</p>
      <div class="canvas-blocks">
        <div v-for="(block, idx) in visualBlocks" :key="block.id" class="canvas-block">
          <span class="block-type">{{ block.type }}</span>
          <button class="remove-block" @click="removeBlock(idx)">Remove</button>
        </div>
        <div v-if="visualBlocks.length === 0" class="canvas-placeholder">No blocks yet. Add from palette.</div>
      </div>
    </div>
    <div class="nocode-tools-palette">
      <p>UI Blocks Palette</p>
      <ul>
        <li v-for="type in supportedBlockTypes" :key="type">
          <button @click="addBlock(type)">{{ type }}</button>
        </li>
      </ul>
    </div>
  </div>
</div>
      </div>

      <!-- Sidebar -->
      <div class="builder-sidebar">
        <h3>Live Preview & Debug</h3>
        <div class="sidebar-section preview-panel">
          <h4>Extension Preview</h4>
          <div v-if="previewableUiBlocks.length > 0" class="preview-content">
            <BlockRenderer
              v-for="block in previewableUiBlocks"
              :key="block.id"
              :block="block"
              @block-event="logBlockEvent"
            />
          </div>
          <div v-else class="preview-placeholder">
             {{ validationResult && validationResult.isValid && jsonInput.trim() !== '' ? 'No UI blocks defined or previewable.' : 'Enter valid JSON with UI blocks to see a preview.' }}
          </div>
        </div>
        <div class="sidebar-section event-debugger-panel">
          <h4>Event Debugger</h4>
          <ul v-if="eventLog.length > 0" class="event-log-list">
            <li v-for="(event, index) in eventLog" :key="index">
              <strong>{{ event.eventName }}</strong> on '{{ event.blockId }}' ({{ event.blockType }})
              <span v-if="event.handlerFunction"> -> calls '{{ event.handlerFunction }}'</span>
            </li>
          </ul>
          <div v-else class="debugger-placeholder">Event logs will appear here.</div>
        </div>
        <div class="sidebar-section state-inspector-panel">
          <h4>State Inspector</h4>
          <div class="inspector-placeholder">Current state values will appear here.</div>
        </div>
      </div>
    </div>
  </div>
    </div>
</template>

<style scoped>
.builder-auth-guard {
  background: #fffbe6;
  border: 1px solid #ffe58f;
  padding: 30px;
  margin: 30px auto;
  max-width: 500px;
  border-radius: 8px;
  text-align: center;
}
.login-btn {
  margin: 10px;
  padding: 10px 20px;
  border-radius: 4px;
  border: none;
  font-size: 1em;
  cursor: pointer;
}
.login-btn.dev {
  background: #007bff;
  color: white;
}
.login-btn.user {
  background: #aaa;
  color: white;
}
.logout-btn {
  margin-top: 10px;
  background: #dc3545;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
}
.login-note {
  color: #888;
  font-size: 0.95em;
  margin-top: 10px;
}
.not-dev-msg {
  margin-top: 20px;
  color: #b00;
}
</style>


<script setup>
import { ref, watch, computed, onMounted } from 'vue';
import { useAuthStore } from '../../store/authStore';

// Only declare authStore once here
const authStore = useAuthStore();
const email = ref('');
const password = ref('');
const loginError = ref('');

async function loginWithGoogle() {
  loginError.value = '';
  try {
    await authStore.loginWithGoogle();
  } catch (e) {
    loginError.value = e.message || 'Google login failed.';
  }
}
async function loginWithEmail() {
  loginError.value = '';
  try {
    await authStore.loginWithEmail(email.value, password.value);
  } catch (e) {
    loginError.value = e.message || 'Email login failed.';
  }
}


// --- No-Code Mode Visual Builder State ---
const supportedBlockTypes = [
  'button', 'input', 'label', 'image_display', 'container'
];
const visualBlocks = ref([]);

function addBlock(type) {
  const id = `block_${Date.now()}_${Math.floor(Math.random() * 10000)}`;
  let block = { id, type };
  // Add minimal required fields for each type
  if (type === 'button') block.content = 'Button';
  if (type === 'input') block.placeholder = 'Input';
  if (type === 'label') block.content = 'Label';
  if (type === 'image_display') block.src = 'https://via.placeholder.com/150';
  if (type === 'container') block.children = [];
  visualBlocks.value.push(block);
  syncVisualBlocksToJson();
}
function removeBlock(idx) {
  visualBlocks.value.splice(idx, 1);
  syncVisualBlocksToJson();
}
function syncVisualBlocksToJson() {
  // Try to update the JSON IDE with the new blocks
  try {
    const current = JSON.parse(jsonInput.value || '{}');
    current.ui_blocks = visualBlocks.value;
    jsonInput.value = JSON.stringify(current, null, 2);
    validateJsonInput();
  } catch (e) { /* ignore */ }
}
function hydrateVisualBlocksFromJson() {
  // Load ui_blocks from JSON into visualBlocks
  try {
    const current = JSON.parse(jsonInput.value || '{}');
    visualBlocks.value = Array.isArray(current.ui_blocks) ? [...current.ui_blocks] : [];
  } catch (e) {
    visualBlocks.value = [];
  }
}

import { useRouter, useRoute } from 'vue-router';
import { parseAndValidateExtensionJSON } from '../../extensions/validationService';
import { useExtensionsStore } from '../../store/extensionsStore';
import BlockRenderer from './preview/BlockRenderer.vue';

const props = defineProps({
  // extensionId prop is removed, will use route.params.id directly for clarity
  initialJson: { // This will be primarily used by ExtensionEditorView to pass loaded JSON
    type: String,
    default: ''
  }
});

const router = useRouter();
const route = useRoute();
const extensionsStore = useExtensionsStore();

const extensionIdFromRoute = ref(route.params.id || null);
const currentMode = ref('code'); // Default to code mode
const jsonInput = ref(''); // Initialize as empty, will be populated by onMounted or prop watch
const validationResult = ref(null);

// Watch for mode switches to sync visual blocks and JSON
watch(currentMode, (newMode, oldMode) => {
  if (newMode === 'nocode') {
    hydrateVisualBlocksFromJson();
  } else if (oldMode === 'nocode' && newMode === 'code') {
    syncVisualBlocksToJson();
  }
});
// Also watch jsonInput for external changes (e.g. loading a template)
watch(jsonInput, (val, oldVal) => {
  if (currentMode.value === 'nocode') {
    hydrateVisualBlocksFromJson();
  }
});

// On mount, hydrate blocks if needed
onMounted(() => {
  if (currentMode.value === 'nocode') {
    hydrateVisualBlocksFromJson();
  }
});

const eventLog = ref([]); // For the event debugger
const saveStatus = ref(null); // To show save success/error messages { type: 'success'/'error', message: '...' }


// Load initial data if editing an existing extension or if initialJson is passed (e.g. forked)
onMounted(async () => {
  if (extensionIdFromRoute.value) {
    // Fetch the specific extension if not already in initialJson (e.g. direct navigation to editor)
    // For now, assume ExtensionEditorView.vue handles pre-loading initialJson via prop
    // or we fetch it here.
    if (!props.initialJson) {
        const existingExtension = extensionsStore.getExtensionById(extensionIdFromRoute.value);
        if (existingExtension) {
            jsonInput.value = JSON.stringify(existingExtension, null, 2);
            validateJsonInput(); // Validate loaded JSON
        } else {
            // If not found (e.g. bad ID), show error or redirect. For now, log and leave empty.
            console.error(`Extension with ID ${extensionIdFromRoute.value} not found for editing.`);
            saveStatus.value = {type: 'error', message: `Error: Extension with ID ${extensionIdFromRoute.value} not found.`};
            jsonInput.value = ''; // Clear input if extension is not found
        }
    } else {
         jsonInput.value = props.initialJson;
         validateJsonInput();
    }
  } else if (props.initialJson) { // For create mode with a template/forked data
    jsonInput.value = props.initialJson;
    validateJsonInput();
  } else { // For create new, start with a basic template
    const defaultTemplate = {
        name: "My New Extension",
        version: "0.1.0",
        description: "A cool new extension.",
        ui_blocks: [],
        logic: {},
        permissions: [],
        tags: [],
        is_public: false, // Default to private
        // dev_metadata will be added by the store on save
    };
    jsonInput.value = JSON.stringify(defaultTemplate, null, 2);
    validateJsonInput();
  }
});


const canSaveChanges = computed(() => {
  return validationResult.value?.isValid && jsonInput.value.trim() !== '';
});

async function handleSaveExtension() {
  if (!canSaveChanges.value) {
    saveStatus.value = { type: 'error', message: 'JSON is invalid or empty. Cannot save.' };
    setTimeout(() => saveStatus.value = null, 3000);
    return;
  }

  if (!authStore.isDev) {
    saveStatus.value = { type: 'error', message: 'Only developers can save extensions.' };
    setTimeout(() => saveStatus.value = null, 3000);
    return;
  }

  try {
    const currentData = JSON.parse(jsonInput.value);
    const extensionToSave = { ...currentData };

    if (extensionIdFromRoute.value) { // If we are editing an existing extension
      extensionToSave.id = extensionIdFromRoute.value;
    }
    // If it's a new extension, extensionToSave.id will be undefined here,
    // and the store's saveExtension will generate one.

    const savedExtension = await extensionsStore.saveExtension(extensionToSave);

    if (savedExtension) {
      saveStatus.value = { type: 'success', message: 'Extension saved successfully!' };
      // If it was a new extension, the store assigned an ID. We should navigate to its editor page.
      if (!extensionIdFromRoute.value && savedExtension.id) {
        extensionIdFromRoute.value = savedExtension.id; // Update internal ID tracking
        // Update URL without full page reload, to reflect the new ID for subsequent saves
        router.replace({ name: 'ExtensionEditor', params: { id: savedExtension.id } });
      }
      // Update jsonInput with potentially modified data from store (e.g., updated timestamps)
      // This ensures consistency if the store modifies the object (like adding dev_metadata).
      jsonInput.value = JSON.stringify(savedExtension, null, 2);
      validateJsonInput(); // Re-validate, though it should still be valid
    } else {
      saveStatus.value = { type: 'error', message: extensionsStore.error || 'Failed to save extension.' };
    }
  } catch (e) {
    console.error("Error saving extension:", e);
    saveStatus.value = { type: 'error', message: `Error saving: ${e.message}` };
  }
  setTimeout(() => saveStatus.value = null, 4000); // Clear status message after a few seconds
}


// Debounce function
function debounce(func, delay) {
  let timeout;
  return (...args) => {
    clearTimeout(timeout);
    timeout = setTimeout(() => func.apply(this, args), delay);
  };
}

const debouncedValidate = debounce(() => {
  if (jsonInput.value.trim() === '') {
    validationResult.value = null; // Clear errors if input is empty
    eventLog.value = []; // Clear event log too
    return;
  }
  validationResult.value = parseAndValidateExtensionJSON(jsonInput.value);
  if (!validationResult.value.isValid) {
    eventLog.value = []; // Clear event log if JSON is invalid
  }
}, 500); // Validate 500ms after user stops typing

function validateJsonInput() {
  debouncedValidate();
}

const previewableUiBlocks = computed(() => {
  if (validationResult.value && validationResult.value.isValid && validationResult.value.data) {
    return validationResult.value.data.ui_blocks || [];
  }
  return [];
});

function logBlockEvent(eventDetails) {
  console.log("Block event received in layout:", eventDetails);
  eventLog.value.unshift(eventDetails); // Add to the beginning of the array
  if (eventLog.value.length > 20) { // Keep log size manageable
    eventLog.value.pop();
  }
}

// Initial validation if initialJson is provided
if (props.initialJson) {
  jsonInput.value = props.initialJson; // Ensure jsonInput is set before validation
  validateJsonInput();
}

// Watch for changes in initialJson if it's loaded asynchronously (e.g. in editor)
watch(() => props.initialJson, (newVal) => {
  if (newVal !== jsonInput.value) {
    jsonInput.value = newVal;
    validateJsonInput(); // This will also update previewableUiBlocks due to computed property
  }
});


// TODO: When switching from no-code to code, jsonInput should be updated with generated JSON
// TODO: When switching from code to no-code, if JSON is valid, it could populate the visual builder
</script>

<style scoped>
.extension-builder {
  display: flex;
  flex-direction: column;
  height: calc(100vh - 100px); /* Adjust based on actual header/nav height */
  border: 1px solid #ccc;
  padding: 10px;
}

.builder-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-bottom: 10px;
  border-bottom: 1px solid #ddd;
}

.builder-toolbar h2 {
  margin: 0;
}
.extension-id-display {
  font-size: 0.7em;
  color: #555;
  font-weight: normal;
}

.toolbar-actions {
  display: flex;
  align-items: center;
}

.toolbar-actions .mode-toggle {
  padding: 5px 10px;
  margin-left: 5px;
  cursor: pointer;
  border: 1px solid #ddd;
  background-color: #f0f0f0;
}
.toolbar-actions .mode-toggle.active {
  background-color: #007bff;
  color: white;
  border-color: #007bff;
}

.action-button {
  padding: 6px 12px;
  margin-right: 10px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.9em;
  display: flex;
  align-items: center;
}
.action-button .fa-save {
  margin-right: 5px;
}

.save-button {
  background-color: #28a745;
  color: white;
}
.save-button:hover:not(:disabled) {
  background-color: #218838;
}
.save-button:disabled {
  background-color: #ccc;
  cursor: not-allowed;
}

.save-status-toast {
  padding: 10px 15px;
  border-radius: 4px;
  color: white;
  text-align: center;
  margin: 10px 0;
  font-size: 0.95em;
}
.save-status-toast.success {
  background-color: #28a745;
}
.save-status-toast.error {
  background-color: #dc3545;
}


.builder-main-area {
  display: flex;
  flex-grow: 1;
  margin-top: 10px;
  overflow: hidden; /* Prevent builder from overflowing page */
}

.builder-content {
  flex-grow: 3; /* Takes more space */
  padding-right: 10px;
  border-right: 1px solid #ddd;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
}

.code-mode-ide {
  display: flex;
  flex-direction: column;
  flex-grow: 1; /* Allow textarea and messages to fill space */
}

.code-mode-ide textarea {
  width: 100%;
  flex-grow: 1; /* Textarea takes available space */
  margin-top: 10px;
  border: 1px solid #ccc;
  font-family: monospace;
  padding: 8px;
  box-sizing: border-box; /* Include padding and border in the element's total width and height */
}

.validation-errors {
  margin-top: 10px;
  padding: 10px;
  background-color: #f8d7da;
  border: 1px solid #f5c6cb;
  color: #721c24;
  border-radius: 4px;
  max-height: 150px; /* Limit height and make it scrollable if needed */
  overflow-y: auto;
}

.validation-errors h4 {
  margin-top: 0;
  margin-bottom: 5px;
}

.validation-errors ul {
  margin: 0;
  padding-left: 20px;
}

.validation-success {
  margin-top: 10px;
  padding: 10px;
  background-color: #d4edda;
  border: 1px solid #c3e6cb;
  color: #155724;
  border-radius: 4px;
}


.nocode-mode-visual-generator .nocode-layout {
  display: flex;
  margin-top: 10px;
}

.nocode-canvas-area {
  flex-grow: 2;
  padding: 10px;
  border: 1px dashed #ccc;
  margin-right: 10px;
  min-height: 300px;
}

.canvas-placeholder {
  background-color: #f9f9f9;
  text-align: center;
  padding: 20px;
  color: #777;
}

.nocode-tools-palette {
  flex-grow: 1;
  padding: 10px;
  border: 1px solid #eee;
  background-color: #fcfcfc;
}

.nocode-tools-palette ul {
  list-style: none;
  padding: 0;
}
.nocode-tools-palette li {
  padding: 5px;
  border: 1px solid #eee;
  margin-bottom: 5px;
  background-color: #fff;
  cursor: grab;
}


.builder-sidebar {
  flex-grow: 1; /* Takes less space */
  padding-left: 10px;
  overflow-y: auto;
}

.sidebar-section {
  margin-bottom: 15px;
  padding: 10px;
  border: 1px solid #eee;
  background-color: #f9f9f9;
}

.sidebar-section h4 {
  margin-top: 0;
  margin-bottom: 5px;
  font-size: 1.1em;
}

.preview-placeholder,
.debugger-placeholder,
.inspector-placeholder {
  min-height: 100px;
  background-color: #e9e9e9;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #666;
  font-style: italic;
}

.preview-content {
  width: 100%;
  height: 100%;
  overflow: auto; /* If content overflows */
}

.event-log-list {
  list-style-type: none;
  padding: 0;
  margin: 0;
  font-size: 0.9em;
  max-height: 200px; /* Limit height and make scrollable */
  overflow-y: auto;
}
.event-log-list li {
  padding: 4px;
  border-bottom: 1px solid #eee;
  word-break: break-all;
}
.event-log-list li:last-child {
  border-bottom: none;
}
</style>
