<template>
  <nav class="breadcrumb" v-if="breadcrumbs.length > 1">
    <ol class="breadcrumb-list">
      <li 
        v-for="(crumb, index) in breadcrumbs" 
        :key="index"
        class="breadcrumb-item"
        :class="{ 'active': index === breadcrumbs.length - 1 }"
      >
        <router-link 
          v-if="index < breadcrumbs.length - 1 && crumb.to" 
          :to="crumb.to"
          class="breadcrumb-link"
        >
          <i v-if="crumb.icon" :class="crumb.icon" class="breadcrumb-icon"></i>
          {{ crumb.text }}
        </router-link>
        <span v-else class="breadcrumb-current">
          <i v-if="crumb.icon" :class="crumb.icon" class="breadcrumb-icon"></i>
          {{ crumb.text }}
        </span>
        <i 
          v-if="index < breadcrumbs.length - 1" 
          class="fas fa-chevron-right breadcrumb-separator"
        ></i>
      </li>
    </ol>
  </nav>
</template>

<script setup>
import { computed } from 'vue'
import { useRoute } from 'vue-router'

const route = useRoute()

const breadcrumbs = computed(() => {
  const crumbs = [
    { text: 'Home', to: '/', icon: 'fas fa-home' }
  ]

  const path = route.path
  const name = route.name
  const params = route.params

  // Add breadcrumbs based on current route
  if (path === '/') {
    return [{ text: 'Home', icon: 'fas fa-home' }]
  }

  if (path === '/join') {
    crumbs.push({ text: 'Join Meeting', icon: 'fas fa-sign-in-alt' })
  } else if (path.startsWith('/meeting/')) {
    crumbs.push({ text: 'Meeting', to: '/join', icon: 'fas fa-video' })
    crumbs.push({ text: `Meeting ${params.id}`, icon: 'fas fa-users' })
  } else if (path === '/marketplace') {
    crumbs.push({ text: 'Marketplace', icon: 'fas fa-store' })
  } else if (path === '/extensions') {
    crumbs.push({ text: 'Extensions', icon: 'fas fa-puzzle-piece' })
  } else if (path.startsWith('/extensions/')) {
    crumbs.push({ text: 'Extensions', to: '/extensions', icon: 'fas fa-puzzle-piece' })
    
    if (path === '/extensions/create') {
      crumbs.push({ text: 'Create Extension', icon: 'fas fa-plus' })
    } else if (path.startsWith('/extensions/editor/')) {
      crumbs.push({ text: 'Extension Editor', icon: 'fas fa-code' })
      if (params.id) {
        crumbs.push({ text: `Edit ${params.id}`, icon: 'fas fa-edit' })
      }
    } else if (params.id) {
      crumbs.push({ text: 'Extension Details', icon: 'fas fa-info-circle' })
    }
  } else if (path === '/dashboard') {
    crumbs.push({ text: 'Developer Dashboard', icon: 'fas fa-tachometer-alt' })
  } else if (path.startsWith('/profile/')) {
    crumbs.push({ text: 'User Profile', icon: 'fas fa-user' })
    if (params.username) {
      crumbs.push({ text: params.username, icon: 'fas fa-user-circle' })
    }
  } else if (path.startsWith('/analytics/')) {
    crumbs.push({ text: 'Analytics', icon: 'fas fa-chart-bar' })
    if (params.id) {
      crumbs.push({ text: `Analytics ${params.id}`, icon: 'fas fa-analytics' })
    }
  } else if (path === '/auth/login') {
    crumbs.push({ text: 'Login', icon: 'fas fa-sign-in-alt' })
  }

  return crumbs
})
</script>

<style scoped>
.breadcrumb {
  background-color: #f8f9fa;
  border-bottom: 1px solid #e9ecef;
  padding: 0.75rem 1rem;
}

.dark .breadcrumb {
  background-color: #111827;
  border-bottom-color: #374151;
}

.breadcrumb-list {
  display: flex;
  align-items: center;
  list-style: none;
  margin: 0;
  padding: 0;
  flex-wrap: wrap;
}

.breadcrumb-item {
  display: flex;
  align-items: center;
}

.breadcrumb-link {
  color: #6b7280;
  text-decoration: none;
  font-size: 0.875rem;
  font-weight: 500;
  display: flex;
  align-items: center;
  padding: 0.25rem 0.5rem;
  border-radius: 0.25rem;
  transition: all 0.2s;
}

.breadcrumb-link:hover {
  color: #007bff;
  background-color: #e3f2fd;
}

.dark .breadcrumb-link {
  color: #9ca3af;
}

.dark .breadcrumb-link:hover {
  color: #60a5fa;
  background-color: #1e3a8a;
}

.breadcrumb-current {
  color: #374151;
  font-size: 0.875rem;
  font-weight: 600;
  display: flex;
  align-items: center;
  padding: 0.25rem 0.5rem;
}

.dark .breadcrumb-current {
  color: #f3f4f6;
}

.breadcrumb-icon {
  margin-right: 0.5rem;
  font-size: 0.75rem;
}

.breadcrumb-separator {
  color: #9ca3af;
  font-size: 0.75rem;
  margin: 0 0.5rem;
}

.dark .breadcrumb-separator {
  color: #6b7280;
}

.breadcrumb-item.active .breadcrumb-current {
  color: #007bff;
}

.dark .breadcrumb-item.active .breadcrumb-current {
  color: #60a5fa;
}
</style>
