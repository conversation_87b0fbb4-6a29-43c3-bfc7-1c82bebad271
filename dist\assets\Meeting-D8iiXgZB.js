import{q as Qi,s as es,x as ue,y as Q,z as tr,A as nt,B as Rt,C as Et,D as ts,r as q,c as V,E as jt,a as j,f as N,m as P,b as d,n as H,p as W,t as $,o as lr,G as rs,H as jn,i as Tt,v as rr,_ as cr,e as we,I as Mn,J as ns,d as se,F as me,k as xe,h as D,K as is,w as An,L as _n,T as ss,M as os,u as as,N as ls,l as cs,O as us,P as ds,Q as fs,g as tt,R as yn,S as Mr}from"./index-DwK_bd6V.js";import{u as ps}from"./meetingConfigStore-al87t19p.js";import{l as hs,b as gs,d as ms,s as Qt}from"./meetings-BW_Clq8y.js";import"./extensionsStore-D2iLnSxl.js";function vs(n,o){for(var i=0;i<o.length;i++){const r=o[i];if(typeof r!="string"&&!Array.isArray(r)){for(const a in r)if(a!=="default"&&!(a in n)){const c=Object.getOwnPropertyDescriptor(r,a);c&&Object.defineProperty(n,a,c.get?c:{enumerable:!0,get:()=>r[a]})}}}return Object.freeze(Object.defineProperty(n,Symbol.toStringTag,{value:"Module"}))}const ys=Qi("meeting",{state:()=>({meetingId:null,localStream:null,remoteStreams:{},isMicMuted:!1,isCameraOff:!1,isScreenSharing:!1,participants:[],messages:[],connectionStatus:"disconnected"}),actions:{setMeetingId(n){this.meetingId=n},setLocalStream(n){this.localStream=n},addRemoteStream(n,o){this.remoteStreams[n]=o},removeRemoteStream(n){delete this.remoteStreams[n]},toggleMic(){this.isMicMuted=!this.isMicMuted,this.localStream&&this.localStream.getAudioTracks().forEach(n=>{n.enabled=!this.isMicMuted})},toggleCamera(){this.isCameraOff=!this.isCameraOff,this.localStream&&this.localStream.getVideoTracks().forEach(n=>{n.enabled=!this.isCameraOff})},toggleScreenSharing(){this.isScreenSharing=!this.isScreenSharing},addParticipant(n){this.participants.push(n)},removeParticipant(n){this.participants=this.participants.filter(o=>o.id!==n)},addMessage(n){this.messages.push(n)},setConnectionStatus(n){this.connectionStatus=n},reset(){this.meetingId=null,this.localStream=null,this.remoteStreams={},this.isMicMuted=!1,this.isCameraOff=!1,this.isScreenSharing=!1,this.participants=[],this.messages=[],this.connectionStatus="disconnected"}}});let In=!0,On=!0;function Pt(n,o,i){const r=n.match(o);return r&&r.length>=i&&parseFloat(r[i],10)}function qe(n,o,i){if(!n.RTCPeerConnection)return;const r=n.RTCPeerConnection.prototype,a=r.addEventListener;r.addEventListener=function(m,h){if(m!==o)return a.apply(this,arguments);const k=b=>{const g=i(b);g&&(h.handleEvent?h.handleEvent(g):h(g))};return this._eventMap=this._eventMap||{},this._eventMap[o]||(this._eventMap[o]=new Map),this._eventMap[o].set(h,k),a.apply(this,[m,k])};const c=r.removeEventListener;r.removeEventListener=function(m,h){if(m!==o||!this._eventMap||!this._eventMap[o])return c.apply(this,arguments);if(!this._eventMap[o].has(h))return c.apply(this,arguments);const k=this._eventMap[o].get(h);return this._eventMap[o].delete(h),this._eventMap[o].size===0&&delete this._eventMap[o],Object.keys(this._eventMap).length===0&&delete this._eventMap,c.apply(this,[m,k])},Object.defineProperty(r,"on"+o,{get(){return this["_on"+o]},set(m){this["_on"+o]&&(this.removeEventListener(o,this["_on"+o]),delete this["_on"+o]),m&&this.addEventListener(o,this["_on"+o]=m)},enumerable:!0,configurable:!0})}function bs(n){return typeof n!="boolean"?new Error("Argument type: "+typeof n+". Please use a boolean."):(In=n,n?"adapter.js logging disabled":"adapter.js logging enabled")}function xs(n){return typeof n!="boolean"?new Error("Argument type: "+typeof n+". Please use a boolean."):(On=!n,"adapter.js deprecation warnings "+(n?"disabled":"enabled"))}function Ln(){if(typeof window=="object"){if(In)return;typeof console<"u"&&typeof console.log=="function"&&console.log.apply(console,arguments)}}function Fr(n,o){On&&console.warn(n+" is deprecated, please use "+o+" instead.")}function ks(n){const o={browser:null,version:null};if(typeof n>"u"||!n.navigator||!n.navigator.userAgent)return o.browser="Not a browser.",o;const{navigator:i}=n;if(i.mozGetUserMedia)o.browser="firefox",o.version=parseInt(Pt(i.userAgent,/Firefox\/(\d+)\./,1));else if(i.webkitGetUserMedia||n.isSecureContext===!1&&n.webkitRTCPeerConnection)o.browser="chrome",o.version=parseInt(Pt(i.userAgent,/Chrom(e|ium)\/(\d+)\./,2));else if(n.RTCPeerConnection&&i.userAgent.match(/AppleWebKit\/(\d+)\./))o.browser="safari",o.version=parseInt(Pt(i.userAgent,/AppleWebKit\/(\d+)\./,1)),o.supportsUnifiedPlan=n.RTCRtpTransceiver&&"currentDirection"in n.RTCRtpTransceiver.prototype,o._safariVersion=Pt(i.userAgent,/Version\/(\d+(\.?\d+))/,1);else return o.browser="Not a supported browser.",o;return o}function bn(n){return Object.prototype.toString.call(n)==="[object Object]"}function Dn(n){return bn(n)?Object.keys(n).reduce(function(o,i){const r=bn(n[i]),a=r?Dn(n[i]):n[i],c=r&&!Object.keys(a).length;return a===void 0||c?o:Object.assign(o,{[i]:a})},{}):n}function Or(n,o,i){!o||i.has(o.id)||(i.set(o.id,o),Object.keys(o).forEach(r=>{r.endsWith("Id")?Or(n,n.get(o[r]),i):r.endsWith("Ids")&&o[r].forEach(a=>{Or(n,n.get(a),i)})}))}function xn(n,o,i){const r=i?"outbound-rtp":"inbound-rtp",a=new Map;if(o===null)return a;const c=[];return n.forEach(m=>{m.type==="track"&&m.trackIdentifier===o.id&&c.push(m)}),c.forEach(m=>{n.forEach(h=>{h.type===r&&h.trackId===m.id&&Or(n,h,a)})}),a}const kn=Ln;function $n(n,o){const i=n&&n.navigator;if(!i.mediaDevices)return;const r=function(h){if(typeof h!="object"||h.mandatory||h.optional)return h;const k={};return Object.keys(h).forEach(b=>{if(b==="require"||b==="advanced"||b==="mediaSource")return;const g=typeof h[b]=="object"?h[b]:{ideal:h[b]};g.exact!==void 0&&typeof g.exact=="number"&&(g.min=g.max=g.exact);const y=function(C,A){return C?C+A.charAt(0).toUpperCase()+A.slice(1):A==="deviceId"?"sourceId":A};if(g.ideal!==void 0){k.optional=k.optional||[];let C={};typeof g.ideal=="number"?(C[y("min",b)]=g.ideal,k.optional.push(C),C={},C[y("max",b)]=g.ideal,k.optional.push(C)):(C[y("",b)]=g.ideal,k.optional.push(C))}g.exact!==void 0&&typeof g.exact!="number"?(k.mandatory=k.mandatory||{},k.mandatory[y("",b)]=g.exact):["min","max"].forEach(C=>{g[C]!==void 0&&(k.mandatory=k.mandatory||{},k.mandatory[y(C,b)]=g[C])})}),h.advanced&&(k.optional=(k.optional||[]).concat(h.advanced)),k},a=function(h,k){if(o.version>=61)return k(h);if(h=JSON.parse(JSON.stringify(h)),h&&typeof h.audio=="object"){const b=function(g,y,C){y in g&&!(C in g)&&(g[C]=g[y],delete g[y])};h=JSON.parse(JSON.stringify(h)),b(h.audio,"autoGainControl","googAutoGainControl"),b(h.audio,"noiseSuppression","googNoiseSuppression"),h.audio=r(h.audio)}if(h&&typeof h.video=="object"){let b=h.video.facingMode;b=b&&(typeof b=="object"?b:{ideal:b});const g=o.version<66;if(b&&(b.exact==="user"||b.exact==="environment"||b.ideal==="user"||b.ideal==="environment")&&!(i.mediaDevices.getSupportedConstraints&&i.mediaDevices.getSupportedConstraints().facingMode&&!g)){delete h.video.facingMode;let y;if(b.exact==="environment"||b.ideal==="environment"?y=["back","rear"]:(b.exact==="user"||b.ideal==="user")&&(y=["front"]),y)return i.mediaDevices.enumerateDevices().then(C=>{C=C.filter(U=>U.kind==="videoinput");let A=C.find(U=>y.some(K=>U.label.toLowerCase().includes(K)));return!A&&C.length&&y.includes("back")&&(A=C[C.length-1]),A&&(h.video.deviceId=b.exact?{exact:A.deviceId}:{ideal:A.deviceId}),h.video=r(h.video),kn("chrome: "+JSON.stringify(h)),k(h)})}h.video=r(h.video)}return kn("chrome: "+JSON.stringify(h)),k(h)},c=function(h){return o.version>=64?h:{name:{PermissionDeniedError:"NotAllowedError",PermissionDismissedError:"NotAllowedError",InvalidStateError:"NotAllowedError",DevicesNotFoundError:"NotFoundError",ConstraintNotSatisfiedError:"OverconstrainedError",TrackStartError:"NotReadableError",MediaDeviceFailedDueToShutdown:"NotAllowedError",MediaDeviceKillSwitchOn:"NotAllowedError",TabCaptureError:"AbortError",ScreenCaptureError:"AbortError",DeviceCaptureError:"AbortError"}[h.name]||h.name,message:h.message,constraint:h.constraint||h.constraintName,toString(){return this.name+(this.message&&": ")+this.message}}},m=function(h,k,b){a(h,g=>{i.webkitGetUserMedia(g,k,y=>{b&&b(c(y))})})};if(i.getUserMedia=m.bind(i),i.mediaDevices.getUserMedia){const h=i.mediaDevices.getUserMedia.bind(i.mediaDevices);i.mediaDevices.getUserMedia=function(k){return a(k,b=>h(b).then(g=>{if(b.audio&&!g.getAudioTracks().length||b.video&&!g.getVideoTracks().length)throw g.getTracks().forEach(y=>{y.stop()}),new DOMException("","NotFoundError");return g},g=>Promise.reject(c(g))))}}}function Ss(n,o){if(!(n.navigator.mediaDevices&&"getDisplayMedia"in n.navigator.mediaDevices)&&n.navigator.mediaDevices){if(typeof o!="function"){console.error("shimGetDisplayMedia: getSourceId argument is not a function");return}n.navigator.mediaDevices.getDisplayMedia=function(r){return o(r).then(a=>{const c=r.video&&r.video.width,m=r.video&&r.video.height,h=r.video&&r.video.frameRate;return r.video={mandatory:{chromeMediaSource:"desktop",chromeMediaSourceId:a,maxFrameRate:h||3}},c&&(r.video.mandatory.maxWidth=c),m&&(r.video.mandatory.maxHeight=m),n.navigator.mediaDevices.getUserMedia(r)})}}}function wn(n){n.MediaStream=n.MediaStream||n.webkitMediaStream}function Un(n){if(typeof n=="object"&&n.RTCPeerConnection&&!("ontrack"in n.RTCPeerConnection.prototype)){Object.defineProperty(n.RTCPeerConnection.prototype,"ontrack",{get(){return this._ontrack},set(i){this._ontrack&&this.removeEventListener("track",this._ontrack),this.addEventListener("track",this._ontrack=i)},enumerable:!0,configurable:!0});const o=n.RTCPeerConnection.prototype.setRemoteDescription;n.RTCPeerConnection.prototype.setRemoteDescription=function(){return this._ontrackpoly||(this._ontrackpoly=r=>{r.stream.addEventListener("addtrack",a=>{let c;n.RTCPeerConnection.prototype.getReceivers?c=this.getReceivers().find(h=>h.track&&h.track.id===a.track.id):c={track:a.track};const m=new Event("track");m.track=a.track,m.receiver=c,m.transceiver={receiver:c},m.streams=[r.stream],this.dispatchEvent(m)}),r.stream.getTracks().forEach(a=>{let c;n.RTCPeerConnection.prototype.getReceivers?c=this.getReceivers().find(h=>h.track&&h.track.id===a.id):c={track:a};const m=new Event("track");m.track=a,m.receiver=c,m.transceiver={receiver:c},m.streams=[r.stream],this.dispatchEvent(m)})},this.addEventListener("addstream",this._ontrackpoly)),o.apply(this,arguments)}}else qe(n,"track",o=>(o.transceiver||Object.defineProperty(o,"transceiver",{value:{receiver:o.receiver}}),o))}function Fn(n){if(typeof n=="object"&&n.RTCPeerConnection&&!("getSenders"in n.RTCPeerConnection.prototype)&&"createDTMFSender"in n.RTCPeerConnection.prototype){const o=function(a,c){return{track:c,get dtmf(){return this._dtmf===void 0&&(c.kind==="audio"?this._dtmf=a.createDTMFSender(c):this._dtmf=null),this._dtmf},_pc:a}};if(!n.RTCPeerConnection.prototype.getSenders){n.RTCPeerConnection.prototype.getSenders=function(){return this._senders=this._senders||[],this._senders.slice()};const a=n.RTCPeerConnection.prototype.addTrack;n.RTCPeerConnection.prototype.addTrack=function(h,k){let b=a.apply(this,arguments);return b||(b=o(this,h),this._senders.push(b)),b};const c=n.RTCPeerConnection.prototype.removeTrack;n.RTCPeerConnection.prototype.removeTrack=function(h){c.apply(this,arguments);const k=this._senders.indexOf(h);k!==-1&&this._senders.splice(k,1)}}const i=n.RTCPeerConnection.prototype.addStream;n.RTCPeerConnection.prototype.addStream=function(c){this._senders=this._senders||[],i.apply(this,[c]),c.getTracks().forEach(m=>{this._senders.push(o(this,m))})};const r=n.RTCPeerConnection.prototype.removeStream;n.RTCPeerConnection.prototype.removeStream=function(c){this._senders=this._senders||[],r.apply(this,[c]),c.getTracks().forEach(m=>{const h=this._senders.find(k=>k.track===m);h&&this._senders.splice(this._senders.indexOf(h),1)})}}else if(typeof n=="object"&&n.RTCPeerConnection&&"getSenders"in n.RTCPeerConnection.prototype&&"createDTMFSender"in n.RTCPeerConnection.prototype&&n.RTCRtpSender&&!("dtmf"in n.RTCRtpSender.prototype)){const o=n.RTCPeerConnection.prototype.getSenders;n.RTCPeerConnection.prototype.getSenders=function(){const r=o.apply(this,[]);return r.forEach(a=>a._pc=this),r},Object.defineProperty(n.RTCRtpSender.prototype,"dtmf",{get(){return this._dtmf===void 0&&(this.track.kind==="audio"?this._dtmf=this._pc.createDTMFSender(this.track):this._dtmf=null),this._dtmf}})}}function Nn(n){if(!n.RTCPeerConnection)return;const o=n.RTCPeerConnection.prototype.getStats;n.RTCPeerConnection.prototype.getStats=function(){const[r,a,c]=arguments;if(arguments.length>0&&typeof r=="function")return o.apply(this,arguments);if(o.length===0&&(arguments.length===0||typeof r!="function"))return o.apply(this,[]);const m=function(k){const b={};return k.result().forEach(y=>{const C={id:y.id,timestamp:y.timestamp,type:{localcandidate:"local-candidate",remotecandidate:"remote-candidate"}[y.type]||y.type};y.names().forEach(A=>{C[A]=y.stat(A)}),b[C.id]=C}),b},h=function(k){return new Map(Object.keys(k).map(b=>[b,k[b]]))};if(arguments.length>=2){const k=function(b){a(h(m(b)))};return o.apply(this,[k,r])}return new Promise((k,b)=>{o.apply(this,[function(g){k(h(m(g)))},b])}).then(a,c)}}function Bn(n){if(!(typeof n=="object"&&n.RTCPeerConnection&&n.RTCRtpSender&&n.RTCRtpReceiver))return;if(!("getStats"in n.RTCRtpSender.prototype)){const i=n.RTCPeerConnection.prototype.getSenders;i&&(n.RTCPeerConnection.prototype.getSenders=function(){const c=i.apply(this,[]);return c.forEach(m=>m._pc=this),c});const r=n.RTCPeerConnection.prototype.addTrack;r&&(n.RTCPeerConnection.prototype.addTrack=function(){const c=r.apply(this,arguments);return c._pc=this,c}),n.RTCRtpSender.prototype.getStats=function(){const c=this;return this._pc.getStats().then(m=>xn(m,c.track,!0))}}if(!("getStats"in n.RTCRtpReceiver.prototype)){const i=n.RTCPeerConnection.prototype.getReceivers;i&&(n.RTCPeerConnection.prototype.getReceivers=function(){const a=i.apply(this,[]);return a.forEach(c=>c._pc=this),a}),qe(n,"track",r=>(r.receiver._pc=r.srcElement,r)),n.RTCRtpReceiver.prototype.getStats=function(){const a=this;return this._pc.getStats().then(c=>xn(c,a.track,!1))}}if(!("getStats"in n.RTCRtpSender.prototype&&"getStats"in n.RTCRtpReceiver.prototype))return;const o=n.RTCPeerConnection.prototype.getStats;n.RTCPeerConnection.prototype.getStats=function(){if(arguments.length>0&&arguments[0]instanceof n.MediaStreamTrack){const r=arguments[0];let a,c,m;return this.getSenders().forEach(h=>{h.track===r&&(a?m=!0:a=h)}),this.getReceivers().forEach(h=>(h.track===r&&(c?m=!0:c=h),h.track===r)),m||a&&c?Promise.reject(new DOMException("There are more than one sender or receiver for the track.","InvalidAccessError")):a?a.getStats():c?c.getStats():Promise.reject(new DOMException("There is no sender or receiver for the track.","InvalidAccessError"))}return o.apply(this,arguments)}}function zn(n){n.RTCPeerConnection.prototype.getLocalStreams=function(){return this._shimmedLocalStreams=this._shimmedLocalStreams||{},Object.keys(this._shimmedLocalStreams).map(m=>this._shimmedLocalStreams[m][0])};const o=n.RTCPeerConnection.prototype.addTrack;n.RTCPeerConnection.prototype.addTrack=function(m,h){if(!h)return o.apply(this,arguments);this._shimmedLocalStreams=this._shimmedLocalStreams||{};const k=o.apply(this,arguments);return this._shimmedLocalStreams[h.id]?this._shimmedLocalStreams[h.id].indexOf(k)===-1&&this._shimmedLocalStreams[h.id].push(k):this._shimmedLocalStreams[h.id]=[h,k],k};const i=n.RTCPeerConnection.prototype.addStream;n.RTCPeerConnection.prototype.addStream=function(m){this._shimmedLocalStreams=this._shimmedLocalStreams||{},m.getTracks().forEach(b=>{if(this.getSenders().find(y=>y.track===b))throw new DOMException("Track already exists.","InvalidAccessError")});const h=this.getSenders();i.apply(this,arguments);const k=this.getSenders().filter(b=>h.indexOf(b)===-1);this._shimmedLocalStreams[m.id]=[m].concat(k)};const r=n.RTCPeerConnection.prototype.removeStream;n.RTCPeerConnection.prototype.removeStream=function(m){return this._shimmedLocalStreams=this._shimmedLocalStreams||{},delete this._shimmedLocalStreams[m.id],r.apply(this,arguments)};const a=n.RTCPeerConnection.prototype.removeTrack;n.RTCPeerConnection.prototype.removeTrack=function(m){return this._shimmedLocalStreams=this._shimmedLocalStreams||{},m&&Object.keys(this._shimmedLocalStreams).forEach(h=>{const k=this._shimmedLocalStreams[h].indexOf(m);k!==-1&&this._shimmedLocalStreams[h].splice(k,1),this._shimmedLocalStreams[h].length===1&&delete this._shimmedLocalStreams[h]}),a.apply(this,arguments)}}function Vn(n,o){if(!n.RTCPeerConnection)return;if(n.RTCPeerConnection.prototype.addTrack&&o.version>=65)return zn(n);const i=n.RTCPeerConnection.prototype.getLocalStreams;n.RTCPeerConnection.prototype.getLocalStreams=function(){const g=i.apply(this);return this._reverseStreams=this._reverseStreams||{},g.map(y=>this._reverseStreams[y.id])};const r=n.RTCPeerConnection.prototype.addStream;n.RTCPeerConnection.prototype.addStream=function(g){if(this._streams=this._streams||{},this._reverseStreams=this._reverseStreams||{},g.getTracks().forEach(y=>{if(this.getSenders().find(A=>A.track===y))throw new DOMException("Track already exists.","InvalidAccessError")}),!this._reverseStreams[g.id]){const y=new n.MediaStream(g.getTracks());this._streams[g.id]=y,this._reverseStreams[y.id]=g,g=y}r.apply(this,[g])};const a=n.RTCPeerConnection.prototype.removeStream;n.RTCPeerConnection.prototype.removeStream=function(g){this._streams=this._streams||{},this._reverseStreams=this._reverseStreams||{},a.apply(this,[this._streams[g.id]||g]),delete this._reverseStreams[this._streams[g.id]?this._streams[g.id].id:g.id],delete this._streams[g.id]},n.RTCPeerConnection.prototype.addTrack=function(g,y){if(this.signalingState==="closed")throw new DOMException("The RTCPeerConnection's signalingState is 'closed'.","InvalidStateError");const C=[].slice.call(arguments,1);if(C.length!==1||!C[0].getTracks().find(K=>K===g))throw new DOMException("The adapter.js addTrack polyfill only supports a single  stream which is associated with the specified track.","NotSupportedError");if(this.getSenders().find(K=>K.track===g))throw new DOMException("Track already exists.","InvalidAccessError");this._streams=this._streams||{},this._reverseStreams=this._reverseStreams||{};const U=this._streams[y.id];if(U)U.addTrack(g),Promise.resolve().then(()=>{this.dispatchEvent(new Event("negotiationneeded"))});else{const K=new n.MediaStream([g]);this._streams[y.id]=K,this._reverseStreams[K.id]=y,this.addStream(K)}return this.getSenders().find(K=>K.track===g)};function c(b,g){let y=g.sdp;return Object.keys(b._reverseStreams||[]).forEach(C=>{const A=b._reverseStreams[C],U=b._streams[A.id];y=y.replace(new RegExp(U.id,"g"),A.id)}),new RTCSessionDescription({type:g.type,sdp:y})}function m(b,g){let y=g.sdp;return Object.keys(b._reverseStreams||[]).forEach(C=>{const A=b._reverseStreams[C],U=b._streams[A.id];y=y.replace(new RegExp(A.id,"g"),U.id)}),new RTCSessionDescription({type:g.type,sdp:y})}["createOffer","createAnswer"].forEach(function(b){const g=n.RTCPeerConnection.prototype[b],y={[b](){const C=arguments;return arguments.length&&typeof arguments[0]=="function"?g.apply(this,[U=>{const K=c(this,U);C[0].apply(null,[K])},U=>{C[1]&&C[1].apply(null,U)},arguments[2]]):g.apply(this,arguments).then(U=>c(this,U))}};n.RTCPeerConnection.prototype[b]=y[b]});const h=n.RTCPeerConnection.prototype.setLocalDescription;n.RTCPeerConnection.prototype.setLocalDescription=function(){return!arguments.length||!arguments[0].type?h.apply(this,arguments):(arguments[0]=m(this,arguments[0]),h.apply(this,arguments))};const k=Object.getOwnPropertyDescriptor(n.RTCPeerConnection.prototype,"localDescription");Object.defineProperty(n.RTCPeerConnection.prototype,"localDescription",{get(){const b=k.get.apply(this);return b.type===""?b:c(this,b)}}),n.RTCPeerConnection.prototype.removeTrack=function(g){if(this.signalingState==="closed")throw new DOMException("The RTCPeerConnection's signalingState is 'closed'.","InvalidStateError");if(!g._pc)throw new DOMException("Argument 1 of RTCPeerConnection.removeTrack does not implement interface RTCRtpSender.","TypeError");if(!(g._pc===this))throw new DOMException("Sender was not created by this connection.","InvalidAccessError");this._streams=this._streams||{};let C;Object.keys(this._streams).forEach(A=>{this._streams[A].getTracks().find(K=>g.track===K)&&(C=this._streams[A])}),C&&(C.getTracks().length===1?this.removeStream(this._reverseStreams[C.id]):C.removeTrack(g.track),this.dispatchEvent(new Event("negotiationneeded")))}}function Lr(n,o){!n.RTCPeerConnection&&n.webkitRTCPeerConnection&&(n.RTCPeerConnection=n.webkitRTCPeerConnection),n.RTCPeerConnection&&o.version<53&&["setLocalDescription","setRemoteDescription","addIceCandidate"].forEach(function(i){const r=n.RTCPeerConnection.prototype[i],a={[i](){return arguments[0]=new(i==="addIceCandidate"?n.RTCIceCandidate:n.RTCSessionDescription)(arguments[0]),r.apply(this,arguments)}};n.RTCPeerConnection.prototype[i]=a[i]})}function Gn(n,o){qe(n,"negotiationneeded",i=>{const r=i.target;if(!((o.version<72||r.getConfiguration&&r.getConfiguration().sdpSemantics==="plan-b")&&r.signalingState!=="stable"))return i})}const Sn=Object.freeze(Object.defineProperty({__proto__:null,fixNegotiationNeeded:Gn,shimAddTrackRemoveTrack:Vn,shimAddTrackRemoveTrackWithNative:zn,shimGetDisplayMedia:Ss,shimGetSendersWithDtmf:Fn,shimGetStats:Nn,shimGetUserMedia:$n,shimMediaStream:wn,shimOnTrack:Un,shimPeerConnection:Lr,shimSenderReceiverGetStats:Bn},Symbol.toStringTag,{value:"Module"}));function Jn(n,o){const i=n&&n.navigator,r=n&&n.MediaStreamTrack;if(i.getUserMedia=function(a,c,m){Fr("navigator.getUserMedia","navigator.mediaDevices.getUserMedia"),i.mediaDevices.getUserMedia(a).then(c,m)},!(o.version>55&&"autoGainControl"in i.mediaDevices.getSupportedConstraints())){const a=function(m,h,k){h in m&&!(k in m)&&(m[k]=m[h],delete m[h])},c=i.mediaDevices.getUserMedia.bind(i.mediaDevices);if(i.mediaDevices.getUserMedia=function(m){return typeof m=="object"&&typeof m.audio=="object"&&(m=JSON.parse(JSON.stringify(m)),a(m.audio,"autoGainControl","mozAutoGainControl"),a(m.audio,"noiseSuppression","mozNoiseSuppression")),c(m)},r&&r.prototype.getSettings){const m=r.prototype.getSettings;r.prototype.getSettings=function(){const h=m.apply(this,arguments);return a(h,"mozAutoGainControl","autoGainControl"),a(h,"mozNoiseSuppression","noiseSuppression"),h}}if(r&&r.prototype.applyConstraints){const m=r.prototype.applyConstraints;r.prototype.applyConstraints=function(h){return this.kind==="audio"&&typeof h=="object"&&(h=JSON.parse(JSON.stringify(h)),a(h,"autoGainControl","mozAutoGainControl"),a(h,"noiseSuppression","mozNoiseSuppression")),m.apply(this,[h])}}}}function Cs(n,o){n.navigator.mediaDevices&&"getDisplayMedia"in n.navigator.mediaDevices||n.navigator.mediaDevices&&(n.navigator.mediaDevices.getDisplayMedia=function(r){if(!(r&&r.video)){const a=new DOMException("getDisplayMedia without video constraints is undefined");return a.name="NotFoundError",a.code=8,Promise.reject(a)}return r.video===!0?r.video={mediaSource:o}:r.video.mediaSource=o,n.navigator.mediaDevices.getUserMedia(r)})}function Hn(n){typeof n=="object"&&n.RTCTrackEvent&&"receiver"in n.RTCTrackEvent.prototype&&!("transceiver"in n.RTCTrackEvent.prototype)&&Object.defineProperty(n.RTCTrackEvent.prototype,"transceiver",{get(){return{receiver:this.receiver}}})}function Dr(n,o){if(typeof n!="object"||!(n.RTCPeerConnection||n.mozRTCPeerConnection))return;!n.RTCPeerConnection&&n.mozRTCPeerConnection&&(n.RTCPeerConnection=n.mozRTCPeerConnection),o.version<53&&["setLocalDescription","setRemoteDescription","addIceCandidate"].forEach(function(a){const c=n.RTCPeerConnection.prototype[a],m={[a](){return arguments[0]=new(a==="addIceCandidate"?n.RTCIceCandidate:n.RTCSessionDescription)(arguments[0]),c.apply(this,arguments)}};n.RTCPeerConnection.prototype[a]=m[a]});const i={inboundrtp:"inbound-rtp",outboundrtp:"outbound-rtp",candidatepair:"candidate-pair",localcandidate:"local-candidate",remotecandidate:"remote-candidate"},r=n.RTCPeerConnection.prototype.getStats;n.RTCPeerConnection.prototype.getStats=function(){const[c,m,h]=arguments;return r.apply(this,[c||null]).then(k=>{if(o.version<53&&!m)try{k.forEach(b=>{b.type=i[b.type]||b.type})}catch(b){if(b.name!=="TypeError")throw b;k.forEach((g,y)=>{k.set(y,Object.assign({},g,{type:i[g.type]||g.type}))})}return k}).then(m,h)}}function qn(n){if(!(typeof n=="object"&&n.RTCPeerConnection&&n.RTCRtpSender)||n.RTCRtpSender&&"getStats"in n.RTCRtpSender.prototype)return;const o=n.RTCPeerConnection.prototype.getSenders;o&&(n.RTCPeerConnection.prototype.getSenders=function(){const a=o.apply(this,[]);return a.forEach(c=>c._pc=this),a});const i=n.RTCPeerConnection.prototype.addTrack;i&&(n.RTCPeerConnection.prototype.addTrack=function(){const a=i.apply(this,arguments);return a._pc=this,a}),n.RTCRtpSender.prototype.getStats=function(){return this.track?this._pc.getStats(this.track):Promise.resolve(new Map)}}function Wn(n){if(!(typeof n=="object"&&n.RTCPeerConnection&&n.RTCRtpSender)||n.RTCRtpSender&&"getStats"in n.RTCRtpReceiver.prototype)return;const o=n.RTCPeerConnection.prototype.getReceivers;o&&(n.RTCPeerConnection.prototype.getReceivers=function(){const r=o.apply(this,[]);return r.forEach(a=>a._pc=this),r}),qe(n,"track",i=>(i.receiver._pc=i.srcElement,i)),n.RTCRtpReceiver.prototype.getStats=function(){return this._pc.getStats(this.track)}}function Yn(n){!n.RTCPeerConnection||"removeStream"in n.RTCPeerConnection.prototype||(n.RTCPeerConnection.prototype.removeStream=function(i){Fr("removeStream","removeTrack"),this.getSenders().forEach(r=>{r.track&&i.getTracks().includes(r.track)&&this.removeTrack(r)})})}function Kn(n){n.DataChannel&&!n.RTCDataChannel&&(n.RTCDataChannel=n.DataChannel)}function Xn(n){if(!(typeof n=="object"&&n.RTCPeerConnection))return;const o=n.RTCPeerConnection.prototype.addTransceiver;o&&(n.RTCPeerConnection.prototype.addTransceiver=function(){this.setParametersPromises=[];let r=arguments[1]&&arguments[1].sendEncodings;r===void 0&&(r=[]),r=[...r];const a=r.length>0;a&&r.forEach(m=>{if("rid"in m&&!/^[a-z0-9]{0,16}$/i.test(m.rid))throw new TypeError("Invalid RID value provided.");if("scaleResolutionDownBy"in m&&!(parseFloat(m.scaleResolutionDownBy)>=1))throw new RangeError("scale_resolution_down_by must be >= 1.0");if("maxFramerate"in m&&!(parseFloat(m.maxFramerate)>=0))throw new RangeError("max_framerate must be >= 0.0")});const c=o.apply(this,arguments);if(a){const{sender:m}=c,h=m.getParameters();(!("encodings"in h)||h.encodings.length===1&&Object.keys(h.encodings[0]).length===0)&&(h.encodings=r,m.sendEncodings=r,this.setParametersPromises.push(m.setParameters(h).then(()=>{delete m.sendEncodings}).catch(()=>{delete m.sendEncodings})))}return c})}function Zn(n){if(!(typeof n=="object"&&n.RTCRtpSender))return;const o=n.RTCRtpSender.prototype.getParameters;o&&(n.RTCRtpSender.prototype.getParameters=function(){const r=o.apply(this,arguments);return"encodings"in r||(r.encodings=[].concat(this.sendEncodings||[{}])),r})}function Qn(n){if(!(typeof n=="object"&&n.RTCPeerConnection))return;const o=n.RTCPeerConnection.prototype.createOffer;n.RTCPeerConnection.prototype.createOffer=function(){return this.setParametersPromises&&this.setParametersPromises.length?Promise.all(this.setParametersPromises).then(()=>o.apply(this,arguments)).finally(()=>{this.setParametersPromises=[]}):o.apply(this,arguments)}}function ei(n){if(!(typeof n=="object"&&n.RTCPeerConnection))return;const o=n.RTCPeerConnection.prototype.createAnswer;n.RTCPeerConnection.prototype.createAnswer=function(){return this.setParametersPromises&&this.setParametersPromises.length?Promise.all(this.setParametersPromises).then(()=>o.apply(this,arguments)).finally(()=>{this.setParametersPromises=[]}):o.apply(this,arguments)}}const Cn=Object.freeze(Object.defineProperty({__proto__:null,shimAddTransceiver:Xn,shimCreateAnswer:ei,shimCreateOffer:Qn,shimGetDisplayMedia:Cs,shimGetParameters:Zn,shimGetUserMedia:Jn,shimOnTrack:Hn,shimPeerConnection:Dr,shimRTCDataChannel:Kn,shimReceiverGetStats:Wn,shimRemoveStream:Yn,shimSenderGetStats:qn},Symbol.toStringTag,{value:"Module"}));function ti(n){if(!(typeof n!="object"||!n.RTCPeerConnection)){if("getLocalStreams"in n.RTCPeerConnection.prototype||(n.RTCPeerConnection.prototype.getLocalStreams=function(){return this._localStreams||(this._localStreams=[]),this._localStreams}),!("addStream"in n.RTCPeerConnection.prototype)){const o=n.RTCPeerConnection.prototype.addTrack;n.RTCPeerConnection.prototype.addStream=function(r){this._localStreams||(this._localStreams=[]),this._localStreams.includes(r)||this._localStreams.push(r),r.getAudioTracks().forEach(a=>o.call(this,a,r)),r.getVideoTracks().forEach(a=>o.call(this,a,r))},n.RTCPeerConnection.prototype.addTrack=function(r,...a){return a&&a.forEach(c=>{this._localStreams?this._localStreams.includes(c)||this._localStreams.push(c):this._localStreams=[c]}),o.apply(this,arguments)}}"removeStream"in n.RTCPeerConnection.prototype||(n.RTCPeerConnection.prototype.removeStream=function(i){this._localStreams||(this._localStreams=[]);const r=this._localStreams.indexOf(i);if(r===-1)return;this._localStreams.splice(r,1);const a=i.getTracks();this.getSenders().forEach(c=>{a.includes(c.track)&&this.removeTrack(c)})})}}function ri(n){if(!(typeof n!="object"||!n.RTCPeerConnection)&&("getRemoteStreams"in n.RTCPeerConnection.prototype||(n.RTCPeerConnection.prototype.getRemoteStreams=function(){return this._remoteStreams?this._remoteStreams:[]}),!("onaddstream"in n.RTCPeerConnection.prototype))){Object.defineProperty(n.RTCPeerConnection.prototype,"onaddstream",{get(){return this._onaddstream},set(i){this._onaddstream&&(this.removeEventListener("addstream",this._onaddstream),this.removeEventListener("track",this._onaddstreampoly)),this.addEventListener("addstream",this._onaddstream=i),this.addEventListener("track",this._onaddstreampoly=r=>{r.streams.forEach(a=>{if(this._remoteStreams||(this._remoteStreams=[]),this._remoteStreams.includes(a))return;this._remoteStreams.push(a);const c=new Event("addstream");c.stream=a,this.dispatchEvent(c)})})}});const o=n.RTCPeerConnection.prototype.setRemoteDescription;n.RTCPeerConnection.prototype.setRemoteDescription=function(){const r=this;return this._onaddstreampoly||this.addEventListener("track",this._onaddstreampoly=function(a){a.streams.forEach(c=>{if(r._remoteStreams||(r._remoteStreams=[]),r._remoteStreams.indexOf(c)>=0)return;r._remoteStreams.push(c);const m=new Event("addstream");m.stream=c,r.dispatchEvent(m)})}),o.apply(r,arguments)}}}function ni(n){if(typeof n!="object"||!n.RTCPeerConnection)return;const o=n.RTCPeerConnection.prototype,i=o.createOffer,r=o.createAnswer,a=o.setLocalDescription,c=o.setRemoteDescription,m=o.addIceCandidate;o.createOffer=function(b,g){const y=arguments.length>=2?arguments[2]:arguments[0],C=i.apply(this,[y]);return g?(C.then(b,g),Promise.resolve()):C},o.createAnswer=function(b,g){const y=arguments.length>=2?arguments[2]:arguments[0],C=r.apply(this,[y]);return g?(C.then(b,g),Promise.resolve()):C};let h=function(k,b,g){const y=a.apply(this,[k]);return g?(y.then(b,g),Promise.resolve()):y};o.setLocalDescription=h,h=function(k,b,g){const y=c.apply(this,[k]);return g?(y.then(b,g),Promise.resolve()):y},o.setRemoteDescription=h,h=function(k,b,g){const y=m.apply(this,[k]);return g?(y.then(b,g),Promise.resolve()):y},o.addIceCandidate=h}function ii(n){const o=n&&n.navigator;if(o.mediaDevices&&o.mediaDevices.getUserMedia){const i=o.mediaDevices,r=i.getUserMedia.bind(i);o.mediaDevices.getUserMedia=a=>r(si(a))}!o.getUserMedia&&o.mediaDevices&&o.mediaDevices.getUserMedia&&(o.getUserMedia=(function(r,a,c){o.mediaDevices.getUserMedia(r).then(a,c)}).bind(o))}function si(n){return n&&n.video!==void 0?Object.assign({},n,{video:Dn(n.video)}):n}function oi(n){if(!n.RTCPeerConnection)return;const o=n.RTCPeerConnection;n.RTCPeerConnection=function(r,a){if(r&&r.iceServers){const c=[];for(let m=0;m<r.iceServers.length;m++){let h=r.iceServers[m];h.urls===void 0&&h.url?(Fr("RTCIceServer.url","RTCIceServer.urls"),h=JSON.parse(JSON.stringify(h)),h.urls=h.url,delete h.url,c.push(h)):c.push(r.iceServers[m])}r.iceServers=c}return new o(r,a)},n.RTCPeerConnection.prototype=o.prototype,"generateCertificate"in o&&Object.defineProperty(n.RTCPeerConnection,"generateCertificate",{get(){return o.generateCertificate}})}function ai(n){typeof n=="object"&&n.RTCTrackEvent&&"receiver"in n.RTCTrackEvent.prototype&&!("transceiver"in n.RTCTrackEvent.prototype)&&Object.defineProperty(n.RTCTrackEvent.prototype,"transceiver",{get(){return{receiver:this.receiver}}})}function li(n){const o=n.RTCPeerConnection.prototype.createOffer;n.RTCPeerConnection.prototype.createOffer=function(r){if(r){typeof r.offerToReceiveAudio<"u"&&(r.offerToReceiveAudio=!!r.offerToReceiveAudio);const a=this.getTransceivers().find(m=>m.receiver.track.kind==="audio");r.offerToReceiveAudio===!1&&a?a.direction==="sendrecv"?a.setDirection?a.setDirection("sendonly"):a.direction="sendonly":a.direction==="recvonly"&&(a.setDirection?a.setDirection("inactive"):a.direction="inactive"):r.offerToReceiveAudio===!0&&!a&&this.addTransceiver("audio",{direction:"recvonly"}),typeof r.offerToReceiveVideo<"u"&&(r.offerToReceiveVideo=!!r.offerToReceiveVideo);const c=this.getTransceivers().find(m=>m.receiver.track.kind==="video");r.offerToReceiveVideo===!1&&c?c.direction==="sendrecv"?c.setDirection?c.setDirection("sendonly"):c.direction="sendonly":c.direction==="recvonly"&&(c.setDirection?c.setDirection("inactive"):c.direction="inactive"):r.offerToReceiveVideo===!0&&!c&&this.addTransceiver("video",{direction:"recvonly"})}return o.apply(this,arguments)}}function ci(n){typeof n!="object"||n.AudioContext||(n.AudioContext=n.webkitAudioContext)}const Rn=Object.freeze(Object.defineProperty({__proto__:null,shimAudioContext:ci,shimCallbacksAPI:ni,shimConstraints:si,shimCreateOfferLegacy:li,shimGetUserMedia:ii,shimLocalStreamsAPI:ti,shimRTCIceServerUrls:oi,shimRemoteStreamsAPI:ri,shimTrackEventTransceiver:ai},Symbol.toStringTag,{value:"Module"}));var Tn=typeof globalThis<"u"?globalThis:typeof window<"u"?window:typeof global<"u"?global:typeof self<"u"?self:{};function Rs(n){return n&&n.__esModule&&Object.prototype.hasOwnProperty.call(n,"default")?n.default:n}var Ar={exports:{}},Pn;function Ts(){return Pn||(Pn=1,function(n){const o={};o.generateIdentifier=function(){return Math.random().toString(36).substring(2,12)},o.localCName=o.generateIdentifier(),o.splitLines=function(i){return i.trim().split(`
`).map(r=>r.trim())},o.splitSections=function(i){return i.split(`
m=`).map((a,c)=>(c>0?"m="+a:a).trim()+`\r
`)},o.getDescription=function(i){const r=o.splitSections(i);return r&&r[0]},o.getMediaSections=function(i){const r=o.splitSections(i);return r.shift(),r},o.matchPrefix=function(i,r){return o.splitLines(i).filter(a=>a.indexOf(r)===0)},o.parseCandidate=function(i){let r;i.indexOf("a=candidate:")===0?r=i.substring(12).split(" "):r=i.substring(10).split(" ");const a={foundation:r[0],component:{1:"rtp",2:"rtcp"}[r[1]]||r[1],protocol:r[2].toLowerCase(),priority:parseInt(r[3],10),ip:r[4],address:r[4],port:parseInt(r[5],10),type:r[7]};for(let c=8;c<r.length;c+=2)switch(r[c]){case"raddr":a.relatedAddress=r[c+1];break;case"rport":a.relatedPort=parseInt(r[c+1],10);break;case"tcptype":a.tcpType=r[c+1];break;case"ufrag":a.ufrag=r[c+1],a.usernameFragment=r[c+1];break;default:a[r[c]]===void 0&&(a[r[c]]=r[c+1]);break}return a},o.writeCandidate=function(i){const r=[];r.push(i.foundation);const a=i.component;a==="rtp"?r.push(1):a==="rtcp"?r.push(2):r.push(a),r.push(i.protocol.toUpperCase()),r.push(i.priority),r.push(i.address||i.ip),r.push(i.port);const c=i.type;return r.push("typ"),r.push(c),c!=="host"&&i.relatedAddress&&i.relatedPort&&(r.push("raddr"),r.push(i.relatedAddress),r.push("rport"),r.push(i.relatedPort)),i.tcpType&&i.protocol.toLowerCase()==="tcp"&&(r.push("tcptype"),r.push(i.tcpType)),(i.usernameFragment||i.ufrag)&&(r.push("ufrag"),r.push(i.usernameFragment||i.ufrag)),"candidate:"+r.join(" ")},o.parseIceOptions=function(i){return i.substring(14).split(" ")},o.parseRtpMap=function(i){let r=i.substring(9).split(" ");const a={payloadType:parseInt(r.shift(),10)};return r=r[0].split("/"),a.name=r[0],a.clockRate=parseInt(r[1],10),a.channels=r.length===3?parseInt(r[2],10):1,a.numChannels=a.channels,a},o.writeRtpMap=function(i){let r=i.payloadType;i.preferredPayloadType!==void 0&&(r=i.preferredPayloadType);const a=i.channels||i.numChannels||1;return"a=rtpmap:"+r+" "+i.name+"/"+i.clockRate+(a!==1?"/"+a:"")+`\r
`},o.parseExtmap=function(i){const r=i.substring(9).split(" ");return{id:parseInt(r[0],10),direction:r[0].indexOf("/")>0?r[0].split("/")[1]:"sendrecv",uri:r[1],attributes:r.slice(2).join(" ")}},o.writeExtmap=function(i){return"a=extmap:"+(i.id||i.preferredId)+(i.direction&&i.direction!=="sendrecv"?"/"+i.direction:"")+" "+i.uri+(i.attributes?" "+i.attributes:"")+`\r
`},o.parseFmtp=function(i){const r={};let a;const c=i.substring(i.indexOf(" ")+1).split(";");for(let m=0;m<c.length;m++)a=c[m].trim().split("="),r[a[0].trim()]=a[1];return r},o.writeFmtp=function(i){let r="",a=i.payloadType;if(i.preferredPayloadType!==void 0&&(a=i.preferredPayloadType),i.parameters&&Object.keys(i.parameters).length){const c=[];Object.keys(i.parameters).forEach(m=>{i.parameters[m]!==void 0?c.push(m+"="+i.parameters[m]):c.push(m)}),r+="a=fmtp:"+a+" "+c.join(";")+`\r
`}return r},o.parseRtcpFb=function(i){const r=i.substring(i.indexOf(" ")+1).split(" ");return{type:r.shift(),parameter:r.join(" ")}},o.writeRtcpFb=function(i){let r="",a=i.payloadType;return i.preferredPayloadType!==void 0&&(a=i.preferredPayloadType),i.rtcpFeedback&&i.rtcpFeedback.length&&i.rtcpFeedback.forEach(c=>{r+="a=rtcp-fb:"+a+" "+c.type+(c.parameter&&c.parameter.length?" "+c.parameter:"")+`\r
`}),r},o.parseSsrcMedia=function(i){const r=i.indexOf(" "),a={ssrc:parseInt(i.substring(7,r),10)},c=i.indexOf(":",r);return c>-1?(a.attribute=i.substring(r+1,c),a.value=i.substring(c+1)):a.attribute=i.substring(r+1),a},o.parseSsrcGroup=function(i){const r=i.substring(13).split(" ");return{semantics:r.shift(),ssrcs:r.map(a=>parseInt(a,10))}},o.getMid=function(i){const r=o.matchPrefix(i,"a=mid:")[0];if(r)return r.substring(6)},o.parseFingerprint=function(i){const r=i.substring(14).split(" ");return{algorithm:r[0].toLowerCase(),value:r[1].toUpperCase()}},o.getDtlsParameters=function(i,r){return{role:"auto",fingerprints:o.matchPrefix(i+r,"a=fingerprint:").map(o.parseFingerprint)}},o.writeDtlsParameters=function(i,r){let a="a=setup:"+r+`\r
`;return i.fingerprints.forEach(c=>{a+="a=fingerprint:"+c.algorithm+" "+c.value+`\r
`}),a},o.parseCryptoLine=function(i){const r=i.substring(9).split(" ");return{tag:parseInt(r[0],10),cryptoSuite:r[1],keyParams:r[2],sessionParams:r.slice(3)}},o.writeCryptoLine=function(i){return"a=crypto:"+i.tag+" "+i.cryptoSuite+" "+(typeof i.keyParams=="object"?o.writeCryptoKeyParams(i.keyParams):i.keyParams)+(i.sessionParams?" "+i.sessionParams.join(" "):"")+`\r
`},o.parseCryptoKeyParams=function(i){if(i.indexOf("inline:")!==0)return null;const r=i.substring(7).split("|");return{keyMethod:"inline",keySalt:r[0],lifeTime:r[1],mkiValue:r[2]?r[2].split(":")[0]:void 0,mkiLength:r[2]?r[2].split(":")[1]:void 0}},o.writeCryptoKeyParams=function(i){return i.keyMethod+":"+i.keySalt+(i.lifeTime?"|"+i.lifeTime:"")+(i.mkiValue&&i.mkiLength?"|"+i.mkiValue+":"+i.mkiLength:"")},o.getCryptoParameters=function(i,r){return o.matchPrefix(i+r,"a=crypto:").map(o.parseCryptoLine)},o.getIceParameters=function(i,r){const a=o.matchPrefix(i+r,"a=ice-ufrag:")[0],c=o.matchPrefix(i+r,"a=ice-pwd:")[0];return a&&c?{usernameFragment:a.substring(12),password:c.substring(10)}:null},o.writeIceParameters=function(i){let r="a=ice-ufrag:"+i.usernameFragment+`\r
a=ice-pwd:`+i.password+`\r
`;return i.iceLite&&(r+=`a=ice-lite\r
`),r},o.parseRtpParameters=function(i){const r={codecs:[],headerExtensions:[],fecMechanisms:[],rtcp:[]},c=o.splitLines(i)[0].split(" ");r.profile=c[2];for(let h=3;h<c.length;h++){const k=c[h],b=o.matchPrefix(i,"a=rtpmap:"+k+" ")[0];if(b){const g=o.parseRtpMap(b),y=o.matchPrefix(i,"a=fmtp:"+k+" ");switch(g.parameters=y.length?o.parseFmtp(y[0]):{},g.rtcpFeedback=o.matchPrefix(i,"a=rtcp-fb:"+k+" ").map(o.parseRtcpFb),r.codecs.push(g),g.name.toUpperCase()){case"RED":case"ULPFEC":r.fecMechanisms.push(g.name.toUpperCase());break}}}o.matchPrefix(i,"a=extmap:").forEach(h=>{r.headerExtensions.push(o.parseExtmap(h))});const m=o.matchPrefix(i,"a=rtcp-fb:* ").map(o.parseRtcpFb);return r.codecs.forEach(h=>{m.forEach(k=>{h.rtcpFeedback.find(g=>g.type===k.type&&g.parameter===k.parameter)||h.rtcpFeedback.push(k)})}),r},o.writeRtpDescription=function(i,r){let a="";a+="m="+i+" ",a+=r.codecs.length>0?"9":"0",a+=" "+(r.profile||"UDP/TLS/RTP/SAVPF")+" ",a+=r.codecs.map(m=>m.preferredPayloadType!==void 0?m.preferredPayloadType:m.payloadType).join(" ")+`\r
`,a+=`c=IN IP4 0.0.0.0\r
`,a+=`a=rtcp:9 IN IP4 0.0.0.0\r
`,r.codecs.forEach(m=>{a+=o.writeRtpMap(m),a+=o.writeFmtp(m),a+=o.writeRtcpFb(m)});let c=0;return r.codecs.forEach(m=>{m.maxptime>c&&(c=m.maxptime)}),c>0&&(a+="a=maxptime:"+c+`\r
`),r.headerExtensions&&r.headerExtensions.forEach(m=>{a+=o.writeExtmap(m)}),a},o.parseRtpEncodingParameters=function(i){const r=[],a=o.parseRtpParameters(i),c=a.fecMechanisms.indexOf("RED")!==-1,m=a.fecMechanisms.indexOf("ULPFEC")!==-1,h=o.matchPrefix(i,"a=ssrc:").map(C=>o.parseSsrcMedia(C)).filter(C=>C.attribute==="cname"),k=h.length>0&&h[0].ssrc;let b;const g=o.matchPrefix(i,"a=ssrc-group:FID").map(C=>C.substring(17).split(" ").map(U=>parseInt(U,10)));g.length>0&&g[0].length>1&&g[0][0]===k&&(b=g[0][1]),a.codecs.forEach(C=>{if(C.name.toUpperCase()==="RTX"&&C.parameters.apt){let A={ssrc:k,codecPayloadType:parseInt(C.parameters.apt,10)};k&&b&&(A.rtx={ssrc:b}),r.push(A),c&&(A=JSON.parse(JSON.stringify(A)),A.fec={ssrc:k,mechanism:m?"red+ulpfec":"red"},r.push(A))}}),r.length===0&&k&&r.push({ssrc:k});let y=o.matchPrefix(i,"b=");return y.length&&(y[0].indexOf("b=TIAS:")===0?y=parseInt(y[0].substring(7),10):y[0].indexOf("b=AS:")===0?y=parseInt(y[0].substring(5),10)*1e3*.95-50*40*8:y=void 0,r.forEach(C=>{C.maxBitrate=y})),r},o.parseRtcpParameters=function(i){const r={},a=o.matchPrefix(i,"a=ssrc:").map(h=>o.parseSsrcMedia(h)).filter(h=>h.attribute==="cname")[0];a&&(r.cname=a.value,r.ssrc=a.ssrc);const c=o.matchPrefix(i,"a=rtcp-rsize");r.reducedSize=c.length>0,r.compound=c.length===0;const m=o.matchPrefix(i,"a=rtcp-mux");return r.mux=m.length>0,r},o.writeRtcpParameters=function(i){let r="";return i.reducedSize&&(r+=`a=rtcp-rsize\r
`),i.mux&&(r+=`a=rtcp-mux\r
`),i.ssrc!==void 0&&i.cname&&(r+="a=ssrc:"+i.ssrc+" cname:"+i.cname+`\r
`),r},o.parseMsid=function(i){let r;const a=o.matchPrefix(i,"a=msid:");if(a.length===1)return r=a[0].substring(7).split(" "),{stream:r[0],track:r[1]};const c=o.matchPrefix(i,"a=ssrc:").map(m=>o.parseSsrcMedia(m)).filter(m=>m.attribute==="msid");if(c.length>0)return r=c[0].value.split(" "),{stream:r[0],track:r[1]}},o.parseSctpDescription=function(i){const r=o.parseMLine(i),a=o.matchPrefix(i,"a=max-message-size:");let c;a.length>0&&(c=parseInt(a[0].substring(19),10)),isNaN(c)&&(c=65536);const m=o.matchPrefix(i,"a=sctp-port:");if(m.length>0)return{port:parseInt(m[0].substring(12),10),protocol:r.fmt,maxMessageSize:c};const h=o.matchPrefix(i,"a=sctpmap:");if(h.length>0){const k=h[0].substring(10).split(" ");return{port:parseInt(k[0],10),protocol:k[1],maxMessageSize:c}}},o.writeSctpDescription=function(i,r){let a=[];return i.protocol!=="DTLS/SCTP"?a=["m="+i.kind+" 9 "+i.protocol+" "+r.protocol+`\r
`,`c=IN IP4 0.0.0.0\r
`,"a=sctp-port:"+r.port+`\r
`]:a=["m="+i.kind+" 9 "+i.protocol+" "+r.port+`\r
`,`c=IN IP4 0.0.0.0\r
`,"a=sctpmap:"+r.port+" "+r.protocol+` 65535\r
`],r.maxMessageSize!==void 0&&a.push("a=max-message-size:"+r.maxMessageSize+`\r
`),a.join("")},o.generateSessionId=function(){return Math.random().toString().substr(2,22)},o.writeSessionBoilerplate=function(i,r,a){let c;const m=r!==void 0?r:2;return i?c=i:c=o.generateSessionId(),`v=0\r
o=`+(a||"thisisadapterortc")+" "+c+" "+m+` IN IP4 127.0.0.1\r
s=-\r
t=0 0\r
`},o.getDirection=function(i,r){const a=o.splitLines(i);for(let c=0;c<a.length;c++)switch(a[c]){case"a=sendrecv":case"a=sendonly":case"a=recvonly":case"a=inactive":return a[c].substring(2)}return r?o.getDirection(r):"sendrecv"},o.getKind=function(i){return o.splitLines(i)[0].split(" ")[0].substring(2)},o.isRejected=function(i){return i.split(" ",2)[1]==="0"},o.parseMLine=function(i){const a=o.splitLines(i)[0].substring(2).split(" ");return{kind:a[0],port:parseInt(a[1],10),protocol:a[2],fmt:a.slice(3).join(" ")}},o.parseOLine=function(i){const a=o.matchPrefix(i,"o=")[0].substring(2).split(" ");return{username:a[0],sessionId:a[1],sessionVersion:parseInt(a[2],10),netType:a[3],addressType:a[4],address:a[5]}},o.isValidSDP=function(i){if(typeof i!="string"||i.length===0)return!1;const r=o.splitLines(i);for(let a=0;a<r.length;a++)if(r[a].length<2||r[a].charAt(1)!=="=")return!1;return!0},n.exports=o}(Ar)),Ar.exports}var ui=Ts();const it=Rs(ui),Ps=vs({__proto__:null,default:it},[ui]);function nr(n){if(!n.RTCIceCandidate||n.RTCIceCandidate&&"foundation"in n.RTCIceCandidate.prototype)return;const o=n.RTCIceCandidate;n.RTCIceCandidate=function(r){if(typeof r=="object"&&r.candidate&&r.candidate.indexOf("a=")===0&&(r=JSON.parse(JSON.stringify(r)),r.candidate=r.candidate.substring(2)),r.candidate&&r.candidate.length){const a=new o(r),c=it.parseCandidate(r.candidate);for(const m in c)m in a||Object.defineProperty(a,m,{value:c[m]});return a.toJSON=function(){return{candidate:a.candidate,sdpMid:a.sdpMid,sdpMLineIndex:a.sdpMLineIndex,usernameFragment:a.usernameFragment}},a}return new o(r)},n.RTCIceCandidate.prototype=o.prototype,qe(n,"icecandidate",i=>(i.candidate&&Object.defineProperty(i,"candidate",{value:new n.RTCIceCandidate(i.candidate),writable:"false"}),i))}function $r(n){!n.RTCIceCandidate||n.RTCIceCandidate&&"relayProtocol"in n.RTCIceCandidate.prototype||qe(n,"icecandidate",o=>{if(o.candidate){const i=it.parseCandidate(o.candidate.candidate);i.type==="relay"&&(o.candidate.relayProtocol={0:"tls",1:"tcp",2:"udp"}[i.priority>>24])}return o})}function ir(n,o){if(!n.RTCPeerConnection)return;"sctp"in n.RTCPeerConnection.prototype||Object.defineProperty(n.RTCPeerConnection.prototype,"sctp",{get(){return typeof this._sctp>"u"?null:this._sctp}});const i=function(h){if(!h||!h.sdp)return!1;const k=it.splitSections(h.sdp);return k.shift(),k.some(b=>{const g=it.parseMLine(b);return g&&g.kind==="application"&&g.protocol.indexOf("SCTP")!==-1})},r=function(h){const k=h.sdp.match(/mozilla...THIS_IS_SDPARTA-(\d+)/);if(k===null||k.length<2)return-1;const b=parseInt(k[1],10);return b!==b?-1:b},a=function(h){let k=65536;return o.browser==="firefox"&&(o.version<57?h===-1?k=16384:k=2147483637:o.version<60?k=o.version===57?65535:65536:k=2147483637),k},c=function(h,k){let b=65536;o.browser==="firefox"&&o.version===57&&(b=65535);const g=it.matchPrefix(h.sdp,"a=max-message-size:");return g.length>0?b=parseInt(g[0].substring(19),10):o.browser==="firefox"&&k!==-1&&(b=2147483637),b},m=n.RTCPeerConnection.prototype.setRemoteDescription;n.RTCPeerConnection.prototype.setRemoteDescription=function(){if(this._sctp=null,o.browser==="chrome"&&o.version>=76){const{sdpSemantics:k}=this.getConfiguration();k==="plan-b"&&Object.defineProperty(this,"sctp",{get(){return typeof this._sctp>"u"?null:this._sctp},enumerable:!0,configurable:!0})}if(i(arguments[0])){const k=r(arguments[0]),b=a(k),g=c(arguments[0],k);let y;b===0&&g===0?y=Number.POSITIVE_INFINITY:b===0||g===0?y=Math.max(b,g):y=Math.min(b,g);const C={};Object.defineProperty(C,"maxMessageSize",{get(){return y}}),this._sctp=C}return m.apply(this,arguments)}}function sr(n){if(!(n.RTCPeerConnection&&"createDataChannel"in n.RTCPeerConnection.prototype))return;function o(r,a){const c=r.send;r.send=function(){const h=arguments[0],k=h.length||h.size||h.byteLength;if(r.readyState==="open"&&a.sctp&&k>a.sctp.maxMessageSize)throw new TypeError("Message too large (can send a maximum of "+a.sctp.maxMessageSize+" bytes)");return c.apply(r,arguments)}}const i=n.RTCPeerConnection.prototype.createDataChannel;n.RTCPeerConnection.prototype.createDataChannel=function(){const a=i.apply(this,arguments);return o(a,this),a},qe(n,"datachannel",r=>(o(r.channel,r.target),r))}function wr(n){if(!n.RTCPeerConnection||"connectionState"in n.RTCPeerConnection.prototype)return;const o=n.RTCPeerConnection.prototype;Object.defineProperty(o,"connectionState",{get(){return{completed:"connected",checking:"connecting"}[this.iceConnectionState]||this.iceConnectionState},enumerable:!0,configurable:!0}),Object.defineProperty(o,"onconnectionstatechange",{get(){return this._onconnectionstatechange||null},set(i){this._onconnectionstatechange&&(this.removeEventListener("connectionstatechange",this._onconnectionstatechange),delete this._onconnectionstatechange),i&&this.addEventListener("connectionstatechange",this._onconnectionstatechange=i)},enumerable:!0,configurable:!0}),["setLocalDescription","setRemoteDescription"].forEach(i=>{const r=o[i];o[i]=function(){return this._connectionstatechangepoly||(this._connectionstatechangepoly=a=>{const c=a.target;if(c._lastConnectionState!==c.connectionState){c._lastConnectionState=c.connectionState;const m=new Event("connectionstatechange",a);c.dispatchEvent(m)}return a},this.addEventListener("iceconnectionstatechange",this._connectionstatechangepoly)),r.apply(this,arguments)}})}function Ur(n,o){if(!n.RTCPeerConnection||o.browser==="chrome"&&o.version>=71||o.browser==="safari"&&o._safariVersion>=13.1)return;const i=n.RTCPeerConnection.prototype.setRemoteDescription;n.RTCPeerConnection.prototype.setRemoteDescription=function(a){if(a&&a.sdp&&a.sdp.indexOf(`
a=extmap-allow-mixed`)!==-1){const c=a.sdp.split(`
`).filter(m=>m.trim()!=="a=extmap-allow-mixed").join(`
`);n.RTCSessionDescription&&a instanceof n.RTCSessionDescription?arguments[0]=new n.RTCSessionDescription({type:a.type,sdp:c}):a.sdp=c}return i.apply(this,arguments)}}function or(n,o){if(!(n.RTCPeerConnection&&n.RTCPeerConnection.prototype))return;const i=n.RTCPeerConnection.prototype.addIceCandidate;!i||i.length===0||(n.RTCPeerConnection.prototype.addIceCandidate=function(){return arguments[0]?(o.browser==="chrome"&&o.version<78||o.browser==="firefox"&&o.version<68||o.browser==="safari")&&arguments[0]&&arguments[0].candidate===""?Promise.resolve():i.apply(this,arguments):(arguments[1]&&arguments[1].apply(null),Promise.resolve())})}function ar(n,o){if(!(n.RTCPeerConnection&&n.RTCPeerConnection.prototype))return;const i=n.RTCPeerConnection.prototype.setLocalDescription;!i||i.length===0||(n.RTCPeerConnection.prototype.setLocalDescription=function(){let a=arguments[0]||{};if(typeof a!="object"||a.type&&a.sdp)return i.apply(this,arguments);if(a={type:a.type,sdp:a.sdp},!a.type)switch(this.signalingState){case"stable":case"have-local-offer":case"have-remote-pranswer":a.type="offer";break;default:a.type="answer";break}return a.sdp||a.type!=="offer"&&a.type!=="answer"?i.apply(this,[a]):(a.type==="offer"?this.createOffer:this.createAnswer).apply(this).then(m=>i.apply(this,[m]))})}const Es=Object.freeze(Object.defineProperty({__proto__:null,removeExtmapAllowMixed:Ur,shimAddIceCandidateNullOrEmpty:or,shimConnectionState:wr,shimMaxMessageSize:ir,shimParameterlessSetLocalDescription:ar,shimRTCIceCandidate:nr,shimRTCIceCandidateRelayProtocol:$r,shimSendThrowTypeError:sr},Symbol.toStringTag,{value:"Module"}));function js({window:n}={},o={shimChrome:!0,shimFirefox:!0,shimSafari:!0}){const i=Ln,r=ks(n),a={browserDetails:r,commonShim:Es,extractVersion:Pt,disableLog:bs,disableWarnings:xs,sdp:Ps};switch(r.browser){case"chrome":if(!Sn||!Lr||!o.shimChrome)return i("Chrome shim is not included in this adapter release."),a;if(r.version===null)return i("Chrome shim can not determine version, not shimming."),a;i("adapter.js shimming chrome."),a.browserShim=Sn,or(n,r),ar(n),$n(n,r),wn(n),Lr(n,r),Un(n),Vn(n,r),Fn(n),Nn(n),Bn(n),Gn(n,r),nr(n),$r(n),wr(n),ir(n,r),sr(n),Ur(n,r);break;case"firefox":if(!Cn||!Dr||!o.shimFirefox)return i("Firefox shim is not included in this adapter release."),a;i("adapter.js shimming firefox."),a.browserShim=Cn,or(n,r),ar(n),Jn(n,r),Dr(n,r),Hn(n),Yn(n),qn(n),Wn(n),Kn(n),Xn(n),Zn(n),Qn(n),ei(n),nr(n),wr(n),ir(n,r),sr(n);break;case"safari":if(!Rn||!o.shimSafari)return i("Safari shim is not included in this adapter release."),a;i("adapter.js shimming safari."),a.browserShim=Rn,or(n,r),ar(n),oi(n),li(n),ni(n),ti(n),ri(n),ai(n),ii(n),ci(n),nr(n),$r(n),ir(n,r),sr(n),Ur(n,r);break;default:i("Unsupported browser!");break}return a}js({window:typeof window>"u"?void 0:window});const Ms={iceServers:[{urls:"stun:stun.l.google.com:19302"},{urls:"stun:stun1.l.google.com:19302"},{urls:"stun:stun2.l.google.com:19302"}]};class As{constructor(o,i,r,a,c,m){this.meetingId=o,this.userId=i,this.peerConnections={},this.localStream=null,this.onRemoteStreamAdded=r,this.onRemoteStreamRemoved=a,this.onParticipantJoined=c,this.onParticipantLeft=m}async initLocalStream(o=!0,i=!0){try{return this.localStream=await navigator.mediaDevices.getUserMedia({audio:o,video:i}),this.localStream}catch(r){throw console.error("Error accessing media devices:",r),r}}async joinMeeting(){try{const o=await es(ue(Q,"meetings",this.meetingId));if(!o.exists())throw new Error("Meeting not found");const i=o.data(),r=i.createdBy===this.userId;if(console.log(`Joining meeting. isAdmin: ${r}, requiresApproval: ${i.requiresApproval}`),r)await this.addParticipantToMeeting(),this.setupMeetingListeners();else if(i.requiresApproval===!0){console.log("Meeting requires approval, sending join request");const c=localStorage.getItem("userName")||"Anonymous";alert("Your request to join this meeting has been sent. Please wait for the host to approve."),await this.sendJoinRequest(),this.joinRequestUnsubscribe=tr(ue(Q,"meetings",this.meetingId,"participants",this.userId),m=>{m.exists()&&(console.log("Join request approved, joining meeting"),alert("Your request to join has been approved!"),this.joinRequestUnsubscribe&&this.joinRequestUnsubscribe(),this.setupMeetingListeners())});return}else console.log("No approval required, joining directly"),await this.addParticipantToMeeting(),this.setupMeetingListeners()}catch(o){throw console.error("Error joining meeting:",o),o}}async addParticipantToMeeting(){const o=nt(Q,"meetings",this.meetingId,"participants");await Rt(ue(o,this.userId),{joined:new Date().toISOString(),userId:this.userId,audioMuted:!1,videoMuted:!1})}async sendJoinRequest(){try{const o=localStorage.getItem("userName")||"Anonymous";await Rt(ue(Q,"meetings",this.meetingId,"joinRequests",this.userId),{userId:this.userId,userName:o,timestamp:new Date().toISOString(),status:"pending"}),console.log(`Join request sent for user ${o}, waiting for approval`)}catch(o){throw console.error("Error sending join request:",o),o}}setupMeetingListeners(){const o=nt(Q,"meetings",this.meetingId,"participants");this.participantsUnsubscribe=tr(o,i=>{i.docChanges().forEach(r=>{const a=r.doc.data(),c=r.doc.id;c!==this.userId&&(r.type==="added"&&(this.onParticipantJoined(a),this.createPeerConnection(c),this.createOffer(c)),r.type==="removed"&&(this.onParticipantLeft(c),this.closePeerConnection(c)),r.type)})}),this.signalingUnsubscribe=tr(nt(Q,"meetings",this.meetingId,"signaling"),i=>{i.docChanges().forEach(r=>{const a=r.doc.data();a.from!==this.userId&&(a.type==="offer"&&a.to===this.userId?this.handleOffer(a):a.type==="answer"&&a.to===this.userId?this.handleAnswer(a):a.type==="ice-candidate"&&a.to===this.userId&&this.handleIceCandidate(a))})})}createPeerConnection(o){const i=new RTCPeerConnection(Ms);return this.localStream&&this.localStream.getTracks().forEach(r=>{i.addTrack(r,this.localStream)}),i.onicecandidate=r=>{r.candidate&&this.sendSignal({type:"ice-candidate",from:this.userId,to:o,candidate:r.candidate.toJSON()})},i.onconnectionstatechange=r=>{console.log(`Connection state change: ${i.connectionState}`),(i.connectionState==="failed"||i.connectionState==="disconnected")&&(console.log(`Connection to ${o} failed or disconnected. Attempting to reconnect...`),this.closePeerConnection(o),setTimeout(()=>{this.createPeerConnection(o),this.createOffer(o)},2e3))},i.oniceconnectionstatechange=r=>{console.log(`ICE connection state change: ${i.iceConnectionState}`)},i.onsignalingstatechange=r=>{console.log(`Signaling state change: ${i.signalingState}`)},i.ontrack=r=>{r.streams&&r.streams[0]&&this.onRemoteStreamAdded(o,r.streams[0])},this.peerConnections[o]=i,i}async createOffer(o){try{const i=this.peerConnections[o];if(!i||i.signalingState==="closed"){console.log("Cannot create offer: peer connection is closed or doesn't exist");return}if(i.signalingState!=="stable")if(console.log(`Cannot create offer: signaling state is ${i.signalingState}`),i.signalingState==="have-remote-offer")console.log("Already have a remote offer, waiting for stable state"),await new Promise(a=>{const c=()=>{i.signalingState==="stable"&&(i.removeEventListener("signalingstatechange",c),a())};i.addEventListener("signalingstatechange",c),setTimeout(a,5e3)});else return;if(i.signalingState!=="stable"){console.log(`Still cannot create offer: signaling state is ${i.signalingState}`);return}const r=await i.createOffer({offerToReceiveAudio:!0,offerToReceiveVideo:!0});await i.setLocalDescription(r),await this.waitForIceGathering(i),this.sendSignal({type:"offer",from:this.userId,to:o,sdp:i.localDescription.toJSON()})}catch(i){console.error("Error creating offer:",i)}}async handleOffer(o){try{const i=o.from;this.peerConnections[i]||this.createPeerConnection(i);const r=this.peerConnections[i];if(r.signalingState==="closed"){console.log("Cannot handle offer: peer connection is closed");return}r.signalingState!=="stable"&&(console.log(`Signaling state is ${r.signalingState}, applying rollback`),await r.setLocalDescription({type:"rollback"})),await r.setRemoteDescription(new RTCSessionDescription(o.sdp));const a=await r.createAnswer();await r.setLocalDescription(a),await this.waitForIceGathering(r),this.sendSignal({type:"answer",from:this.userId,to:i,sdp:r.localDescription.toJSON()})}catch(i){console.error("Error handling offer:",i)}}async handleAnswer(o){try{const i=o.from,r=this.peerConnections[i];if(!r){console.log("Cannot handle answer: peer connection doesn't exist");return}if(r.signalingState==="closed"){console.log("Cannot handle answer: peer connection is closed");return}r.signalingState==="have-local-offer"?await r.setRemoteDescription(new RTCSessionDescription(o.sdp)):console.log(`Cannot handle answer: signaling state is ${r.signalingState}`)}catch(i){console.error("Error handling answer:",i)}}async handleIceCandidate(o){try{const i=o.from,r=this.peerConnections[i];if(!r){console.log("Cannot handle ICE candidate: peer connection doesn't exist");return}if(r.signalingState==="closed"){console.log("Cannot handle ICE candidate: peer connection is closed");return}r.remoteDescription?await r.addIceCandidate(new RTCIceCandidate(o.candidate)):console.log("Cannot add ICE candidate: remote description is null")}catch(i){console.error("Error handling ICE candidate:",i)}}async waitForIceGathering(o){if(o.iceGatheringState!=="complete")return new Promise(i=>{const r=()=>{o.iceGatheringState==="complete"&&(o.removeEventListener("icegatheringstatechange",r),i())};o.addEventListener("icegatheringstatechange",r),setTimeout(i,1e3)})}async sendSignal(o){try{const i=nt(Q,"meetings",this.meetingId,"signaling");await Rt(ue(i),{...o,timestamp:new Date().toISOString()})}catch(i){console.error("Error sending signal:",i)}}closePeerConnection(o){const i=this.peerConnections[o];i&&(i.close(),delete this.peerConnections[o],this.onRemoteStreamRemoved(o))}async leaveMeeting(){Object.keys(this.peerConnections).forEach(o=>{this.closePeerConnection(o)}),this.localStream&&this.localStream.getTracks().forEach(o=>o.stop());try{await Et(ue(Q,"meetings",this.meetingId,"participants",this.userId)),this.checkAndEndMeetingIfEmpty()}catch(o){console.error("Error leaving meeting:",o)}this.participantsUnsubscribe&&this.participantsUnsubscribe(),this.signalingUnsubscribe&&this.signalingUnsubscribe(),this.joinRequestUnsubscribe&&this.joinRequestUnsubscribe()}async checkAndEndMeetingIfEmpty(){try{const o=nt(Q,"meetings",this.meetingId,"participants");(await ts(o)).empty&&(console.log("No participants left in the meeting. Ending the meeting."),await Rt(ue(Q,"meetings",this.meetingId),{active:!1,endedAt:new Date().toISOString()},{merge:!0}))}catch(o){console.error("Error checking if meeting is empty:",o)}}async removeParticipant(o){try{return this.closePeerConnection(o),await Et(ue(Q,"meetings",this.meetingId,"participants",o)),!0}catch(i){return console.error("Error removing participant:",i),!1}}async toggleScreenSharing(o){if(o)try{const i=await navigator.mediaDevices.getDisplayMedia({video:!0}),r=i.getVideoTracks()[0];return Object.values(this.peerConnections).forEach(a=>{const c=a.getSenders().find(m=>m.track&&m.track.kind==="video");c&&c.replaceTrack(r)}),this.screenTrack=r,this.screenTrack.onended=()=>{this.toggleScreenSharing(!1)},i}catch(i){throw console.error("Error starting screen sharing:",i),i}else{if(this.localStream){const i=this.localStream.getVideoTracks()[0];i&&Object.values(this.peerConnections).forEach(r=>{const a=r.getSenders().find(c=>c.track&&c.track.kind==="video");a&&a.replaceTrack(i)})}this.screenTrack&&(this.screenTrack.stop(),this.screenTrack=null)}}async updateVideoTrack(o){try{Object.values(this.peerConnections).forEach(i=>{const r=i.getSenders().find(a=>a.track&&a.track.kind==="video");r&&r.replaceTrack(o)})}catch(i){throw console.error("Error updating video track:",i),i}}}var _r={},En;function _s(){return En||(En=1,(function(){var n;function o(e){var t=0;return function(){return t<e.length?{done:!1,value:e[t++]}:{done:!0}}}var i=typeof Object.defineProperties=="function"?Object.defineProperty:function(e,t,s){return e==Array.prototype||e==Object.prototype||(e[t]=s.value),e};function r(e){e=[typeof globalThis=="object"&&globalThis,e,typeof window=="object"&&window,typeof self=="object"&&self,typeof Tn=="object"&&Tn];for(var t=0;t<e.length;++t){var s=e[t];if(s&&s.Math==Math)return s}throw Error("Cannot find global object")}var a=r(this);function c(e,t){if(t)e:{var s=a;e=e.split(".");for(var l=0;l<e.length-1;l++){var u=e[l];if(!(u in s))break e;s=s[u]}e=e[e.length-1],l=s[e],t=t(l),t!=l&&t!=null&&i(s,e,{configurable:!0,writable:!0,value:t})}}c("Symbol",function(e){function t(p){if(this instanceof t)throw new TypeError("Symbol is not a constructor");return new s(l+(p||"")+"_"+u++,p)}function s(p,f){this.h=p,i(this,"description",{configurable:!0,writable:!0,value:f})}if(e)return e;s.prototype.toString=function(){return this.h};var l="jscomp_symbol_"+(1e9*Math.random()>>>0)+"_",u=0;return t}),c("Symbol.iterator",function(e){if(e)return e;e=Symbol("Symbol.iterator");for(var t="Array Int8Array Uint8Array Uint8ClampedArray Int16Array Uint16Array Int32Array Uint32Array Float32Array Float64Array".split(" "),s=0;s<t.length;s++){var l=a[t[s]];typeof l=="function"&&typeof l.prototype[e]!="function"&&i(l.prototype,e,{configurable:!0,writable:!0,value:function(){return m(o(this))}})}return e});function m(e){return e={next:e},e[Symbol.iterator]=function(){return this},e}function h(e){var t=typeof Symbol<"u"&&Symbol.iterator&&e[Symbol.iterator];return t?t.call(e):{next:o(e)}}function k(e){if(!(e instanceof Array)){e=h(e);for(var t,s=[];!(t=e.next()).done;)s.push(t.value);e=s}return e}var b=typeof Object.assign=="function"?Object.assign:function(e,t){for(var s=1;s<arguments.length;s++){var l=arguments[s];if(l)for(var u in l)Object.prototype.hasOwnProperty.call(l,u)&&(e[u]=l[u])}return e};c("Object.assign",function(e){return e||b});var g=typeof Object.create=="function"?Object.create:function(e){function t(){}return t.prototype=e,new t},y;if(typeof Object.setPrototypeOf=="function")y=Object.setPrototypeOf;else{var C;e:{var A={a:!0},U={};try{U.__proto__=A,C=U.a;break e}catch{}C=!1}y=C?function(e,t){if(e.__proto__=t,e.__proto__!==t)throw new TypeError(e+" is not extensible");return e}:null}var K=y;function ae(e,t){if(e.prototype=g(t.prototype),e.prototype.constructor=e,K)K(e,t);else for(var s in t)if(s!="prototype")if(Object.defineProperties){var l=Object.getOwnPropertyDescriptor(t,s);l&&Object.defineProperty(e,s,l)}else e[s]=t[s];e.za=t.prototype}function Pe(){this.m=!1,this.j=null,this.i=void 0,this.h=1,this.v=this.s=0,this.l=null}function st(e){if(e.m)throw new TypeError("Generator is already running");e.m=!0}Pe.prototype.u=function(e){this.i=e};function ot(e,t){e.l={ma:t,na:!0},e.h=e.s||e.v}Pe.prototype.return=function(e){this.l={return:e},this.h=this.v};function Y(e,t,s){return e.h=s,{value:t}}function Mt(e){this.h=new Pe,this.i=e}function ee(e,t){st(e.h);var s=e.h.j;return s?te(e,"return"in s?s.return:function(l){return{value:l,done:!0}},t,e.h.return):(e.h.return(t),_e(e))}function te(e,t,s,l){try{var u=t.call(e.h.j,s);if(!(u instanceof Object))throw new TypeError("Iterator result "+u+" is not an object");if(!u.done)return e.h.m=!1,u;var p=u.value}catch(f){return e.h.j=null,ot(e.h,f),_e(e)}return e.h.j=null,l.call(e.h,p),_e(e)}function _e(e){for(;e.h.h;)try{var t=e.i(e.h);if(t)return e.h.m=!1,{value:t.value,done:!1}}catch(s){e.h.i=void 0,ot(e.h,s)}if(e.h.m=!1,e.h.l){if(t=e.h.l,e.h.l=null,t.na)throw t.ma;return{value:t.return,done:!0}}return{value:void 0,done:!0}}function Ue(e){this.next=function(t){return st(e.h),e.h.j?t=te(e,e.h.j.next,t,e.h.u):(e.h.u(t),t=_e(e)),t},this.throw=function(t){return st(e.h),e.h.j?t=te(e,e.h.j.throw,t,e.h.u):(ot(e.h,t),t=_e(e)),t},this.return=function(t){return ee(e,t)},this[Symbol.iterator]=function(){return this}}function ur(e){function t(l){return e.next(l)}function s(l){return e.throw(l)}return new Promise(function(l,u){function p(f){f.done?l(f.value):Promise.resolve(f.value).then(t,s).then(p,u)}p(e.next())})}function z(e){return ur(new Ue(new Mt(e)))}c("Promise",function(e){function t(f){this.i=0,this.j=void 0,this.h=[],this.u=!1;var v=this.l();try{f(v.resolve,v.reject)}catch(S){v.reject(S)}}function s(){this.h=null}function l(f){return f instanceof t?f:new t(function(v){v(f)})}if(e)return e;s.prototype.i=function(f){if(this.h==null){this.h=[];var v=this;this.j(function(){v.m()})}this.h.push(f)};var u=a.setTimeout;s.prototype.j=function(f){u(f,0)},s.prototype.m=function(){for(;this.h&&this.h.length;){var f=this.h;this.h=[];for(var v=0;v<f.length;++v){var S=f[v];f[v]=null;try{S()}catch(T){this.l(T)}}}this.h=null},s.prototype.l=function(f){this.j(function(){throw f})},t.prototype.l=function(){function f(T){return function(E){S||(S=!0,T.call(v,E))}}var v=this,S=!1;return{resolve:f(this.I),reject:f(this.m)}},t.prototype.I=function(f){if(f===this)this.m(new TypeError("A Promise cannot resolve to itself"));else if(f instanceof t)this.L(f);else{e:switch(typeof f){case"object":var v=f!=null;break e;case"function":v=!0;break e;default:v=!1}v?this.F(f):this.s(f)}},t.prototype.F=function(f){var v=void 0;try{v=f.then}catch(S){this.m(S);return}typeof v=="function"?this.M(v,f):this.s(f)},t.prototype.m=function(f){this.v(2,f)},t.prototype.s=function(f){this.v(1,f)},t.prototype.v=function(f,v){if(this.i!=0)throw Error("Cannot settle("+f+", "+v+"): Promise already settled in state"+this.i);this.i=f,this.j=v,this.i===2&&this.K(),this.H()},t.prototype.K=function(){var f=this;u(function(){if(f.D()){var v=a.console;typeof v<"u"&&v.error(f.j)}},1)},t.prototype.D=function(){if(this.u)return!1;var f=a.CustomEvent,v=a.Event,S=a.dispatchEvent;return typeof S>"u"?!0:(typeof f=="function"?f=new f("unhandledrejection",{cancelable:!0}):typeof v=="function"?f=new v("unhandledrejection",{cancelable:!0}):(f=a.document.createEvent("CustomEvent"),f.initCustomEvent("unhandledrejection",!1,!0,f)),f.promise=this,f.reason=this.j,S(f))},t.prototype.H=function(){if(this.h!=null){for(var f=0;f<this.h.length;++f)p.i(this.h[f]);this.h=null}};var p=new s;return t.prototype.L=function(f){var v=this.l();f.T(v.resolve,v.reject)},t.prototype.M=function(f,v){var S=this.l();try{f.call(v,S.resolve,S.reject)}catch(T){S.reject(T)}},t.prototype.then=function(f,v){function S(I,M){return typeof I=="function"?function(L){try{T(I(L))}catch(F){E(F)}}:M}var T,E,O=new t(function(I,M){T=I,E=M});return this.T(S(f,T),S(v,E)),O},t.prototype.catch=function(f){return this.then(void 0,f)},t.prototype.T=function(f,v){function S(){switch(T.i){case 1:f(T.j);break;case 2:v(T.j);break;default:throw Error("Unexpected state: "+T.i)}}var T=this;this.h==null?p.i(S):this.h.push(S),this.u=!0},t.resolve=l,t.reject=function(f){return new t(function(v,S){S(f)})},t.race=function(f){return new t(function(v,S){for(var T=h(f),E=T.next();!E.done;E=T.next())l(E.value).T(v,S)})},t.all=function(f){var v=h(f),S=v.next();return S.done?l([]):new t(function(T,E){function O(L){return function(F){I[L]=F,M--,M==0&&T(I)}}var I=[],M=0;do I.push(void 0),M++,l(S.value).T(O(I.length-1),E),S=v.next();while(!S.done)})},t});function Fe(e,t){e instanceof String&&(e+="");var s=0,l=!1,u={next:function(){if(!l&&s<e.length){var p=s++;return{value:t(p,e[p]),done:!1}}return l=!0,{done:!0,value:void 0}}};return u[Symbol.iterator]=function(){return u},u}c("Array.prototype.keys",function(e){return e||function(){return Fe(this,function(t){return t})}}),c("Array.prototype.fill",function(e){return e||function(t,s,l){var u=this.length||0;for(0>s&&(s=Math.max(0,u+s)),(l==null||l>u)&&(l=u),l=Number(l),0>l&&(l=Math.max(0,u+l)),s=Number(s||0);s<l;s++)this[s]=t;return this}});function oe(e){return e||Array.prototype.fill}c("Int8Array.prototype.fill",oe),c("Uint8Array.prototype.fill",oe),c("Uint8ClampedArray.prototype.fill",oe),c("Int16Array.prototype.fill",oe),c("Uint16Array.prototype.fill",oe),c("Int32Array.prototype.fill",oe),c("Uint32Array.prototype.fill",oe),c("Float32Array.prototype.fill",oe),c("Float64Array.prototype.fill",oe),c("Object.is",function(e){return e||function(t,s){return t===s?t!==0||1/t===1/s:t!==t&&s!==s}}),c("Array.prototype.includes",function(e){return e||function(t,s){var l=this;l instanceof String&&(l=String(l));var u=l.length;for(s=s||0,0>s&&(s=Math.max(s+u,0));s<u;s++){var p=l[s];if(p===t||Object.is(p,t))return!0}return!1}}),c("String.prototype.includes",function(e){return e||function(t,s){if(this==null)throw new TypeError("The 'this' value for String.prototype.includes must not be null or undefined");if(t instanceof RegExp)throw new TypeError("First argument to String.prototype.includes must not be a regular expression");return this.indexOf(t,s||0)!==-1}});var Ne=this||self;function ve(e,t){e=e.split(".");var s=Ne;e[0]in s||typeof s.execScript>"u"||s.execScript("var "+e[0]);for(var l;e.length&&(l=e.shift());)e.length||t===void 0?s[l]&&s[l]!==Object.prototype[l]?s=s[l]:s=s[l]={}:s[l]=t}function Be(e){var t;e:{if((t=Ne.navigator)&&(t=t.userAgent))break e;t=""}return t.indexOf(e)!=-1}var Ee=Array.prototype.map?function(e,t){return Array.prototype.map.call(e,t,void 0)}:function(e,t){for(var s=e.length,l=Array(s),u=typeof e=="string"?e.split(""):e,p=0;p<s;p++)p in u&&(l[p]=t.call(void 0,u[p],p,e));return l},ze={},de=null;function Ie(e){var t=e.length,s=3*t/4;s%3?s=Math.floor(s):"=.".indexOf(e[t-1])!=-1&&(s="=.".indexOf(e[t-2])!=-1?s-2:s-1);var l=new Uint8Array(s),u=0;return at(e,function(p){l[u++]=p}),u!==s?l.subarray(0,u):l}function at(e,t){function s(S){for(;l<e.length;){var T=e.charAt(l++),E=de[T];if(E!=null)return E;if(!/^[\s\xa0]*$/.test(T))throw Error("Unknown base64 encoding at char: "+T)}return S}Oe();for(var l=0;;){var u=s(-1),p=s(0),f=s(64),v=s(64);if(v===64&&u===-1)break;t(u<<2|p>>4),f!=64&&(t(p<<4&240|f>>2),v!=64&&t(f<<6&192|v))}}function Oe(){if(!de){de={};for(var e="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789".split(""),t=["+/=","+/","-_=","-_.","-_"],s=0;5>s;s++){var l=e.concat(t[s].split(""));ze[s]=l;for(var u=0;u<l.length;u++){var p=l[u];de[p]===void 0&&(de[p]=u)}}}}var Ve=typeof Uint8Array<"u",We=!(Be("Trident")||Be("MSIE"))&&typeof Ne.btoa=="function";function Ye(e){if(!We){var t;t===void 0&&(t=0),Oe(),t=ze[t];for(var s=Array(Math.floor(e.length/3)),l=t[64]||"",u=0,p=0;u<e.length-2;u+=3){var f=e[u],v=e[u+1],S=e[u+2],T=t[f>>2];f=t[(f&3)<<4|v>>4],v=t[(v&15)<<2|S>>6],S=t[S&63],s[p++]=T+f+v+S}switch(T=0,S=l,e.length-u){case 2:T=e[u+1],S=t[(T&15)<<2]||l;case 1:e=e[u],s[p]=t[e>>2]+t[(e&3)<<4|T>>4]+S+l}return s.join("")}for(t="";10240<e.length;)t+=String.fromCharCode.apply(null,e.subarray(0,10240)),e=e.subarray(10240);return t+=String.fromCharCode.apply(null,e),btoa(t)}var Ke=RegExp("[-_.]","g");function dr(e){switch(e){case"-":return"+";case"_":return"/";case".":return"=";default:return""}}function At(e){if(!We)return Ie(e);Ke.test(e)&&(e=e.replace(Ke,dr)),e=atob(e);for(var t=new Uint8Array(e.length),s=0;s<e.length;s++)t[s]=e.charCodeAt(s);return t}var lt;function ct(){return lt||(lt=new Uint8Array(0))}var Ge={},_t=typeof Uint8Array.prototype.slice=="function",X=0,re=0;function It(e){var t=0>e;e=Math.abs(e);var s=e>>>0;e=Math.floor((e-s)/4294967296),t&&(s=h(je(s,e)),t=s.next().value,e=s.next().value,s=t),X=s>>>0,re=e>>>0}var Ot=typeof BigInt=="function";function je(e,t){return t=~t,e?e=~e+1:t+=1,[e,t]}function Lt(e,t){this.i=e>>>0,this.h=t>>>0}function Dt(e){if(!e)return $t||($t=new Lt(0,0));if(!/^-?\d+$/.test(e))return null;if(16>e.length)It(Number(e));else if(Ot)e=BigInt(e),X=Number(e&BigInt(4294967295))>>>0,re=Number(e>>BigInt(32)&BigInt(4294967295));else{var t=+(e[0]==="-");re=X=0;for(var s=e.length,l=t,u=(s-t)%6+t;u<=s;l=u,u+=6)l=Number(e.slice(l,u)),re*=1e6,X=1e6*X+l,4294967296<=X&&(re+=X/4294967296|0,X%=4294967296);t&&(t=h(je(X,re)),e=t.next().value,t=t.next().value,X=e,re=t)}return new Lt(X,re)}var $t;function wt(e,t){return Error("Invalid wire type: "+e+" (at position "+t+")")}function ut(){return Error("Failed to read varint, encoding is invalid.")}function Ut(e,t){return Error("Tried to read past the end of the data "+t+" > "+e)}function Me(){throw Error("Invalid UTF8")}function Ft(e,t){return t=String.fromCharCode.apply(null,t),e==null?t:e+t}var Xe=void 0,dt,fr=typeof TextDecoder<"u",ft,pr=typeof TextEncoder<"u",Ze;function pt(e){if(e!==Ge)throw Error("illegal external caller")}function Je(e,t){if(pt(t),this.V=e,e!=null&&e.length===0)throw Error("ByteString should be constructed with non-empty values")}function ht(){return Ze||(Ze=new Je(null,Ge))}function Nt(e){pt(Ge);var t=e.V;return t=t==null||Ve&&t!=null&&t instanceof Uint8Array?t:typeof t=="string"?At(t):null,t==null?t:e.V=t}function Bt(e){if(typeof e=="string")return{buffer:At(e),C:!1};if(Array.isArray(e))return{buffer:new Uint8Array(e),C:!1};if(e.constructor===Uint8Array)return{buffer:e,C:!1};if(e.constructor===ArrayBuffer)return{buffer:new Uint8Array(e),C:!1};if(e.constructor===Je)return{buffer:Nt(e)||ct(),C:!0};if(e instanceof Uint8Array)return{buffer:new Uint8Array(e.buffer,e.byteOffset,e.byteLength),C:!1};throw Error("Type not convertible to a Uint8Array, expected a Uint8Array, an ArrayBuffer, a base64 encoded string, a ByteString or an Array of numbers")}function zt(e,t){this.i=null,this.m=!1,this.h=this.j=this.l=0,gt(this,e,t)}function gt(e,t,s){s=s===void 0?{}:s,e.S=s.S===void 0?!1:s.S,t&&(t=Bt(t),e.i=t.buffer,e.m=t.C,e.l=0,e.j=e.i.length,e.h=e.l)}zt.prototype.reset=function(){this.h=this.l};function ke(e,t){if(e.h=t,t>e.j)throw Ut(e.j,t)}function Le(e){var t=e.i,s=e.h,l=t[s++],u=l&127;if(l&128&&(l=t[s++],u|=(l&127)<<7,l&128&&(l=t[s++],u|=(l&127)<<14,l&128&&(l=t[s++],u|=(l&127)<<21,l&128&&(l=t[s++],u|=l<<28,l&128&&t[s++]&128&&t[s++]&128&&t[s++]&128&&t[s++]&128&&t[s++]&128)))))throw ut();return ke(e,s),u}function R(e,t){if(0>t)throw Error("Tried to read a negative byte length: "+t);var s=e.h,l=s+t;if(l>e.j)throw Ut(t,e.j-s);return e.h=l,s}var x=[];function w(){this.h=[]}w.prototype.length=function(){return this.h.length},w.prototype.end=function(){var e=this.h;return this.h=[],e};function De(e,t,s){for(;0<s||127<t;)e.h.push(t&127|128),t=(t>>>7|s<<25)>>>0,s>>>=7;e.h.push(t)}function pe(e,t){for(;127<t;)e.h.push(t&127|128),t>>>=7;e.h.push(t)}function mt(e,t){if(x.length){var s=x.pop();gt(s,e,t),e=s}else e=new zt(e,t);this.h=e,this.j=this.h.h,this.i=this.l=-1,this.setOptions(t)}mt.prototype.setOptions=function(e){e=e===void 0?{}:e,this.ca=e.ca===void 0?!1:e.ca},mt.prototype.reset=function(){this.h.reset(),this.j=this.h.h,this.i=this.l=-1};function Vt(e){var t=e.h;if(t.h==t.j)return!1;e.j=e.h.h;var s=Le(e.h)>>>0;if(t=s>>>3,s&=7,!(0<=s&&5>=s))throw wt(s,e.j);if(1>t)throw Error("Invalid field number: "+t+" (at position "+e.j+")");return e.l=t,e.i=s,!0}function Qe(e){switch(e.i){case 0:if(e.i!=0)Qe(e);else e:{e=e.h;for(var t=e.h,s=t+10,l=e.i;t<s;)if((l[t++]&128)===0){ke(e,t);break e}throw ut()}break;case 1:e=e.h,ke(e,e.h+8);break;case 2:e.i!=2?Qe(e):(t=Le(e.h)>>>0,e=e.h,ke(e,e.h+t));break;case 5:e=e.h,ke(e,e.h+4);break;case 3:t=e.l;do{if(!Vt(e))throw Error("Unmatched start-group tag: stream EOF");if(e.i==4){if(e.l!=t)throw Error("Unmatched end-group tag");break}Qe(e)}while(!0);break;default:throw wt(e.i,e.j)}}var _=[];function fe(){this.j=[],this.i=0,this.h=new w}function he(e,t){t.length!==0&&(e.j.push(t),e.i+=t.length)}function fi(e,t){if(t=t.R){he(e,e.h.end());for(var s=0;s<t.length;s++)he(e,Nt(t[s])||ct())}}var Ae=typeof Symbol=="function"&&typeof Symbol()=="symbol"?Symbol():void 0;function He(e,t){return Ae?e[Ae]|=t:e.A!==void 0?e.A|=t:(Object.defineProperties(e,{A:{value:t,configurable:!0,writable:!0,enumerable:!1}}),t)}function Nr(e,t){Ae?e[Ae]&&(e[Ae]&=~t):e.A!==void 0&&(e.A&=~t)}function Z(e){var t;return Ae?t=e[Ae]:t=e.A,t??0}function Se(e,t){Ae?e[Ae]=t:e.A!==void 0?e.A=t:Object.defineProperties(e,{A:{value:t,configurable:!0,writable:!0,enumerable:!1}})}function hr(e){return He(e,1),e}function pi(e,t){Se(t,(e|0)&-51)}function Gt(e,t){Se(t,(e|18)&-41)}var gr={};function Jt(e){return e!==null&&typeof e=="object"&&!Array.isArray(e)&&e.constructor===Object}var vt,Br=[];Se(Br,23),vt=Object.freeze(Br);function mr(e){if(Z(e.o)&2)throw Error("Cannot mutate an immutable Message")}function vr(e){var t=e.length;(t=t?e[t-1]:void 0)&&Jt(t)?t.g=1:(t={},e.push((t.g=1,t)))}function zr(e){var t=e.i+e.G;return e.B||(e.B=e.o[t]={})}function ye(e,t){return t===-1?null:t>=e.i?e.B?e.B[t]:void 0:e.o[t+e.G]}function Ce(e,t,s,l){mr(e),yt(e,t,s,l)}function yt(e,t,s,l){e.j&&(e.j=void 0),t>=e.i||l?zr(e)[t]=s:(e.o[t+e.G]=s,(e=e.B)&&t in e&&delete e[t])}function yr(e,t,s,l){var u=ye(e,t);Array.isArray(u)||(u=vt);var p=Z(u);if(p&1||hr(u),l)p&2||He(u,2),s&1||Object.freeze(u);else{l=!(s&2);var f=p&2;s&1||!f?l&&p&16&&!f&&Nr(u,16):(u=hr(Array.prototype.slice.call(u)),yt(e,t,u))}return u}function br(e,t){var s=ye(e,t),l=s==null?s:typeof s=="number"||s==="NaN"||s==="Infinity"||s==="-Infinity"?Number(s):void 0;return l!=null&&l!==s&&yt(e,t,l),l}function Vr(e,t,s,l,u){e.h||(e.h={});var p=e.h[s],f=yr(e,s,3,u);if(!p){var v=f;p=[];var S=!!(Z(e.o)&16);f=!!(Z(v)&2);var T=v;!u&&f&&(v=Array.prototype.slice.call(v));for(var E=f,O=0;O<v.length;O++){var I=v[O],M=t,L=!1;if(L=L===void 0?!1:L,I=Array.isArray(I)?new M(I):L?new M:void 0,I!==void 0){M=I.o;var F=L=Z(M);f&&(F|=2),S&&(F|=16),F!=L&&Se(M,F),M=F,E=E||!!(2&M),p.push(I)}}return e.h[s]=p,S=Z(v),t=S|33,t=E?t&-9:t|8,S!=t&&(E=v,Object.isFrozen(E)&&(E=Array.prototype.slice.call(E)),Se(E,t),v=E),T!==v&&yt(e,s,v),(u||l&&f)&&He(p,2),l&&Object.freeze(p),p}return u||(u=Object.isFrozen(p),l&&!u?Object.freeze(p):!l&&u&&(p=Array.prototype.slice.call(p),e.h[s]=p)),p}function xr(e,t,s){var l=!!(Z(e.o)&2);if(t=Vr(e,t,s,l,l),e=yr(e,s,3,l),!(l||Z(e)&8)){for(l=0;l<t.length;l++){if(s=t[l],Z(s.o)&2){var u=Yr(s,!1);u.j=s}else u=s;s!==u&&(t[l]=u,e[l]=u.o)}He(e,8)}return t}function Re(e,t,s){if(s!=null&&typeof s!="number")throw Error("Value of float/double field must be a number|null|undefined, found "+typeof s+": "+s);Ce(e,t,s)}function Gr(e,t,s,l,u){mr(e);var p=Vr(e,s,t,!1,!1);return s=l??new s,e=yr(e,t,2,!1),u!=null?(p.splice(u,0,s),e.splice(u,0,s.o)):(p.push(s),e.push(s.o)),s.C()&&Nr(e,8),s}function Ht(e,t){return e??t}function Te(e,t,s){return s=s===void 0?0:s,Ht(br(e,t),s)}var qt;function hi(e){switch(typeof e){case"number":return isFinite(e)?e:String(e);case"object":if(e)if(Array.isArray(e)){if((Z(e)&128)!==0)return e=Array.prototype.slice.call(e),vr(e),e}else{if(Ve&&e!=null&&e instanceof Uint8Array)return Ye(e);if(e instanceof Je){var t=e.V;return t==null?"":typeof t=="string"?t:e.V=Ye(t)}}}return e}function Jr(e,t,s,l){if(e!=null){if(Array.isArray(e))e=kr(e,t,s,l!==void 0);else if(Jt(e)){var u={},p;for(p in e)u[p]=Jr(e[p],t,s,l);e=u}else e=t(e,l);return e}}function kr(e,t,s,l){var u=Z(e);l=l?!!(u&16):void 0,e=Array.prototype.slice.call(e);for(var p=0;p<e.length;p++)e[p]=Jr(e[p],t,s,l);return s(u,e),e}function gi(e){return e.ja===gr?e.toJSON():hi(e)}function mi(e,t){e&128&&vr(t)}function Hr(e,t,s){if(s=s===void 0?Gt:s,e!=null){if(Ve&&e instanceof Uint8Array)return e.length?new Je(new Uint8Array(e),Ge):ht();if(Array.isArray(e)){var l=Z(e);return l&2?e:t&&!(l&32)&&(l&16||l===0)?(Se(e,l|2),e):(e=kr(e,Hr,l&4?Gt:s,!0),t=Z(e),t&4&&t&2&&Object.freeze(e),e)}return e.ja===gr?Wr(e):e}}function qr(e,t,s,l,u,p,f){if(e=e.h&&e.h[s]){if(l=Z(e),l&2?l=e:(p=Ee(e,Wr),Gt(l,p),Object.freeze(p),l=p),mr(t),f=l==null?vt:hr([]),l!=null){for(p=!!l.length,e=0;e<l.length;e++){var v=l[e];p=p&&!(Z(v.o)&2),f[e]=v.o}p=(p?8:0)|1,e=Z(f),(e&p)!==p&&(Object.isFrozen(f)&&(f=Array.prototype.slice.call(f)),Se(f,e|p)),t.h||(t.h={}),t.h[s]=l}else t.h&&(t.h[s]=void 0);yt(t,s,f,u)}else Ce(t,s,Hr(l,p,f),u)}function Wr(e){return Z(e.o)&2||(e=Yr(e,!0),He(e.o,2)),e}function Yr(e,t){var s=e.o,l=[];He(l,16);var u=e.constructor.h;if(u&&l.push(u),u=e.B,u){l.length=s.length,l.fill(void 0,l.length,s.length);var p={};l[l.length-1]=p}(Z(s)&128)!==0&&vr(l),t=t||e.C()?Gt:pi,p=e.constructor,qt=l,l=new p(l),qt=void 0,e.R&&(l.R=e.R.slice()),p=!!(Z(s)&16);for(var f=u?s.length-1:s.length,v=0;v<f;v++)qr(e,l,v-e.G,s[v],!1,p,t);if(u)for(var S in u)qr(e,l,+S,u[S],!0,p,t);return l}function le(e,t,s){e==null&&(e=qt),qt=void 0;var l=this.constructor.i||0,u=0<l,p=this.constructor.h,f=!1;if(e==null){e=p?[p]:[];var v=48,S=!0;u&&(l=0,v|=128),Se(e,v)}else{if(!Array.isArray(e)||p&&p!==e[0])throw Error();var T=v=He(e,0);if((S=(16&T)!==0)&&((f=(32&T)!==0)||(T|=32)),u){if(128&T)l=0;else if(0<e.length){var E=e[e.length-1];if(Jt(E)&&"g"in E){l=0,T|=128,delete E.g;var O=!0,I;for(I in E){O=!1;break}O&&e.pop()}}}else if(128&T)throw Error();v!==T&&Se(e,T)}this.G=(p?0:-1)-l,this.h=void 0,this.o=e;e:{if(p=this.o.length,l=p-1,p&&(p=this.o[l],Jt(p))){this.B=p,this.i=l-this.G;break e}t!==void 0&&-1<t?(this.i=Math.max(t,l+1-this.G),this.B=void 0):this.i=Number.MAX_VALUE}if(!u&&this.B&&"g"in this.B)throw Error('Unexpected "g" flag in sparse object of message that is not a group type.');if(s){t=S&&!f&&!0,u=this.i;var M;for(S=0;S<s.length;S++)f=s[S],f<u?(f+=this.G,(l=e[f])?Kr(l,t):e[f]=vt):(M||(M=zr(this)),(l=M[f])?Kr(l,t):M[f]=vt)}}le.prototype.toJSON=function(){return kr(this.o,gi,mi)},le.prototype.C=function(){return!!(Z(this.o)&2)};function Kr(e,t){if(Array.isArray(e)){var s=Z(e),l=1;!t||s&2||(l|=16),(s&l)!==l&&Se(e,s|l)}}le.prototype.ja=gr,le.prototype.toString=function(){return this.o.toString()};function Xr(e,t,s){if(s){var l={},u;for(u in s){var p=s[u],f=p.ra;f||(l.J=p.xa||p.oa.W,p.ia?(l.aa=rn(p.ia),f=function(v){return function(S,T,E){return v.J(S,T,E,v.aa)}}(l)):p.ka?(l.Z=nn(p.da.P,p.ka),f=function(v){return function(S,T,E){return v.J(S,T,E,v.Z)}}(l)):f=l.J,p.ra=f),f(t,e,p.da),l={J:l.J,aa:l.aa,Z:l.Z}}}fi(t,e)}var Wt=Symbol();function Zr(e,t,s){return e[Wt]||(e[Wt]=function(l,u){return t(l,u,s)})}function Qr(e){var t=e[Wt];if(!t){var s=Cr(e);t=function(l,u){return sn(l,u,s)},e[Wt]=t}return t}function vi(e){var t=e.ia;if(t)return Qr(t);if(t=e.wa)return Zr(e.da.P,t,e.ka)}function yi(e){var t=vi(e),s=e.da,l=e.oa.U;return t?function(u,p){return l(u,p,s,t)}:function(u,p){return l(u,p,s)}}function en(e,t){var s=e[t];return typeof s=="function"&&s.length===0&&(s=s(),e[t]=s),Array.isArray(s)&&(xt in s||bt in s||0<s.length&&typeof s[0]=="function")?s:void 0}function tn(e,t,s,l,u,p){t.P=e[0];var f=1;if(e.length>f&&typeof e[f]!="number"){var v=e[f++];s(t,v)}for(;f<e.length;){s=e[f++];for(var S=f+1;S<e.length&&typeof e[S]!="number";)S++;switch(v=e[f++],S-=f,S){case 0:l(t,s,v);break;case 1:(S=en(e,f))?(f++,u(t,s,v,S)):l(t,s,v,e[f++]);break;case 2:S=f++,S=en(e,S),u(t,s,v,S,e[f++]);break;case 3:p(t,s,v,e[f++],e[f++],e[f++]);break;case 4:p(t,s,v,e[f++],e[f++],e[f++],e[f++]);break;default:throw Error("unexpected number of binary field arguments: "+S)}}return t}var Yt=Symbol();function rn(e){var t=e[Yt];if(!t){var s=Sr(e);t=function(l,u){return on(l,u,s)},e[Yt]=t}return t}function nn(e,t){var s=e[Yt];return s||(s=function(l,u){return Xr(l,u,t)},e[Yt]=s),s}var bt=Symbol();function bi(e,t){e.push(t)}function xi(e,t,s){e.push(t,s.W)}function ki(e,t,s,l){var u=rn(l),p=Sr(l).P,f=s.W;e.push(t,function(v,S,T){return f(v,S,T,p,u)})}function Si(e,t,s,l,u,p){var f=nn(l,p),v=s.W;e.push(t,function(S,T,E){return v(S,T,E,l,f)})}function Sr(e){var t=e[bt];return t||(t=tn(e,e[bt]=[],bi,xi,ki,Si),xt in e&&bt in e&&(e.length=0),t)}var xt=Symbol();function Ci(e,t){e[0]=t}function Ri(e,t,s,l){var u=s.U;e[t]=l?function(p,f,v){return u(p,f,v,l)}:u}function Ti(e,t,s,l,u){var p=s.U,f=Qr(l),v=Cr(l).P;e[t]=function(S,T,E){return p(S,T,E,v,f,u)}}function Pi(e,t,s,l,u,p,f){var v=s.U,S=Zr(l,u,p);e[t]=function(T,E,O){return v(T,E,O,l,S,f)}}function Cr(e){var t=e[xt];return t||(t=tn(e,e[xt]={},Ci,Ri,Ti,Pi),xt in e&&bt in e&&(e.length=0),t)}function sn(e,t,s){for(;Vt(t)&&t.i!=4;){var l=t.l,u=s[l];if(!u){var p=s[0];p&&(p=p[l])&&(u=s[l]=yi(p))}if(!u||!u(t,e,l)){u=t,l=e,p=u.j,Qe(u);var f=u;if(!f.ca){if(u=f.h.h-p,f.h.h=p,f=f.h,u==0)u=ht();else{if(p=R(f,u),f.S&&f.m)u=f.i.subarray(p,p+u);else{f=f.i;var v=p;u=p+u,u=v===u?ct():_t?f.slice(v,u):new Uint8Array(f.subarray(v,u))}u=u.length==0?ht():new Je(u,Ge)}(p=l.R)?p.push(u):l.R=[u]}}}return e}function on(e,t,s){for(var l=s.length,u=l%2==1,p=u?1:0;p<l;p+=2)(0,s[p+1])(t,e,s[p]);Xr(e,t,u?s[0]:void 0)}function kt(e,t){return{U:e,W:t}}var be=kt(function(e,t,s){if(e.i!==5)return!1;e=e.h;var l=e.i,u=e.h,p=l[u],f=l[u+1],v=l[u+2];return l=l[u+3],ke(e,e.h+4),f=(p<<0|f<<8|v<<16|l<<24)>>>0,e=2*(f>>31)+1,p=f>>>23&255,f&=8388607,Ce(t,s,p==255?f?NaN:1/0*e:p==0?e*Math.pow(2,-149)*f:e*Math.pow(2,p-150)*(f+Math.pow(2,23))),!0},function(e,t,s){if(t=br(t,s),t!=null){pe(e.h,8*s+5),e=e.h;var l=+t;l===0?0<1/l?X=re=0:(re=0,X=2147483648):isNaN(l)?(re=0,X=2147483647):(l=(s=0>l?-2147483648:0)?-l:l,34028234663852886e22<l?(re=0,X=(s|2139095040)>>>0):11754943508222875e-54>l?(l=Math.round(l/Math.pow(2,-149)),re=0,X=(s|l)>>>0):(t=Math.floor(Math.log(l)/Math.LN2),l*=Math.pow(2,-t),l=Math.round(8388608*l),16777216<=l&&++t,re=0,X=(s|t+127<<23|l&8388607)>>>0)),s=X,e.h.push(s>>>0&255),e.h.push(s>>>8&255),e.h.push(s>>>16&255),e.h.push(s>>>24&255)}}),Ei=kt(function(e,t,s){if(e.i!==0)return!1;var l=e.h,u=0,p=e=0,f=l.i,v=l.h;do{var S=f[v++];u|=(S&127)<<p,p+=7}while(32>p&&S&128);for(32<p&&(e|=(S&127)>>4),p=3;32>p&&S&128;p+=7)S=f[v++],e|=(S&127)<<p;if(ke(l,v),128>S)l=u>>>0,S=e>>>0,(e=S&2147483648)&&(l=~l+1>>>0,S=~S>>>0,l==0&&(S=S+1>>>0)),l=4294967296*S+(l>>>0);else throw ut();return Ce(t,s,e?-l:l),!0},function(e,t,s){t=ye(t,s),t!=null&&(typeof t=="string"&&Dt(t),t!=null&&(pe(e.h,8*s),typeof t=="number"?(e=e.h,It(t),De(e,X,re)):(s=Dt(t),De(e.h,s.i,s.h))))}),ji=kt(function(e,t,s){return e.i!==0?!1:(Ce(t,s,Le(e.h)),!0)},function(e,t,s){if(t=ye(t,s),t!=null&&t!=null)if(pe(e.h,8*s),e=e.h,s=t,0<=s)pe(e,s);else{for(t=0;9>t;t++)e.h.push(s&127|128),s>>=7;e.h.push(1)}}),an=kt(function(e,t,s){if(e.i!==2)return!1;var l=Le(e.h)>>>0;e=e.h;var u=R(e,l);if(e=e.i,fr){var p=e,f;(f=dt)||(f=dt=new TextDecoder("utf-8",{fatal:!0})),e=u+l,p=u===0&&e===p.length?p:p.subarray(u,e);try{var v=f.decode(p)}catch(O){if(Xe===void 0){try{f.decode(new Uint8Array([128]))}catch{}try{f.decode(new Uint8Array([97])),Xe=!0}catch{Xe=!1}}throw!Xe&&(dt=void 0),O}}else{v=u,l=v+l,u=[];for(var S=null,T,E;v<l;)T=e[v++],128>T?u.push(T):224>T?v>=l?Me():(E=e[v++],194>T||(E&192)!==128?(v--,Me()):u.push((T&31)<<6|E&63)):240>T?v>=l-1?Me():(E=e[v++],(E&192)!==128||T===224&&160>E||T===237&&160<=E||((p=e[v++])&192)!==128?(v--,Me()):u.push((T&15)<<12|(E&63)<<6|p&63)):244>=T?v>=l-2?Me():(E=e[v++],(E&192)!==128||(T<<28)+(E-144)>>30!==0||((p=e[v++])&192)!==128||((f=e[v++])&192)!==128?(v--,Me()):(T=(T&7)<<18|(E&63)<<12|(p&63)<<6|f&63,T-=65536,u.push((T>>10&1023)+55296,(T&1023)+56320))):Me(),8192<=u.length&&(S=Ft(S,u),u.length=0);v=Ft(S,u)}return Ce(t,s,v),!0},function(e,t,s){if(t=ye(t,s),t!=null){var l=!1;if(l=l===void 0?!1:l,pr){if(l&&/(?:[^\uD800-\uDBFF]|^)[\uDC00-\uDFFF]|[\uD800-\uDBFF](?![\uDC00-\uDFFF])/.test(t))throw Error("Found an unpaired surrogate");t=(ft||(ft=new TextEncoder)).encode(t)}else{for(var u=0,p=new Uint8Array(3*t.length),f=0;f<t.length;f++){var v=t.charCodeAt(f);if(128>v)p[u++]=v;else{if(2048>v)p[u++]=v>>6|192;else{if(55296<=v&&57343>=v){if(56319>=v&&f<t.length){var S=t.charCodeAt(++f);if(56320<=S&&57343>=S){v=1024*(v-55296)+S-56320+65536,p[u++]=v>>18|240,p[u++]=v>>12&63|128,p[u++]=v>>6&63|128,p[u++]=v&63|128;continue}else f--}if(l)throw Error("Found an unpaired surrogate");v=65533}p[u++]=v>>12|224,p[u++]=v>>6&63|128}p[u++]=v&63|128}}t=u===p.length?p:p.subarray(0,u)}pe(e.h,8*s+2),pe(e.h,t.length),he(e,e.h.end()),he(e,t)}}),ln=kt(function(e,t,s,l,u){if(e.i!==2)return!1;t=Gr(t,s,l),s=e.h.j,l=Le(e.h)>>>0;var p=e.h.h+l,f=p-s;if(0>=f&&(e.h.j=p,u(t,e,void 0,void 0,void 0),f=p-e.h.h),f)throw Error("Message parsing ended unexpectedly. Expected to read "+(l+" bytes, instead read "+(l-f)+" bytes, either the data ended unexpectedly or the message misreported its own length"));return e.h.h=p,e.h.j=s,!0},function(e,t,s,l,u){if(t=xr(t,l,s),t!=null)for(l=0;l<t.length;l++){var p=e;pe(p.h,8*s+2);var f=p.h.end();he(p,f),f.push(p.i),p=f,u(t[l],e),f=e;var v=p.pop();for(v=f.i+f.h.length()-v;127<v;)p.push(v&127|128),v>>>=7,f.i++;p.push(v),f.i++}});function Rr(e){return function(t,s){e:{if(_.length){var l=_.pop();l.setOptions(s),gt(l.h,t,s),t=l}else t=new mt(t,s);try{var u=Cr(e),p=sn(new u.P,t,u);break e}finally{u=t.h,u.i=null,u.m=!1,u.l=0,u.j=0,u.h=0,u.S=!1,t.l=-1,t.i=-1,100>_.length&&_.push(t)}p=void 0}return p}}function Tr(e){return function(){var t=new fe;on(this,t,Sr(e)),he(t,t.h.end());for(var s=new Uint8Array(t.i),l=t.j,u=l.length,p=0,f=0;f<u;f++){var v=l[f];s.set(v,p),p+=v.length}return t.j=[s],s}}function et(e){le.call(this,e)}ae(et,le);var cn=[et,1,ji,2,be,3,an,4,an];et.prototype.l=Tr(cn);function Pr(e){le.call(this,e,-1,Mi)}ae(Pr,le),Pr.prototype.addClassification=function(e,t){return Gr(this,1,et,e,t),this};var Mi=[1],Ai=Rr([Pr,1,ln,cn]);function St(e){le.call(this,e)}ae(St,le);var un=[St,1,be,2,be,3,be,4,be,5,be];St.prototype.l=Tr(un);function dn(e){le.call(this,e,-1,_i)}ae(dn,le);var _i=[1],Ii=Rr([dn,1,ln,un]);function Kt(e){le.call(this,e)}ae(Kt,le);var fn=[Kt,1,be,2,be,3,be,4,be,5,be,6,Ei],Oi=Rr(fn);Kt.prototype.l=Tr(fn);function pn(e,t,s){if(s=e.createShader(s===0?e.VERTEX_SHADER:e.FRAGMENT_SHADER),e.shaderSource(s,t),e.compileShader(s),!e.getShaderParameter(s,e.COMPILE_STATUS))throw Error(`Could not compile WebGL shader.

`+e.getShaderInfoLog(s));return s}function Li(e){return xr(e,et,1).map(function(t){var s=ye(t,1);return{index:s??0,qa:Te(t,2),label:ye(t,3)!=null?Ht(ye(t,3),""):void 0,displayName:ye(t,4)!=null?Ht(ye(t,4),""):void 0}})}function Di(e){return{x:Te(e,1),y:Te(e,2),z:Te(e,3),visibility:br(e,4)!=null?Te(e,4):void 0}}function Er(e,t){this.i=e,this.h=t,this.m=0}function hn(e,t,s){return $i(e,t),typeof e.h.canvas.transferToImageBitmap=="function"?Promise.resolve(e.h.canvas.transferToImageBitmap()):s?Promise.resolve(e.h.canvas):typeof createImageBitmap=="function"?createImageBitmap(e.h.canvas):(e.j===void 0&&(e.j=document.createElement("canvas")),new Promise(function(l){e.j.height=e.h.canvas.height,e.j.width=e.h.canvas.width,e.j.getContext("2d",{}).drawImage(e.h.canvas,0,0,e.h.canvas.width,e.h.canvas.height),l(e.j)}))}function $i(e,t){var s=e.h;if(e.s===void 0){var l=pn(s,`
  attribute vec2 aVertex;
  attribute vec2 aTex;
  varying vec2 vTex;
  void main(void) {
    gl_Position = vec4(aVertex, 0.0, 1.0);
    vTex = aTex;
  }`,0),u=pn(s,`
  precision mediump float;
  varying vec2 vTex;
  uniform sampler2D sampler0;
  void main(){
    gl_FragColor = texture2D(sampler0, vTex);
  }`,1),p=s.createProgram();if(s.attachShader(p,l),s.attachShader(p,u),s.linkProgram(p),!s.getProgramParameter(p,s.LINK_STATUS))throw Error(`Could not compile WebGL program.

`+s.getProgramInfoLog(p));l=e.s=p,s.useProgram(l),u=s.getUniformLocation(l,"sampler0"),e.l={O:s.getAttribLocation(l,"aVertex"),N:s.getAttribLocation(l,"aTex"),ya:u},e.v=s.createBuffer(),s.bindBuffer(s.ARRAY_BUFFER,e.v),s.enableVertexAttribArray(e.l.O),s.vertexAttribPointer(e.l.O,2,s.FLOAT,!1,0,0),s.bufferData(s.ARRAY_BUFFER,new Float32Array([-1,-1,-1,1,1,1,1,-1]),s.STATIC_DRAW),s.bindBuffer(s.ARRAY_BUFFER,null),e.u=s.createBuffer(),s.bindBuffer(s.ARRAY_BUFFER,e.u),s.enableVertexAttribArray(e.l.N),s.vertexAttribPointer(e.l.N,2,s.FLOAT,!1,0,0),s.bufferData(s.ARRAY_BUFFER,new Float32Array([0,1,0,0,1,0,1,1]),s.STATIC_DRAW),s.bindBuffer(s.ARRAY_BUFFER,null),s.uniform1i(u,0)}l=e.l,s.useProgram(e.s),s.canvas.width=t.width,s.canvas.height=t.height,s.viewport(0,0,t.width,t.height),s.activeTexture(s.TEXTURE0),e.i.bindTexture2d(t.glName),s.enableVertexAttribArray(l.O),s.bindBuffer(s.ARRAY_BUFFER,e.v),s.vertexAttribPointer(l.O,2,s.FLOAT,!1,0,0),s.enableVertexAttribArray(l.N),s.bindBuffer(s.ARRAY_BUFFER,e.u),s.vertexAttribPointer(l.N,2,s.FLOAT,!1,0,0),s.bindFramebuffer(s.DRAW_FRAMEBUFFER?s.DRAW_FRAMEBUFFER:s.FRAMEBUFFER,null),s.clearColor(0,0,0,0),s.clear(s.COLOR_BUFFER_BIT),s.colorMask(!0,!0,!0,!0),s.drawArrays(s.TRIANGLE_FAN,0,4),s.disableVertexAttribArray(l.O),s.disableVertexAttribArray(l.N),s.bindBuffer(s.ARRAY_BUFFER,null),e.i.bindTexture2d(0)}function wi(e){this.h=e}var Ui=new Uint8Array([0,97,115,109,1,0,0,0,1,4,1,96,0,0,3,2,1,0,10,9,1,7,0,65,0,253,15,26,11]);function Fi(e,t){return t+e}function gn(e,t){window[e]=t}function Ni(e){var t=document.createElement("script");return t.setAttribute("src",e),t.setAttribute("crossorigin","anonymous"),new Promise(function(s){t.addEventListener("load",function(){s()},!1),t.addEventListener("error",function(){s()},!1),document.body.appendChild(t)})}function Bi(){return z(function(e){switch(e.h){case 1:return e.s=2,Y(e,WebAssembly.instantiate(Ui),4);case 4:e.h=3,e.s=0;break;case 2:return e.s=0,e.l=null,e.return(!1);case 3:return e.return(!0)}})}function jr(e){if(this.h=e,this.listeners={},this.l={},this.L={},this.s={},this.v={},this.M=this.u=this.ga=!0,this.I=Promise.resolve(),this.fa="",this.D={},this.locateFile=e&&e.locateFile||Fi,typeof window=="object")var t=window.location.pathname.toString().substring(0,window.location.pathname.toString().lastIndexOf("/"))+"/";else if(typeof location<"u")t=location.pathname.toString().substring(0,location.pathname.toString().lastIndexOf("/"))+"/";else throw Error("solutions can only be loaded on a web page or in a web worker");if(this.ha=t,e.options){t=h(Object.keys(e.options));for(var s=t.next();!s.done;s=t.next()){s=s.value;var l=e.options[s].default;l!==void 0&&(this.l[s]=typeof l=="function"?l():l)}}}n=jr.prototype,n.close=function(){return this.j&&this.j.delete(),Promise.resolve()};function zi(e){var t,s,l,u,p,f,v,S,T,E,O;return z(function(I){switch(I.h){case 1:return e.ga?(t=e.h.files===void 0?[]:typeof e.h.files=="function"?e.h.files(e.l):e.h.files,Y(I,Bi(),2)):I.return();case 2:if(s=I.i,typeof window=="object")return gn("createMediapipeSolutionsWasm",{locateFile:e.locateFile}),gn("createMediapipeSolutionsPackedAssets",{locateFile:e.locateFile}),f=t.filter(function(M){return M.data!==void 0}),v=t.filter(function(M){return M.data===void 0}),S=Promise.all(f.map(function(M){var L=Xt(e,M.url);if(M.path!==void 0){var F=M.path;L=L.then(function(J){return e.overrideFile(F,J),Promise.resolve(J)})}return L})),T=Promise.all(v.map(function(M){return M.simd===void 0||M.simd&&s||!M.simd&&!s?Ni(e.locateFile(M.url,e.ha)):Promise.resolve()})).then(function(){var M,L,F;return z(function(J){if(J.h==1)return M=window.createMediapipeSolutionsWasm,L=window.createMediapipeSolutionsPackedAssets,F=e,Y(J,M(L),2);F.i=J.i,J.h=0})}),E=function(){return z(function(M){return e.h.graph&&e.h.graph.url?M=Y(M,Xt(e,e.h.graph.url),0):(M.h=0,M=void 0),M})}(),Y(I,Promise.all([T,S,E]),7);if(typeof importScripts!="function")throw Error("solutions can only be loaded on a web page or in a web worker");return l=t.filter(function(M){return M.simd===void 0||M.simd&&s||!M.simd&&!s}).map(function(M){return e.locateFile(M.url,e.ha)}),importScripts.apply(null,k(l)),u=e,Y(I,createMediapipeSolutionsWasm(Module),6);case 6:u.i=I.i,e.m=new OffscreenCanvas(1,1),e.i.canvas=e.m,p=e.i.GL.createContext(e.m,{antialias:!1,alpha:!1,va:typeof WebGL2RenderingContext<"u"?2:1}),e.i.GL.makeContextCurrent(p),I.h=4;break;case 7:if(e.m=document.createElement("canvas"),O=e.m.getContext("webgl2",{}),!O&&(O=e.m.getContext("webgl",{}),!O))return alert("Failed to create WebGL canvas context when passing video frame."),I.return();e.K=O,e.i.canvas=e.m,e.i.createContext(e.m,!0,!0,{});case 4:e.j=new e.i.SolutionWasm,e.ga=!1,I.h=0}})}function Vi(e){var t,s,l,u,p,f,v,S;return z(function(T){if(T.h==1){if(e.h.graph&&e.h.graph.url&&e.fa===e.h.graph.url)return T.return();if(e.u=!0,!e.h.graph||!e.h.graph.url){T.h=2;return}return e.fa=e.h.graph.url,Y(T,Xt(e,e.h.graph.url),3)}for(T.h!=2&&(t=T.i,e.j.loadGraph(t)),s=h(Object.keys(e.D)),l=s.next();!l.done;l=s.next())u=l.value,e.j.overrideFile(u,e.D[u]);if(e.D={},e.h.listeners)for(p=h(e.h.listeners),f=p.next();!f.done;f=p.next())v=f.value,qi(e,v);S=e.l,e.l={},e.setOptions(S),T.h=0})}n.reset=function(){var e=this;return z(function(t){e.j&&(e.j.reset(),e.s={},e.v={}),t.h=0})},n.setOptions=function(e,t){var s=this;if(t=t||this.h.options){for(var l=[],u=[],p={},f=h(Object.keys(e)),v=f.next();!v.done;p={X:p.X,Y:p.Y},v=f.next())if(v=v.value,!(v in this.l&&this.l[v]===e[v])){this.l[v]=e[v];var S=t[v];S!==void 0&&(S.onChange&&(p.X=S.onChange,p.Y=e[v],l.push(function(T){return function(){var E;return z(function(O){if(O.h==1)return Y(O,T.X(T.Y),2);E=O.i,E===!0&&(s.u=!0),O.h=0})}}(p))),S.graphOptionXref&&(v=Object.assign({},{calculatorName:"",calculatorIndex:0},S.graphOptionXref,{valueNumber:S.type===1?e[v]:0,valueBoolean:S.type===0?e[v]:!1,valueString:S.type===2?e[v]:""}),u.push(v)))}(l.length!==0||u.length!==0)&&(this.u=!0,this.H=(this.H===void 0?[]:this.H).concat(u),this.F=(this.F===void 0?[]:this.F).concat(l))}};function Gi(e){var t,s,l,u,p,f,v;return z(function(S){switch(S.h){case 1:if(!e.u)return S.return();if(!e.F){S.h=2;break}t=h(e.F),s=t.next();case 3:if(s.done){S.h=5;break}return l=s.value,Y(S,l(),4);case 4:s=t.next(),S.h=3;break;case 5:e.F=void 0;case 2:if(e.H){for(u=new e.i.GraphOptionChangeRequestList,p=h(e.H),f=p.next();!f.done;f=p.next())v=f.value,u.push_back(v);e.j.changeOptions(u),u.delete(),e.H=void 0}e.u=!1,S.h=0}})}n.initialize=function(){var e=this;return z(function(t){return t.h==1?Y(t,zi(e),2):t.h!=3?Y(t,Vi(e),3):Y(t,Gi(e),0)})};function Xt(e,t){var s,l;return z(function(u){return t in e.L?u.return(e.L[t]):(s=e.locateFile(t,""),l=fetch(s).then(function(p){return p.arrayBuffer()}),e.L[t]=l,u.return(l))})}n.overrideFile=function(e,t){this.j?this.j.overrideFile(e,t):this.D[e]=t},n.clearOverriddenFiles=function(){this.D={},this.j&&this.j.clearOverriddenFiles()},n.send=function(e,t){var s=this,l,u,p,f,v,S,T,E,O;return z(function(I){switch(I.h){case 1:return s.h.inputs?(l=1e3*(t??performance.now()),Y(I,s.I,2)):I.return();case 2:return Y(I,s.initialize(),3);case 3:for(u=new s.i.PacketDataList,p=h(Object.keys(e)),f=p.next();!f.done;f=p.next())if(v=f.value,S=s.h.inputs[v]){e:{var M=e[v];switch(S.type){case"video":var L=s.s[S.stream];if(L||(L=new Er(s.i,s.K),s.s[S.stream]=L),L.m===0&&(L.m=L.i.createTexture()),typeof HTMLVideoElement<"u"&&M instanceof HTMLVideoElement)var F=M.videoWidth,J=M.videoHeight;else typeof HTMLImageElement<"u"&&M instanceof HTMLImageElement?(F=M.naturalWidth,J=M.naturalHeight):(F=M.width,J=M.height);J={glName:L.m,width:F,height:J},F=L.h,F.canvas.width=J.width,F.canvas.height=J.height,F.activeTexture(F.TEXTURE0),L.i.bindTexture2d(L.m),F.texImage2D(F.TEXTURE_2D,0,F.RGBA,F.RGBA,F.UNSIGNED_BYTE,M),L.i.bindTexture2d(0),L=J;break e;case"detections":for(L=s.s[S.stream],L||(L=new wi(s.i),s.s[S.stream]=L),L.data||(L.data=new L.h.DetectionListData),L.data.reset(M.length),J=0;J<M.length;++J){F=M[J];var G=L.data,ne=G.setBoundingBox,ge=J,ce=F.la,B=new Kt;if(Re(B,1,ce.sa),Re(B,2,ce.ta),Re(B,3,ce.height),Re(B,4,ce.width),Re(B,5,ce.rotation),Ce(B,6,ce.pa),ce=B.l(),ne.call(G,ge,ce),F.ea)for(G=0;G<F.ea.length;++G){B=F.ea[G],ne=L.data,ge=ne.addNormalizedLandmark,ce=J,B=Object.assign({},B,{visibility:B.visibility?B.visibility:0});var ie=new St;Re(ie,1,B.x),Re(ie,2,B.y),Re(ie,3,B.z),B.visibility&&Re(ie,4,B.visibility),B=ie.l(),ge.call(ne,ce,B)}if(F.ba)for(G=0;G<F.ba.length;++G)ne=L.data,ge=ne.addClassification,ce=J,B=F.ba[G],ie=new et,Re(ie,2,B.qa),B.index&&Ce(ie,1,B.index),B.label&&Ce(ie,3,B.label),B.displayName&&Ce(ie,4,B.displayName),B=ie.l(),ge.call(ne,ce,B)}L=L.data;break e;default:L={}}}switch(T=L,E=S.stream,S.type){case"video":u.pushTexture2d(Object.assign({},T,{stream:E,timestamp:l}));break;case"detections":O=T,O.stream=E,O.timestamp=l,u.pushDetectionList(O);break;default:throw Error("Unknown input config type: '"+S.type+"'")}}return s.j.send(u),Y(I,s.I,4);case 4:u.delete(),I.h=0}})};function Ji(e,t,s){var l,u,p,f,v,S,T,E,O,I,M,L,F,J;return z(function(G){switch(G.h){case 1:if(!s)return G.return(t);for(l={},u=0,p=h(Object.keys(s)),f=p.next();!f.done;f=p.next())v=f.value,S=s[v],typeof S!="string"&&S.type==="texture"&&t[S.stream]!==void 0&&++u;1<u&&(e.M=!1),T=h(Object.keys(s)),f=T.next();case 2:if(f.done){G.h=4;break}if(E=f.value,O=s[E],typeof O=="string")return F=l,J=E,Y(G,Hi(e,E,t[O]),14);if(I=t[O.stream],O.type==="detection_list"){if(I){for(var ne=I.getRectList(),ge=I.getLandmarksList(),ce=I.getClassificationsList(),B=[],ie=0;ie<ne.size();++ie){var $e=Oi(ne.get(ie)),Wi=Te($e,1),Yi=Te($e,2),Ki=Te($e,3),Xi=Te($e,4),Zi=Te($e,5,0),Zt=void 0;Zt=Zt===void 0?0:Zt,$e={la:{sa:Wi,ta:Yi,height:Ki,width:Xi,rotation:Zi,pa:Ht(ye($e,6),Zt)},ea:xr(Ii(ge.get(ie)),St,1).map(Di),ba:Li(Ai(ce.get(ie)))},B.push($e)}ne=B}else ne=[];l[E]=ne,G.h=7;break}if(O.type==="proto_list"){if(I){for(ne=Array(I.size()),ge=0;ge<I.size();ge++)ne[ge]=I.get(ge);I.delete()}else ne=[];l[E]=ne,G.h=7;break}if(I===void 0){G.h=3;break}if(O.type==="float_list"){l[E]=I,G.h=7;break}if(O.type==="proto"){l[E]=I,G.h=7;break}if(O.type!=="texture")throw Error("Unknown output config type: '"+O.type+"'");return M=e.v[E],M||(M=new Er(e.i,e.K),e.v[E]=M),Y(G,hn(M,I,e.M),13);case 13:L=G.i,l[E]=L;case 7:O.transform&&l[E]&&(l[E]=O.transform(l[E])),G.h=3;break;case 14:F[J]=G.i;case 3:f=T.next(),G.h=2;break;case 4:return G.return(l)}})}function Hi(e,t,s){var l;return z(function(u){return typeof s=="number"||s instanceof Uint8Array||s instanceof e.i.Uint8BlobList?u.return(s):s instanceof e.i.Texture2dDataOut?(l=e.v[t],l||(l=new Er(e.i,e.K),e.v[t]=l),u.return(hn(l,s,e.M))):u.return(void 0)})}function qi(e,t){for(var s=t.name||"$",l=[].concat(k(t.wants)),u=new e.i.StringList,p=h(t.wants),f=p.next();!f.done;f=p.next())u.push_back(f.value);p=e.i.PacketListener.implement({onResults:function(v){for(var S={},T=0;T<t.wants.length;++T)S[l[T]]=v.get(T);var E=e.listeners[s];E&&(e.I=Ji(e,S,t.outs).then(function(O){O=E(O);for(var I=0;I<t.wants.length;++I){var M=S[l[I]];typeof M=="object"&&M.hasOwnProperty&&M.hasOwnProperty("delete")&&M.delete()}O&&(e.I=O)}))}}),e.j.attachMultiListener(u,p),u.delete()}n.onResults=function(e,t){this.listeners[t||"$"]=e},ve("Solution",jr),ve("OptionType",{BOOL:0,NUMBER:1,ua:2,0:"BOOL",1:"NUMBER",2:"STRING"});function mn(e){switch(e===void 0&&(e=0),e){case 1:return"selfie_segmentation_landscape.tflite";default:return"selfie_segmentation.tflite"}}function vn(e){var t=this;e=e||{},this.h=new jr({locateFile:e.locateFile,files:function(s){return[{simd:!0,url:"selfie_segmentation_solution_simd_wasm_bin.js"},{simd:!1,url:"selfie_segmentation_solution_wasm_bin.js"},{data:!0,url:mn(s.modelSelection)}]},graph:{url:"selfie_segmentation.binarypb"},listeners:[{wants:["segmentation_mask","image_transformed"],outs:{image:{type:"texture",stream:"image_transformed"},segmentationMask:{type:"texture",stream:"segmentation_mask"}}}],inputs:{image:{type:"video",stream:"input_frames_gpu"}},options:{useCpuInference:{type:0,graphOptionXref:{calculatorType:"InferenceCalculator",fieldName:"use_cpu_inference"},default:typeof window!="object"||window.navigator===void 0?!1:"iPad Simulator;iPhone Simulator;iPod Simulator;iPad;iPhone;iPod".split(";").includes(navigator.platform)||navigator.userAgent.includes("Mac")&&"ontouchend"in document},selfieMode:{type:0,graphOptionXref:{calculatorType:"GlScalerCalculator",calculatorIndex:1,fieldName:"flip_horizontal"}},modelSelection:{type:1,graphOptionXref:{calculatorType:"ConstantSidePacketCalculator",calculatorName:"ConstantSidePacketCalculatorModelSelection",fieldName:"int_value"},onChange:function(s){var l,u,p;return z(function(f){return f.h==1?(l=mn(s),u="third_party/mediapipe/modules/selfie_segmentation/"+l,Y(f,Xt(t.h,l),2)):(p=f.i,t.h.overrideFile(u,p),f.return(!0))})}}}})}n=vn.prototype,n.close=function(){return this.h.close(),Promise.resolve()},n.onResults=function(e){this.h.onResults(e)},n.initialize=function(){var e=this;return z(function(t){return Y(t,e.h.initialize(),0)})},n.reset=function(){this.h.reset()},n.send=function(e){var t=this;return z(function(s){return Y(s,t.h.send(e),0)})},n.setOptions=function(e){this.h.setOptions(e)},ve("SelfieSegmentation",vn),ve("VERSION","0.1.1675465747")}).call(_r)),_r}var Is=_s();class Os{constructor(){this.canvas=document.createElement("canvas"),this.ctx=this.canvas.getContext("2d"),this.backgroundImage=null,this.isBlur=!1,this.processingStream=null,this.videoProcessor=null,this.selfieSegmentation=null}async initialize(o){if(!o)return null;const i=document.createElement("video");return i.autoplay=!0,i.muted=!0,i.srcObject=new MediaStream([o]),await new Promise(r=>{i.onloadedmetadata=()=>{i.play(),r()}}),this.canvas.width=i.videoWidth,this.canvas.height=i.videoHeight,this.selfieSegmentation=new Is.SelfieSegmentation({locateFile:r=>`https://cdn.jsdelivr.net/npm/@mediapipe/selfie_segmentation/${r}`}),this.selfieSegmentation.setOptions({modelSelection:1,selfieMode:!0}),this.processingStream=this.canvas.captureStream(30),this.videoProcessor=setInterval(()=>{this.processFrame(i)},1e3/30),this.processingStream.getVideoTracks()[0]}setBackground(o){if(o==="blur"){this.isBlur=!0,this.backgroundImage=null;return}if(this.isBlur=!1,!o){this.backgroundImage=null;return}const i=new Image;i.crossOrigin="anonymous",i.src=o,i.onload=()=>{this.backgroundImage=i}}async processFrame(o){if(!this.ctx||!o||!this.selfieSegmentation)return;const i=await this.selfieSegmentation.send({image:o});i.segmentationMask&&(this.isBlur?(this.ctx.filter="blur(10px)",this.ctx.drawImage(o,0,0,this.canvas.width,this.canvas.height),this.ctx.filter="none"):this.backgroundImage&&this.ctx.drawImage(this.backgroundImage,0,0,this.canvas.width,this.canvas.height),this.ctx.globalCompositeOperation="source-over",this.ctx.drawImage(i.segmentationMask,0,0,this.canvas.width,this.canvas.height))}stop(){this.videoProcessor&&(clearInterval(this.videoProcessor),this.videoProcessor=null),this.processingStream&&(this.processingStream.getTracks().forEach(o=>o.stop()),this.processingStream=null),this.selfieSegmentation&&(this.selfieSegmentation.close(),this.selfieSegmentation=null)}}const Ls={key:0,class:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50"},Ds={class:"bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6 max-w-lg w-full mx-4"},$s={class:"flex items-center justify-between mb-4"},ws={class:"space-y-4"},Us={class:"flex items-center"},Fs=["value"],Ns={class:"bg-gray-50 dark:bg-gray-700 rounded-lg p-3"},Bs={class:"space-y-1 text-xs text-gray-600 dark:text-gray-400"},zs={key:0},Vs={class:"bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-3"},Gs={class:"flex items-start"},Js={class:"text-xs text-blue-700 dark:text-blue-300"},Hs={class:"flex justify-between items-center mt-6"},qs={class:"flex space-x-2"},Ws={__name:"MeetingInvitePopup",props:{visible:Boolean,inviteLink:String,meetingId:String,meetingName:String,hostName:String,linkSharingPolicy:{type:String,default:"host"}},emits:["close","linkShared"],setup(n,{emit:o}){const i=n,r=o,a=q(!1),c=V(()=>`Join me in "${i.meetingName||`Meeting ${i.meetingId}`}" on TheMeet!

Meeting Link: ${i.inviteLink}
Meeting ID: ${i.meetingId}

Hosted by: ${i.hostName}`);function m(){navigator.clipboard.writeText(i.inviteLink).then(()=>{a.value=!0,setTimeout(()=>a.value=!1,2e3),b("copy")}).catch(g=>{console.error("Failed to copy link:",g);const y=document.createElement("textarea");y.value=i.inviteLink,document.body.appendChild(y),y.select(),document.execCommand("copy"),document.body.removeChild(y),a.value=!0,setTimeout(()=>a.value=!1,2e3),b("copy")})}function h(){const g=encodeURIComponent(`Join me in "${i.meetingName||"TheMeet Meeting"}"`),y=encodeURIComponent(c.value);window.open(`mailto:?subject=${g}&body=${y}`),b("email")}function k(){const g=encodeURIComponent(c.value);window.open(`https://wa.me/?text=${g}`),b("whatsapp")}function b(g="manual"){r("linkShared",{method:g,timestamp:new Date().toISOString(),link:i.inviteLink})}return jt(()=>i.visible,g=>{g||(a.value=!1)}),(g,y)=>n.visible?(P(),j("div",Ls,[d("div",Ds,[d("div",$s,[y[3]||(y[3]=d("h2",{class:"text-xl font-semibold text-gray-900 dark:text-white"},[d("i",{class:"fas fa-share-alt mr-2 text-primary"}),H(" Invite Participants ")],-1)),d("button",{onClick:y[0]||(y[0]=C=>g.$emit("close")),class:"text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"},y[2]||(y[2]=[d("i",{class:"fas fa-times text-lg"},null,-1)]))]),d("div",ws,[d("div",null,[y[4]||(y[4]=d("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"},[d("i",{class:"fas fa-link mr-1"}),H(" Meeting Link ")],-1)),d("div",Us,[d("input",{value:n.inviteLink,readonly:"",class:"flex-1 p-3 border rounded-l-lg bg-gray-50 dark:bg-gray-700 text-gray-900 dark:text-white text-sm font-mono",id:"invite-link-input"},null,8,Fs),d("button",{onClick:m,class:W(["px-4 py-3 bg-primary text-white rounded-r-lg hover:bg-primary-dark transition-colors flex items-center",{"bg-green-500 hover:bg-green-600":a.value}])},[d("i",{class:W([a.value?"fas fa-check":"fas fa-copy","mr-1"])},null,2),H(" "+$(a.value?"Copied!":"Copy"),1)],2)])]),d("div",null,[y[7]||(y[7]=d("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"},[d("i",{class:"fas fa-share mr-1"}),H(" Quick Share ")],-1)),d("div",{class:"grid grid-cols-2 gap-2"},[d("button",{onClick:h,class:"flex items-center justify-center p-3 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"},y[5]||(y[5]=[d("i",{class:"fas fa-envelope mr-2 text-blue-500"},null,-1),d("span",{class:"text-sm"},"Email",-1)])),d("button",{onClick:k,class:"flex items-center justify-center p-3 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"},y[6]||(y[6]=[d("i",{class:"fab fa-whatsapp mr-2 text-green-500"},null,-1),d("span",{class:"text-sm"},"WhatsApp",-1)]))])]),d("div",Ns,[y[11]||(y[11]=d("h3",{class:"text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"},"Meeting Information",-1)),d("div",Bs,[d("div",null,[y[8]||(y[8]=d("strong",null,"Meeting ID:",-1)),H(" "+$(n.meetingId),1)]),d("div",null,[y[9]||(y[9]=d("strong",null,"Host:",-1)),H(" "+$(n.hostName),1)]),n.meetingName?(P(),j("div",zs,[y[10]||(y[10]=d("strong",null,"Title:",-1)),H(" "+$(n.meetingName),1)])):N("",!0)])]),d("div",Vs,[d("div",Gs,[y[13]||(y[13]=d("i",{class:"fas fa-shield-alt text-blue-500 mr-2 mt-0.5"},null,-1)),d("div",Js,[y[12]||(y[12]=d("strong",null,"Security Notice:",-1)),H(" External users joining via this link will be prompted to enter their name before joining the meeting. "+$(n.linkSharingPolicy==="host"?"Only you can share this link.":"All participants can share this link."),1)])])])]),d("div",Hs,[d("button",{onClick:b,class:"text-sm text-primary hover:text-primary-dark flex items-center"},y[14]||(y[14]=[d("i",{class:"fas fa-chart-line mr-1"},null,-1),H(" Mark as Shared ")])),d("div",qs,[d("button",{onClick:y[1]||(y[1]=C=>g.$emit("close")),class:"px-4 py-2 bg-gray-200 dark:bg-gray-700 text-gray-800 dark:text-gray-200 rounded-lg hover:bg-gray-300 dark:hover:bg-gray-600 transition-colors"}," Close ")])])])])):N("",!0)}},Ys={key:0,class:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50"},Ks={class:"bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6 max-w-md w-full mx-4"},Xs={key:0,class:"bg-gray-50 dark:bg-gray-700 rounded-lg p-3 mb-4"},Zs={class:"text-center"},Qs={class:"font-medium text-gray-900 dark:text-white"},eo={class:"text-xs text-gray-600 dark:text-gray-400 mt-1"},to={class:"flex items-center justify-center mt-2 text-xs text-gray-500 dark:text-gray-400"},ro=["disabled"],no={class:"flex justify-end space-x-3 pt-2"},io=["disabled"],so=["disabled"],oo={key:0,class:"fas fa-spinner fa-spin mr-2"},ao={key:1,class:"fas fa-sign-in-alt mr-2"},lo={__name:"UsernamePromptPopup",props:{visible:Boolean,meetingInfo:{type:Object,default:()=>({})},sharedBy:String,joinViaLink:Boolean},emits:["submit","cancel"],setup(n,{emit:o}){const i=n,r=o,a=q(""),c=q(!1);function m(){a.value.trim()&&!c.value&&(c.value=!0,r("submit",{username:a.value.trim(),joinMethod:"shared_link",sharedBy:i.sharedBy,timestamp:new Date().toISOString()}),setTimeout(()=>{a.value="",c.value=!1},1e3))}return jt(()=>i.visible,h=>{h?setTimeout(()=>{const k=document.getElementById("username");k&&k.focus()},100):(a.value="",c.value=!1)}),lr(()=>{const h=localStorage.getItem("userName");h&&!a.value&&(a.value=h)}),(h,k)=>n.visible?(P(),j("div",Ys,[d("div",Ks,[k[6]||(k[6]=rs('<div class="text-center mb-6"><div class="w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-4"><i class="fas fa-user-plus text-2xl text-primary"></i></div><h2 class="text-xl font-semibold text-gray-900 dark:text-white mb-2">Join Meeting</h2><p class="text-sm text-gray-600 dark:text-gray-400"> You&#39;re joining via a shared link. Please enter your name to continue. </p></div>',1)),n.meetingInfo?(P(),j("div",Xs,[d("div",Zs,[d("h3",Qs,$(n.meetingInfo.name||"TheMeet Meeting"),1),d("p",eo," Hosted by "+$(n.meetingInfo.hostName||"Host"),1),d("div",to,[k[2]||(k[2]=d("i",{class:"fas fa-users mr-1"},null,-1)),H(" "+$(n.meetingInfo.participantCount||0)+" participant"+$((n.meetingInfo.participantCount||0)!==1?"s":""),1)])])])):N("",!0),d("form",{onSubmit:jn(m,["prevent"]),class:"space-y-4"},[d("div",null,[k[3]||(k[3]=d("label",{for:"username",class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"},[H(" Your Name "),d("span",{class:"text-red-500"},"*")],-1)),Tt(d("input",{id:"username","onUpdate:modelValue":k[0]||(k[0]=b=>a.value=b),type:"text",class:"w-full p-3 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:ring-2 focus:ring-primary focus:border-transparent",placeholder:"Enter your full name",maxlength:"50",required:"",autocomplete:"name",disabled:c.value},null,8,ro),[[rr,a.value]]),k[4]||(k[4]=d("p",{class:"text-xs text-gray-500 dark:text-gray-400 mt-1"}," This name will be visible to other participants ",-1))]),k[5]||(k[5]=d("div",{class:"bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-3"},[d("div",{class:"flex items-start"},[d("i",{class:"fas fa-shield-alt text-blue-500 mr-2 mt-0.5 text-sm"}),d("div",{class:"text-xs text-blue-700 dark:text-blue-300"},[d("strong",null,"Secure Meeting:"),H(" Your join will be tracked for security purposes. The host can see who shared the link that you used to join. ")])])],-1)),d("div",no,[d("button",{type:"button",onClick:k[1]||(k[1]=b=>h.$emit("cancel")),class:"px-4 py-2 bg-gray-200 dark:bg-gray-700 text-gray-800 dark:text-gray-200 rounded-lg hover:bg-gray-300 dark:hover:bg-gray-600 transition-colors",disabled:c.value}," Cancel ",8,io),d("button",{type:"submit",class:"px-6 py-2 bg-primary text-white rounded-lg hover:bg-primary-dark transition-colors flex items-center",disabled:!a.value.trim()||c.value},[c.value?(P(),j("i",oo)):(P(),j("i",ao)),H(" "+$(c.value?"Joining...":"Join Meeting"),1)],8,so)])],32)])])):N("",!0)}},co=["aria-label","role"],uo={__name:"SvgIcon",props:{name:{type:String,required:!0,validator:n=>["camera-off","camera-on","microphone-off","microphone-on","user-avatar","broadcasting","screen-share","chat"].includes(n)},size:{type:[String,Number],default:"6"},color:{type:String,default:"currentColor"},ariaLabel:{type:String,default:null},role:{type:String,default:"img"}},setup(n){const o=n,i={"camera-off":()=>({template:`
      <path d="M3.707 2.293a1 1 0 0 0-1.414 1.414l18 18a1 1 0 0 0 1.414-1.414l-18-18z"/>
      <path d="M21 6.5l-4 4v-1.5a2 2 0 0 0-2-2H9.414l-2-2H15a4 4 0 0 1 4 4v1.5l2-2v8.5a1 1 0 0 1-1.707.707L21 6.5z"/>
      <path d="M3 7a1 1 0 0 1 1-1h.586l2 2H4v8a2 2 0 0 0 2 2h8a2 2 0 0 0 2-2v-.586l2 2H6a4 4 0 0 1-4-4V7z"/>
    `}),"camera-on":()=>({template:'<path d="M4 6a2 2 0 0 1 2-2h8a2 2 0 0 1 2 2v1.5l4-4v13l-4-4V18a2 2 0 0 1-2 2H6a2 2 0 0 1-2-2V6z"/>'}),"microphone-off":()=>({template:`
      <path d="M3.707 2.293a1 1 0 0 0-1.414 1.414l18 18a1 1 0 0 0 1.414-1.414l-18-18z"/>
      <path d="M12 2a3 3 0 0 1 3 3v6c0 .386-.074.752-.121 1.121l-1.758-1.758A1 1 0 0 0 13 10V5a1 1 0 0 0-2 0v3.586l-2-2V5a3 3 0 0 1 3-3z"/>
      <path d="M5.586 9H5a1 1 0 0 0-1 1v1a8 8 0 0 0 8 8 8.001 8.001 0 0 0 6.929-4.071l1.414 1.414A9.969 9.969 0 0 1 12 21a10 10 0 0 1-10-10v-1a1 1 0 0 1 1-1h1.586l2 2z"/>
    `}),"microphone-on":()=>({template:`
      <path d="M12 2a3 3 0 0 1 3 3v6a3 3 0 0 1-6 0V5a3 3 0 0 1 3-3z"/>
      <path d="M19 10v1a7 7 0 0 1-14 0v-1a1 1 0 0 1 2 0v1a5 5 0 0 0 10 0v-1a1 1 0 0 1 2 0z"/>
      <path d="M12 18.95a1 1 0 0 1 1 1V22a1 1 0 0 1-2 0v-2.05a1 1 0 0 1 1-1z"/>
    `}),"screen-share":()=>({template:`
      <path d="M20 3H4c-1.11 0-2 .89-2 2v11c0 1.11.89 2 2 2h4v2h8v-2h4c1.11 0 2-.89 2-2V5c0-1.11-.89-2-2-2zm0 13H4V5h16v11z"/>
      <path d="M6.5 7.5v1L10 6l-3.5-2.5v1H5v3h1.5zM17.5 16.5v-1L14 18l3.5 2.5v-1H19v-3h-1.5z"/>
    `}),chat:()=>({template:`
      <path d="M20 2H4c-1.1 0-2 .9-2 2v12c0 1.1.9 2 2 2h4v3c0 .6.4 1 1 1 .2 0 .5-.1.7-.3L14.4 18H20c1.1 0 2-.9 2-2V4c0-1.1-.9-2-2-2z"/>
      <circle cx="8" cy="10" r="1"/>
      <circle cx="12" cy="10" r="1"/>
      <circle cx="16" cy="10" r="1"/>
    `})},r=V(()=>{const h=i[o.name];return h?h():null}),a=V(()=>[typeof o.size=="string"&&o.size.includes("w-")?o.size:`w-${o.size} h-${o.size}`,"inline-block","flex-shrink-0"]),c=V(()=>({color:o.color})),m=V(()=>o.ariaLabel?o.ariaLabel:{"camera-off":"Camera is off","camera-on":"Camera is on","microphone-off":"Microphone is muted","microphone-on":"Microphone is active","user-avatar":"User avatar",broadcasting:"Broadcasting","screen-share":"Screen sharing",chat:"Chat"}[o.name]||o.name);return(h,k)=>(P(),j("svg",ns({class:a.value,style:c.value,"aria-label":m.value,role:n.role,fill:"currentColor",viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg"},h.$attrs),[(P(),we(Mn(r.value)))],16,co))}},er=cr(uo,[["__scopeId","data-v-39e54c6f"]]),fo={class:"bg-white dark:bg-gray-800 shadow-lg px-4 py-3 flex justify-center items-center space-x-4"},po=["title","aria-label"],ho=["title","aria-label"],go=["title","aria-label"],mo=["title","aria-label"],vo=["title"],yo=["title"],bo={class:"absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center"},xo=["title"],ko={class:"absolute -bottom-1 -right-1 text-xs"},So={key:0,class:"fas fa-users text-green-500"},Co={key:1,class:"fas fa-crown text-orange-500"},Ro={__name:"MeetingControls",props:{isMicMuted:{type:Boolean,default:!1},isCameraOff:{type:Boolean,default:!1},isScreenSharing:{type:Boolean,default:!1},isChatOpen:{type:Boolean,default:!1},isAISEnabled:{type:Boolean,default:!1},isReactionsEnabled:{type:Boolean,default:!1},isBreakoutRoomsEnabled:{type:Boolean,default:!1},isVirtualBackgroundEnabled:{type:Boolean,default:!1},isWhiteboardEnabled:{type:Boolean,default:!1},isRecordingEnabled:{type:Boolean,default:!1},isRecording:{type:Boolean,default:!1},hasJoinRequests:{type:Boolean,default:!1},showJoinRequestsPanel:{type:Boolean,default:!1},joinRequestsCount:{type:Number,default:0},isAdmin:{type:Boolean,default:!1},canShareLink:{type:Boolean,default:!1},linkSharingPolicy:{type:String,default:"host"}},emits:["toggle-mic","toggle-camera","toggle-screen","toggle-chat","toggle-ais","toggle-reactions","toggle-breakout-rooms","toggle-virtual-background","toggle-whiteboard","toggle-recording","toggle-join-requests","show-invite","show-link-sharing","leave-meeting"],setup(n){return(o,i)=>(P(),j("div",fo,[d("button",{onClick:i[0]||(i[0]=r=>o.$emit("toggle-mic")),class:W(["p-3 rounded-full transition-colors duration-200",n.isMicMuted?"bg-red-100 text-red-600 dark:bg-red-900 dark:text-red-300":"bg-gray-100 text-gray-700 dark:bg-gray-700 dark:text-gray-300"]),title:n.isMicMuted?"Unmute microphone":"Mute microphone","aria-label":n.isMicMuted?"Unmute microphone":"Mute microphone"},[se(er,{name:n.isMicMuted?"microphone-off":"microphone-on",size:"5","aria-label":n.isMicMuted?"Microphone is muted":"Microphone is active"},null,8,["name","aria-label"])],10,po),d("button",{onClick:i[1]||(i[1]=r=>o.$emit("toggle-camera")),class:W(["p-3 rounded-full transition-colors duration-200",n.isCameraOff?"bg-red-100 text-red-600 dark:bg-red-900 dark:text-red-300":"bg-gray-100 text-gray-700 dark:bg-gray-700 dark:text-gray-300"]),title:n.isCameraOff?"Turn on camera":"Turn off camera","aria-label":n.isCameraOff?"Turn on camera":"Turn off camera"},[se(er,{name:n.isCameraOff?"camera-off":"camera-on",size:"5","aria-label":n.isCameraOff?"Camera is off":"Camera is on"},null,8,["name","aria-label"])],10,ho),d("button",{onClick:i[2]||(i[2]=r=>o.$emit("toggle-screen")),class:W(["p-3 rounded-full transition-colors duration-200",n.isScreenSharing?"bg-green-100 text-green-600 dark:bg-green-900 dark:text-green-300":"bg-gray-100 text-gray-700 dark:bg-gray-700 dark:text-gray-300"]),title:n.isScreenSharing?"Stop sharing screen":"Share screen","aria-label":n.isScreenSharing?"Stop sharing screen":"Share screen"},[se(er,{name:"screen-share",size:"5","aria-label":n.isScreenSharing?"Screen sharing active":"Start screen sharing"},null,8,["aria-label"])],10,go),d("button",{onClick:i[3]||(i[3]=r=>o.$emit("toggle-chat")),class:W(["p-3 rounded-full transition-colors duration-200",n.isChatOpen?"bg-primary-light text-white":"bg-gray-100 text-gray-700 dark:bg-gray-700 dark:text-gray-300"]),title:n.isChatOpen?"Close chat":"Open chat","aria-label":n.isChatOpen?"Close chat":"Open chat"},[se(er,{name:"chat",size:"5","aria-label":n.isChatOpen?"Chat is open":"Open chat"},null,8,["aria-label"])],10,mo),d("button",{onClick:i[4]||(i[4]=r=>o.$emit("toggle-ais")),class:W(["p-3 rounded-full",n.isAISEnabled?"bg-purple-100 text-purple-600 dark:bg-purple-900 dark:text-purple-300":"bg-gray-100 text-gray-700 dark:bg-gray-700 dark:text-gray-300"]),title:n.isAISEnabled?"Disable transcription":"Enable transcription"},i[14]||(i[14]=[d("i",{class:"fas fa-closed-captioning"},null,-1)]),10,vo),d("button",{onClick:i[5]||(i[5]=r=>o.$emit("toggle-reactions")),class:W(["p-3 rounded-full",n.isReactionsEnabled?"bg-yellow-100 text-yellow-600 dark:bg-yellow-900 dark:text-yellow-300":"bg-gray-100 text-gray-700 dark:bg-gray-700 dark:text-gray-300"]),title:"Toggle reactions"},i[15]||(i[15]=[d("i",{class:"fas fa-smile"},null,-1)]),2),n.isAdmin?(P(),j("button",{key:0,onClick:i[6]||(i[6]=r=>o.$emit("toggle-breakout-rooms")),class:W(["p-3 rounded-full",n.isBreakoutRoomsEnabled?"bg-blue-100 text-blue-600 dark:bg-blue-900 dark:text-blue-300":"bg-gray-100 text-gray-700 dark:bg-gray-700 dark:text-gray-300"]),title:"Toggle breakout rooms"},i[16]||(i[16]=[d("i",{class:"fas fa-th-large"},null,-1)]),2)):N("",!0),d("button",{onClick:i[7]||(i[7]=r=>o.$emit("toggle-virtual-background")),class:W(["p-3 rounded-full",n.isVirtualBackgroundEnabled?"bg-indigo-100 text-indigo-600 dark:bg-indigo-900 dark:text-indigo-300":"bg-gray-100 text-gray-700 dark:bg-gray-700 dark:text-gray-300"]),title:"Toggle virtual background"},i[17]||(i[17]=[d("i",{class:"fas fa-image"},null,-1)]),2),d("button",{onClick:i[8]||(i[8]=r=>o.$emit("toggle-whiteboard")),class:W(["p-3 rounded-full",n.isWhiteboardEnabled?"bg-pink-100 text-pink-600 dark:bg-pink-900 dark:text-pink-300":"bg-gray-100 text-gray-700 dark:bg-gray-700 dark:text-gray-300"]),title:"Toggle whiteboard"},i[18]||(i[18]=[d("i",{class:"fas fa-edit"},null,-1)]),2),d("button",{onClick:i[9]||(i[9]=r=>o.$emit("toggle-recording")),class:W(["p-3 rounded-full",n.isRecordingEnabled?n.isRecording?"bg-red-100 text-red-600 dark:bg-red-900 dark:text-red-300 animate-pulse":"bg-green-100 text-green-600 dark:bg-green-900 dark:text-green-300":"bg-gray-100 text-gray-700 dark:bg-gray-700 dark:text-gray-300"]),title:n.isRecordingEnabled?n.isRecording?"Recording in progress":"Stop recording":"Start recording"},[d("i",{class:W(n.isRecording?"fas fa-stop-circle":"fas fa-record-vinyl")},null,2)],10,yo),n.hasJoinRequests?(P(),j("button",{key:1,onClick:i[10]||(i[10]=r=>o.$emit("toggle-join-requests")),class:W(["p-3 rounded-full relative",n.showJoinRequestsPanel?"bg-primary-light text-white":"bg-gray-100 text-gray-700 dark:bg-gray-700 dark:text-gray-300"]),title:"Manage join requests"},[i[19]||(i[19]=d("i",{class:"fas fa-user-plus"},null,-1)),d("span",bo,$(n.joinRequestsCount),1)],2)):N("",!0),n.canShareLink?(P(),j("button",{key:2,onClick:i[11]||(i[11]=r=>o.$emit("show-invite")),class:"p-3 rounded-full bg-blue-100 text-blue-600 dark:bg-blue-900 dark:text-blue-300 hover:bg-blue-200 dark:hover:bg-blue-800 transition-colors",title:"Invite participants"},i[20]||(i[20]=[d("i",{class:"fas fa-user-plus"},null,-1)]))):N("",!0),n.isAdmin?(P(),j("button",{key:3,onClick:i[12]||(i[12]=r=>o.$emit("show-link-sharing")),class:W(["p-3 rounded-full relative",n.linkSharingPolicy==="all"?"bg-green-100 text-green-600 dark:bg-green-900 dark:text-green-300":"bg-orange-100 text-orange-600 dark:bg-orange-900 dark:text-orange-300"]),title:n.linkSharingPolicy==="all"?"Link sharing: All members":"Link sharing: Host only"},[i[21]||(i[21]=d("i",{class:"fas fa-share-alt"},null,-1)),d("span",ko,[n.linkSharingPolicy==="all"?(P(),j("i",So)):(P(),j("i",Co))])],10,xo)):N("",!0),d("button",{onClick:i[13]||(i[13]=r=>o.$emit("leave-meeting")),class:"p-3 rounded-full bg-red-100 text-red-600 dark:bg-red-900 dark:text-red-300",title:"Leave meeting"},i[22]||(i[22]=[d("i",{class:"fas fa-sign-out-alt"},null,-1)]))]))}},rt=[{id:"none",name:"None",url:null},{id:"blur",name:"Blur",url:"blur"}],To={class:"virtual-background-selector bg-white dark:bg-gray-800 rounded-lg shadow-lg overflow-hidden"},Po={class:"p-4"},Eo={class:"grid grid-cols-3 gap-3"},jo=["onClick"],Mo={key:0,class:"w-full h-full flex items-center justify-center bg-gray-100 dark:bg-gray-700"},Ao={key:1,class:"w-full h-full flex items-center justify-center bg-gray-200 dark:bg-gray-600 backdrop-blur"},_o=["src","alt"],Io={class:"mt-4"},Oo={class:"flex"},Lo={class:"p-4 bg-gray-50 dark:bg-gray-700 flex justify-end"},Do={__name:"VirtualBackgroundSelector",props:{currentBackground:{type:String,default:"none"},stream:{type:Object,default:null}},emits:["apply","cancel"],setup(n,{emit:o}){const i=n,r=o,a=q(i.currentBackground),c=q(null),m=q(null);lr(()=>{a.value=i.currentBackground});const h=g=>{a.value=g.id},k=g=>{const y=g.target.files[0];if(!y)return;c.value=URL.createObjectURL(y);const C={id:"custom",name:"Custom",url:c.value};if(!rt.find(A=>A.id==="custom"))rt.push(C);else{const A=rt.findIndex(U=>U.id==="custom");rt[A]=C}a.value="custom"},b=()=>{const g=rt.find(y=>y.id===a.value);g&&r("apply",{id:g.id,url:g.url})};return(g,y)=>(P(),j("div",To,[y[5]||(y[5]=d("div",{class:"p-4 border-b border-gray-200 dark:border-gray-700"},[d("h2",{class:"text-lg font-semibold text-gray-900 dark:text-white"},"Virtual Background")],-1)),d("div",Po,[d("div",Eo,[(P(!0),j(me,null,xe(D(rt),C=>(P(),j("div",{key:C.id,onClick:A=>h(C),class:W(["aspect-video rounded-lg overflow-hidden cursor-pointer border-2",{"border-primary":a.value===C.id,"border-transparent":a.value!==C.id}])},[C.id==="none"?(P(),j("div",Mo,y[2]||(y[2]=[d("span",{class:"text-gray-700 dark:text-gray-300"},"None",-1)]))):C.id==="blur"?(P(),j("div",Ao,y[3]||(y[3]=[d("span",{class:"text-gray-700 dark:text-gray-300"},"Blur",-1)]))):(P(),j("img",{key:2,src:C.url,alt:C.name,class:"w-full h-full object-cover"},null,8,_o))],10,jo))),128))]),d("div",Io,[y[4]||(y[4]=d("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"}," Custom Background ",-1)),d("div",Oo,[d("input",{type:"file",accept:"image/*",onChange:k,class:"hidden",ref_key:"fileInput",ref:m},null,544),d("button",{onClick:y[0]||(y[0]=C=>g.$refs.fileInput.click()),class:"btn btn-outline flex-1"}," Upload Image ")])])]),d("div",Lo,[d("button",{onClick:y[1]||(y[1]=C=>g.$emit("cancel")),class:"btn btn-outline mr-2"}," Cancel "),d("button",{onClick:b,class:"btn btn-primary"}," Apply ")])]))}},$o={class:"bg-white dark:bg-gray-800 shadow-lg border-l border-gray-200 dark:border-gray-700 w-80 flex flex-col"},wo={class:"p-4 border-b border-gray-200 dark:border-gray-700 flex justify-between items-center"},Uo={class:"p-4 border-b border-gray-200 dark:border-gray-700 space-y-3"},Fo=["value"],No={class:"flex-1 overflow-y-auto"},Bo={class:"p-4"},zo={key:0,class:"text-center py-8"},Vo={key:1,class:"space-y-3"},Go={key:0,class:"flex items-start space-x-3"},Jo={class:"flex-1 min-w-0"},Ho={class:"text-sm text-gray-900 dark:text-white"},qo={class:"flex items-center mt-1 text-xs text-gray-500 dark:text-gray-400"},Wo={key:0,class:"ml-2 px-2 py-0.5 bg-gray-200 dark:bg-gray-600 rounded text-xs"},Yo={key:1,class:"flex items-start space-x-3"},Ko={class:"flex-1 min-w-0"},Xo={class:"text-sm text-gray-900 dark:text-white"},Zo={class:"flex items-center mt-1 text-xs text-gray-500 dark:text-gray-400"},Qo={key:0,class:"ml-2 text-xs"},ea={class:"p-4 border-t border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-700"},ta={class:"grid grid-cols-2 gap-4 text-center"},ra={class:"text-lg font-semibold text-primary"},na={class:"text-lg font-semibold text-green-600"},ia={__name:"LinkSharingPanel",props:{activities:{type:Array,default:()=>[]},linkSharingPolicy:{type:String,default:"host"},participants:{type:Array,default:()=>[]}},emits:["close","updatePolicy","showInvite"],setup(n,{emit:o}){const i=n,r=V(()=>[...i.activities].sort((k,b)=>new Date(b.timestamp)-new Date(k.timestamp))),a=V(()=>i.activities.filter(k=>k.type==="link_shared").length),c=V(()=>i.activities.filter(k=>k.type==="user_joined").length);function m(k){const b=i.participants.find(g=>g.id===k||g.userId===k);return(b==null?void 0:b.userName)||"Unknown User"}function h(k){return k?(k.toDate?k.toDate():new Date(k)).toLocaleTimeString([],{hour:"2-digit",minute:"2-digit"}):""}return(k,b)=>(P(),j("div",$o,[d("div",wo,[b[4]||(b[4]=d("h2",{class:"font-semibold text-gray-900 dark:text-white flex items-center"},[d("i",{class:"fas fa-share-alt mr-2 text-primary"}),H(" Link Activity ")],-1)),d("button",{onClick:b[0]||(b[0]=g=>k.$emit("close")),class:"text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"},b[3]||(b[3]=[d("i",{class:"fas fa-times"},null,-1)]))]),d("div",Uo,[d("div",null,[b[6]||(b[6]=d("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"}," Who can share the meeting link? ",-1)),d("select",{value:n.linkSharingPolicy,onChange:b[1]||(b[1]=g=>k.$emit("updatePolicy",g.target.value)),class:"w-full p-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white text-sm"},b[5]||(b[5]=[d("option",{value:"host"},"Host Only",-1),d("option",{value:"all"},"All Participants",-1)]),40,Fo)]),d("button",{onClick:b[2]||(b[2]=g=>k.$emit("showInvite")),class:"w-full btn btn-primary flex items-center justify-center"},b[7]||(b[7]=[d("i",{class:"fas fa-share mr-2"},null,-1),H(" Share Meeting Link ")]))]),d("div",No,[d("div",Bo,[b[15]||(b[15]=d("h3",{class:"text-sm font-medium text-gray-700 dark:text-gray-300 mb-3 flex items-center"},[d("i",{class:"fas fa-history mr-2"}),H(" Recent Activity ")],-1)),n.activities.length===0?(P(),j("div",zo,b[8]||(b[8]=[d("i",{class:"fas fa-share-alt text-4xl text-gray-300 dark:text-gray-600 mb-3"},null,-1),d("p",{class:"text-sm text-gray-500 dark:text-gray-400"},"No link sharing activity yet",-1)]))):(P(),j("div",Vo,[(P(!0),j(me,null,xe(r.value,g=>(P(),j("div",{key:g.id,class:"bg-gray-50 dark:bg-gray-700 rounded-lg p-3 border border-gray-200 dark:border-gray-600"},[g.type==="link_shared"?(P(),j("div",Go,[b[11]||(b[11]=d("div",{class:"w-8 h-8 bg-blue-100 dark:bg-blue-900 rounded-full flex items-center justify-center flex-shrink-0"},[d("i",{class:"fas fa-share text-blue-600 dark:text-blue-400 text-sm"})],-1)),d("div",Jo,[d("p",Ho,[d("strong",null,$(m(g.sharedBy)),1),b[9]||(b[9]=H(" shared the meeting link "))]),d("div",qo,[b[10]||(b[10]=d("i",{class:"fas fa-clock mr-1"},null,-1)),H(" "+$(h(g.timestamp))+" ",1),g.method?(P(),j("span",Wo,$(g.method),1)):N("",!0)])])])):g.type==="user_joined"?(P(),j("div",Yo,[b[14]||(b[14]=d("div",{class:"w-8 h-8 bg-green-100 dark:bg-green-900 rounded-full flex items-center justify-center flex-shrink-0"},[d("i",{class:"fas fa-user-plus text-green-600 dark:text-green-400 text-sm"})],-1)),d("div",Ko,[d("p",Xo,[d("strong",null,$(g.joinedName),1),b[12]||(b[12]=H(" joined via shared link "))]),d("div",Zo,[b[13]||(b[13]=d("i",{class:"fas fa-clock mr-1"},null,-1)),H(" "+$(h(g.timestamp))+" ",1),g.sharedBy?(P(),j("span",Qo," (Link shared by "+$(m(g.sharedBy))+") ",1)):N("",!0)])])])):N("",!0)]))),128))]))])]),d("div",ea,[b[18]||(b[18]=d("h3",{class:"text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"},"Statistics",-1)),d("div",ta,[d("div",null,[d("div",ra,$(a.value),1),b[16]||(b[16]=d("div",{class:"text-xs text-gray-500 dark:text-gray-400"},"Links Shared",-1))]),d("div",null,[d("div",na,$(c.value),1),b[17]||(b[17]=d("div",{class:"text-xs text-gray-500 dark:text-gray-400"},"Joined via Link",-1))])])])]))}},Ct=q([]);let sa=0;function di(){const n=y=>{const C=++sa,A={id:C,...y,timestamp:new Date};Ct.value.push(A);const U=y.autoRemoveTime||5e3;return y.autoDismiss!==!1&&setTimeout(()=>{o(C)},U),C},o=y=>{const C=Ct.value.findIndex(A=>A.id===y);C>-1&&Ct.value.splice(C,1)};return{notifications:Ct,addNotification:n,removeNotification:o,clearAllNotifications:()=>{Ct.value=[]},showSuccess:(y,C,A={})=>n({type:"success",title:y,message:C,...A}),showError:(y,C,A={})=>n({type:"error",title:y,message:C,autoDismiss:!1,...A}),showWarning:(y,C,A={})=>n({type:"warning",title:y,message:C,...A}),showInfo:(y,C,A={})=>n({type:"info",title:y,message:C,...A}),showLinkShared:(y,C,A={})=>n({type:"link_shared",title:"Meeting Link Shared",message:`${y} shared the meeting link${C?` via ${C}`:""}`,...A}),showUserJoinedViaLink:(y,C,A={})=>n({type:"user_joined",title:"New Participant Joined",message:`${y} joined via link shared by ${C}`,...A}),showInvitePrompt:(y,C={})=>n({type:"info",title:"Invite Participants",message:y===1?"You're alone in the meeting. Invite others to join!":"Share the meeting link to invite more participants",actions:[{label:"Share Link",primary:!0,action:"show_invite"},{label:"Dismiss",action:"dismiss"}],autoDismiss:!1,...C}),showLinkSharingPolicyChanged:(y,C,A={})=>n({type:"info",title:"Link Sharing Policy Updated",message:`${C} changed link sharing to: ${y==="all"?"All participants":"Host only"}`,...A})}}const oa={key:0,class:"max-w-sm w-full bg-white dark:bg-gray-800 shadow-lg rounded-lg pointer-events-auto ring-1 ring-black ring-opacity-5 overflow-hidden"},aa={class:"p-4"},la={class:"flex items-start"},ca={class:"flex-shrink-0"},ua={class:"ml-3 w-0 flex-1 pt-0.5"},da={class:"text-sm font-medium text-gray-900 dark:text-white"},fa={class:"mt-1 text-sm text-gray-500 dark:text-gray-400"},pa={key:0,class:"mt-3 flex space-x-2"},ha=["onClick"],ga={class:"ml-4 flex-shrink-0 flex"},ma={key:0,class:"h-1 bg-gray-200 dark:bg-gray-700"},va={__name:"NotificationToast",props:{notification:{type:Object,required:!0},visible:{type:Boolean,default:!0},autoDismiss:{type:Boolean,default:!0},autoDismissTime:{type:Number,default:5e3}},emits:["close","action"],setup(n,{emit:o}){const i=n,r=o,a=q(i.autoDismissTime);let c=null,m=null;const h=V(()=>{const C="text-white";switch(i.notification.type){case"success":return`${C} bg-green-500`;case"error":return`${C} bg-red-500`;case"warning":return`${C} bg-yellow-500`;case"info":return`${C} bg-blue-500`;case"link_shared":return`${C} bg-blue-500`;case"user_joined":return`${C} bg-green-500`;default:return`${C} bg-gray-500`}}),k=V(()=>{switch(i.notification.type){case"success":return"fas fa-check";case"error":return"fas fa-exclamation-triangle";case"warning":return"fas fa-exclamation";case"info":return"fas fa-info";case"link_shared":return"fas fa-share-alt";case"user_joined":return"fas fa-user-plus";default:return"fas fa-bell"}}),b=C=>{r("action",C),C.closeOnClick!==!1&&r("close")},g=()=>{if(!i.autoDismiss)return;c=setTimeout(()=>{r("close")},i.autoDismissTime);const C=100;m=setInterval(()=>{a.value-=C,a.value<=0&&clearInterval(m)},C)},y=()=>{c&&(clearTimeout(c),c=null),m&&(clearInterval(m),m=null)};return lr(()=>{i.visible&&g()}),is(()=>{y()}),(C,A)=>(P(),we(ss,{"enter-active-class":"transform ease-out duration-300 transition","enter-from-class":"translate-y-2 opacity-0 sm:translate-y-0 sm:translate-x-2","enter-to-class":"translate-y-0 opacity-100 sm:translate-x-0","leave-active-class":"transition ease-in duration-100","leave-from-class":"opacity-100","leave-to-class":"opacity-0"},{default:An(()=>[n.visible?(P(),j("div",oa,[d("div",aa,[d("div",la,[d("div",ca,[d("div",{class:W(["w-8 h-8 rounded-full flex items-center justify-center",h.value])},[d("i",{class:W([k.value,"text-sm"])},null,2)],2)]),d("div",ua,[d("p",da,$(n.notification.title),1),d("p",fa,$(n.notification.message),1),n.notification.actions?(P(),j("div",pa,[(P(!0),j(me,null,xe(n.notification.actions,U=>(P(),j("button",{key:U.label,onClick:K=>b(U),class:W(["text-sm font-medium rounded-md px-3 py-1.5 transition-colors",U.primary?"bg-primary text-white hover:bg-primary-dark":"bg-gray-100 text-gray-700 hover:bg-gray-200 dark:bg-gray-700 dark:text-gray-300 dark:hover:bg-gray-600"])},$(U.label),11,ha))),128))])):N("",!0)]),d("div",ga,[d("button",{onClick:A[0]||(A[0]=U=>C.$emit("close")),class:"bg-white dark:bg-gray-800 rounded-md inline-flex text-gray-400 hover:text-gray-500 dark:hover:text-gray-300 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary"},A[1]||(A[1]=[d("span",{class:"sr-only"},"Close",-1),d("i",{class:"fas fa-times text-sm"},null,-1)]))])])]),n.autoDismiss&&a.value>0?(P(),j("div",ma,[d("div",{class:"h-full bg-primary transition-all duration-100 ease-linear",style:_n({width:`${a.value/n.autoDismissTime*100}%`})},null,4)])):N("",!0)])):N("",!0)]),_:1}))}},ya=cr(va,[["__scopeId","data-v-da0fc769"]]),ba={class:"fixed inset-0 flex items-end justify-center px-4 py-6 pointer-events-none sm:p-6 sm:items-start sm:justify-end z-50"},xa={class:"w-full flex flex-col items-center space-y-4 sm:items-end"},ka={__name:"NotificationContainer",emits:["action"],setup(n,{emit:o}){const{notifications:i,removeNotification:r}=di(),a=o,c=m=>{a("action",m)};return(m,h)=>(P(),j("div",ba,[d("div",xa,[se(os,{"enter-active-class":"transform ease-out duration-300 transition","enter-from-class":"translate-y-2 opacity-0 sm:translate-y-0 sm:translate-x-2","enter-to-class":"translate-y-0 opacity-100 sm:translate-x-0","leave-active-class":"transition ease-in duration-100","leave-from-class":"opacity-100","leave-to-class":"opacity-0","move-class":"transition-all duration-300"},{default:An(()=>[(P(!0),j(me,null,xe(D(i),k=>(P(),we(ya,{key:k.id,notification:k,visible:!0,onClose:b=>D(r)(k.id),onAction:c},null,8,["notification","onClose"]))),128))]),_:1})])]))}},Sa=cr(ka,[["__scopeId","data-v-da453db0"]]),Ca={class:"relative z-10"},Ra=["aria-label"],Ta={key:0,class:"absolute inset-0 flex items-center justify-center"},Pa={__name:"UserAvatar",props:{size:{type:String,default:"md",validator:n=>["sm","md","lg","xl"].includes(n)},isBroadcasting:{type:Boolean,default:!1},userName:{type:String,default:"User"},color:{type:String,default:"text-white"}},setup(n){const o=n,i={sm:{container:"w-12 h-12",avatar:"w-8 h-8",waves:[{size:"w-16 h-16",opacity:"opacity-30",delay:"0s"},{size:"w-20 h-20",opacity:"opacity-20",delay:"0.5s"},{size:"w-24 h-24",opacity:"opacity-10",delay:"1s"}]},md:{container:"w-16 h-16",avatar:"w-12 h-12",waves:[{size:"w-20 h-20",opacity:"opacity-30",delay:"0s"},{size:"w-24 h-24",opacity:"opacity-20",delay:"0.5s"},{size:"w-28 h-28",opacity:"opacity-10",delay:"1s"}]},lg:{container:"w-24 h-24",avatar:"w-16 h-16",waves:[{size:"w-32 h-32",opacity:"opacity-30",delay:"0s"},{size:"w-40 h-40",opacity:"opacity-20",delay:"0.5s"},{size:"w-48 h-48",opacity:"opacity-10",delay:"1s"}]},xl:{container:"w-32 h-32",avatar:"w-24 h-24",waves:[{size:"w-40 h-40",opacity:"opacity-30",delay:"0s"},{size:"w-48 h-48",opacity:"opacity-20",delay:"0.5s"},{size:"w-56 h-56",opacity:"opacity-10",delay:"1s"}]}},r=V(()=>[i[o.size].container,o.color]),a=V(()=>[i[o.size].avatar]),c=V(()=>o.isBroadcasting?i[o.size].waves.map(h=>({classes:[h.size,h.opacity],style:{animationDelay:h.delay,animationDuration:"2s"}})):[]),m=V(()=>{const h=`${o.userName} avatar`;return o.isBroadcasting?`${h} - broadcasting`:h});return(h,k)=>(P(),j("div",{class:W(["relative flex items-center justify-center",r.value])},[d("div",Ca,[(P(),j("svg",{class:W(a.value),fill:"currentColor",viewBox:"0 0 24 24","aria-label":m.value,role:"img"},k[0]||(k[0]=[d("circle",{cx:"12",cy:"8",r:"3"},null,-1),d("path",{d:"M12 12c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z"},null,-1)]),10,Ra))]),n.isBroadcasting?(P(),j("div",Ta,[(P(!0),j(me,null,xe(c.value,(b,g)=>(P(),j("div",{key:g,class:W(["absolute border-2 border-current rounded-full animate-ping",b.classes]),style:_n(b.style)},null,6))),128))])):N("",!0)],2))}},Ir=cr(Pa,[["__scopeId","data-v-e66a78a1"]]),Ea={key:2,class:"h-screen flex items-center justify-center bg-gray-100 dark:bg-gray-900"},ja={class:"text-center"},Ma={class:"mt-4 text-lg font-medium text-gray-700 dark:text-gray-300"},Aa={key:3,class:"h-screen flex items-center justify-center bg-gray-100 dark:bg-gray-900"},_a={class:"text-center p-6 bg-white dark:bg-gray-800 rounded-lg shadow-xl"},Ia={class:"text-gray-600 dark:text-gray-300 mb-6"},Oa={key:4,class:"h-screen flex flex-col bg-gray-100 dark:bg-gray-900"},La={class:"sticky top-0 z-30 bg-white dark:bg-gray-800 shadow-md px-2 sm:px-4 py-2 flex flex-col sm:flex-row justify-between items-center gap-2 border-b border-gray-200 dark:border-gray-700"},Da={class:"flex items-center"},$a={class:"text-xl font-semibold text-gray-900 dark:text-white"},wa={class:"ml-4 px-2 py-1 bg-gray-100 dark:bg-gray-700 rounded text-sm text-gray-600 dark:text-gray-300"},Ua={key:0,class:"ml-4 flex items-center gap-2"},Fa={key:0,class:"w-full mt-2"},Na={class:"text-xs max-h-24 overflow-y-auto"},Ba={class:"font-bold"},za={class:"font-bold"},Va={class:"flex items-center"},Ga={class:"text-sm text-gray-600 dark:text-gray-300 mr-2"},Ja={class:"flex-1 flex overflow-hidden"},Ha={class:"flex-1 p-4 overflow-auto"},qa={class:"text-center mb-6"},Wa={class:"text-2xl font-bold text-gray-900 dark:text-white"},Ya={class:"flex flex-col lg:flex-row gap-6 lg:gap-8 h-full max-h-[600px]"},Ka={class:"flex-shrink-0 w-full lg:w-2/5"},Xa={class:"relative bg-black rounded-2xl overflow-hidden shadow-xl h-full min-h-[300px] lg:min-h-[400px] border-2 border-gray-300 dark:border-gray-600"},Za={key:1,class:"w-full h-full flex flex-col items-center justify-center bg-black"},Qa={key:2,class:"absolute top-4 right-4 bg-red-600 rounded-full p-2"},el={class:"text-center mt-4"},tl={class:"text-xl font-bold text-gray-900 dark:text-white uppercase tracking-wide"},rl={class:"flex-1"},nl={class:"grid grid-cols-2 sm:grid-cols-3 gap-3 lg:gap-4 h-full auto-rows-fr"},il={class:"relative bg-black rounded-xl overflow-hidden shadow-lg aspect-square border border-gray-300 dark:border-gray-600"},sl={key:1,class:"w-full h-full flex items-center justify-center bg-gray-800"},ol={key:2,class:"absolute top-2 right-2 bg-red-600 rounded-full p-1.5"},al={key:3,class:"absolute top-2 left-2"},ll=["onClick"],cl={class:"text-center mt-2"},ul={class:"text-sm font-semibold text-gray-900 dark:text-white truncate"},dl={class:"relative bg-gray-800 rounded-xl overflow-hidden shadow-lg aspect-square border border-gray-300 dark:border-gray-600 flex items-center justify-center"},fl={key:0,class:"mt-4"},pl={key:0},hl={class:"font-semibold"},gl={class:"text-sm text-red-500"},ml={key:0,class:"w-80 bg-white dark:bg-gray-800 shadow-lg flex flex-col border-l border-gray-200 dark:border-gray-700"},vl={class:"flex-1 p-3 overflow-y-auto"},yl={class:"font-semibold mb-1 text-gray-900 dark:text-white"},bl={class:"text-sm text-gray-600 dark:text-gray-300 mb-2"},xl={class:"flex space-x-2"},kl=["onClick"],Sl=["onClick"],Cl={key:1,class:"w-80 bg-white dark:bg-gray-800 shadow-lg flex flex-col border-l border-gray-200 dark:border-gray-700"},Rl={class:"p-3 border-b border-gray-200 dark:border-gray-700 flex justify-between items-center"},Tl={class:"flex-1"},Pl={key:2,class:"w-80 bg-white dark:bg-gray-800 shadow-lg flex flex-col border-l border-gray-200 dark:border-gray-700"},El={class:"p-3 border-b border-gray-200 dark:border-gray-700 flex justify-between items-center"},jl={class:"flex-1"},Ml={key:3,class:"w-80 bg-white dark:bg-gray-800 shadow-lg flex flex-col border-l border-gray-200 dark:border-gray-700"},Al={class:"flex-1"},_l={key:5,class:"w-80 bg-white dark:bg-gray-800 shadow-lg flex flex-col border-l border-gray-200 dark:border-gray-700"},Il={class:"p-3 border-b border-gray-200 dark:border-gray-700 flex justify-between items-center"},Ol={class:"flex items-start"},Ll={class:"font-semibold mb-1"},Dl={class:"p-3 border-t border-gray-200 dark:border-gray-700"},$l=["disabled"],wl={key:1,class:"border-t border-gray-200 dark:border-gray-700"},Ul={key:2,class:"border-t border-gray-200 dark:border-gray-700 p-2"},Fl={key:3,class:"fixed bottom-24 left-1/2 transform -translate-x-1/2 z-10"},Nl={key:5,class:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50"},Bl={class:"bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6 max-w-md w-full"},zl={class:"flex justify-end space-x-2"},Vl={key:6,class:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50"},Gl={class:"bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6 max-w-md w-full"},Jl={class:"mb-4 text-gray-700 dark:text-gray-300"},Hl={class:"font-semibold"},ql={class:"flex justify-end space-x-2"},Zl={__name:"Meeting",setup(n){const o=cs(),i=ls(),r=ys(),a=ps(),c=as(),{showLinkShared:m,showUserJoinedViaLink:h,showInvitePrompt:k,showLinkSharingPolicyChanged:b}=di(),g=V(()=>i.params.id),y=V(()=>{var R;return(R=c.user)!=null&&R.id?c.user.id:localStorage.getItem("userId")||`guest_${Date.now()}`}),C=V(()=>{var R;return(R=c.user)!=null&&R.name?c.user.name:localStorage.getItem("userName")||"Guest User"}),A=V(()=>a.getMeetingName||`Meeting ${g.value}`),U=V(()=>a.linkSharingPolicy),K=V(()=>ee.value&&r.participants.length<=1);jt(K,R=>{R&&!ae.value&&!Pe.value&&setTimeout(()=>{K.value&&!ae.value&&k(1)},3e3)}),jt(()=>a.linkSharingPolicy,(R,x)=>{x&&R!==x&&b(R,Ot())});const ae=q(!1),Pe=q(!1),st=(R=null)=>{const x=`${window.location.origin}/meeting/${g.value}`,w=new URLSearchParams;return w.set("joinViaLink","true"),R&&w.set("sharedBy",R),w.set("timestamp",Date.now().toString()),`${x}?${w.toString()}`},ot=V(()=>st(y.value)),Y=V(()=>a.getJoinTracking);function Mt(R){a.updateLinkSharingPolicy(R)}const ee=V(()=>a.isHost(y.value)),te=V(()=>a.activeFeatures),_e=V(()=>a.getAllActiveExtensions),Ue=q(null),ur=q({}),z=q(null),Fe=q(!1),oe=q(!1),Ne=q(!1),ve=q(""),Be=q(null),Ee=q([]),ze=q(!1),de=q(null),Ie=q(""),at=q(!1),Oe=q("");let Ve=null,We=null,Ye=null,Ke=null;const dr=()=>{const R=i.query.joinViaLink,x=i.query.sharedBy,w=localStorage.getItem("userName");return R&&!w?(Pe.value=!0,!0):(R&&w&&x&&At(x,w),!1)},At=async(R,x)=>{try{await a.recordJoinViaLink({sharedBy:R,joinedName:x,joinedBy:y.value,timestamp:new Date().toISOString()})}catch(w){console.error("Failed to record join via link:",w)}};lr(async()=>{if(!dr()){if(!y.value){alert("User ID not found. Please ensure you are logged in or have provided a name."),o.push("/");return}localStorage.getItem("userId")||localStorage.setItem("userId",y.value),C.value&&!localStorage.getItem("userName")&&localStorage.setItem("userName",C.value);try{await _t()}catch(R){console.error("Error in onMounted:",R)}}});const lt=()=>{z.value&&z.value.leaveMeeting()};us(()=>{window.removeEventListener("beforeunload",lt),z.value&&z.value.leaveMeeting(),r.localStream&&r.localStream.getTracks().forEach(R=>R.stop()),r.reset(),a.resetMeetingConfig(),We&&We(),Ye&&Ye(),Ve&&Ve(),Ke&&Ke()});const ct=R=>{console.log("Participant joined (WebRTC):",R)},Ge=R=>{console.log("Participant left (WebRTC):",R)},_t=async()=>{try{if(r.setMeetingId(g.value),await a.loadMeetingConfiguration(g.value,y.value),a.error){alert(`Error loading meeting: ${a.error}`),o.push("/");return}z.value=new As(g.value,y.value,(x,w)=>r.addRemoteStream(x,w),x=>r.removeRemoteStream(x),ct,Ge);const R=await z.value.initLocalStream();r.setLocalStream(R),Ue.value&&r.localStream&&(Ue.value.srcObject=r.localStream),await z.value.joinMeeting(),r.setConnectionStatus("connected"),We=hs(g.value,x=>{r.messages=x,pt()}),Ye=gs(g.value,x=>{r.participants=x},x=>{if(ee.value){console.log("User joined via shared link:",x);const w=je(x.participantData.sharedBy)||"Unknown";h(x.participantData.userName||"Unknown User",w)}}),ee.value&&(Je(),Ke=ms(g.value,x=>{a.linkSharingActivities=x})),window.addEventListener("beforeunload",lt)}catch(R){console.error("Error initializing meeting:",R),r.setConnectionStatus("disconnected"),alert(`Failed to join the meeting: ${R.message}. Please try again.`),o.push("/")}},X=async R=>{typeof R=="string"?(localStorage.setItem("userName",R),console.log("Username set:",R)):(localStorage.setItem("userName",R.username),console.log("Username set with tracking:",R),R.joinMethod==="shared_link"&&await a.recordJoinViaLink({sharedBy:R.sharedBy||"unknown",joinedName:R.username,joinedBy:y.value,timestamp:R.timestamp})),Pe.value=!1,await _t()},re=()=>{o.push("/")},It=async R=>{await a.recordLinkSharingActivity({type:"link_shared",sharedBy:y.value,method:R.method,timestamp:R.timestamp}),ee.value&&m(C.value,R.method),console.log("Link shared via:",R.method)},Ot=()=>{const R=a.meetingSettings.hostUserId,x=r.participants.find(w=>w.userId===R||w.id===R);return(x==null?void 0:x.userName)||"Host"},je=R=>{const x=r.participants.find(w=>w.id===R||w.userId===R);return(x==null?void 0:x.userName)||"Unknown User"},Lt=R=>{switch(R.action){case"show_invite":ae.value=!0;break;case"dismiss":break;default:console.log("Unknown notification action:",R)}},Dt=()=>{r.toggleMic()},$t=()=>{r.toggleCamera()},wt=async()=>{try{const R=r.isScreenSharing;await z.value.toggleScreenSharing(!R),r.toggleScreenSharing()}catch(R){console.error("Error toggling screen sharing:",R),r.isScreenSharing&&r.toggleScreenSharing()}},ut=async()=>{if(ve.value.trim())try{await Qt(g.value,y.value,C.value,ve.value),ve.value=""}catch(R){console.error("Error sending message:",R)}},Ut=()=>{navigator.clipboard.writeText(g.value).then(()=>alert("Meeting ID copied to clipboard")).catch(R=>console.error("Failed to copy meeting ID:",R))},Me=R=>{const x=r.participants.find(w=>w.id===R||w.userId===R);return(x==null?void 0:x.audioMuted)||!1},Ft=R=>{const x=r.participants.find(w=>w.id===R||w.userId===R);return(x==null?void 0:x.videoMuted)||!1},Xe=async R=>{if(!(!a.isFeatureActive("virtualBackground")||!r.localStream))try{const x=r.localStream.getVideoTracks()[0];if(!x)return;const w=new Os,De=await w.initialize(x);if(De){w.setBackground(R.url);const pe=new MediaStream([De,...r.localStream.getAudioTracks()]);r.setLocalStream(pe),Ue.value&&(Ue.value.srcObject=pe),z.value&&await z.value.updateVideoTrack(De)}await a.toggleFeature("virtualBackground")}catch(x){console.error("Error applying virtual background:",x),alert("Failed to apply virtual background.")}},dt=R=>{de.value=R,Ie.value="",ze.value=!0},fr=async()=>{if(!(!ee.value||!de.value))try{if(z.value){await Et(ue(Q,"meetings",g.value,"participants",de.value));const R=Ie.value.trim()?` (Reason: ${Ie.value})`:"";await Qt(g.value,"system","System",`${je(de.value)} was removed by the host${R}`),ze.value=!1,de.value=null,Ie.value=""}}catch(R){console.error("Error removing participant:",R),alert("Failed to remove participant.")}},ft=()=>{Oe.value="",at.value=!0},pr=async()=>{try{Oe.value.trim()&&await Qt(g.value,"system","System",`${C.value} left the meeting (Reason: ${Oe.value})`),o.push("/")}catch(R){console.error("Error leaving meeting with reason:",R),o.push("/")}},Ze=R=>R?(R.toDate?R.toDate():new Date(R)).toLocaleTimeString([],{hour:"2-digit",minute:"2-digit"}):"",pt=()=>{ds(()=>{Be.value&&(Be.value.scrollTop=Be.value.scrollHeight)})};jt(()=>r.messages,pt,{deep:!0,immediate:!0});const Je=()=>{const R=nt(Q,"meetings",g.value,"joinRequests");Ve=tr(R,x=>{Ee.value=x.docs.map(w=>({id:w.id,...w.data()})),Ee.value.length>0&&oe.value})},ht=async R=>{try{const x=Ee.value.find(w=>w.id===R);if(!x)return;await yn(ue(Q,"meetings",g.value,"joinRequests",R),{status:"approved",approvedAt:Mr(),approvedBy:y.value}),await Rt(ue(Q,"meetings",g.value,"participants",x.userId),{userId:x.userId,userName:x.userName,joined:Mr(),approved:!0,isAdmin:!1}),await Qt(g.value,"system","System",`${x.userName} joined the meeting.`),await Et(ue(Q,"meetings",g.value,"joinRequests",R))}catch(x){console.error("Error approving join request:",x),alert("Failed to approve join request.")}},Nt=async R=>{try{await yn(ue(Q,"meetings",g.value,"joinRequests",R),{status:"denied",deniedAt:Mr(),deniedBy:y.value}),await Et(ue(Q,"meetings",g.value,"joinRequests",R))}catch(x){console.error("Error denying join request:",x),alert("Failed to deny join request.")}},Bt=()=>{oe.value=!oe.value},zt=V(()=>a.isFeatureActive("recording")),gt=V(()=>a.isFeatureActive("whiteboard")),ke=async()=>{ee.value&&await a.toggleFeature("whiteboard")},Le=R=>(R&&console.warn(`Dynamic component resolution for entry point "${R}" not yet fully implemented. Attempting placeholder lookup.`),console.warn(`Component for entry point "${R}" could not be resolved.`),null);return(R,x)=>{const w=tt("BreakoutRooms"),De=tt("Whiteboard"),pe=tt("ReactionDisplay"),mt=tt("AudibleImpairedSystem"),Vt=tt("RecordingControls"),Qe=tt("ReactionSelector");return P(),j(me,null,[ae.value?(P(),we(Ws,{key:0,visible:ae.value,inviteLink:ot.value,meetingId:g.value,meetingName:A.value,hostName:C.value,linkSharingPolicy:U.value,onClose:x[0]||(x[0]=_=>ae.value=!1),onLinkShared:It},null,8,["visible","inviteLink","meetingId","meetingName","hostName","linkSharingPolicy"])):N("",!0),Pe.value?(P(),we(lo,{key:1,visible:Pe.value,meetingInfo:{name:A.value,hostName:Ot(),participantCount:D(r).participants.length},sharedBy:D(i).query.sharedBy,joinViaLink:!!D(i).query.joinViaLink,onSubmit:X,onCancel:re},null,8,["visible","meetingInfo","sharedBy","joinViaLink"])):N("",!0),D(a).isLoading||D(r).connectionStatus==="connecting"?(P(),j("div",Ea,[d("div",ja,[x[24]||(x[24]=d("svg",{class:"mx-auto h-12 w-12 text-primary animate-spin",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","aria-label":"Loading meeting",role:"img"},[d("circle",{class:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor","stroke-width":"4"}),d("path",{class:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})],-1)),d("p",Ma,$(D(r).connectionStatus==="connecting"?"Connecting to meeting...":"Loading meeting details..."),1)])])):D(a).error?(P(),j("div",Aa,[d("div",_a,[x[25]||(x[25]=d("i",{class:"fas fa-exclamation-triangle text-4xl text-red-500 mb-4"},null,-1)),x[26]||(x[26]=d("h2",{class:"text-2xl font-semibold text-gray-900 dark:text-white mb-2"},"Error Loading Meeting",-1)),d("p",Ia,$(D(a).error),1),d("button",{onClick:x[1]||(x[1]=_=>D(o).push("/")),class:"btn btn-primary"},"Go to Homepage")])])):(P(),j("div",Oa,[d("div",La,[d("div",Da,[d("h1",$a,$(A.value),1),d("div",wa,[H($(g.value)+" ",1),d("button",{onClick:Ut,class:"ml-1 text-primary hover:text-primary-dark",title:"Copy meeting ID"},x[27]||(x[27]=[d("i",{class:"fas fa-copy"},null,-1)]))]),ee.value?(P(),j("div",Ua,[x[29]||(x[29]=d("label",{class:"text-xs text-gray-600 dark:text-gray-300"},"Who can share invite?",-1)),Tt(d("select",{"onUpdate:modelValue":x[2]||(x[2]=_=>U.value=_),onChange:Mt,class:"text-xs p-1 rounded border bg-gray-50 dark:bg-gray-700"},x[28]||(x[28]=[d("option",{value:"host"},"Host Only",-1),d("option",{value:"all"},"All Members",-1)]),544),[[fs,U.value]])])):N("",!0)]),ee.value&&Y.value.length?(P(),j("div",Fa,[x[31]||(x[31]=d("div",{class:"text-xs text-gray-600 dark:text-gray-300 font-semibold mb-1"},"Join Link Activity",-1)),d("ul",Na,[(P(!0),j(me,null,xe(Y.value,(_,fe)=>(P(),j("li",{key:fe,class:"mb-1"},[d("span",Ba,$(je(_.sharedBy)),1),x[30]||(x[30]=H(" shared link → ")),d("span",za,$(_.joinedName),1),H(" joined ("+$(Ze(_.joinedAt))+") ",1)]))),128))])])):N("",!0),d("div",Va,[d("span",Ga,$(D(r).participants.length)+" participant"+$(D(r).participants.length!==1?"s":""),1),d("button",{onClick:ft,class:"btn btn-outline text-red-600 hover:bg-red-50 dark:hover:bg-red-900 dark:text-red-400"}," Leave ")])]),d("div",Ja,[d("div",Ha,[d("div",qa,[d("h1",Wa,$(A.value||"MVP DEVELOPMENT MEETING"),1)]),d("div",Ya,[d("div",Ka,[d("div",Xa,[D(r).isCameraOff?(P(),j("div",Za,[se(Ir,{size:"xl","is-broadcasting":!0,"user-name":C.value||"Host",color:"text-white",class:"mb-6"},null,8,["user-name"])])):(P(),j("video",{key:0,ref_key:"localVideo",ref:Ue,muted:!0,autoplay:"",playsinline:"",class:"w-full h-full object-cover"},null,512)),D(r).isMicMuted?(P(),j("div",Qa,x[32]||(x[32]=[d("i",{class:"fas fa-microphone-slash text-white"},null,-1)]))):N("",!0)]),d("div",el,[d("div",tl,$(C.value||"KATHLEEN JOHNSON"),1),x[33]||(x[33]=d("div",{class:"text-base font-bold text-gray-900 dark:text-white mt-1"}," HOST ",-1))])]),d("div",rl,[d("div",nl,[(P(!0),j(me,null,xe(D(r).remoteStreams,(_,fe)=>(P(),j("div",{key:fe,class:"flex flex-col"},[d("div",il,[Ft(fe)?(P(),j("div",sl,[se(Ir,{size:"lg","is-broadcasting":!1,"user-name":je(fe),color:"text-gray-400"},null,8,["user-name"])])):(P(),j("video",{key:0,ref_for:!0,ref:he=>{he&&(ur.value[fe]=he),he&&_&&(he.srcObject=_)},autoplay:"",playsinline:"",class:"w-full h-full object-cover"},null,512)),Me(fe)?(P(),j("div",ol,x[34]||(x[34]=[d("i",{class:"fas fa-microphone-slash text-white text-xs"},null,-1)]))):N("",!0),ee.value?(P(),j("div",al,[d("button",{onClick:he=>dt(fe),class:"bg-red-600 text-white p-1.5 rounded-full hover:bg-red-700 text-xs",title:"Remove participant"},x[35]||(x[35]=[d("i",{class:"fas fa-user-times"},null,-1)]),8,ll)])):N("",!0)]),d("div",cl,[d("div",ul,$(je(fe)),1)])]))),128)),(P(!0),j(me,null,xe(Math.max(0,9-Object.keys(D(r).remoteStreams).length),_=>(P(),j("div",{key:`empty-${_}`,class:"flex flex-col"},[d("div",dl,[se(Ir,{size:"md","is-broadcasting":!1,"user-name":"Empty Slot",color:"text-gray-500"})]),x[36]||(x[36]=d("div",{class:"text-center mt-2"},[d("div",{class:"text-sm font-semibold text-gray-900 dark:text-white"}," <name> ")],-1))]))),128))])])]),_e.value.length>0?(P(),j("div",fl,[x[37]||(x[37]=d("h3",{class:"text-lg font-semibold text-gray-800 dark:text-white mb-2"},"Active Extensions",-1)),(P(!0),j(me,null,xe(_e.value,_=>(P(),j("div",{key:_.id,class:"p-2 border rounded mb-2 bg-gray-50 dark:bg-gray-800"},[(P(),we(Mn(Le(_.entryPoint||_.id)),{config:_.config,meetingId:g.value,userId:y.value,isAdmin:ee.value,meetingContext:{meetingId:g.value,userId:y.value,userName:C.value.value,meetingConfigStore:D(a),meetingStore:D(r)}},null,8,["config","meetingId","userId","isAdmin","meetingContext"])),Le(_.entryPoint||_.id)?N("",!0):(P(),j("div",pl,[d("p",hl,$(_.name),1),d("p",gl,"Cannot load UI for this extension (entry point: "+$(_.entryPoint||"not defined")+").",1)]))]))),128))])):N("",!0)]),ee.value&&oe.value&&Ee.value.length>0?(P(),j("div",ml,[d("div",{class:"p-3 border-b border-gray-200 dark:border-gray-700 flex justify-between items-center"},[x[39]||(x[39]=d("h2",{class:"font-semibold text-gray-900 dark:text-white"},"Join Requests",-1)),d("button",{onClick:Bt,class:"text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"},x[38]||(x[38]=[d("i",{class:"fas fa-times"},null,-1)]))]),d("div",vl,[(P(!0),j(me,null,xe(Ee.value,_=>(P(),j("div",{key:_.id,class:"mb-4 p-3 bg-gray-100 dark:bg-gray-700 rounded-lg"},[d("div",yl,$(_.userName),1),d("div",bl,"Requested at "+$(Ze(_.timestamp)),1),d("div",xl,[d("button",{onClick:fe=>ht(_.id),class:"btn btn-primary text-sm py-1 px-3"},"Approve",8,kl),d("button",{onClick:fe=>Nt(_.id),class:"btn btn-outline text-sm py-1 px-3"},"Deny",8,Sl)])]))),128))])])):N("",!0),te.value.breakoutRooms?(P(),j("div",Cl,[d("div",Rl,[x[41]||(x[41]=d("h2",{class:"font-semibold text-gray-900 dark:text-white"},"Breakout Rooms",-1)),d("button",{onClick:x[3]||(x[3]=_=>D(a).toggleFeature("breakoutRooms")),class:"text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"},x[40]||(x[40]=[d("i",{class:"fas fa-times"},null,-1)]))]),d("div",Tl,[se(w,{meetingId:g.value,userId:y.value,isHost:ee.value,participants:D(r).participants},null,8,["meetingId","userId","isHost","participants"])])])):N("",!0),te.value.virtualBackground?(P(),j("div",Pl,[d("div",El,[x[43]||(x[43]=d("h2",{class:"font-semibold text-gray-900 dark:text-white"},"Virtual Background",-1)),d("button",{onClick:x[4]||(x[4]=_=>D(a).toggleFeature("virtualBackground")),class:"text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"},x[42]||(x[42]=[d("i",{class:"fas fa-times"},null,-1)]))]),d("div",jl,[se(Do,{stream:D(r).localStream,currentBackground:"none",onApply:Xe,onCancel:x[5]||(x[5]=_=>D(a).toggleFeature("virtualBackground"))},null,8,["stream"])])])):N("",!0),te.value.whiteboard?(P(),j("div",Ml,[d("div",{class:"p-3 border-b border-gray-200 dark:border-gray-700 flex justify-between items-center"},[x[45]||(x[45]=d("h2",{class:"font-semibold text-gray-900 dark:text-white"},"Whiteboard",-1)),d("button",{onClick:ke,class:"text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"},x[44]||(x[44]=[d("i",{class:"fas fa-times"},null,-1)]))]),d("div",Al,[se(De,{meetingId:g.value,userId:y.value,isHost:ee.value},null,8,["meetingId","userId","isHost"])])])):N("",!0),Ne.value?(P(),we(ia,{key:4,activities:D(a).getLinkSharingActivities,linkSharingPolicy:U.value,participants:D(r).participants,onClose:x[6]||(x[6]=_=>Ne.value=!1),onUpdatePolicy:Mt,onShowInvite:x[7]||(x[7]=_=>ae.value=!0)},null,8,["activities","linkSharingPolicy","participants"])):N("",!0),Fe.value&&te.value.chat?(P(),j("div",_l,[d("div",Il,[x[47]||(x[47]=d("h2",{class:"font-semibold text-gray-900 dark:text-white"},"Chat",-1)),d("button",{onClick:x[8]||(x[8]=_=>Fe.value=!1),class:"text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"},x[46]||(x[46]=[d("i",{class:"fas fa-times"},null,-1)]))]),d("div",{class:"flex-1 p-3 overflow-y-auto",ref_key:"chatMessagesContainer",ref:Be},[(P(!0),j(me,null,xe(D(r).messages,_=>(P(),j("div",{key:_.id,class:"mb-3"},[d("div",Ol,[d("div",{class:W(["flex-1 bg-gray-100 dark:bg-gray-700 rounded-lg px-3 py-2 text-sm",{"bg-primary-light text-white":_.userId===y.value,"ml-auto":_.userId===y.value}])},[d("div",Ll,$(_.userId===y.value?"You":_.userName),1),d("div",null,$(_.message),1)],2)]),d("div",{class:W(["text-xs text-gray-500 dark:text-gray-400 mt-1",{"text-right":_.userId===y.value}])},$(Ze(_.timestamp)),3)]))),128))],512),d("div",Dl,[d("form",{onSubmit:jn(ut,["prevent"]),class:"flex"},[Tt(d("input",{"onUpdate:modelValue":x[9]||(x[9]=_=>ve.value=_),type:"text",placeholder:"Type a message...",class:"input flex-1 mr-2"},null,512),[[rr,ve.value]]),d("button",{type:"submit",class:"btn btn-primary",disabled:!ve.value.trim()},"Send",8,$l)],32)])])):N("",!0)]),te.value.reactions?(P(),we(pe,{key:0,meetingId:g.value},null,8,["meetingId"])):N("",!0),te.value.audibleImpairedSystem?(P(),j("div",wl,[se(mt,{meetingId:g.value,currentUserId:y.value,currentUserName:C.value.value,localStream:D(r).localStream},null,8,["meetingId","currentUserId","currentUserName","localStream"])])):N("",!0),te.value.recording?(P(),j("div",Ul,[se(Vt,{meetingId:g.value,userId:y.value,stream:D(r).localStream,onRecordingStarted:x[10]||(x[10]=_=>D(r).isRecording=!0),onRecordingStopped:x[11]||(x[11]=_=>D(r).isRecording=!1)},null,8,["meetingId","userId","stream"])])):N("",!0),te.value.reactions?(P(),j("div",Fl,[se(Qe,{meetingId:g.value,userId:y.value,userName:C.value.value},null,8,["meetingId","userId","userName"])])):N("",!0),se(Ro,{isMicMuted:D(r).isMicMuted,isCameraOff:D(r).isCameraOff,isScreenSharing:D(r).isScreenSharing,isChatOpen:Fe.value,isChatEnabled:te.value.chat,isAISEnabled:te.value.audibleImpairedSystem,isReactionsEnabled:te.value.reactions,isBreakoutRoomsEnabled:te.value.breakoutRooms,isVirtualBackgroundEnabled:te.value.virtualBackground,isWhiteboardEnabled:gt.value,isRecordingEnabled:zt.value,isRecordingActive:D(r).isRecording,hasJoinRequests:ee.value&&Ee.value.length>0,showJoinRequestsPanel:oe.value,joinRequestsCount:Ee.value.length,isAdmin:ee.value,canShareLink:D(a).canUserShareLink(y.value),linkSharingPolicy:U.value,onToggleMic:Dt,onToggleCamera:$t,onToggleScreen:wt,onToggleChat:x[12]||(x[12]=_=>Fe.value=!Fe.value),onToggleAis:x[13]||(x[13]=_=>D(a).toggleFeature("audibleImpairedSystem")),onToggleReactions:x[14]||(x[14]=_=>D(a).toggleFeature("reactions")),onToggleBreakoutRooms:x[15]||(x[15]=_=>D(a).toggleFeature("breakoutRooms")),onToggleVirtualBackground:x[16]||(x[16]=_=>D(a).toggleFeature("virtualBackground")),onToggleWhiteboard:ke,onToggleRecording:x[17]||(x[17]=_=>D(a).toggleFeature("recording")),onToggleJoinRequests:Bt,onShowInvite:x[18]||(x[18]=_=>ae.value=!0),onShowLinkSharing:x[19]||(x[19]=_=>Ne.value=!0),onLeaveMeeting:ft},null,8,["isMicMuted","isCameraOff","isScreenSharing","isChatOpen","isChatEnabled","isAISEnabled","isReactionsEnabled","isBreakoutRoomsEnabled","isVirtualBackgroundEnabled","isWhiteboardEnabled","isRecordingEnabled","isRecordingActive","hasJoinRequests","showJoinRequestsPanel","joinRequestsCount","isAdmin","canShareLink","linkSharingPolicy"])])),at.value?(P(),j("div",Nl,[d("div",Bl,[x[48]||(x[48]=d("h2",{class:"text-xl font-semibold mb-4 text-gray-900 dark:text-white"},"Leave Meeting",-1)),x[49]||(x[49]=d("p",{class:"mb-4 text-gray-700 dark:text-gray-300"},"Please provide a reason for leaving the meeting:",-1)),Tt(d("textarea",{"onUpdate:modelValue":x[20]||(x[20]=_=>Oe.value=_),class:"w-full p-2 border border-gray-300 dark:border-gray-600 rounded mb-4 bg-white dark:bg-gray-700 text-gray-900 dark:text-white",placeholder:"Reason for leaving (optional)",rows:"3"},null,512),[[rr,Oe.value]]),d("div",zl,[d("button",{onClick:x[21]||(x[21]=_=>at.value=!1),class:"px-4 py-2 bg-gray-200 dark:bg-gray-700 text-gray-800 dark:text-gray-200 rounded hover:bg-gray-300 dark:hover:bg-gray-600"},"Cancel"),d("button",{onClick:pr,class:"px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700"},"Leave Meeting")])])])):N("",!0),ze.value?(P(),j("div",Vl,[d("div",Gl,[x[52]||(x[52]=d("h2",{class:"text-xl font-semibold mb-4 text-gray-900 dark:text-white"},"Remove Participant",-1)),d("p",Jl,[x[50]||(x[50]=H(" You are about to remove ")),d("span",Hl,$(de.value?je(de.value):""),1),x[51]||(x[51]=H(" from the meeting. "))]),x[53]||(x[53]=d("p",{class:"mb-4 text-gray-700 dark:text-gray-300"},"Please provide a reason (visible only to the host):",-1)),Tt(d("textarea",{"onUpdate:modelValue":x[22]||(x[22]=_=>Ie.value=_),class:"w-full p-2 border border-gray-300 dark:border-gray-600 rounded mb-4 bg-white dark:bg-gray-700 text-gray-900 dark:text-white",placeholder:"Reason for removal (optional)",rows:"3"},null,512),[[rr,Ie.value]]),d("div",ql,[d("button",{onClick:x[23]||(x[23]=_=>ze.value=!1),class:"px-4 py-2 bg-gray-200 dark:bg-gray-700 text-gray-800 dark:text-gray-200 rounded hover:bg-gray-300 dark:hover:bg-gray-600"},"Cancel"),d("button",{onClick:fr,class:"px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700"},"Remove")])])])):N("",!0),se(Sa,{onAction:Lt})],64)}}};export{Zl as default};
