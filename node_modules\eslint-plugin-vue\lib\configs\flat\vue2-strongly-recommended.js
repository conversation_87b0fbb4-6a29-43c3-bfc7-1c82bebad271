/*
 * IMPORTANT!
 * This file has been automatically generated,
 * in order to update its content execute "npm run update"
 */
'use strict'
const config = require('./vue2-essential.js')

module.exports = [
  ...config,
  {
    name: 'vue/vue2-strongly-recommended/rules',
    rules: {
      'vue/attribute-hyphenation': 'warn',
      'vue/component-definition-name-casing': 'warn',
      'vue/first-attribute-linebreak': 'warn',
      'vue/html-closing-bracket-newline': 'warn',
      'vue/html-closing-bracket-spacing': 'warn',
      'vue/html-end-tags': 'warn',
      'vue/html-indent': 'warn',
      'vue/html-quotes': 'warn',
      'vue/html-self-closing': 'warn',
      'vue/max-attributes-per-line': 'warn',
      'vue/multiline-html-element-content-newline': 'warn',
      'vue/mustache-interpolation-spacing': 'warn',
      'vue/no-multi-spaces': 'warn',
      'vue/no-spaces-around-equal-signs-in-attribute': 'warn',
      'vue/no-template-shadow': 'warn',
      'vue/one-component-per-file': 'warn',
      'vue/prop-name-casing': 'warn',
      'vue/require-default-prop': 'warn',
      'vue/require-prop-types': 'warn',
      'vue/singleline-html-element-content-newline': 'warn',
      'vue/v-bind-style': 'warn',
      'vue/v-on-style': 'warn',
      'vue/v-slot-style': 'warn'
    }
  }
]
