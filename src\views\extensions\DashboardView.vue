<template>
  <div class="dashboard-container">
    <div class="dashboard-header">
      <h1>Developer Dashboard</h1>
      <router-link :to="{ name: 'ExtensionCreate' }" class="create-new-button">
        <i class="fas fa-plus-circle"></i> Create New Extension
      </router-link>
    </div>
    <p v-if="authStore.user">Welcome, {{ authStore.user.name }}! Manage your extensions here.</p>
    <p v-else>Loading user information...</p>

    <div v-if="extensionsStore.isLoading" class="loading-message">Loading your extensions...</div>
    <div v-if="extensionsStore.error" class="error-message">Error: {{ extensionsStore.error }}</div>

    <div v-if="userExtensions.length === 0 && !extensionsStore.isLoading" class="no-extensions-message">
      You haven't created any extensions yet.
      <router-link :to="{ name: 'ExtensionCreate' }">Get started by creating one!</router-link>
    </div>

    <div v-if="userExtensions.length > 0" class="extensions-list">
      <div v-for="ext in userExtensions" :key="ext.id" class="extension-item">
        <div class="item-header">
          <h3>{{ ext.name }} <span class="version-tag">v{{ ext.version }}</span></h3>
          <span :class="['status-tag', ext.is_public ? 'public' : 'private']">
            {{ ext.is_public ? 'Public' : 'Private' }}
          </span>
        </div>
        <p class="description-snippet">{{ ext.description.substring(0, 100) }}{{ ext.description.length > 100 ? '...' : '' }}</p>
        <div class="item-details">
          <p><i class="fas fa-download"></i> {{ ext.downloads }} downloads</p>
          <p><i class="fas fa-clock"></i> Last updated: {{ formatDate(ext.dev_metadata.updated_at) }}</p>
        </div>
        <div class="item-actions">
          <router-link :to="{ name: 'ExtensionEditor', params: { id: ext.id } }" class="action-btn edit-btn">
            <i class="fas fa-edit"></i> Edit
          </router-link>
          <router-link :to="{ name: 'Analytics', params: { id: ext.id } }" class="action-btn analytics-btn">
            <i class="fas fa-chart-line"></i> View Analytics
          </router-link>
           <router-link :to="{ name: 'ExtensionDetail', params: { id: ext.id } }" class="action-btn view-btn">
            <i class="fas fa-eye"></i> View Public Page
          </router-link>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue';
import { useAuthStore } from '../../store/authStore';
import { useExtensionsStore } from '../../store/extensionsStore';
import { useRouter } from 'vue-router';

const authStore = useAuthStore();
const extensionsStore = useExtensionsStore();
const router = useRouter();

const userExtensions = computed(() => {
  if (authStore.user && authStore.user.id) {
    return extensionsStore.getUserExtensions(authStore.user.id);
  }
  return [];
});

onMounted(async () => {
  // Ensure extensions are loaded if not already
  if (extensionsStore.extensions.length === 0) {
    await extensionsStore.fetchExtensions();
  }
  // The computed property will update once authStore.user is available
});

function formatDate(dateString) {
  if (!dateString) return 'N/A';
  const options = { year: 'numeric', month: 'long', day: 'numeric', hour: '2-digit', minute: '2-digit' };
  return new Date(dateString).toLocaleDateString(undefined, options);
}

</script>

<style scoped>
.dashboard-container {
  padding: 20px;
  max-width: 1000px;
  margin: 0 auto;
}

.dashboard-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.dashboard-header h1 {
  font-size: 2.2em;
  color: #333;
  margin: 0;
}

.create-new-button {
  background-color: #007bff;
  color: white;
  padding: 10px 15px;
  border-radius: 5px;
  text-decoration: none;
  font-size: 1em;
  display: inline-flex;
  align-items: center;
  gap: 8px;
  transition: background-color 0.2s;
}
.create-new-button:hover {
  background-color: #0056b3;
}
.create-new-button .fa-plus-circle {
  font-size: 1.1em;
}


.dashboard-container > p {
  margin-bottom: 20px;
  font-size: 1.1em;
  color: #555;
}

.loading-message, .error-message, .no-extensions-message {
  text-align: center;
  font-size: 1.1em;
  padding: 20px;
  color: #777;
  background-color: #f9f9f9;
  border-radius: 5px;
}
.error-message {
  color: #dc3545;
  background-color: #f8d7da;
}
.no-extensions-message a {
  color: #007bff;
  font-weight: bold;
}

.extensions-list {
  display: grid;
  gap: 20px;
}

.extension-item {
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  padding: 15px 20px;
  background-color: #fff;
  box-shadow: 0 1px 3px rgba(0,0,0,0.05);
}

.item-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}
.item-header h3 {
  font-size: 1.5em;
  color: #2c3e50;
  margin: 0;
}
.version-tag {
  font-size: 0.75em;
  background-color: #f0f0f0;
  color: #555;
  padding: 3px 6px;
  border-radius: 4px;
  vertical-align: middle;
  margin-left: 8px;
}
.status-tag {
  font-size: 0.8em;
  padding: 3px 8px;
  border-radius: 10px;
  font-weight: 500;
}
.status-tag.public {
  background-color: #d4edda;
  color: #155724;
}
.status-tag.private {
  background-color: #e2e3e5;
  color: #495057;
}


.description-snippet {
  font-size: 0.95em;
  color: #666;
  margin-bottom: 10px;
  line-height: 1.5;
}

.item-details {
  font-size: 0.9em;
  color: #777;
  display: flex;
  gap: 15px;
  margin-bottom: 15px;
}
.item-details p {
  margin: 0;
  display: inline-flex;
  align-items: center;
  gap: 5px;
}

.item-actions {
  display: flex;
  gap: 10px;
  border-top: 1px solid #f0f0f0;
  padding-top: 15px;
}
.action-btn {
  padding: 8px 12px;
  text-decoration: none;
  border-radius: 5px;
  font-size: 0.9em;
  display: inline-flex;
  align-items: center;
  gap: 6px;
  transition: opacity 0.2s;
}
.action-btn:hover {
  opacity: 0.8;
}
.edit-btn {
  background-color: #ffc107;
  color: #333;
}
.analytics-btn {
  background-color: #17a2b8;
  color: white;
}
.view-btn {
  background-color: #6c757d;
  color: white;
}
</style>
