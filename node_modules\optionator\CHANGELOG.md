# 0.9.0
- update dependencies, in particular `levn` and `type-check` - this could affect behaviour of argument parsing

# 0.8.3
- changes dependency from `wordwrap` to `word-wrap` due to license issue
- update dependencies

# 0.8.2
- fix bug #18 - detect missing value when flag is last item
- update dependencies

# 0.8.1
- update `fast-levenshtein` dependency

# 0.8.0
- update `levn` dependency - supplying a float value to an option with type `Int` now throws an error, instead of silently converting to an `Int`

# 0.7.1
- fix bug with use of `defaults` and `concatRepeatedArrays` or `mergeRepeatedObjects`

# 0.7.0
- added `concatrepeatedarrays` option: `oneValuePerFlag`, only allows one array value per flag
- added `typeAliases` option
- added `parseArgv` which takes an array and parses with the first two items sliced off
- changed enum help style
- bug fixes (#12)
- use of `concatRepeatedArrays` and `mergeRepeatedObjects` at the top level is deprecated, use it as either a per-option option, or set them in the `defaults` object to set them for all objects

# 0.6.0
- added `defaults` lib-option flag, allowing one to set default properties for all options
- added `concatRepeatedArrays` and `mergeRepeatedObjects` as option level properties, allowing you to turn this feature on for specific options only

# 0.5.0
- `Boolean` flags with `default: 'true'`, and no short aliases, will by default show the `--no` version in help

# 0.4.0
- add `mergeRepeatedObjects` setting

# 0.3.0
- add `concatRepeatedArrays` setting
- add `overrideRequired` option setting
- use just Levenshtein string compare algo rather than Levenshtein Damerau to due dependency license issue

# 0.2.2
- bug fixes

# 0.2.1
- improved interpolation
- added changelog

# 0.2.0
- add dependency checks to options - added `dependsOn` as an option property
- add interpolation for `prepend` and `append` text with new `generateHelp` option, `interpolate`

# 0.1.1
- update dependencies

# 0.1.0
- initial release
