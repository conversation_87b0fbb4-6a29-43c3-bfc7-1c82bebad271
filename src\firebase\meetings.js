import { db } from './config'
import { collection, doc, setDoc, getDoc, addDoc, updateDoc, serverTimestamp, query, orderBy, onSnapshot } from 'firebase/firestore'
import { nanoid } from 'nanoid'

/**
 * Creates a new meeting document in Firebase with initial settings, features, and extensions.
 * Also adds the creator as the first participant.
 *
 * @async
 * @function createMeeting
 * @param {string} userId - The ID of the user creating the meeting.
 * @param {string} [meetingName=''] - Optional name for the meeting.
 * @param {boolean} [requireApproval=false] - Whether participants need host approval to join.
 * @param {Object} [initialFeatures={}] - Initial state of core features (e.g., `{ whiteboard: true }`).
 * @param {Array<Object>} [initialExtensions=[]] - Array of selected extensions with their initial config
 *                                                (e.g., `[{ id: 'poll-ext', name: 'Poll', config: {} }]`).
 * @returns {Promise<string>} The ID of the newly created meeting.
 * @throws {Error} If there's an issue creating the meeting in Firebase.
 */
export const createMeeting = async (
  userId,
  meetingName = '',
  requireApproval = false,
  initialFeatures = {}, // New parameter
  initialExtensions = [] // New parameter
) => {
  try {
    // Generate a unique meeting ID
    const meetingId = nanoid(10)

    console.log(`Creating meeting with requireApproval: ${requireApproval}`)
    console.log('Initial features:', initialFeatures)
    console.log('Initial extensions:', initialExtensions)

    // Create the meeting document
    await setDoc(doc(db, 'meetings', meetingId), {
      createdBy: userId,
      createdAt: serverTimestamp(),
      meetingName: meetingName || `Meeting ${meetingId}`,
      active: true,
      requiresApproval: requireApproval, // Firebase field name should be consistent
      features: initialFeatures, // Store selected core features
      // Store a simplified version of extensions, just ID, name, and any instance-specific config
      extensions: initialExtensions.map(ext => ({
        id: ext.id,
        name: ext.name, // Good to have for quick display if manifest isn't loaded yet
        config: ext.config || {}
      })),
    })

    // Add the creator as a participant
    await setDoc(doc(db, 'meetings', meetingId, 'participants', userId), {
      userId,
      userName: localStorage.getItem('userName') || 'Host',
      joined: serverTimestamp(),
      isAdmin: true // Creator is admin
    })

    return meetingId
  } catch (error) {
    console.error('Error creating meeting:', error)
    throw error
  }
}

// Check if a meeting exists
export const checkMeeting = async (meetingId) => {
  try {
    const meetingDoc = await getDoc(doc(db, 'meetings', meetingId))
    return meetingDoc.exists() && meetingDoc.data().active
  } catch (error) {
    console.error('Error checking meeting:', error)
    return false
  }
}

// Send a chat message
export const sendMessage = async (meetingId, userId, userName, message) => {
  try {
    await addDoc(collection(db, 'meetings', meetingId, 'messages'), {
      userId,
      userName,
      message,
      timestamp: serverTimestamp()
    })
  } catch (error) {
    console.error('Error sending message:', error)
    throw error
  }
}

// Listen for chat messages
export const listenForMessages = (meetingId, callback) => {
  const messagesQuery = query(
    collection(db, 'meetings', meetingId, 'messages'),
    orderBy('timestamp', 'asc')
  )

  return onSnapshot(messagesQuery, (snapshot) => {
    const messages = snapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data()
    }))

    callback(messages)
  })
}

// Listen for participants
export const listenForParticipants = (meetingId, callback, onJoinViaLink = null) => {
  const participantsRef = collection(db, 'meetings', meetingId, 'participants')

  return onSnapshot(participantsRef, (snapshot) => {
    const participants = snapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data()
    }))

    // Track new participants joining via shared links
    if (onJoinViaLink) {
      snapshot.docChanges().forEach((change) => {
        if (change.type === 'added') {
          const participantData = change.doc.data();
          if (participantData.joinMethod === 'shared_link' && participantData.sharedBy) {
            onJoinViaLink({
              participantId: change.doc.id,
              participantData,
              timestamp: participantData.joinedAt || new Date()
            });
          }
        }
      });
    }

    callback(participants)
  })
}

// End a meeting
export const endMeeting = async (meetingId) => {
  try {
    await setDoc(doc(db, 'meetings', meetingId), {
      active: false,
      endedAt: serverTimestamp()
    }, { merge: true })
  } catch (error) {
    console.error('Error ending meeting:', error)
    throw error
  }
}

// Add a participant to a meeting
export const addParticipant = async (meetingId, participantData) => {
  const participantRef = doc(db, 'meetings', meetingId, 'participants', participantData.userId)

  await setDoc(participantRef, {
    ...participantData,
    joinedAt: serverTimestamp()
  })
}

// Record link sharing activity
export const recordLinkSharingActivity = async (meetingId, activityData) => {
  const meetingRef = doc(db, 'meetings', meetingId)

  try {
    // Get current activities
    const meetingDoc = await getDoc(meetingRef)
    const currentActivities = meetingDoc.data()?.linkSharingActivities || []

    // Add new activity
    const newActivity = {
      ...activityData,
      id: `${activityData.type}_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`,
      timestamp: activityData.timestamp || serverTimestamp()
    }

    const updatedActivities = [...currentActivities, newActivity]

    // Update meeting document
    await updateDoc(meetingRef, {
      linkSharingActivities: updatedActivities
    })

    return newActivity
  } catch (error) {
    console.error('Error recording link sharing activity:', error)
    throw error
  }
}

// Listen for link sharing activities
export const listenForLinkSharingActivities = (meetingId, callback) => {
  const meetingRef = doc(db, 'meetings', meetingId)

  return onSnapshot(meetingRef, (snapshot) => {
    const data = snapshot.data()
    const activities = data?.linkSharingActivities || []
    callback(activities)
  })
}
