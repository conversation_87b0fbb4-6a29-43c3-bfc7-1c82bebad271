{"version": 3, "file": "index.sw.esm2017.js", "sources": ["../../src/util/constants.ts", "../../src/interfaces/internal-message-payload.ts", "../../src/helpers/array-base64-translator.ts", "../../src/helpers/migrate-old-database.ts", "../../src/internals/idb-manager.ts", "../../src/util/errors.ts", "../../src/internals/requests.ts", "../../src/internals/token-manager.ts", "../../src/helpers/externalizePayload.ts", "../../src/helpers/is-console-message.ts", "../../src/helpers/sleep.ts", "../../src/helpers/logToFirelog.ts", "../../src/listeners/sw-listeners.ts", "../../src/helpers/extract-app-config.ts", "../../src/messaging-service.ts", "../../src/helpers/register.ts", "../../src/api/isSupported.ts", "../../src/api/onBackgroundMessage.ts", "../../src/api/setDeliveryMetricsExportedToBigQueryEnabled.ts", "../../src/api.ts", "../../src/index.sw.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nexport const DEFAULT_SW_PATH = '/firebase-messaging-sw.js';\nexport const DEFAULT_SW_SCOPE = '/firebase-cloud-messaging-push-scope';\n\nexport const DEFAULT_VAPID_KEY =\n  'BDOU99-h67HcA6JeFXHbSNMu7e2yNNu3RzoMj8TM4W88jITfq7ZmPvIM1Iv-4_l2LxQcYwhqby2xGpWwzjfAnG4';\n\nexport const ENDPOINT = 'https://fcmregistrations.googleapis.com/v1';\n\n/** Key of FCM Payload in Notification's data field. */\nexport const FCM_MSG = 'FCM_MSG';\n\nexport const CONSOLE_CAMPAIGN_ID = 'google.c.a.c_id';\nexport const CONSOLE_CAMPAIGN_NAME = 'google.c.a.c_l';\nexport const CONSOLE_CAMPAIGN_TIME = 'google.c.a.ts';\n/** Set to '1' if Analytics is enabled for the campaign */\nexport const CONSOLE_CAMPAIGN_ANALYTICS_ENABLED = 'google.c.a.e';\nexport const TAG = 'FirebaseMessaging: ';\nexport const MAX_NUMBER_OF_EVENTS_PER_LOG_REQUEST = 1000;\nexport const MAX_RETRIES = 3;\nexport const LOG_INTERVAL_IN_MS = 86400000; //24 hour\nexport const DEFAULT_BACKOFF_TIME_MS = 5000;\n\n// FCM log source name registered at Firelog: 'FCM_CLIENT_EVENT_LOGGING'. It uniquely identifies\n// FCM's logging configuration.\nexport const FCM_LOG_SOURCE = 1249;\n\n// Defined as in proto/messaging_event.proto. Neglecting fields that are supported.\nexport const SDK_PLATFORM_WEB = 3;\nexport const EVENT_MESSAGE_DELIVERED = 1;\n\nexport enum MessageType {\n  DATA_MESSAGE = 1,\n  DISPLAY_NOTIFICATION = 3\n}\n", "/**\n * @license\n * Copyright 2018 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\"); you may not use this file except\n * in compliance with the License. You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under the License\n * is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express\n * or implied. See the License for the specific language governing permissions and limitations under\n * the License.\n */\n\nimport {\n  CONSOLE_CAMPAIGN_ANALYTICS_ENABLED,\n  CONSOLE_CAMPAIGN_ID,\n  CONSOLE_CAMPAIGN_NAME,\n  CONSOLE_CAMPAIGN_TIME\n} from '../util/constants';\n\nexport interface MessagePayloadInternal {\n  notification?: NotificationPayloadInternal;\n  data?: unknown;\n  fcmOptions?: FcmOptionsInternal;\n  messageType?: MessageType;\n  isFirebaseMessaging?: boolean;\n  from: string;\n  fcmMessageId: string;\n  productId: number;\n  // eslint-disable-next-line camelcase\n  collapse_key: string;\n}\n\nexport interface NotificationPayloadInternal extends NotificationOptions {\n  title: string;\n  // Supported in the Legacy Send API.\n  // See:https://firebase.google.com/docs/cloud-messaging/xmpp-server-ref.\n  // eslint-disable-next-line camelcase\n  click_action?: string;\n  icon?: string;\n}\n\n// Defined in\n// https://firebase.google.com/docs/reference/fcm/rest/v1/projects.messages#webpushfcmoptions. Note\n// that the keys are sent to the clients in snake cases which we need to convert to camel so it can\n// be exposed as a type to match the Firebase API convention.\nexport interface FcmOptionsInternal {\n  link?: string;\n\n  // eslint-disable-next-line camelcase\n  analytics_label?: string;\n}\n\nexport enum MessageType {\n  PUSH_RECEIVED = 'push-received',\n  NOTIFICATION_CLICKED = 'notification-clicked'\n}\n\n/** Additional data of a message sent from the FN Console. */\nexport interface ConsoleMessageData {\n  [CONSOLE_CAMPAIGN_ID]: string;\n  [CONSOLE_CAMPAIGN_TIME]: string;\n  [CONSOLE_CAMPAIGN_NAME]?: string;\n  [CONSOLE_CAMPAIGN_ANALYTICS_ENABLED]?: '1';\n}\n", "/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nexport function arrayToBase64(array: Uint8Array | ArrayBuffer): string {\n  const uint8Array = new Uint8Array(array);\n  const base64String = btoa(String.fromCharCode(...uint8Array));\n  return base64String.replace(/=/g, '').replace(/\\+/g, '-').replace(/\\//g, '_');\n}\n\nexport function base64ToArray(base64String: string): Uint8Array {\n  const padding = '='.repeat((4 - (base64String.length % 4)) % 4);\n  const base64 = (base64String + padding)\n    .replace(/\\-/g, '+')\n    .replace(/_/g, '/');\n\n  const rawData = atob(base64);\n  const outputArray = new Uint8Array(rawData.length);\n\n  for (let i = 0; i < rawData.length; ++i) {\n    outputArray[i] = rawData.charCodeAt(i);\n  }\n  return outputArray;\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { deleteDB, openDB } from 'idb';\n\nimport { TokenDetails } from '../interfaces/token-details';\nimport { arrayToBase64 } from './array-base64-translator';\n\n// https://github.com/firebase/firebase-js-sdk/blob/7857c212f944a2a9eb421fd4cb7370181bc034b5/packages/messaging/src/interfaces/token-details.ts\nexport interface V2TokenDetails {\n  fcmToken: string;\n  swScope: string;\n  vapidKey: string | Uint8Array;\n  subscription: PushSubscription;\n  fcmSenderId: string;\n  fcmPushSet: string;\n  createTime?: number;\n  endpoint?: string;\n  auth?: string;\n  p256dh?: string;\n}\n\n// https://github.com/firebase/firebase-js-sdk/blob/6b5b15ce4ea3df5df5df8a8b33a4e41e249c7715/packages/messaging/src/interfaces/token-details.ts\nexport interface V3TokenDetails {\n  fcmToken: string;\n  swScope: string;\n  vapidKey: Uint8Array;\n  fcmSenderId: string;\n  fcmPushSet: string;\n  endpoint: string;\n  auth: ArrayBuffer;\n  p256dh: ArrayBuffer;\n  createTime: number;\n}\n\n// https://github.com/firebase/firebase-js-sdk/blob/9567dba664732f681fa7fe60f5b7032bb1daf4c9/packages/messaging/src/interfaces/token-details.ts\nexport interface V4TokenDetails {\n  fcmToken: string;\n  swScope: string;\n  vapidKey: Uint8Array;\n  fcmSenderId: string;\n  endpoint: string;\n  auth: ArrayBufferLike;\n  p256dh: ArrayBufferLike;\n  createTime: number;\n}\n\nconst OLD_DB_NAME = 'fcm_token_details_db';\n/**\n * The last DB version of 'fcm_token_details_db' was 4. This is one higher, so that the upgrade\n * callback is called for all versions of the old DB.\n */\nconst OLD_DB_VERSION = 5;\nconst OLD_OBJECT_STORE_NAME = 'fcm_token_object_Store';\n\nexport async function migrateOldDatabase(\n  senderId: string\n): Promise<TokenDetails | null> {\n  if ('databases' in indexedDB) {\n    // indexedDb.databases() is an IndexedDB v3 API and does not exist in all browsers. TODO: Remove\n    // typecast when it lands in TS types.\n    const databases = await (\n      indexedDB as {\n        databases(): Promise<Array<{ name: string; version: number }>>;\n      }\n    ).databases();\n    const dbNames = databases.map(db => db.name);\n\n    if (!dbNames.includes(OLD_DB_NAME)) {\n      // old DB didn't exist, no need to open.\n      return null;\n    }\n  }\n\n  let tokenDetails: TokenDetails | null = null;\n\n  const db = await openDB(OLD_DB_NAME, OLD_DB_VERSION, {\n    upgrade: async (db, oldVersion, newVersion, upgradeTransaction) => {\n      if (oldVersion < 2) {\n        // Database too old, skip migration.\n        return;\n      }\n\n      if (!db.objectStoreNames.contains(OLD_OBJECT_STORE_NAME)) {\n        // Database did not exist. Nothing to do.\n        return;\n      }\n\n      const objectStore = upgradeTransaction.objectStore(OLD_OBJECT_STORE_NAME);\n      const value = await objectStore.index('fcmSenderId').get(senderId);\n      await objectStore.clear();\n\n      if (!value) {\n        // No entry in the database, nothing to migrate.\n        return;\n      }\n\n      if (oldVersion === 2) {\n        const oldDetails = value as V2TokenDetails;\n\n        if (!oldDetails.auth || !oldDetails.p256dh || !oldDetails.endpoint) {\n          return;\n        }\n\n        tokenDetails = {\n          token: oldDetails.fcmToken,\n          createTime: oldDetails.createTime ?? Date.now(),\n          subscriptionOptions: {\n            auth: oldDetails.auth,\n            p256dh: oldDetails.p256dh,\n            endpoint: oldDetails.endpoint,\n            swScope: oldDetails.swScope,\n            vapidKey:\n              typeof oldDetails.vapidKey === 'string'\n                ? oldDetails.vapidKey\n                : arrayToBase64(oldDetails.vapidKey)\n          }\n        };\n      } else if (oldVersion === 3) {\n        const oldDetails = value as V3TokenDetails;\n\n        tokenDetails = {\n          token: oldDetails.fcmToken,\n          createTime: oldDetails.createTime,\n          subscriptionOptions: {\n            auth: arrayToBase64(oldDetails.auth),\n            p256dh: arrayToBase64(oldDetails.p256dh),\n            endpoint: oldDetails.endpoint,\n            swScope: oldDetails.swScope,\n            vapidKey: arrayToBase64(oldDetails.vapidKey)\n          }\n        };\n      } else if (oldVersion === 4) {\n        const oldDetails = value as V4TokenDetails;\n\n        tokenDetails = {\n          token: oldDetails.fcmToken,\n          createTime: oldDetails.createTime,\n          subscriptionOptions: {\n            auth: arrayToBase64(oldDetails.auth),\n            p256dh: arrayToBase64(oldDetails.p256dh),\n            endpoint: oldDetails.endpoint,\n            swScope: oldDetails.swScope,\n            vapidKey: arrayToBase64(oldDetails.vapidKey)\n          }\n        };\n      }\n    }\n  });\n  db.close();\n\n  // Delete all old databases.\n  await deleteDB(OLD_DB_NAME);\n  await deleteDB('fcm_vapid_details_db');\n  await deleteDB('undefined');\n\n  return checkTokenDetails(tokenDetails) ? tokenDetails : null;\n}\n\nfunction checkTokenDetails(\n  tokenDetails: TokenDetails | null\n): tokenDetails is TokenDetails {\n  if (!tokenDetails || !tokenDetails.subscriptionOptions) {\n    return false;\n  }\n  const { subscriptionOptions } = tokenDetails;\n  return (\n    typeof tokenDetails.createTime === 'number' &&\n    tokenDetails.createTime > 0 &&\n    typeof tokenDetails.token === 'string' &&\n    tokenDetails.token.length > 0 &&\n    typeof subscriptionOptions.auth === 'string' &&\n    subscriptionOptions.auth.length > 0 &&\n    typeof subscriptionOptions.p256dh === 'string' &&\n    subscriptionOptions.p256dh.length > 0 &&\n    typeof subscriptionOptions.endpoint === 'string' &&\n    subscriptionOptions.endpoint.length > 0 &&\n    typeof subscriptionOptions.swScope === 'string' &&\n    subscriptionOptions.swScope.length > 0 &&\n    typeof subscriptionOptions.vapidKey === 'string' &&\n    subscriptionOptions.vapidKey.length > 0\n  );\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { DBSchema, IDBPDatabase, deleteDB, openDB } from 'idb';\n\nimport { FirebaseInternalDependencies } from '../interfaces/internal-dependencies';\nimport { TokenDetails } from '../interfaces/token-details';\nimport { migrateOldDatabase } from '../helpers/migrate-old-database';\n\n// Exported for tests.\nexport const DATABASE_NAME = 'firebase-messaging-database';\nconst DATABASE_VERSION = 1;\nconst OBJECT_STORE_NAME = 'firebase-messaging-store';\n\ninterface MessagingDB extends DBSchema {\n  'firebase-messaging-store': {\n    key: string;\n    value: TokenDetails;\n  };\n}\n\nlet dbPromise: Promise<IDBPDatabase<MessagingDB>> | null = null;\nfunction getDbPromise(): Promise<IDBPDatabase<MessagingDB>> {\n  if (!dbPromise) {\n    dbPromise = openDB(DATABASE_NAME, DATABASE_VERSION, {\n      upgrade: (upgradeDb, oldVersion) => {\n        // We don't use 'break' in this switch statement, the fall-through behavior is what we want,\n        // because if there are multiple versions between the old version and the current version, we\n        // want ALL the migrations that correspond to those versions to run, not only the last one.\n        // eslint-disable-next-line default-case\n        switch (oldVersion) {\n          case 0:\n            upgradeDb.createObjectStore(OBJECT_STORE_NAME);\n        }\n      }\n    });\n  }\n  return dbPromise;\n}\n\n/** Gets record(s) from the objectStore that match the given key. */\nexport async function dbGet(\n  firebaseDependencies: FirebaseInternalDependencies\n): Promise<TokenDetails | undefined> {\n  const key = getKey(firebaseDependencies);\n  const db = await getDbPromise();\n  const tokenDetails = (await db\n    .transaction(OBJECT_STORE_NAME)\n    .objectStore(OBJECT_STORE_NAME)\n    .get(key)) as TokenDetails;\n\n  if (tokenDetails) {\n    return tokenDetails;\n  } else {\n    // Check if there is a tokenDetails object in the old DB.\n    const oldTokenDetails = await migrateOldDatabase(\n      firebaseDependencies.appConfig.senderId\n    );\n    if (oldTokenDetails) {\n      await dbSet(firebaseDependencies, oldTokenDetails);\n      return oldTokenDetails;\n    }\n  }\n}\n\n/** Assigns or overwrites the record for the given key with the given value. */\nexport async function dbSet(\n  firebaseDependencies: FirebaseInternalDependencies,\n  tokenDetails: TokenDetails\n): Promise<TokenDetails> {\n  const key = getKey(firebaseDependencies);\n  const db = await getDbPromise();\n  const tx = db.transaction(OBJECT_STORE_NAME, 'readwrite');\n  await tx.objectStore(OBJECT_STORE_NAME).put(tokenDetails, key);\n  await tx.done;\n  return tokenDetails;\n}\n\n/** Removes record(s) from the objectStore that match the given key. */\nexport async function dbRemove(\n  firebaseDependencies: FirebaseInternalDependencies\n): Promise<void> {\n  const key = getKey(firebaseDependencies);\n  const db = await getDbPromise();\n  const tx = db.transaction(OBJECT_STORE_NAME, 'readwrite');\n  await tx.objectStore(OBJECT_STORE_NAME).delete(key);\n  await tx.done;\n}\n\n/** Deletes the DB. Useful for tests. */\nexport async function dbDelete(): Promise<void> {\n  if (dbPromise) {\n    (await dbPromise).close();\n    await deleteDB(DATABASE_NAME);\n    dbPromise = null;\n  }\n}\n\nfunction getKey({ appConfig }: FirebaseInternalDependencies): string {\n  return appConfig.appId;\n}\n", "/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { ErrorFactory, ErrorMap } from '@firebase/util';\n\nexport const enum ErrorCode {\n  MISSING_APP_CONFIG_VALUES = 'missing-app-config-values',\n  AVAILABLE_IN_WINDOW = 'only-available-in-window',\n  AVAILABLE_IN_SW = 'only-available-in-sw',\n  PERMISSION_DEFAULT = 'permission-default',\n  PERMISSION_BLOCKED = 'permission-blocked',\n  UNSUPPORTED_BROWSER = 'unsupported-browser',\n  INDEXED_DB_UNSUPPORTED = 'indexed-db-unsupported',\n  FAILED_DEFAULT_REGISTRATION = 'failed-service-worker-registration',\n  TOKEN_SUBSCRIBE_FAILED = 'token-subscribe-failed',\n  TOKEN_SUBSCRIBE_NO_TOKEN = 'token-subscribe-no-token',\n  TOKEN_UNSUBSCRIBE_FAILED = 'token-unsubscribe-failed',\n  TOKEN_UPDATE_FAILED = 'token-update-failed',\n  TOKEN_UPDATE_NO_TOKEN = 'token-update-no-token',\n  INVALID_BG_HANDLER = 'invalid-bg-handler',\n  USE_SW_AFTER_GET_TOKEN = 'use-sw-after-get-token',\n  INVALID_SW_REGISTRATION = 'invalid-sw-registration',\n  USE_VAPID_KEY_AFTER_GET_TOKEN = 'use-vapid-key-after-get-token',\n  INVALID_VAPID_KEY = 'invalid-vapid-key'\n}\n\nexport const ERROR_MAP: ErrorMap<ErrorCode> = {\n  [ErrorCode.MISSING_APP_CONFIG_VALUES]:\n    'Missing App configuration value: \"{$valueName}\"',\n  [ErrorCode.AVAILABLE_IN_WINDOW]:\n    'This method is available in a Window context.',\n  [ErrorCode.AVAILABLE_IN_SW]:\n    'This method is available in a service worker context.',\n  [ErrorCode.PERMISSION_DEFAULT]:\n    'The notification permission was not granted and dismissed instead.',\n  [ErrorCode.PERMISSION_BLOCKED]:\n    'The notification permission was not granted and blocked instead.',\n  [ErrorCode.UNSUPPORTED_BROWSER]:\n    \"This browser doesn't support the API's required to use the Firebase SDK.\",\n  [ErrorCode.INDEXED_DB_UNSUPPORTED]:\n    \"This browser doesn't support indexedDb.open() (ex. Safari iFrame, Firefox Private Browsing, etc)\",\n  [ErrorCode.FAILED_DEFAULT_REGISTRATION]:\n    'We are unable to register the default service worker. {$browserErrorMessage}',\n  [ErrorCode.TOKEN_SUBSCRIBE_FAILED]:\n    'A problem occurred while subscribing the user to FCM: {$errorInfo}',\n  [ErrorCode.TOKEN_SUBSCRIBE_NO_TOKEN]:\n    'FCM returned no token when subscribing the user to push.',\n  [ErrorCode.TOKEN_UNSUBSCRIBE_FAILED]:\n    'A problem occurred while unsubscribing the ' +\n    'user from FCM: {$errorInfo}',\n  [ErrorCode.TOKEN_UPDATE_FAILED]:\n    'A problem occurred while updating the user from FCM: {$errorInfo}',\n  [ErrorCode.TOKEN_UPDATE_NO_TOKEN]:\n    'FCM returned no token when updating the user to push.',\n  [ErrorCode.USE_SW_AFTER_GET_TOKEN]:\n    'The useServiceWorker() method may only be called once and must be ' +\n    'called before calling getToken() to ensure your service worker is used.',\n  [ErrorCode.INVALID_SW_REGISTRATION]:\n    'The input to useServiceWorker() must be a ServiceWorkerRegistration.',\n  [ErrorCode.INVALID_BG_HANDLER]:\n    'The input to setBackgroundMessageHandler() must be a function.',\n  [ErrorCode.INVALID_VAPID_KEY]: 'The public VAPID key must be a string.',\n  [ErrorCode.USE_VAPID_KEY_AFTER_GET_TOKEN]:\n    'The usePublicVapidKey() method may only be called once and must be ' +\n    'called before calling getToken() to ensure your VAPID key is used.'\n};\n\ninterface ErrorParams {\n  [ErrorCode.MISSING_APP_CONFIG_VALUES]: {\n    valueName: string;\n  };\n  [ErrorCode.FAILED_DEFAULT_REGISTRATION]: { browserErrorMessage: string };\n  [ErrorCode.TOKEN_SUBSCRIBE_FAILED]: { errorInfo: string };\n  [ErrorCode.TOKEN_UNSUBSCRIBE_FAILED]: { errorInfo: string };\n  [ErrorCode.TOKEN_UPDATE_FAILED]: { errorInfo: string };\n}\n\nexport const ERROR_FACTORY = new ErrorFactory<ErrorCode, ErrorParams>(\n  'messaging',\n  'Messaging',\n  ERROR_MAP\n);\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { DEFAULT_VAPID_KEY, ENDPOINT } from '../util/constants';\nimport { ERROR_FACTORY, ErrorCode } from '../util/errors';\nimport { SubscriptionOptions, TokenDetails } from '../interfaces/token-details';\n\nimport { AppConfig } from '../interfaces/app-config';\nimport { FirebaseInternalDependencies } from '../interfaces/internal-dependencies';\n\nexport interface ApiResponse {\n  token?: string;\n  error?: { message: string };\n}\n\nexport interface ApiRequestBody {\n  web: {\n    endpoint: string;\n    p256dh: string;\n    auth: string;\n    applicationPubKey?: string;\n  };\n}\n\nexport async function requestGetToken(\n  firebaseDependencies: FirebaseInternalDependencies,\n  subscriptionOptions: SubscriptionOptions\n): Promise<string> {\n  const headers = await getHeaders(firebaseDependencies);\n  const body = getBody(subscriptionOptions);\n\n  const subscribeOptions = {\n    method: 'POST',\n    headers,\n    body: JSON.stringify(body)\n  };\n\n  let responseData: ApiResponse;\n  try {\n    const response = await fetch(\n      getEndpoint(firebaseDependencies.appConfig),\n      subscribeOptions\n    );\n    responseData = await response.json();\n  } catch (err) {\n    throw ERROR_FACTORY.create(ErrorCode.TOKEN_SUBSCRIBE_FAILED, {\n      errorInfo: (err as Error)?.toString()\n    });\n  }\n\n  if (responseData.error) {\n    const message = responseData.error.message;\n    throw ERROR_FACTORY.create(ErrorCode.TOKEN_SUBSCRIBE_FAILED, {\n      errorInfo: message\n    });\n  }\n\n  if (!responseData.token) {\n    throw ERROR_FACTORY.create(ErrorCode.TOKEN_SUBSCRIBE_NO_TOKEN);\n  }\n\n  return responseData.token;\n}\n\nexport async function requestUpdateToken(\n  firebaseDependencies: FirebaseInternalDependencies,\n  tokenDetails: TokenDetails\n): Promise<string> {\n  const headers = await getHeaders(firebaseDependencies);\n  const body = getBody(tokenDetails.subscriptionOptions!);\n\n  const updateOptions = {\n    method: 'PATCH',\n    headers,\n    body: JSON.stringify(body)\n  };\n\n  let responseData: ApiResponse;\n  try {\n    const response = await fetch(\n      `${getEndpoint(firebaseDependencies.appConfig)}/${tokenDetails.token}`,\n      updateOptions\n    );\n    responseData = await response.json();\n  } catch (err) {\n    throw ERROR_FACTORY.create(ErrorCode.TOKEN_UPDATE_FAILED, {\n      errorInfo: (err as Error)?.toString()\n    });\n  }\n\n  if (responseData.error) {\n    const message = responseData.error.message;\n    throw ERROR_FACTORY.create(ErrorCode.TOKEN_UPDATE_FAILED, {\n      errorInfo: message\n    });\n  }\n\n  if (!responseData.token) {\n    throw ERROR_FACTORY.create(ErrorCode.TOKEN_UPDATE_NO_TOKEN);\n  }\n\n  return responseData.token;\n}\n\nexport async function requestDeleteToken(\n  firebaseDependencies: FirebaseInternalDependencies,\n  token: string\n): Promise<void> {\n  const headers = await getHeaders(firebaseDependencies);\n\n  const unsubscribeOptions = {\n    method: 'DELETE',\n    headers\n  };\n\n  try {\n    const response = await fetch(\n      `${getEndpoint(firebaseDependencies.appConfig)}/${token}`,\n      unsubscribeOptions\n    );\n    const responseData: ApiResponse = await response.json();\n    if (responseData.error) {\n      const message = responseData.error.message;\n      throw ERROR_FACTORY.create(ErrorCode.TOKEN_UNSUBSCRIBE_FAILED, {\n        errorInfo: message\n      });\n    }\n  } catch (err) {\n    throw ERROR_FACTORY.create(ErrorCode.TOKEN_UNSUBSCRIBE_FAILED, {\n      errorInfo: (err as Error)?.toString()\n    });\n  }\n}\n\nfunction getEndpoint({ projectId }: AppConfig): string {\n  return `${ENDPOINT}/projects/${projectId!}/registrations`;\n}\n\nasync function getHeaders({\n  appConfig,\n  installations\n}: FirebaseInternalDependencies): Promise<Headers> {\n  const authToken = await installations.getToken();\n\n  return new Headers({\n    'Content-Type': 'application/json',\n    Accept: 'application/json',\n    'x-goog-api-key': appConfig.apiKey!,\n    'x-goog-firebase-installations-auth': `FIS ${authToken}`\n  });\n}\n\nfunction getBody({\n  p256dh,\n  auth,\n  endpoint,\n  vapidKey\n}: SubscriptionOptions): ApiRequestBody {\n  const body: ApiRequestBody = {\n    web: {\n      endpoint,\n      auth,\n      p256dh\n    }\n  };\n\n  if (vapidKey !== DEFAULT_VAPID_KEY) {\n    body.web.applicationPubKey = vapidKey;\n  }\n\n  return body;\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { SubscriptionOptions, TokenDetails } from '../interfaces/token-details';\nimport {\n  arrayToBase64,\n  base64ToArray\n} from '../helpers/array-base64-translator';\nimport { dbGet, dbRemove, dbSet } from './idb-manager';\nimport {\n  requestDeleteToken,\n  requestGetToken,\n  requestUpdateToken\n} from './requests';\n\nimport { FirebaseInternalDependencies } from '../interfaces/internal-dependencies';\nimport { MessagingService } from '../messaging-service';\n\n// UpdateRegistration will be called once every week.\nconst TOKEN_EXPIRATION_MS = 7 * 24 * 60 * 60 * 1000; // 7 days\n\nexport async function getTokenInternal(\n  messaging: MessagingService\n): Promise<string> {\n  const pushSubscription = await getPushSubscription(\n    messaging.swRegistration!,\n    messaging.vapidKey!\n  );\n\n  const subscriptionOptions: SubscriptionOptions = {\n    vapidKey: messaging.vapidKey!,\n    swScope: messaging.swRegistration!.scope,\n    endpoint: pushSubscription.endpoint,\n    auth: arrayToBase64(pushSubscription.getKey('auth')!),\n    p256dh: arrayToBase64(pushSubscription.getKey('p256dh')!)\n  };\n\n  const tokenDetails = await dbGet(messaging.firebaseDependencies);\n  if (!tokenDetails) {\n    // No token, get a new one.\n    return getNewToken(messaging.firebaseDependencies, subscriptionOptions);\n  } else if (\n    !isTokenValid(tokenDetails.subscriptionOptions!, subscriptionOptions)\n  ) {\n    // Invalid token, get a new one.\n    try {\n      await requestDeleteToken(\n        messaging.firebaseDependencies!,\n        tokenDetails.token\n      );\n    } catch (e) {\n      // Suppress errors because of #2364\n      console.warn(e);\n    }\n\n    return getNewToken(messaging.firebaseDependencies!, subscriptionOptions);\n  } else if (Date.now() >= tokenDetails.createTime + TOKEN_EXPIRATION_MS) {\n    // Weekly token refresh\n    return updateToken(messaging, {\n      token: tokenDetails.token,\n      createTime: Date.now(),\n      subscriptionOptions\n    });\n  } else {\n    // Valid token, nothing to do.\n    return tokenDetails.token;\n  }\n}\n\n/**\n * This method deletes the token from the database, unsubscribes the token from FCM, and unregisters\n * the push subscription if it exists.\n */\nexport async function deleteTokenInternal(\n  messaging: MessagingService\n): Promise<boolean> {\n  const tokenDetails = await dbGet(messaging.firebaseDependencies);\n  if (tokenDetails) {\n    await requestDeleteToken(\n      messaging.firebaseDependencies,\n      tokenDetails.token\n    );\n    await dbRemove(messaging.firebaseDependencies);\n  }\n\n  // Unsubscribe from the push subscription.\n  const pushSubscription =\n    await messaging.swRegistration!.pushManager.getSubscription();\n  if (pushSubscription) {\n    return pushSubscription.unsubscribe();\n  }\n\n  // If there's no SW, consider it a success.\n  return true;\n}\n\nasync function updateToken(\n  messaging: MessagingService,\n  tokenDetails: TokenDetails\n): Promise<string> {\n  try {\n    const updatedToken = await requestUpdateToken(\n      messaging.firebaseDependencies,\n      tokenDetails\n    );\n\n    const updatedTokenDetails: TokenDetails = {\n      ...tokenDetails,\n      token: updatedToken,\n      createTime: Date.now()\n    };\n\n    await dbSet(messaging.firebaseDependencies, updatedTokenDetails);\n    return updatedToken;\n  } catch (e) {\n    throw e;\n  }\n}\n\nasync function getNewToken(\n  firebaseDependencies: FirebaseInternalDependencies,\n  subscriptionOptions: SubscriptionOptions\n): Promise<string> {\n  const token = await requestGetToken(\n    firebaseDependencies,\n    subscriptionOptions\n  );\n  const tokenDetails: TokenDetails = {\n    token,\n    createTime: Date.now(),\n    subscriptionOptions\n  };\n  await dbSet(firebaseDependencies, tokenDetails);\n  return tokenDetails.token;\n}\n\n/**\n * Gets a PushSubscription for the current user.\n */\nasync function getPushSubscription(\n  swRegistration: ServiceWorkerRegistration,\n  vapidKey: string\n): Promise<PushSubscription> {\n  const subscription = await swRegistration.pushManager.getSubscription();\n  if (subscription) {\n    return subscription;\n  }\n\n  return swRegistration.pushManager.subscribe({\n    userVisibleOnly: true,\n    // Chrome <= 75 doesn't support base64-encoded VAPID key. For backward compatibility, VAPID key\n    // submitted to pushManager#subscribe must be of type Uint8Array.\n    applicationServerKey: base64ToArray(vapidKey)\n  });\n}\n\n/**\n * Checks if the saved tokenDetails object matches the configuration provided.\n */\nfunction isTokenValid(\n  dbOptions: SubscriptionOptions,\n  currentOptions: SubscriptionOptions\n): boolean {\n  const isVapidKeyEqual = currentOptions.vapidKey === dbOptions.vapidKey;\n  const isEndpointEqual = currentOptions.endpoint === dbOptions.endpoint;\n  const isAuthEqual = currentOptions.auth === dbOptions.auth;\n  const isP256dhEqual = currentOptions.p256dh === dbOptions.p256dh;\n\n  return isVapidKeyEqual && isEndpointEqual && isAuthEqual && isP256dhEqual;\n}\n", "/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { MessagePayload } from '../interfaces/public-types';\nimport { MessagePayloadInternal } from '../interfaces/internal-message-payload';\n\nexport function externalizePayload(\n  internalPayload: MessagePayloadInternal\n): MessagePayload {\n  const payload: MessagePayload = {\n    from: internalPayload.from,\n    // eslint-disable-next-line camelcase\n    collapseKey: internalPayload.collapse_key,\n    // eslint-disable-next-line camelcase\n    messageId: internalPayload.fcmMessageId\n  } as MessagePayload;\n\n  propagateNotificationPayload(payload, internalPayload);\n  propagateDataPayload(payload, internalPayload);\n  propagateFcmOptions(payload, internalPayload);\n\n  return payload;\n}\n\nfunction propagateNotificationPayload(\n  payload: MessagePayload,\n  messagePayloadInternal: MessagePayloadInternal\n): void {\n  if (!messagePayloadInternal.notification) {\n    return;\n  }\n\n  payload.notification = {};\n\n  const title = messagePayloadInternal.notification!.title;\n  if (!!title) {\n    payload.notification!.title = title;\n  }\n\n  const body = messagePayloadInternal.notification!.body;\n  if (!!body) {\n    payload.notification!.body = body;\n  }\n\n  const image = messagePayloadInternal.notification!.image;\n  if (!!image) {\n    payload.notification!.image = image;\n  }\n\n  const icon = messagePayloadInternal.notification!.icon;\n  if (!!icon) {\n    payload.notification!.icon = icon;\n  }\n}\n\nfunction propagateDataPayload(\n  payload: MessagePayload,\n  messagePayloadInternal: MessagePayloadInternal\n): void {\n  if (!messagePayloadInternal.data) {\n    return;\n  }\n\n  payload.data = messagePayloadInternal.data as { [key: string]: string };\n}\n\nfunction propagateFcmOptions(\n  payload: MessagePayload,\n  messagePayloadInternal: MessagePayloadInternal\n): void {\n  // fcmOptions.link value is written into notification.click_action. see more in b/232072111\n  if (\n    !messagePayloadInternal.fcmOptions &&\n    !messagePayloadInternal.notification?.click_action\n  ) {\n    return;\n  }\n\n  payload.fcmOptions = {};\n\n  const link =\n    messagePayloadInternal.fcmOptions?.link ??\n    messagePayloadInternal.notification?.click_action;\n\n  if (!!link) {\n    payload.fcmOptions!.link = link;\n  }\n\n  // eslint-disable-next-line camelcase\n  const analyticsLabel = messagePayloadInternal.fcmOptions?.analytics_label;\n  if (!!analyticsLabel) {\n    payload.fcmOptions!.analyticsLabel = analyticsLabel;\n  }\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { CONSOLE_CAMPAIGN_ID } from '../util/constants';\nimport { ConsoleMessageData } from '../interfaces/internal-message-payload';\n\nexport function isConsoleMessage(data: unknown): data is ConsoleMessageData {\n  // This message has a campaign ID, meaning it was sent using the Firebase Console.\n  return typeof data === 'object' && !!data && CONSOLE_CAMPAIGN_ID in data;\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n/** Returns a promise that resolves after given time passes. */\nexport function sleep(ms: number): Promise<void> {\n  return new Promise<void>(resolve => {\n    setTimeout(resolve, ms);\n  });\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {\n  DEFAULT_BACKOFF_TIME_MS,\n  EVENT_MESSAGE_DELIVERED,\n  FCM_LOG_SOURCE,\n  LOG_INTERVAL_IN_MS,\n  MAX_NUMBER_OF_EVENTS_PER_LOG_REQUEST,\n  MAX_RETRIES,\n  MessageType,\n  SDK_PLATFORM_WEB\n} from '../util/constants';\nimport {\n  FcmEvent,\n  LogEvent,\n  LogRequest,\n  LogResponse,\n  ComplianceData\n} from '../interfaces/logging-types';\n\nimport { MessagePayloadInternal } from '../interfaces/internal-message-payload';\nimport { MessagingService } from '../messaging-service';\n\nconst LOG_ENDPOINT = 'https://play.google.com/log?format=json_proto3';\n\nconst FCM_TRANSPORT_KEY = _mergeStrings(\n  'AzSCbw63g1R0nCw85jG8',\n  'Iaya3yLKwmgvh7cF0q4'\n);\n\nexport function startLoggingService(messaging: MessagingService): void {\n  if (!messaging.isLogServiceStarted) {\n    _processQueue(messaging, LOG_INTERVAL_IN_MS);\n    messaging.isLogServiceStarted = true;\n  }\n}\n\n/**\n *\n * @param messaging the messaging instance.\n * @param offsetInMs this method execute after `offsetInMs` elapsed .\n */\nexport function _processQueue(\n  messaging: MessagingService,\n  offsetInMs: number\n): void {\n  setTimeout(async () => {\n    if (!messaging.deliveryMetricsExportedToBigQueryEnabled) {\n      // flush events and terminate logging service\n      messaging.logEvents = [];\n      messaging.isLogServiceStarted = false;\n\n      return;\n    }\n\n    if (!messaging.logEvents.length) {\n      return _processQueue(messaging, LOG_INTERVAL_IN_MS);\n    }\n\n    await _dispatchLogEvents(messaging);\n  }, offsetInMs);\n}\n\nexport async function _dispatchLogEvents(\n  messaging: MessagingService\n): Promise<void> {\n  for (\n    let i = 0, n = messaging.logEvents.length;\n    i < n;\n    i += MAX_NUMBER_OF_EVENTS_PER_LOG_REQUEST\n  ) {\n    const logRequest = _createLogRequest(\n      messaging.logEvents.slice(i, i + MAX_NUMBER_OF_EVENTS_PER_LOG_REQUEST)\n    );\n\n    let retryCount = 0,\n      response = {} as Response;\n\n    do {\n      try {\n        response = await fetch(\n          LOG_ENDPOINT.concat('&key=', FCM_TRANSPORT_KEY),\n          {\n            method: 'POST',\n            body: JSON.stringify(logRequest)\n          }\n        );\n\n        // don't retry on 200s or non retriable errors\n        if (response.ok || (!response.ok && !isRetriableError(response))) {\n          break;\n        }\n\n        if (!response.ok && isRetriableError(response)) {\n          // rethrow to retry with quota\n          throw new Error(\n            'a retriable Non-200 code is returned in fetch to Firelog endpoint. Retry'\n          );\n        }\n      } catch (error) {\n        const isLastAttempt = retryCount === MAX_RETRIES;\n        if (isLastAttempt) {\n          // existing the do-while interactive retry logic because retry quota has reached.\n          break;\n        }\n      }\n\n      let delayInMs: number;\n      try {\n        delayInMs = Number(\n          ((await response.json()) as LogResponse).nextRequestWaitMillis\n        );\n      } catch (e) {\n        delayInMs = DEFAULT_BACKOFF_TIME_MS;\n      }\n\n      await new Promise(resolve => setTimeout(resolve, delayInMs));\n\n      retryCount++;\n    } while (retryCount < MAX_RETRIES);\n  }\n\n  messaging.logEvents = [];\n  // schedule for next logging\n  _processQueue(messaging, LOG_INTERVAL_IN_MS);\n}\n\nfunction isRetriableError(response: Response): boolean {\n  const httpStatus = response.status;\n\n  return (\n    httpStatus === 429 ||\n    httpStatus === 500 ||\n    httpStatus === 503 ||\n    httpStatus === 504\n  );\n}\n\nexport async function stageLog(\n  messaging: MessagingService,\n  internalPayload: MessagePayloadInternal\n): Promise<void> {\n  const fcmEvent = createFcmEvent(\n    internalPayload,\n    await messaging.firebaseDependencies.installations.getId()\n  );\n\n  createAndEnqueueLogEvent(messaging, fcmEvent, internalPayload.productId);\n}\n\nfunction createFcmEvent(\n  internalPayload: MessagePayloadInternal,\n  fid: string\n): FcmEvent {\n  const fcmEvent = {} as FcmEvent;\n\n  /* eslint-disable camelcase */\n  // some fields should always be non-null. Still check to ensure.\n  if (!!internalPayload.from) {\n    fcmEvent.project_number = internalPayload.from;\n  }\n\n  if (!!internalPayload.fcmMessageId) {\n    fcmEvent.message_id = internalPayload.fcmMessageId;\n  }\n\n  fcmEvent.instance_id = fid;\n\n  if (!!internalPayload.notification) {\n    fcmEvent.message_type = MessageType.DISPLAY_NOTIFICATION.toString();\n  } else {\n    fcmEvent.message_type = MessageType.DATA_MESSAGE.toString();\n  }\n\n  fcmEvent.sdk_platform = SDK_PLATFORM_WEB.toString();\n  fcmEvent.package_name = self.origin.replace(/(^\\w+:|^)\\/\\//, '');\n\n  if (!!internalPayload.collapse_key) {\n    fcmEvent.collapse_key = internalPayload.collapse_key;\n  }\n\n  fcmEvent.event = EVENT_MESSAGE_DELIVERED.toString();\n\n  if (!!internalPayload.fcmOptions?.analytics_label) {\n    fcmEvent.analytics_label = internalPayload.fcmOptions?.analytics_label;\n  }\n\n  /* eslint-enable camelcase */\n  return fcmEvent;\n}\n\nfunction createAndEnqueueLogEvent(\n  messaging: MessagingService,\n  fcmEvent: FcmEvent,\n  productId: number\n): void {\n  const logEvent = {} as LogEvent;\n\n  /* eslint-disable camelcase */\n  logEvent.event_time_ms = Math.floor(Date.now()).toString();\n  logEvent.source_extension_json_proto3 = JSON.stringify({\n    messaging_client_event: fcmEvent\n  });\n\n  if (!!productId) {\n    logEvent.compliance_data = buildComplianceData(productId);\n  }\n  // eslint-disable-next-line camelcase\n\n  messaging.logEvents.push(logEvent);\n}\n\nfunction buildComplianceData(productId: number): ComplianceData {\n  const complianceData: ComplianceData = {\n    privacy_context: {\n      prequest: {\n        origin_associated_product_id: productId\n      }\n    }\n  };\n\n  return complianceData;\n}\n\nexport function _createLogRequest(logEventQueue: LogEvent[]): LogRequest {\n  const logRequest = {} as LogRequest;\n\n  /* eslint-disable camelcase */\n  logRequest.log_source = FCM_LOG_SOURCE.toString();\n  logRequest.log_event = logEventQueue;\n  /* eslint-enable camelcase */\n\n  return logRequest;\n}\n\nexport function _mergeStrings(s1: string, s2: string): string {\n  const resultArray = [];\n  for (let i = 0; i < s1.length; i++) {\n    resultArray.push(s1.charAt(i));\n    if (i < s2.length) {\n      resultArray.push(s2.charAt(i));\n    }\n  }\n\n  return resultArray.join('');\n}\n", "/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { DEFAULT_VAPID_KEY, FCM_MSG } from '../util/constants';\nimport {\n  MessagePayloadInternal,\n  MessageType,\n  NotificationPayloadInternal\n} from '../interfaces/internal-message-payload';\nimport {\n  NotificationEvent,\n  PushEvent,\n  PushSubscriptionChangeEvent,\n  ServiceWorkerGlobalScope,\n  WindowClient\n} from '../util/sw-types';\nimport {\n  deleteTokenInternal,\n  getTokenInternal\n} from '../internals/token-manager';\n\nimport { MessagingService } from '../messaging-service';\nimport { dbGet } from '../internals/idb-manager';\nimport { externalizePayload } from '../helpers/externalizePayload';\nimport { isConsoleMessage } from '../helpers/is-console-message';\nimport { sleep } from '../helpers/sleep';\nimport { stageLog } from '../helpers/logToFirelog';\n\n// maxActions is an experimental property and not part of the official\n// TypeScript interface\n// https://developer.mozilla.org/en-US/docs/Web/API/Notification/maxActions\ninterface NotificationExperimental extends Notification {\n  maxActions?: number;\n}\n\n// Let TS know that this is a service worker\ndeclare const self: ServiceWorkerGlobalScope;\n\nexport async function onSubChange(\n  event: PushSubscriptionChangeEvent,\n  messaging: MessagingService\n): Promise<void> {\n  const { newSubscription } = event;\n  if (!newSubscription) {\n    // Subscription revoked, delete token\n    await deleteTokenInternal(messaging);\n    return;\n  }\n\n  const tokenDetails = await dbGet(messaging.firebaseDependencies);\n  await deleteTokenInternal(messaging);\n\n  messaging.vapidKey =\n    tokenDetails?.subscriptionOptions?.vapidKey ?? DEFAULT_VAPID_KEY;\n  await getTokenInternal(messaging);\n}\n\nexport async function onPush(\n  event: PushEvent,\n  messaging: MessagingService\n): Promise<void> {\n  const internalPayload = getMessagePayloadInternal(event);\n  if (!internalPayload) {\n    // Failed to get parsed MessagePayload from the PushEvent. Skip handling the push.\n    return;\n  }\n\n  // log to Firelog with user consent\n  if (messaging.deliveryMetricsExportedToBigQueryEnabled) {\n    await stageLog(messaging, internalPayload);\n  }\n\n  // foreground handling: eventually passed to onMessage hook\n  const clientList = await getClientList();\n  if (hasVisibleClients(clientList)) {\n    return sendMessagePayloadInternalToWindows(clientList, internalPayload);\n  }\n\n  // background handling: display if possible and pass to onBackgroundMessage hook\n  if (!!internalPayload.notification) {\n    await showNotification(wrapInternalPayload(internalPayload));\n  }\n\n  if (!messaging) {\n    return;\n  }\n\n  if (!!messaging.onBackgroundMessageHandler) {\n    const payload = externalizePayload(internalPayload);\n\n    if (typeof messaging.onBackgroundMessageHandler === 'function') {\n      await messaging.onBackgroundMessageHandler(payload);\n    } else {\n      messaging.onBackgroundMessageHandler.next(payload);\n    }\n  }\n}\n\nexport async function onNotificationClick(\n  event: NotificationEvent\n): Promise<void> {\n  const internalPayload: MessagePayloadInternal =\n    event.notification?.data?.[FCM_MSG];\n\n  if (!internalPayload) {\n    return;\n  } else if (event.action) {\n    // User clicked on an action button. This will allow developers to act on action button clicks\n    // by using a custom onNotificationClick listener that they define.\n    return;\n  }\n\n  // Prevent other listeners from receiving the event\n  event.stopImmediatePropagation();\n  event.notification.close();\n\n  // Note clicking on a notification with no link set will focus the Chrome's current tab.\n  const link = getLink(internalPayload);\n  if (!link) {\n    return;\n  }\n\n  // FM should only open/focus links from app's origin.\n  const url = new URL(link, self.location.href);\n  const originUrl = new URL(self.location.origin);\n\n  if (url.host !== originUrl.host) {\n    return;\n  }\n\n  let client = await getWindowClient(url);\n\n  if (!client) {\n    client = await self.clients.openWindow(link);\n\n    // Wait three seconds for the client to initialize and set up the message handler so that it\n    // can receive the message.\n    await sleep(3000);\n  } else {\n    client = await client.focus();\n  }\n\n  if (!client) {\n    // Window Client will not be returned if it's for a third party origin.\n    return;\n  }\n\n  internalPayload.messageType = MessageType.NOTIFICATION_CLICKED;\n  internalPayload.isFirebaseMessaging = true;\n  return client.postMessage(internalPayload);\n}\n\nfunction wrapInternalPayload(\n  internalPayload: MessagePayloadInternal\n): NotificationPayloadInternal {\n  const wrappedInternalPayload: NotificationPayloadInternal = {\n    ...(internalPayload.notification as unknown as NotificationPayloadInternal)\n  };\n\n  // Put the message payload under FCM_MSG name so we can identify the notification as being an FCM\n  // notification vs a notification from somewhere else (i.e. normal web push or developer generated\n  // notification).\n  wrappedInternalPayload.data = {\n    [FCM_MSG]: internalPayload\n  };\n\n  return wrappedInternalPayload;\n}\n\nfunction getMessagePayloadInternal({\n  data\n}: PushEvent): MessagePayloadInternal | null {\n  if (!data) {\n    return null;\n  }\n\n  try {\n    return data.json();\n  } catch (err) {\n    // Not JSON so not an FCM message.\n    return null;\n  }\n}\n\n/**\n * @param url The URL to look for when focusing a client.\n * @return Returns an existing window client or a newly opened WindowClient.\n */\nasync function getWindowClient(url: URL): Promise<WindowClient | null> {\n  const clientList = await getClientList();\n\n  for (const client of clientList) {\n    const clientUrl = new URL(client.url, self.location.href);\n\n    if (url.host === clientUrl.host) {\n      return client;\n    }\n  }\n\n  return null;\n}\n\n/**\n * @returns If there is currently a visible WindowClient, this method will resolve to true,\n * otherwise false.\n */\nfunction hasVisibleClients(clientList: WindowClient[]): boolean {\n  return clientList.some(\n    client =>\n      client.visibilityState === 'visible' &&\n      // Ignore chrome-extension clients as that matches the background pages of extensions, which\n      // are always considered visible for some reason.\n      !client.url.startsWith('chrome-extension://')\n  );\n}\n\nfunction sendMessagePayloadInternalToWindows(\n  clientList: WindowClient[],\n  internalPayload: MessagePayloadInternal\n): void {\n  internalPayload.isFirebaseMessaging = true;\n  internalPayload.messageType = MessageType.PUSH_RECEIVED;\n\n  for (const client of clientList) {\n    client.postMessage(internalPayload);\n  }\n}\n\nfunction getClientList(): Promise<WindowClient[]> {\n  return self.clients.matchAll({\n    type: 'window',\n    includeUncontrolled: true\n    // TS doesn't know that \"type: 'window'\" means it'll return WindowClient[]\n  }) as Promise<WindowClient[]>;\n}\n\nfunction showNotification(\n  notificationPayloadInternal: NotificationPayloadInternal\n): Promise<void> {\n  // Note: Firefox does not support the maxActions property.\n  // https://developer.mozilla.org/en-US/docs/Web/API/notification/maxActions\n  const { actions } = notificationPayloadInternal;\n  const { maxActions } = Notification as unknown as NotificationExperimental;\n  if (actions && maxActions && actions.length > maxActions) {\n    console.warn(\n      `This browser only supports ${maxActions} actions. The remaining actions will not be displayed.`\n    );\n  }\n\n  return self.registration.showNotification(\n    /* title= */ notificationPayloadInternal.title ?? '',\n    notificationPayloadInternal\n  );\n}\n\nfunction getLink(payload: MessagePayloadInternal): string | null {\n  // eslint-disable-next-line camelcase\n  const link = payload.fcmOptions?.link ?? payload.notification?.click_action;\n  if (link) {\n    return link;\n  }\n\n  if (isConsoleMessage(payload.data)) {\n    // Notification created in the Firebase Console. Redirect to origin.\n    return self.location.origin;\n  } else {\n    return null;\n  }\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { ERROR_FACTORY, ErrorCode } from '../util/errors';\nimport { FirebaseApp, FirebaseOptions } from '@firebase/app';\n\nimport { AppConfig } from '../interfaces/app-config';\nimport { FirebaseError } from '@firebase/util';\n\nexport function extractAppConfig(app: FirebaseApp): AppConfig {\n  if (!app || !app.options) {\n    throw getMissingValueError('App Configuration Object');\n  }\n\n  if (!app.name) {\n    throw getMissingValueError('App Name');\n  }\n\n  // Required app config keys\n  const configKeys: ReadonlyArray<keyof FirebaseOptions> = [\n    'projectId',\n    'apiKey',\n    'appId',\n    'messagingSenderId'\n  ];\n\n  const { options } = app;\n  for (const keyName of configKeys) {\n    if (!options[keyName]) {\n      throw getMissingValueError(keyName);\n    }\n  }\n\n  return {\n    appName: app.name,\n    projectId: options.projectId!,\n    apiKey: options.apiKey!,\n    appId: options.appId!,\n    senderId: options.messagingSenderId!\n  };\n}\n\nfunction getMissingValueError(valueName: string): FirebaseError {\n  return ERROR_FACTORY.create(ErrorCode.MISSING_APP_CONFIG_VALUES, {\n    valueName\n  });\n}\n", "/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { FirebaseApp, _FirebaseService } from '@firebase/app';\nimport { MessagePayload, NextFn, Observer } from './interfaces/public-types';\n\nimport { FirebaseAnalyticsInternalName } from '@firebase/analytics-interop-types';\nimport { FirebaseInternalDependencies } from './interfaces/internal-dependencies';\nimport { LogEvent } from './interfaces/logging-types';\nimport { Provider } from '@firebase/component';\nimport { _FirebaseInstallationsInternal } from '@firebase/installations';\nimport { extractAppConfig } from './helpers/extract-app-config';\n\nexport class MessagingService implements _FirebaseService {\n  readonly app!: FirebaseApp;\n  readonly firebaseDependencies!: FirebaseInternalDependencies;\n\n  swRegistration?: ServiceWorkerRegistration;\n  vapidKey?: string;\n  // logging is only done with end user consent. Default to false.\n  deliveryMetricsExportedToBigQueryEnabled: boolean = false;\n\n  onBackgroundMessageHandler:\n    | NextFn<MessagePayload>\n    | Observer<MessagePayload>\n    | null = null;\n\n  onMessageHandler: NextFn<MessagePayload> | Observer<MessagePayload> | null =\n    null;\n\n  logEvents: LogEvent[] = [];\n  isLogServiceStarted: boolean = false;\n\n  constructor(\n    app: FirebaseApp,\n    installations: _FirebaseInstallationsInternal,\n    analyticsProvider: Provider<FirebaseAnalyticsInternalName>\n  ) {\n    const appConfig = extractAppConfig(app);\n\n    this.firebaseDependencies = {\n      app,\n      appConfig,\n      installations,\n      analyticsProvider\n    };\n  }\n\n  _delete(): Promise<void> {\n    return Promise.resolve();\n  }\n}\n", "/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {\n  Component,\n  ComponentContainer,\n  ComponentType,\n  InstanceFactory\n} from '@firebase/component';\nimport {\n  onNotificationClick,\n  onPush,\n  onSubChange\n} from '../listeners/sw-listeners';\n\nimport { GetTokenOptions } from '../interfaces/public-types';\nimport { MessagingInternal } from '@firebase/messaging-interop-types';\nimport { MessagingService } from '../messaging-service';\nimport { ServiceWorkerGlobalScope } from '../util/sw-types';\nimport { _registerComponent, registerVersion } from '@firebase/app';\nimport { getToken } from '../api/getToken';\nimport { messageEventListener } from '../listeners/window-listener';\n\nimport { name, version } from '../../package.json';\n\nconst WindowMessagingFactory: InstanceFactory<'messaging'> = (\n  container: ComponentContainer\n) => {\n  const messaging = new MessagingService(\n    container.getProvider('app').getImmediate(),\n    container.getProvider('installations-internal').getImmediate(),\n    container.getProvider('analytics-internal')\n  );\n\n  navigator.serviceWorker.addEventListener('message', e =>\n    messageEventListener(messaging as MessagingService, e)\n  );\n\n  return messaging;\n};\n\nconst WindowMessagingInternalFactory: InstanceFactory<'messaging-internal'> = (\n  container: ComponentContainer\n) => {\n  const messaging = container\n    .getProvider('messaging')\n    .getImmediate() as MessagingService;\n\n  const messagingInternal: MessagingInternal = {\n    getToken: (options?: GetTokenOptions) => getToken(messaging, options)\n  };\n\n  return messagingInternal;\n};\n\ndeclare const self: ServiceWorkerGlobalScope;\nconst SwMessagingFactory: InstanceFactory<'messaging'> = (\n  container: ComponentContainer\n) => {\n  const messaging = new MessagingService(\n    container.getProvider('app').getImmediate(),\n    container.getProvider('installations-internal').getImmediate(),\n    container.getProvider('analytics-internal')\n  );\n\n  self.addEventListener('push', e => {\n    e.waitUntil(onPush(e, messaging as MessagingService));\n  });\n  self.addEventListener('pushsubscriptionchange', e => {\n    e.waitUntil(onSubChange(e, messaging as MessagingService));\n  });\n  self.addEventListener('notificationclick', e => {\n    e.waitUntil(onNotificationClick(e));\n  });\n\n  return messaging;\n};\n\nexport function registerMessagingInWindow(): void {\n  _registerComponent(\n    new Component('messaging', WindowMessagingFactory, ComponentType.PUBLIC)\n  );\n\n  _registerComponent(\n    new Component(\n      'messaging-internal',\n      WindowMessagingInternalFactory,\n      ComponentType.PRIVATE\n    )\n  );\n\n  registerVersion(name, version);\n  // BUILD_TARGET will be replaced by values like esm5, esm2017, cjs5, etc during the compilation\n  registerVersion(name, version, '__BUILD_TARGET__');\n}\n\n/**\n * The messaging instance registered in sw is named differently than that of in client. This is\n * because both `registerMessagingInWindow` and `registerMessagingInSw` would be called in\n * `messaging-compat` and component with the same name can only be registered once.\n */\nexport function registerMessagingInSw(): void {\n  _registerComponent(\n    new Component('messaging-sw', SwMessagingFactory, ComponentType.PUBLIC)\n  );\n}\n", "/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {\n  areCookiesEnabled,\n  isIndexedDBAvailable,\n  validateIndexedDBOpenable\n} from '@firebase/util';\n\n/**\n * Checks if all required APIs exist in the browser.\n * @returns a Promise that resolves to a boolean.\n *\n * @public\n */\nexport async function isWindowSupported(): Promise<boolean> {\n  try {\n    // This throws if open() is unsupported, so adding it to the conditional\n    // statement below can cause an uncaught error.\n    await validateIndexedDBOpenable();\n  } catch (e) {\n    return false;\n  }\n  // firebase-js-sdk/issues/2393 reveals that idb#open in Safari iframe and Firefox private browsing\n  // might be prohibited to run. In these contexts, an error would be thrown during the messaging\n  // instantiating phase, informing the developers to import/call isSupported for special handling.\n  return (\n    typeof window !== 'undefined' &&\n    isIndexedDBAvailable() &&\n    areCookiesEnabled() &&\n    'serviceWorker' in navigator &&\n    'PushManager' in window &&\n    'Notification' in window &&\n    'fetch' in window &&\n    ServiceWorkerRegistration.prototype.hasOwnProperty('showNotification') &&\n    PushSubscription.prototype.hasOwnProperty('getKey')\n  );\n}\n\n/**\n * Checks whether all required APIs exist within SW Context\n * @returns a Promise that resolves to a boolean.\n *\n * @public\n */\nexport async function isSwSupported(): Promise<boolean> {\n  // firebase-js-sdk/issues/2393 reveals that idb#open in Safari iframe and Firefox private browsing\n  // might be prohibited to run. In these contexts, an error would be thrown during the messaging\n  // instantiating phase, informing the developers to import/call isSupported for special handling.\n  return (\n    isIndexedDBAvailable() &&\n    (await validateIndexedDBOpenable()) &&\n    'PushManager' in self &&\n    'Notification' in self &&\n    ServiceWorkerRegistration.prototype.hasOwnProperty('showNotification') &&\n    PushSubscription.prototype.hasOwnProperty('getKey')\n  );\n}\n", "/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { ERROR_FACTORY, ErrorCode } from '../util/errors';\n\nimport {\n  MessagePayload,\n  NextFn,\n  Observer,\n  Unsubscribe\n} from '../interfaces/public-types';\nimport { MessagingService } from '../messaging-service';\n\nexport function onBackgroundMessage(\n  messaging: MessagingService,\n  nextOrObserver: NextFn<MessagePayload> | Observer<MessagePayload>\n): Unsubscribe {\n  if (self.document !== undefined) {\n    throw ERROR_FACTORY.create(ErrorCode.AVAILABLE_IN_SW);\n  }\n\n  messaging.onBackgroundMessageHandler = nextOrObserver;\n\n  return () => {\n    messaging.onBackgroundMessageHandler = null;\n  };\n}\n", "/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { Messaging } from '../interfaces/public-types';\nimport { MessagingService } from '../messaging-service';\n\nexport function _setDeliveryMetricsExportedToBigQueryEnabled(\n  messaging: Messaging,\n  enable: boolean\n): void {\n  (messaging as MessagingService).deliveryMetricsExportedToBigQueryEnabled =\n    enable;\n}\n", "/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { ERROR_FACTORY, ErrorCode } from './util/errors';\nimport { FirebaseApp, _getProvider, getApp } from '@firebase/app';\nimport {\n  GetTokenOptions,\n  MessagePayload,\n  Messaging\n} from './interfaces/public-types';\nimport {\n  NextFn,\n  Observer,\n  Unsubscribe,\n  getModularInstance\n} from '@firebase/util';\nimport { isSwSupported, isWindowSupported } from './api/isSupported';\n\nimport { MessagingService } from './messaging-service';\nimport { deleteToken as _deleteToken } from './api/deleteToken';\nimport { getToken as _getToken } from './api/getToken';\nimport { onBackgroundMessage as _onBackgroundMessage } from './api/onBackgroundMessage';\nimport { onMessage as _onMessage } from './api/onMessage';\nimport { _setDeliveryMetricsExportedToBigQueryEnabled } from './api/setDeliveryMetricsExportedToBigQueryEnabled';\n\n/**\n * Retrieves a Firebase Cloud Messaging instance.\n *\n * @returns The Firebase Cloud Messaging instance associated with the provided firebase app.\n *\n * @public\n */\nexport function getMessagingInWindow(app: FirebaseApp = getApp()): Messaging {\n  // Conscious decision to make this async check non-blocking during the messaging instance\n  // initialization phase for performance consideration. An error would be thrown latter for\n  // developer's information. Developers can then choose to import and call `isSupported` for\n  // special handling.\n  isWindowSupported().then(\n    isSupported => {\n      // If `isWindowSupported()` resolved, but returned false.\n      if (!isSupported) {\n        throw ERROR_FACTORY.create(ErrorCode.UNSUPPORTED_BROWSER);\n      }\n    },\n    _ => {\n      // If `isWindowSupported()` rejected.\n      throw ERROR_FACTORY.create(ErrorCode.INDEXED_DB_UNSUPPORTED);\n    }\n  );\n  return _getProvider(getModularInstance(app), 'messaging').getImmediate();\n}\n\n/**\n * Retrieves a Firebase Cloud Messaging instance.\n *\n * @returns The Firebase Cloud Messaging instance associated with the provided firebase app.\n *\n * @public\n */\nexport function getMessagingInSw(app: FirebaseApp = getApp()): Messaging {\n  // Conscious decision to make this async check non-blocking during the messaging instance\n  // initialization phase for performance consideration. An error would be thrown latter for\n  // developer's information. Developers can then choose to import and call `isSupported` for\n  // special handling.\n  isSwSupported().then(\n    isSupported => {\n      // If `isSwSupported()` resolved, but returned false.\n      if (!isSupported) {\n        throw ERROR_FACTORY.create(ErrorCode.UNSUPPORTED_BROWSER);\n      }\n    },\n    _ => {\n      // If `isSwSupported()` rejected.\n      throw ERROR_FACTORY.create(ErrorCode.INDEXED_DB_UNSUPPORTED);\n    }\n  );\n  return _getProvider(getModularInstance(app), 'messaging-sw').getImmediate();\n}\n\n/**\n * Subscribes the {@link Messaging} instance to push notifications. Returns a Firebase Cloud\n * Messaging registration token that can be used to send push messages to that {@link Messaging}\n * instance.\n *\n * If notification permission isn't already granted, this method asks the user for permission. The\n * returned promise rejects if the user does not allow the app to show notifications.\n *\n * @param messaging - The {@link Messaging} instance.\n * @param options - Provides an optional vapid key and an optional service worker registration.\n *\n * @returns The promise resolves with an FCM registration token.\n *\n * @public\n */\nexport async function getToken(\n  messaging: Messaging,\n  options?: GetTokenOptions\n): Promise<string> {\n  messaging = getModularInstance(messaging);\n  return _getToken(messaging as MessagingService, options);\n}\n\n/**\n * Deletes the registration token associated with this {@link Messaging} instance and unsubscribes\n * the {@link Messaging} instance from the push subscription.\n *\n * @param messaging - The {@link Messaging} instance.\n *\n * @returns The promise resolves when the token has been successfully deleted.\n *\n * @public\n */\nexport function deleteToken(messaging: Messaging): Promise<boolean> {\n  messaging = getModularInstance(messaging);\n  return _deleteToken(messaging as MessagingService);\n}\n\n/**\n * When a push message is received and the user is currently on a page for your origin, the\n * message is passed to the page and an `onMessage()` event is dispatched with the payload of\n * the push message.\n *\n *\n * @param messaging - The {@link Messaging} instance.\n * @param nextOrObserver - This function, or observer object with `next` defined,\n *     is called when a message is received and the user is currently viewing your page.\n * @returns To stop listening for messages execute this returned function.\n *\n * @public\n */\nexport function onMessage(\n  messaging: Messaging,\n  nextOrObserver: NextFn<MessagePayload> | Observer<MessagePayload>\n): Unsubscribe {\n  messaging = getModularInstance(messaging);\n  return _onMessage(messaging as MessagingService, nextOrObserver);\n}\n\n/**\n * Called when a message is received while the app is in the background. An app is considered to be\n * in the background if no active window is displayed.\n *\n * @param messaging - The {@link Messaging} instance.\n * @param nextOrObserver - This function, or observer object with `next` defined, is called when a\n * message is received and the app is currently in the background.\n *\n * @returns To stop listening for messages execute this returned function\n *\n * @public\n */\nexport function onBackgroundMessage(\n  messaging: Messaging,\n  nextOrObserver: NextFn<MessagePayload> | Observer<MessagePayload>\n): Unsubscribe {\n  messaging = getModularInstance(messaging);\n  return _onBackgroundMessage(messaging as MessagingService, nextOrObserver);\n}\n\n/**\n * Enables or disables Firebase Cloud Messaging message delivery metrics export to BigQuery. By\n * default, message delivery metrics are not exported to BigQuery. Use this method to enable or\n * disable the export at runtime.\n *\n * @param messaging - The `FirebaseMessaging` instance.\n * @param enable - Whether Firebase Cloud Messaging should export message delivery metrics to\n * BigQuery.\n *\n * @public\n */\nexport function experimentalSetDeliveryMetricsExportedToBigQueryEnabled(\n  messaging: Messaging,\n  enable: boolean\n): void {\n  messaging = getModularInstance(messaging);\n  return _setDeliveryMetricsExportedToBigQueryEnabled(messaging, enable);\n}\n", "/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport '@firebase/installations';\n\nimport { Messaging } from './interfaces/public-types';\nimport { registerMessagingInSw } from './helpers/register';\n\nexport * from './interfaces/public-types';\nexport {\n  onBackgroundMessage,\n  getMessagingInSw as getMessaging,\n  experimentalSetDeliveryMetricsExportedToBigQueryEnabled\n} from './api';\nexport { isSwSupported as isSupported } from './api/isSupported';\n\ndeclare module '@firebase/component' {\n  interface NameServiceMapping {\n    'messaging-sw': Messaging;\n  }\n}\n\nregisterMessagingInSw();\n"], "names": ["MessageType", "onBackgroundMessage", "_onBackgroundMessage"], "mappings": ";;;;;;AAAA;;;;;;;;;;;;;;;AAeG;AAKI,MAAM,iBAAiB,GAC5B,yFAAyF,CAAC;AAErF,MAAM,QAAQ,GAAG,4CAA4C,CAAC;AAErE;AACO,MAAM,OAAO,GAAG,SAAS,CAAC;AAE1B,MAAM,mBAAmB,GAAG,iBAAiB,CAAC;AAerD;AACO,MAAM,gBAAgB,GAAG,CAAC,CAAC;AAC3B,MAAM,uBAAuB,GAAG,CAAC,CAAC;AAEzC,IAAYA,aAGX,CAAA;AAHD,CAAA,UAAY,WAAW,EAAA;AACrB,IAAA,WAAA,CAAA,WAAA,CAAA,cAAA,CAAA,GAAA,CAAA,CAAA,GAAA,cAAgB,CAAA;AAChB,IAAA,WAAA,CAAA,WAAA,CAAA,sBAAA,CAAA,GAAA,CAAA,CAAA,GAAA,sBAAwB,CAAA;AAC1B,CAAC,EAHWA,aAAW,KAAXA,aAAW,GAGtB,EAAA,CAAA,CAAA;;AClDD;;;;;;;;;;;;;AAaG;AA0CH,IAAY,WAGX,CAAA;AAHD,CAAA,UAAY,WAAW,EAAA;AACrB,IAAA,WAAA,CAAA,eAAA,CAAA,GAAA,eAA+B,CAAA;AAC/B,IAAA,WAAA,CAAA,sBAAA,CAAA,GAAA,sBAA6C,CAAA;AAC/C,CAAC,EAHW,WAAW,KAAX,WAAW,GAGtB,EAAA,CAAA,CAAA;;AC1DD;;;;;;;;;;;;;;;AAeG;AAEG,SAAU,aAAa,CAAC,KAA+B,EAAA;AAC3D,IAAA,MAAM,UAAU,GAAG,IAAI,UAAU,CAAC,KAAK,CAAC,CAAC;AACzC,IAAA,MAAM,YAAY,GAAG,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,GAAG,UAAU,CAAC,CAAC,CAAC;IAC9D,OAAO,YAAY,CAAC,OAAO,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC,OAAO,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC,OAAO,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC;AAChF,CAAC;AAEK,SAAU,aAAa,CAAC,YAAoB,EAAA;IAChD,MAAM,OAAO,GAAG,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,YAAY,CAAC,MAAM,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC;AAChE,IAAA,MAAM,MAAM,GAAG,CAAC,YAAY,GAAG,OAAO;AACnC,SAAA,OAAO,CAAC,KAAK,EAAE,GAAG,CAAC;AACnB,SAAA,OAAO,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC;AAEtB,IAAA,MAAM,OAAO,GAAG,IAAI,CAAC,MAAM,CAAC,CAAC;IAC7B,MAAM,WAAW,GAAG,IAAI,UAAU,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;AAEnD,IAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,OAAO,CAAC,MAAM,EAAE,EAAE,CAAC,EAAE;QACvC,WAAW,CAAC,CAAC,CAAC,GAAG,OAAO,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;AACxC,KAAA;AACD,IAAA,OAAO,WAAW,CAAC;AACrB;;ACpCA;;;;;;;;;;;;;;;AAeG;AA8CH,MAAM,WAAW,GAAG,sBAAsB,CAAC;AAC3C;;;AAGG;AACH,MAAM,cAAc,GAAG,CAAC,CAAC;AACzB,MAAM,qBAAqB,GAAG,wBAAwB,CAAC;AAEhD,eAAe,kBAAkB,CACtC,QAAgB,EAAA;IAEhB,IAAI,WAAW,IAAI,SAAS,EAAE;;;AAG5B,QAAA,MAAM,SAAS,GAAG,MAChB,SAGD,CAAC,SAAS,EAAE,CAAC;AACd,QAAA,MAAM,OAAO,GAAG,SAAS,CAAC,GAAG,CAAC,EAAE,IAAI,EAAE,CAAC,IAAI,CAAC,CAAC;AAE7C,QAAA,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,WAAW,CAAC,EAAE;;AAElC,YAAA,OAAO,IAAI,CAAC;AACb,SAAA;AACF,KAAA;IAED,IAAI,YAAY,GAAwB,IAAI,CAAC;IAE7C,MAAM,EAAE,GAAG,MAAM,MAAM,CAAC,WAAW,EAAE,cAAc,EAAE;QACnD,OAAO,EAAE,OAAO,EAAE,EAAE,UAAU,EAAE,UAAU,EAAE,kBAAkB,KAAI;;YAChE,IAAI,UAAU,GAAG,CAAC,EAAE;;gBAElB,OAAO;AACR,aAAA;YAED,IAAI,CAAC,EAAE,CAAC,gBAAgB,CAAC,QAAQ,CAAC,qBAAqB,CAAC,EAAE;;gBAExD,OAAO;AACR,aAAA;YAED,MAAM,WAAW,GAAG,kBAAkB,CAAC,WAAW,CAAC,qBAAqB,CAAC,CAAC;AAC1E,YAAA,MAAM,KAAK,GAAG,MAAM,WAAW,CAAC,KAAK,CAAC,aAAa,CAAC,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;AACnE,YAAA,MAAM,WAAW,CAAC,KAAK,EAAE,CAAC;YAE1B,IAAI,CAAC,KAAK,EAAE;;gBAEV,OAAO;AACR,aAAA;YAED,IAAI,UAAU,KAAK,CAAC,EAAE;gBACpB,MAAM,UAAU,GAAG,KAAuB,CAAC;AAE3C,gBAAA,IAAI,CAAC,UAAU,CAAC,IAAI,IAAI,CAAC,UAAU,CAAC,MAAM,IAAI,CAAC,UAAU,CAAC,QAAQ,EAAE;oBAClE,OAAO;AACR,iBAAA;AAED,gBAAA,YAAY,GAAG;oBACb,KAAK,EAAE,UAAU,CAAC,QAAQ;oBAC1B,UAAU,EAAE,MAAA,UAAU,CAAC,UAAU,MAAI,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,EAAA,GAAA,IAAI,CAAC,GAAG,EAAE;AAC/C,oBAAA,mBAAmB,EAAE;wBACnB,IAAI,EAAE,UAAU,CAAC,IAAI;wBACrB,MAAM,EAAE,UAAU,CAAC,MAAM;wBACzB,QAAQ,EAAE,UAAU,CAAC,QAAQ;wBAC7B,OAAO,EAAE,UAAU,CAAC,OAAO;AAC3B,wBAAA,QAAQ,EACN,OAAO,UAAU,CAAC,QAAQ,KAAK,QAAQ;8BACnC,UAAU,CAAC,QAAQ;AACrB,8BAAE,aAAa,CAAC,UAAU,CAAC,QAAQ,CAAC;AACzC,qBAAA;iBACF,CAAC;AACH,aAAA;iBAAM,IAAI,UAAU,KAAK,CAAC,EAAE;gBAC3B,MAAM,UAAU,GAAG,KAAuB,CAAC;AAE3C,gBAAA,YAAY,GAAG;oBACb,KAAK,EAAE,UAAU,CAAC,QAAQ;oBAC1B,UAAU,EAAE,UAAU,CAAC,UAAU;AACjC,oBAAA,mBAAmB,EAAE;AACnB,wBAAA,IAAI,EAAE,aAAa,CAAC,UAAU,CAAC,IAAI,CAAC;AACpC,wBAAA,MAAM,EAAE,aAAa,CAAC,UAAU,CAAC,MAAM,CAAC;wBACxC,QAAQ,EAAE,UAAU,CAAC,QAAQ;wBAC7B,OAAO,EAAE,UAAU,CAAC,OAAO;AAC3B,wBAAA,QAAQ,EAAE,aAAa,CAAC,UAAU,CAAC,QAAQ,CAAC;AAC7C,qBAAA;iBACF,CAAC;AACH,aAAA;iBAAM,IAAI,UAAU,KAAK,CAAC,EAAE;gBAC3B,MAAM,UAAU,GAAG,KAAuB,CAAC;AAE3C,gBAAA,YAAY,GAAG;oBACb,KAAK,EAAE,UAAU,CAAC,QAAQ;oBAC1B,UAAU,EAAE,UAAU,CAAC,UAAU;AACjC,oBAAA,mBAAmB,EAAE;AACnB,wBAAA,IAAI,EAAE,aAAa,CAAC,UAAU,CAAC,IAAI,CAAC;AACpC,wBAAA,MAAM,EAAE,aAAa,CAAC,UAAU,CAAC,MAAM,CAAC;wBACxC,QAAQ,EAAE,UAAU,CAAC,QAAQ;wBAC7B,OAAO,EAAE,UAAU,CAAC,OAAO;AAC3B,wBAAA,QAAQ,EAAE,aAAa,CAAC,UAAU,CAAC,QAAQ,CAAC;AAC7C,qBAAA;iBACF,CAAC;AACH,aAAA;SACF;AACF,KAAA,CAAC,CAAC;IACH,EAAE,CAAC,KAAK,EAAE,CAAC;;AAGX,IAAA,MAAM,QAAQ,CAAC,WAAW,CAAC,CAAC;AAC5B,IAAA,MAAM,QAAQ,CAAC,sBAAsB,CAAC,CAAC;AACvC,IAAA,MAAM,QAAQ,CAAC,WAAW,CAAC,CAAC;AAE5B,IAAA,OAAO,iBAAiB,CAAC,YAAY,CAAC,GAAG,YAAY,GAAG,IAAI,CAAC;AAC/D,CAAC;AAED,SAAS,iBAAiB,CACxB,YAAiC,EAAA;AAEjC,IAAA,IAAI,CAAC,YAAY,IAAI,CAAC,YAAY,CAAC,mBAAmB,EAAE;AACtD,QAAA,OAAO,KAAK,CAAC;AACd,KAAA;AACD,IAAA,MAAM,EAAE,mBAAmB,EAAE,GAAG,YAAY,CAAC;AAC7C,IAAA,QACE,OAAO,YAAY,CAAC,UAAU,KAAK,QAAQ;QAC3C,YAAY,CAAC,UAAU,GAAG,CAAC;AAC3B,QAAA,OAAO,YAAY,CAAC,KAAK,KAAK,QAAQ;AACtC,QAAA,YAAY,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC;AAC7B,QAAA,OAAO,mBAAmB,CAAC,IAAI,KAAK,QAAQ;AAC5C,QAAA,mBAAmB,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC;AACnC,QAAA,OAAO,mBAAmB,CAAC,MAAM,KAAK,QAAQ;AAC9C,QAAA,mBAAmB,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC;AACrC,QAAA,OAAO,mBAAmB,CAAC,QAAQ,KAAK,QAAQ;AAChD,QAAA,mBAAmB,CAAC,QAAQ,CAAC,MAAM,GAAG,CAAC;AACvC,QAAA,OAAO,mBAAmB,CAAC,OAAO,KAAK,QAAQ;AAC/C,QAAA,mBAAmB,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC;AACtC,QAAA,OAAO,mBAAmB,CAAC,QAAQ,KAAK,QAAQ;AAChD,QAAA,mBAAmB,CAAC,QAAQ,CAAC,MAAM,GAAG,CAAC,EACvC;AACJ;;ACpMA;;;;;;;;;;;;;;;AAeG;AAQH;AACO,MAAM,aAAa,GAAG,6BAA6B,CAAC;AAC3D,MAAM,gBAAgB,GAAG,CAAC,CAAC;AAC3B,MAAM,iBAAiB,GAAG,0BAA0B,CAAC;AASrD,IAAI,SAAS,GAA8C,IAAI,CAAC;AAChE,SAAS,YAAY,GAAA;IACnB,IAAI,CAAC,SAAS,EAAE;AACd,QAAA,SAAS,GAAG,MAAM,CAAC,aAAa,EAAE,gBAAgB,EAAE;AAClD,YAAA,OAAO,EAAE,CAAC,SAAS,EAAE,UAAU,KAAI;;;;;AAKjC,gBAAA,QAAQ,UAAU;AAChB,oBAAA,KAAK,CAAC;AACJ,wBAAA,SAAS,CAAC,iBAAiB,CAAC,iBAAiB,CAAC,CAAC;AAClD,iBAAA;aACF;AACF,SAAA,CAAC,CAAC;AACJ,KAAA;AACD,IAAA,OAAO,SAAS,CAAC;AACnB,CAAC;AAED;AACO,eAAe,KAAK,CACzB,oBAAkD,EAAA;AAElD,IAAA,MAAM,GAAG,GAAG,MAAM,CAAC,oBAAoB,CAAC,CAAC;AACzC,IAAA,MAAM,EAAE,GAAG,MAAM,YAAY,EAAE,CAAC;AAChC,IAAA,MAAM,YAAY,IAAI,MAAM,EAAE;SAC3B,WAAW,CAAC,iBAAiB,CAAC;SAC9B,WAAW,CAAC,iBAAiB,CAAC;AAC9B,SAAA,GAAG,CAAC,GAAG,CAAC,CAAiB,CAAC;AAE7B,IAAA,IAAI,YAAY,EAAE;AAChB,QAAA,OAAO,YAAY,CAAC;AACrB,KAAA;AAAM,SAAA;;QAEL,MAAM,eAAe,GAAG,MAAM,kBAAkB,CAC9C,oBAAoB,CAAC,SAAS,CAAC,QAAQ,CACxC,CAAC;AACF,QAAA,IAAI,eAAe,EAAE;AACnB,YAAA,MAAM,KAAK,CAAC,oBAAoB,EAAE,eAAe,CAAC,CAAC;AACnD,YAAA,OAAO,eAAe,CAAC;AACxB,SAAA;AACF,KAAA;AACH,CAAC;AAED;AACO,eAAe,KAAK,CACzB,oBAAkD,EAClD,YAA0B,EAAA;AAE1B,IAAA,MAAM,GAAG,GAAG,MAAM,CAAC,oBAAoB,CAAC,CAAC;AACzC,IAAA,MAAM,EAAE,GAAG,MAAM,YAAY,EAAE,CAAC;IAChC,MAAM,EAAE,GAAG,EAAE,CAAC,WAAW,CAAC,iBAAiB,EAAE,WAAW,CAAC,CAAC;AAC1D,IAAA,MAAM,EAAE,CAAC,WAAW,CAAC,iBAAiB,CAAC,CAAC,GAAG,CAAC,YAAY,EAAE,GAAG,CAAC,CAAC;IAC/D,MAAM,EAAE,CAAC,IAAI,CAAC;AACd,IAAA,OAAO,YAAY,CAAC;AACtB,CAAC;AAED;AACO,eAAe,QAAQ,CAC5B,oBAAkD,EAAA;AAElD,IAAA,MAAM,GAAG,GAAG,MAAM,CAAC,oBAAoB,CAAC,CAAC;AACzC,IAAA,MAAM,EAAE,GAAG,MAAM,YAAY,EAAE,CAAC;IAChC,MAAM,EAAE,GAAG,EAAE,CAAC,WAAW,CAAC,iBAAiB,EAAE,WAAW,CAAC,CAAC;IAC1D,MAAM,EAAE,CAAC,WAAW,CAAC,iBAAiB,CAAC,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;IACpD,MAAM,EAAE,CAAC,IAAI,CAAC;AAChB,CAAC;AAWD,SAAS,MAAM,CAAC,EAAE,SAAS,EAAgC,EAAA;IACzD,OAAO,SAAS,CAAC,KAAK,CAAC;AACzB;;AClHA;;;;;;;;;;;;;;;AAeG;AAyBI,MAAM,SAAS,GAAwB;AAC5C,IAAA,CAAA,2BAAA,6CACE,iDAAiD;AACnD,IAAA,CAAA,0BAAA,uCACE,+CAA+C;AACjD,IAAA,CAAA,sBAAA,mCACE,uDAAuD;AACzD,IAAA,CAAA,oBAAA,sCACE,oEAAoE;AACtE,IAAA,CAAA,oBAAA,sCACE,kEAAkE;AACpE,IAAA,CAAA,qBAAA,uCACE,0EAA0E;AAC5E,IAAA,CAAA,wBAAA,0CACE,kGAAkG;AACpG,IAAA,CAAA,oCAAA,+CACE,8EAA8E;AAChF,IAAA,CAAA,wBAAA,0CACE,oEAAoE;AACtE,IAAA,CAAA,0BAAA,4CACE,0DAA0D;AAC5D,IAAA,CAAA,0BAAA,4CACE,6CAA6C;QAC7C,6BAA6B;AAC/B,IAAA,CAAA,qBAAA,uCACE,mEAAmE;AACrE,IAAA,CAAA,uBAAA,yCACE,uDAAuD;AACzD,IAAA,CAAA,wBAAA,0CACE,oEAAoE;QACpE,yEAAyE;AAC3E,IAAA,CAAA,yBAAA,2CACE,sEAAsE;AACxE,IAAA,CAAA,oBAAA,sCACE,gEAAgE;AAClE,IAAA,CAAA,mBAAA,qCAA+B,wCAAwC;AACvE,IAAA,CAAA,+BAAA,iDACE,qEAAqE;QACrE,oEAAoE;CACvE,CAAC;AAYK,MAAM,aAAa,GAAG,IAAI,YAAY,CAC3C,WAAW,EACX,WAAW,EACX,SAAS,CACV;;AC/FD;;;;;;;;;;;;;;;AAeG;AAuBI,eAAe,eAAe,CACnC,oBAAkD,EAClD,mBAAwC,EAAA;AAExC,IAAA,MAAM,OAAO,GAAG,MAAM,UAAU,CAAC,oBAAoB,CAAC,CAAC;AACvD,IAAA,MAAM,IAAI,GAAG,OAAO,CAAC,mBAAmB,CAAC,CAAC;AAE1C,IAAA,MAAM,gBAAgB,GAAG;AACvB,QAAA,MAAM,EAAE,MAAM;QACd,OAAO;AACP,QAAA,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC;KAC3B,CAAC;AAEF,IAAA,IAAI,YAAyB,CAAC;IAC9B,IAAI;AACF,QAAA,MAAM,QAAQ,GAAG,MAAM,KAAK,CAC1B,WAAW,CAAC,oBAAoB,CAAC,SAAS,CAAC,EAC3C,gBAAgB,CACjB,CAAC;AACF,QAAA,YAAY,GAAG,MAAM,QAAQ,CAAC,IAAI,EAAE,CAAC;AACtC,KAAA;AAAC,IAAA,OAAO,GAAG,EAAE;QACZ,MAAM,aAAa,CAAC,MAAM,CAAmC,wBAAA,yCAAA;YAC3D,SAAS,EAAG,GAAa,KAAb,IAAA,IAAA,GAAG,uBAAH,GAAG,CAAY,QAAQ,EAAE;AACtC,SAAA,CAAC,CAAC;AACJ,KAAA;IAED,IAAI,YAAY,CAAC,KAAK,EAAE;AACtB,QAAA,MAAM,OAAO,GAAG,YAAY,CAAC,KAAK,CAAC,OAAO,CAAC;QAC3C,MAAM,aAAa,CAAC,MAAM,CAAmC,wBAAA,yCAAA;AAC3D,YAAA,SAAS,EAAE,OAAO;AACnB,SAAA,CAAC,CAAC;AACJ,KAAA;AAED,IAAA,IAAI,CAAC,YAAY,CAAC,KAAK,EAAE;AACvB,QAAA,MAAM,aAAa,CAAC,MAAM,CAAA,0BAAA,0CAAoC,CAAC;AAChE,KAAA;IAED,OAAO,YAAY,CAAC,KAAK,CAAC;AAC5B,CAAC;AAEM,eAAe,kBAAkB,CACtC,oBAAkD,EAClD,YAA0B,EAAA;AAE1B,IAAA,MAAM,OAAO,GAAG,MAAM,UAAU,CAAC,oBAAoB,CAAC,CAAC;IACvD,MAAM,IAAI,GAAG,OAAO,CAAC,YAAY,CAAC,mBAAoB,CAAC,CAAC;AAExD,IAAA,MAAM,aAAa,GAAG;AACpB,QAAA,MAAM,EAAE,OAAO;QACf,OAAO;AACP,QAAA,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC;KAC3B,CAAC;AAEF,IAAA,IAAI,YAAyB,CAAC;IAC9B,IAAI;QACF,MAAM,QAAQ,GAAG,MAAM,KAAK,CAC1B,CAAG,EAAA,WAAW,CAAC,oBAAoB,CAAC,SAAS,CAAC,CAAA,CAAA,EAAI,YAAY,CAAC,KAAK,EAAE,EACtE,aAAa,CACd,CAAC;AACF,QAAA,YAAY,GAAG,MAAM,QAAQ,CAAC,IAAI,EAAE,CAAC;AACtC,KAAA;AAAC,IAAA,OAAO,GAAG,EAAE;QACZ,MAAM,aAAa,CAAC,MAAM,CAAgC,qBAAA,sCAAA;YACxD,SAAS,EAAG,GAAa,KAAb,IAAA,IAAA,GAAG,uBAAH,GAAG,CAAY,QAAQ,EAAE;AACtC,SAAA,CAAC,CAAC;AACJ,KAAA;IAED,IAAI,YAAY,CAAC,KAAK,EAAE;AACtB,QAAA,MAAM,OAAO,GAAG,YAAY,CAAC,KAAK,CAAC,OAAO,CAAC;QAC3C,MAAM,aAAa,CAAC,MAAM,CAAgC,qBAAA,sCAAA;AACxD,YAAA,SAAS,EAAE,OAAO;AACnB,SAAA,CAAC,CAAC;AACJ,KAAA;AAED,IAAA,IAAI,CAAC,YAAY,CAAC,KAAK,EAAE;AACvB,QAAA,MAAM,aAAa,CAAC,MAAM,CAAA,uBAAA,uCAAiC,CAAC;AAC7D,KAAA;IAED,OAAO,YAAY,CAAC,KAAK,CAAC;AAC5B,CAAC;AAEM,eAAe,kBAAkB,CACtC,oBAAkD,EAClD,KAAa,EAAA;AAEb,IAAA,MAAM,OAAO,GAAG,MAAM,UAAU,CAAC,oBAAoB,CAAC,CAAC;AAEvD,IAAA,MAAM,kBAAkB,GAAG;AACzB,QAAA,MAAM,EAAE,QAAQ;QAChB,OAAO;KACR,CAAC;IAEF,IAAI;AACF,QAAA,MAAM,QAAQ,GAAG,MAAM,KAAK,CAC1B,CAAA,EAAG,WAAW,CAAC,oBAAoB,CAAC,SAAS,CAAC,CAAI,CAAA,EAAA,KAAK,EAAE,EACzD,kBAAkB,CACnB,CAAC;AACF,QAAA,MAAM,YAAY,GAAgB,MAAM,QAAQ,CAAC,IAAI,EAAE,CAAC;QACxD,IAAI,YAAY,CAAC,KAAK,EAAE;AACtB,YAAA,MAAM,OAAO,GAAG,YAAY,CAAC,KAAK,CAAC,OAAO,CAAC;YAC3C,MAAM,aAAa,CAAC,MAAM,CAAqC,0BAAA,2CAAA;AAC7D,gBAAA,SAAS,EAAE,OAAO;AACnB,aAAA,CAAC,CAAC;AACJ,SAAA;AACF,KAAA;AAAC,IAAA,OAAO,GAAG,EAAE;QACZ,MAAM,aAAa,CAAC,MAAM,CAAqC,0BAAA,2CAAA;YAC7D,SAAS,EAAG,GAAa,KAAb,IAAA,IAAA,GAAG,uBAAH,GAAG,CAAY,QAAQ,EAAE;AACtC,SAAA,CAAC,CAAC;AACJ,KAAA;AACH,CAAC;AAED,SAAS,WAAW,CAAC,EAAE,SAAS,EAAa,EAAA;AAC3C,IAAA,OAAO,CAAG,EAAA,QAAQ,CAAa,UAAA,EAAA,SAAU,gBAAgB,CAAC;AAC5D,CAAC;AAED,eAAe,UAAU,CAAC,EACxB,SAAS,EACT,aAAa,EACgB,EAAA;AAC7B,IAAA,MAAM,SAAS,GAAG,MAAM,aAAa,CAAC,QAAQ,EAAE,CAAC;IAEjD,OAAO,IAAI,OAAO,CAAC;AACjB,QAAA,cAAc,EAAE,kBAAkB;AAClC,QAAA,MAAM,EAAE,kBAAkB;QAC1B,gBAAgB,EAAE,SAAS,CAAC,MAAO;QACnC,oCAAoC,EAAE,CAAO,IAAA,EAAA,SAAS,CAAE,CAAA;AACzD,KAAA,CAAC,CAAC;AACL,CAAC;AAED,SAAS,OAAO,CAAC,EACf,MAAM,EACN,IAAI,EACJ,QAAQ,EACR,QAAQ,EACY,EAAA;AACpB,IAAA,MAAM,IAAI,GAAmB;AAC3B,QAAA,GAAG,EAAE;YACH,QAAQ;YACR,IAAI;YACJ,MAAM;AACP,SAAA;KACF,CAAC;IAEF,IAAI,QAAQ,KAAK,iBAAiB,EAAE;AAClC,QAAA,IAAI,CAAC,GAAG,CAAC,iBAAiB,GAAG,QAAQ,CAAC;AACvC,KAAA;AAED,IAAA,OAAO,IAAI,CAAC;AACd;;ACzLA;;;;;;;;;;;;;;;AAeG;AAiBH;AACA,MAAM,mBAAmB,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC;AAE7C,eAAe,gBAAgB,CACpC,SAA2B,EAAA;AAE3B,IAAA,MAAM,gBAAgB,GAAG,MAAM,mBAAmB,CAChD,SAAS,CAAC,cAAe,EACzB,SAAS,CAAC,QAAS,CACpB,CAAC;AAEF,IAAA,MAAM,mBAAmB,GAAwB;QAC/C,QAAQ,EAAE,SAAS,CAAC,QAAS;AAC7B,QAAA,OAAO,EAAE,SAAS,CAAC,cAAe,CAAC,KAAK;QACxC,QAAQ,EAAE,gBAAgB,CAAC,QAAQ;QACnC,IAAI,EAAE,aAAa,CAAC,gBAAgB,CAAC,MAAM,CAAC,MAAM,CAAE,CAAC;QACrD,MAAM,EAAE,aAAa,CAAC,gBAAgB,CAAC,MAAM,CAAC,QAAQ,CAAE,CAAC;KAC1D,CAAC;IAEF,MAAM,YAAY,GAAG,MAAM,KAAK,CAAC,SAAS,CAAC,oBAAoB,CAAC,CAAC;IACjE,IAAI,CAAC,YAAY,EAAE;;QAEjB,OAAO,WAAW,CAAC,SAAS,CAAC,oBAAoB,EAAE,mBAAmB,CAAC,CAAC;AACzE,KAAA;SAAM,IACL,CAAC,YAAY,CAAC,YAAY,CAAC,mBAAoB,EAAE,mBAAmB,CAAC,EACrE;;QAEA,IAAI;YACF,MAAM,kBAAkB,CACtB,SAAS,CAAC,oBAAqB,EAC/B,YAAY,CAAC,KAAK,CACnB,CAAC;AACH,SAAA;AAAC,QAAA,OAAO,CAAC,EAAE;;AAEV,YAAA,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;AACjB,SAAA;QAED,OAAO,WAAW,CAAC,SAAS,CAAC,oBAAqB,EAAE,mBAAmB,CAAC,CAAC;AAC1E,KAAA;SAAM,IAAI,IAAI,CAAC,GAAG,EAAE,IAAI,YAAY,CAAC,UAAU,GAAG,mBAAmB,EAAE;;QAEtE,OAAO,WAAW,CAAC,SAAS,EAAE;YAC5B,KAAK,EAAE,YAAY,CAAC,KAAK;AACzB,YAAA,UAAU,EAAE,IAAI,CAAC,GAAG,EAAE;YACtB,mBAAmB;AACpB,SAAA,CAAC,CAAC;AACJ,KAAA;AAAM,SAAA;;QAEL,OAAO,YAAY,CAAC,KAAK,CAAC;AAC3B,KAAA;AACH,CAAC;AAED;;;AAGG;AACI,eAAe,mBAAmB,CACvC,SAA2B,EAAA;IAE3B,MAAM,YAAY,GAAG,MAAM,KAAK,CAAC,SAAS,CAAC,oBAAoB,CAAC,CAAC;AACjE,IAAA,IAAI,YAAY,EAAE;QAChB,MAAM,kBAAkB,CACtB,SAAS,CAAC,oBAAoB,EAC9B,YAAY,CAAC,KAAK,CACnB,CAAC;AACF,QAAA,MAAM,QAAQ,CAAC,SAAS,CAAC,oBAAoB,CAAC,CAAC;AAChD,KAAA;;IAGD,MAAM,gBAAgB,GACpB,MAAM,SAAS,CAAC,cAAe,CAAC,WAAW,CAAC,eAAe,EAAE,CAAC;AAChE,IAAA,IAAI,gBAAgB,EAAE;AACpB,QAAA,OAAO,gBAAgB,CAAC,WAAW,EAAE,CAAC;AACvC,KAAA;;AAGD,IAAA,OAAO,IAAI,CAAC;AACd,CAAC;AAED,eAAe,WAAW,CACxB,SAA2B,EAC3B,YAA0B,EAAA;IAE1B,IAAI;QACF,MAAM,YAAY,GAAG,MAAM,kBAAkB,CAC3C,SAAS,CAAC,oBAAoB,EAC9B,YAAY,CACb,CAAC;AAEF,QAAA,MAAM,mBAAmB,GAAA,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,EAAA,EACpB,YAAY,CAAA,EAAA,EACf,KAAK,EAAE,YAAY,EACnB,UAAU,EAAE,IAAI,CAAC,GAAG,EAAE,GACvB,CAAC;QAEF,MAAM,KAAK,CAAC,SAAS,CAAC,oBAAoB,EAAE,mBAAmB,CAAC,CAAC;AACjE,QAAA,OAAO,YAAY,CAAC;AACrB,KAAA;AAAC,IAAA,OAAO,CAAC,EAAE;AACV,QAAA,MAAM,CAAC,CAAC;AACT,KAAA;AACH,CAAC;AAED,eAAe,WAAW,CACxB,oBAAkD,EAClD,mBAAwC,EAAA;IAExC,MAAM,KAAK,GAAG,MAAM,eAAe,CACjC,oBAAoB,EACpB,mBAAmB,CACpB,CAAC;AACF,IAAA,MAAM,YAAY,GAAiB;QACjC,KAAK;AACL,QAAA,UAAU,EAAE,IAAI,CAAC,GAAG,EAAE;QACtB,mBAAmB;KACpB,CAAC;AACF,IAAA,MAAM,KAAK,CAAC,oBAAoB,EAAE,YAAY,CAAC,CAAC;IAChD,OAAO,YAAY,CAAC,KAAK,CAAC;AAC5B,CAAC;AAED;;AAEG;AACH,eAAe,mBAAmB,CAChC,cAAyC,EACzC,QAAgB,EAAA;IAEhB,MAAM,YAAY,GAAG,MAAM,cAAc,CAAC,WAAW,CAAC,eAAe,EAAE,CAAC;AACxE,IAAA,IAAI,YAAY,EAAE;AAChB,QAAA,OAAO,YAAY,CAAC;AACrB,KAAA;AAED,IAAA,OAAO,cAAc,CAAC,WAAW,CAAC,SAAS,CAAC;AAC1C,QAAA,eAAe,EAAE,IAAI;;;AAGrB,QAAA,oBAAoB,EAAE,aAAa,CAAC,QAAQ,CAAC;AAC9C,KAAA,CAAC,CAAC;AACL,CAAC;AAED;;AAEG;AACH,SAAS,YAAY,CACnB,SAA8B,EAC9B,cAAmC,EAAA;IAEnC,MAAM,eAAe,GAAG,cAAc,CAAC,QAAQ,KAAK,SAAS,CAAC,QAAQ,CAAC;IACvE,MAAM,eAAe,GAAG,cAAc,CAAC,QAAQ,KAAK,SAAS,CAAC,QAAQ,CAAC;IACvE,MAAM,WAAW,GAAG,cAAc,CAAC,IAAI,KAAK,SAAS,CAAC,IAAI,CAAC;IAC3D,MAAM,aAAa,GAAG,cAAc,CAAC,MAAM,KAAK,SAAS,CAAC,MAAM,CAAC;AAEjE,IAAA,OAAO,eAAe,IAAI,eAAe,IAAI,WAAW,IAAI,aAAa,CAAC;AAC5E;;ACvLA;;;;;;;;;;;;;;;AAeG;AAKG,SAAU,kBAAkB,CAChC,eAAuC,EAAA;AAEvC,IAAA,MAAM,OAAO,GAAmB;QAC9B,IAAI,EAAE,eAAe,CAAC,IAAI;;QAE1B,WAAW,EAAE,eAAe,CAAC,YAAY;;QAEzC,SAAS,EAAE,eAAe,CAAC,YAAY;KACtB,CAAC;AAEpB,IAAA,4BAA4B,CAAC,OAAO,EAAE,eAAe,CAAC,CAAC;AACvD,IAAA,oBAAoB,CAAC,OAAO,EAAE,eAAe,CAAC,CAAC;AAC/C,IAAA,mBAAmB,CAAC,OAAO,EAAE,eAAe,CAAC,CAAC;AAE9C,IAAA,OAAO,OAAO,CAAC;AACjB,CAAC;AAED,SAAS,4BAA4B,CACnC,OAAuB,EACvB,sBAA8C,EAAA;AAE9C,IAAA,IAAI,CAAC,sBAAsB,CAAC,YAAY,EAAE;QACxC,OAAO;AACR,KAAA;AAED,IAAA,OAAO,CAAC,YAAY,GAAG,EAAE,CAAC;AAE1B,IAAA,MAAM,KAAK,GAAG,sBAAsB,CAAC,YAAa,CAAC,KAAK,CAAC;IACzD,IAAI,CAAC,CAAC,KAAK,EAAE;AACX,QAAA,OAAO,CAAC,YAAa,CAAC,KAAK,GAAG,KAAK,CAAC;AACrC,KAAA;AAED,IAAA,MAAM,IAAI,GAAG,sBAAsB,CAAC,YAAa,CAAC,IAAI,CAAC;IACvD,IAAI,CAAC,CAAC,IAAI,EAAE;AACV,QAAA,OAAO,CAAC,YAAa,CAAC,IAAI,GAAG,IAAI,CAAC;AACnC,KAAA;AAED,IAAA,MAAM,KAAK,GAAG,sBAAsB,CAAC,YAAa,CAAC,KAAK,CAAC;IACzD,IAAI,CAAC,CAAC,KAAK,EAAE;AACX,QAAA,OAAO,CAAC,YAAa,CAAC,KAAK,GAAG,KAAK,CAAC;AACrC,KAAA;AAED,IAAA,MAAM,IAAI,GAAG,sBAAsB,CAAC,YAAa,CAAC,IAAI,CAAC;IACvD,IAAI,CAAC,CAAC,IAAI,EAAE;AACV,QAAA,OAAO,CAAC,YAAa,CAAC,IAAI,GAAG,IAAI,CAAC;AACnC,KAAA;AACH,CAAC;AAED,SAAS,oBAAoB,CAC3B,OAAuB,EACvB,sBAA8C,EAAA;AAE9C,IAAA,IAAI,CAAC,sBAAsB,CAAC,IAAI,EAAE;QAChC,OAAO;AACR,KAAA;AAED,IAAA,OAAO,CAAC,IAAI,GAAG,sBAAsB,CAAC,IAAiC,CAAC;AAC1E,CAAC;AAED,SAAS,mBAAmB,CAC1B,OAAuB,EACvB,sBAA8C,EAAA;;;IAG9C,IACE,CAAC,sBAAsB,CAAC,UAAU;QAClC,EAAC,MAAA,sBAAsB,CAAC,YAAY,MAAE,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,YAAY,CAAA,EAClD;QACA,OAAO;AACR,KAAA;AAED,IAAA,OAAO,CAAC,UAAU,GAAG,EAAE,CAAC;AAExB,IAAA,MAAM,IAAI,GACR,CAAA,EAAA,GAAA,CAAA,EAAA,GAAA,sBAAsB,CAAC,UAAU,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAE,IAAI,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,EAAA,GACvC,MAAA,sBAAsB,CAAC,YAAY,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAE,YAAY,CAAC;IAEpD,IAAI,CAAC,CAAC,IAAI,EAAE;AACV,QAAA,OAAO,CAAC,UAAW,CAAC,IAAI,GAAG,IAAI,CAAC;AACjC,KAAA;;IAGD,MAAM,cAAc,GAAG,CAAA,EAAA,GAAA,sBAAsB,CAAC,UAAU,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAE,eAAe,CAAC;IAC1E,IAAI,CAAC,CAAC,cAAc,EAAE;AACpB,QAAA,OAAO,CAAC,UAAW,CAAC,cAAc,GAAG,cAAc,CAAC;AACrD,KAAA;AACH;;AC3GA;;;;;;;;;;;;;;;AAeG;AAKG,SAAU,gBAAgB,CAAC,IAAa,EAAA;;AAE5C,IAAA,OAAO,OAAO,IAAI,KAAK,QAAQ,IAAI,CAAC,CAAC,IAAI,IAAI,mBAAmB,IAAI,IAAI,CAAC;AAC3E;;ACvBA;;;;;;;;;;;;;;;AAeG;AAEH;AACM,SAAU,KAAK,CAAC,EAAU,EAAA;AAC9B,IAAA,OAAO,IAAI,OAAO,CAAO,OAAO,IAAG;AACjC,QAAA,UAAU,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC;AAC1B,KAAC,CAAC,CAAC;AACL;;ACtBA;;;;;;;;;;;;;;;AAeG;AAyBuB,aAAa,CACrC,sBAAsB,EACtB,qBAAqB,EACrB;AA8GK,eAAe,QAAQ,CAC5B,SAA2B,EAC3B,eAAuC,EAAA;AAEvC,IAAA,MAAM,QAAQ,GAAG,cAAc,CAC7B,eAAe,EACf,MAAM,SAAS,CAAC,oBAAoB,CAAC,aAAa,CAAC,KAAK,EAAE,CAC3D,CAAC;IAEF,wBAAwB,CAAC,SAAS,EAAE,QAAQ,EAAE,eAAe,CAAC,SAAS,CAAC,CAAC;AAC3E,CAAC;AAED,SAAS,cAAc,CACrB,eAAuC,EACvC,GAAW,EAAA;;IAEX,MAAM,QAAQ,GAAG,EAAc,CAAC;;;AAIhC,IAAA,IAAI,CAAC,CAAC,eAAe,CAAC,IAAI,EAAE;AAC1B,QAAA,QAAQ,CAAC,cAAc,GAAG,eAAe,CAAC,IAAI,CAAC;AAChD,KAAA;AAED,IAAA,IAAI,CAAC,CAAC,eAAe,CAAC,YAAY,EAAE;AAClC,QAAA,QAAQ,CAAC,UAAU,GAAG,eAAe,CAAC,YAAY,CAAC;AACpD,KAAA;AAED,IAAA,QAAQ,CAAC,WAAW,GAAG,GAAG,CAAC;AAE3B,IAAA,IAAI,CAAC,CAAC,eAAe,CAAC,YAAY,EAAE;QAClC,QAAQ,CAAC,YAAY,GAAGA,aAAW,CAAC,oBAAoB,CAAC,QAAQ,EAAE,CAAC;AACrE,KAAA;AAAM,SAAA;QACL,QAAQ,CAAC,YAAY,GAAGA,aAAW,CAAC,YAAY,CAAC,QAAQ,EAAE,CAAC;AAC7D,KAAA;AAED,IAAA,QAAQ,CAAC,YAAY,GAAG,gBAAgB,CAAC,QAAQ,EAAE,CAAC;AACpD,IAAA,QAAQ,CAAC,YAAY,GAAG,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,eAAe,EAAE,EAAE,CAAC,CAAC;AAEjE,IAAA,IAAI,CAAC,CAAC,eAAe,CAAC,YAAY,EAAE;AAClC,QAAA,QAAQ,CAAC,YAAY,GAAG,eAAe,CAAC,YAAY,CAAC;AACtD,KAAA;AAED,IAAA,QAAQ,CAAC,KAAK,GAAG,uBAAuB,CAAC,QAAQ,EAAE,CAAC;IAEpD,IAAI,CAAC,EAAC,CAAA,EAAA,GAAA,eAAe,CAAC,UAAU,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAE,eAAe,CAAA,EAAE;QACjD,QAAQ,CAAC,eAAe,GAAG,CAAA,EAAA,GAAA,eAAe,CAAC,UAAU,MAAE,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,eAAe,CAAC;AACxE,KAAA;;AAGD,IAAA,OAAO,QAAQ,CAAC;AAClB,CAAC;AAED,SAAS,wBAAwB,CAC/B,SAA2B,EAC3B,QAAkB,EAClB,SAAiB,EAAA;IAEjB,MAAM,QAAQ,GAAG,EAAc,CAAC;;AAGhC,IAAA,QAAQ,CAAC,aAAa,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC,CAAC,QAAQ,EAAE,CAAC;AAC3D,IAAA,QAAQ,CAAC,4BAA4B,GAAG,IAAI,CAAC,SAAS,CAAC;AACrD,QAAA,sBAAsB,EAAE,QAAQ;AACjC,KAAA,CAAC,CAAC;IAEH,IAAI,CAAC,CAAC,SAAS,EAAE;AACf,QAAA,QAAQ,CAAC,eAAe,GAAG,mBAAmB,CAAC,SAAS,CAAC,CAAC;AAC3D,KAAA;;AAGD,IAAA,SAAS,CAAC,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;AACrC,CAAC;AAED,SAAS,mBAAmB,CAAC,SAAiB,EAAA;AAC5C,IAAA,MAAM,cAAc,GAAmB;AACrC,QAAA,eAAe,EAAE;AACf,YAAA,QAAQ,EAAE;AACR,gBAAA,4BAA4B,EAAE,SAAS;AACxC,aAAA;AACF,SAAA;KACF,CAAC;AAEF,IAAA,OAAO,cAAc,CAAC;AACxB,CAAC;AAae,SAAA,aAAa,CAAC,EAAU,EAAE,EAAU,EAAA;IAClD,MAAM,WAAW,GAAG,EAAE,CAAC;AACvB,IAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;QAClC,WAAW,CAAC,IAAI,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;AAC/B,QAAA,IAAI,CAAC,GAAG,EAAE,CAAC,MAAM,EAAE;YACjB,WAAW,CAAC,IAAI,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;AAChC,SAAA;AACF,KAAA;AAED,IAAA,OAAO,WAAW,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;AAC9B;;ACpQA;;;;;;;;;;;;;;;AAeG;AAqCI,eAAe,WAAW,CAC/B,KAAkC,EAClC,SAA2B,EAAA;;AAE3B,IAAA,MAAM,EAAE,eAAe,EAAE,GAAG,KAAK,CAAC;IAClC,IAAI,CAAC,eAAe,EAAE;;AAEpB,QAAA,MAAM,mBAAmB,CAAC,SAAS,CAAC,CAAC;QACrC,OAAO;AACR,KAAA;IAED,MAAM,YAAY,GAAG,MAAM,KAAK,CAAC,SAAS,CAAC,oBAAoB,CAAC,CAAC;AACjE,IAAA,MAAM,mBAAmB,CAAC,SAAS,CAAC,CAAC;AAErC,IAAA,SAAS,CAAC,QAAQ;AAChB,QAAA,CAAA,EAAA,GAAA,CAAA,EAAA,GAAA,YAAY,KAAZ,IAAA,IAAA,YAAY,KAAZ,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,YAAY,CAAE,mBAAmB,MAAE,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,QAAQ,MAAI,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,EAAA,GAAA,iBAAiB,CAAC;AACnE,IAAA,MAAM,gBAAgB,CAAC,SAAS,CAAC,CAAC;AACpC,CAAC;AAEM,eAAe,MAAM,CAC1B,KAAgB,EAChB,SAA2B,EAAA;AAE3B,IAAA,MAAM,eAAe,GAAG,yBAAyB,CAAC,KAAK,CAAC,CAAC;IACzD,IAAI,CAAC,eAAe,EAAE;;QAEpB,OAAO;AACR,KAAA;;IAGD,IAAI,SAAS,CAAC,wCAAwC,EAAE;AACtD,QAAA,MAAM,QAAQ,CAAC,SAAS,EAAE,eAAe,CAAC,CAAC;AAC5C,KAAA;;AAGD,IAAA,MAAM,UAAU,GAAG,MAAM,aAAa,EAAE,CAAC;AACzC,IAAA,IAAI,iBAAiB,CAAC,UAAU,CAAC,EAAE;AACjC,QAAA,OAAO,mCAAmC,CAAC,UAAU,EAAE,eAAe,CAAC,CAAC;AACzE,KAAA;;AAGD,IAAA,IAAI,CAAC,CAAC,eAAe,CAAC,YAAY,EAAE;AAClC,QAAA,MAAM,gBAAgB,CAAC,mBAAmB,CAAC,eAAe,CAAC,CAAC,CAAC;AAC9D,KAAA;IAED,IAAI,CAAC,SAAS,EAAE;QACd,OAAO;AACR,KAAA;AAED,IAAA,IAAI,CAAC,CAAC,SAAS,CAAC,0BAA0B,EAAE;AAC1C,QAAA,MAAM,OAAO,GAAG,kBAAkB,CAAC,eAAe,CAAC,CAAC;AAEpD,QAAA,IAAI,OAAO,SAAS,CAAC,0BAA0B,KAAK,UAAU,EAAE;AAC9D,YAAA,MAAM,SAAS,CAAC,0BAA0B,CAAC,OAAO,CAAC,CAAC;AACrD,SAAA;AAAM,aAAA;AACL,YAAA,SAAS,CAAC,0BAA0B,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;AACpD,SAAA;AACF,KAAA;AACH,CAAC;AAEM,eAAe,mBAAmB,CACvC,KAAwB,EAAA;;AAExB,IAAA,MAAM,eAAe,GACnB,CAAA,EAAA,GAAA,CAAA,EAAA,GAAA,KAAK,CAAC,YAAY,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAE,IAAI,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAG,OAAO,CAAC,CAAC;IAEtC,IAAI,CAAC,eAAe,EAAE;QACpB,OAAO;AACR,KAAA;SAAM,IAAI,KAAK,CAAC,MAAM,EAAE;;;QAGvB,OAAO;AACR,KAAA;;IAGD,KAAK,CAAC,wBAAwB,EAAE,CAAC;AACjC,IAAA,KAAK,CAAC,YAAY,CAAC,KAAK,EAAE,CAAC;;AAG3B,IAAA,MAAM,IAAI,GAAG,OAAO,CAAC,eAAe,CAAC,CAAC;IACtC,IAAI,CAAC,IAAI,EAAE;QACT,OAAO;AACR,KAAA;;AAGD,IAAA,MAAM,GAAG,GAAG,IAAI,GAAG,CAAC,IAAI,EAAE,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;IAC9C,MAAM,SAAS,GAAG,IAAI,GAAG,CAAC,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;AAEhD,IAAA,IAAI,GAAG,CAAC,IAAI,KAAK,SAAS,CAAC,IAAI,EAAE;QAC/B,OAAO;AACR,KAAA;AAED,IAAA,IAAI,MAAM,GAAG,MAAM,eAAe,CAAC,GAAG,CAAC,CAAC;IAExC,IAAI,CAAC,MAAM,EAAE;QACX,MAAM,GAAG,MAAM,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;;;AAI7C,QAAA,MAAM,KAAK,CAAC,IAAI,CAAC,CAAC;AACnB,KAAA;AAAM,SAAA;AACL,QAAA,MAAM,GAAG,MAAM,MAAM,CAAC,KAAK,EAAE,CAAC;AAC/B,KAAA;IAED,IAAI,CAAC,MAAM,EAAE;;QAEX,OAAO;AACR,KAAA;AAED,IAAA,eAAe,CAAC,WAAW,GAAG,WAAW,CAAC,oBAAoB,CAAC;AAC/D,IAAA,eAAe,CAAC,mBAAmB,GAAG,IAAI,CAAC;AAC3C,IAAA,OAAO,MAAM,CAAC,WAAW,CAAC,eAAe,CAAC,CAAC;AAC7C,CAAC;AAED,SAAS,mBAAmB,CAC1B,eAAuC,EAAA;AAEvC,IAAA,MAAM,sBAAsB,GACtB,MAAA,CAAA,MAAA,CAAA,EAAA,EAAA,eAAe,CAAC,YAAuD,CAC5E,CAAC;;;;IAKF,sBAAsB,CAAC,IAAI,GAAG;QAC5B,CAAC,OAAO,GAAG,eAAe;KAC3B,CAAC;AAEF,IAAA,OAAO,sBAAsB,CAAC;AAChC,CAAC;AAED,SAAS,yBAAyB,CAAC,EACjC,IAAI,EACM,EAAA;IACV,IAAI,CAAC,IAAI,EAAE;AACT,QAAA,OAAO,IAAI,CAAC;AACb,KAAA;IAED,IAAI;AACF,QAAA,OAAO,IAAI,CAAC,IAAI,EAAE,CAAC;AACpB,KAAA;AAAC,IAAA,OAAO,GAAG,EAAE;;AAEZ,QAAA,OAAO,IAAI,CAAC;AACb,KAAA;AACH,CAAC;AAED;;;AAGG;AACH,eAAe,eAAe,CAAC,GAAQ,EAAA;AACrC,IAAA,MAAM,UAAU,GAAG,MAAM,aAAa,EAAE,CAAC;AAEzC,IAAA,KAAK,MAAM,MAAM,IAAI,UAAU,EAAE;AAC/B,QAAA,MAAM,SAAS,GAAG,IAAI,GAAG,CAAC,MAAM,CAAC,GAAG,EAAE,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;AAE1D,QAAA,IAAI,GAAG,CAAC,IAAI,KAAK,SAAS,CAAC,IAAI,EAAE;AAC/B,YAAA,OAAO,MAAM,CAAC;AACf,SAAA;AACF,KAAA;AAED,IAAA,OAAO,IAAI,CAAC;AACd,CAAC;AAED;;;AAGG;AACH,SAAS,iBAAiB,CAAC,UAA0B,EAAA;AACnD,IAAA,OAAO,UAAU,CAAC,IAAI,CACpB,MAAM,IACJ,MAAM,CAAC,eAAe,KAAK,SAAS;;;QAGpC,CAAC,MAAM,CAAC,GAAG,CAAC,UAAU,CAAC,qBAAqB,CAAC,CAChD,CAAC;AACJ,CAAC;AAED,SAAS,mCAAmC,CAC1C,UAA0B,EAC1B,eAAuC,EAAA;AAEvC,IAAA,eAAe,CAAC,mBAAmB,GAAG,IAAI,CAAC;AAC3C,IAAA,eAAe,CAAC,WAAW,GAAG,WAAW,CAAC,aAAa,CAAC;AAExD,IAAA,KAAK,MAAM,MAAM,IAAI,UAAU,EAAE;AAC/B,QAAA,MAAM,CAAC,WAAW,CAAC,eAAe,CAAC,CAAC;AACrC,KAAA;AACH,CAAC;AAED,SAAS,aAAa,GAAA;AACpB,IAAA,OAAO,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC;AAC3B,QAAA,IAAI,EAAE,QAAQ;AACd,QAAA,mBAAmB,EAAE,IAAI;;AAE1B,KAAA,CAA4B,CAAC;AAChC,CAAC;AAED,SAAS,gBAAgB,CACvB,2BAAwD,EAAA;;;;AAIxD,IAAA,MAAM,EAAE,OAAO,EAAE,GAAG,2BAA2B,CAAC;AAChD,IAAA,MAAM,EAAE,UAAU,EAAE,GAAG,YAAmD,CAAC;IAC3E,IAAI,OAAO,IAAI,UAAU,IAAI,OAAO,CAAC,MAAM,GAAG,UAAU,EAAE;AACxD,QAAA,OAAO,CAAC,IAAI,CACV,8BAA8B,UAAU,CAAA,sDAAA,CAAwD,CACjG,CAAC;AACH,KAAA;AAED,IAAA,OAAO,IAAI,CAAC,YAAY,CAAC,gBAAgB;iBAC1B,CAAA,EAAA,GAAA,2BAA2B,CAAC,KAAK,mCAAI,EAAE,EACpD,2BAA2B,CAC5B,CAAC;AACJ,CAAC;AAED,SAAS,OAAO,CAAC,OAA+B,EAAA;;;AAE9C,IAAA,MAAM,IAAI,GAAG,CAAA,EAAA,GAAA,CAAA,EAAA,GAAA,OAAO,CAAC,UAAU,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAE,IAAI,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,EAAA,GAAI,MAAA,OAAO,CAAC,YAAY,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAE,YAAY,CAAC;AAC5E,IAAA,IAAI,IAAI,EAAE;AACR,QAAA,OAAO,IAAI,CAAC;AACb,KAAA;AAED,IAAA,IAAI,gBAAgB,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE;;AAElC,QAAA,OAAO,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC;AAC7B,KAAA;AAAM,SAAA;AACL,QAAA,OAAO,IAAI,CAAC;AACb,KAAA;AACH;;AC1RA;;;;;;;;;;;;;;;AAeG;AAQG,SAAU,gBAAgB,CAAC,GAAgB,EAAA;AAC/C,IAAA,IAAI,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,OAAO,EAAE;AACxB,QAAA,MAAM,oBAAoB,CAAC,0BAA0B,CAAC,CAAC;AACxD,KAAA;AAED,IAAA,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE;AACb,QAAA,MAAM,oBAAoB,CAAC,UAAU,CAAC,CAAC;AACxC,KAAA;;AAGD,IAAA,MAAM,UAAU,GAAyC;QACvD,WAAW;QACX,QAAQ;QACR,OAAO;QACP,mBAAmB;KACpB,CAAC;AAEF,IAAA,MAAM,EAAE,OAAO,EAAE,GAAG,GAAG,CAAC;AACxB,IAAA,KAAK,MAAM,OAAO,IAAI,UAAU,EAAE;AAChC,QAAA,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE;AACrB,YAAA,MAAM,oBAAoB,CAAC,OAAO,CAAC,CAAC;AACrC,SAAA;AACF,KAAA;IAED,OAAO;QACL,OAAO,EAAE,GAAG,CAAC,IAAI;QACjB,SAAS,EAAE,OAAO,CAAC,SAAU;QAC7B,MAAM,EAAE,OAAO,CAAC,MAAO;QACvB,KAAK,EAAE,OAAO,CAAC,KAAM;QACrB,QAAQ,EAAE,OAAO,CAAC,iBAAkB;KACrC,CAAC;AACJ,CAAC;AAED,SAAS,oBAAoB,CAAC,SAAiB,EAAA;IAC7C,OAAO,aAAa,CAAC,MAAM,CAAsC,2BAAA,4CAAA;QAC/D,SAAS;AACV,KAAA,CAAC,CAAC;AACL;;AC5DA;;;;;;;;;;;;;;;AAeG;MAYU,gBAAgB,CAAA;AAoB3B,IAAA,WAAA,CACE,GAAgB,EAChB,aAA6C,EAC7C,iBAA0D,EAAA;;QAhB5D,IAAwC,CAAA,wCAAA,GAAY,KAAK,CAAC;QAE1D,IAA0B,CAAA,0BAAA,GAGf,IAAI,CAAC;QAEhB,IAAgB,CAAA,gBAAA,GACd,IAAI,CAAC;QAEP,IAAS,CAAA,SAAA,GAAe,EAAE,CAAC;QAC3B,IAAmB,CAAA,mBAAA,GAAY,KAAK,CAAC;AAOnC,QAAA,MAAM,SAAS,GAAG,gBAAgB,CAAC,GAAG,CAAC,CAAC;QAExC,IAAI,CAAC,oBAAoB,GAAG;YAC1B,GAAG;YACH,SAAS;YACT,aAAa;YACb,iBAAiB;SAClB,CAAC;KACH;IAED,OAAO,GAAA;AACL,QAAA,OAAO,OAAO,CAAC,OAAO,EAAE,CAAC;KAC1B;AACF;;ACjED;;;;;;;;;;;;;;;AAeG;AAuDH,MAAM,kBAAkB,GAAiC,CACvD,SAA6B,KAC3B;AACF,IAAA,MAAM,SAAS,GAAG,IAAI,gBAAgB,CACpC,SAAS,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC,YAAY,EAAE,EAC3C,SAAS,CAAC,WAAW,CAAC,wBAAwB,CAAC,CAAC,YAAY,EAAE,EAC9D,SAAS,CAAC,WAAW,CAAC,oBAAoB,CAAC,CAC5C,CAAC;AAEF,IAAA,IAAI,CAAC,gBAAgB,CAAC,MAAM,EAAE,CAAC,IAAG;QAChC,CAAC,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC,EAAE,SAA6B,CAAC,CAAC,CAAC;AACxD,KAAC,CAAC,CAAC;AACH,IAAA,IAAI,CAAC,gBAAgB,CAAC,wBAAwB,EAAE,CAAC,IAAG;QAClD,CAAC,CAAC,SAAS,CAAC,WAAW,CAAC,CAAC,EAAE,SAA6B,CAAC,CAAC,CAAC;AAC7D,KAAC,CAAC,CAAC;AACH,IAAA,IAAI,CAAC,gBAAgB,CAAC,mBAAmB,EAAE,CAAC,IAAG;QAC7C,CAAC,CAAC,SAAS,CAAC,mBAAmB,CAAC,CAAC,CAAC,CAAC,CAAC;AACtC,KAAC,CAAC,CAAC;AAEH,IAAA,OAAO,SAAS,CAAC;AACnB,CAAC,CAAC;AAoBF;;;;AAIG;SACa,qBAAqB,GAAA;IACnC,kBAAkB,CAChB,IAAI,SAAS,CAAC,cAAc,EAAE,kBAAkB,EAAuB,QAAA,4BAAA,CACxE,CAAC;AACJ;;ACvHA;;;;;;;;;;;;;;;AAeG;AAsCH;;;;;AAKG;AACI,eAAe,aAAa,GAAA;;;;IAIjC,QACE,oBAAoB,EAAE;SACrB,MAAM,yBAAyB,EAAE,CAAC;AACnC,QAAA,aAAa,IAAI,IAAI;AACrB,QAAA,cAAc,IAAI,IAAI;AACtB,QAAA,yBAAyB,CAAC,SAAS,CAAC,cAAc,CAAC,kBAAkB,CAAC;QACtE,gBAAgB,CAAC,SAAS,CAAC,cAAc,CAAC,QAAQ,CAAC,EACnD;AACJ;;ACvEA;;;;;;;;;;;;;;;AAeG;AAYa,SAAAC,qBAAmB,CACjC,SAA2B,EAC3B,cAAiE,EAAA;AAEjE,IAAA,IAAI,IAAI,CAAC,QAAQ,KAAK,SAAS,EAAE;AAC/B,QAAA,MAAM,aAAa,CAAC,MAAM,CAAA,sBAAA,iCAA2B,CAAC;AACvD,KAAA;AAED,IAAA,SAAS,CAAC,0BAA0B,GAAG,cAAc,CAAC;AAEtD,IAAA,OAAO,MAAK;AACV,QAAA,SAAS,CAAC,0BAA0B,GAAG,IAAI,CAAC;AAC9C,KAAC,CAAC;AACJ;;ACxCA;;;;;;;;;;;;;;;AAeG;AAKa,SAAA,4CAA4C,CAC1D,SAAoB,EACpB,MAAe,EAAA;AAEd,IAAA,SAA8B,CAAC,wCAAwC;AACtE,QAAA,MAAM,CAAC;AACX;;AC1BA;;;;;;;;;;;;;;;AAeG;AAmDH;;;;;;AAMG;AACa,SAAA,gBAAgB,CAAC,GAAA,GAAmB,MAAM,EAAE,EAAA;;;;;AAK1D,IAAA,aAAa,EAAE,CAAC,IAAI,CAClB,WAAW,IAAG;;QAEZ,IAAI,CAAC,WAAW,EAAE;AAChB,YAAA,MAAM,aAAa,CAAC,MAAM,CAAA,qBAAA,qCAA+B,CAAC;AAC3D,SAAA;KACF,EACD,CAAC,IAAG;;AAEF,QAAA,MAAM,aAAa,CAAC,MAAM,CAAA,wBAAA,wCAAkC,CAAC;AAC/D,KAAC,CACF,CAAC;AACF,IAAA,OAAO,YAAY,CAAC,kBAAkB,CAAC,GAAG,CAAC,EAAE,cAAc,CAAC,CAAC,YAAY,EAAE,CAAC;AAC9E,CAAC;AA6DD;;;;;;;;;;;AAWG;AACa,SAAA,mBAAmB,CACjC,SAAoB,EACpB,cAAiE,EAAA;AAEjE,IAAA,SAAS,GAAG,kBAAkB,CAAC,SAAS,CAAC,CAAC;AAC1C,IAAA,OAAOC,qBAAoB,CAAC,SAA6B,EAAE,cAAc,CAAC,CAAC;AAC7E,CAAC;AAED;;;;;;;;;;AAUG;AACa,SAAA,uDAAuD,CACrE,SAAoB,EACpB,MAAe,EAAA;AAEf,IAAA,SAAS,GAAG,kBAAkB,CAAC,SAAS,CAAC,CAAC;AAC1C,IAAA,OAAO,4CAA4C,CAAC,SAAS,EAAE,MAAM,CAAC,CAAC;AACzE;;AC7LA;;;;;;;;;;;;;;;AAeG;AAqBH,qBAAqB,EAAE;;;;"}