// Note: In a real environment, you would install ajv: npm install ajv
// import Ajv from 'ajv'; // This line would work after installation
// import addFormats from 'ajv-formats'; // For formats like date-time, url

import { extensionSchema } from './schema/extensionSchema';

// --- Simulated AJV for environments where it's not installed ---
// This is a very basic mock to allow the code to run without AJV.
// In a real setup, the actual AJV library would be used.
const mockAjv = () => {
  let currentSchema = null;
  return {
    compile: (schema) => {
      currentSchema = schema; // "Store" the schema
      // Return a validation function
      return (data) => {
        // Super basic validation: check for required top-level fields
        if (!currentSchema || typeof data !== 'object' || data === null) {
          return false; // Not an object
        }
        const errors = [];
        for (const key of currentSchema.required || []) {
          if (data[key] === undefined) {
            errors.push({
              instancePath: `/${key}`,
              schemaPath: `#/required`,
              keyword: 'required',
              params: { missingProperty: key },
              message: `must have required property '${key}'`,
            });
          }
        }
        // Basic type check for name and version as an example
        if (data.name && typeof data.name !== 'string') {
            errors.push({ message: "property 'name' must be a string" });
        }
        if (data.version && typeof data.version !== 'string') {
            errors.push({ message: "property 'version' must be a string" });
        }

        if (errors.length > 0) {
          // Populate the errors property on the function object, like AJV does
          this.errors = errors;
          return false;
        }
        this.errors = null;
        return true;
      };
    },
    // Mock addFormat, doesn't do anything in this simulation
    addFormat: (name, definition) => {
        console.warn(`AJV Mock: addFormat for '${name}' called, but not implemented in mock.`);
    }
  };
};

// Use actual Ajv if available (e.g., in a local dev environment after npm install)
// For the sandbox, it will use the mock.
let Ajv;
let addFormats;
try {
  // Attempt to dynamically import Ajv if it exists (won't work in this sandbox directly)
  // This is more for illustrating how one might try to use an optional real library
  // const AjvModule = await import('ajv'); // Top-level await is tricky
  // Ajv = AjvModule.default;
  // const addFormatsModule = await import('ajv-formats');
  // addFormats = addFormatsModule.default;
  // For now, assume it's not available in the sandbox and use the mock.
  if (typeof window !== 'undefined' && window.Ajv) { // A way to check if Ajv was loaded globally
    Ajv = window.Ajv;
    addFormats = window.addFormats; // if ajv-formats was also loaded globally
  } else {
    throw new Error("AJV not found, using mock.");
  }
} catch (e) {
  console.warn(
    "AJV library not found. Using a basic mock for schema validation. " +
    "Functionality will be limited. Please install 'ajv' and 'ajv-formats' for full schema validation."
  );
  const ajvInstance = mockAjv();
  Ajv = function() { return ajvInstance; }; // Make Ajv a constructor that returns the mock
  addFormats = function(ajv) { ajv.addFormat() }; // Mock addFormats call
}
// --- End Simulated AJV ---


const ajvInstance = new Ajv({ allErrors: true, useDefaults: true });
addFormats(ajvInstance); // Add formats like date-time, email, url, etc.

// Pre-compile the schema for efficiency
let validateFunction;
try {
    validateFunction = ajvInstance.compile(extensionSchema);
} catch (e) {
    console.error("Error compiling extension schema with AJV:", e);
    // Fallback to a dummy validator if compilation fails
    validateFunction = (data) => {
        console.warn("Schema compilation failed. Validation is not active.");
        validateFunction.errors = [{ message: "Schema compilation failed." }];
        return false;
    };
}


/**
 * Validates the given extension data object against the predefined schema.
 * @param {object} extensionData - The extension data to validate.
 * @returns {boolean} - True if valid, false otherwise.
 *                      Access errors via `validate.errors` property on this function
 *                      if validation fails (mimicking AJV's behavior).
 */
export function validateExtensionData(extensionData) {
  if (!validateFunction) {
      validateExtensionData.errors = [{ message: "Validator not initialized." }];
      return false;
  }
  const isValid = validateFunction(extensionData);
  if (!isValid) {
    validateExtensionData.errors = validateFunction.errors;
  } else {
    validateExtensionData.errors = null;
  }
  return isValid;
}

/**
 * Parses a JSON string and then validates it.
 * @param {string} jsonString - The JSON string to parse and validate.
 * @returns {{ data: object|null, isValid: boolean, errors: Array|null, parseError: string|null }}
 */
export function parseAndValidateExtensionJSON(jsonString) {
  let data;
  let parseError = null;
  try {
    data = JSON.parse(jsonString);
  } catch (e) {
    parseError = e.message;
    return { data: null, isValid: false, errors: null, parseError };
  }

  const isValid = validateExtensionData(data);
  return {
    data,
    isValid,
    errors: validateExtensionData.errors,
    parseError: null,
  };
}
