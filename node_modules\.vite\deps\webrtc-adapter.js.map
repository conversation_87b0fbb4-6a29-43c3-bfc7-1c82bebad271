{"version": 3, "sources": ["../../sdp/sdp.js", "../../webrtc-adapter/src/js/utils.js", "../../webrtc-adapter/src/js/chrome/chrome_shim.js", "../../webrtc-adapter/src/js/chrome/getusermedia.js", "../../webrtc-adapter/src/js/chrome/getdisplaymedia.js", "../../webrtc-adapter/src/js/firefox/firefox_shim.js", "../../webrtc-adapter/src/js/firefox/getusermedia.js", "../../webrtc-adapter/src/js/firefox/getdisplaymedia.js", "../../webrtc-adapter/src/js/safari/safari_shim.js", "../../webrtc-adapter/src/js/common_shim.js", "../../webrtc-adapter/src/js/adapter_factory.js", "../../webrtc-adapter/src/js/adapter_core.js"], "sourcesContent": ["/* eslint-env node */\r\n'use strict';\r\n\r\n// SDP helpers.\r\nconst SDPUtils = {};\r\n\r\n// Generate an alphanumeric identifier for cname or mids.\r\n// TODO: use UUIDs instead? https://gist.github.com/jed/982883\r\nSDPUtils.generateIdentifier = function() {\r\n  return Math.random().toString(36).substring(2, 12);\r\n};\r\n\r\n// The RTCP CNAME used by all peerconnections from the same JS.\r\nSDPUtils.localCName = SDPUtils.generateIdentifier();\r\n\r\n// Splits SDP into lines, dealing with both CRLF and LF.\r\nSDPUtils.splitLines = function(blob) {\r\n  return blob.trim().split('\\n').map(line => line.trim());\r\n};\r\n// Splits SDP into sessionpart and mediasections. Ensures CRLF.\r\nSDPUtils.splitSections = function(blob) {\r\n  const parts = blob.split('\\nm=');\r\n  return parts.map((part, index) => (index > 0 ?\r\n    'm=' + part : part).trim() + '\\r\\n');\r\n};\r\n\r\n// Returns the session description.\r\nSDPUtils.getDescription = function(blob) {\r\n  const sections = SDPUtils.splitSections(blob);\r\n  return sections && sections[0];\r\n};\r\n\r\n// Returns the individual media sections.\r\nSDPUtils.getMediaSections = function(blob) {\r\n  const sections = SDPUtils.splitSections(blob);\r\n  sections.shift();\r\n  return sections;\r\n};\r\n\r\n// Returns lines that start with a certain prefix.\r\nSDPUtils.matchPrefix = function(blob, prefix) {\r\n  return SDPUtils.splitLines(blob).filter(line => line.indexOf(prefix) === 0);\r\n};\r\n\r\n// Parses an ICE candidate line. Sample input:\r\n// candidate:702786350 2 udp 41819902 ******* 60769 typ relay raddr *******\r\n// rport 55996\"\r\n// Input can be prefixed with a=.\r\nSDPUtils.parseCandidate = function(line) {\r\n  let parts;\r\n  // Parse both variants.\r\n  if (line.indexOf('a=candidate:') === 0) {\r\n    parts = line.substring(12).split(' ');\r\n  } else {\r\n    parts = line.substring(10).split(' ');\r\n  }\r\n\r\n  const candidate = {\r\n    foundation: parts[0],\r\n    component: {1: 'rtp', 2: 'rtcp'}[parts[1]] || parts[1],\r\n    protocol: parts[2].toLowerCase(),\r\n    priority: parseInt(parts[3], 10),\r\n    ip: parts[4],\r\n    address: parts[4], // address is an alias for ip.\r\n    port: parseInt(parts[5], 10),\r\n    // skip parts[6] == 'typ'\r\n    type: parts[7],\r\n  };\r\n\r\n  for (let i = 8; i < parts.length; i += 2) {\r\n    switch (parts[i]) {\r\n      case 'raddr':\r\n        candidate.relatedAddress = parts[i + 1];\r\n        break;\r\n      case 'rport':\r\n        candidate.relatedPort = parseInt(parts[i + 1], 10);\r\n        break;\r\n      case 'tcptype':\r\n        candidate.tcpType = parts[i + 1];\r\n        break;\r\n      case 'ufrag':\r\n        candidate.ufrag = parts[i + 1]; // for backward compatibility.\r\n        candidate.usernameFragment = parts[i + 1];\r\n        break;\r\n      default: // extension handling, in particular ufrag. Don't overwrite.\r\n        if (candidate[parts[i]] === undefined) {\r\n          candidate[parts[i]] = parts[i + 1];\r\n        }\r\n        break;\r\n    }\r\n  }\r\n  return candidate;\r\n};\r\n\r\n// Translates a candidate object into SDP candidate attribute.\r\n// This does not include the a= prefix!\r\nSDPUtils.writeCandidate = function(candidate) {\r\n  const sdp = [];\r\n  sdp.push(candidate.foundation);\r\n\r\n  const component = candidate.component;\r\n  if (component === 'rtp') {\r\n    sdp.push(1);\r\n  } else if (component === 'rtcp') {\r\n    sdp.push(2);\r\n  } else {\r\n    sdp.push(component);\r\n  }\r\n  sdp.push(candidate.protocol.toUpperCase());\r\n  sdp.push(candidate.priority);\r\n  sdp.push(candidate.address || candidate.ip);\r\n  sdp.push(candidate.port);\r\n\r\n  const type = candidate.type;\r\n  sdp.push('typ');\r\n  sdp.push(type);\r\n  if (type !== 'host' && candidate.relatedAddress &&\r\n      candidate.relatedPort) {\r\n    sdp.push('raddr');\r\n    sdp.push(candidate.relatedAddress);\r\n    sdp.push('rport');\r\n    sdp.push(candidate.relatedPort);\r\n  }\r\n  if (candidate.tcpType && candidate.protocol.toLowerCase() === 'tcp') {\r\n    sdp.push('tcptype');\r\n    sdp.push(candidate.tcpType);\r\n  }\r\n  if (candidate.usernameFragment || candidate.ufrag) {\r\n    sdp.push('ufrag');\r\n    sdp.push(candidate.usernameFragment || candidate.ufrag);\r\n  }\r\n  return 'candidate:' + sdp.join(' ');\r\n};\r\n\r\n// Parses an ice-options line, returns an array of option tags.\r\n// Sample input:\r\n// a=ice-options:foo bar\r\nSDPUtils.parseIceOptions = function(line) {\r\n  return line.substring(14).split(' ');\r\n};\r\n\r\n// Parses a rtpmap line, returns RTCRtpCoddecParameters. Sample input:\r\n// a=rtpmap:111 opus/48000/2\r\nSDPUtils.parseRtpMap = function(line) {\r\n  let parts = line.substring(9).split(' ');\r\n  const parsed = {\r\n    payloadType: parseInt(parts.shift(), 10), // was: id\r\n  };\r\n\r\n  parts = parts[0].split('/');\r\n\r\n  parsed.name = parts[0];\r\n  parsed.clockRate = parseInt(parts[1], 10); // was: clockrate\r\n  parsed.channels = parts.length === 3 ? parseInt(parts[2], 10) : 1;\r\n  // legacy alias, got renamed back to channels in ORTC.\r\n  parsed.numChannels = parsed.channels;\r\n  return parsed;\r\n};\r\n\r\n// Generates a rtpmap line from RTCRtpCodecCapability or\r\n// RTCRtpCodecParameters.\r\nSDPUtils.writeRtpMap = function(codec) {\r\n  let pt = codec.payloadType;\r\n  if (codec.preferredPayloadType !== undefined) {\r\n    pt = codec.preferredPayloadType;\r\n  }\r\n  const channels = codec.channels || codec.numChannels || 1;\r\n  return 'a=rtpmap:' + pt + ' ' + codec.name + '/' + codec.clockRate +\r\n      (channels !== 1 ? '/' + channels : '') + '\\r\\n';\r\n};\r\n\r\n// Parses a extmap line (headerextension from RFC 5285). Sample input:\r\n// a=extmap:2 urn:ietf:params:rtp-hdrext:toffset\r\n// a=extmap:2/sendonly urn:ietf:params:rtp-hdrext:toffset\r\nSDPUtils.parseExtmap = function(line) {\r\n  const parts = line.substring(9).split(' ');\r\n  return {\r\n    id: parseInt(parts[0], 10),\r\n    direction: parts[0].indexOf('/') > 0 ? parts[0].split('/')[1] : 'sendrecv',\r\n    uri: parts[1],\r\n    attributes: parts.slice(2).join(' '),\r\n  };\r\n};\r\n\r\n// Generates an extmap line from RTCRtpHeaderExtensionParameters or\r\n// RTCRtpHeaderExtension.\r\nSDPUtils.writeExtmap = function(headerExtension) {\r\n  return 'a=extmap:' + (headerExtension.id || headerExtension.preferredId) +\r\n      (headerExtension.direction && headerExtension.direction !== 'sendrecv'\r\n        ? '/' + headerExtension.direction\r\n        : '') +\r\n      ' ' + headerExtension.uri +\r\n      (headerExtension.attributes ? ' ' + headerExtension.attributes : '') +\r\n      '\\r\\n';\r\n};\r\n\r\n// Parses a fmtp line, returns dictionary. Sample input:\r\n// a=fmtp:96 vbr=on;cng=on\r\n// Also deals with vbr=on; cng=on\r\n// Non-key-value such as telephone-events `0-15` get parsed as\r\n// {`0-15`:undefined}\r\nSDPUtils.parseFmtp = function(line) {\r\n  const parsed = {};\r\n  let kv;\r\n  const parts = line.substring(line.indexOf(' ') + 1).split(';');\r\n  for (let j = 0; j < parts.length; j++) {\r\n    kv = parts[j].trim().split('=');\r\n    parsed[kv[0].trim()] = kv[1];\r\n  }\r\n  return parsed;\r\n};\r\n\r\n// Generates a fmtp line from RTCRtpCodecCapability or RTCRtpCodecParameters.\r\nSDPUtils.writeFmtp = function(codec) {\r\n  let line = '';\r\n  let pt = codec.payloadType;\r\n  if (codec.preferredPayloadType !== undefined) {\r\n    pt = codec.preferredPayloadType;\r\n  }\r\n  if (codec.parameters && Object.keys(codec.parameters).length) {\r\n    const params = [];\r\n    Object.keys(codec.parameters).forEach(param => {\r\n      if (codec.parameters[param] !== undefined) {\r\n        params.push(param + '=' + codec.parameters[param]);\r\n      } else {\r\n        params.push(param);\r\n      }\r\n    });\r\n    line += 'a=fmtp:' + pt + ' ' + params.join(';') + '\\r\\n';\r\n  }\r\n  return line;\r\n};\r\n\r\n// Parses a rtcp-fb line, returns RTCPRtcpFeedback object. Sample input:\r\n// a=rtcp-fb:98 nack rpsi\r\nSDPUtils.parseRtcpFb = function(line) {\r\n  const parts = line.substring(line.indexOf(' ') + 1).split(' ');\r\n  return {\r\n    type: parts.shift(),\r\n    parameter: parts.join(' '),\r\n  };\r\n};\r\n\r\n// Generate a=rtcp-fb lines from RTCRtpCodecCapability or RTCRtpCodecParameters.\r\nSDPUtils.writeRtcpFb = function(codec) {\r\n  let lines = '';\r\n  let pt = codec.payloadType;\r\n  if (codec.preferredPayloadType !== undefined) {\r\n    pt = codec.preferredPayloadType;\r\n  }\r\n  if (codec.rtcpFeedback && codec.rtcpFeedback.length) {\r\n    // FIXME: special handling for trr-int?\r\n    codec.rtcpFeedback.forEach(fb => {\r\n      lines += 'a=rtcp-fb:' + pt + ' ' + fb.type +\r\n      (fb.parameter && fb.parameter.length ? ' ' + fb.parameter : '') +\r\n          '\\r\\n';\r\n    });\r\n  }\r\n  return lines;\r\n};\r\n\r\n// Parses a RFC 5576 ssrc media attribute. Sample input:\r\n// a=ssrc:3735928559 cname:something\r\nSDPUtils.parseSsrcMedia = function(line) {\r\n  const sp = line.indexOf(' ');\r\n  const parts = {\r\n    ssrc: parseInt(line.substring(7, sp), 10),\r\n  };\r\n  const colon = line.indexOf(':', sp);\r\n  if (colon > -1) {\r\n    parts.attribute = line.substring(sp + 1, colon);\r\n    parts.value = line.substring(colon + 1);\r\n  } else {\r\n    parts.attribute = line.substring(sp + 1);\r\n  }\r\n  return parts;\r\n};\r\n\r\n// Parse a ssrc-group line (see RFC 5576). Sample input:\r\n// a=ssrc-group:semantics 12 34\r\nSDPUtils.parseSsrcGroup = function(line) {\r\n  const parts = line.substring(13).split(' ');\r\n  return {\r\n    semantics: parts.shift(),\r\n    ssrcs: parts.map(ssrc => parseInt(ssrc, 10)),\r\n  };\r\n};\r\n\r\n// Extracts the MID (RFC 5888) from a media section.\r\n// Returns the MID or undefined if no mid line was found.\r\nSDPUtils.getMid = function(mediaSection) {\r\n  const mid = SDPUtils.matchPrefix(mediaSection, 'a=mid:')[0];\r\n  if (mid) {\r\n    return mid.substring(6);\r\n  }\r\n};\r\n\r\n// Parses a fingerprint line for DTLS-SRTP.\r\nSDPUtils.parseFingerprint = function(line) {\r\n  const parts = line.substring(14).split(' ');\r\n  return {\r\n    algorithm: parts[0].toLowerCase(), // algorithm is case-sensitive in Edge.\r\n    value: parts[1].toUpperCase(), // the definition is upper-case in RFC 4572.\r\n  };\r\n};\r\n\r\n// Extracts DTLS parameters from SDP media section or sessionpart.\r\n// FIXME: for consistency with other functions this should only\r\n//   get the fingerprint line as input. See also getIceParameters.\r\nSDPUtils.getDtlsParameters = function(mediaSection, sessionpart) {\r\n  const lines = SDPUtils.matchPrefix(mediaSection + sessionpart,\r\n    'a=fingerprint:');\r\n  // Note: a=setup line is ignored since we use the 'auto' role in Edge.\r\n  return {\r\n    role: 'auto',\r\n    fingerprints: lines.map(SDPUtils.parseFingerprint),\r\n  };\r\n};\r\n\r\n// Serializes DTLS parameters to SDP.\r\nSDPUtils.writeDtlsParameters = function(params, setupType) {\r\n  let sdp = 'a=setup:' + setupType + '\\r\\n';\r\n  params.fingerprints.forEach(fp => {\r\n    sdp += 'a=fingerprint:' + fp.algorithm + ' ' + fp.value + '\\r\\n';\r\n  });\r\n  return sdp;\r\n};\r\n\r\n// Parses a=crypto lines into\r\n//   https://rawgit.com/aboba/edgertc/master/msortc-rs4.html#dictionary-rtcsrtpsdesparameters-members\r\nSDPUtils.parseCryptoLine = function(line) {\r\n  const parts = line.substring(9).split(' ');\r\n  return {\r\n    tag: parseInt(parts[0], 10),\r\n    cryptoSuite: parts[1],\r\n    keyParams: parts[2],\r\n    sessionParams: parts.slice(3),\r\n  };\r\n};\r\n\r\nSDPUtils.writeCryptoLine = function(parameters) {\r\n  return 'a=crypto:' + parameters.tag + ' ' +\r\n    parameters.cryptoSuite + ' ' +\r\n    (typeof parameters.keyParams === 'object'\r\n      ? SDPUtils.writeCryptoKeyParams(parameters.keyParams)\r\n      : parameters.keyParams) +\r\n    (parameters.sessionParams ? ' ' + parameters.sessionParams.join(' ') : '') +\r\n    '\\r\\n';\r\n};\r\n\r\n// Parses the crypto key parameters into\r\n//   https://rawgit.com/aboba/edgertc/master/msortc-rs4.html#rtcsrtpkeyparam*\r\nSDPUtils.parseCryptoKeyParams = function(keyParams) {\r\n  if (keyParams.indexOf('inline:') !== 0) {\r\n    return null;\r\n  }\r\n  const parts = keyParams.substring(7).split('|');\r\n  return {\r\n    keyMethod: 'inline',\r\n    keySalt: parts[0],\r\n    lifeTime: parts[1],\r\n    mkiValue: parts[2] ? parts[2].split(':')[0] : undefined,\r\n    mkiLength: parts[2] ? parts[2].split(':')[1] : undefined,\r\n  };\r\n};\r\n\r\nSDPUtils.writeCryptoKeyParams = function(keyParams) {\r\n  return keyParams.keyMethod + ':'\r\n    + keyParams.keySalt +\r\n    (keyParams.lifeTime ? '|' + keyParams.lifeTime : '') +\r\n    (keyParams.mkiValue && keyParams.mkiLength\r\n      ? '|' + keyParams.mkiValue + ':' + keyParams.mkiLength\r\n      : '');\r\n};\r\n\r\n// Extracts all SDES parameters.\r\nSDPUtils.getCryptoParameters = function(mediaSection, sessionpart) {\r\n  const lines = SDPUtils.matchPrefix(mediaSection + sessionpart,\r\n    'a=crypto:');\r\n  return lines.map(SDPUtils.parseCryptoLine);\r\n};\r\n\r\n// Parses ICE information from SDP media section or sessionpart.\r\n// FIXME: for consistency with other functions this should only\r\n//   get the ice-ufrag and ice-pwd lines as input.\r\nSDPUtils.getIceParameters = function(mediaSection, sessionpart) {\r\n  const ufrag = SDPUtils.matchPrefix(mediaSection + sessionpart,\r\n    'a=ice-ufrag:')[0];\r\n  const pwd = SDPUtils.matchPrefix(mediaSection + sessionpart,\r\n    'a=ice-pwd:')[0];\r\n  if (!(ufrag && pwd)) {\r\n    return null;\r\n  }\r\n  return {\r\n    usernameFragment: ufrag.substring(12),\r\n    password: pwd.substring(10),\r\n  };\r\n};\r\n\r\n// Serializes ICE parameters to SDP.\r\nSDPUtils.writeIceParameters = function(params) {\r\n  let sdp = 'a=ice-ufrag:' + params.usernameFragment + '\\r\\n' +\r\n      'a=ice-pwd:' + params.password + '\\r\\n';\r\n  if (params.iceLite) {\r\n    sdp += 'a=ice-lite\\r\\n';\r\n  }\r\n  return sdp;\r\n};\r\n\r\n// Parses the SDP media section and returns RTCRtpParameters.\r\nSDPUtils.parseRtpParameters = function(mediaSection) {\r\n  const description = {\r\n    codecs: [],\r\n    headerExtensions: [],\r\n    fecMechanisms: [],\r\n    rtcp: [],\r\n  };\r\n  const lines = SDPUtils.splitLines(mediaSection);\r\n  const mline = lines[0].split(' ');\r\n  description.profile = mline[2];\r\n  for (let i = 3; i < mline.length; i++) { // find all codecs from mline[3..]\r\n    const pt = mline[i];\r\n    const rtpmapline = SDPUtils.matchPrefix(\r\n      mediaSection, 'a=rtpmap:' + pt + ' ')[0];\r\n    if (rtpmapline) {\r\n      const codec = SDPUtils.parseRtpMap(rtpmapline);\r\n      const fmtps = SDPUtils.matchPrefix(\r\n        mediaSection, 'a=fmtp:' + pt + ' ');\r\n      // Only the first a=fmtp:<pt> is considered.\r\n      codec.parameters = fmtps.length ? SDPUtils.parseFmtp(fmtps[0]) : {};\r\n      codec.rtcpFeedback = SDPUtils.matchPrefix(\r\n        mediaSection, 'a=rtcp-fb:' + pt + ' ')\r\n        .map(SDPUtils.parseRtcpFb);\r\n      description.codecs.push(codec);\r\n      // parse FEC mechanisms from rtpmap lines.\r\n      switch (codec.name.toUpperCase()) {\r\n        case 'RED':\r\n        case 'ULPFEC':\r\n          description.fecMechanisms.push(codec.name.toUpperCase());\r\n          break;\r\n        default: // only RED and ULPFEC are recognized as FEC mechanisms.\r\n          break;\r\n      }\r\n    }\r\n  }\r\n  SDPUtils.matchPrefix(mediaSection, 'a=extmap:').forEach(line => {\r\n    description.headerExtensions.push(SDPUtils.parseExtmap(line));\r\n  });\r\n  const wildcardRtcpFb = SDPUtils.matchPrefix(mediaSection, 'a=rtcp-fb:* ')\r\n    .map(SDPUtils.parseRtcpFb);\r\n  description.codecs.forEach(codec => {\r\n    wildcardRtcpFb.forEach(fb=> {\r\n      const duplicate = codec.rtcpFeedback.find(existingFeedback => {\r\n        return existingFeedback.type === fb.type &&\r\n          existingFeedback.parameter === fb.parameter;\r\n      });\r\n      if (!duplicate) {\r\n        codec.rtcpFeedback.push(fb);\r\n      }\r\n    });\r\n  });\r\n  // FIXME: parse rtcp.\r\n  return description;\r\n};\r\n\r\n// Generates parts of the SDP media section describing the capabilities /\r\n// parameters.\r\nSDPUtils.writeRtpDescription = function(kind, caps) {\r\n  let sdp = '';\r\n\r\n  // Build the mline.\r\n  sdp += 'm=' + kind + ' ';\r\n  sdp += caps.codecs.length > 0 ? '9' : '0'; // reject if no codecs.\r\n  sdp += ' ' + (caps.profile || 'UDP/TLS/RTP/SAVPF') + ' ';\r\n  sdp += caps.codecs.map(codec => {\r\n    if (codec.preferredPayloadType !== undefined) {\r\n      return codec.preferredPayloadType;\r\n    }\r\n    return codec.payloadType;\r\n  }).join(' ') + '\\r\\n';\r\n\r\n  sdp += 'c=IN IP4 0.0.0.0\\r\\n';\r\n  sdp += 'a=rtcp:9 IN IP4 0.0.0.0\\r\\n';\r\n\r\n  // Add a=rtpmap lines for each codec. Also fmtp and rtcp-fb.\r\n  caps.codecs.forEach(codec => {\r\n    sdp += SDPUtils.writeRtpMap(codec);\r\n    sdp += SDPUtils.writeFmtp(codec);\r\n    sdp += SDPUtils.writeRtcpFb(codec);\r\n  });\r\n  let maxptime = 0;\r\n  caps.codecs.forEach(codec => {\r\n    if (codec.maxptime > maxptime) {\r\n      maxptime = codec.maxptime;\r\n    }\r\n  });\r\n  if (maxptime > 0) {\r\n    sdp += 'a=maxptime:' + maxptime + '\\r\\n';\r\n  }\r\n\r\n  if (caps.headerExtensions) {\r\n    caps.headerExtensions.forEach(extension => {\r\n      sdp += SDPUtils.writeExtmap(extension);\r\n    });\r\n  }\r\n  // FIXME: write fecMechanisms.\r\n  return sdp;\r\n};\r\n\r\n// Parses the SDP media section and returns an array of\r\n// RTCRtpEncodingParameters.\r\nSDPUtils.parseRtpEncodingParameters = function(mediaSection) {\r\n  const encodingParameters = [];\r\n  const description = SDPUtils.parseRtpParameters(mediaSection);\r\n  const hasRed = description.fecMechanisms.indexOf('RED') !== -1;\r\n  const hasUlpfec = description.fecMechanisms.indexOf('ULPFEC') !== -1;\r\n\r\n  // filter a=ssrc:... cname:, ignore PlanB-msid\r\n  const ssrcs = SDPUtils.matchPrefix(mediaSection, 'a=ssrc:')\r\n    .map(line => SDPUtils.parseSsrcMedia(line))\r\n    .filter(parts => parts.attribute === 'cname');\r\n  const primarySsrc = ssrcs.length > 0 && ssrcs[0].ssrc;\r\n  let secondarySsrc;\r\n\r\n  const flows = SDPUtils.matchPrefix(mediaSection, 'a=ssrc-group:FID')\r\n    .map(line => {\r\n      const parts = line.substring(17).split(' ');\r\n      return parts.map(part => parseInt(part, 10));\r\n    });\r\n  if (flows.length > 0 && flows[0].length > 1 && flows[0][0] === primarySsrc) {\r\n    secondarySsrc = flows[0][1];\r\n  }\r\n\r\n  description.codecs.forEach(codec => {\r\n    if (codec.name.toUpperCase() === 'RTX' && codec.parameters.apt) {\r\n      let encParam = {\r\n        ssrc: primarySsrc,\r\n        codecPayloadType: parseInt(codec.parameters.apt, 10),\r\n      };\r\n      if (primarySsrc && secondarySsrc) {\r\n        encParam.rtx = {ssrc: secondarySsrc};\r\n      }\r\n      encodingParameters.push(encParam);\r\n      if (hasRed) {\r\n        encParam = JSON.parse(JSON.stringify(encParam));\r\n        encParam.fec = {\r\n          ssrc: primarySsrc,\r\n          mechanism: hasUlpfec ? 'red+ulpfec' : 'red',\r\n        };\r\n        encodingParameters.push(encParam);\r\n      }\r\n    }\r\n  });\r\n  if (encodingParameters.length === 0 && primarySsrc) {\r\n    encodingParameters.push({\r\n      ssrc: primarySsrc,\r\n    });\r\n  }\r\n\r\n  // we support both b=AS and b=TIAS but interpret AS as TIAS.\r\n  let bandwidth = SDPUtils.matchPrefix(mediaSection, 'b=');\r\n  if (bandwidth.length) {\r\n    if (bandwidth[0].indexOf('b=TIAS:') === 0) {\r\n      bandwidth = parseInt(bandwidth[0].substring(7), 10);\r\n    } else if (bandwidth[0].indexOf('b=AS:') === 0) {\r\n      // use formula from JSEP to convert b=AS to TIAS value.\r\n      bandwidth = parseInt(bandwidth[0].substring(5), 10) * 1000 * 0.95\r\n          - (50 * 40 * 8);\r\n    } else {\r\n      bandwidth = undefined;\r\n    }\r\n    encodingParameters.forEach(params => {\r\n      params.maxBitrate = bandwidth;\r\n    });\r\n  }\r\n  return encodingParameters;\r\n};\r\n\r\n// parses http://draft.ortc.org/#rtcrtcpparameters*\r\nSDPUtils.parseRtcpParameters = function(mediaSection) {\r\n  const rtcpParameters = {};\r\n\r\n  // Gets the first SSRC. Note that with RTX there might be multiple\r\n  // SSRCs.\r\n  const remoteSsrc = SDPUtils.matchPrefix(mediaSection, 'a=ssrc:')\r\n    .map(line => SDPUtils.parseSsrcMedia(line))\r\n    .filter(obj => obj.attribute === 'cname')[0];\r\n  if (remoteSsrc) {\r\n    rtcpParameters.cname = remoteSsrc.value;\r\n    rtcpParameters.ssrc = remoteSsrc.ssrc;\r\n  }\r\n\r\n  // Edge uses the compound attribute instead of reducedSize\r\n  // compound is !reducedSize\r\n  const rsize = SDPUtils.matchPrefix(mediaSection, 'a=rtcp-rsize');\r\n  rtcpParameters.reducedSize = rsize.length > 0;\r\n  rtcpParameters.compound = rsize.length === 0;\r\n\r\n  // parses the rtcp-mux attrіbute.\r\n  // Note that Edge does not support unmuxed RTCP.\r\n  const mux = SDPUtils.matchPrefix(mediaSection, 'a=rtcp-mux');\r\n  rtcpParameters.mux = mux.length > 0;\r\n\r\n  return rtcpParameters;\r\n};\r\n\r\nSDPUtils.writeRtcpParameters = function(rtcpParameters) {\r\n  let sdp = '';\r\n  if (rtcpParameters.reducedSize) {\r\n    sdp += 'a=rtcp-rsize\\r\\n';\r\n  }\r\n  if (rtcpParameters.mux) {\r\n    sdp += 'a=rtcp-mux\\r\\n';\r\n  }\r\n  if (rtcpParameters.ssrc !== undefined && rtcpParameters.cname) {\r\n    sdp += 'a=ssrc:' + rtcpParameters.ssrc +\r\n      ' cname:' + rtcpParameters.cname + '\\r\\n';\r\n  }\r\n  return sdp;\r\n};\r\n\r\n\r\n// parses either a=msid: or a=ssrc:... msid lines and returns\r\n// the id of the MediaStream and MediaStreamTrack.\r\nSDPUtils.parseMsid = function(mediaSection) {\r\n  let parts;\r\n  const spec = SDPUtils.matchPrefix(mediaSection, 'a=msid:');\r\n  if (spec.length === 1) {\r\n    parts = spec[0].substring(7).split(' ');\r\n    return {stream: parts[0], track: parts[1]};\r\n  }\r\n  const planB = SDPUtils.matchPrefix(mediaSection, 'a=ssrc:')\r\n    .map(line => SDPUtils.parseSsrcMedia(line))\r\n    .filter(msidParts => msidParts.attribute === 'msid');\r\n  if (planB.length > 0) {\r\n    parts = planB[0].value.split(' ');\r\n    return {stream: parts[0], track: parts[1]};\r\n  }\r\n};\r\n\r\n// SCTP\r\n// parses draft-ietf-mmusic-sctp-sdp-26 first and falls back\r\n// to draft-ietf-mmusic-sctp-sdp-05\r\nSDPUtils.parseSctpDescription = function(mediaSection) {\r\n  const mline = SDPUtils.parseMLine(mediaSection);\r\n  const maxSizeLine = SDPUtils.matchPrefix(mediaSection, 'a=max-message-size:');\r\n  let maxMessageSize;\r\n  if (maxSizeLine.length > 0) {\r\n    maxMessageSize = parseInt(maxSizeLine[0].substring(19), 10);\r\n  }\r\n  if (isNaN(maxMessageSize)) {\r\n    maxMessageSize = 65536;\r\n  }\r\n  const sctpPort = SDPUtils.matchPrefix(mediaSection, 'a=sctp-port:');\r\n  if (sctpPort.length > 0) {\r\n    return {\r\n      port: parseInt(sctpPort[0].substring(12), 10),\r\n      protocol: mline.fmt,\r\n      maxMessageSize,\r\n    };\r\n  }\r\n  const sctpMapLines = SDPUtils.matchPrefix(mediaSection, 'a=sctpmap:');\r\n  if (sctpMapLines.length > 0) {\r\n    const parts = sctpMapLines[0]\r\n      .substring(10)\r\n      .split(' ');\r\n    return {\r\n      port: parseInt(parts[0], 10),\r\n      protocol: parts[1],\r\n      maxMessageSize,\r\n    };\r\n  }\r\n};\r\n\r\n// SCTP\r\n// outputs the draft-ietf-mmusic-sctp-sdp-26 version that all browsers\r\n// support by now receiving in this format, unless we originally parsed\r\n// as the draft-ietf-mmusic-sctp-sdp-05 format (indicated by the m-line\r\n// protocol of DTLS/SCTP -- without UDP/ or TCP/)\r\nSDPUtils.writeSctpDescription = function(media, sctp) {\r\n  let output = [];\r\n  if (media.protocol !== 'DTLS/SCTP') {\r\n    output = [\r\n      'm=' + media.kind + ' 9 ' + media.protocol + ' ' + sctp.protocol + '\\r\\n',\r\n      'c=IN IP4 0.0.0.0\\r\\n',\r\n      'a=sctp-port:' + sctp.port + '\\r\\n',\r\n    ];\r\n  } else {\r\n    output = [\r\n      'm=' + media.kind + ' 9 ' + media.protocol + ' ' + sctp.port + '\\r\\n',\r\n      'c=IN IP4 0.0.0.0\\r\\n',\r\n      'a=sctpmap:' + sctp.port + ' ' + sctp.protocol + ' 65535\\r\\n',\r\n    ];\r\n  }\r\n  if (sctp.maxMessageSize !== undefined) {\r\n    output.push('a=max-message-size:' + sctp.maxMessageSize + '\\r\\n');\r\n  }\r\n  return output.join('');\r\n};\r\n\r\n// Generate a session ID for SDP.\r\n// https://tools.ietf.org/html/draft-ietf-rtcweb-jsep-20#section-5.2.1\r\n// recommends using a cryptographically random +ve 64-bit value\r\n// but right now this should be acceptable and within the right range\r\nSDPUtils.generateSessionId = function() {\r\n  return Math.random().toString().substr(2, 22);\r\n};\r\n\r\n// Write boiler plate for start of SDP\r\n// sessId argument is optional - if not supplied it will\r\n// be generated randomly\r\n// sessVersion is optional and defaults to 2\r\n// sessUser is optional and defaults to 'thisisadapterortc'\r\nSDPUtils.writeSessionBoilerplate = function(sessId, sessVer, sessUser) {\r\n  let sessionId;\r\n  const version = sessVer !== undefined ? sessVer : 2;\r\n  if (sessId) {\r\n    sessionId = sessId;\r\n  } else {\r\n    sessionId = SDPUtils.generateSessionId();\r\n  }\r\n  const user = sessUser || 'thisisadapterortc';\r\n  // FIXME: sess-id should be an NTP timestamp.\r\n  return 'v=0\\r\\n' +\r\n      'o=' + user + ' ' + sessionId + ' ' + version +\r\n        ' IN IP4 127.0.0.1\\r\\n' +\r\n      's=-\\r\\n' +\r\n      't=0 0\\r\\n';\r\n};\r\n\r\n// Gets the direction from the mediaSection or the sessionpart.\r\nSDPUtils.getDirection = function(mediaSection, sessionpart) {\r\n  // Look for sendrecv, sendonly, recvonly, inactive, default to sendrecv.\r\n  const lines = SDPUtils.splitLines(mediaSection);\r\n  for (let i = 0; i < lines.length; i++) {\r\n    switch (lines[i]) {\r\n      case 'a=sendrecv':\r\n      case 'a=sendonly':\r\n      case 'a=recvonly':\r\n      case 'a=inactive':\r\n        return lines[i].substring(2);\r\n      default:\r\n        // FIXME: What should happen here?\r\n    }\r\n  }\r\n  if (sessionpart) {\r\n    return SDPUtils.getDirection(sessionpart);\r\n  }\r\n  return 'sendrecv';\r\n};\r\n\r\nSDPUtils.getKind = function(mediaSection) {\r\n  const lines = SDPUtils.splitLines(mediaSection);\r\n  const mline = lines[0].split(' ');\r\n  return mline[0].substring(2);\r\n};\r\n\r\nSDPUtils.isRejected = function(mediaSection) {\r\n  return mediaSection.split(' ', 2)[1] === '0';\r\n};\r\n\r\nSDPUtils.parseMLine = function(mediaSection) {\r\n  const lines = SDPUtils.splitLines(mediaSection);\r\n  const parts = lines[0].substring(2).split(' ');\r\n  return {\r\n    kind: parts[0],\r\n    port: parseInt(parts[1], 10),\r\n    protocol: parts[2],\r\n    fmt: parts.slice(3).join(' '),\r\n  };\r\n};\r\n\r\nSDPUtils.parseOLine = function(mediaSection) {\r\n  const line = SDPUtils.matchPrefix(mediaSection, 'o=')[0];\r\n  const parts = line.substring(2).split(' ');\r\n  return {\r\n    username: parts[0],\r\n    sessionId: parts[1],\r\n    sessionVersion: parseInt(parts[2], 10),\r\n    netType: parts[3],\r\n    addressType: parts[4],\r\n    address: parts[5],\r\n  };\r\n};\r\n\r\n// a very naive interpretation of a valid SDP.\r\nSDPUtils.isValidSDP = function(blob) {\r\n  if (typeof blob !== 'string' || blob.length === 0) {\r\n    return false;\r\n  }\r\n  const lines = SDPUtils.splitLines(blob);\r\n  for (let i = 0; i < lines.length; i++) {\r\n    if (lines[i].length < 2 || lines[i].charAt(1) !== '=') {\r\n      return false;\r\n    }\r\n    // TODO: check the modifier a bit more.\r\n  }\r\n  return true;\r\n};\r\n\r\n// Expose public methods.\r\nif (typeof module === 'object') {\r\n  module.exports = SDPUtils;\r\n}\r\n", "/*\r\n *  Copyright (c) 2016 The WebRTC project authors. All Rights Reserved.\r\n *\r\n *  Use of this source code is governed by a BSD-style license\r\n *  that can be found in the LICENSE file in the root of the source\r\n *  tree.\r\n */\r\n/* eslint-env node */\r\n'use strict';\r\n\r\nlet logDisabled_ = true;\r\nlet deprecationWarnings_ = true;\r\n\r\n/**\r\n * Extract browser version out of the provided user agent string.\r\n *\r\n * @param {!string} uastring userAgent string.\r\n * @param {!string} expr Regular expression used as match criteria.\r\n * @param {!number} pos position in the version string to be returned.\r\n * @return {!number} browser version.\r\n */\r\nexport function extractVersion(uastring, expr, pos) {\r\n  const match = uastring.match(expr);\r\n  return match && match.length >= pos && parseFloat(match[pos], 10);\r\n}\r\n\r\n// Wraps the peerconnection event eventNameToWrap in a function\r\n// which returns the modified event object (or false to prevent\r\n// the event).\r\nexport function wrapPeerConnectionEvent(window, eventNameToWrap, wrapper) {\r\n  if (!window.RTCPeerConnection) {\r\n    return;\r\n  }\r\n  const proto = window.RTCPeerConnection.prototype;\r\n  const nativeAddEventListener = proto.addEventListener;\r\n  proto.addEventListener = function(nativeEventName, cb) {\r\n    if (nativeEventName !== eventNameToWrap) {\r\n      return nativeAddEventListener.apply(this, arguments);\r\n    }\r\n    const wrappedCallback = (e) => {\r\n      const modifiedEvent = wrapper(e);\r\n      if (modifiedEvent) {\r\n        if (cb.handleEvent) {\r\n          cb.handleEvent(modifiedEvent);\r\n        } else {\r\n          cb(modifiedEvent);\r\n        }\r\n      }\r\n    };\r\n    this._eventMap = this._eventMap || {};\r\n    if (!this._eventMap[eventNameToWrap]) {\r\n      this._eventMap[eventNameToWrap] = new Map();\r\n    }\r\n    this._eventMap[eventNameToWrap].set(cb, wrappedCallback);\r\n    return nativeAddEventListener.apply(this, [nativeEventName,\r\n      wrappedCallback]);\r\n  };\r\n\r\n  const nativeRemoveEventListener = proto.removeEventListener;\r\n  proto.removeEventListener = function(nativeEventName, cb) {\r\n    if (nativeEventName !== eventNameToWrap || !this._eventMap\r\n        || !this._eventMap[eventNameToWrap]) {\r\n      return nativeRemoveEventListener.apply(this, arguments);\r\n    }\r\n    if (!this._eventMap[eventNameToWrap].has(cb)) {\r\n      return nativeRemoveEventListener.apply(this, arguments);\r\n    }\r\n    const unwrappedCb = this._eventMap[eventNameToWrap].get(cb);\r\n    this._eventMap[eventNameToWrap].delete(cb);\r\n    if (this._eventMap[eventNameToWrap].size === 0) {\r\n      delete this._eventMap[eventNameToWrap];\r\n    }\r\n    if (Object.keys(this._eventMap).length === 0) {\r\n      delete this._eventMap;\r\n    }\r\n    return nativeRemoveEventListener.apply(this, [nativeEventName,\r\n      unwrappedCb]);\r\n  };\r\n\r\n  Object.defineProperty(proto, 'on' + eventNameToWrap, {\r\n    get() {\r\n      return this['_on' + eventNameToWrap];\r\n    },\r\n    set(cb) {\r\n      if (this['_on' + eventNameToWrap]) {\r\n        this.removeEventListener(eventNameToWrap,\r\n          this['_on' + eventNameToWrap]);\r\n        delete this['_on' + eventNameToWrap];\r\n      }\r\n      if (cb) {\r\n        this.addEventListener(eventNameToWrap,\r\n          this['_on' + eventNameToWrap] = cb);\r\n      }\r\n    },\r\n    enumerable: true,\r\n    configurable: true\r\n  });\r\n}\r\n\r\nexport function disableLog(bool) {\r\n  if (typeof bool !== 'boolean') {\r\n    return new Error('Argument type: ' + typeof bool +\r\n        '. Please use a boolean.');\r\n  }\r\n  logDisabled_ = bool;\r\n  return (bool) ? 'adapter.js logging disabled' :\r\n    'adapter.js logging enabled';\r\n}\r\n\r\n/**\r\n * Disable or enable deprecation warnings\r\n * @param {!boolean} bool set to true to disable warnings.\r\n */\r\nexport function disableWarnings(bool) {\r\n  if (typeof bool !== 'boolean') {\r\n    return new Error('Argument type: ' + typeof bool +\r\n        '. Please use a boolean.');\r\n  }\r\n  deprecationWarnings_ = !bool;\r\n  return 'adapter.js deprecation warnings ' + (bool ? 'disabled' : 'enabled');\r\n}\r\n\r\nexport function log() {\r\n  if (typeof window === 'object') {\r\n    if (logDisabled_) {\r\n      return;\r\n    }\r\n    if (typeof console !== 'undefined' && typeof console.log === 'function') {\r\n      console.log.apply(console, arguments);\r\n    }\r\n  }\r\n}\r\n\r\n/**\r\n * Shows a deprecation warning suggesting the modern and spec-compatible API.\r\n */\r\nexport function deprecated(oldMethod, newMethod) {\r\n  if (!deprecationWarnings_) {\r\n    return;\r\n  }\r\n  console.warn(oldMethod + ' is deprecated, please use ' + newMethod +\r\n      ' instead.');\r\n}\r\n\r\n/**\r\n * Browser detector.\r\n *\r\n * @return {object} result containing browser and version\r\n *     properties.\r\n */\r\nexport function detectBrowser(window) {\r\n  // Returned result object.\r\n  const result = {browser: null, version: null};\r\n\r\n  // Fail early if it's not a browser\r\n  if (typeof window === 'undefined' || !window.navigator ||\r\n      !window.navigator.userAgent) {\r\n    result.browser = 'Not a browser.';\r\n    return result;\r\n  }\r\n\r\n  const {navigator} = window;\r\n\r\n  if (navigator.mozGetUserMedia) { // Firefox.\r\n    result.browser = 'firefox';\r\n    result.version = parseInt(extractVersion(navigator.userAgent,\r\n      /Firefox\\/(\\d+)\\./, 1));\r\n  } else if (navigator.webkitGetUserMedia ||\r\n      (window.isSecureContext === false && window.webkitRTCPeerConnection)) {\r\n    // Chrome, Chromium, Webview, Opera.\r\n    // Version matches Chrome/WebRTC version.\r\n    // Chrome 74 removed webkitGetUserMedia on http as well so we need the\r\n    // more complicated fallback to webkitRTCPeerConnection.\r\n    result.browser = 'chrome';\r\n    result.version = parseInt(extractVersion(navigator.userAgent,\r\n      /Chrom(e|ium)\\/(\\d+)\\./, 2));\r\n  } else if (window.RTCPeerConnection &&\r\n      navigator.userAgent.match(/AppleWebKit\\/(\\d+)\\./)) { // Safari.\r\n    result.browser = 'safari';\r\n    result.version = parseInt(extractVersion(navigator.userAgent,\r\n      /AppleWebKit\\/(\\d+)\\./, 1));\r\n    result.supportsUnifiedPlan = window.RTCRtpTransceiver &&\r\n        'currentDirection' in window.RTCRtpTransceiver.prototype;\r\n    // Only for internal usage.\r\n    result._safariVersion = extractVersion(navigator.userAgent,\r\n      /Version\\/(\\d+(\\.?\\d+))/, 1);\r\n  } else { // Default fallthrough: not supported.\r\n    result.browser = 'Not a supported browser.';\r\n    return result;\r\n  }\r\n\r\n  return result;\r\n}\r\n\r\n/**\r\n * Checks if something is an object.\r\n *\r\n * @param {*} val The something you want to check.\r\n * @return true if val is an object, false otherwise.\r\n */\r\nfunction isObject(val) {\r\n  return Object.prototype.toString.call(val) === '[object Object]';\r\n}\r\n\r\n/**\r\n * Remove all empty objects and undefined values\r\n * from a nested object -- an enhanced and vanilla version\r\n * of Lodash's `compact`.\r\n */\r\nexport function compactObject(data) {\r\n  if (!isObject(data)) {\r\n    return data;\r\n  }\r\n\r\n  return Object.keys(data).reduce(function(accumulator, key) {\r\n    const isObj = isObject(data[key]);\r\n    const value = isObj ? compactObject(data[key]) : data[key];\r\n    const isEmptyObject = isObj && !Object.keys(value).length;\r\n    if (value === undefined || isEmptyObject) {\r\n      return accumulator;\r\n    }\r\n    return Object.assign(accumulator, {[key]: value});\r\n  }, {});\r\n}\r\n\r\n/* iterates the stats graph recursively. */\r\nexport function walkStats(stats, base, resultSet) {\r\n  if (!base || resultSet.has(base.id)) {\r\n    return;\r\n  }\r\n  resultSet.set(base.id, base);\r\n  Object.keys(base).forEach(name => {\r\n    if (name.endsWith('Id')) {\r\n      walkStats(stats, stats.get(base[name]), resultSet);\r\n    } else if (name.endsWith('Ids')) {\r\n      base[name].forEach(id => {\r\n        walkStats(stats, stats.get(id), resultSet);\r\n      });\r\n    }\r\n  });\r\n}\r\n\r\n/* filter getStats for a sender/receiver track. */\r\nexport function filterStats(result, track, outbound) {\r\n  const streamStatsType = outbound ? 'outbound-rtp' : 'inbound-rtp';\r\n  const filteredResult = new Map();\r\n  if (track === null) {\r\n    return filteredResult;\r\n  }\r\n  const trackStats = [];\r\n  result.forEach(value => {\r\n    if (value.type === 'track' &&\r\n        value.trackIdentifier === track.id) {\r\n      trackStats.push(value);\r\n    }\r\n  });\r\n  trackStats.forEach(trackStat => {\r\n    result.forEach(stats => {\r\n      if (stats.type === streamStatsType && stats.trackId === trackStat.id) {\r\n        walkStats(result, stats, filteredResult);\r\n      }\r\n    });\r\n  });\r\n  return filteredResult;\r\n}\r\n\r\n", "/*\r\n *  Copyright (c) 2016 The WebRTC project authors. All Rights Reserved.\r\n *\r\n *  Use of this source code is governed by a BSD-style license\r\n *  that can be found in the LICENSE file in the root of the source\r\n *  tree.\r\n */\r\n/* eslint-env node */\r\n'use strict';\r\nimport * as utils from '../utils.js';\r\n\r\nexport {shimGetUserMedia} from './getusermedia';\r\nexport {shimGetDisplayMedia} from './getdisplaymedia';\r\n\r\nexport function shimMediaStream(window) {\r\n  window.MediaStream = window.MediaStream || window.webkitMediaStream;\r\n}\r\n\r\nexport function shimOnTrack(window) {\r\n  if (typeof window === 'object' && window.RTCPeerConnection && !('ontrack' in\r\n      window.RTCPeerConnection.prototype)) {\r\n    Object.defineProperty(window.RTCPeerConnection.prototype, 'ontrack', {\r\n      get() {\r\n        return this._ontrack;\r\n      },\r\n      set(f) {\r\n        if (this._ontrack) {\r\n          this.removeEventListener('track', this._ontrack);\r\n        }\r\n        this.addEventListener('track', this._ontrack = f);\r\n      },\r\n      enumerable: true,\r\n      configurable: true\r\n    });\r\n    const origSetRemoteDescription =\r\n        window.RTCPeerConnection.prototype.setRemoteDescription;\r\n    window.RTCPeerConnection.prototype.setRemoteDescription =\r\n      function setRemoteDescription() {\r\n        if (!this._ontrackpoly) {\r\n          this._ontrackpoly = (e) => {\r\n            // onaddstream does not fire when a track is added to an existing\r\n            // stream. But stream.onaddtrack is implemented so we use that.\r\n            e.stream.addEventListener('addtrack', te => {\r\n              let receiver;\r\n              if (window.RTCPeerConnection.prototype.getReceivers) {\r\n                receiver = this.getReceivers()\r\n                  .find(r => r.track && r.track.id === te.track.id);\r\n              } else {\r\n                receiver = {track: te.track};\r\n              }\r\n\r\n              const event = new Event('track');\r\n              event.track = te.track;\r\n              event.receiver = receiver;\r\n              event.transceiver = {receiver};\r\n              event.streams = [e.stream];\r\n              this.dispatchEvent(event);\r\n            });\r\n            e.stream.getTracks().forEach(track => {\r\n              let receiver;\r\n              if (window.RTCPeerConnection.prototype.getReceivers) {\r\n                receiver = this.getReceivers()\r\n                  .find(r => r.track && r.track.id === track.id);\r\n              } else {\r\n                receiver = {track};\r\n              }\r\n              const event = new Event('track');\r\n              event.track = track;\r\n              event.receiver = receiver;\r\n              event.transceiver = {receiver};\r\n              event.streams = [e.stream];\r\n              this.dispatchEvent(event);\r\n            });\r\n          };\r\n          this.addEventListener('addstream', this._ontrackpoly);\r\n        }\r\n        return origSetRemoteDescription.apply(this, arguments);\r\n      };\r\n  } else {\r\n    // even if RTCRtpTransceiver is in window, it is only used and\r\n    // emitted in unified-plan. Unfortunately this means we need\r\n    // to unconditionally wrap the event.\r\n    utils.wrapPeerConnectionEvent(window, 'track', e => {\r\n      if (!e.transceiver) {\r\n        Object.defineProperty(e, 'transceiver',\r\n          {value: {receiver: e.receiver}});\r\n      }\r\n      return e;\r\n    });\r\n  }\r\n}\r\n\r\nexport function shimGetSendersWithDtmf(window) {\r\n  // Overrides addTrack/removeTrack, depends on shimAddTrackRemoveTrack.\r\n  if (typeof window === 'object' && window.RTCPeerConnection &&\r\n      !('getSenders' in window.RTCPeerConnection.prototype) &&\r\n      'createDTMFSender' in window.RTCPeerConnection.prototype) {\r\n    const shimSenderWithDtmf = function(pc, track) {\r\n      return {\r\n        track,\r\n        get dtmf() {\r\n          if (this._dtmf === undefined) {\r\n            if (track.kind === 'audio') {\r\n              this._dtmf = pc.createDTMFSender(track);\r\n            } else {\r\n              this._dtmf = null;\r\n            }\r\n          }\r\n          return this._dtmf;\r\n        },\r\n        _pc: pc\r\n      };\r\n    };\r\n\r\n    // augment addTrack when getSenders is not available.\r\n    if (!window.RTCPeerConnection.prototype.getSenders) {\r\n      window.RTCPeerConnection.prototype.getSenders = function getSenders() {\r\n        this._senders = this._senders || [];\r\n        return this._senders.slice(); // return a copy of the internal state.\r\n      };\r\n      const origAddTrack = window.RTCPeerConnection.prototype.addTrack;\r\n      window.RTCPeerConnection.prototype.addTrack =\r\n        function addTrack(track, stream) {\r\n          let sender = origAddTrack.apply(this, arguments);\r\n          if (!sender) {\r\n            sender = shimSenderWithDtmf(this, track);\r\n            this._senders.push(sender);\r\n          }\r\n          return sender;\r\n        };\r\n\r\n      const origRemoveTrack = window.RTCPeerConnection.prototype.removeTrack;\r\n      window.RTCPeerConnection.prototype.removeTrack =\r\n        function removeTrack(sender) {\r\n          origRemoveTrack.apply(this, arguments);\r\n          const idx = this._senders.indexOf(sender);\r\n          if (idx !== -1) {\r\n            this._senders.splice(idx, 1);\r\n          }\r\n        };\r\n    }\r\n    const origAddStream = window.RTCPeerConnection.prototype.addStream;\r\n    window.RTCPeerConnection.prototype.addStream = function addStream(stream) {\r\n      this._senders = this._senders || [];\r\n      origAddStream.apply(this, [stream]);\r\n      stream.getTracks().forEach(track => {\r\n        this._senders.push(shimSenderWithDtmf(this, track));\r\n      });\r\n    };\r\n\r\n    const origRemoveStream = window.RTCPeerConnection.prototype.removeStream;\r\n    window.RTCPeerConnection.prototype.removeStream =\r\n      function removeStream(stream) {\r\n        this._senders = this._senders || [];\r\n        origRemoveStream.apply(this, [stream]);\r\n\r\n        stream.getTracks().forEach(track => {\r\n          const sender = this._senders.find(s => s.track === track);\r\n          if (sender) { // remove sender\r\n            this._senders.splice(this._senders.indexOf(sender), 1);\r\n          }\r\n        });\r\n      };\r\n  } else if (typeof window === 'object' && window.RTCPeerConnection &&\r\n             'getSenders' in window.RTCPeerConnection.prototype &&\r\n             'createDTMFSender' in window.RTCPeerConnection.prototype &&\r\n             window.RTCRtpSender &&\r\n             !('dtmf' in window.RTCRtpSender.prototype)) {\r\n    const origGetSenders = window.RTCPeerConnection.prototype.getSenders;\r\n    window.RTCPeerConnection.prototype.getSenders = function getSenders() {\r\n      const senders = origGetSenders.apply(this, []);\r\n      senders.forEach(sender => sender._pc = this);\r\n      return senders;\r\n    };\r\n\r\n    Object.defineProperty(window.RTCRtpSender.prototype, 'dtmf', {\r\n      get() {\r\n        if (this._dtmf === undefined) {\r\n          if (this.track.kind === 'audio') {\r\n            this._dtmf = this._pc.createDTMFSender(this.track);\r\n          } else {\r\n            this._dtmf = null;\r\n          }\r\n        }\r\n        return this._dtmf;\r\n      }\r\n    });\r\n  }\r\n}\r\n\r\nexport function shimGetStats(window) {\r\n  if (!window.RTCPeerConnection) {\r\n    return;\r\n  }\r\n\r\n  const origGetStats = window.RTCPeerConnection.prototype.getStats;\r\n  window.RTCPeerConnection.prototype.getStats = function getStats() {\r\n    const [selector, onSucc, onErr] = arguments;\r\n\r\n    // If selector is a function then we are in the old style stats so just\r\n    // pass back the original getStats format to avoid breaking old users.\r\n    if (arguments.length > 0 && typeof selector === 'function') {\r\n      return origGetStats.apply(this, arguments);\r\n    }\r\n\r\n    // When spec-style getStats is supported, return those when called with\r\n    // either no arguments or the selector argument is null.\r\n    if (origGetStats.length === 0 && (arguments.length === 0 ||\r\n        typeof selector !== 'function')) {\r\n      return origGetStats.apply(this, []);\r\n    }\r\n\r\n    const fixChromeStats_ = function(response) {\r\n      const standardReport = {};\r\n      const reports = response.result();\r\n      reports.forEach(report => {\r\n        const standardStats = {\r\n          id: report.id,\r\n          timestamp: report.timestamp,\r\n          type: {\r\n            localcandidate: 'local-candidate',\r\n            remotecandidate: 'remote-candidate'\r\n          }[report.type] || report.type\r\n        };\r\n        report.names().forEach(name => {\r\n          standardStats[name] = report.stat(name);\r\n        });\r\n        standardReport[standardStats.id] = standardStats;\r\n      });\r\n\r\n      return standardReport;\r\n    };\r\n\r\n    // shim getStats with maplike support\r\n    const makeMapStats = function(stats) {\r\n      return new Map(Object.keys(stats).map(key => [key, stats[key]]));\r\n    };\r\n\r\n    if (arguments.length >= 2) {\r\n      const successCallbackWrapper_ = function(response) {\r\n        onSucc(makeMapStats(fixChromeStats_(response)));\r\n      };\r\n\r\n      return origGetStats.apply(this, [successCallbackWrapper_,\r\n        selector]);\r\n    }\r\n\r\n    // promise-support\r\n    return new Promise((resolve, reject) => {\r\n      origGetStats.apply(this, [\r\n        function(response) {\r\n          resolve(makeMapStats(fixChromeStats_(response)));\r\n        }, reject]);\r\n    }).then(onSucc, onErr);\r\n  };\r\n}\r\n\r\nexport function shimSenderReceiverGetStats(window) {\r\n  if (!(typeof window === 'object' && window.RTCPeerConnection &&\r\n      window.RTCRtpSender && window.RTCRtpReceiver)) {\r\n    return;\r\n  }\r\n\r\n  // shim sender stats.\r\n  if (!('getStats' in window.RTCRtpSender.prototype)) {\r\n    const origGetSenders = window.RTCPeerConnection.prototype.getSenders;\r\n    if (origGetSenders) {\r\n      window.RTCPeerConnection.prototype.getSenders = function getSenders() {\r\n        const senders = origGetSenders.apply(this, []);\r\n        senders.forEach(sender => sender._pc = this);\r\n        return senders;\r\n      };\r\n    }\r\n\r\n    const origAddTrack = window.RTCPeerConnection.prototype.addTrack;\r\n    if (origAddTrack) {\r\n      window.RTCPeerConnection.prototype.addTrack = function addTrack() {\r\n        const sender = origAddTrack.apply(this, arguments);\r\n        sender._pc = this;\r\n        return sender;\r\n      };\r\n    }\r\n    window.RTCRtpSender.prototype.getStats = function getStats() {\r\n      const sender = this;\r\n      return this._pc.getStats().then(result =>\r\n        /* Note: this will include stats of all senders that\r\n         *   send a track with the same id as sender.track as\r\n         *   it is not possible to identify the RTCRtpSender.\r\n         */\r\n        utils.filterStats(result, sender.track, true));\r\n    };\r\n  }\r\n\r\n  // shim receiver stats.\r\n  if (!('getStats' in window.RTCRtpReceiver.prototype)) {\r\n    const origGetReceivers = window.RTCPeerConnection.prototype.getReceivers;\r\n    if (origGetReceivers) {\r\n      window.RTCPeerConnection.prototype.getReceivers =\r\n        function getReceivers() {\r\n          const receivers = origGetReceivers.apply(this, []);\r\n          receivers.forEach(receiver => receiver._pc = this);\r\n          return receivers;\r\n        };\r\n    }\r\n    utils.wrapPeerConnectionEvent(window, 'track', e => {\r\n      e.receiver._pc = e.srcElement;\r\n      return e;\r\n    });\r\n    window.RTCRtpReceiver.prototype.getStats = function getStats() {\r\n      const receiver = this;\r\n      return this._pc.getStats().then(result =>\r\n        utils.filterStats(result, receiver.track, false));\r\n    };\r\n  }\r\n\r\n  if (!('getStats' in window.RTCRtpSender.prototype &&\r\n      'getStats' in window.RTCRtpReceiver.prototype)) {\r\n    return;\r\n  }\r\n\r\n  // shim RTCPeerConnection.getStats(track).\r\n  const origGetStats = window.RTCPeerConnection.prototype.getStats;\r\n  window.RTCPeerConnection.prototype.getStats = function getStats() {\r\n    if (arguments.length > 0 &&\r\n        arguments[0] instanceof window.MediaStreamTrack) {\r\n      const track = arguments[0];\r\n      let sender;\r\n      let receiver;\r\n      let err;\r\n      this.getSenders().forEach(s => {\r\n        if (s.track === track) {\r\n          if (sender) {\r\n            err = true;\r\n          } else {\r\n            sender = s;\r\n          }\r\n        }\r\n      });\r\n      this.getReceivers().forEach(r => {\r\n        if (r.track === track) {\r\n          if (receiver) {\r\n            err = true;\r\n          } else {\r\n            receiver = r;\r\n          }\r\n        }\r\n        return r.track === track;\r\n      });\r\n      if (err || (sender && receiver)) {\r\n        return Promise.reject(new DOMException(\r\n          'There are more than one sender or receiver for the track.',\r\n          'InvalidAccessError'));\r\n      } else if (sender) {\r\n        return sender.getStats();\r\n      } else if (receiver) {\r\n        return receiver.getStats();\r\n      }\r\n      return Promise.reject(new DOMException(\r\n        'There is no sender or receiver for the track.',\r\n        'InvalidAccessError'));\r\n    }\r\n    return origGetStats.apply(this, arguments);\r\n  };\r\n}\r\n\r\nexport function shimAddTrackRemoveTrackWithNative(window) {\r\n  // shim addTrack/removeTrack with native variants in order to make\r\n  // the interactions with legacy getLocalStreams behave as in other browsers.\r\n  // Keeps a mapping stream.id => [stream, rtpsenders...]\r\n  window.RTCPeerConnection.prototype.getLocalStreams =\r\n    function getLocalStreams() {\r\n      this._shimmedLocalStreams = this._shimmedLocalStreams || {};\r\n      return Object.keys(this._shimmedLocalStreams)\r\n        .map(streamId => this._shimmedLocalStreams[streamId][0]);\r\n    };\r\n\r\n  const origAddTrack = window.RTCPeerConnection.prototype.addTrack;\r\n  window.RTCPeerConnection.prototype.addTrack =\r\n    function addTrack(track, stream) {\r\n      if (!stream) {\r\n        return origAddTrack.apply(this, arguments);\r\n      }\r\n      this._shimmedLocalStreams = this._shimmedLocalStreams || {};\r\n\r\n      const sender = origAddTrack.apply(this, arguments);\r\n      if (!this._shimmedLocalStreams[stream.id]) {\r\n        this._shimmedLocalStreams[stream.id] = [stream, sender];\r\n      } else if (this._shimmedLocalStreams[stream.id].indexOf(sender) === -1) {\r\n        this._shimmedLocalStreams[stream.id].push(sender);\r\n      }\r\n      return sender;\r\n    };\r\n\r\n  const origAddStream = window.RTCPeerConnection.prototype.addStream;\r\n  window.RTCPeerConnection.prototype.addStream = function addStream(stream) {\r\n    this._shimmedLocalStreams = this._shimmedLocalStreams || {};\r\n\r\n    stream.getTracks().forEach(track => {\r\n      const alreadyExists = this.getSenders().find(s => s.track === track);\r\n      if (alreadyExists) {\r\n        throw new DOMException('Track already exists.',\r\n          'InvalidAccessError');\r\n      }\r\n    });\r\n    const existingSenders = this.getSenders();\r\n    origAddStream.apply(this, arguments);\r\n    const newSenders = this.getSenders()\r\n      .filter(newSender => existingSenders.indexOf(newSender) === -1);\r\n    this._shimmedLocalStreams[stream.id] = [stream].concat(newSenders);\r\n  };\r\n\r\n  const origRemoveStream = window.RTCPeerConnection.prototype.removeStream;\r\n  window.RTCPeerConnection.prototype.removeStream =\r\n    function removeStream(stream) {\r\n      this._shimmedLocalStreams = this._shimmedLocalStreams || {};\r\n      delete this._shimmedLocalStreams[stream.id];\r\n      return origRemoveStream.apply(this, arguments);\r\n    };\r\n\r\n  const origRemoveTrack = window.RTCPeerConnection.prototype.removeTrack;\r\n  window.RTCPeerConnection.prototype.removeTrack =\r\n    function removeTrack(sender) {\r\n      this._shimmedLocalStreams = this._shimmedLocalStreams || {};\r\n      if (sender) {\r\n        Object.keys(this._shimmedLocalStreams).forEach(streamId => {\r\n          const idx = this._shimmedLocalStreams[streamId].indexOf(sender);\r\n          if (idx !== -1) {\r\n            this._shimmedLocalStreams[streamId].splice(idx, 1);\r\n          }\r\n          if (this._shimmedLocalStreams[streamId].length === 1) {\r\n            delete this._shimmedLocalStreams[streamId];\r\n          }\r\n        });\r\n      }\r\n      return origRemoveTrack.apply(this, arguments);\r\n    };\r\n}\r\n\r\nexport function shimAddTrackRemoveTrack(window, browserDetails) {\r\n  if (!window.RTCPeerConnection) {\r\n    return;\r\n  }\r\n  // shim addTrack and removeTrack.\r\n  if (window.RTCPeerConnection.prototype.addTrack &&\r\n      browserDetails.version >= 65) {\r\n    return shimAddTrackRemoveTrackWithNative(window);\r\n  }\r\n\r\n  // also shim pc.getLocalStreams when addTrack is shimmed\r\n  // to return the original streams.\r\n  const origGetLocalStreams = window.RTCPeerConnection.prototype\r\n    .getLocalStreams;\r\n  window.RTCPeerConnection.prototype.getLocalStreams =\r\n    function getLocalStreams() {\r\n      const nativeStreams = origGetLocalStreams.apply(this);\r\n      this._reverseStreams = this._reverseStreams || {};\r\n      return nativeStreams.map(stream => this._reverseStreams[stream.id]);\r\n    };\r\n\r\n  const origAddStream = window.RTCPeerConnection.prototype.addStream;\r\n  window.RTCPeerConnection.prototype.addStream = function addStream(stream) {\r\n    this._streams = this._streams || {};\r\n    this._reverseStreams = this._reverseStreams || {};\r\n\r\n    stream.getTracks().forEach(track => {\r\n      const alreadyExists = this.getSenders().find(s => s.track === track);\r\n      if (alreadyExists) {\r\n        throw new DOMException('Track already exists.',\r\n          'InvalidAccessError');\r\n      }\r\n    });\r\n    // Add identity mapping for consistency with addTrack.\r\n    // Unless this is being used with a stream from addTrack.\r\n    if (!this._reverseStreams[stream.id]) {\r\n      const newStream = new window.MediaStream(stream.getTracks());\r\n      this._streams[stream.id] = newStream;\r\n      this._reverseStreams[newStream.id] = stream;\r\n      stream = newStream;\r\n    }\r\n    origAddStream.apply(this, [stream]);\r\n  };\r\n\r\n  const origRemoveStream = window.RTCPeerConnection.prototype.removeStream;\r\n  window.RTCPeerConnection.prototype.removeStream =\r\n    function removeStream(stream) {\r\n      this._streams = this._streams || {};\r\n      this._reverseStreams = this._reverseStreams || {};\r\n\r\n      origRemoveStream.apply(this, [(this._streams[stream.id] || stream)]);\r\n      delete this._reverseStreams[(this._streams[stream.id] ?\r\n        this._streams[stream.id].id : stream.id)];\r\n      delete this._streams[stream.id];\r\n    };\r\n\r\n  window.RTCPeerConnection.prototype.addTrack =\r\n    function addTrack(track, stream) {\r\n      if (this.signalingState === 'closed') {\r\n        throw new DOMException(\r\n          'The RTCPeerConnection\\'s signalingState is \\'closed\\'.',\r\n          'InvalidStateError');\r\n      }\r\n      const streams = [].slice.call(arguments, 1);\r\n      if (streams.length !== 1 ||\r\n          !streams[0].getTracks().find(t => t === track)) {\r\n        // this is not fully correct but all we can manage without\r\n        // [[associated MediaStreams]] internal slot.\r\n        throw new DOMException(\r\n          'The adapter.js addTrack polyfill only supports a single ' +\r\n          ' stream which is associated with the specified track.',\r\n          'NotSupportedError');\r\n      }\r\n\r\n      const alreadyExists = this.getSenders().find(s => s.track === track);\r\n      if (alreadyExists) {\r\n        throw new DOMException('Track already exists.',\r\n          'InvalidAccessError');\r\n      }\r\n\r\n      this._streams = this._streams || {};\r\n      this._reverseStreams = this._reverseStreams || {};\r\n      const oldStream = this._streams[stream.id];\r\n      if (oldStream) {\r\n        // this is using odd Chrome behaviour, use with caution:\r\n        // https://bugs.chromium.org/p/webrtc/issues/detail?id=7815\r\n        // Note: we rely on the high-level addTrack/dtmf shim to\r\n        // create the sender with a dtmf sender.\r\n        oldStream.addTrack(track);\r\n\r\n        // Trigger ONN async.\r\n        Promise.resolve().then(() => {\r\n          this.dispatchEvent(new Event('negotiationneeded'));\r\n        });\r\n      } else {\r\n        const newStream = new window.MediaStream([track]);\r\n        this._streams[stream.id] = newStream;\r\n        this._reverseStreams[newStream.id] = stream;\r\n        this.addStream(newStream);\r\n      }\r\n      return this.getSenders().find(s => s.track === track);\r\n    };\r\n\r\n  // replace the internal stream id with the external one and\r\n  // vice versa.\r\n  function replaceInternalStreamId(pc, description) {\r\n    let sdp = description.sdp;\r\n    Object.keys(pc._reverseStreams || []).forEach(internalId => {\r\n      const externalStream = pc._reverseStreams[internalId];\r\n      const internalStream = pc._streams[externalStream.id];\r\n      sdp = sdp.replace(new RegExp(internalStream.id, 'g'),\r\n        externalStream.id);\r\n    });\r\n    return new RTCSessionDescription({\r\n      type: description.type,\r\n      sdp\r\n    });\r\n  }\r\n  function replaceExternalStreamId(pc, description) {\r\n    let sdp = description.sdp;\r\n    Object.keys(pc._reverseStreams || []).forEach(internalId => {\r\n      const externalStream = pc._reverseStreams[internalId];\r\n      const internalStream = pc._streams[externalStream.id];\r\n      sdp = sdp.replace(new RegExp(externalStream.id, 'g'),\r\n        internalStream.id);\r\n    });\r\n    return new RTCSessionDescription({\r\n      type: description.type,\r\n      sdp\r\n    });\r\n  }\r\n  ['createOffer', 'createAnswer'].forEach(function(method) {\r\n    const nativeMethod = window.RTCPeerConnection.prototype[method];\r\n    const methodObj = {[method]() {\r\n      const args = arguments;\r\n      const isLegacyCall = arguments.length &&\r\n          typeof arguments[0] === 'function';\r\n      if (isLegacyCall) {\r\n        return nativeMethod.apply(this, [\r\n          (description) => {\r\n            const desc = replaceInternalStreamId(this, description);\r\n            args[0].apply(null, [desc]);\r\n          },\r\n          (err) => {\r\n            if (args[1]) {\r\n              args[1].apply(null, err);\r\n            }\r\n          }, arguments[2]\r\n        ]);\r\n      }\r\n      return nativeMethod.apply(this, arguments)\r\n        .then(description => replaceInternalStreamId(this, description));\r\n    }};\r\n    window.RTCPeerConnection.prototype[method] = methodObj[method];\r\n  });\r\n\r\n  const origSetLocalDescription =\r\n      window.RTCPeerConnection.prototype.setLocalDescription;\r\n  window.RTCPeerConnection.prototype.setLocalDescription =\r\n    function setLocalDescription() {\r\n      if (!arguments.length || !arguments[0].type) {\r\n        return origSetLocalDescription.apply(this, arguments);\r\n      }\r\n      arguments[0] = replaceExternalStreamId(this, arguments[0]);\r\n      return origSetLocalDescription.apply(this, arguments);\r\n    };\r\n\r\n  // TODO: mangle getStats: https://w3c.github.io/webrtc-stats/#dom-rtcmediastreamstats-streamidentifier\r\n\r\n  const origLocalDescription = Object.getOwnPropertyDescriptor(\r\n    window.RTCPeerConnection.prototype, 'localDescription');\r\n  Object.defineProperty(window.RTCPeerConnection.prototype,\r\n    'localDescription', {\r\n      get() {\r\n        const description = origLocalDescription.get.apply(this);\r\n        if (description.type === '') {\r\n          return description;\r\n        }\r\n        return replaceInternalStreamId(this, description);\r\n      }\r\n    });\r\n\r\n  window.RTCPeerConnection.prototype.removeTrack =\r\n    function removeTrack(sender) {\r\n      if (this.signalingState === 'closed') {\r\n        throw new DOMException(\r\n          'The RTCPeerConnection\\'s signalingState is \\'closed\\'.',\r\n          'InvalidStateError');\r\n      }\r\n      // We can not yet check for sender instanceof RTCRtpSender\r\n      // since we shim RTPSender. So we check if sender._pc is set.\r\n      if (!sender._pc) {\r\n        throw new DOMException('Argument 1 of RTCPeerConnection.removeTrack ' +\r\n            'does not implement interface RTCRtpSender.', 'TypeError');\r\n      }\r\n      const isLocal = sender._pc === this;\r\n      if (!isLocal) {\r\n        throw new DOMException('Sender was not created by this connection.',\r\n          'InvalidAccessError');\r\n      }\r\n\r\n      // Search for the native stream the senders track belongs to.\r\n      this._streams = this._streams || {};\r\n      let stream;\r\n      Object.keys(this._streams).forEach(streamid => {\r\n        const hasTrack = this._streams[streamid].getTracks()\r\n          .find(track => sender.track === track);\r\n        if (hasTrack) {\r\n          stream = this._streams[streamid];\r\n        }\r\n      });\r\n\r\n      if (stream) {\r\n        if (stream.getTracks().length === 1) {\r\n          // if this is the last track of the stream, remove the stream. This\r\n          // takes care of any shimmed _senders.\r\n          this.removeStream(this._reverseStreams[stream.id]);\r\n        } else {\r\n          // relying on the same odd chrome behaviour as above.\r\n          stream.removeTrack(sender.track);\r\n        }\r\n        this.dispatchEvent(new Event('negotiationneeded'));\r\n      }\r\n    };\r\n}\r\n\r\nexport function shimPeerConnection(window, browserDetails) {\r\n  if (!window.RTCPeerConnection && window.webkitRTCPeerConnection) {\r\n    // very basic support for old versions.\r\n    window.RTCPeerConnection = window.webkitRTCPeerConnection;\r\n  }\r\n  if (!window.RTCPeerConnection) {\r\n    return;\r\n  }\r\n\r\n  // shim implicit creation of RTCSessionDescription/RTCIceCandidate\r\n  if (browserDetails.version < 53) {\r\n    ['setLocalDescription', 'setRemoteDescription', 'addIceCandidate']\r\n      .forEach(function(method) {\r\n        const nativeMethod = window.RTCPeerConnection.prototype[method];\r\n        const methodObj = {[method]() {\r\n          arguments[0] = new ((method === 'addIceCandidate') ?\r\n            window.RTCIceCandidate :\r\n            window.RTCSessionDescription)(arguments[0]);\r\n          return nativeMethod.apply(this, arguments);\r\n        }};\r\n        window.RTCPeerConnection.prototype[method] = methodObj[method];\r\n      });\r\n  }\r\n}\r\n\r\n// Attempt to fix ONN in plan-b mode.\r\nexport function fixNegotiationNeeded(window, browserDetails) {\r\n  utils.wrapPeerConnectionEvent(window, 'negotiationneeded', e => {\r\n    const pc = e.target;\r\n    if (browserDetails.version < 72 || (pc.getConfiguration &&\r\n        pc.getConfiguration().sdpSemantics === 'plan-b')) {\r\n      if (pc.signalingState !== 'stable') {\r\n        return;\r\n      }\r\n    }\r\n    return e;\r\n  });\r\n}\r\n", "/*\r\n *  Copyright (c) 2016 The WebRTC project authors. All Rights Reserved.\r\n *\r\n *  Use of this source code is governed by a BSD-style license\r\n *  that can be found in the LICENSE file in the root of the source\r\n *  tree.\r\n */\r\n/* eslint-env node */\r\n'use strict';\r\nimport * as utils from '../utils.js';\r\nconst logging = utils.log;\r\n\r\nexport function shimGetUserMedia(window, browserDetails) {\r\n  const navigator = window && window.navigator;\r\n\r\n  if (!navigator.mediaDevices) {\r\n    return;\r\n  }\r\n\r\n  const constraintsToChrome_ = function(c) {\r\n    if (typeof c !== 'object' || c.mandatory || c.optional) {\r\n      return c;\r\n    }\r\n    const cc = {};\r\n    Object.keys(c).forEach(key => {\r\n      if (key === 'require' || key === 'advanced' || key === 'mediaSource') {\r\n        return;\r\n      }\r\n      const r = (typeof c[key] === 'object') ? c[key] : {ideal: c[key]};\r\n      if (r.exact !== undefined && typeof r.exact === 'number') {\r\n        r.min = r.max = r.exact;\r\n      }\r\n      const oldname_ = function(prefix, name) {\r\n        if (prefix) {\r\n          return prefix + name.charAt(0).toUpperCase() + name.slice(1);\r\n        }\r\n        return (name === 'deviceId') ? 'sourceId' : name;\r\n      };\r\n      if (r.ideal !== undefined) {\r\n        cc.optional = cc.optional || [];\r\n        let oc = {};\r\n        if (typeof r.ideal === 'number') {\r\n          oc[oldname_('min', key)] = r.ideal;\r\n          cc.optional.push(oc);\r\n          oc = {};\r\n          oc[oldname_('max', key)] = r.ideal;\r\n          cc.optional.push(oc);\r\n        } else {\r\n          oc[oldname_('', key)] = r.ideal;\r\n          cc.optional.push(oc);\r\n        }\r\n      }\r\n      if (r.exact !== undefined && typeof r.exact !== 'number') {\r\n        cc.mandatory = cc.mandatory || {};\r\n        cc.mandatory[oldname_('', key)] = r.exact;\r\n      } else {\r\n        ['min', 'max'].forEach(mix => {\r\n          if (r[mix] !== undefined) {\r\n            cc.mandatory = cc.mandatory || {};\r\n            cc.mandatory[oldname_(mix, key)] = r[mix];\r\n          }\r\n        });\r\n      }\r\n    });\r\n    if (c.advanced) {\r\n      cc.optional = (cc.optional || []).concat(c.advanced);\r\n    }\r\n    return cc;\r\n  };\r\n\r\n  const shimConstraints_ = function(constraints, func) {\r\n    if (browserDetails.version >= 61) {\r\n      return func(constraints);\r\n    }\r\n    constraints = JSON.parse(JSON.stringify(constraints));\r\n    if (constraints && typeof constraints.audio === 'object') {\r\n      const remap = function(obj, a, b) {\r\n        if (a in obj && !(b in obj)) {\r\n          obj[b] = obj[a];\r\n          delete obj[a];\r\n        }\r\n      };\r\n      constraints = JSON.parse(JSON.stringify(constraints));\r\n      remap(constraints.audio, 'autoGainControl', 'googAutoGainControl');\r\n      remap(constraints.audio, 'noiseSuppression', 'googNoiseSuppression');\r\n      constraints.audio = constraintsToChrome_(constraints.audio);\r\n    }\r\n    if (constraints && typeof constraints.video === 'object') {\r\n      // Shim facingMode for mobile & surface pro.\r\n      let face = constraints.video.facingMode;\r\n      face = face && ((typeof face === 'object') ? face : {ideal: face});\r\n      const getSupportedFacingModeLies = browserDetails.version < 66;\r\n\r\n      if ((face && (face.exact === 'user' || face.exact === 'environment' ||\r\n                    face.ideal === 'user' || face.ideal === 'environment')) &&\r\n          !(navigator.mediaDevices.getSupportedConstraints &&\r\n            navigator.mediaDevices.getSupportedConstraints().facingMode &&\r\n            !getSupportedFacingModeLies)) {\r\n        delete constraints.video.facingMode;\r\n        let matches;\r\n        if (face.exact === 'environment' || face.ideal === 'environment') {\r\n          matches = ['back', 'rear'];\r\n        } else if (face.exact === 'user' || face.ideal === 'user') {\r\n          matches = ['front'];\r\n        }\r\n        if (matches) {\r\n          // Look for matches in label, or use last cam for back (typical).\r\n          return navigator.mediaDevices.enumerateDevices()\r\n            .then(devices => {\r\n              devices = devices.filter(d => d.kind === 'videoinput');\r\n              let dev = devices.find(d => matches.some(match =>\r\n                d.label.toLowerCase().includes(match)));\r\n              if (!dev && devices.length && matches.includes('back')) {\r\n                dev = devices[devices.length - 1]; // more likely the back cam\r\n              }\r\n              if (dev) {\r\n                constraints.video.deviceId = face.exact\r\n                  ? {exact: dev.deviceId}\r\n                  : {ideal: dev.deviceId};\r\n              }\r\n              constraints.video = constraintsToChrome_(constraints.video);\r\n              logging('chrome: ' + JSON.stringify(constraints));\r\n              return func(constraints);\r\n            });\r\n        }\r\n      }\r\n      constraints.video = constraintsToChrome_(constraints.video);\r\n    }\r\n    logging('chrome: ' + JSON.stringify(constraints));\r\n    return func(constraints);\r\n  };\r\n\r\n  const shimError_ = function(e) {\r\n    if (browserDetails.version >= 64) {\r\n      return e;\r\n    }\r\n    return {\r\n      name: {\r\n        PermissionDeniedError: 'NotAllowedError',\r\n        PermissionDismissedError: 'NotAllowedError',\r\n        InvalidStateError: 'NotAllowedError',\r\n        DevicesNotFoundError: 'NotFoundError',\r\n        ConstraintNotSatisfiedError: 'OverconstrainedError',\r\n        TrackStartError: 'NotReadableError',\r\n        MediaDeviceFailedDueToShutdown: 'NotAllowedError',\r\n        MediaDeviceKillSwitchOn: 'NotAllowedError',\r\n        TabCaptureError: 'AbortError',\r\n        ScreenCaptureError: 'AbortError',\r\n        DeviceCaptureError: 'AbortError'\r\n      }[e.name] || e.name,\r\n      message: e.message,\r\n      constraint: e.constraint || e.constraintName,\r\n      toString() {\r\n        return this.name + (this.message && ': ') + this.message;\r\n      }\r\n    };\r\n  };\r\n\r\n  const getUserMedia_ = function(constraints, onSuccess, onError) {\r\n    shimConstraints_(constraints, c => {\r\n      navigator.webkitGetUserMedia(c, onSuccess, e => {\r\n        if (onError) {\r\n          onError(shimError_(e));\r\n        }\r\n      });\r\n    });\r\n  };\r\n  navigator.getUserMedia = getUserMedia_.bind(navigator);\r\n\r\n  // Even though Chrome 45 has navigator.mediaDevices and a getUserMedia\r\n  // function which returns a Promise, it does not accept spec-style\r\n  // constraints.\r\n  if (navigator.mediaDevices.getUserMedia) {\r\n    const origGetUserMedia = navigator.mediaDevices.getUserMedia.\r\n      bind(navigator.mediaDevices);\r\n    navigator.mediaDevices.getUserMedia = function(cs) {\r\n      return shimConstraints_(cs, c => origGetUserMedia(c).then(stream => {\r\n        if (c.audio && !stream.getAudioTracks().length ||\r\n            c.video && !stream.getVideoTracks().length) {\r\n          stream.getTracks().forEach(track => {\r\n            track.stop();\r\n          });\r\n          throw new DOMException('', 'NotFoundError');\r\n        }\r\n        return stream;\r\n      }, e => Promise.reject(shimError_(e))));\r\n    };\r\n  }\r\n}\r\n", "/*\r\n *  Copyright (c) 2018 The adapter.js project authors. All Rights Reserved.\r\n *\r\n *  Use of this source code is governed by a BSD-style license\r\n *  that can be found in the LICENSE file in the root of the source\r\n *  tree.\r\n */\r\n/* eslint-env node */\r\n'use strict';\r\nexport function shimGetDisplayMedia(window, getSourceId) {\r\n  if (window.navigator.mediaDevices &&\r\n    'getDisplayMedia' in window.navigator.mediaDevices) {\r\n    return;\r\n  }\r\n  if (!(window.navigator.mediaDevices)) {\r\n    return;\r\n  }\r\n  // getSourceId is a function that returns a promise resolving with\r\n  // the sourceId of the screen/window/tab to be shared.\r\n  if (typeof getSourceId !== 'function') {\r\n    console.error('shimGetDisplayMedia: getSourceId argument is not ' +\r\n        'a function');\r\n    return;\r\n  }\r\n  window.navigator.mediaDevices.getDisplayMedia =\r\n    function getDisplayMedia(constraints) {\r\n      return getSourceId(constraints)\r\n        .then(sourceId => {\r\n          const widthSpecified = constraints.video && constraints.video.width;\r\n          const heightSpecified = constraints.video &&\r\n            constraints.video.height;\r\n          const frameRateSpecified = constraints.video &&\r\n            constraints.video.frameRate;\r\n          constraints.video = {\r\n            mandatory: {\r\n              chromeMediaSource: 'desktop',\r\n              chromeMediaSourceId: sourceId,\r\n              maxFrameRate: frameRateSpecified || 3\r\n            }\r\n          };\r\n          if (widthSpecified) {\r\n            constraints.video.mandatory.maxWidth = widthSpecified;\r\n          }\r\n          if (heightSpecified) {\r\n            constraints.video.mandatory.maxHeight = heightSpecified;\r\n          }\r\n          return window.navigator.mediaDevices.getUserMedia(constraints);\r\n        });\r\n    };\r\n}\r\n", "/*\r\n *  Copyright (c) 2016 The WebRTC project authors. All Rights Reserved.\r\n *\r\n *  Use of this source code is governed by a BSD-style license\r\n *  that can be found in the LICENSE file in the root of the source\r\n *  tree.\r\n */\r\n/* eslint-env node */\r\n'use strict';\r\n\r\nimport * as utils from '../utils';\r\nexport {shimGetUserMedia} from './getusermedia';\r\nexport {shimGetDisplayMedia} from './getdisplaymedia';\r\n\r\nexport function shimOnTrack(window) {\r\n  if (typeof window === 'object' && window.RTCTrackEvent &&\r\n      ('receiver' in window.RTCTrackEvent.prototype) &&\r\n      !('transceiver' in window.RTCTrackEvent.prototype)) {\r\n    Object.defineProperty(window.RTCTrackEvent.prototype, 'transceiver', {\r\n      get() {\r\n        return {receiver: this.receiver};\r\n      }\r\n    });\r\n  }\r\n}\r\n\r\nexport function shimPeerConnection(window, browserDetails) {\r\n  if (typeof window !== 'object' ||\r\n      !(window.RTCPeerConnection || window.mozRTCPeerConnection)) {\r\n    return; // probably media.peerconnection.enabled=false in about:config\r\n  }\r\n  if (!window.RTCPeerConnection && window.mozRTCPeerConnection) {\r\n    // very basic support for old versions.\r\n    window.RTCPeerConnection = window.mozRTCPeerConnection;\r\n  }\r\n\r\n  if (browserDetails.version < 53) {\r\n    // shim away need for obsolete RTCIceCandidate/RTCSessionDescription.\r\n    ['setLocalDescription', 'setRemoteDescription', 'addIceCandidate']\r\n      .forEach(function(method) {\r\n        const nativeMethod = window.RTCPeerConnection.prototype[method];\r\n        const methodObj = {[method]() {\r\n          arguments[0] = new ((method === 'addIceCandidate') ?\r\n            window.RTCIceCandidate :\r\n            window.RTCSessionDescription)(arguments[0]);\r\n          return nativeMethod.apply(this, arguments);\r\n        }};\r\n        window.RTCPeerConnection.prototype[method] = methodObj[method];\r\n      });\r\n  }\r\n\r\n  const modernStatsTypes = {\r\n    inboundrtp: 'inbound-rtp',\r\n    outboundrtp: 'outbound-rtp',\r\n    candidatepair: 'candidate-pair',\r\n    localcandidate: 'local-candidate',\r\n    remotecandidate: 'remote-candidate'\r\n  };\r\n\r\n  const nativeGetStats = window.RTCPeerConnection.prototype.getStats;\r\n  window.RTCPeerConnection.prototype.getStats = function getStats() {\r\n    const [selector, onSucc, onErr] = arguments;\r\n    return nativeGetStats.apply(this, [selector || null])\r\n      .then(stats => {\r\n        if (browserDetails.version < 53 && !onSucc) {\r\n          // Shim only promise getStats with spec-hyphens in type names\r\n          // Leave callback version alone; misc old uses of forEach before Map\r\n          try {\r\n            stats.forEach(stat => {\r\n              stat.type = modernStatsTypes[stat.type] || stat.type;\r\n            });\r\n          } catch (e) {\r\n            if (e.name !== 'TypeError') {\r\n              throw e;\r\n            }\r\n            // Avoid TypeError: \"type\" is read-only, in old versions. 34-43ish\r\n            stats.forEach((stat, i) => {\r\n              stats.set(i, Object.assign({}, stat, {\r\n                type: modernStatsTypes[stat.type] || stat.type\r\n              }));\r\n            });\r\n          }\r\n        }\r\n        return stats;\r\n      })\r\n      .then(onSucc, onErr);\r\n  };\r\n}\r\n\r\nexport function shimSenderGetStats(window) {\r\n  if (!(typeof window === 'object' && window.RTCPeerConnection &&\r\n      window.RTCRtpSender)) {\r\n    return;\r\n  }\r\n  if (window.RTCRtpSender && 'getStats' in window.RTCRtpSender.prototype) {\r\n    return;\r\n  }\r\n  const origGetSenders = window.RTCPeerConnection.prototype.getSenders;\r\n  if (origGetSenders) {\r\n    window.RTCPeerConnection.prototype.getSenders = function getSenders() {\r\n      const senders = origGetSenders.apply(this, []);\r\n      senders.forEach(sender => sender._pc = this);\r\n      return senders;\r\n    };\r\n  }\r\n\r\n  const origAddTrack = window.RTCPeerConnection.prototype.addTrack;\r\n  if (origAddTrack) {\r\n    window.RTCPeerConnection.prototype.addTrack = function addTrack() {\r\n      const sender = origAddTrack.apply(this, arguments);\r\n      sender._pc = this;\r\n      return sender;\r\n    };\r\n  }\r\n  window.RTCRtpSender.prototype.getStats = function getStats() {\r\n    return this.track ? this._pc.getStats(this.track) :\r\n      Promise.resolve(new Map());\r\n  };\r\n}\r\n\r\nexport function shimReceiverGetStats(window) {\r\n  if (!(typeof window === 'object' && window.RTCPeerConnection &&\r\n      window.RTCRtpSender)) {\r\n    return;\r\n  }\r\n  if (window.RTCRtpSender && 'getStats' in window.RTCRtpReceiver.prototype) {\r\n    return;\r\n  }\r\n  const origGetReceivers = window.RTCPeerConnection.prototype.getReceivers;\r\n  if (origGetReceivers) {\r\n    window.RTCPeerConnection.prototype.getReceivers = function getReceivers() {\r\n      const receivers = origGetReceivers.apply(this, []);\r\n      receivers.forEach(receiver => receiver._pc = this);\r\n      return receivers;\r\n    };\r\n  }\r\n  utils.wrapPeerConnectionEvent(window, 'track', e => {\r\n    e.receiver._pc = e.srcElement;\r\n    return e;\r\n  });\r\n  window.RTCRtpReceiver.prototype.getStats = function getStats() {\r\n    return this._pc.getStats(this.track);\r\n  };\r\n}\r\n\r\nexport function shimRemoveStream(window) {\r\n  if (!window.RTCPeerConnection ||\r\n      'removeStream' in window.RTCPeerConnection.prototype) {\r\n    return;\r\n  }\r\n  window.RTCPeerConnection.prototype.removeStream =\r\n    function removeStream(stream) {\r\n      utils.deprecated('removeStream', 'removeTrack');\r\n      this.getSenders().forEach(sender => {\r\n        if (sender.track && stream.getTracks().includes(sender.track)) {\r\n          this.removeTrack(sender);\r\n        }\r\n      });\r\n    };\r\n}\r\n\r\nexport function shimRTCDataChannel(window) {\r\n  // rename DataChannel to RTCDataChannel (native fix in FF60):\r\n  // https://bugzilla.mozilla.org/show_bug.cgi?id=1173851\r\n  if (window.DataChannel && !window.RTCDataChannel) {\r\n    window.RTCDataChannel = window.DataChannel;\r\n  }\r\n}\r\n\r\nexport function shimAddTransceiver(window) {\r\n  // https://github.com/webrtcHacks/adapter/issues/998#issuecomment-516921647\r\n  // Firefox ignores the init sendEncodings options passed to addTransceiver\r\n  // https://bugzilla.mozilla.org/show_bug.cgi?id=1396918\r\n  if (!(typeof window === 'object' && window.RTCPeerConnection)) {\r\n    return;\r\n  }\r\n  const origAddTransceiver = window.RTCPeerConnection.prototype.addTransceiver;\r\n  if (origAddTransceiver) {\r\n    window.RTCPeerConnection.prototype.addTransceiver =\r\n      function addTransceiver() {\r\n        this.setParametersPromises = [];\r\n        // WebIDL input coercion and validation\r\n        let sendEncodings = arguments[1] && arguments[1].sendEncodings;\r\n        if (sendEncodings === undefined) {\r\n          sendEncodings = [];\r\n        }\r\n        sendEncodings = [...sendEncodings];\r\n        const shouldPerformCheck = sendEncodings.length > 0;\r\n        if (shouldPerformCheck) {\r\n          // If sendEncodings params are provided, validate grammar\r\n          sendEncodings.forEach((encodingParam) => {\r\n            if ('rid' in encodingParam) {\r\n              const ridRegex = /^[a-z0-9]{0,16}$/i;\r\n              if (!ridRegex.test(encodingParam.rid)) {\r\n                throw new TypeError('Invalid RID value provided.');\r\n              }\r\n            }\r\n            if ('scaleResolutionDownBy' in encodingParam) {\r\n              if (!(parseFloat(encodingParam.scaleResolutionDownBy) >= 1.0)) {\r\n                throw new RangeError('scale_resolution_down_by must be >= 1.0');\r\n              }\r\n            }\r\n            if ('maxFramerate' in encodingParam) {\r\n              if (!(parseFloat(encodingParam.maxFramerate) >= 0)) {\r\n                throw new RangeError('max_framerate must be >= 0.0');\r\n              }\r\n            }\r\n          });\r\n        }\r\n        const transceiver = origAddTransceiver.apply(this, arguments);\r\n        if (shouldPerformCheck) {\r\n          // Check if the init options were applied. If not we do this in an\r\n          // asynchronous way and save the promise reference in a global object.\r\n          // This is an ugly hack, but at the same time is way more robust than\r\n          // checking the sender parameters before and after the createOffer\r\n          // Also note that after the createoffer we are not 100% sure that\r\n          // the params were asynchronously applied so we might miss the\r\n          // opportunity to recreate offer.\r\n          const {sender} = transceiver;\r\n          const params = sender.getParameters();\r\n          if (!('encodings' in params) ||\r\n              // Avoid being fooled by patched getParameters() below.\r\n              (params.encodings.length === 1 &&\r\n               Object.keys(params.encodings[0]).length === 0)) {\r\n            params.encodings = sendEncodings;\r\n            sender.sendEncodings = sendEncodings;\r\n            this.setParametersPromises.push(sender.setParameters(params)\r\n              .then(() => {\r\n                delete sender.sendEncodings;\r\n              }).catch(() => {\r\n                delete sender.sendEncodings;\r\n              })\r\n            );\r\n          }\r\n        }\r\n        return transceiver;\r\n      };\r\n  }\r\n}\r\n\r\nexport function shimGetParameters(window) {\r\n  if (!(typeof window === 'object' && window.RTCRtpSender)) {\r\n    return;\r\n  }\r\n  const origGetParameters = window.RTCRtpSender.prototype.getParameters;\r\n  if (origGetParameters) {\r\n    window.RTCRtpSender.prototype.getParameters =\r\n      function getParameters() {\r\n        const params = origGetParameters.apply(this, arguments);\r\n        if (!('encodings' in params)) {\r\n          params.encodings = [].concat(this.sendEncodings || [{}]);\r\n        }\r\n        return params;\r\n      };\r\n  }\r\n}\r\n\r\nexport function shimCreateOffer(window) {\r\n  // https://github.com/webrtcHacks/adapter/issues/998#issuecomment-516921647\r\n  // Firefox ignores the init sendEncodings options passed to addTransceiver\r\n  // https://bugzilla.mozilla.org/show_bug.cgi?id=1396918\r\n  if (!(typeof window === 'object' && window.RTCPeerConnection)) {\r\n    return;\r\n  }\r\n  const origCreateOffer = window.RTCPeerConnection.prototype.createOffer;\r\n  window.RTCPeerConnection.prototype.createOffer = function createOffer() {\r\n    if (this.setParametersPromises && this.setParametersPromises.length) {\r\n      return Promise.all(this.setParametersPromises)\r\n        .then(() => {\r\n          return origCreateOffer.apply(this, arguments);\r\n        })\r\n        .finally(() => {\r\n          this.setParametersPromises = [];\r\n        });\r\n    }\r\n    return origCreateOffer.apply(this, arguments);\r\n  };\r\n}\r\n\r\nexport function shimCreateAnswer(window) {\r\n  // https://github.com/webrtcHacks/adapter/issues/998#issuecomment-516921647\r\n  // Firefox ignores the init sendEncodings options passed to addTransceiver\r\n  // https://bugzilla.mozilla.org/show_bug.cgi?id=1396918\r\n  if (!(typeof window === 'object' && window.RTCPeerConnection)) {\r\n    return;\r\n  }\r\n  const origCreateAnswer = window.RTCPeerConnection.prototype.createAnswer;\r\n  window.RTCPeerConnection.prototype.createAnswer = function createAnswer() {\r\n    if (this.setParametersPromises && this.setParametersPromises.length) {\r\n      return Promise.all(this.setParametersPromises)\r\n        .then(() => {\r\n          return origCreateAnswer.apply(this, arguments);\r\n        })\r\n        .finally(() => {\r\n          this.setParametersPromises = [];\r\n        });\r\n    }\r\n    return origCreateAnswer.apply(this, arguments);\r\n  };\r\n}\r\n", "/*\r\n *  Copyright (c) 2016 The WebRTC project authors. All Rights Reserved.\r\n *\r\n *  Use of this source code is governed by a BSD-style license\r\n *  that can be found in the LICENSE file in the root of the source\r\n *  tree.\r\n */\r\n/* eslint-env node */\r\n'use strict';\r\n\r\nimport * as utils from '../utils';\r\n\r\nexport function shimGetUserMedia(window, browserDetails) {\r\n  const navigator = window && window.navigator;\r\n  const MediaStreamTrack = window && window.MediaStreamTrack;\r\n\r\n  navigator.getUserMedia = function(constraints, onSuccess, onError) {\r\n    // Replace Firefox 44+'s deprecation warning with unprefixed version.\r\n    utils.deprecated('navigator.getUserMedia',\r\n      'navigator.mediaDevices.getUserMedia');\r\n    navigator.mediaDevices.getUserMedia(constraints).then(onSuccess, onError);\r\n  };\r\n\r\n  if (!(browserDetails.version > 55 &&\r\n      'autoGainControl' in navigator.mediaDevices.getSupportedConstraints())) {\r\n    const remap = function(obj, a, b) {\r\n      if (a in obj && !(b in obj)) {\r\n        obj[b] = obj[a];\r\n        delete obj[a];\r\n      }\r\n    };\r\n\r\n    const nativeGetUserMedia = navigator.mediaDevices.getUserMedia.\r\n      bind(navigator.mediaDevices);\r\n    navigator.mediaDevices.getUserMedia = function(c) {\r\n      if (typeof c === 'object' && typeof c.audio === 'object') {\r\n        c = JSON.parse(JSON.stringify(c));\r\n        remap(c.audio, 'autoGainControl', 'mozAutoGainControl');\r\n        remap(c.audio, 'noiseSuppression', 'mozNoiseSuppression');\r\n      }\r\n      return nativeGetUserMedia(c);\r\n    };\r\n\r\n    if (MediaStreamTrack && MediaStreamTrack.prototype.getSettings) {\r\n      const nativeGetSettings = MediaStreamTrack.prototype.getSettings;\r\n      MediaStreamTrack.prototype.getSettings = function() {\r\n        const obj = nativeGetSettings.apply(this, arguments);\r\n        remap(obj, 'mozAutoGainControl', 'autoGainControl');\r\n        remap(obj, 'mozNoiseSuppression', 'noiseSuppression');\r\n        return obj;\r\n      };\r\n    }\r\n\r\n    if (MediaStreamTrack && MediaStreamTrack.prototype.applyConstraints) {\r\n      const nativeApplyConstraints =\r\n        MediaStreamTrack.prototype.applyConstraints;\r\n      MediaStreamTrack.prototype.applyConstraints = function(c) {\r\n        if (this.kind === 'audio' && typeof c === 'object') {\r\n          c = JSON.parse(JSON.stringify(c));\r\n          remap(c, 'autoGainControl', 'mozAutoGainControl');\r\n          remap(c, 'noiseSuppression', 'mozNoiseSuppression');\r\n        }\r\n        return nativeApplyConstraints.apply(this, [c]);\r\n      };\r\n    }\r\n  }\r\n}\r\n", "/*\r\n *  Copyright (c) 2018 The adapter.js project authors. All Rights Reserved.\r\n *\r\n *  Use of this source code is governed by a BSD-style license\r\n *  that can be found in the LICENSE file in the root of the source\r\n *  tree.\r\n */\r\n/* eslint-env node */\r\n'use strict';\r\n\r\nexport function shimGetDisplayMedia(window, preferredMediaSource) {\r\n  if (window.navigator.mediaDevices &&\r\n    'getDisplayMedia' in window.navigator.mediaDevices) {\r\n    return;\r\n  }\r\n  if (!(window.navigator.mediaDevices)) {\r\n    return;\r\n  }\r\n  window.navigator.mediaDevices.getDisplayMedia =\r\n    function getDisplayMedia(constraints) {\r\n      if (!(constraints && constraints.video)) {\r\n        const err = new DOMException('getDisplayMedia without video ' +\r\n            'constraints is undefined');\r\n        err.name = 'NotFoundError';\r\n        // from https://heycam.github.io/webidl/#idl-DOMException-error-names\r\n        err.code = 8;\r\n        return Promise.reject(err);\r\n      }\r\n      if (constraints.video === true) {\r\n        constraints.video = {mediaSource: preferredMediaSource};\r\n      } else {\r\n        constraints.video.mediaSource = preferredMediaSource;\r\n      }\r\n      return window.navigator.mediaDevices.getUserMedia(constraints);\r\n    };\r\n}\r\n", "/*\r\n *  Copyright (c) 2016 The WebRTC project authors. All Rights Reserved.\r\n *\r\n *  Use of this source code is governed by a BSD-style license\r\n *  that can be found in the LICENSE file in the root of the source\r\n *  tree.\r\n */\r\n'use strict';\r\nimport * as utils from '../utils';\r\n\r\nexport function shimLocalStreamsAPI(window) {\r\n  if (typeof window !== 'object' || !window.RTCPeerConnection) {\r\n    return;\r\n  }\r\n  if (!('getLocalStreams' in window.RTCPeerConnection.prototype)) {\r\n    window.RTCPeerConnection.prototype.getLocalStreams =\r\n      function getLocalStreams() {\r\n        if (!this._localStreams) {\r\n          this._localStreams = [];\r\n        }\r\n        return this._localStreams;\r\n      };\r\n  }\r\n  if (!('addStream' in window.RTCPeerConnection.prototype)) {\r\n    const _addTrack = window.RTCPeerConnection.prototype.addTrack;\r\n    window.RTCPeerConnection.prototype.addStream = function addStream(stream) {\r\n      if (!this._localStreams) {\r\n        this._localStreams = [];\r\n      }\r\n      if (!this._localStreams.includes(stream)) {\r\n        this._localStreams.push(stream);\r\n      }\r\n      // Try to emulate Chrome's behaviour of adding in audio-video order.\r\n      // Safari orders by track id.\r\n      stream.getAudioTracks().forEach(track => _addTrack.call(this, track,\r\n        stream));\r\n      stream.getVideoTracks().forEach(track => _addTrack.call(this, track,\r\n        stream));\r\n    };\r\n\r\n    window.RTCPeerConnection.prototype.addTrack =\r\n      function addTrack(track, ...streams) {\r\n        if (streams) {\r\n          streams.forEach((stream) => {\r\n            if (!this._localStreams) {\r\n              this._localStreams = [stream];\r\n            } else if (!this._localStreams.includes(stream)) {\r\n              this._localStreams.push(stream);\r\n            }\r\n          });\r\n        }\r\n        return _addTrack.apply(this, arguments);\r\n      };\r\n  }\r\n  if (!('removeStream' in window.RTCPeerConnection.prototype)) {\r\n    window.RTCPeerConnection.prototype.removeStream =\r\n      function removeStream(stream) {\r\n        if (!this._localStreams) {\r\n          this._localStreams = [];\r\n        }\r\n        const index = this._localStreams.indexOf(stream);\r\n        if (index === -1) {\r\n          return;\r\n        }\r\n        this._localStreams.splice(index, 1);\r\n        const tracks = stream.getTracks();\r\n        this.getSenders().forEach(sender => {\r\n          if (tracks.includes(sender.track)) {\r\n            this.removeTrack(sender);\r\n          }\r\n        });\r\n      };\r\n  }\r\n}\r\n\r\nexport function shimRemoteStreamsAPI(window) {\r\n  if (typeof window !== 'object' || !window.RTCPeerConnection) {\r\n    return;\r\n  }\r\n  if (!('getRemoteStreams' in window.RTCPeerConnection.prototype)) {\r\n    window.RTCPeerConnection.prototype.getRemoteStreams =\r\n      function getRemoteStreams() {\r\n        return this._remoteStreams ? this._remoteStreams : [];\r\n      };\r\n  }\r\n  if (!('onaddstream' in window.RTCPeerConnection.prototype)) {\r\n    Object.defineProperty(window.RTCPeerConnection.prototype, 'onaddstream', {\r\n      get() {\r\n        return this._onaddstream;\r\n      },\r\n      set(f) {\r\n        if (this._onaddstream) {\r\n          this.removeEventListener('addstream', this._onaddstream);\r\n          this.removeEventListener('track', this._onaddstreampoly);\r\n        }\r\n        this.addEventListener('addstream', this._onaddstream = f);\r\n        this.addEventListener('track', this._onaddstreampoly = (e) => {\r\n          e.streams.forEach(stream => {\r\n            if (!this._remoteStreams) {\r\n              this._remoteStreams = [];\r\n            }\r\n            if (this._remoteStreams.includes(stream)) {\r\n              return;\r\n            }\r\n            this._remoteStreams.push(stream);\r\n            const event = new Event('addstream');\r\n            event.stream = stream;\r\n            this.dispatchEvent(event);\r\n          });\r\n        });\r\n      }\r\n    });\r\n    const origSetRemoteDescription =\r\n      window.RTCPeerConnection.prototype.setRemoteDescription;\r\n    window.RTCPeerConnection.prototype.setRemoteDescription =\r\n      function setRemoteDescription() {\r\n        const pc = this;\r\n        if (!this._onaddstreampoly) {\r\n          this.addEventListener('track', this._onaddstreampoly = function(e) {\r\n            e.streams.forEach(stream => {\r\n              if (!pc._remoteStreams) {\r\n                pc._remoteStreams = [];\r\n              }\r\n              if (pc._remoteStreams.indexOf(stream) >= 0) {\r\n                return;\r\n              }\r\n              pc._remoteStreams.push(stream);\r\n              const event = new Event('addstream');\r\n              event.stream = stream;\r\n              pc.dispatchEvent(event);\r\n            });\r\n          });\r\n        }\r\n        return origSetRemoteDescription.apply(pc, arguments);\r\n      };\r\n  }\r\n}\r\n\r\nexport function shimCallbacksAPI(window) {\r\n  if (typeof window !== 'object' || !window.RTCPeerConnection) {\r\n    return;\r\n  }\r\n  const prototype = window.RTCPeerConnection.prototype;\r\n  const origCreateOffer = prototype.createOffer;\r\n  const origCreateAnswer = prototype.createAnswer;\r\n  const setLocalDescription = prototype.setLocalDescription;\r\n  const setRemoteDescription = prototype.setRemoteDescription;\r\n  const addIceCandidate = prototype.addIceCandidate;\r\n\r\n  prototype.createOffer =\r\n    function createOffer(successCallback, failureCallback) {\r\n      const options = (arguments.length >= 2) ? arguments[2] : arguments[0];\r\n      const promise = origCreateOffer.apply(this, [options]);\r\n      if (!failureCallback) {\r\n        return promise;\r\n      }\r\n      promise.then(successCallback, failureCallback);\r\n      return Promise.resolve();\r\n    };\r\n\r\n  prototype.createAnswer =\r\n    function createAnswer(successCallback, failureCallback) {\r\n      const options = (arguments.length >= 2) ? arguments[2] : arguments[0];\r\n      const promise = origCreateAnswer.apply(this, [options]);\r\n      if (!failureCallback) {\r\n        return promise;\r\n      }\r\n      promise.then(successCallback, failureCallback);\r\n      return Promise.resolve();\r\n    };\r\n\r\n  let withCallback = function(description, successCallback, failureCallback) {\r\n    const promise = setLocalDescription.apply(this, [description]);\r\n    if (!failureCallback) {\r\n      return promise;\r\n    }\r\n    promise.then(successCallback, failureCallback);\r\n    return Promise.resolve();\r\n  };\r\n  prototype.setLocalDescription = withCallback;\r\n\r\n  withCallback = function(description, successCallback, failureCallback) {\r\n    const promise = setRemoteDescription.apply(this, [description]);\r\n    if (!failureCallback) {\r\n      return promise;\r\n    }\r\n    promise.then(successCallback, failureCallback);\r\n    return Promise.resolve();\r\n  };\r\n  prototype.setRemoteDescription = withCallback;\r\n\r\n  withCallback = function(candidate, successCallback, failureCallback) {\r\n    const promise = addIceCandidate.apply(this, [candidate]);\r\n    if (!failureCallback) {\r\n      return promise;\r\n    }\r\n    promise.then(successCallback, failureCallback);\r\n    return Promise.resolve();\r\n  };\r\n  prototype.addIceCandidate = withCallback;\r\n}\r\n\r\nexport function shimGetUserMedia(window) {\r\n  const navigator = window && window.navigator;\r\n\r\n  if (navigator.mediaDevices && navigator.mediaDevices.getUserMedia) {\r\n    // shim not needed in Safari 12.1\r\n    const mediaDevices = navigator.mediaDevices;\r\n    const _getUserMedia = mediaDevices.getUserMedia.bind(mediaDevices);\r\n    navigator.mediaDevices.getUserMedia = (constraints) => {\r\n      return _getUserMedia(shimConstraints(constraints));\r\n    };\r\n  }\r\n\r\n  if (!navigator.getUserMedia && navigator.mediaDevices &&\r\n    navigator.mediaDevices.getUserMedia) {\r\n    navigator.getUserMedia = function getUserMedia(constraints, cb, errcb) {\r\n      navigator.mediaDevices.getUserMedia(constraints)\r\n        .then(cb, errcb);\r\n    }.bind(navigator);\r\n  }\r\n}\r\n\r\nexport function shimConstraints(constraints) {\r\n  if (constraints && constraints.video !== undefined) {\r\n    return Object.assign({},\r\n      constraints,\r\n      {video: utils.compactObject(constraints.video)}\r\n    );\r\n  }\r\n\r\n  return constraints;\r\n}\r\n\r\nexport function shimRTCIceServerUrls(window) {\r\n  if (!window.RTCPeerConnection) {\r\n    return;\r\n  }\r\n  // migrate from non-spec RTCIceServer.url to RTCIceServer.urls\r\n  const OrigPeerConnection = window.RTCPeerConnection;\r\n  window.RTCPeerConnection =\r\n    function RTCPeerConnection(pcConfig, pcConstraints) {\r\n      if (pcConfig && pcConfig.iceServers) {\r\n        const newIceServers = [];\r\n        for (let i = 0; i < pcConfig.iceServers.length; i++) {\r\n          let server = pcConfig.iceServers[i];\r\n          if (server.urls === undefined && server.url) {\r\n            utils.deprecated('RTCIceServer.url', 'RTCIceServer.urls');\r\n            server = JSON.parse(JSON.stringify(server));\r\n            server.urls = server.url;\r\n            delete server.url;\r\n            newIceServers.push(server);\r\n          } else {\r\n            newIceServers.push(pcConfig.iceServers[i]);\r\n          }\r\n        }\r\n        pcConfig.iceServers = newIceServers;\r\n      }\r\n      return new OrigPeerConnection(pcConfig, pcConstraints);\r\n    };\r\n  window.RTCPeerConnection.prototype = OrigPeerConnection.prototype;\r\n  // wrap static methods. Currently just generateCertificate.\r\n  if ('generateCertificate' in OrigPeerConnection) {\r\n    Object.defineProperty(window.RTCPeerConnection, 'generateCertificate', {\r\n      get() {\r\n        return OrigPeerConnection.generateCertificate;\r\n      }\r\n    });\r\n  }\r\n}\r\n\r\nexport function shimTrackEventTransceiver(window) {\r\n  // Add event.transceiver member over deprecated event.receiver\r\n  if (typeof window === 'object' && window.RTCTrackEvent &&\r\n      'receiver' in window.RTCTrackEvent.prototype &&\r\n      !('transceiver' in window.RTCTrackEvent.prototype)) {\r\n    Object.defineProperty(window.RTCTrackEvent.prototype, 'transceiver', {\r\n      get() {\r\n        return {receiver: this.receiver};\r\n      }\r\n    });\r\n  }\r\n}\r\n\r\nexport function shimCreateOfferLegacy(window) {\r\n  const origCreateOffer = window.RTCPeerConnection.prototype.createOffer;\r\n  window.RTCPeerConnection.prototype.createOffer =\r\n    function createOffer(offerOptions) {\r\n      if (offerOptions) {\r\n        if (typeof offerOptions.offerToReceiveAudio !== 'undefined') {\r\n          // support bit values\r\n          offerOptions.offerToReceiveAudio =\r\n            !!offerOptions.offerToReceiveAudio;\r\n        }\r\n        const audioTransceiver = this.getTransceivers().find(transceiver =>\r\n          transceiver.receiver.track.kind === 'audio');\r\n        if (offerOptions.offerToReceiveAudio === false && audioTransceiver) {\r\n          if (audioTransceiver.direction === 'sendrecv') {\r\n            if (audioTransceiver.setDirection) {\r\n              audioTransceiver.setDirection('sendonly');\r\n            } else {\r\n              audioTransceiver.direction = 'sendonly';\r\n            }\r\n          } else if (audioTransceiver.direction === 'recvonly') {\r\n            if (audioTransceiver.setDirection) {\r\n              audioTransceiver.setDirection('inactive');\r\n            } else {\r\n              audioTransceiver.direction = 'inactive';\r\n            }\r\n          }\r\n        } else if (offerOptions.offerToReceiveAudio === true &&\r\n            !audioTransceiver) {\r\n          this.addTransceiver('audio', {direction: 'recvonly'});\r\n        }\r\n\r\n        if (typeof offerOptions.offerToReceiveVideo !== 'undefined') {\r\n          // support bit values\r\n          offerOptions.offerToReceiveVideo =\r\n            !!offerOptions.offerToReceiveVideo;\r\n        }\r\n        const videoTransceiver = this.getTransceivers().find(transceiver =>\r\n          transceiver.receiver.track.kind === 'video');\r\n        if (offerOptions.offerToReceiveVideo === false && videoTransceiver) {\r\n          if (videoTransceiver.direction === 'sendrecv') {\r\n            if (videoTransceiver.setDirection) {\r\n              videoTransceiver.setDirection('sendonly');\r\n            } else {\r\n              videoTransceiver.direction = 'sendonly';\r\n            }\r\n          } else if (videoTransceiver.direction === 'recvonly') {\r\n            if (videoTransceiver.setDirection) {\r\n              videoTransceiver.setDirection('inactive');\r\n            } else {\r\n              videoTransceiver.direction = 'inactive';\r\n            }\r\n          }\r\n        } else if (offerOptions.offerToReceiveVideo === true &&\r\n            !videoTransceiver) {\r\n          this.addTransceiver('video', {direction: 'recvonly'});\r\n        }\r\n      }\r\n      return origCreateOffer.apply(this, arguments);\r\n    };\r\n}\r\n\r\nexport function shimAudioContext(window) {\r\n  if (typeof window !== 'object' || window.AudioContext) {\r\n    return;\r\n  }\r\n  window.AudioContext = window.webkitAudioContext;\r\n}\r\n\r\n", "/*\r\n *  Copyright (c) 2017 The WebRTC project authors. All Rights Reserved.\r\n *\r\n *  Use of this source code is governed by a BSD-style license\r\n *  that can be found in the LICENSE file in the root of the source\r\n *  tree.\r\n */\r\n/* eslint-env node */\r\n'use strict';\r\n\r\nimport SDPUtils from 'sdp';\r\nimport * as utils from './utils';\r\n\r\nexport function shimRTCIceCandidate(window) {\r\n  // foundation is arbitrarily chosen as an indicator for full support for\r\n  // https://w3c.github.io/webrtc-pc/#rtcicecandidate-interface\r\n  if (!window.RTCIceCandidate || (window.RTCIceCandidate && 'foundation' in\r\n      window.RTCIceCandidate.prototype)) {\r\n    return;\r\n  }\r\n\r\n  const NativeRTCIceCandidate = window.RTCIceCandidate;\r\n  window.RTCIceCandidate = function RTCIceCandidate(args) {\r\n    // Remove the a= which shouldn't be part of the candidate string.\r\n    if (typeof args === 'object' && args.candidate &&\r\n        args.candidate.indexOf('a=') === 0) {\r\n      args = JSON.parse(JSON.stringify(args));\r\n      args.candidate = args.candidate.substring(2);\r\n    }\r\n\r\n    if (args.candidate && args.candidate.length) {\r\n      // Augment the native candidate with the parsed fields.\r\n      const nativeCandidate = new NativeRTCIceCandidate(args);\r\n      const parsedCandidate = SDPUtils.parseCandidate(args.candidate);\r\n      for (const key in parsedCandidate) {\r\n        if (!(key in nativeCandidate)) {\r\n          Object.defineProperty(nativeCandidate, key,\r\n            {value: parsedCandidate[key]});\r\n        }\r\n      }\r\n\r\n      // Override serializer to not serialize the extra attributes.\r\n      nativeCandidate.toJSON = function toJSON() {\r\n        return {\r\n          candidate: nativeCandidate.candidate,\r\n          sdpMid: nativeCandidate.sdpMid,\r\n          sdpMLineIndex: nativeCandidate.sdpMLineIndex,\r\n          usernameFragment: nativeCandidate.usernameFragment,\r\n        };\r\n      };\r\n      return nativeCandidate;\r\n    }\r\n    return new NativeRTCIceCandidate(args);\r\n  };\r\n  window.RTCIceCandidate.prototype = NativeRTCIceCandidate.prototype;\r\n\r\n  // Hook up the augmented candidate in onicecandidate and\r\n  // addEventListener('icecandidate', ...)\r\n  utils.wrapPeerConnectionEvent(window, 'icecandidate', e => {\r\n    if (e.candidate) {\r\n      Object.defineProperty(e, 'candidate', {\r\n        value: new window.RTCIceCandidate(e.candidate),\r\n        writable: 'false'\r\n      });\r\n    }\r\n    return e;\r\n  });\r\n}\r\n\r\nexport function shimRTCIceCandidateRelayProtocol(window) {\r\n  if (!window.RTCIceCandidate || (window.RTCIceCandidate && 'relayProtocol' in\r\n      window.RTCIceCandidate.prototype)) {\r\n    return;\r\n  }\r\n\r\n  // Hook up the augmented candidate in onicecandidate and\r\n  // addEventListener('icecandidate', ...)\r\n  utils.wrapPeerConnectionEvent(window, 'icecandidate', e => {\r\n    if (e.candidate) {\r\n      const parsedCandidate = SDPUtils.parseCandidate(e.candidate.candidate);\r\n      if (parsedCandidate.type === 'relay') {\r\n        // This is a libwebrtc-specific mapping of local type preference\r\n        // to relayProtocol.\r\n        e.candidate.relayProtocol = {\r\n          0: 'tls',\r\n          1: 'tcp',\r\n          2: 'udp',\r\n        }[parsedCandidate.priority >> 24];\r\n      }\r\n    }\r\n    return e;\r\n  });\r\n}\r\n\r\nexport function shimMaxMessageSize(window, browserDetails) {\r\n  if (!window.RTCPeerConnection) {\r\n    return;\r\n  }\r\n\r\n  if (!('sctp' in window.RTCPeerConnection.prototype)) {\r\n    Object.defineProperty(window.RTCPeerConnection.prototype, 'sctp', {\r\n      get() {\r\n        return typeof this._sctp === 'undefined' ? null : this._sctp;\r\n      }\r\n    });\r\n  }\r\n\r\n  const sctpInDescription = function(description) {\r\n    if (!description || !description.sdp) {\r\n      return false;\r\n    }\r\n    const sections = SDPUtils.splitSections(description.sdp);\r\n    sections.shift();\r\n    return sections.some(mediaSection => {\r\n      const mLine = SDPUtils.parseMLine(mediaSection);\r\n      return mLine && mLine.kind === 'application'\r\n          && mLine.protocol.indexOf('SCTP') !== -1;\r\n    });\r\n  };\r\n\r\n  const getRemoteFirefoxVersion = function(description) {\r\n    // TODO: Is there a better solution for detecting Firefox?\r\n    const match = description.sdp.match(/mozilla...THIS_IS_SDPARTA-(\\d+)/);\r\n    if (match === null || match.length < 2) {\r\n      return -1;\r\n    }\r\n    const version = parseInt(match[1], 10);\r\n    // Test for NaN (yes, this is ugly)\r\n    return version !== version ? -1 : version;\r\n  };\r\n\r\n  const getCanSendMaxMessageSize = function(remoteIsFirefox) {\r\n    // Every implementation we know can send at least 64 KiB.\r\n    // Note: Although Chrome is technically able to send up to 256 KiB, the\r\n    //       data does not reach the other peer reliably.\r\n    //       See: https://bugs.chromium.org/p/webrtc/issues/detail?id=8419\r\n    let canSendMaxMessageSize = 65536;\r\n    if (browserDetails.browser === 'firefox') {\r\n      if (browserDetails.version < 57) {\r\n        if (remoteIsFirefox === -1) {\r\n          // FF < 57 will send in 16 KiB chunks using the deprecated PPID\r\n          // fragmentation.\r\n          canSendMaxMessageSize = 16384;\r\n        } else {\r\n          // However, other FF (and RAWRTC) can reassemble PPID-fragmented\r\n          // messages. Thus, supporting ~2 GiB when sending.\r\n          canSendMaxMessageSize = 2147483637;\r\n        }\r\n      } else if (browserDetails.version < 60) {\r\n        // Currently, all FF >= 57 will reset the remote maximum message size\r\n        // to the default value when a data channel is created at a later\r\n        // stage. :(\r\n        // See: https://bugzilla.mozilla.org/show_bug.cgi?id=1426831\r\n        canSendMaxMessageSize =\r\n          browserDetails.version === 57 ? 65535 : 65536;\r\n      } else {\r\n        // FF >= 60 supports sending ~2 GiB\r\n        canSendMaxMessageSize = 2147483637;\r\n      }\r\n    }\r\n    return canSendMaxMessageSize;\r\n  };\r\n\r\n  const getMaxMessageSize = function(description, remoteIsFirefox) {\r\n    // Note: 65536 bytes is the default value from the SDP spec. Also,\r\n    //       every implementation we know supports receiving 65536 bytes.\r\n    let maxMessageSize = 65536;\r\n\r\n    // FF 57 has a slightly incorrect default remote max message size, so\r\n    // we need to adjust it here to avoid a failure when sending.\r\n    // See: https://bugzilla.mozilla.org/show_bug.cgi?id=1425697\r\n    if (browserDetails.browser === 'firefox'\r\n         && browserDetails.version === 57) {\r\n      maxMessageSize = 65535;\r\n    }\r\n\r\n    const match = SDPUtils.matchPrefix(description.sdp,\r\n      'a=max-message-size:');\r\n    if (match.length > 0) {\r\n      maxMessageSize = parseInt(match[0].substring(19), 10);\r\n    } else if (browserDetails.browser === 'firefox' &&\r\n                remoteIsFirefox !== -1) {\r\n      // If the maximum message size is not present in the remote SDP and\r\n      // both local and remote are Firefox, the remote peer can receive\r\n      // ~2 GiB.\r\n      maxMessageSize = 2147483637;\r\n    }\r\n    return maxMessageSize;\r\n  };\r\n\r\n  const origSetRemoteDescription =\r\n      window.RTCPeerConnection.prototype.setRemoteDescription;\r\n  window.RTCPeerConnection.prototype.setRemoteDescription =\r\n    function setRemoteDescription() {\r\n      this._sctp = null;\r\n      // Chrome decided to not expose .sctp in plan-b mode.\r\n      // As usual, adapter.js has to do an 'ugly worakaround'\r\n      // to cover up the mess.\r\n      if (browserDetails.browser === 'chrome' && browserDetails.version >= 76) {\r\n        const {sdpSemantics} = this.getConfiguration();\r\n        if (sdpSemantics === 'plan-b') {\r\n          Object.defineProperty(this, 'sctp', {\r\n            get() {\r\n              return typeof this._sctp === 'undefined' ? null : this._sctp;\r\n            },\r\n            enumerable: true,\r\n            configurable: true,\r\n          });\r\n        }\r\n      }\r\n\r\n      if (sctpInDescription(arguments[0])) {\r\n        // Check if the remote is FF.\r\n        const isFirefox = getRemoteFirefoxVersion(arguments[0]);\r\n\r\n        // Get the maximum message size the local peer is capable of sending\r\n        const canSendMMS = getCanSendMaxMessageSize(isFirefox);\r\n\r\n        // Get the maximum message size of the remote peer.\r\n        const remoteMMS = getMaxMessageSize(arguments[0], isFirefox);\r\n\r\n        // Determine final maximum message size\r\n        let maxMessageSize;\r\n        if (canSendMMS === 0 && remoteMMS === 0) {\r\n          maxMessageSize = Number.POSITIVE_INFINITY;\r\n        } else if (canSendMMS === 0 || remoteMMS === 0) {\r\n          maxMessageSize = Math.max(canSendMMS, remoteMMS);\r\n        } else {\r\n          maxMessageSize = Math.min(canSendMMS, remoteMMS);\r\n        }\r\n\r\n        // Create a dummy RTCSctpTransport object and the 'maxMessageSize'\r\n        // attribute.\r\n        const sctp = {};\r\n        Object.defineProperty(sctp, 'maxMessageSize', {\r\n          get() {\r\n            return maxMessageSize;\r\n          }\r\n        });\r\n        this._sctp = sctp;\r\n      }\r\n\r\n      return origSetRemoteDescription.apply(this, arguments);\r\n    };\r\n}\r\n\r\nexport function shimSendThrowTypeError(window) {\r\n  if (!(window.RTCPeerConnection &&\r\n      'createDataChannel' in window.RTCPeerConnection.prototype)) {\r\n    return;\r\n  }\r\n\r\n  // Note: Although Firefox >= 57 has a native implementation, the maximum\r\n  //       message size can be reset for all data channels at a later stage.\r\n  //       See: https://bugzilla.mozilla.org/show_bug.cgi?id=1426831\r\n\r\n  function wrapDcSend(dc, pc) {\r\n    const origDataChannelSend = dc.send;\r\n    dc.send = function send() {\r\n      const data = arguments[0];\r\n      const length = data.length || data.size || data.byteLength;\r\n      if (dc.readyState === 'open' &&\r\n          pc.sctp && length > pc.sctp.maxMessageSize) {\r\n        throw new TypeError('Message too large (can send a maximum of ' +\r\n          pc.sctp.maxMessageSize + ' bytes)');\r\n      }\r\n      return origDataChannelSend.apply(dc, arguments);\r\n    };\r\n  }\r\n  const origCreateDataChannel =\r\n    window.RTCPeerConnection.prototype.createDataChannel;\r\n  window.RTCPeerConnection.prototype.createDataChannel =\r\n    function createDataChannel() {\r\n      const dataChannel = origCreateDataChannel.apply(this, arguments);\r\n      wrapDcSend(dataChannel, this);\r\n      return dataChannel;\r\n    };\r\n  utils.wrapPeerConnectionEvent(window, 'datachannel', e => {\r\n    wrapDcSend(e.channel, e.target);\r\n    return e;\r\n  });\r\n}\r\n\r\n\r\n/* shims RTCConnectionState by pretending it is the same as iceConnectionState.\r\n * See https://bugs.chromium.org/p/webrtc/issues/detail?id=6145#c12\r\n * for why this is a valid hack in Chrome. In Firefox it is slightly incorrect\r\n * since DTLS failures would be hidden. See\r\n * https://bugzilla.mozilla.org/show_bug.cgi?id=1265827\r\n * for the Firefox tracking bug.\r\n */\r\nexport function shimConnectionState(window) {\r\n  if (!window.RTCPeerConnection ||\r\n      'connectionState' in window.RTCPeerConnection.prototype) {\r\n    return;\r\n  }\r\n  const proto = window.RTCPeerConnection.prototype;\r\n  Object.defineProperty(proto, 'connectionState', {\r\n    get() {\r\n      return {\r\n        completed: 'connected',\r\n        checking: 'connecting'\r\n      }[this.iceConnectionState] || this.iceConnectionState;\r\n    },\r\n    enumerable: true,\r\n    configurable: true\r\n  });\r\n  Object.defineProperty(proto, 'onconnectionstatechange', {\r\n    get() {\r\n      return this._onconnectionstatechange || null;\r\n    },\r\n    set(cb) {\r\n      if (this._onconnectionstatechange) {\r\n        this.removeEventListener('connectionstatechange',\r\n          this._onconnectionstatechange);\r\n        delete this._onconnectionstatechange;\r\n      }\r\n      if (cb) {\r\n        this.addEventListener('connectionstatechange',\r\n          this._onconnectionstatechange = cb);\r\n      }\r\n    },\r\n    enumerable: true,\r\n    configurable: true\r\n  });\r\n\r\n  ['setLocalDescription', 'setRemoteDescription'].forEach((method) => {\r\n    const origMethod = proto[method];\r\n    proto[method] = function() {\r\n      if (!this._connectionstatechangepoly) {\r\n        this._connectionstatechangepoly = e => {\r\n          const pc = e.target;\r\n          if (pc._lastConnectionState !== pc.connectionState) {\r\n            pc._lastConnectionState = pc.connectionState;\r\n            const newEvent = new Event('connectionstatechange', e);\r\n            pc.dispatchEvent(newEvent);\r\n          }\r\n          return e;\r\n        };\r\n        this.addEventListener('iceconnectionstatechange',\r\n          this._connectionstatechangepoly);\r\n      }\r\n      return origMethod.apply(this, arguments);\r\n    };\r\n  });\r\n}\r\n\r\nexport function removeExtmapAllowMixed(window, browserDetails) {\r\n  /* remove a=extmap-allow-mixed for webrtc.org < M71 */\r\n  if (!window.RTCPeerConnection) {\r\n    return;\r\n  }\r\n  if (browserDetails.browser === 'chrome' && browserDetails.version >= 71) {\r\n    return;\r\n  }\r\n  if (browserDetails.browser === 'safari' &&\r\n      browserDetails._safariVersion >= 13.1) {\r\n    return;\r\n  }\r\n  const nativeSRD = window.RTCPeerConnection.prototype.setRemoteDescription;\r\n  window.RTCPeerConnection.prototype.setRemoteDescription =\r\n  function setRemoteDescription(desc) {\r\n    if (desc && desc.sdp && desc.sdp.indexOf('\\na=extmap-allow-mixed') !== -1) {\r\n      const sdp = desc.sdp.split('\\n').filter((line) => {\r\n        return line.trim() !== 'a=extmap-allow-mixed';\r\n      }).join('\\n');\r\n      // Safari enforces read-only-ness of RTCSessionDescription fields.\r\n      if (window.RTCSessionDescription &&\r\n          desc instanceof window.RTCSessionDescription) {\r\n        arguments[0] = new window.RTCSessionDescription({\r\n          type: desc.type,\r\n          sdp,\r\n        });\r\n      } else {\r\n        desc.sdp = sdp;\r\n      }\r\n    }\r\n    return nativeSRD.apply(this, arguments);\r\n  };\r\n}\r\n\r\nexport function shimAddIceCandidateNullOrEmpty(window, browserDetails) {\r\n  // Support for addIceCandidate(null or undefined)\r\n  // as well as addIceCandidate({candidate: \"\", ...})\r\n  // https://bugs.chromium.org/p/chromium/issues/detail?id=978582\r\n  // Note: must be called before other polyfills which change the signature.\r\n  if (!(window.RTCPeerConnection && window.RTCPeerConnection.prototype)) {\r\n    return;\r\n  }\r\n  const nativeAddIceCandidate =\r\n      window.RTCPeerConnection.prototype.addIceCandidate;\r\n  if (!nativeAddIceCandidate || nativeAddIceCandidate.length === 0) {\r\n    return;\r\n  }\r\n  window.RTCPeerConnection.prototype.addIceCandidate =\r\n    function addIceCandidate() {\r\n      if (!arguments[0]) {\r\n        if (arguments[1]) {\r\n          arguments[1].apply(null);\r\n        }\r\n        return Promise.resolve();\r\n      }\r\n      // Firefox 68+ emits and processes {candidate: \"\", ...}, ignore\r\n      // in older versions.\r\n      // Native support for ignoring exists for Chrome M77+.\r\n      // Safari ignores as well, exact version unknown but works in the same\r\n      // version that also ignores addIceCandidate(null).\r\n      if (((browserDetails.browser === 'chrome' && browserDetails.version < 78)\r\n           || (browserDetails.browser === 'firefox'\r\n               && browserDetails.version < 68)\r\n           || (browserDetails.browser === 'safari'))\r\n          && arguments[0] && arguments[0].candidate === '') {\r\n        return Promise.resolve();\r\n      }\r\n      return nativeAddIceCandidate.apply(this, arguments);\r\n    };\r\n}\r\n\r\n// Note: Make sure to call this ahead of APIs that modify\r\n// setLocalDescription.length\r\nexport function shimParameterlessSetLocalDescription(window, browserDetails) {\r\n  if (!(window.RTCPeerConnection && window.RTCPeerConnection.prototype)) {\r\n    return;\r\n  }\r\n  const nativeSetLocalDescription =\r\n      window.RTCPeerConnection.prototype.setLocalDescription;\r\n  if (!nativeSetLocalDescription || nativeSetLocalDescription.length === 0) {\r\n    return;\r\n  }\r\n  window.RTCPeerConnection.prototype.setLocalDescription =\r\n    function setLocalDescription() {\r\n      let desc = arguments[0] || {};\r\n      if (typeof desc !== 'object' || (desc.type && desc.sdp)) {\r\n        return nativeSetLocalDescription.apply(this, arguments);\r\n      }\r\n      // The remaining steps should technically happen when SLD comes off the\r\n      // RTCPeerConnection's operations chain (not ahead of going on it), but\r\n      // this is too difficult to shim. Instead, this shim only covers the\r\n      // common case where the operations chain is empty. This is imperfect, but\r\n      // should cover many cases. Rationale: Even if we can't reduce the glare\r\n      // window to zero on imperfect implementations, there's value in tapping\r\n      // into the perfect negotiation pattern that several browsers support.\r\n      desc = {type: desc.type, sdp: desc.sdp};\r\n      if (!desc.type) {\r\n        switch (this.signalingState) {\r\n          case 'stable':\r\n          case 'have-local-offer':\r\n          case 'have-remote-pranswer':\r\n            desc.type = 'offer';\r\n            break;\r\n          default:\r\n            desc.type = 'answer';\r\n            break;\r\n        }\r\n      }\r\n      if (desc.sdp || (desc.type !== 'offer' && desc.type !== 'answer')) {\r\n        return nativeSetLocalDescription.apply(this, [desc]);\r\n      }\r\n      const func = desc.type === 'offer' ? this.createOffer : this.createAnswer;\r\n      return func.apply(this)\r\n        .then(d => nativeSetLocalDescription.apply(this, [d]));\r\n    };\r\n}\r\n", "/*\r\n *  Copyright (c) 2016 The WebRTC project authors. All Rights Reserved.\r\n *\r\n *  Use of this source code is governed by a BSD-style license\r\n *  that can be found in the LICENSE file in the root of the source\r\n *  tree.\r\n */\r\nimport * as utils from './utils';\r\n\r\n// Browser shims.\r\nimport * as chromeShim from './chrome/chrome_shim';\r\nimport * as firefoxShim from './firefox/firefox_shim';\r\nimport * as safariShim from './safari/safari_shim';\r\nimport * as commonShim from './common_shim';\r\nimport * as sdp from 'sdp';\r\n\r\n// Shimming starts here.\r\nexport function adapterFactory({window} = {}, options = {\r\n  shimChrome: true,\r\n  shimFirefox: true,\r\n  shimSafari: true,\r\n}) {\r\n  // Utils.\r\n  const logging = utils.log;\r\n  const browserDetails = utils.detectBrowser(window);\r\n\r\n  const adapter = {\r\n    browserDetails,\r\n    commonShim,\r\n    extractVersion: utils.extractVersion,\r\n    disableLog: utils.disableLog,\r\n    disableWarnings: utils.disableWarnings,\r\n    // Expose sdp as a convenience. For production apps include directly.\r\n    sdp,\r\n  };\r\n\r\n  // Shim browser if found.\r\n  switch (browserDetails.browser) {\r\n    case 'chrome':\r\n      if (!chromeShim || !chromeShim.shimPeerConnection ||\r\n          !options.shimChrome) {\r\n        logging('Chrome shim is not included in this adapter release.');\r\n        return adapter;\r\n      }\r\n      if (browserDetails.version === null) {\r\n        logging('Chrome shim can not determine version, not shimming.');\r\n        return adapter;\r\n      }\r\n      logging('adapter.js shimming chrome.');\r\n      // Export to the adapter global object visible in the browser.\r\n      adapter.browserShim = chromeShim;\r\n\r\n      // Must be called before shimPeerConnection.\r\n      commonShim.shimAddIceCandidateNullOrEmpty(window, browserDetails);\r\n      commonShim.shimParameterlessSetLocalDescription(window, browserDetails);\r\n\r\n      chromeShim.shimGetUserMedia(window, browserDetails);\r\n      chromeShim.shimMediaStream(window, browserDetails);\r\n      chromeShim.shimPeerConnection(window, browserDetails);\r\n      chromeShim.shimOnTrack(window, browserDetails);\r\n      chromeShim.shimAddTrackRemoveTrack(window, browserDetails);\r\n      chromeShim.shimGetSendersWithDtmf(window, browserDetails);\r\n      chromeShim.shimGetStats(window, browserDetails);\r\n      chromeShim.shimSenderReceiverGetStats(window, browserDetails);\r\n      chromeShim.fixNegotiationNeeded(window, browserDetails);\r\n\r\n      commonShim.shimRTCIceCandidate(window, browserDetails);\r\n      commonShim.shimRTCIceCandidateRelayProtocol(window, browserDetails);\r\n      commonShim.shimConnectionState(window, browserDetails);\r\n      commonShim.shimMaxMessageSize(window, browserDetails);\r\n      commonShim.shimSendThrowTypeError(window, browserDetails);\r\n      commonShim.removeExtmapAllowMixed(window, browserDetails);\r\n      break;\r\n    case 'firefox':\r\n      if (!firefoxShim || !firefoxShim.shimPeerConnection ||\r\n          !options.shimFirefox) {\r\n        logging('Firefox shim is not included in this adapter release.');\r\n        return adapter;\r\n      }\r\n      logging('adapter.js shimming firefox.');\r\n      // Export to the adapter global object visible in the browser.\r\n      adapter.browserShim = firefoxShim;\r\n\r\n      // Must be called before shimPeerConnection.\r\n      commonShim.shimAddIceCandidateNullOrEmpty(window, browserDetails);\r\n      commonShim.shimParameterlessSetLocalDescription(window, browserDetails);\r\n\r\n      firefoxShim.shimGetUserMedia(window, browserDetails);\r\n      firefoxShim.shimPeerConnection(window, browserDetails);\r\n      firefoxShim.shimOnTrack(window, browserDetails);\r\n      firefoxShim.shimRemoveStream(window, browserDetails);\r\n      firefoxShim.shimSenderGetStats(window, browserDetails);\r\n      firefoxShim.shimReceiverGetStats(window, browserDetails);\r\n      firefoxShim.shimRTCDataChannel(window, browserDetails);\r\n      firefoxShim.shimAddTransceiver(window, browserDetails);\r\n      firefoxShim.shimGetParameters(window, browserDetails);\r\n      firefoxShim.shimCreateOffer(window, browserDetails);\r\n      firefoxShim.shimCreateAnswer(window, browserDetails);\r\n\r\n      commonShim.shimRTCIceCandidate(window, browserDetails);\r\n      commonShim.shimConnectionState(window, browserDetails);\r\n      commonShim.shimMaxMessageSize(window, browserDetails);\r\n      commonShim.shimSendThrowTypeError(window, browserDetails);\r\n      break;\r\n    case 'safari':\r\n      if (!safariShim || !options.shimSafari) {\r\n        logging('Safari shim is not included in this adapter release.');\r\n        return adapter;\r\n      }\r\n      logging('adapter.js shimming safari.');\r\n      // Export to the adapter global object visible in the browser.\r\n      adapter.browserShim = safariShim;\r\n\r\n      // Must be called before shimCallbackAPI.\r\n      commonShim.shimAddIceCandidateNullOrEmpty(window, browserDetails);\r\n      commonShim.shimParameterlessSetLocalDescription(window, browserDetails);\r\n\r\n      safariShim.shimRTCIceServerUrls(window, browserDetails);\r\n      safariShim.shimCreateOfferLegacy(window, browserDetails);\r\n      safariShim.shimCallbacksAPI(window, browserDetails);\r\n      safariShim.shimLocalStreamsAPI(window, browserDetails);\r\n      safariShim.shimRemoteStreamsAPI(window, browserDetails);\r\n      safariShim.shimTrackEventTransceiver(window, browserDetails);\r\n      safariShim.shimGetUserMedia(window, browserDetails);\r\n      safariShim.shimAudioContext(window, browserDetails);\r\n\r\n      commonShim.shimRTCIceCandidate(window, browserDetails);\r\n      commonShim.shimRTCIceCandidateRelayProtocol(window, browserDetails);\r\n      commonShim.shimMaxMessageSize(window, browserDetails);\r\n      commonShim.shimSendThrowTypeError(window, browserDetails);\r\n      commonShim.removeExtmapAllowMixed(window, browserDetails);\r\n      break;\r\n    default:\r\n      logging('Unsupported browser!');\r\n      break;\r\n  }\r\n\r\n  return adapter;\r\n}\r\n", "/*\r\n *  Copyright (c) 2016 The WebRTC project authors. All Rights Reserved.\r\n *\r\n *  Use of this source code is governed by a BSD-style license\r\n *  that can be found in the LICENSE file in the root of the source\r\n *  tree.\r\n */\r\n/* eslint-env node */\r\n\r\n'use strict';\r\n\r\nimport {adapterFactory} from './adapter_factory.js';\r\n\r\nconst adapter =\r\n  adapterFactory({window: typeof window === 'undefined' ? undefined : window});\r\nexport default adapter;\r\n"], "mappings": ";;;;;;;AAAA;AAAA;AAAA;AAIA,QAAMA,YAAW,CAAC;AAIlB,IAAAA,UAAS,qBAAqB,WAAW;AACvC,aAAO,KAAK,OAAO,EAAE,SAAS,EAAE,EAAE,UAAU,GAAG,EAAE;AAAA,IACnD;AAGA,IAAAA,UAAS,aAAaA,UAAS,mBAAmB;AAGlD,IAAAA,UAAS,aAAa,SAAS,MAAM;AACnC,aAAO,KAAK,KAAK,EAAE,MAAM,IAAI,EAAE,IAAI,UAAQ,KAAK,KAAK,CAAC;AAAA,IACxD;AAEA,IAAAA,UAAS,gBAAgB,SAAS,MAAM;AACtC,YAAM,QAAQ,KAAK,MAAM,MAAM;AAC/B,aAAO,MAAM,IAAI,CAAC,MAAM,WAAW,QAAQ,IACzC,OAAO,OAAO,MAAM,KAAK,IAAI,MAAM;AAAA,IACvC;AAGA,IAAAA,UAAS,iBAAiB,SAAS,MAAM;AACvC,YAAM,WAAWA,UAAS,cAAc,IAAI;AAC5C,aAAO,YAAY,SAAS,CAAC;AAAA,IAC/B;AAGA,IAAAA,UAAS,mBAAmB,SAAS,MAAM;AACzC,YAAM,WAAWA,UAAS,cAAc,IAAI;AAC5C,eAAS,MAAM;AACf,aAAO;AAAA,IACT;AAGA,IAAAA,UAAS,cAAc,SAAS,MAAM,QAAQ;AAC5C,aAAOA,UAAS,WAAW,IAAI,EAAE,OAAO,UAAQ,KAAK,QAAQ,MAAM,MAAM,CAAC;AAAA,IAC5E;AAMA,IAAAA,UAAS,iBAAiB,SAAS,MAAM;AACvC,UAAI;AAEJ,UAAI,KAAK,QAAQ,cAAc,MAAM,GAAG;AACtC,gBAAQ,KAAK,UAAU,EAAE,EAAE,MAAM,GAAG;AAAA,MACtC,OAAO;AACL,gBAAQ,KAAK,UAAU,EAAE,EAAE,MAAM,GAAG;AAAA,MACtC;AAEA,YAAM,YAAY;AAAA,QAChB,YAAY,MAAM,CAAC;AAAA,QACnB,WAAW,EAAC,GAAG,OAAO,GAAG,OAAM,EAAE,MAAM,CAAC,CAAC,KAAK,MAAM,CAAC;AAAA,QACrD,UAAU,MAAM,CAAC,EAAE,YAAY;AAAA,QAC/B,UAAU,SAAS,MAAM,CAAC,GAAG,EAAE;AAAA,QAC/B,IAAI,MAAM,CAAC;AAAA,QACX,SAAS,MAAM,CAAC;AAAA;AAAA,QAChB,MAAM,SAAS,MAAM,CAAC,GAAG,EAAE;AAAA;AAAA,QAE3B,MAAM,MAAM,CAAC;AAAA,MACf;AAEA,eAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK,GAAG;AACxC,gBAAQ,MAAM,CAAC,GAAG;AAAA,UAChB,KAAK;AACH,sBAAU,iBAAiB,MAAM,IAAI,CAAC;AACtC;AAAA,UACF,KAAK;AACH,sBAAU,cAAc,SAAS,MAAM,IAAI,CAAC,GAAG,EAAE;AACjD;AAAA,UACF,KAAK;AACH,sBAAU,UAAU,MAAM,IAAI,CAAC;AAC/B;AAAA,UACF,KAAK;AACH,sBAAU,QAAQ,MAAM,IAAI,CAAC;AAC7B,sBAAU,mBAAmB,MAAM,IAAI,CAAC;AACxC;AAAA,UACF;AACE,gBAAI,UAAU,MAAM,CAAC,CAAC,MAAM,QAAW;AACrC,wBAAU,MAAM,CAAC,CAAC,IAAI,MAAM,IAAI,CAAC;AAAA,YACnC;AACA;AAAA,QACJ;AAAA,MACF;AACA,aAAO;AAAA,IACT;AAIA,IAAAA,UAAS,iBAAiB,SAAS,WAAW;AAC5C,YAAMC,OAAM,CAAC;AACb,MAAAA,KAAI,KAAK,UAAU,UAAU;AAE7B,YAAM,YAAY,UAAU;AAC5B,UAAI,cAAc,OAAO;AACvB,QAAAA,KAAI,KAAK,CAAC;AAAA,MACZ,WAAW,cAAc,QAAQ;AAC/B,QAAAA,KAAI,KAAK,CAAC;AAAA,MACZ,OAAO;AACL,QAAAA,KAAI,KAAK,SAAS;AAAA,MACpB;AACA,MAAAA,KAAI,KAAK,UAAU,SAAS,YAAY,CAAC;AACzC,MAAAA,KAAI,KAAK,UAAU,QAAQ;AAC3B,MAAAA,KAAI,KAAK,UAAU,WAAW,UAAU,EAAE;AAC1C,MAAAA,KAAI,KAAK,UAAU,IAAI;AAEvB,YAAM,OAAO,UAAU;AACvB,MAAAA,KAAI,KAAK,KAAK;AACd,MAAAA,KAAI,KAAK,IAAI;AACb,UAAI,SAAS,UAAU,UAAU,kBAC7B,UAAU,aAAa;AACzB,QAAAA,KAAI,KAAK,OAAO;AAChB,QAAAA,KAAI,KAAK,UAAU,cAAc;AACjC,QAAAA,KAAI,KAAK,OAAO;AAChB,QAAAA,KAAI,KAAK,UAAU,WAAW;AAAA,MAChC;AACA,UAAI,UAAU,WAAW,UAAU,SAAS,YAAY,MAAM,OAAO;AACnE,QAAAA,KAAI,KAAK,SAAS;AAClB,QAAAA,KAAI,KAAK,UAAU,OAAO;AAAA,MAC5B;AACA,UAAI,UAAU,oBAAoB,UAAU,OAAO;AACjD,QAAAA,KAAI,KAAK,OAAO;AAChB,QAAAA,KAAI,KAAK,UAAU,oBAAoB,UAAU,KAAK;AAAA,MACxD;AACA,aAAO,eAAeA,KAAI,KAAK,GAAG;AAAA,IACpC;AAKA,IAAAD,UAAS,kBAAkB,SAAS,MAAM;AACxC,aAAO,KAAK,UAAU,EAAE,EAAE,MAAM,GAAG;AAAA,IACrC;AAIA,IAAAA,UAAS,cAAc,SAAS,MAAM;AACpC,UAAI,QAAQ,KAAK,UAAU,CAAC,EAAE,MAAM,GAAG;AACvC,YAAM,SAAS;AAAA,QACb,aAAa,SAAS,MAAM,MAAM,GAAG,EAAE;AAAA;AAAA,MACzC;AAEA,cAAQ,MAAM,CAAC,EAAE,MAAM,GAAG;AAE1B,aAAO,OAAO,MAAM,CAAC;AACrB,aAAO,YAAY,SAAS,MAAM,CAAC,GAAG,EAAE;AACxC,aAAO,WAAW,MAAM,WAAW,IAAI,SAAS,MAAM,CAAC,GAAG,EAAE,IAAI;AAEhE,aAAO,cAAc,OAAO;AAC5B,aAAO;AAAA,IACT;AAIA,IAAAA,UAAS,cAAc,SAAS,OAAO;AACrC,UAAI,KAAK,MAAM;AACf,UAAI,MAAM,yBAAyB,QAAW;AAC5C,aAAK,MAAM;AAAA,MACb;AACA,YAAM,WAAW,MAAM,YAAY,MAAM,eAAe;AACxD,aAAO,cAAc,KAAK,MAAM,MAAM,OAAO,MAAM,MAAM,aACpD,aAAa,IAAI,MAAM,WAAW,MAAM;AAAA,IAC/C;AAKA,IAAAA,UAAS,cAAc,SAAS,MAAM;AACpC,YAAM,QAAQ,KAAK,UAAU,CAAC,EAAE,MAAM,GAAG;AACzC,aAAO;AAAA,QACL,IAAI,SAAS,MAAM,CAAC,GAAG,EAAE;AAAA,QACzB,WAAW,MAAM,CAAC,EAAE,QAAQ,GAAG,IAAI,IAAI,MAAM,CAAC,EAAE,MAAM,GAAG,EAAE,CAAC,IAAI;AAAA,QAChE,KAAK,MAAM,CAAC;AAAA,QACZ,YAAY,MAAM,MAAM,CAAC,EAAE,KAAK,GAAG;AAAA,MACrC;AAAA,IACF;AAIA,IAAAA,UAAS,cAAc,SAAS,iBAAiB;AAC/C,aAAO,eAAe,gBAAgB,MAAM,gBAAgB,gBACvD,gBAAgB,aAAa,gBAAgB,cAAc,aACxD,MAAM,gBAAgB,YACtB,MACJ,MAAM,gBAAgB,OACrB,gBAAgB,aAAa,MAAM,gBAAgB,aAAa,MACjE;AAAA,IACN;AAOA,IAAAA,UAAS,YAAY,SAAS,MAAM;AAClC,YAAM,SAAS,CAAC;AAChB,UAAI;AACJ,YAAM,QAAQ,KAAK,UAAU,KAAK,QAAQ,GAAG,IAAI,CAAC,EAAE,MAAM,GAAG;AAC7D,eAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACrC,aAAK,MAAM,CAAC,EAAE,KAAK,EAAE,MAAM,GAAG;AAC9B,eAAO,GAAG,CAAC,EAAE,KAAK,CAAC,IAAI,GAAG,CAAC;AAAA,MAC7B;AACA,aAAO;AAAA,IACT;AAGA,IAAAA,UAAS,YAAY,SAAS,OAAO;AACnC,UAAI,OAAO;AACX,UAAI,KAAK,MAAM;AACf,UAAI,MAAM,yBAAyB,QAAW;AAC5C,aAAK,MAAM;AAAA,MACb;AACA,UAAI,MAAM,cAAc,OAAO,KAAK,MAAM,UAAU,EAAE,QAAQ;AAC5D,cAAM,SAAS,CAAC;AAChB,eAAO,KAAK,MAAM,UAAU,EAAE,QAAQ,WAAS;AAC7C,cAAI,MAAM,WAAW,KAAK,MAAM,QAAW;AACzC,mBAAO,KAAK,QAAQ,MAAM,MAAM,WAAW,KAAK,CAAC;AAAA,UACnD,OAAO;AACL,mBAAO,KAAK,KAAK;AAAA,UACnB;AAAA,QACF,CAAC;AACD,gBAAQ,YAAY,KAAK,MAAM,OAAO,KAAK,GAAG,IAAI;AAAA,MACpD;AACA,aAAO;AAAA,IACT;AAIA,IAAAA,UAAS,cAAc,SAAS,MAAM;AACpC,YAAM,QAAQ,KAAK,UAAU,KAAK,QAAQ,GAAG,IAAI,CAAC,EAAE,MAAM,GAAG;AAC7D,aAAO;AAAA,QACL,MAAM,MAAM,MAAM;AAAA,QAClB,WAAW,MAAM,KAAK,GAAG;AAAA,MAC3B;AAAA,IACF;AAGA,IAAAA,UAAS,cAAc,SAAS,OAAO;AACrC,UAAI,QAAQ;AACZ,UAAI,KAAK,MAAM;AACf,UAAI,MAAM,yBAAyB,QAAW;AAC5C,aAAK,MAAM;AAAA,MACb;AACA,UAAI,MAAM,gBAAgB,MAAM,aAAa,QAAQ;AAEnD,cAAM,aAAa,QAAQ,QAAM;AAC/B,mBAAS,eAAe,KAAK,MAAM,GAAG,QACrC,GAAG,aAAa,GAAG,UAAU,SAAS,MAAM,GAAG,YAAY,MACxD;AAAA,QACN,CAAC;AAAA,MACH;AACA,aAAO;AAAA,IACT;AAIA,IAAAA,UAAS,iBAAiB,SAAS,MAAM;AACvC,YAAM,KAAK,KAAK,QAAQ,GAAG;AAC3B,YAAM,QAAQ;AAAA,QACZ,MAAM,SAAS,KAAK,UAAU,GAAG,EAAE,GAAG,EAAE;AAAA,MAC1C;AACA,YAAM,QAAQ,KAAK,QAAQ,KAAK,EAAE;AAClC,UAAI,QAAQ,IAAI;AACd,cAAM,YAAY,KAAK,UAAU,KAAK,GAAG,KAAK;AAC9C,cAAM,QAAQ,KAAK,UAAU,QAAQ,CAAC;AAAA,MACxC,OAAO;AACL,cAAM,YAAY,KAAK,UAAU,KAAK,CAAC;AAAA,MACzC;AACA,aAAO;AAAA,IACT;AAIA,IAAAA,UAAS,iBAAiB,SAAS,MAAM;AACvC,YAAM,QAAQ,KAAK,UAAU,EAAE,EAAE,MAAM,GAAG;AAC1C,aAAO;AAAA,QACL,WAAW,MAAM,MAAM;AAAA,QACvB,OAAO,MAAM,IAAI,UAAQ,SAAS,MAAM,EAAE,CAAC;AAAA,MAC7C;AAAA,IACF;AAIA,IAAAA,UAAS,SAAS,SAAS,cAAc;AACvC,YAAM,MAAMA,UAAS,YAAY,cAAc,QAAQ,EAAE,CAAC;AAC1D,UAAI,KAAK;AACP,eAAO,IAAI,UAAU,CAAC;AAAA,MACxB;AAAA,IACF;AAGA,IAAAA,UAAS,mBAAmB,SAAS,MAAM;AACzC,YAAM,QAAQ,KAAK,UAAU,EAAE,EAAE,MAAM,GAAG;AAC1C,aAAO;AAAA,QACL,WAAW,MAAM,CAAC,EAAE,YAAY;AAAA;AAAA,QAChC,OAAO,MAAM,CAAC,EAAE,YAAY;AAAA;AAAA,MAC9B;AAAA,IACF;AAKA,IAAAA,UAAS,oBAAoB,SAAS,cAAc,aAAa;AAC/D,YAAM,QAAQA,UAAS;AAAA,QAAY,eAAe;AAAA,QAChD;AAAA,MAAgB;AAElB,aAAO;AAAA,QACL,MAAM;AAAA,QACN,cAAc,MAAM,IAAIA,UAAS,gBAAgB;AAAA,MACnD;AAAA,IACF;AAGA,IAAAA,UAAS,sBAAsB,SAAS,QAAQ,WAAW;AACzD,UAAIC,OAAM,aAAa,YAAY;AACnC,aAAO,aAAa,QAAQ,QAAM;AAChC,QAAAA,QAAO,mBAAmB,GAAG,YAAY,MAAM,GAAG,QAAQ;AAAA,MAC5D,CAAC;AACD,aAAOA;AAAA,IACT;AAIA,IAAAD,UAAS,kBAAkB,SAAS,MAAM;AACxC,YAAM,QAAQ,KAAK,UAAU,CAAC,EAAE,MAAM,GAAG;AACzC,aAAO;AAAA,QACL,KAAK,SAAS,MAAM,CAAC,GAAG,EAAE;AAAA,QAC1B,aAAa,MAAM,CAAC;AAAA,QACpB,WAAW,MAAM,CAAC;AAAA,QAClB,eAAe,MAAM,MAAM,CAAC;AAAA,MAC9B;AAAA,IACF;AAEA,IAAAA,UAAS,kBAAkB,SAAS,YAAY;AAC9C,aAAO,cAAc,WAAW,MAAM,MACpC,WAAW,cAAc,OACxB,OAAO,WAAW,cAAc,WAC7BA,UAAS,qBAAqB,WAAW,SAAS,IAClD,WAAW,cACd,WAAW,gBAAgB,MAAM,WAAW,cAAc,KAAK,GAAG,IAAI,MACvE;AAAA,IACJ;AAIA,IAAAA,UAAS,uBAAuB,SAAS,WAAW;AAClD,UAAI,UAAU,QAAQ,SAAS,MAAM,GAAG;AACtC,eAAO;AAAA,MACT;AACA,YAAM,QAAQ,UAAU,UAAU,CAAC,EAAE,MAAM,GAAG;AAC9C,aAAO;AAAA,QACL,WAAW;AAAA,QACX,SAAS,MAAM,CAAC;AAAA,QAChB,UAAU,MAAM,CAAC;AAAA,QACjB,UAAU,MAAM,CAAC,IAAI,MAAM,CAAC,EAAE,MAAM,GAAG,EAAE,CAAC,IAAI;AAAA,QAC9C,WAAW,MAAM,CAAC,IAAI,MAAM,CAAC,EAAE,MAAM,GAAG,EAAE,CAAC,IAAI;AAAA,MACjD;AAAA,IACF;AAEA,IAAAA,UAAS,uBAAuB,SAAS,WAAW;AAClD,aAAO,UAAU,YAAY,MACzB,UAAU,WACX,UAAU,WAAW,MAAM,UAAU,WAAW,OAChD,UAAU,YAAY,UAAU,YAC7B,MAAM,UAAU,WAAW,MAAM,UAAU,YAC3C;AAAA,IACR;AAGA,IAAAA,UAAS,sBAAsB,SAAS,cAAc,aAAa;AACjE,YAAM,QAAQA,UAAS;AAAA,QAAY,eAAe;AAAA,QAChD;AAAA,MAAW;AACb,aAAO,MAAM,IAAIA,UAAS,eAAe;AAAA,IAC3C;AAKA,IAAAA,UAAS,mBAAmB,SAAS,cAAc,aAAa;AAC9D,YAAM,QAAQA,UAAS;AAAA,QAAY,eAAe;AAAA,QAChD;AAAA,MAAc,EAAE,CAAC;AACnB,YAAM,MAAMA,UAAS;AAAA,QAAY,eAAe;AAAA,QAC9C;AAAA,MAAY,EAAE,CAAC;AACjB,UAAI,EAAE,SAAS,MAAM;AACnB,eAAO;AAAA,MACT;AACA,aAAO;AAAA,QACL,kBAAkB,MAAM,UAAU,EAAE;AAAA,QACpC,UAAU,IAAI,UAAU,EAAE;AAAA,MAC5B;AAAA,IACF;AAGA,IAAAA,UAAS,qBAAqB,SAAS,QAAQ;AAC7C,UAAIC,OAAM,iBAAiB,OAAO,mBAAmB,mBAClC,OAAO,WAAW;AACrC,UAAI,OAAO,SAAS;AAClB,QAAAA,QAAO;AAAA,MACT;AACA,aAAOA;AAAA,IACT;AAGA,IAAAD,UAAS,qBAAqB,SAAS,cAAc;AACnD,YAAM,cAAc;AAAA,QAClB,QAAQ,CAAC;AAAA,QACT,kBAAkB,CAAC;AAAA,QACnB,eAAe,CAAC;AAAA,QAChB,MAAM,CAAC;AAAA,MACT;AACA,YAAM,QAAQA,UAAS,WAAW,YAAY;AAC9C,YAAM,QAAQ,MAAM,CAAC,EAAE,MAAM,GAAG;AAChC,kBAAY,UAAU,MAAM,CAAC;AAC7B,eAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACrC,cAAM,KAAK,MAAM,CAAC;AAClB,cAAM,aAAaA,UAAS;AAAA,UAC1B;AAAA,UAAc,cAAc,KAAK;AAAA,QAAG,EAAE,CAAC;AACzC,YAAI,YAAY;AACd,gBAAM,QAAQA,UAAS,YAAY,UAAU;AAC7C,gBAAM,QAAQA,UAAS;AAAA,YACrB;AAAA,YAAc,YAAY,KAAK;AAAA,UAAG;AAEpC,gBAAM,aAAa,MAAM,SAASA,UAAS,UAAU,MAAM,CAAC,CAAC,IAAI,CAAC;AAClE,gBAAM,eAAeA,UAAS;AAAA,YAC5B;AAAA,YAAc,eAAe,KAAK;AAAA,UAAG,EACpC,IAAIA,UAAS,WAAW;AAC3B,sBAAY,OAAO,KAAK,KAAK;AAE7B,kBAAQ,MAAM,KAAK,YAAY,GAAG;AAAA,YAChC,KAAK;AAAA,YACL,KAAK;AACH,0BAAY,cAAc,KAAK,MAAM,KAAK,YAAY,CAAC;AACvD;AAAA,YACF;AACE;AAAA,UACJ;AAAA,QACF;AAAA,MACF;AACA,MAAAA,UAAS,YAAY,cAAc,WAAW,EAAE,QAAQ,UAAQ;AAC9D,oBAAY,iBAAiB,KAAKA,UAAS,YAAY,IAAI,CAAC;AAAA,MAC9D,CAAC;AACD,YAAM,iBAAiBA,UAAS,YAAY,cAAc,cAAc,EACrE,IAAIA,UAAS,WAAW;AAC3B,kBAAY,OAAO,QAAQ,WAAS;AAClC,uBAAe,QAAQ,QAAK;AAC1B,gBAAM,YAAY,MAAM,aAAa,KAAK,sBAAoB;AAC5D,mBAAO,iBAAiB,SAAS,GAAG,QAClC,iBAAiB,cAAc,GAAG;AAAA,UACtC,CAAC;AACD,cAAI,CAAC,WAAW;AACd,kBAAM,aAAa,KAAK,EAAE;AAAA,UAC5B;AAAA,QACF,CAAC;AAAA,MACH,CAAC;AAED,aAAO;AAAA,IACT;AAIA,IAAAA,UAAS,sBAAsB,SAAS,MAAM,MAAM;AAClD,UAAIC,OAAM;AAGV,MAAAA,QAAO,OAAO,OAAO;AACrB,MAAAA,QAAO,KAAK,OAAO,SAAS,IAAI,MAAM;AACtC,MAAAA,QAAO,OAAO,KAAK,WAAW,uBAAuB;AACrD,MAAAA,QAAO,KAAK,OAAO,IAAI,WAAS;AAC9B,YAAI,MAAM,yBAAyB,QAAW;AAC5C,iBAAO,MAAM;AAAA,QACf;AACA,eAAO,MAAM;AAAA,MACf,CAAC,EAAE,KAAK,GAAG,IAAI;AAEf,MAAAA,QAAO;AACP,MAAAA,QAAO;AAGP,WAAK,OAAO,QAAQ,WAAS;AAC3B,QAAAA,QAAOD,UAAS,YAAY,KAAK;AACjC,QAAAC,QAAOD,UAAS,UAAU,KAAK;AAC/B,QAAAC,QAAOD,UAAS,YAAY,KAAK;AAAA,MACnC,CAAC;AACD,UAAI,WAAW;AACf,WAAK,OAAO,QAAQ,WAAS;AAC3B,YAAI,MAAM,WAAW,UAAU;AAC7B,qBAAW,MAAM;AAAA,QACnB;AAAA,MACF,CAAC;AACD,UAAI,WAAW,GAAG;AAChB,QAAAC,QAAO,gBAAgB,WAAW;AAAA,MACpC;AAEA,UAAI,KAAK,kBAAkB;AACzB,aAAK,iBAAiB,QAAQ,eAAa;AACzC,UAAAA,QAAOD,UAAS,YAAY,SAAS;AAAA,QACvC,CAAC;AAAA,MACH;AAEA,aAAOC;AAAA,IACT;AAIA,IAAAD,UAAS,6BAA6B,SAAS,cAAc;AAC3D,YAAM,qBAAqB,CAAC;AAC5B,YAAM,cAAcA,UAAS,mBAAmB,YAAY;AAC5D,YAAM,SAAS,YAAY,cAAc,QAAQ,KAAK,MAAM;AAC5D,YAAM,YAAY,YAAY,cAAc,QAAQ,QAAQ,MAAM;AAGlE,YAAM,QAAQA,UAAS,YAAY,cAAc,SAAS,EACvD,IAAI,UAAQA,UAAS,eAAe,IAAI,CAAC,EACzC,OAAO,WAAS,MAAM,cAAc,OAAO;AAC9C,YAAM,cAAc,MAAM,SAAS,KAAK,MAAM,CAAC,EAAE;AACjD,UAAI;AAEJ,YAAM,QAAQA,UAAS,YAAY,cAAc,kBAAkB,EAChE,IAAI,UAAQ;AACX,cAAM,QAAQ,KAAK,UAAU,EAAE,EAAE,MAAM,GAAG;AAC1C,eAAO,MAAM,IAAI,UAAQ,SAAS,MAAM,EAAE,CAAC;AAAA,MAC7C,CAAC;AACH,UAAI,MAAM,SAAS,KAAK,MAAM,CAAC,EAAE,SAAS,KAAK,MAAM,CAAC,EAAE,CAAC,MAAM,aAAa;AAC1E,wBAAgB,MAAM,CAAC,EAAE,CAAC;AAAA,MAC5B;AAEA,kBAAY,OAAO,QAAQ,WAAS;AAClC,YAAI,MAAM,KAAK,YAAY,MAAM,SAAS,MAAM,WAAW,KAAK;AAC9D,cAAI,WAAW;AAAA,YACb,MAAM;AAAA,YACN,kBAAkB,SAAS,MAAM,WAAW,KAAK,EAAE;AAAA,UACrD;AACA,cAAI,eAAe,eAAe;AAChC,qBAAS,MAAM,EAAC,MAAM,cAAa;AAAA,UACrC;AACA,6BAAmB,KAAK,QAAQ;AAChC,cAAI,QAAQ;AACV,uBAAW,KAAK,MAAM,KAAK,UAAU,QAAQ,CAAC;AAC9C,qBAAS,MAAM;AAAA,cACb,MAAM;AAAA,cACN,WAAW,YAAY,eAAe;AAAA,YACxC;AACA,+BAAmB,KAAK,QAAQ;AAAA,UAClC;AAAA,QACF;AAAA,MACF,CAAC;AACD,UAAI,mBAAmB,WAAW,KAAK,aAAa;AAClD,2BAAmB,KAAK;AAAA,UACtB,MAAM;AAAA,QACR,CAAC;AAAA,MACH;AAGA,UAAI,YAAYA,UAAS,YAAY,cAAc,IAAI;AACvD,UAAI,UAAU,QAAQ;AACpB,YAAI,UAAU,CAAC,EAAE,QAAQ,SAAS,MAAM,GAAG;AACzC,sBAAY,SAAS,UAAU,CAAC,EAAE,UAAU,CAAC,GAAG,EAAE;AAAA,QACpD,WAAW,UAAU,CAAC,EAAE,QAAQ,OAAO,MAAM,GAAG;AAE9C,sBAAY,SAAS,UAAU,CAAC,EAAE,UAAU,CAAC,GAAG,EAAE,IAAI,MAAO,OACtD,KAAK,KAAK;AAAA,QACnB,OAAO;AACL,sBAAY;AAAA,QACd;AACA,2BAAmB,QAAQ,YAAU;AACnC,iBAAO,aAAa;AAAA,QACtB,CAAC;AAAA,MACH;AACA,aAAO;AAAA,IACT;AAGA,IAAAA,UAAS,sBAAsB,SAAS,cAAc;AACpD,YAAM,iBAAiB,CAAC;AAIxB,YAAM,aAAaA,UAAS,YAAY,cAAc,SAAS,EAC5D,IAAI,UAAQA,UAAS,eAAe,IAAI,CAAC,EACzC,OAAO,SAAO,IAAI,cAAc,OAAO,EAAE,CAAC;AAC7C,UAAI,YAAY;AACd,uBAAe,QAAQ,WAAW;AAClC,uBAAe,OAAO,WAAW;AAAA,MACnC;AAIA,YAAM,QAAQA,UAAS,YAAY,cAAc,cAAc;AAC/D,qBAAe,cAAc,MAAM,SAAS;AAC5C,qBAAe,WAAW,MAAM,WAAW;AAI3C,YAAM,MAAMA,UAAS,YAAY,cAAc,YAAY;AAC3D,qBAAe,MAAM,IAAI,SAAS;AAElC,aAAO;AAAA,IACT;AAEA,IAAAA,UAAS,sBAAsB,SAAS,gBAAgB;AACtD,UAAIC,OAAM;AACV,UAAI,eAAe,aAAa;AAC9B,QAAAA,QAAO;AAAA,MACT;AACA,UAAI,eAAe,KAAK;AACtB,QAAAA,QAAO;AAAA,MACT;AACA,UAAI,eAAe,SAAS,UAAa,eAAe,OAAO;AAC7D,QAAAA,QAAO,YAAY,eAAe,OAChC,YAAY,eAAe,QAAQ;AAAA,MACvC;AACA,aAAOA;AAAA,IACT;AAKA,IAAAD,UAAS,YAAY,SAAS,cAAc;AAC1C,UAAI;AACJ,YAAM,OAAOA,UAAS,YAAY,cAAc,SAAS;AACzD,UAAI,KAAK,WAAW,GAAG;AACrB,gBAAQ,KAAK,CAAC,EAAE,UAAU,CAAC,EAAE,MAAM,GAAG;AACtC,eAAO,EAAC,QAAQ,MAAM,CAAC,GAAG,OAAO,MAAM,CAAC,EAAC;AAAA,MAC3C;AACA,YAAM,QAAQA,UAAS,YAAY,cAAc,SAAS,EACvD,IAAI,UAAQA,UAAS,eAAe,IAAI,CAAC,EACzC,OAAO,eAAa,UAAU,cAAc,MAAM;AACrD,UAAI,MAAM,SAAS,GAAG;AACpB,gBAAQ,MAAM,CAAC,EAAE,MAAM,MAAM,GAAG;AAChC,eAAO,EAAC,QAAQ,MAAM,CAAC,GAAG,OAAO,MAAM,CAAC,EAAC;AAAA,MAC3C;AAAA,IACF;AAKA,IAAAA,UAAS,uBAAuB,SAAS,cAAc;AACrD,YAAM,QAAQA,UAAS,WAAW,YAAY;AAC9C,YAAM,cAAcA,UAAS,YAAY,cAAc,qBAAqB;AAC5E,UAAI;AACJ,UAAI,YAAY,SAAS,GAAG;AAC1B,yBAAiB,SAAS,YAAY,CAAC,EAAE,UAAU,EAAE,GAAG,EAAE;AAAA,MAC5D;AACA,UAAI,MAAM,cAAc,GAAG;AACzB,yBAAiB;AAAA,MACnB;AACA,YAAM,WAAWA,UAAS,YAAY,cAAc,cAAc;AAClE,UAAI,SAAS,SAAS,GAAG;AACvB,eAAO;AAAA,UACL,MAAM,SAAS,SAAS,CAAC,EAAE,UAAU,EAAE,GAAG,EAAE;AAAA,UAC5C,UAAU,MAAM;AAAA,UAChB;AAAA,QACF;AAAA,MACF;AACA,YAAM,eAAeA,UAAS,YAAY,cAAc,YAAY;AACpE,UAAI,aAAa,SAAS,GAAG;AAC3B,cAAM,QAAQ,aAAa,CAAC,EACzB,UAAU,EAAE,EACZ,MAAM,GAAG;AACZ,eAAO;AAAA,UACL,MAAM,SAAS,MAAM,CAAC,GAAG,EAAE;AAAA,UAC3B,UAAU,MAAM,CAAC;AAAA,UACjB;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAOA,IAAAA,UAAS,uBAAuB,SAAS,OAAO,MAAM;AACpD,UAAI,SAAS,CAAC;AACd,UAAI,MAAM,aAAa,aAAa;AAClC,iBAAS;AAAA,UACP,OAAO,MAAM,OAAO,QAAQ,MAAM,WAAW,MAAM,KAAK,WAAW;AAAA,UACnE;AAAA,UACA,iBAAiB,KAAK,OAAO;AAAA,QAC/B;AAAA,MACF,OAAO;AACL,iBAAS;AAAA,UACP,OAAO,MAAM,OAAO,QAAQ,MAAM,WAAW,MAAM,KAAK,OAAO;AAAA,UAC/D;AAAA,UACA,eAAe,KAAK,OAAO,MAAM,KAAK,WAAW;AAAA,QACnD;AAAA,MACF;AACA,UAAI,KAAK,mBAAmB,QAAW;AACrC,eAAO,KAAK,wBAAwB,KAAK,iBAAiB,MAAM;AAAA,MAClE;AACA,aAAO,OAAO,KAAK,EAAE;AAAA,IACvB;AAMA,IAAAA,UAAS,oBAAoB,WAAW;AACtC,aAAO,KAAK,OAAO,EAAE,SAAS,EAAE,OAAO,GAAG,EAAE;AAAA,IAC9C;AAOA,IAAAA,UAAS,0BAA0B,SAAS,QAAQ,SAAS,UAAU;AACrE,UAAI;AACJ,YAAM,UAAU,YAAY,SAAY,UAAU;AAClD,UAAI,QAAQ;AACV,oBAAY;AAAA,MACd,OAAO;AACL,oBAAYA,UAAS,kBAAkB;AAAA,MACzC;AACA,YAAM,OAAO,YAAY;AAEzB,aAAO,cACI,OAAO,MAAM,YAAY,MAAM,UACpC;AAAA,IAGR;AAGA,IAAAA,UAAS,eAAe,SAAS,cAAc,aAAa;AAE1D,YAAM,QAAQA,UAAS,WAAW,YAAY;AAC9C,eAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACrC,gBAAQ,MAAM,CAAC,GAAG;AAAA,UAChB,KAAK;AAAA,UACL,KAAK;AAAA,UACL,KAAK;AAAA,UACL,KAAK;AACH,mBAAO,MAAM,CAAC,EAAE,UAAU,CAAC;AAAA,UAC7B;AAAA,QAEF;AAAA,MACF;AACA,UAAI,aAAa;AACf,eAAOA,UAAS,aAAa,WAAW;AAAA,MAC1C;AACA,aAAO;AAAA,IACT;AAEA,IAAAA,UAAS,UAAU,SAAS,cAAc;AACxC,YAAM,QAAQA,UAAS,WAAW,YAAY;AAC9C,YAAM,QAAQ,MAAM,CAAC,EAAE,MAAM,GAAG;AAChC,aAAO,MAAM,CAAC,EAAE,UAAU,CAAC;AAAA,IAC7B;AAEA,IAAAA,UAAS,aAAa,SAAS,cAAc;AAC3C,aAAO,aAAa,MAAM,KAAK,CAAC,EAAE,CAAC,MAAM;AAAA,IAC3C;AAEA,IAAAA,UAAS,aAAa,SAAS,cAAc;AAC3C,YAAM,QAAQA,UAAS,WAAW,YAAY;AAC9C,YAAM,QAAQ,MAAM,CAAC,EAAE,UAAU,CAAC,EAAE,MAAM,GAAG;AAC7C,aAAO;AAAA,QACL,MAAM,MAAM,CAAC;AAAA,QACb,MAAM,SAAS,MAAM,CAAC,GAAG,EAAE;AAAA,QAC3B,UAAU,MAAM,CAAC;AAAA,QACjB,KAAK,MAAM,MAAM,CAAC,EAAE,KAAK,GAAG;AAAA,MAC9B;AAAA,IACF;AAEA,IAAAA,UAAS,aAAa,SAAS,cAAc;AAC3C,YAAM,OAAOA,UAAS,YAAY,cAAc,IAAI,EAAE,CAAC;AACvD,YAAM,QAAQ,KAAK,UAAU,CAAC,EAAE,MAAM,GAAG;AACzC,aAAO;AAAA,QACL,UAAU,MAAM,CAAC;AAAA,QACjB,WAAW,MAAM,CAAC;AAAA,QAClB,gBAAgB,SAAS,MAAM,CAAC,GAAG,EAAE;AAAA,QACrC,SAAS,MAAM,CAAC;AAAA,QAChB,aAAa,MAAM,CAAC;AAAA,QACpB,SAAS,MAAM,CAAC;AAAA,MAClB;AAAA,IACF;AAGA,IAAAA,UAAS,aAAa,SAAS,MAAM;AACnC,UAAI,OAAO,SAAS,YAAY,KAAK,WAAW,GAAG;AACjD,eAAO;AAAA,MACT;AACA,YAAM,QAAQA,UAAS,WAAW,IAAI;AACtC,eAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACrC,YAAI,MAAM,CAAC,EAAE,SAAS,KAAK,MAAM,CAAC,EAAE,OAAO,CAAC,MAAM,KAAK;AACrD,iBAAO;AAAA,QACT;AAAA,MAEF;AACA,aAAO;AAAA,IACT;AAGA,QAAI,OAAO,WAAW,UAAU;AAC9B,aAAO,UAAUA;AAAA,IACnB;AAAA;AAAA;;;ACzxBA,IAAI,eAAe;AACnB,IAAI,uBAAuB;AAUpB,SAAS,eAAe,UAAU,MAAM,KAAK;AAClD,QAAM,QAAQ,SAAS,MAAM,IAAI;AACjC,SAAO,SAAS,MAAM,UAAU,OAAO,WAAW,MAAM,GAAG,GAAG,EAAE;AAClE;AAKO,SAAS,wBAAwBE,SAAQ,iBAAiB,SAAS;AACxE,MAAI,CAACA,QAAO,mBAAmB;AAC7B;AAAA,EACF;AACA,QAAM,QAAQA,QAAO,kBAAkB;AACvC,QAAM,yBAAyB,MAAM;AACrC,QAAM,mBAAmB,SAAS,iBAAiB,IAAI;AACrD,QAAI,oBAAoB,iBAAiB;AACvC,aAAO,uBAAuB,MAAM,MAAM,SAAS;AAAA,IACrD;AACA,UAAM,kBAAkB,CAAC,MAAM;AAC7B,YAAM,gBAAgB,QAAQ,CAAC;AAC/B,UAAI,eAAe;AACjB,YAAI,GAAG,aAAa;AAClB,aAAG,YAAY,aAAa;AAAA,QAC9B,OAAO;AACL,aAAG,aAAa;AAAA,QAClB;AAAA,MACF;AAAA,IACF;AACA,SAAK,YAAY,KAAK,aAAa,CAAC;AACpC,QAAI,CAAC,KAAK,UAAU,eAAe,GAAG;AACpC,WAAK,UAAU,eAAe,IAAI,oBAAI,IAAI;AAAA,IAC5C;AACA,SAAK,UAAU,eAAe,EAAE,IAAI,IAAI,eAAe;AACvD,WAAO,uBAAuB,MAAM,MAAM;AAAA,MAAC;AAAA,MACzC;AAAA,IAAe,CAAC;AAAA,EACpB;AAEA,QAAM,4BAA4B,MAAM;AACxC,QAAM,sBAAsB,SAAS,iBAAiB,IAAI;AACxD,QAAI,oBAAoB,mBAAmB,CAAC,KAAK,aAC1C,CAAC,KAAK,UAAU,eAAe,GAAG;AACvC,aAAO,0BAA0B,MAAM,MAAM,SAAS;AAAA,IACxD;AACA,QAAI,CAAC,KAAK,UAAU,eAAe,EAAE,IAAI,EAAE,GAAG;AAC5C,aAAO,0BAA0B,MAAM,MAAM,SAAS;AAAA,IACxD;AACA,UAAM,cAAc,KAAK,UAAU,eAAe,EAAE,IAAI,EAAE;AAC1D,SAAK,UAAU,eAAe,EAAE,OAAO,EAAE;AACzC,QAAI,KAAK,UAAU,eAAe,EAAE,SAAS,GAAG;AAC9C,aAAO,KAAK,UAAU,eAAe;AAAA,IACvC;AACA,QAAI,OAAO,KAAK,KAAK,SAAS,EAAE,WAAW,GAAG;AAC5C,aAAO,KAAK;AAAA,IACd;AACA,WAAO,0BAA0B,MAAM,MAAM;AAAA,MAAC;AAAA,MAC5C;AAAA,IAAW,CAAC;AAAA,EAChB;AAEA,SAAO,eAAe,OAAO,OAAO,iBAAiB;AAAA,IACnD,MAAM;AACJ,aAAO,KAAK,QAAQ,eAAe;AAAA,IACrC;AAAA,IACA,IAAI,IAAI;AACN,UAAI,KAAK,QAAQ,eAAe,GAAG;AACjC,aAAK;AAAA,UAAoB;AAAA,UACvB,KAAK,QAAQ,eAAe;AAAA,QAAC;AAC/B,eAAO,KAAK,QAAQ,eAAe;AAAA,MACrC;AACA,UAAI,IAAI;AACN,aAAK;AAAA,UAAiB;AAAA,UACpB,KAAK,QAAQ,eAAe,IAAI;AAAA,QAAE;AAAA,MACtC;AAAA,IACF;AAAA,IACA,YAAY;AAAA,IACZ,cAAc;AAAA,EAChB,CAAC;AACH;AAEO,SAAS,WAAW,MAAM;AAC/B,MAAI,OAAO,SAAS,WAAW;AAC7B,WAAO,IAAI,MAAM,oBAAoB,OAAO,OACxC,yBAAyB;AAAA,EAC/B;AACA,iBAAe;AACf,SAAQ,OAAQ,gCACd;AACJ;AAMO,SAAS,gBAAgB,MAAM;AACpC,MAAI,OAAO,SAAS,WAAW;AAC7B,WAAO,IAAI,MAAM,oBAAoB,OAAO,OACxC,yBAAyB;AAAA,EAC/B;AACA,yBAAuB,CAAC;AACxB,SAAO,sCAAsC,OAAO,aAAa;AACnE;AAEO,SAAS,MAAM;AACpB,MAAI,OAAO,WAAW,UAAU;AAC9B,QAAI,cAAc;AAChB;AAAA,IACF;AACA,QAAI,OAAO,YAAY,eAAe,OAAO,QAAQ,QAAQ,YAAY;AACvE,cAAQ,IAAI,MAAM,SAAS,SAAS;AAAA,IACtC;AAAA,EACF;AACF;AAKO,SAAS,WAAW,WAAW,WAAW;AAC/C,MAAI,CAAC,sBAAsB;AACzB;AAAA,EACF;AACA,UAAQ,KAAK,YAAY,gCAAgC,YACrD,WAAW;AACjB;AAQO,SAAS,cAAcA,SAAQ;AAEpC,QAAM,SAAS,EAAC,SAAS,MAAM,SAAS,KAAI;AAG5C,MAAI,OAAOA,YAAW,eAAe,CAACA,QAAO,aACzC,CAACA,QAAO,UAAU,WAAW;AAC/B,WAAO,UAAU;AACjB,WAAO;AAAA,EACT;AAEA,QAAM,EAAC,UAAS,IAAIA;AAEpB,MAAI,UAAU,iBAAiB;AAC7B,WAAO,UAAU;AACjB,WAAO,UAAU,SAAS;AAAA,MAAe,UAAU;AAAA,MACjD;AAAA,MAAoB;AAAA,IAAC,CAAC;AAAA,EAC1B,WAAW,UAAU,sBAChBA,QAAO,oBAAoB,SAASA,QAAO,yBAA0B;AAKxE,WAAO,UAAU;AACjB,WAAO,UAAU,SAAS;AAAA,MAAe,UAAU;AAAA,MACjD;AAAA,MAAyB;AAAA,IAAC,CAAC;AAAA,EAC/B,WAAWA,QAAO,qBACd,UAAU,UAAU,MAAM,sBAAsB,GAAG;AACrD,WAAO,UAAU;AACjB,WAAO,UAAU,SAAS;AAAA,MAAe,UAAU;AAAA,MACjD;AAAA,MAAwB;AAAA,IAAC,CAAC;AAC5B,WAAO,sBAAsBA,QAAO,qBAChC,sBAAsBA,QAAO,kBAAkB;AAEnD,WAAO,iBAAiB;AAAA,MAAe,UAAU;AAAA,MAC/C;AAAA,MAA0B;AAAA,IAAC;AAAA,EAC/B,OAAO;AACL,WAAO,UAAU;AACjB,WAAO;AAAA,EACT;AAEA,SAAO;AACT;AAQA,SAAS,SAAS,KAAK;AACrB,SAAO,OAAO,UAAU,SAAS,KAAK,GAAG,MAAM;AACjD;AAOO,SAAS,cAAc,MAAM;AAClC,MAAI,CAAC,SAAS,IAAI,GAAG;AACnB,WAAO;AAAA,EACT;AAEA,SAAO,OAAO,KAAK,IAAI,EAAE,OAAO,SAAS,aAAa,KAAK;AACzD,UAAM,QAAQ,SAAS,KAAK,GAAG,CAAC;AAChC,UAAM,QAAQ,QAAQ,cAAc,KAAK,GAAG,CAAC,IAAI,KAAK,GAAG;AACzD,UAAM,gBAAgB,SAAS,CAAC,OAAO,KAAK,KAAK,EAAE;AACnD,QAAI,UAAU,UAAa,eAAe;AACxC,aAAO;AAAA,IACT;AACA,WAAO,OAAO,OAAO,aAAa,EAAC,CAAC,GAAG,GAAG,MAAK,CAAC;AAAA,EAClD,GAAG,CAAC,CAAC;AACP;AAGO,SAAS,UAAU,OAAO,MAAM,WAAW;AAChD,MAAI,CAAC,QAAQ,UAAU,IAAI,KAAK,EAAE,GAAG;AACnC;AAAA,EACF;AACA,YAAU,IAAI,KAAK,IAAI,IAAI;AAC3B,SAAO,KAAK,IAAI,EAAE,QAAQ,UAAQ;AAChC,QAAI,KAAK,SAAS,IAAI,GAAG;AACvB,gBAAU,OAAO,MAAM,IAAI,KAAK,IAAI,CAAC,GAAG,SAAS;AAAA,IACnD,WAAW,KAAK,SAAS,KAAK,GAAG;AAC/B,WAAK,IAAI,EAAE,QAAQ,QAAM;AACvB,kBAAU,OAAO,MAAM,IAAI,EAAE,GAAG,SAAS;AAAA,MAC3C,CAAC;AAAA,IACH;AAAA,EACF,CAAC;AACH;AAGO,SAAS,YAAY,QAAQ,OAAO,UAAU;AACnD,QAAM,kBAAkB,WAAW,iBAAiB;AACpD,QAAM,iBAAiB,oBAAI,IAAI;AAC/B,MAAI,UAAU,MAAM;AAClB,WAAO;AAAA,EACT;AACA,QAAM,aAAa,CAAC;AACpB,SAAO,QAAQ,WAAS;AACtB,QAAI,MAAM,SAAS,WACf,MAAM,oBAAoB,MAAM,IAAI;AACtC,iBAAW,KAAK,KAAK;AAAA,IACvB;AAAA,EACF,CAAC;AACD,aAAW,QAAQ,eAAa;AAC9B,WAAO,QAAQ,WAAS;AACtB,UAAI,MAAM,SAAS,mBAAmB,MAAM,YAAY,UAAU,IAAI;AACpE,kBAAU,QAAQ,OAAO,cAAc;AAAA,MACzC;AAAA,IACF,CAAC;AAAA,EACH,CAAC;AACD,SAAO;AACT;;;ACxQA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;ACUA,IAAM,UAAgB;AAEf,SAAS,iBAAiBC,SAAQ,gBAAgB;AACvD,QAAM,YAAYA,WAAUA,QAAO;AAEnC,MAAI,CAAC,UAAU,cAAc;AAC3B;AAAA,EACF;AAEA,QAAM,uBAAuB,SAAS,GAAG;AACvC,QAAI,OAAO,MAAM,YAAY,EAAE,aAAa,EAAE,UAAU;AACtD,aAAO;AAAA,IACT;AACA,UAAM,KAAK,CAAC;AACZ,WAAO,KAAK,CAAC,EAAE,QAAQ,SAAO;AAC5B,UAAI,QAAQ,aAAa,QAAQ,cAAc,QAAQ,eAAe;AACpE;AAAA,MACF;AACA,YAAM,IAAK,OAAO,EAAE,GAAG,MAAM,WAAY,EAAE,GAAG,IAAI,EAAC,OAAO,EAAE,GAAG,EAAC;AAChE,UAAI,EAAE,UAAU,UAAa,OAAO,EAAE,UAAU,UAAU;AACxD,UAAE,MAAM,EAAE,MAAM,EAAE;AAAA,MACpB;AACA,YAAM,WAAW,SAAS,QAAQ,MAAM;AACtC,YAAI,QAAQ;AACV,iBAAO,SAAS,KAAK,OAAO,CAAC,EAAE,YAAY,IAAI,KAAK,MAAM,CAAC;AAAA,QAC7D;AACA,eAAQ,SAAS,aAAc,aAAa;AAAA,MAC9C;AACA,UAAI,EAAE,UAAU,QAAW;AACzB,WAAG,WAAW,GAAG,YAAY,CAAC;AAC9B,YAAI,KAAK,CAAC;AACV,YAAI,OAAO,EAAE,UAAU,UAAU;AAC/B,aAAG,SAAS,OAAO,GAAG,CAAC,IAAI,EAAE;AAC7B,aAAG,SAAS,KAAK,EAAE;AACnB,eAAK,CAAC;AACN,aAAG,SAAS,OAAO,GAAG,CAAC,IAAI,EAAE;AAC7B,aAAG,SAAS,KAAK,EAAE;AAAA,QACrB,OAAO;AACL,aAAG,SAAS,IAAI,GAAG,CAAC,IAAI,EAAE;AAC1B,aAAG,SAAS,KAAK,EAAE;AAAA,QACrB;AAAA,MACF;AACA,UAAI,EAAE,UAAU,UAAa,OAAO,EAAE,UAAU,UAAU;AACxD,WAAG,YAAY,GAAG,aAAa,CAAC;AAChC,WAAG,UAAU,SAAS,IAAI,GAAG,CAAC,IAAI,EAAE;AAAA,MACtC,OAAO;AACL,SAAC,OAAO,KAAK,EAAE,QAAQ,SAAO;AAC5B,cAAI,EAAE,GAAG,MAAM,QAAW;AACxB,eAAG,YAAY,GAAG,aAAa,CAAC;AAChC,eAAG,UAAU,SAAS,KAAK,GAAG,CAAC,IAAI,EAAE,GAAG;AAAA,UAC1C;AAAA,QACF,CAAC;AAAA,MACH;AAAA,IACF,CAAC;AACD,QAAI,EAAE,UAAU;AACd,SAAG,YAAY,GAAG,YAAY,CAAC,GAAG,OAAO,EAAE,QAAQ;AAAA,IACrD;AACA,WAAO;AAAA,EACT;AAEA,QAAM,mBAAmB,SAAS,aAAa,MAAM;AACnD,QAAI,eAAe,WAAW,IAAI;AAChC,aAAO,KAAK,WAAW;AAAA,IACzB;AACA,kBAAc,KAAK,MAAM,KAAK,UAAU,WAAW,CAAC;AACpD,QAAI,eAAe,OAAO,YAAY,UAAU,UAAU;AACxD,YAAM,QAAQ,SAAS,KAAK,GAAG,GAAG;AAChC,YAAI,KAAK,OAAO,EAAE,KAAK,MAAM;AAC3B,cAAI,CAAC,IAAI,IAAI,CAAC;AACd,iBAAO,IAAI,CAAC;AAAA,QACd;AAAA,MACF;AACA,oBAAc,KAAK,MAAM,KAAK,UAAU,WAAW,CAAC;AACpD,YAAM,YAAY,OAAO,mBAAmB,qBAAqB;AACjE,YAAM,YAAY,OAAO,oBAAoB,sBAAsB;AACnE,kBAAY,QAAQ,qBAAqB,YAAY,KAAK;AAAA,IAC5D;AACA,QAAI,eAAe,OAAO,YAAY,UAAU,UAAU;AAExD,UAAI,OAAO,YAAY,MAAM;AAC7B,aAAO,SAAU,OAAO,SAAS,WAAY,OAAO,EAAC,OAAO,KAAI;AAChE,YAAM,6BAA6B,eAAe,UAAU;AAE5D,UAAK,SAAS,KAAK,UAAU,UAAU,KAAK,UAAU,iBACxC,KAAK,UAAU,UAAU,KAAK,UAAU,kBAClD,EAAE,UAAU,aAAa,2BACvB,UAAU,aAAa,wBAAwB,EAAE,cACjD,CAAC,6BAA6B;AAClC,eAAO,YAAY,MAAM;AACzB,YAAI;AACJ,YAAI,KAAK,UAAU,iBAAiB,KAAK,UAAU,eAAe;AAChE,oBAAU,CAAC,QAAQ,MAAM;AAAA,QAC3B,WAAW,KAAK,UAAU,UAAU,KAAK,UAAU,QAAQ;AACzD,oBAAU,CAAC,OAAO;AAAA,QACpB;AACA,YAAI,SAAS;AAEX,iBAAO,UAAU,aAAa,iBAAiB,EAC5C,KAAK,aAAW;AACf,sBAAU,QAAQ,OAAO,OAAK,EAAE,SAAS,YAAY;AACrD,gBAAI,MAAM,QAAQ,KAAK,OAAK,QAAQ,KAAK,WACvC,EAAE,MAAM,YAAY,EAAE,SAAS,KAAK,CAAC,CAAC;AACxC,gBAAI,CAAC,OAAO,QAAQ,UAAU,QAAQ,SAAS,MAAM,GAAG;AACtD,oBAAM,QAAQ,QAAQ,SAAS,CAAC;AAAA,YAClC;AACA,gBAAI,KAAK;AACP,0BAAY,MAAM,WAAW,KAAK,QAC9B,EAAC,OAAO,IAAI,SAAQ,IACpB,EAAC,OAAO,IAAI,SAAQ;AAAA,YAC1B;AACA,wBAAY,QAAQ,qBAAqB,YAAY,KAAK;AAC1D,oBAAQ,aAAa,KAAK,UAAU,WAAW,CAAC;AAChD,mBAAO,KAAK,WAAW;AAAA,UACzB,CAAC;AAAA,QACL;AAAA,MACF;AACA,kBAAY,QAAQ,qBAAqB,YAAY,KAAK;AAAA,IAC5D;AACA,YAAQ,aAAa,KAAK,UAAU,WAAW,CAAC;AAChD,WAAO,KAAK,WAAW;AAAA,EACzB;AAEA,QAAM,aAAa,SAAS,GAAG;AAC7B,QAAI,eAAe,WAAW,IAAI;AAChC,aAAO;AAAA,IACT;AACA,WAAO;AAAA,MACL,MAAM;AAAA,QACJ,uBAAuB;AAAA,QACvB,0BAA0B;AAAA,QAC1B,mBAAmB;AAAA,QACnB,sBAAsB;AAAA,QACtB,6BAA6B;AAAA,QAC7B,iBAAiB;AAAA,QACjB,gCAAgC;AAAA,QAChC,yBAAyB;AAAA,QACzB,iBAAiB;AAAA,QACjB,oBAAoB;AAAA,QACpB,oBAAoB;AAAA,MACtB,EAAE,EAAE,IAAI,KAAK,EAAE;AAAA,MACf,SAAS,EAAE;AAAA,MACX,YAAY,EAAE,cAAc,EAAE;AAAA,MAC9B,WAAW;AACT,eAAO,KAAK,QAAQ,KAAK,WAAW,QAAQ,KAAK;AAAA,MACnD;AAAA,IACF;AAAA,EACF;AAEA,QAAM,gBAAgB,SAAS,aAAa,WAAW,SAAS;AAC9D,qBAAiB,aAAa,OAAK;AACjC,gBAAU,mBAAmB,GAAG,WAAW,OAAK;AAC9C,YAAI,SAAS;AACX,kBAAQ,WAAW,CAAC,CAAC;AAAA,QACvB;AAAA,MACF,CAAC;AAAA,IACH,CAAC;AAAA,EACH;AACA,YAAU,eAAe,cAAc,KAAK,SAAS;AAKrD,MAAI,UAAU,aAAa,cAAc;AACvC,UAAM,mBAAmB,UAAU,aAAa,aAC9C,KAAK,UAAU,YAAY;AAC7B,cAAU,aAAa,eAAe,SAAS,IAAI;AACjD,aAAO,iBAAiB,IAAI,OAAK,iBAAiB,CAAC,EAAE,KAAK,YAAU;AAClE,YAAI,EAAE,SAAS,CAAC,OAAO,eAAe,EAAE,UACpC,EAAE,SAAS,CAAC,OAAO,eAAe,EAAE,QAAQ;AAC9C,iBAAO,UAAU,EAAE,QAAQ,WAAS;AAClC,kBAAM,KAAK;AAAA,UACb,CAAC;AACD,gBAAM,IAAI,aAAa,IAAI,eAAe;AAAA,QAC5C;AACA,eAAO;AAAA,MACT,GAAG,OAAK,QAAQ,OAAO,WAAW,CAAC,CAAC,CAAC,CAAC;AAAA,IACxC;AAAA,EACF;AACF;;;ACnLO,SAAS,oBAAoBC,SAAQ,aAAa;AACvD,MAAIA,QAAO,UAAU,gBACnB,qBAAqBA,QAAO,UAAU,cAAc;AACpD;AAAA,EACF;AACA,MAAI,CAAEA,QAAO,UAAU,cAAe;AACpC;AAAA,EACF;AAGA,MAAI,OAAO,gBAAgB,YAAY;AACrC,YAAQ,MAAM,6DACE;AAChB;AAAA,EACF;AACA,EAAAA,QAAO,UAAU,aAAa,kBAC5B,SAAS,gBAAgB,aAAa;AACpC,WAAO,YAAY,WAAW,EAC3B,KAAK,cAAY;AAChB,YAAM,iBAAiB,YAAY,SAAS,YAAY,MAAM;AAC9D,YAAM,kBAAkB,YAAY,SAClC,YAAY,MAAM;AACpB,YAAM,qBAAqB,YAAY,SACrC,YAAY,MAAM;AACpB,kBAAY,QAAQ;AAAA,QAClB,WAAW;AAAA,UACT,mBAAmB;AAAA,UACnB,qBAAqB;AAAA,UACrB,cAAc,sBAAsB;AAAA,QACtC;AAAA,MACF;AACA,UAAI,gBAAgB;AAClB,oBAAY,MAAM,UAAU,WAAW;AAAA,MACzC;AACA,UAAI,iBAAiB;AACnB,oBAAY,MAAM,UAAU,YAAY;AAAA,MAC1C;AACA,aAAOA,QAAO,UAAU,aAAa,aAAa,WAAW;AAAA,IAC/D,CAAC;AAAA,EACL;AACJ;;;AFnCO,SAAS,gBAAgBC,SAAQ;AACtC,EAAAA,QAAO,cAAcA,QAAO,eAAeA,QAAO;AACpD;AAEO,SAAS,YAAYA,SAAQ;AAClC,MAAI,OAAOA,YAAW,YAAYA,QAAO,qBAAqB,EAAE,aAC5DA,QAAO,kBAAkB,YAAY;AACvC,WAAO,eAAeA,QAAO,kBAAkB,WAAW,WAAW;AAAA,MACnE,MAAM;AACJ,eAAO,KAAK;AAAA,MACd;AAAA,MACA,IAAI,GAAG;AACL,YAAI,KAAK,UAAU;AACjB,eAAK,oBAAoB,SAAS,KAAK,QAAQ;AAAA,QACjD;AACA,aAAK,iBAAiB,SAAS,KAAK,WAAW,CAAC;AAAA,MAClD;AAAA,MACA,YAAY;AAAA,MACZ,cAAc;AAAA,IAChB,CAAC;AACD,UAAM,2BACFA,QAAO,kBAAkB,UAAU;AACvC,IAAAA,QAAO,kBAAkB,UAAU,uBACjC,SAAS,uBAAuB;AAC9B,UAAI,CAAC,KAAK,cAAc;AACtB,aAAK,eAAe,CAAC,MAAM;AAGzB,YAAE,OAAO,iBAAiB,YAAY,QAAM;AAC1C,gBAAI;AACJ,gBAAIA,QAAO,kBAAkB,UAAU,cAAc;AACnD,yBAAW,KAAK,aAAa,EAC1B,KAAK,OAAK,EAAE,SAAS,EAAE,MAAM,OAAO,GAAG,MAAM,EAAE;AAAA,YACpD,OAAO;AACL,yBAAW,EAAC,OAAO,GAAG,MAAK;AAAA,YAC7B;AAEA,kBAAM,QAAQ,IAAI,MAAM,OAAO;AAC/B,kBAAM,QAAQ,GAAG;AACjB,kBAAM,WAAW;AACjB,kBAAM,cAAc,EAAC,SAAQ;AAC7B,kBAAM,UAAU,CAAC,EAAE,MAAM;AACzB,iBAAK,cAAc,KAAK;AAAA,UAC1B,CAAC;AACD,YAAE,OAAO,UAAU,EAAE,QAAQ,WAAS;AACpC,gBAAI;AACJ,gBAAIA,QAAO,kBAAkB,UAAU,cAAc;AACnD,yBAAW,KAAK,aAAa,EAC1B,KAAK,OAAK,EAAE,SAAS,EAAE,MAAM,OAAO,MAAM,EAAE;AAAA,YACjD,OAAO;AACL,yBAAW,EAAC,MAAK;AAAA,YACnB;AACA,kBAAM,QAAQ,IAAI,MAAM,OAAO;AAC/B,kBAAM,QAAQ;AACd,kBAAM,WAAW;AACjB,kBAAM,cAAc,EAAC,SAAQ;AAC7B,kBAAM,UAAU,CAAC,EAAE,MAAM;AACzB,iBAAK,cAAc,KAAK;AAAA,UAC1B,CAAC;AAAA,QACH;AACA,aAAK,iBAAiB,aAAa,KAAK,YAAY;AAAA,MACtD;AACA,aAAO,yBAAyB,MAAM,MAAM,SAAS;AAAA,IACvD;AAAA,EACJ,OAAO;AAIL,IAAM,wBAAwBA,SAAQ,SAAS,OAAK;AAClD,UAAI,CAAC,EAAE,aAAa;AAClB,eAAO;AAAA,UAAe;AAAA,UAAG;AAAA,UACvB,EAAC,OAAO,EAAC,UAAU,EAAE,SAAQ,EAAC;AAAA,QAAC;AAAA,MACnC;AACA,aAAO;AAAA,IACT,CAAC;AAAA,EACH;AACF;AAEO,SAAS,uBAAuBA,SAAQ;AAE7C,MAAI,OAAOA,YAAW,YAAYA,QAAO,qBACrC,EAAE,gBAAgBA,QAAO,kBAAkB,cAC3C,sBAAsBA,QAAO,kBAAkB,WAAW;AAC5D,UAAM,qBAAqB,SAAS,IAAI,OAAO;AAC7C,aAAO;AAAA,QACL;AAAA,QACA,IAAI,OAAO;AACT,cAAI,KAAK,UAAU,QAAW;AAC5B,gBAAI,MAAM,SAAS,SAAS;AAC1B,mBAAK,QAAQ,GAAG,iBAAiB,KAAK;AAAA,YACxC,OAAO;AACL,mBAAK,QAAQ;AAAA,YACf;AAAA,UACF;AACA,iBAAO,KAAK;AAAA,QACd;AAAA,QACA,KAAK;AAAA,MACP;AAAA,IACF;AAGA,QAAI,CAACA,QAAO,kBAAkB,UAAU,YAAY;AAClD,MAAAA,QAAO,kBAAkB,UAAU,aAAa,SAAS,aAAa;AACpE,aAAK,WAAW,KAAK,YAAY,CAAC;AAClC,eAAO,KAAK,SAAS,MAAM;AAAA,MAC7B;AACA,YAAM,eAAeA,QAAO,kBAAkB,UAAU;AACxD,MAAAA,QAAO,kBAAkB,UAAU,WACjC,SAAS,SAAS,OAAO,QAAQ;AAC/B,YAAI,SAAS,aAAa,MAAM,MAAM,SAAS;AAC/C,YAAI,CAAC,QAAQ;AACX,mBAAS,mBAAmB,MAAM,KAAK;AACvC,eAAK,SAAS,KAAK,MAAM;AAAA,QAC3B;AACA,eAAO;AAAA,MACT;AAEF,YAAM,kBAAkBA,QAAO,kBAAkB,UAAU;AAC3D,MAAAA,QAAO,kBAAkB,UAAU,cACjC,SAAS,YAAY,QAAQ;AAC3B,wBAAgB,MAAM,MAAM,SAAS;AACrC,cAAM,MAAM,KAAK,SAAS,QAAQ,MAAM;AACxC,YAAI,QAAQ,IAAI;AACd,eAAK,SAAS,OAAO,KAAK,CAAC;AAAA,QAC7B;AAAA,MACF;AAAA,IACJ;AACA,UAAM,gBAAgBA,QAAO,kBAAkB,UAAU;AACzD,IAAAA,QAAO,kBAAkB,UAAU,YAAY,SAAS,UAAU,QAAQ;AACxE,WAAK,WAAW,KAAK,YAAY,CAAC;AAClC,oBAAc,MAAM,MAAM,CAAC,MAAM,CAAC;AAClC,aAAO,UAAU,EAAE,QAAQ,WAAS;AAClC,aAAK,SAAS,KAAK,mBAAmB,MAAM,KAAK,CAAC;AAAA,MACpD,CAAC;AAAA,IACH;AAEA,UAAM,mBAAmBA,QAAO,kBAAkB,UAAU;AAC5D,IAAAA,QAAO,kBAAkB,UAAU,eACjC,SAAS,aAAa,QAAQ;AAC5B,WAAK,WAAW,KAAK,YAAY,CAAC;AAClC,uBAAiB,MAAM,MAAM,CAAC,MAAM,CAAC;AAErC,aAAO,UAAU,EAAE,QAAQ,WAAS;AAClC,cAAM,SAAS,KAAK,SAAS,KAAK,OAAK,EAAE,UAAU,KAAK;AACxD,YAAI,QAAQ;AACV,eAAK,SAAS,OAAO,KAAK,SAAS,QAAQ,MAAM,GAAG,CAAC;AAAA,QACvD;AAAA,MACF,CAAC;AAAA,IACH;AAAA,EACJ,WAAW,OAAOA,YAAW,YAAYA,QAAO,qBACrC,gBAAgBA,QAAO,kBAAkB,aACzC,sBAAsBA,QAAO,kBAAkB,aAC/CA,QAAO,gBACP,EAAE,UAAUA,QAAO,aAAa,YAAY;AACrD,UAAM,iBAAiBA,QAAO,kBAAkB,UAAU;AAC1D,IAAAA,QAAO,kBAAkB,UAAU,aAAa,SAAS,aAAa;AACpE,YAAM,UAAU,eAAe,MAAM,MAAM,CAAC,CAAC;AAC7C,cAAQ,QAAQ,YAAU,OAAO,MAAM,IAAI;AAC3C,aAAO;AAAA,IACT;AAEA,WAAO,eAAeA,QAAO,aAAa,WAAW,QAAQ;AAAA,MAC3D,MAAM;AACJ,YAAI,KAAK,UAAU,QAAW;AAC5B,cAAI,KAAK,MAAM,SAAS,SAAS;AAC/B,iBAAK,QAAQ,KAAK,IAAI,iBAAiB,KAAK,KAAK;AAAA,UACnD,OAAO;AACL,iBAAK,QAAQ;AAAA,UACf;AAAA,QACF;AACA,eAAO,KAAK;AAAA,MACd;AAAA,IACF,CAAC;AAAA,EACH;AACF;AAEO,SAAS,aAAaA,SAAQ;AACnC,MAAI,CAACA,QAAO,mBAAmB;AAC7B;AAAA,EACF;AAEA,QAAM,eAAeA,QAAO,kBAAkB,UAAU;AACxD,EAAAA,QAAO,kBAAkB,UAAU,WAAW,SAAS,WAAW;AAChE,UAAM,CAAC,UAAU,QAAQ,KAAK,IAAI;AAIlC,QAAI,UAAU,SAAS,KAAK,OAAO,aAAa,YAAY;AAC1D,aAAO,aAAa,MAAM,MAAM,SAAS;AAAA,IAC3C;AAIA,QAAI,aAAa,WAAW,MAAM,UAAU,WAAW,KACnD,OAAO,aAAa,aAAa;AACnC,aAAO,aAAa,MAAM,MAAM,CAAC,CAAC;AAAA,IACpC;AAEA,UAAM,kBAAkB,SAAS,UAAU;AACzC,YAAM,iBAAiB,CAAC;AACxB,YAAM,UAAU,SAAS,OAAO;AAChC,cAAQ,QAAQ,YAAU;AACxB,cAAM,gBAAgB;AAAA,UACpB,IAAI,OAAO;AAAA,UACX,WAAW,OAAO;AAAA,UAClB,MAAM;AAAA,YACJ,gBAAgB;AAAA,YAChB,iBAAiB;AAAA,UACnB,EAAE,OAAO,IAAI,KAAK,OAAO;AAAA,QAC3B;AACA,eAAO,MAAM,EAAE,QAAQ,UAAQ;AAC7B,wBAAc,IAAI,IAAI,OAAO,KAAK,IAAI;AAAA,QACxC,CAAC;AACD,uBAAe,cAAc,EAAE,IAAI;AAAA,MACrC,CAAC;AAED,aAAO;AAAA,IACT;AAGA,UAAM,eAAe,SAAS,OAAO;AACnC,aAAO,IAAI,IAAI,OAAO,KAAK,KAAK,EAAE,IAAI,SAAO,CAAC,KAAK,MAAM,GAAG,CAAC,CAAC,CAAC;AAAA,IACjE;AAEA,QAAI,UAAU,UAAU,GAAG;AACzB,YAAM,0BAA0B,SAAS,UAAU;AACjD,eAAO,aAAa,gBAAgB,QAAQ,CAAC,CAAC;AAAA,MAChD;AAEA,aAAO,aAAa,MAAM,MAAM;AAAA,QAAC;AAAA,QAC/B;AAAA,MAAQ,CAAC;AAAA,IACb;AAGA,WAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AACtC,mBAAa,MAAM,MAAM;AAAA,QACvB,SAAS,UAAU;AACjB,kBAAQ,aAAa,gBAAgB,QAAQ,CAAC,CAAC;AAAA,QACjD;AAAA,QAAG;AAAA,MAAM,CAAC;AAAA,IACd,CAAC,EAAE,KAAK,QAAQ,KAAK;AAAA,EACvB;AACF;AAEO,SAAS,2BAA2BA,SAAQ;AACjD,MAAI,EAAE,OAAOA,YAAW,YAAYA,QAAO,qBACvCA,QAAO,gBAAgBA,QAAO,iBAAiB;AACjD;AAAA,EACF;AAGA,MAAI,EAAE,cAAcA,QAAO,aAAa,YAAY;AAClD,UAAM,iBAAiBA,QAAO,kBAAkB,UAAU;AAC1D,QAAI,gBAAgB;AAClB,MAAAA,QAAO,kBAAkB,UAAU,aAAa,SAAS,aAAa;AACpE,cAAM,UAAU,eAAe,MAAM,MAAM,CAAC,CAAC;AAC7C,gBAAQ,QAAQ,YAAU,OAAO,MAAM,IAAI;AAC3C,eAAO;AAAA,MACT;AAAA,IACF;AAEA,UAAM,eAAeA,QAAO,kBAAkB,UAAU;AACxD,QAAI,cAAc;AAChB,MAAAA,QAAO,kBAAkB,UAAU,WAAW,SAAS,WAAW;AAChE,cAAM,SAAS,aAAa,MAAM,MAAM,SAAS;AACjD,eAAO,MAAM;AACb,eAAO;AAAA,MACT;AAAA,IACF;AACA,IAAAA,QAAO,aAAa,UAAU,WAAW,SAAS,WAAW;AAC3D,YAAM,SAAS;AACf,aAAO,KAAK,IAAI,SAAS,EAAE,KAAK;AAAA;AAAA;AAAA;AAAA;AAAA,QAKxB,YAAY,QAAQ,OAAO,OAAO,IAAI;AAAA,OAAC;AAAA,IACjD;AAAA,EACF;AAGA,MAAI,EAAE,cAAcA,QAAO,eAAe,YAAY;AACpD,UAAM,mBAAmBA,QAAO,kBAAkB,UAAU;AAC5D,QAAI,kBAAkB;AACpB,MAAAA,QAAO,kBAAkB,UAAU,eACjC,SAAS,eAAe;AACtB,cAAM,YAAY,iBAAiB,MAAM,MAAM,CAAC,CAAC;AACjD,kBAAU,QAAQ,cAAY,SAAS,MAAM,IAAI;AACjD,eAAO;AAAA,MACT;AAAA,IACJ;AACA,IAAM,wBAAwBA,SAAQ,SAAS,OAAK;AAClD,QAAE,SAAS,MAAM,EAAE;AACnB,aAAO;AAAA,IACT,CAAC;AACD,IAAAA,QAAO,eAAe,UAAU,WAAW,SAAS,WAAW;AAC7D,YAAM,WAAW;AACjB,aAAO,KAAK,IAAI,SAAS,EAAE,KAAK,YACxB,YAAY,QAAQ,SAAS,OAAO,KAAK,CAAC;AAAA,IACpD;AAAA,EACF;AAEA,MAAI,EAAE,cAAcA,QAAO,aAAa,aACpC,cAAcA,QAAO,eAAe,YAAY;AAClD;AAAA,EACF;AAGA,QAAM,eAAeA,QAAO,kBAAkB,UAAU;AACxD,EAAAA,QAAO,kBAAkB,UAAU,WAAW,SAAS,WAAW;AAChE,QAAI,UAAU,SAAS,KACnB,UAAU,CAAC,aAAaA,QAAO,kBAAkB;AACnD,YAAM,QAAQ,UAAU,CAAC;AACzB,UAAI;AACJ,UAAI;AACJ,UAAI;AACJ,WAAK,WAAW,EAAE,QAAQ,OAAK;AAC7B,YAAI,EAAE,UAAU,OAAO;AACrB,cAAI,QAAQ;AACV,kBAAM;AAAA,UACR,OAAO;AACL,qBAAS;AAAA,UACX;AAAA,QACF;AAAA,MACF,CAAC;AACD,WAAK,aAAa,EAAE,QAAQ,OAAK;AAC/B,YAAI,EAAE,UAAU,OAAO;AACrB,cAAI,UAAU;AACZ,kBAAM;AAAA,UACR,OAAO;AACL,uBAAW;AAAA,UACb;AAAA,QACF;AACA,eAAO,EAAE,UAAU;AAAA,MACrB,CAAC;AACD,UAAI,OAAQ,UAAU,UAAW;AAC/B,eAAO,QAAQ,OAAO,IAAI;AAAA,UACxB;AAAA,UACA;AAAA,QAAoB,CAAC;AAAA,MACzB,WAAW,QAAQ;AACjB,eAAO,OAAO,SAAS;AAAA,MACzB,WAAW,UAAU;AACnB,eAAO,SAAS,SAAS;AAAA,MAC3B;AACA,aAAO,QAAQ,OAAO,IAAI;AAAA,QACxB;AAAA,QACA;AAAA,MAAoB,CAAC;AAAA,IACzB;AACA,WAAO,aAAa,MAAM,MAAM,SAAS;AAAA,EAC3C;AACF;AAEO,SAAS,kCAAkCA,SAAQ;AAIxD,EAAAA,QAAO,kBAAkB,UAAU,kBACjC,SAAS,kBAAkB;AACzB,SAAK,uBAAuB,KAAK,wBAAwB,CAAC;AAC1D,WAAO,OAAO,KAAK,KAAK,oBAAoB,EACzC,IAAI,cAAY,KAAK,qBAAqB,QAAQ,EAAE,CAAC,CAAC;AAAA,EAC3D;AAEF,QAAM,eAAeA,QAAO,kBAAkB,UAAU;AACxD,EAAAA,QAAO,kBAAkB,UAAU,WACjC,SAAS,SAAS,OAAO,QAAQ;AAC/B,QAAI,CAAC,QAAQ;AACX,aAAO,aAAa,MAAM,MAAM,SAAS;AAAA,IAC3C;AACA,SAAK,uBAAuB,KAAK,wBAAwB,CAAC;AAE1D,UAAM,SAAS,aAAa,MAAM,MAAM,SAAS;AACjD,QAAI,CAAC,KAAK,qBAAqB,OAAO,EAAE,GAAG;AACzC,WAAK,qBAAqB,OAAO,EAAE,IAAI,CAAC,QAAQ,MAAM;AAAA,IACxD,WAAW,KAAK,qBAAqB,OAAO,EAAE,EAAE,QAAQ,MAAM,MAAM,IAAI;AACtE,WAAK,qBAAqB,OAAO,EAAE,EAAE,KAAK,MAAM;AAAA,IAClD;AACA,WAAO;AAAA,EACT;AAEF,QAAM,gBAAgBA,QAAO,kBAAkB,UAAU;AACzD,EAAAA,QAAO,kBAAkB,UAAU,YAAY,SAAS,UAAU,QAAQ;AACxE,SAAK,uBAAuB,KAAK,wBAAwB,CAAC;AAE1D,WAAO,UAAU,EAAE,QAAQ,WAAS;AAClC,YAAM,gBAAgB,KAAK,WAAW,EAAE,KAAK,OAAK,EAAE,UAAU,KAAK;AACnE,UAAI,eAAe;AACjB,cAAM,IAAI;AAAA,UAAa;AAAA,UACrB;AAAA,QAAoB;AAAA,MACxB;AAAA,IACF,CAAC;AACD,UAAM,kBAAkB,KAAK,WAAW;AACxC,kBAAc,MAAM,MAAM,SAAS;AACnC,UAAM,aAAa,KAAK,WAAW,EAChC,OAAO,eAAa,gBAAgB,QAAQ,SAAS,MAAM,EAAE;AAChE,SAAK,qBAAqB,OAAO,EAAE,IAAI,CAAC,MAAM,EAAE,OAAO,UAAU;AAAA,EACnE;AAEA,QAAM,mBAAmBA,QAAO,kBAAkB,UAAU;AAC5D,EAAAA,QAAO,kBAAkB,UAAU,eACjC,SAAS,aAAa,QAAQ;AAC5B,SAAK,uBAAuB,KAAK,wBAAwB,CAAC;AAC1D,WAAO,KAAK,qBAAqB,OAAO,EAAE;AAC1C,WAAO,iBAAiB,MAAM,MAAM,SAAS;AAAA,EAC/C;AAEF,QAAM,kBAAkBA,QAAO,kBAAkB,UAAU;AAC3D,EAAAA,QAAO,kBAAkB,UAAU,cACjC,SAAS,YAAY,QAAQ;AAC3B,SAAK,uBAAuB,KAAK,wBAAwB,CAAC;AAC1D,QAAI,QAAQ;AACV,aAAO,KAAK,KAAK,oBAAoB,EAAE,QAAQ,cAAY;AACzD,cAAM,MAAM,KAAK,qBAAqB,QAAQ,EAAE,QAAQ,MAAM;AAC9D,YAAI,QAAQ,IAAI;AACd,eAAK,qBAAqB,QAAQ,EAAE,OAAO,KAAK,CAAC;AAAA,QACnD;AACA,YAAI,KAAK,qBAAqB,QAAQ,EAAE,WAAW,GAAG;AACpD,iBAAO,KAAK,qBAAqB,QAAQ;AAAA,QAC3C;AAAA,MACF,CAAC;AAAA,IACH;AACA,WAAO,gBAAgB,MAAM,MAAM,SAAS;AAAA,EAC9C;AACJ;AAEO,SAAS,wBAAwBA,SAAQ,gBAAgB;AAC9D,MAAI,CAACA,QAAO,mBAAmB;AAC7B;AAAA,EACF;AAEA,MAAIA,QAAO,kBAAkB,UAAU,YACnC,eAAe,WAAW,IAAI;AAChC,WAAO,kCAAkCA,OAAM;AAAA,EACjD;AAIA,QAAM,sBAAsBA,QAAO,kBAAkB,UAClD;AACH,EAAAA,QAAO,kBAAkB,UAAU,kBACjC,SAAS,kBAAkB;AACzB,UAAM,gBAAgB,oBAAoB,MAAM,IAAI;AACpD,SAAK,kBAAkB,KAAK,mBAAmB,CAAC;AAChD,WAAO,cAAc,IAAI,YAAU,KAAK,gBAAgB,OAAO,EAAE,CAAC;AAAA,EACpE;AAEF,QAAM,gBAAgBA,QAAO,kBAAkB,UAAU;AACzD,EAAAA,QAAO,kBAAkB,UAAU,YAAY,SAAS,UAAU,QAAQ;AACxE,SAAK,WAAW,KAAK,YAAY,CAAC;AAClC,SAAK,kBAAkB,KAAK,mBAAmB,CAAC;AAEhD,WAAO,UAAU,EAAE,QAAQ,WAAS;AAClC,YAAM,gBAAgB,KAAK,WAAW,EAAE,KAAK,OAAK,EAAE,UAAU,KAAK;AACnE,UAAI,eAAe;AACjB,cAAM,IAAI;AAAA,UAAa;AAAA,UACrB;AAAA,QAAoB;AAAA,MACxB;AAAA,IACF,CAAC;AAGD,QAAI,CAAC,KAAK,gBAAgB,OAAO,EAAE,GAAG;AACpC,YAAM,YAAY,IAAIA,QAAO,YAAY,OAAO,UAAU,CAAC;AAC3D,WAAK,SAAS,OAAO,EAAE,IAAI;AAC3B,WAAK,gBAAgB,UAAU,EAAE,IAAI;AACrC,eAAS;AAAA,IACX;AACA,kBAAc,MAAM,MAAM,CAAC,MAAM,CAAC;AAAA,EACpC;AAEA,QAAM,mBAAmBA,QAAO,kBAAkB,UAAU;AAC5D,EAAAA,QAAO,kBAAkB,UAAU,eACjC,SAAS,aAAa,QAAQ;AAC5B,SAAK,WAAW,KAAK,YAAY,CAAC;AAClC,SAAK,kBAAkB,KAAK,mBAAmB,CAAC;AAEhD,qBAAiB,MAAM,MAAM,CAAE,KAAK,SAAS,OAAO,EAAE,KAAK,MAAO,CAAC;AACnE,WAAO,KAAK,gBAAiB,KAAK,SAAS,OAAO,EAAE,IAClD,KAAK,SAAS,OAAO,EAAE,EAAE,KAAK,OAAO,EAAG;AAC1C,WAAO,KAAK,SAAS,OAAO,EAAE;AAAA,EAChC;AAEF,EAAAA,QAAO,kBAAkB,UAAU,WACjC,SAAS,SAAS,OAAO,QAAQ;AAC/B,QAAI,KAAK,mBAAmB,UAAU;AACpC,YAAM,IAAI;AAAA,QACR;AAAA,QACA;AAAA,MAAmB;AAAA,IACvB;AACA,UAAM,UAAU,CAAC,EAAE,MAAM,KAAK,WAAW,CAAC;AAC1C,QAAI,QAAQ,WAAW,KACnB,CAAC,QAAQ,CAAC,EAAE,UAAU,EAAE,KAAK,OAAK,MAAM,KAAK,GAAG;AAGlD,YAAM,IAAI;AAAA,QACR;AAAA,QAEA;AAAA,MAAmB;AAAA,IACvB;AAEA,UAAM,gBAAgB,KAAK,WAAW,EAAE,KAAK,OAAK,EAAE,UAAU,KAAK;AACnE,QAAI,eAAe;AACjB,YAAM,IAAI;AAAA,QAAa;AAAA,QACrB;AAAA,MAAoB;AAAA,IACxB;AAEA,SAAK,WAAW,KAAK,YAAY,CAAC;AAClC,SAAK,kBAAkB,KAAK,mBAAmB,CAAC;AAChD,UAAM,YAAY,KAAK,SAAS,OAAO,EAAE;AACzC,QAAI,WAAW;AAKb,gBAAU,SAAS,KAAK;AAGxB,cAAQ,QAAQ,EAAE,KAAK,MAAM;AAC3B,aAAK,cAAc,IAAI,MAAM,mBAAmB,CAAC;AAAA,MACnD,CAAC;AAAA,IACH,OAAO;AACL,YAAM,YAAY,IAAIA,QAAO,YAAY,CAAC,KAAK,CAAC;AAChD,WAAK,SAAS,OAAO,EAAE,IAAI;AAC3B,WAAK,gBAAgB,UAAU,EAAE,IAAI;AACrC,WAAK,UAAU,SAAS;AAAA,IAC1B;AACA,WAAO,KAAK,WAAW,EAAE,KAAK,OAAK,EAAE,UAAU,KAAK;AAAA,EACtD;AAIF,WAAS,wBAAwB,IAAI,aAAa;AAChD,QAAIC,OAAM,YAAY;AACtB,WAAO,KAAK,GAAG,mBAAmB,CAAC,CAAC,EAAE,QAAQ,gBAAc;AAC1D,YAAM,iBAAiB,GAAG,gBAAgB,UAAU;AACpD,YAAM,iBAAiB,GAAG,SAAS,eAAe,EAAE;AACpD,MAAAA,OAAMA,KAAI;AAAA,QAAQ,IAAI,OAAO,eAAe,IAAI,GAAG;AAAA,QACjD,eAAe;AAAA,MAAE;AAAA,IACrB,CAAC;AACD,WAAO,IAAI,sBAAsB;AAAA,MAC/B,MAAM,YAAY;AAAA,MAClB,KAAAA;AAAA,IACF,CAAC;AAAA,EACH;AACA,WAAS,wBAAwB,IAAI,aAAa;AAChD,QAAIA,OAAM,YAAY;AACtB,WAAO,KAAK,GAAG,mBAAmB,CAAC,CAAC,EAAE,QAAQ,gBAAc;AAC1D,YAAM,iBAAiB,GAAG,gBAAgB,UAAU;AACpD,YAAM,iBAAiB,GAAG,SAAS,eAAe,EAAE;AACpD,MAAAA,OAAMA,KAAI;AAAA,QAAQ,IAAI,OAAO,eAAe,IAAI,GAAG;AAAA,QACjD,eAAe;AAAA,MAAE;AAAA,IACrB,CAAC;AACD,WAAO,IAAI,sBAAsB;AAAA,MAC/B,MAAM,YAAY;AAAA,MAClB,KAAAA;AAAA,IACF,CAAC;AAAA,EACH;AACA,GAAC,eAAe,cAAc,EAAE,QAAQ,SAAS,QAAQ;AACvD,UAAM,eAAeD,QAAO,kBAAkB,UAAU,MAAM;AAC9D,UAAM,YAAY,EAAC,CAAC,MAAM,IAAI;AAC5B,YAAM,OAAO;AACb,YAAM,eAAe,UAAU,UAC3B,OAAO,UAAU,CAAC,MAAM;AAC5B,UAAI,cAAc;AAChB,eAAO,aAAa,MAAM,MAAM;AAAA,UAC9B,CAAC,gBAAgB;AACf,kBAAM,OAAO,wBAAwB,MAAM,WAAW;AACtD,iBAAK,CAAC,EAAE,MAAM,MAAM,CAAC,IAAI,CAAC;AAAA,UAC5B;AAAA,UACA,CAAC,QAAQ;AACP,gBAAI,KAAK,CAAC,GAAG;AACX,mBAAK,CAAC,EAAE,MAAM,MAAM,GAAG;AAAA,YACzB;AAAA,UACF;AAAA,UAAG,UAAU,CAAC;AAAA,QAChB,CAAC;AAAA,MACH;AACA,aAAO,aAAa,MAAM,MAAM,SAAS,EACtC,KAAK,iBAAe,wBAAwB,MAAM,WAAW,CAAC;AAAA,IACnE,EAAC;AACD,IAAAA,QAAO,kBAAkB,UAAU,MAAM,IAAI,UAAU,MAAM;AAAA,EAC/D,CAAC;AAED,QAAM,0BACFA,QAAO,kBAAkB,UAAU;AACvC,EAAAA,QAAO,kBAAkB,UAAU,sBACjC,SAAS,sBAAsB;AAC7B,QAAI,CAAC,UAAU,UAAU,CAAC,UAAU,CAAC,EAAE,MAAM;AAC3C,aAAO,wBAAwB,MAAM,MAAM,SAAS;AAAA,IACtD;AACA,cAAU,CAAC,IAAI,wBAAwB,MAAM,UAAU,CAAC,CAAC;AACzD,WAAO,wBAAwB,MAAM,MAAM,SAAS;AAAA,EACtD;AAIF,QAAM,uBAAuB,OAAO;AAAA,IAClCA,QAAO,kBAAkB;AAAA,IAAW;AAAA,EAAkB;AACxD,SAAO;AAAA,IAAeA,QAAO,kBAAkB;AAAA,IAC7C;AAAA,IAAoB;AAAA,MAClB,MAAM;AACJ,cAAM,cAAc,qBAAqB,IAAI,MAAM,IAAI;AACvD,YAAI,YAAY,SAAS,IAAI;AAC3B,iBAAO;AAAA,QACT;AACA,eAAO,wBAAwB,MAAM,WAAW;AAAA,MAClD;AAAA,IACF;AAAA,EAAC;AAEH,EAAAA,QAAO,kBAAkB,UAAU,cACjC,SAAS,YAAY,QAAQ;AAC3B,QAAI,KAAK,mBAAmB,UAAU;AACpC,YAAM,IAAI;AAAA,QACR;AAAA,QACA;AAAA,MAAmB;AAAA,IACvB;AAGA,QAAI,CAAC,OAAO,KAAK;AACf,YAAM,IAAI,aAAa,0FAC2B,WAAW;AAAA,IAC/D;AACA,UAAM,UAAU,OAAO,QAAQ;AAC/B,QAAI,CAAC,SAAS;AACZ,YAAM,IAAI;AAAA,QAAa;AAAA,QACrB;AAAA,MAAoB;AAAA,IACxB;AAGA,SAAK,WAAW,KAAK,YAAY,CAAC;AAClC,QAAI;AACJ,WAAO,KAAK,KAAK,QAAQ,EAAE,QAAQ,cAAY;AAC7C,YAAM,WAAW,KAAK,SAAS,QAAQ,EAAE,UAAU,EAChD,KAAK,WAAS,OAAO,UAAU,KAAK;AACvC,UAAI,UAAU;AACZ,iBAAS,KAAK,SAAS,QAAQ;AAAA,MACjC;AAAA,IACF,CAAC;AAED,QAAI,QAAQ;AACV,UAAI,OAAO,UAAU,EAAE,WAAW,GAAG;AAGnC,aAAK,aAAa,KAAK,gBAAgB,OAAO,EAAE,CAAC;AAAA,MACnD,OAAO;AAEL,eAAO,YAAY,OAAO,KAAK;AAAA,MACjC;AACA,WAAK,cAAc,IAAI,MAAM,mBAAmB,CAAC;AAAA,IACnD;AAAA,EACF;AACJ;AAEO,SAAS,mBAAmBA,SAAQ,gBAAgB;AACzD,MAAI,CAACA,QAAO,qBAAqBA,QAAO,yBAAyB;AAE/D,IAAAA,QAAO,oBAAoBA,QAAO;AAAA,EACpC;AACA,MAAI,CAACA,QAAO,mBAAmB;AAC7B;AAAA,EACF;AAGA,MAAI,eAAe,UAAU,IAAI;AAC/B,KAAC,uBAAuB,wBAAwB,iBAAiB,EAC9D,QAAQ,SAAS,QAAQ;AACxB,YAAM,eAAeA,QAAO,kBAAkB,UAAU,MAAM;AAC9D,YAAM,YAAY,EAAC,CAAC,MAAM,IAAI;AAC5B,kBAAU,CAAC,IAAI,KAAM,WAAW,oBAC9BA,QAAO,kBACPA,QAAO,uBAAuB,UAAU,CAAC,CAAC;AAC5C,eAAO,aAAa,MAAM,MAAM,SAAS;AAAA,MAC3C,EAAC;AACD,MAAAA,QAAO,kBAAkB,UAAU,MAAM,IAAI,UAAU,MAAM;AAAA,IAC/D,CAAC;AAAA,EACL;AACF;AAGO,SAAS,qBAAqBA,SAAQ,gBAAgB;AAC3D,EAAM,wBAAwBA,SAAQ,qBAAqB,OAAK;AAC9D,UAAM,KAAK,EAAE;AACb,QAAI,eAAe,UAAU,MAAO,GAAG,oBACnC,GAAG,iBAAiB,EAAE,iBAAiB,UAAW;AACpD,UAAI,GAAG,mBAAmB,UAAU;AAClC;AAAA,MACF;AAAA,IACF;AACA,WAAO;AAAA,EACT,CAAC;AACH;;;AG7rBA;AAAA;AAAA;AAAA;AAAA;AAAA,6BAAAE;AAAA,EAAA;AAAA,0BAAAC;AAAA,EAAA,mBAAAC;AAAA,EAAA,0BAAAC;AAAA,EAAA;AAAA;AAAA;AAAA;AAAA;;;ACYO,SAASC,kBAAiBC,SAAQ,gBAAgB;AACvD,QAAM,YAAYA,WAAUA,QAAO;AACnC,QAAM,mBAAmBA,WAAUA,QAAO;AAE1C,YAAU,eAAe,SAAS,aAAa,WAAW,SAAS;AAEjE,IAAM;AAAA,MAAW;AAAA,MACf;AAAA,IAAqC;AACvC,cAAU,aAAa,aAAa,WAAW,EAAE,KAAK,WAAW,OAAO;AAAA,EAC1E;AAEA,MAAI,EAAE,eAAe,UAAU,MAC3B,qBAAqB,UAAU,aAAa,wBAAwB,IAAI;AAC1E,UAAM,QAAQ,SAAS,KAAK,GAAG,GAAG;AAChC,UAAI,KAAK,OAAO,EAAE,KAAK,MAAM;AAC3B,YAAI,CAAC,IAAI,IAAI,CAAC;AACd,eAAO,IAAI,CAAC;AAAA,MACd;AAAA,IACF;AAEA,UAAM,qBAAqB,UAAU,aAAa,aAChD,KAAK,UAAU,YAAY;AAC7B,cAAU,aAAa,eAAe,SAAS,GAAG;AAChD,UAAI,OAAO,MAAM,YAAY,OAAO,EAAE,UAAU,UAAU;AACxD,YAAI,KAAK,MAAM,KAAK,UAAU,CAAC,CAAC;AAChC,cAAM,EAAE,OAAO,mBAAmB,oBAAoB;AACtD,cAAM,EAAE,OAAO,oBAAoB,qBAAqB;AAAA,MAC1D;AACA,aAAO,mBAAmB,CAAC;AAAA,IAC7B;AAEA,QAAI,oBAAoB,iBAAiB,UAAU,aAAa;AAC9D,YAAM,oBAAoB,iBAAiB,UAAU;AACrD,uBAAiB,UAAU,cAAc,WAAW;AAClD,cAAM,MAAM,kBAAkB,MAAM,MAAM,SAAS;AACnD,cAAM,KAAK,sBAAsB,iBAAiB;AAClD,cAAM,KAAK,uBAAuB,kBAAkB;AACpD,eAAO;AAAA,MACT;AAAA,IACF;AAEA,QAAI,oBAAoB,iBAAiB,UAAU,kBAAkB;AACnE,YAAM,yBACJ,iBAAiB,UAAU;AAC7B,uBAAiB,UAAU,mBAAmB,SAAS,GAAG;AACxD,YAAI,KAAK,SAAS,WAAW,OAAO,MAAM,UAAU;AAClD,cAAI,KAAK,MAAM,KAAK,UAAU,CAAC,CAAC;AAChC,gBAAM,GAAG,mBAAmB,oBAAoB;AAChD,gBAAM,GAAG,oBAAoB,qBAAqB;AAAA,QACpD;AACA,eAAO,uBAAuB,MAAM,MAAM,CAAC,CAAC,CAAC;AAAA,MAC/C;AAAA,IACF;AAAA,EACF;AACF;;;ACxDO,SAASC,qBAAoBC,SAAQ,sBAAsB;AAChE,MAAIA,QAAO,UAAU,gBACnB,qBAAqBA,QAAO,UAAU,cAAc;AACpD;AAAA,EACF;AACA,MAAI,CAAEA,QAAO,UAAU,cAAe;AACpC;AAAA,EACF;AACA,EAAAA,QAAO,UAAU,aAAa,kBAC5B,SAAS,gBAAgB,aAAa;AACpC,QAAI,EAAE,eAAe,YAAY,QAAQ;AACvC,YAAM,MAAM,IAAI,aAAa,wDACC;AAC9B,UAAI,OAAO;AAEX,UAAI,OAAO;AACX,aAAO,QAAQ,OAAO,GAAG;AAAA,IAC3B;AACA,QAAI,YAAY,UAAU,MAAM;AAC9B,kBAAY,QAAQ,EAAC,aAAa,qBAAoB;AAAA,IACxD,OAAO;AACL,kBAAY,MAAM,cAAc;AAAA,IAClC;AACA,WAAOA,QAAO,UAAU,aAAa,aAAa,WAAW;AAAA,EAC/D;AACJ;;;AFrBO,SAASC,aAAYC,SAAQ;AAClC,MAAI,OAAOA,YAAW,YAAYA,QAAO,iBACpC,cAAcA,QAAO,cAAc,aACpC,EAAE,iBAAiBA,QAAO,cAAc,YAAY;AACtD,WAAO,eAAeA,QAAO,cAAc,WAAW,eAAe;AAAA,MACnE,MAAM;AACJ,eAAO,EAAC,UAAU,KAAK,SAAQ;AAAA,MACjC;AAAA,IACF,CAAC;AAAA,EACH;AACF;AAEO,SAASC,oBAAmBD,SAAQ,gBAAgB;AACzD,MAAI,OAAOA,YAAW,YAClB,EAAEA,QAAO,qBAAqBA,QAAO,uBAAuB;AAC9D;AAAA,EACF;AACA,MAAI,CAACA,QAAO,qBAAqBA,QAAO,sBAAsB;AAE5D,IAAAA,QAAO,oBAAoBA,QAAO;AAAA,EACpC;AAEA,MAAI,eAAe,UAAU,IAAI;AAE/B,KAAC,uBAAuB,wBAAwB,iBAAiB,EAC9D,QAAQ,SAAS,QAAQ;AACxB,YAAM,eAAeA,QAAO,kBAAkB,UAAU,MAAM;AAC9D,YAAM,YAAY,EAAC,CAAC,MAAM,IAAI;AAC5B,kBAAU,CAAC,IAAI,KAAM,WAAW,oBAC9BA,QAAO,kBACPA,QAAO,uBAAuB,UAAU,CAAC,CAAC;AAC5C,eAAO,aAAa,MAAM,MAAM,SAAS;AAAA,MAC3C,EAAC;AACD,MAAAA,QAAO,kBAAkB,UAAU,MAAM,IAAI,UAAU,MAAM;AAAA,IAC/D,CAAC;AAAA,EACL;AAEA,QAAM,mBAAmB;AAAA,IACvB,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,iBAAiB;AAAA,EACnB;AAEA,QAAM,iBAAiBA,QAAO,kBAAkB,UAAU;AAC1D,EAAAA,QAAO,kBAAkB,UAAU,WAAW,SAAS,WAAW;AAChE,UAAM,CAAC,UAAU,QAAQ,KAAK,IAAI;AAClC,WAAO,eAAe,MAAM,MAAM,CAAC,YAAY,IAAI,CAAC,EACjD,KAAK,WAAS;AACb,UAAI,eAAe,UAAU,MAAM,CAAC,QAAQ;AAG1C,YAAI;AACF,gBAAM,QAAQ,UAAQ;AACpB,iBAAK,OAAO,iBAAiB,KAAK,IAAI,KAAK,KAAK;AAAA,UAClD,CAAC;AAAA,QACH,SAAS,GAAG;AACV,cAAI,EAAE,SAAS,aAAa;AAC1B,kBAAM;AAAA,UACR;AAEA,gBAAM,QAAQ,CAAC,MAAM,MAAM;AACzB,kBAAM,IAAI,GAAG,OAAO,OAAO,CAAC,GAAG,MAAM;AAAA,cACnC,MAAM,iBAAiB,KAAK,IAAI,KAAK,KAAK;AAAA,YAC5C,CAAC,CAAC;AAAA,UACJ,CAAC;AAAA,QACH;AAAA,MACF;AACA,aAAO;AAAA,IACT,CAAC,EACA,KAAK,QAAQ,KAAK;AAAA,EACvB;AACF;AAEO,SAAS,mBAAmBA,SAAQ;AACzC,MAAI,EAAE,OAAOA,YAAW,YAAYA,QAAO,qBACvCA,QAAO,eAAe;AACxB;AAAA,EACF;AACA,MAAIA,QAAO,gBAAgB,cAAcA,QAAO,aAAa,WAAW;AACtE;AAAA,EACF;AACA,QAAM,iBAAiBA,QAAO,kBAAkB,UAAU;AAC1D,MAAI,gBAAgB;AAClB,IAAAA,QAAO,kBAAkB,UAAU,aAAa,SAAS,aAAa;AACpE,YAAM,UAAU,eAAe,MAAM,MAAM,CAAC,CAAC;AAC7C,cAAQ,QAAQ,YAAU,OAAO,MAAM,IAAI;AAC3C,aAAO;AAAA,IACT;AAAA,EACF;AAEA,QAAM,eAAeA,QAAO,kBAAkB,UAAU;AACxD,MAAI,cAAc;AAChB,IAAAA,QAAO,kBAAkB,UAAU,WAAW,SAAS,WAAW;AAChE,YAAM,SAAS,aAAa,MAAM,MAAM,SAAS;AACjD,aAAO,MAAM;AACb,aAAO;AAAA,IACT;AAAA,EACF;AACA,EAAAA,QAAO,aAAa,UAAU,WAAW,SAAS,WAAW;AAC3D,WAAO,KAAK,QAAQ,KAAK,IAAI,SAAS,KAAK,KAAK,IAC9C,QAAQ,QAAQ,oBAAI,IAAI,CAAC;AAAA,EAC7B;AACF;AAEO,SAAS,qBAAqBA,SAAQ;AAC3C,MAAI,EAAE,OAAOA,YAAW,YAAYA,QAAO,qBACvCA,QAAO,eAAe;AACxB;AAAA,EACF;AACA,MAAIA,QAAO,gBAAgB,cAAcA,QAAO,eAAe,WAAW;AACxE;AAAA,EACF;AACA,QAAM,mBAAmBA,QAAO,kBAAkB,UAAU;AAC5D,MAAI,kBAAkB;AACpB,IAAAA,QAAO,kBAAkB,UAAU,eAAe,SAAS,eAAe;AACxE,YAAM,YAAY,iBAAiB,MAAM,MAAM,CAAC,CAAC;AACjD,gBAAU,QAAQ,cAAY,SAAS,MAAM,IAAI;AACjD,aAAO;AAAA,IACT;AAAA,EACF;AACA,EAAM,wBAAwBA,SAAQ,SAAS,OAAK;AAClD,MAAE,SAAS,MAAM,EAAE;AACnB,WAAO;AAAA,EACT,CAAC;AACD,EAAAA,QAAO,eAAe,UAAU,WAAW,SAAS,WAAW;AAC7D,WAAO,KAAK,IAAI,SAAS,KAAK,KAAK;AAAA,EACrC;AACF;AAEO,SAAS,iBAAiBA,SAAQ;AACvC,MAAI,CAACA,QAAO,qBACR,kBAAkBA,QAAO,kBAAkB,WAAW;AACxD;AAAA,EACF;AACA,EAAAA,QAAO,kBAAkB,UAAU,eACjC,SAAS,aAAa,QAAQ;AAC5B,IAAM,WAAW,gBAAgB,aAAa;AAC9C,SAAK,WAAW,EAAE,QAAQ,YAAU;AAClC,UAAI,OAAO,SAAS,OAAO,UAAU,EAAE,SAAS,OAAO,KAAK,GAAG;AAC7D,aAAK,YAAY,MAAM;AAAA,MACzB;AAAA,IACF,CAAC;AAAA,EACH;AACJ;AAEO,SAAS,mBAAmBA,SAAQ;AAGzC,MAAIA,QAAO,eAAe,CAACA,QAAO,gBAAgB;AAChD,IAAAA,QAAO,iBAAiBA,QAAO;AAAA,EACjC;AACF;AAEO,SAAS,mBAAmBA,SAAQ;AAIzC,MAAI,EAAE,OAAOA,YAAW,YAAYA,QAAO,oBAAoB;AAC7D;AAAA,EACF;AACA,QAAM,qBAAqBA,QAAO,kBAAkB,UAAU;AAC9D,MAAI,oBAAoB;AACtB,IAAAA,QAAO,kBAAkB,UAAU,iBACjC,SAAS,iBAAiB;AACxB,WAAK,wBAAwB,CAAC;AAE9B,UAAI,gBAAgB,UAAU,CAAC,KAAK,UAAU,CAAC,EAAE;AACjD,UAAI,kBAAkB,QAAW;AAC/B,wBAAgB,CAAC;AAAA,MACnB;AACA,sBAAgB,CAAC,GAAG,aAAa;AACjC,YAAM,qBAAqB,cAAc,SAAS;AAClD,UAAI,oBAAoB;AAEtB,sBAAc,QAAQ,CAAC,kBAAkB;AACvC,cAAI,SAAS,eAAe;AAC1B,kBAAM,WAAW;AACjB,gBAAI,CAAC,SAAS,KAAK,cAAc,GAAG,GAAG;AACrC,oBAAM,IAAI,UAAU,6BAA6B;AAAA,YACnD;AAAA,UACF;AACA,cAAI,2BAA2B,eAAe;AAC5C,gBAAI,EAAE,WAAW,cAAc,qBAAqB,KAAK,IAAM;AAC7D,oBAAM,IAAI,WAAW,yCAAyC;AAAA,YAChE;AAAA,UACF;AACA,cAAI,kBAAkB,eAAe;AACnC,gBAAI,EAAE,WAAW,cAAc,YAAY,KAAK,IAAI;AAClD,oBAAM,IAAI,WAAW,8BAA8B;AAAA,YACrD;AAAA,UACF;AAAA,QACF,CAAC;AAAA,MACH;AACA,YAAM,cAAc,mBAAmB,MAAM,MAAM,SAAS;AAC5D,UAAI,oBAAoB;AAQtB,cAAM,EAAC,OAAM,IAAI;AACjB,cAAM,SAAS,OAAO,cAAc;AACpC,YAAI,EAAE,eAAe;AAAA,QAEhB,OAAO,UAAU,WAAW,KAC5B,OAAO,KAAK,OAAO,UAAU,CAAC,CAAC,EAAE,WAAW,GAAI;AACnD,iBAAO,YAAY;AACnB,iBAAO,gBAAgB;AACvB,eAAK,sBAAsB;AAAA,YAAK,OAAO,cAAc,MAAM,EACxD,KAAK,MAAM;AACV,qBAAO,OAAO;AAAA,YAChB,CAAC,EAAE,MAAM,MAAM;AACb,qBAAO,OAAO;AAAA,YAChB,CAAC;AAAA,UACH;AAAA,QACF;AAAA,MACF;AACA,aAAO;AAAA,IACT;AAAA,EACJ;AACF;AAEO,SAAS,kBAAkBA,SAAQ;AACxC,MAAI,EAAE,OAAOA,YAAW,YAAYA,QAAO,eAAe;AACxD;AAAA,EACF;AACA,QAAM,oBAAoBA,QAAO,aAAa,UAAU;AACxD,MAAI,mBAAmB;AACrB,IAAAA,QAAO,aAAa,UAAU,gBAC5B,SAAS,gBAAgB;AACvB,YAAM,SAAS,kBAAkB,MAAM,MAAM,SAAS;AACtD,UAAI,EAAE,eAAe,SAAS;AAC5B,eAAO,YAAY,CAAC,EAAE,OAAO,KAAK,iBAAiB,CAAC,CAAC,CAAC,CAAC;AAAA,MACzD;AACA,aAAO;AAAA,IACT;AAAA,EACJ;AACF;AAEO,SAAS,gBAAgBA,SAAQ;AAItC,MAAI,EAAE,OAAOA,YAAW,YAAYA,QAAO,oBAAoB;AAC7D;AAAA,EACF;AACA,QAAM,kBAAkBA,QAAO,kBAAkB,UAAU;AAC3D,EAAAA,QAAO,kBAAkB,UAAU,cAAc,SAAS,cAAc;AACtE,QAAI,KAAK,yBAAyB,KAAK,sBAAsB,QAAQ;AACnE,aAAO,QAAQ,IAAI,KAAK,qBAAqB,EAC1C,KAAK,MAAM;AACV,eAAO,gBAAgB,MAAM,MAAM,SAAS;AAAA,MAC9C,CAAC,EACA,QAAQ,MAAM;AACb,aAAK,wBAAwB,CAAC;AAAA,MAChC,CAAC;AAAA,IACL;AACA,WAAO,gBAAgB,MAAM,MAAM,SAAS;AAAA,EAC9C;AACF;AAEO,SAAS,iBAAiBA,SAAQ;AAIvC,MAAI,EAAE,OAAOA,YAAW,YAAYA,QAAO,oBAAoB;AAC7D;AAAA,EACF;AACA,QAAM,mBAAmBA,QAAO,kBAAkB,UAAU;AAC5D,EAAAA,QAAO,kBAAkB,UAAU,eAAe,SAAS,eAAe;AACxE,QAAI,KAAK,yBAAyB,KAAK,sBAAsB,QAAQ;AACnE,aAAO,QAAQ,IAAI,KAAK,qBAAqB,EAC1C,KAAK,MAAM;AACV,eAAO,iBAAiB,MAAM,MAAM,SAAS;AAAA,MAC/C,CAAC,EACA,QAAQ,MAAM;AACb,aAAK,wBAAwB,CAAC;AAAA,MAChC,CAAC;AAAA,IACL;AACA,WAAO,iBAAiB,MAAM,MAAM,SAAS;AAAA,EAC/C;AACF;;;AG3SA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,0BAAAE;AAAA,EAAA;AAAA;AAAA;AAAA;AAAA;AAUO,SAAS,oBAAoBC,SAAQ;AAC1C,MAAI,OAAOA,YAAW,YAAY,CAACA,QAAO,mBAAmB;AAC3D;AAAA,EACF;AACA,MAAI,EAAE,qBAAqBA,QAAO,kBAAkB,YAAY;AAC9D,IAAAA,QAAO,kBAAkB,UAAU,kBACjC,SAAS,kBAAkB;AACzB,UAAI,CAAC,KAAK,eAAe;AACvB,aAAK,gBAAgB,CAAC;AAAA,MACxB;AACA,aAAO,KAAK;AAAA,IACd;AAAA,EACJ;AACA,MAAI,EAAE,eAAeA,QAAO,kBAAkB,YAAY;AACxD,UAAM,YAAYA,QAAO,kBAAkB,UAAU;AACrD,IAAAA,QAAO,kBAAkB,UAAU,YAAY,SAAS,UAAU,QAAQ;AACxE,UAAI,CAAC,KAAK,eAAe;AACvB,aAAK,gBAAgB,CAAC;AAAA,MACxB;AACA,UAAI,CAAC,KAAK,cAAc,SAAS,MAAM,GAAG;AACxC,aAAK,cAAc,KAAK,MAAM;AAAA,MAChC;AAGA,aAAO,eAAe,EAAE,QAAQ,WAAS,UAAU;AAAA,QAAK;AAAA,QAAM;AAAA,QAC5D;AAAA,MAAM,CAAC;AACT,aAAO,eAAe,EAAE,QAAQ,WAAS,UAAU;AAAA,QAAK;AAAA,QAAM;AAAA,QAC5D;AAAA,MAAM,CAAC;AAAA,IACX;AAEA,IAAAA,QAAO,kBAAkB,UAAU,WACjC,SAAS,SAAS,UAAU,SAAS;AACnC,UAAI,SAAS;AACX,gBAAQ,QAAQ,CAAC,WAAW;AAC1B,cAAI,CAAC,KAAK,eAAe;AACvB,iBAAK,gBAAgB,CAAC,MAAM;AAAA,UAC9B,WAAW,CAAC,KAAK,cAAc,SAAS,MAAM,GAAG;AAC/C,iBAAK,cAAc,KAAK,MAAM;AAAA,UAChC;AAAA,QACF,CAAC;AAAA,MACH;AACA,aAAO,UAAU,MAAM,MAAM,SAAS;AAAA,IACxC;AAAA,EACJ;AACA,MAAI,EAAE,kBAAkBA,QAAO,kBAAkB,YAAY;AAC3D,IAAAA,QAAO,kBAAkB,UAAU,eACjC,SAAS,aAAa,QAAQ;AAC5B,UAAI,CAAC,KAAK,eAAe;AACvB,aAAK,gBAAgB,CAAC;AAAA,MACxB;AACA,YAAM,QAAQ,KAAK,cAAc,QAAQ,MAAM;AAC/C,UAAI,UAAU,IAAI;AAChB;AAAA,MACF;AACA,WAAK,cAAc,OAAO,OAAO,CAAC;AAClC,YAAM,SAAS,OAAO,UAAU;AAChC,WAAK,WAAW,EAAE,QAAQ,YAAU;AAClC,YAAI,OAAO,SAAS,OAAO,KAAK,GAAG;AACjC,eAAK,YAAY,MAAM;AAAA,QACzB;AAAA,MACF,CAAC;AAAA,IACH;AAAA,EACJ;AACF;AAEO,SAAS,qBAAqBA,SAAQ;AAC3C,MAAI,OAAOA,YAAW,YAAY,CAACA,QAAO,mBAAmB;AAC3D;AAAA,EACF;AACA,MAAI,EAAE,sBAAsBA,QAAO,kBAAkB,YAAY;AAC/D,IAAAA,QAAO,kBAAkB,UAAU,mBACjC,SAAS,mBAAmB;AAC1B,aAAO,KAAK,iBAAiB,KAAK,iBAAiB,CAAC;AAAA,IACtD;AAAA,EACJ;AACA,MAAI,EAAE,iBAAiBA,QAAO,kBAAkB,YAAY;AAC1D,WAAO,eAAeA,QAAO,kBAAkB,WAAW,eAAe;AAAA,MACvE,MAAM;AACJ,eAAO,KAAK;AAAA,MACd;AAAA,MACA,IAAI,GAAG;AACL,YAAI,KAAK,cAAc;AACrB,eAAK,oBAAoB,aAAa,KAAK,YAAY;AACvD,eAAK,oBAAoB,SAAS,KAAK,gBAAgB;AAAA,QACzD;AACA,aAAK,iBAAiB,aAAa,KAAK,eAAe,CAAC;AACxD,aAAK,iBAAiB,SAAS,KAAK,mBAAmB,CAAC,MAAM;AAC5D,YAAE,QAAQ,QAAQ,YAAU;AAC1B,gBAAI,CAAC,KAAK,gBAAgB;AACxB,mBAAK,iBAAiB,CAAC;AAAA,YACzB;AACA,gBAAI,KAAK,eAAe,SAAS,MAAM,GAAG;AACxC;AAAA,YACF;AACA,iBAAK,eAAe,KAAK,MAAM;AAC/B,kBAAM,QAAQ,IAAI,MAAM,WAAW;AACnC,kBAAM,SAAS;AACf,iBAAK,cAAc,KAAK;AAAA,UAC1B,CAAC;AAAA,QACH,CAAC;AAAA,MACH;AAAA,IACF,CAAC;AACD,UAAM,2BACJA,QAAO,kBAAkB,UAAU;AACrC,IAAAA,QAAO,kBAAkB,UAAU,uBACjC,SAAS,uBAAuB;AAC9B,YAAM,KAAK;AACX,UAAI,CAAC,KAAK,kBAAkB;AAC1B,aAAK,iBAAiB,SAAS,KAAK,mBAAmB,SAAS,GAAG;AACjE,YAAE,QAAQ,QAAQ,YAAU;AAC1B,gBAAI,CAAC,GAAG,gBAAgB;AACtB,iBAAG,iBAAiB,CAAC;AAAA,YACvB;AACA,gBAAI,GAAG,eAAe,QAAQ,MAAM,KAAK,GAAG;AAC1C;AAAA,YACF;AACA,eAAG,eAAe,KAAK,MAAM;AAC7B,kBAAM,QAAQ,IAAI,MAAM,WAAW;AACnC,kBAAM,SAAS;AACf,eAAG,cAAc,KAAK;AAAA,UACxB,CAAC;AAAA,QACH,CAAC;AAAA,MACH;AACA,aAAO,yBAAyB,MAAM,IAAI,SAAS;AAAA,IACrD;AAAA,EACJ;AACF;AAEO,SAAS,iBAAiBA,SAAQ;AACvC,MAAI,OAAOA,YAAW,YAAY,CAACA,QAAO,mBAAmB;AAC3D;AAAA,EACF;AACA,QAAM,YAAYA,QAAO,kBAAkB;AAC3C,QAAM,kBAAkB,UAAU;AAClC,QAAM,mBAAmB,UAAU;AACnC,QAAM,sBAAsB,UAAU;AACtC,QAAM,uBAAuB,UAAU;AACvC,QAAM,kBAAkB,UAAU;AAElC,YAAU,cACR,SAAS,YAAY,iBAAiB,iBAAiB;AACrD,UAAM,UAAW,UAAU,UAAU,IAAK,UAAU,CAAC,IAAI,UAAU,CAAC;AACpE,UAAM,UAAU,gBAAgB,MAAM,MAAM,CAAC,OAAO,CAAC;AACrD,QAAI,CAAC,iBAAiB;AACpB,aAAO;AAAA,IACT;AACA,YAAQ,KAAK,iBAAiB,eAAe;AAC7C,WAAO,QAAQ,QAAQ;AAAA,EACzB;AAEF,YAAU,eACR,SAAS,aAAa,iBAAiB,iBAAiB;AACtD,UAAM,UAAW,UAAU,UAAU,IAAK,UAAU,CAAC,IAAI,UAAU,CAAC;AACpE,UAAM,UAAU,iBAAiB,MAAM,MAAM,CAAC,OAAO,CAAC;AACtD,QAAI,CAAC,iBAAiB;AACpB,aAAO;AAAA,IACT;AACA,YAAQ,KAAK,iBAAiB,eAAe;AAC7C,WAAO,QAAQ,QAAQ;AAAA,EACzB;AAEF,MAAI,eAAe,SAAS,aAAa,iBAAiB,iBAAiB;AACzE,UAAM,UAAU,oBAAoB,MAAM,MAAM,CAAC,WAAW,CAAC;AAC7D,QAAI,CAAC,iBAAiB;AACpB,aAAO;AAAA,IACT;AACA,YAAQ,KAAK,iBAAiB,eAAe;AAC7C,WAAO,QAAQ,QAAQ;AAAA,EACzB;AACA,YAAU,sBAAsB;AAEhC,iBAAe,SAAS,aAAa,iBAAiB,iBAAiB;AACrE,UAAM,UAAU,qBAAqB,MAAM,MAAM,CAAC,WAAW,CAAC;AAC9D,QAAI,CAAC,iBAAiB;AACpB,aAAO;AAAA,IACT;AACA,YAAQ,KAAK,iBAAiB,eAAe;AAC7C,WAAO,QAAQ,QAAQ;AAAA,EACzB;AACA,YAAU,uBAAuB;AAEjC,iBAAe,SAAS,WAAW,iBAAiB,iBAAiB;AACnE,UAAM,UAAU,gBAAgB,MAAM,MAAM,CAAC,SAAS,CAAC;AACvD,QAAI,CAAC,iBAAiB;AACpB,aAAO;AAAA,IACT;AACA,YAAQ,KAAK,iBAAiB,eAAe;AAC7C,WAAO,QAAQ,QAAQ;AAAA,EACzB;AACA,YAAU,kBAAkB;AAC9B;AAEO,SAASC,kBAAiBD,SAAQ;AACvC,QAAM,YAAYA,WAAUA,QAAO;AAEnC,MAAI,UAAU,gBAAgB,UAAU,aAAa,cAAc;AAEjE,UAAM,eAAe,UAAU;AAC/B,UAAM,gBAAgB,aAAa,aAAa,KAAK,YAAY;AACjE,cAAU,aAAa,eAAe,CAAC,gBAAgB;AACrD,aAAO,cAAc,gBAAgB,WAAW,CAAC;AAAA,IACnD;AAAA,EACF;AAEA,MAAI,CAAC,UAAU,gBAAgB,UAAU,gBACvC,UAAU,aAAa,cAAc;AACrC,cAAU,gBAAe,SAAS,aAAa,aAAa,IAAI,OAAO;AACrE,gBAAU,aAAa,aAAa,WAAW,EAC5C,KAAK,IAAI,KAAK;AAAA,IACnB,GAAE,KAAK,SAAS;AAAA,EAClB;AACF;AAEO,SAAS,gBAAgB,aAAa;AAC3C,MAAI,eAAe,YAAY,UAAU,QAAW;AAClD,WAAO,OAAO;AAAA,MAAO,CAAC;AAAA,MACpB;AAAA,MACA,EAAC,OAAa,cAAc,YAAY,KAAK,EAAC;AAAA,IAChD;AAAA,EACF;AAEA,SAAO;AACT;AAEO,SAAS,qBAAqBA,SAAQ;AAC3C,MAAI,CAACA,QAAO,mBAAmB;AAC7B;AAAA,EACF;AAEA,QAAM,qBAAqBA,QAAO;AAClC,EAAAA,QAAO,oBACL,SAAS,kBAAkB,UAAU,eAAe;AAClD,QAAI,YAAY,SAAS,YAAY;AACnC,YAAM,gBAAgB,CAAC;AACvB,eAAS,IAAI,GAAG,IAAI,SAAS,WAAW,QAAQ,KAAK;AACnD,YAAI,SAAS,SAAS,WAAW,CAAC;AAClC,YAAI,OAAO,SAAS,UAAa,OAAO,KAAK;AAC3C,UAAM,WAAW,oBAAoB,mBAAmB;AACxD,mBAAS,KAAK,MAAM,KAAK,UAAU,MAAM,CAAC;AAC1C,iBAAO,OAAO,OAAO;AACrB,iBAAO,OAAO;AACd,wBAAc,KAAK,MAAM;AAAA,QAC3B,OAAO;AACL,wBAAc,KAAK,SAAS,WAAW,CAAC,CAAC;AAAA,QAC3C;AAAA,MACF;AACA,eAAS,aAAa;AAAA,IACxB;AACA,WAAO,IAAI,mBAAmB,UAAU,aAAa;AAAA,EACvD;AACF,EAAAA,QAAO,kBAAkB,YAAY,mBAAmB;AAExD,MAAI,yBAAyB,oBAAoB;AAC/C,WAAO,eAAeA,QAAO,mBAAmB,uBAAuB;AAAA,MACrE,MAAM;AACJ,eAAO,mBAAmB;AAAA,MAC5B;AAAA,IACF,CAAC;AAAA,EACH;AACF;AAEO,SAAS,0BAA0BA,SAAQ;AAEhD,MAAI,OAAOA,YAAW,YAAYA,QAAO,iBACrC,cAAcA,QAAO,cAAc,aACnC,EAAE,iBAAiBA,QAAO,cAAc,YAAY;AACtD,WAAO,eAAeA,QAAO,cAAc,WAAW,eAAe;AAAA,MACnE,MAAM;AACJ,eAAO,EAAC,UAAU,KAAK,SAAQ;AAAA,MACjC;AAAA,IACF,CAAC;AAAA,EACH;AACF;AAEO,SAAS,sBAAsBA,SAAQ;AAC5C,QAAM,kBAAkBA,QAAO,kBAAkB,UAAU;AAC3D,EAAAA,QAAO,kBAAkB,UAAU,cACjC,SAAS,YAAY,cAAc;AACjC,QAAI,cAAc;AAChB,UAAI,OAAO,aAAa,wBAAwB,aAAa;AAE3D,qBAAa,sBACX,CAAC,CAAC,aAAa;AAAA,MACnB;AACA,YAAM,mBAAmB,KAAK,gBAAgB,EAAE,KAAK,iBACnD,YAAY,SAAS,MAAM,SAAS,OAAO;AAC7C,UAAI,aAAa,wBAAwB,SAAS,kBAAkB;AAClE,YAAI,iBAAiB,cAAc,YAAY;AAC7C,cAAI,iBAAiB,cAAc;AACjC,6BAAiB,aAAa,UAAU;AAAA,UAC1C,OAAO;AACL,6BAAiB,YAAY;AAAA,UAC/B;AAAA,QACF,WAAW,iBAAiB,cAAc,YAAY;AACpD,cAAI,iBAAiB,cAAc;AACjC,6BAAiB,aAAa,UAAU;AAAA,UAC1C,OAAO;AACL,6BAAiB,YAAY;AAAA,UAC/B;AAAA,QACF;AAAA,MACF,WAAW,aAAa,wBAAwB,QAC5C,CAAC,kBAAkB;AACrB,aAAK,eAAe,SAAS,EAAC,WAAW,WAAU,CAAC;AAAA,MACtD;AAEA,UAAI,OAAO,aAAa,wBAAwB,aAAa;AAE3D,qBAAa,sBACX,CAAC,CAAC,aAAa;AAAA,MACnB;AACA,YAAM,mBAAmB,KAAK,gBAAgB,EAAE,KAAK,iBACnD,YAAY,SAAS,MAAM,SAAS,OAAO;AAC7C,UAAI,aAAa,wBAAwB,SAAS,kBAAkB;AAClE,YAAI,iBAAiB,cAAc,YAAY;AAC7C,cAAI,iBAAiB,cAAc;AACjC,6BAAiB,aAAa,UAAU;AAAA,UAC1C,OAAO;AACL,6BAAiB,YAAY;AAAA,UAC/B;AAAA,QACF,WAAW,iBAAiB,cAAc,YAAY;AACpD,cAAI,iBAAiB,cAAc;AACjC,6BAAiB,aAAa,UAAU;AAAA,UAC1C,OAAO;AACL,6BAAiB,YAAY;AAAA,UAC/B;AAAA,QACF;AAAA,MACF,WAAW,aAAa,wBAAwB,QAC5C,CAAC,kBAAkB;AACrB,aAAK,eAAe,SAAS,EAAC,WAAW,WAAU,CAAC;AAAA,MACtD;AAAA,IACF;AACA,WAAO,gBAAgB,MAAM,MAAM,SAAS;AAAA,EAC9C;AACJ;AAEO,SAAS,iBAAiBA,SAAQ;AACvC,MAAI,OAAOA,YAAW,YAAYA,QAAO,cAAc;AACrD;AAAA,EACF;AACA,EAAAA,QAAO,eAAeA,QAAO;AAC/B;;;AC9VA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAUA,iBAAqB;AAGd,SAAS,oBAAoBE,SAAQ;AAG1C,MAAI,CAACA,QAAO,mBAAoBA,QAAO,mBAAmB,gBACtDA,QAAO,gBAAgB,WAAY;AACrC;AAAA,EACF;AAEA,QAAM,wBAAwBA,QAAO;AACrC,EAAAA,QAAO,kBAAkB,SAAS,gBAAgB,MAAM;AAEtD,QAAI,OAAO,SAAS,YAAY,KAAK,aACjC,KAAK,UAAU,QAAQ,IAAI,MAAM,GAAG;AACtC,aAAO,KAAK,MAAM,KAAK,UAAU,IAAI,CAAC;AACtC,WAAK,YAAY,KAAK,UAAU,UAAU,CAAC;AAAA,IAC7C;AAEA,QAAI,KAAK,aAAa,KAAK,UAAU,QAAQ;AAE3C,YAAM,kBAAkB,IAAI,sBAAsB,IAAI;AACtD,YAAM,kBAAkB,WAAAC,QAAS,eAAe,KAAK,SAAS;AAC9D,iBAAW,OAAO,iBAAiB;AACjC,YAAI,EAAE,OAAO,kBAAkB;AAC7B,iBAAO;AAAA,YAAe;AAAA,YAAiB;AAAA,YACrC,EAAC,OAAO,gBAAgB,GAAG,EAAC;AAAA,UAAC;AAAA,QACjC;AAAA,MACF;AAGA,sBAAgB,SAAS,SAAS,SAAS;AACzC,eAAO;AAAA,UACL,WAAW,gBAAgB;AAAA,UAC3B,QAAQ,gBAAgB;AAAA,UACxB,eAAe,gBAAgB;AAAA,UAC/B,kBAAkB,gBAAgB;AAAA,QACpC;AAAA,MACF;AACA,aAAO;AAAA,IACT;AACA,WAAO,IAAI,sBAAsB,IAAI;AAAA,EACvC;AACA,EAAAD,QAAO,gBAAgB,YAAY,sBAAsB;AAIzD,EAAM,wBAAwBA,SAAQ,gBAAgB,OAAK;AACzD,QAAI,EAAE,WAAW;AACf,aAAO,eAAe,GAAG,aAAa;AAAA,QACpC,OAAO,IAAIA,QAAO,gBAAgB,EAAE,SAAS;AAAA,QAC7C,UAAU;AAAA,MACZ,CAAC;AAAA,IACH;AACA,WAAO;AAAA,EACT,CAAC;AACH;AAEO,SAAS,iCAAiCA,SAAQ;AACvD,MAAI,CAACA,QAAO,mBAAoBA,QAAO,mBAAmB,mBACtDA,QAAO,gBAAgB,WAAY;AACrC;AAAA,EACF;AAIA,EAAM,wBAAwBA,SAAQ,gBAAgB,OAAK;AACzD,QAAI,EAAE,WAAW;AACf,YAAM,kBAAkB,WAAAC,QAAS,eAAe,EAAE,UAAU,SAAS;AACrE,UAAI,gBAAgB,SAAS,SAAS;AAGpC,UAAE,UAAU,gBAAgB;AAAA,UAC1B,GAAG;AAAA,UACH,GAAG;AAAA,UACH,GAAG;AAAA,QACL,EAAE,gBAAgB,YAAY,EAAE;AAAA,MAClC;AAAA,IACF;AACA,WAAO;AAAA,EACT,CAAC;AACH;AAEO,SAAS,mBAAmBD,SAAQ,gBAAgB;AACzD,MAAI,CAACA,QAAO,mBAAmB;AAC7B;AAAA,EACF;AAEA,MAAI,EAAE,UAAUA,QAAO,kBAAkB,YAAY;AACnD,WAAO,eAAeA,QAAO,kBAAkB,WAAW,QAAQ;AAAA,MAChE,MAAM;AACJ,eAAO,OAAO,KAAK,UAAU,cAAc,OAAO,KAAK;AAAA,MACzD;AAAA,IACF,CAAC;AAAA,EACH;AAEA,QAAM,oBAAoB,SAAS,aAAa;AAC9C,QAAI,CAAC,eAAe,CAAC,YAAY,KAAK;AACpC,aAAO;AAAA,IACT;AACA,UAAM,WAAW,WAAAC,QAAS,cAAc,YAAY,GAAG;AACvD,aAAS,MAAM;AACf,WAAO,SAAS,KAAK,kBAAgB;AACnC,YAAM,QAAQ,WAAAA,QAAS,WAAW,YAAY;AAC9C,aAAO,SAAS,MAAM,SAAS,iBACxB,MAAM,SAAS,QAAQ,MAAM,MAAM;AAAA,IAC5C,CAAC;AAAA,EACH;AAEA,QAAM,0BAA0B,SAAS,aAAa;AAEpD,UAAM,QAAQ,YAAY,IAAI,MAAM,iCAAiC;AACrE,QAAI,UAAU,QAAQ,MAAM,SAAS,GAAG;AACtC,aAAO;AAAA,IACT;AACA,UAAM,UAAU,SAAS,MAAM,CAAC,GAAG,EAAE;AAErC,WAAO,YAAY,UAAU,KAAK;AAAA,EACpC;AAEA,QAAM,2BAA2B,SAAS,iBAAiB;AAKzD,QAAI,wBAAwB;AAC5B,QAAI,eAAe,YAAY,WAAW;AACxC,UAAI,eAAe,UAAU,IAAI;AAC/B,YAAI,oBAAoB,IAAI;AAG1B,kCAAwB;AAAA,QAC1B,OAAO;AAGL,kCAAwB;AAAA,QAC1B;AAAA,MACF,WAAW,eAAe,UAAU,IAAI;AAKtC,gCACE,eAAe,YAAY,KAAK,QAAQ;AAAA,MAC5C,OAAO;AAEL,gCAAwB;AAAA,MAC1B;AAAA,IACF;AACA,WAAO;AAAA,EACT;AAEA,QAAM,oBAAoB,SAAS,aAAa,iBAAiB;AAG/D,QAAI,iBAAiB;AAKrB,QAAI,eAAe,YAAY,aACvB,eAAe,YAAY,IAAI;AACrC,uBAAiB;AAAA,IACnB;AAEA,UAAM,QAAQ,WAAAA,QAAS;AAAA,MAAY,YAAY;AAAA,MAC7C;AAAA,IAAqB;AACvB,QAAI,MAAM,SAAS,GAAG;AACpB,uBAAiB,SAAS,MAAM,CAAC,EAAE,UAAU,EAAE,GAAG,EAAE;AAAA,IACtD,WAAW,eAAe,YAAY,aAC1B,oBAAoB,IAAI;AAIlC,uBAAiB;AAAA,IACnB;AACA,WAAO;AAAA,EACT;AAEA,QAAM,2BACFD,QAAO,kBAAkB,UAAU;AACvC,EAAAA,QAAO,kBAAkB,UAAU,uBACjC,SAAS,uBAAuB;AAC9B,SAAK,QAAQ;AAIb,QAAI,eAAe,YAAY,YAAY,eAAe,WAAW,IAAI;AACvE,YAAM,EAAC,aAAY,IAAI,KAAK,iBAAiB;AAC7C,UAAI,iBAAiB,UAAU;AAC7B,eAAO,eAAe,MAAM,QAAQ;AAAA,UAClC,MAAM;AACJ,mBAAO,OAAO,KAAK,UAAU,cAAc,OAAO,KAAK;AAAA,UACzD;AAAA,UACA,YAAY;AAAA,UACZ,cAAc;AAAA,QAChB,CAAC;AAAA,MACH;AAAA,IACF;AAEA,QAAI,kBAAkB,UAAU,CAAC,CAAC,GAAG;AAEnC,YAAM,YAAY,wBAAwB,UAAU,CAAC,CAAC;AAGtD,YAAM,aAAa,yBAAyB,SAAS;AAGrD,YAAM,YAAY,kBAAkB,UAAU,CAAC,GAAG,SAAS;AAG3D,UAAI;AACJ,UAAI,eAAe,KAAK,cAAc,GAAG;AACvC,yBAAiB,OAAO;AAAA,MAC1B,WAAW,eAAe,KAAK,cAAc,GAAG;AAC9C,yBAAiB,KAAK,IAAI,YAAY,SAAS;AAAA,MACjD,OAAO;AACL,yBAAiB,KAAK,IAAI,YAAY,SAAS;AAAA,MACjD;AAIA,YAAM,OAAO,CAAC;AACd,aAAO,eAAe,MAAM,kBAAkB;AAAA,QAC5C,MAAM;AACJ,iBAAO;AAAA,QACT;AAAA,MACF,CAAC;AACD,WAAK,QAAQ;AAAA,IACf;AAEA,WAAO,yBAAyB,MAAM,MAAM,SAAS;AAAA,EACvD;AACJ;AAEO,SAAS,uBAAuBA,SAAQ;AAC7C,MAAI,EAAEA,QAAO,qBACT,uBAAuBA,QAAO,kBAAkB,YAAY;AAC9D;AAAA,EACF;AAMA,WAAS,WAAW,IAAI,IAAI;AAC1B,UAAM,sBAAsB,GAAG;AAC/B,OAAG,OAAO,SAAS,OAAO;AACxB,YAAM,OAAO,UAAU,CAAC;AACxB,YAAM,SAAS,KAAK,UAAU,KAAK,QAAQ,KAAK;AAChD,UAAI,GAAG,eAAe,UAClB,GAAG,QAAQ,SAAS,GAAG,KAAK,gBAAgB;AAC9C,cAAM,IAAI,UAAU,8CAClB,GAAG,KAAK,iBAAiB,SAAS;AAAA,MACtC;AACA,aAAO,oBAAoB,MAAM,IAAI,SAAS;AAAA,IAChD;AAAA,EACF;AACA,QAAM,wBACJA,QAAO,kBAAkB,UAAU;AACrC,EAAAA,QAAO,kBAAkB,UAAU,oBACjC,SAAS,oBAAoB;AAC3B,UAAM,cAAc,sBAAsB,MAAM,MAAM,SAAS;AAC/D,eAAW,aAAa,IAAI;AAC5B,WAAO;AAAA,EACT;AACF,EAAM,wBAAwBA,SAAQ,eAAe,OAAK;AACxD,eAAW,EAAE,SAAS,EAAE,MAAM;AAC9B,WAAO;AAAA,EACT,CAAC;AACH;AAUO,SAAS,oBAAoBA,SAAQ;AAC1C,MAAI,CAACA,QAAO,qBACR,qBAAqBA,QAAO,kBAAkB,WAAW;AAC3D;AAAA,EACF;AACA,QAAM,QAAQA,QAAO,kBAAkB;AACvC,SAAO,eAAe,OAAO,mBAAmB;AAAA,IAC9C,MAAM;AACJ,aAAO;AAAA,QACL,WAAW;AAAA,QACX,UAAU;AAAA,MACZ,EAAE,KAAK,kBAAkB,KAAK,KAAK;AAAA,IACrC;AAAA,IACA,YAAY;AAAA,IACZ,cAAc;AAAA,EAChB,CAAC;AACD,SAAO,eAAe,OAAO,2BAA2B;AAAA,IACtD,MAAM;AACJ,aAAO,KAAK,4BAA4B;AAAA,IAC1C;AAAA,IACA,IAAI,IAAI;AACN,UAAI,KAAK,0BAA0B;AACjC,aAAK;AAAA,UAAoB;AAAA,UACvB,KAAK;AAAA,QAAwB;AAC/B,eAAO,KAAK;AAAA,MACd;AACA,UAAI,IAAI;AACN,aAAK;AAAA,UAAiB;AAAA,UACpB,KAAK,2BAA2B;AAAA,QAAE;AAAA,MACtC;AAAA,IACF;AAAA,IACA,YAAY;AAAA,IACZ,cAAc;AAAA,EAChB,CAAC;AAED,GAAC,uBAAuB,sBAAsB,EAAE,QAAQ,CAAC,WAAW;AAClE,UAAM,aAAa,MAAM,MAAM;AAC/B,UAAM,MAAM,IAAI,WAAW;AACzB,UAAI,CAAC,KAAK,4BAA4B;AACpC,aAAK,6BAA6B,OAAK;AACrC,gBAAM,KAAK,EAAE;AACb,cAAI,GAAG,yBAAyB,GAAG,iBAAiB;AAClD,eAAG,uBAAuB,GAAG;AAC7B,kBAAM,WAAW,IAAI,MAAM,yBAAyB,CAAC;AACrD,eAAG,cAAc,QAAQ;AAAA,UAC3B;AACA,iBAAO;AAAA,QACT;AACA,aAAK;AAAA,UAAiB;AAAA,UACpB,KAAK;AAAA,QAA0B;AAAA,MACnC;AACA,aAAO,WAAW,MAAM,MAAM,SAAS;AAAA,IACzC;AAAA,EACF,CAAC;AACH;AAEO,SAAS,uBAAuBA,SAAQ,gBAAgB;AAE7D,MAAI,CAACA,QAAO,mBAAmB;AAC7B;AAAA,EACF;AACA,MAAI,eAAe,YAAY,YAAY,eAAe,WAAW,IAAI;AACvE;AAAA,EACF;AACA,MAAI,eAAe,YAAY,YAC3B,eAAe,kBAAkB,MAAM;AACzC;AAAA,EACF;AACA,QAAM,YAAYA,QAAO,kBAAkB,UAAU;AACrD,EAAAA,QAAO,kBAAkB,UAAU,uBACnC,SAAS,qBAAqB,MAAM;AAClC,QAAI,QAAQ,KAAK,OAAO,KAAK,IAAI,QAAQ,wBAAwB,MAAM,IAAI;AACzE,YAAME,OAAM,KAAK,IAAI,MAAM,IAAI,EAAE,OAAO,CAAC,SAAS;AAChD,eAAO,KAAK,KAAK,MAAM;AAAA,MACzB,CAAC,EAAE,KAAK,IAAI;AAEZ,UAAIF,QAAO,yBACP,gBAAgBA,QAAO,uBAAuB;AAChD,kBAAU,CAAC,IAAI,IAAIA,QAAO,sBAAsB;AAAA,UAC9C,MAAM,KAAK;AAAA,UACX,KAAAE;AAAA,QACF,CAAC;AAAA,MACH,OAAO;AACL,aAAK,MAAMA;AAAA,MACb;AAAA,IACF;AACA,WAAO,UAAU,MAAM,MAAM,SAAS;AAAA,EACxC;AACF;AAEO,SAAS,+BAA+BF,SAAQ,gBAAgB;AAKrE,MAAI,EAAEA,QAAO,qBAAqBA,QAAO,kBAAkB,YAAY;AACrE;AAAA,EACF;AACA,QAAM,wBACFA,QAAO,kBAAkB,UAAU;AACvC,MAAI,CAAC,yBAAyB,sBAAsB,WAAW,GAAG;AAChE;AAAA,EACF;AACA,EAAAA,QAAO,kBAAkB,UAAU,kBACjC,SAAS,kBAAkB;AACzB,QAAI,CAAC,UAAU,CAAC,GAAG;AACjB,UAAI,UAAU,CAAC,GAAG;AAChB,kBAAU,CAAC,EAAE,MAAM,IAAI;AAAA,MACzB;AACA,aAAO,QAAQ,QAAQ;AAAA,IACzB;AAMA,SAAM,eAAe,YAAY,YAAY,eAAe,UAAU,MAC7D,eAAe,YAAY,aACxB,eAAe,UAAU,MAC5B,eAAe,YAAY,aAC7B,UAAU,CAAC,KAAK,UAAU,CAAC,EAAE,cAAc,IAAI;AACpD,aAAO,QAAQ,QAAQ;AAAA,IACzB;AACA,WAAO,sBAAsB,MAAM,MAAM,SAAS;AAAA,EACpD;AACJ;AAIO,SAAS,qCAAqCA,SAAQ,gBAAgB;AAC3E,MAAI,EAAEA,QAAO,qBAAqBA,QAAO,kBAAkB,YAAY;AACrE;AAAA,EACF;AACA,QAAM,4BACFA,QAAO,kBAAkB,UAAU;AACvC,MAAI,CAAC,6BAA6B,0BAA0B,WAAW,GAAG;AACxE;AAAA,EACF;AACA,EAAAA,QAAO,kBAAkB,UAAU,sBACjC,SAAS,sBAAsB;AAC7B,QAAI,OAAO,UAAU,CAAC,KAAK,CAAC;AAC5B,QAAI,OAAO,SAAS,YAAa,KAAK,QAAQ,KAAK,KAAM;AACvD,aAAO,0BAA0B,MAAM,MAAM,SAAS;AAAA,IACxD;AAQA,WAAO,EAAC,MAAM,KAAK,MAAM,KAAK,KAAK,IAAG;AACtC,QAAI,CAAC,KAAK,MAAM;AACd,cAAQ,KAAK,gBAAgB;AAAA,QAC3B,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AACH,eAAK,OAAO;AACZ;AAAA,QACF;AACE,eAAK,OAAO;AACZ;AAAA,MACJ;AAAA,IACF;AACA,QAAI,KAAK,OAAQ,KAAK,SAAS,WAAW,KAAK,SAAS,UAAW;AACjE,aAAO,0BAA0B,MAAM,MAAM,CAAC,IAAI,CAAC;AAAA,IACrD;AACA,UAAM,OAAO,KAAK,SAAS,UAAU,KAAK,cAAc,KAAK;AAC7D,WAAO,KAAK,MAAM,IAAI,EACnB,KAAK,OAAK,0BAA0B,MAAM,MAAM,CAAC,CAAC,CAAC,CAAC;AAAA,EACzD;AACJ;;;AChcA,UAAqB;AAGd,SAAS,eAAe,EAAC,QAAAG,QAAM,IAAI,CAAC,GAAG,UAAU;AAAA,EACtD,YAAY;AAAA,EACZ,aAAa;AAAA,EACb,YAAY;AACd,GAAG;AAED,QAAMC,WAAgB;AACtB,QAAM,iBAAuB,cAAcD,OAAM;AAEjD,QAAME,WAAU;AAAA,IACd;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA;AAAA,IAEA;AAAA,EACF;AAGA,UAAQ,eAAe,SAAS;AAAA,IAC9B,KAAK;AACH,UAAI,CAAC,uBAAc,CAAY,sBAC3B,CAAC,QAAQ,YAAY;AACvB,QAAAD,SAAQ,sDAAsD;AAC9D,eAAOC;AAAA,MACT;AACA,UAAI,eAAe,YAAY,MAAM;AACnC,QAAAD,SAAQ,sDAAsD;AAC9D,eAAOC;AAAA,MACT;AACA,MAAAD,SAAQ,6BAA6B;AAErC,MAAAC,SAAQ,cAAc;AAGtB,MAAW,+BAA+BF,SAAQ,cAAc;AAChE,MAAW,qCAAqCA,SAAQ,cAAc;AAEtE,MAAW,iBAAiBA,SAAQ,cAAc;AAClD,MAAW,gBAAgBA,SAAQ,cAAc;AACjD,MAAW,mBAAmBA,SAAQ,cAAc;AACpD,MAAW,YAAYA,SAAQ,cAAc;AAC7C,MAAW,wBAAwBA,SAAQ,cAAc;AACzD,MAAW,uBAAuBA,SAAQ,cAAc;AACxD,MAAW,aAAaA,SAAQ,cAAc;AAC9C,MAAW,2BAA2BA,SAAQ,cAAc;AAC5D,MAAW,qBAAqBA,SAAQ,cAAc;AAEtD,MAAW,oBAAoBA,SAAQ,cAAc;AACrD,MAAW,iCAAiCA,SAAQ,cAAc;AAClE,MAAW,oBAAoBA,SAAQ,cAAc;AACrD,MAAW,mBAAmBA,SAAQ,cAAc;AACpD,MAAW,uBAAuBA,SAAQ,cAAc;AACxD,MAAW,uBAAuBA,SAAQ,cAAc;AACxD;AAAA,IACF,KAAK;AACH,UAAI,CAAC,wBAAe,CAAaG,uBAC7B,CAAC,QAAQ,aAAa;AACxB,QAAAF,SAAQ,uDAAuD;AAC/D,eAAOC;AAAA,MACT;AACA,MAAAD,SAAQ,8BAA8B;AAEtC,MAAAC,SAAQ,cAAc;AAGtB,MAAW,+BAA+BF,SAAQ,cAAc;AAChE,MAAW,qCAAqCA,SAAQ,cAAc;AAEtE,MAAYI,kBAAiBJ,SAAQ,cAAc;AACnD,MAAYG,oBAAmBH,SAAQ,cAAc;AACrD,MAAYK,aAAYL,SAAQ,cAAc;AAC9C,MAAY,iBAAiBA,SAAQ,cAAc;AACnD,MAAY,mBAAmBA,SAAQ,cAAc;AACrD,MAAY,qBAAqBA,SAAQ,cAAc;AACvD,MAAY,mBAAmBA,SAAQ,cAAc;AACrD,MAAY,mBAAmBA,SAAQ,cAAc;AACrD,MAAY,kBAAkBA,SAAQ,cAAc;AACpD,MAAY,gBAAgBA,SAAQ,cAAc;AAClD,MAAY,iBAAiBA,SAAQ,cAAc;AAEnD,MAAW,oBAAoBA,SAAQ,cAAc;AACrD,MAAW,oBAAoBA,SAAQ,cAAc;AACrD,MAAW,mBAAmBA,SAAQ,cAAc;AACpD,MAAW,uBAAuBA,SAAQ,cAAc;AACxD;AAAA,IACF,KAAK;AACH,UAAI,CAAC,uBAAc,CAAC,QAAQ,YAAY;AACtC,QAAAC,SAAQ,sDAAsD;AAC9D,eAAOC;AAAA,MACT;AACA,MAAAD,SAAQ,6BAA6B;AAErC,MAAAC,SAAQ,cAAc;AAGtB,MAAW,+BAA+BF,SAAQ,cAAc;AAChE,MAAW,qCAAqCA,SAAQ,cAAc;AAEtE,MAAW,qBAAqBA,SAAQ,cAAc;AACtD,MAAW,sBAAsBA,SAAQ,cAAc;AACvD,MAAW,iBAAiBA,SAAQ,cAAc;AAClD,MAAW,oBAAoBA,SAAQ,cAAc;AACrD,MAAW,qBAAqBA,SAAQ,cAAc;AACtD,MAAW,0BAA0BA,SAAQ,cAAc;AAC3D,MAAWI,kBAAiBJ,SAAQ,cAAc;AAClD,MAAW,iBAAiBA,SAAQ,cAAc;AAElD,MAAW,oBAAoBA,SAAQ,cAAc;AACrD,MAAW,iCAAiCA,SAAQ,cAAc;AAClE,MAAW,mBAAmBA,SAAQ,cAAc;AACpD,MAAW,uBAAuBA,SAAQ,cAAc;AACxD,MAAW,uBAAuBA,SAAQ,cAAc;AACxD;AAAA,IACF;AACE,MAAAC,SAAQ,sBAAsB;AAC9B;AAAA,EACJ;AAEA,SAAOC;AACT;;;AC7HA,IAAM,UACJ,eAAe,EAAC,QAAQ,OAAO,WAAW,cAAc,SAAY,OAAM,CAAC;AAC7E,IAAO,uBAAQ;", "names": ["SDPUtils", "sdp", "window", "window", "window", "window", "sdp", "shimGetDisplayMedia", "shimGetUserMedia", "shimOnTrack", "shimPeerConnection", "shimGetUserMedia", "window", "shimGetDisplayMedia", "window", "shimOnTrack", "window", "shimPeerConnection", "shimGetUserMedia", "window", "shimGetUserMedia", "window", "SDPUtils", "sdp", "window", "logging", "adapter", "shimPeerConnection", "shimGetUserMedia", "shimOnTrack"]}