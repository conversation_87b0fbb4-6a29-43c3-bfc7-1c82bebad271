{"version": 3, "sources": ["../../nanoid/url-alphabet/index.js", "../../nanoid/index.browser.js"], "sourcesContent": ["let urlAlphabet =\r\n  'useandom-26T198340PX75pxJACKVERYMINDBUSHWOLF_GQZbfghjklqvwyzrict'\r\nexport { urlAlphabet }\r\n", "import { urlAlphabet } from './url-alphabet/index.js'\r\nlet random = bytes => crypto.getRandomValues(new Uint8Array(bytes))\r\nlet customRandom = (alphabet, defaultSize, getRandom) => {\r\n  let mask = (2 << (Math.log(alphabet.length - 1) / Math.LN2)) - 1\r\n  let step = -~((1.6 * mask * defaultSize) / alphabet.length)\r\n  return (size = defaultSize) => {\r\n    let id = ''\r\n    while (true) {\r\n      let bytes = getRandom(step)\r\n      let j = step | 0\r\n      while (j--) {\r\n        id += alphabet[bytes[j] & mask] || ''\r\n        if (id.length === size) return id\r\n      }\r\n    }\r\n  }\r\n}\r\nlet customAlphabet = (alphabet, size = 21) =>\r\n  customRandom(alphabet, size, random)\r\nlet nanoid = (size = 21) =>\r\n  crypto.getRandomValues(new Uint8Array(size)).reduce((id, byte) => {\r\n    byte &= 63\r\n    if (byte < 36) {\r\n      id += byte.toString(36)\r\n    } else if (byte < 62) {\r\n      id += (byte - 26).toString(36).toUpperCase()\r\n    } else if (byte > 62) {\r\n      id += '-'\r\n    } else {\r\n      id += '_'\r\n    }\r\n    return id\r\n  }, '')\r\nexport { nanoid, customAlphabet, customRandom, urlAlphabet, random }\r\n"], "mappings": ";;;AAAA,IAAI,cACF;;;ACAF,IAAI,SAAS,WAAS,OAAO,gBAAgB,IAAI,WAAW,KAAK,CAAC;AAClE,IAAI,eAAe,CAAC,UAAU,aAAa,cAAc;AACvD,MAAI,QAAQ,KAAM,KAAK,IAAI,SAAS,SAAS,CAAC,IAAI,KAAK,OAAQ;AAC/D,MAAI,OAAO,CAAC,EAAG,MAAM,OAAO,cAAe,SAAS;AACpD,SAAO,CAAC,OAAO,gBAAgB;AAC7B,QAAI,KAAK;AACT,WAAO,MAAM;AACX,UAAI,QAAQ,UAAU,IAAI;AAC1B,UAAI,IAAI,OAAO;AACf,aAAO,KAAK;AACV,cAAM,SAAS,MAAM,CAAC,IAAI,IAAI,KAAK;AACnC,YAAI,GAAG,WAAW,KAAM,QAAO;AAAA,MACjC;AAAA,IACF;AAAA,EACF;AACF;AACA,IAAI,iBAAiB,CAAC,UAAU,OAAO,OACrC,aAAa,UAAU,MAAM,MAAM;AACrC,IAAI,SAAS,CAAC,OAAO,OACnB,OAAO,gBAAgB,IAAI,WAAW,IAAI,CAAC,EAAE,OAAO,CAAC,IAAI,SAAS;AAChE,UAAQ;AACR,MAAI,OAAO,IAAI;AACb,UAAM,KAAK,SAAS,EAAE;AAAA,EACxB,WAAW,OAAO,IAAI;AACpB,WAAO,OAAO,IAAI,SAAS,EAAE,EAAE,YAAY;AAAA,EAC7C,WAAW,OAAO,IAAI;AACpB,UAAM;AAAA,EACR,OAAO;AACL,UAAM;AAAA,EACR;AACA,SAAO;AACT,GAAG,EAAE;", "names": []}