import{q as Ki,s as Xi,x as ce,y as Q,z as er,A as nt,B as Rt,C as Et,D as Zi,r as H,c as Z,E as jt,a as j,f as N,m as E,b as d,n as J,p as W,t as w,o as ar,G as Ar,H as Pn,i as Tt,v as tr,F as ye,k as Re,h as $,_ as En,I as Qi,e as He,w as jn,J as es,T as ts,d as Ae,K as rs,u as ns,L as is,l as ss,M as os,N as as,O as ls,g as tt,P as cs,Q as mn,R as Er}from"./index-oz5Qducj.js";import{u as us}from"./meetingConfigStore-DhouCaW2.js";import{l as ds,b as fs,d as ps,s as Qt}from"./meetings-Bk-V09ki.js";import"./extensionsStore-C2g9n1A4.js";function hs(i,o){for(var n=0;n<o.length;n++){const r=o[n];if(typeof r!="string"&&!Array.isArray(r)){for(const a in r)if(a!=="default"&&!(a in i)){const u=Object.getOwnPropertyDescriptor(r,a);u&&Object.defineProperty(i,a,u.get?u:{enumerable:!0,get:()=>r[a]})}}}return Object.freeze(Object.defineProperty(i,Symbol.toStringTag,{value:"Module"}))}const gs=Ki("meeting",{state:()=>({meetingId:null,localStream:null,remoteStreams:{},isMicMuted:!1,isCameraOff:!1,isScreenSharing:!1,participants:[],messages:[],connectionStatus:"disconnected"}),actions:{setMeetingId(i){this.meetingId=i},setLocalStream(i){this.localStream=i},addRemoteStream(i,o){this.remoteStreams[i]=o},removeRemoteStream(i){delete this.remoteStreams[i]},toggleMic(){this.isMicMuted=!this.isMicMuted,this.localStream&&this.localStream.getAudioTracks().forEach(i=>{i.enabled=!this.isMicMuted})},toggleCamera(){this.isCameraOff=!this.isCameraOff,this.localStream&&this.localStream.getVideoTracks().forEach(i=>{i.enabled=!this.isCameraOff})},toggleScreenSharing(){this.isScreenSharing=!this.isScreenSharing},addParticipant(i){this.participants.push(i)},removeParticipant(i){this.participants=this.participants.filter(o=>o.id!==i)},addMessage(i){this.messages.push(i)},setConnectionStatus(i){this.connectionStatus=i},reset(){this.meetingId=null,this.localStream=null,this.remoteStreams={},this.isMicMuted=!1,this.isCameraOff=!1,this.isScreenSharing=!1,this.participants=[],this.messages=[],this.connectionStatus="disconnected"}}});let Mn=!0,An=!0;function Pt(i,o,n){const r=i.match(o);return r&&r.length>=n&&parseFloat(r[n],10)}function qe(i,o,n){if(!i.RTCPeerConnection)return;const r=i.RTCPeerConnection.prototype,a=r.addEventListener;r.addEventListener=function(v,h){if(v!==o)return a.apply(this,arguments);const k=b=>{const g=n(b);g&&(h.handleEvent?h.handleEvent(g):h(g))};return this._eventMap=this._eventMap||{},this._eventMap[o]||(this._eventMap[o]=new Map),this._eventMap[o].set(h,k),a.apply(this,[v,k])};const u=r.removeEventListener;r.removeEventListener=function(v,h){if(v!==o||!this._eventMap||!this._eventMap[o])return u.apply(this,arguments);if(!this._eventMap[o].has(h))return u.apply(this,arguments);const k=this._eventMap[o].get(h);return this._eventMap[o].delete(h),this._eventMap[o].size===0&&delete this._eventMap[o],Object.keys(this._eventMap).length===0&&delete this._eventMap,u.apply(this,[v,k])},Object.defineProperty(r,"on"+o,{get(){return this["_on"+o]},set(v){this["_on"+o]&&(this.removeEventListener(o,this["_on"+o]),delete this["_on"+o]),v&&this.addEventListener(o,this["_on"+o]=v)},enumerable:!0,configurable:!0})}function ms(i){return typeof i!="boolean"?new Error("Argument type: "+typeof i+". Please use a boolean."):(Mn=i,i?"adapter.js logging disabled":"adapter.js logging enabled")}function vs(i){return typeof i!="boolean"?new Error("Argument type: "+typeof i+". Please use a boolean."):(An=!i,"adapter.js deprecation warnings "+(i?"disabled":"enabled"))}function In(){if(typeof window=="object"){if(Mn)return;typeof console<"u"&&typeof console.log=="function"&&console.log.apply(console,arguments)}}function wr(i,o){An&&console.warn(i+" is deprecated, please use "+o+" instead.")}function ys(i){const o={browser:null,version:null};if(typeof i>"u"||!i.navigator||!i.navigator.userAgent)return o.browser="Not a browser.",o;const{navigator:n}=i;if(n.mozGetUserMedia)o.browser="firefox",o.version=parseInt(Pt(n.userAgent,/Firefox\/(\d+)\./,1));else if(n.webkitGetUserMedia||i.isSecureContext===!1&&i.webkitRTCPeerConnection)o.browser="chrome",o.version=parseInt(Pt(n.userAgent,/Chrom(e|ium)\/(\d+)\./,2));else if(i.RTCPeerConnection&&n.userAgent.match(/AppleWebKit\/(\d+)\./))o.browser="safari",o.version=parseInt(Pt(n.userAgent,/AppleWebKit\/(\d+)\./,1)),o.supportsUnifiedPlan=i.RTCRtpTransceiver&&"currentDirection"in i.RTCRtpTransceiver.prototype,o._safariVersion=Pt(n.userAgent,/Version\/(\d+(\.?\d+))/,1);else return o.browser="Not a supported browser.",o;return o}function vn(i){return Object.prototype.toString.call(i)==="[object Object]"}function _n(i){return vn(i)?Object.keys(i).reduce(function(o,n){const r=vn(i[n]),a=r?_n(i[n]):i[n],u=r&&!Object.keys(a).length;return a===void 0||u?o:Object.assign(o,{[n]:a})},{}):i}function Ir(i,o,n){!o||n.has(o.id)||(n.set(o.id,o),Object.keys(o).forEach(r=>{r.endsWith("Id")?Ir(i,i.get(o[r]),n):r.endsWith("Ids")&&o[r].forEach(a=>{Ir(i,i.get(a),n)})}))}function yn(i,o,n){const r=n?"outbound-rtp":"inbound-rtp",a=new Map;if(o===null)return a;const u=[];return i.forEach(v=>{v.type==="track"&&v.trackIdentifier===o.id&&u.push(v)}),u.forEach(v=>{i.forEach(h=>{h.type===r&&h.trackId===v.id&&Ir(i,h,a)})}),a}const bn=In;function On(i,o){const n=i&&i.navigator;if(!n.mediaDevices)return;const r=function(h){if(typeof h!="object"||h.mandatory||h.optional)return h;const k={};return Object.keys(h).forEach(b=>{if(b==="require"||b==="advanced"||b==="mediaSource")return;const g=typeof h[b]=="object"?h[b]:{ideal:h[b]};g.exact!==void 0&&typeof g.exact=="number"&&(g.min=g.max=g.exact);const y=function(C,A){return C?C+A.charAt(0).toUpperCase()+A.slice(1):A==="deviceId"?"sourceId":A};if(g.ideal!==void 0){k.optional=k.optional||[];let C={};typeof g.ideal=="number"?(C[y("min",b)]=g.ideal,k.optional.push(C),C={},C[y("max",b)]=g.ideal,k.optional.push(C)):(C[y("",b)]=g.ideal,k.optional.push(C))}g.exact!==void 0&&typeof g.exact!="number"?(k.mandatory=k.mandatory||{},k.mandatory[y("",b)]=g.exact):["min","max"].forEach(C=>{g[C]!==void 0&&(k.mandatory=k.mandatory||{},k.mandatory[y(C,b)]=g[C])})}),h.advanced&&(k.optional=(k.optional||[]).concat(h.advanced)),k},a=function(h,k){if(o.version>=61)return k(h);if(h=JSON.parse(JSON.stringify(h)),h&&typeof h.audio=="object"){const b=function(g,y,C){y in g&&!(C in g)&&(g[C]=g[y],delete g[y])};h=JSON.parse(JSON.stringify(h)),b(h.audio,"autoGainControl","googAutoGainControl"),b(h.audio,"noiseSuppression","googNoiseSuppression"),h.audio=r(h.audio)}if(h&&typeof h.video=="object"){let b=h.video.facingMode;b=b&&(typeof b=="object"?b:{ideal:b});const g=o.version<66;if(b&&(b.exact==="user"||b.exact==="environment"||b.ideal==="user"||b.ideal==="environment")&&!(n.mediaDevices.getSupportedConstraints&&n.mediaDevices.getSupportedConstraints().facingMode&&!g)){delete h.video.facingMode;let y;if(b.exact==="environment"||b.ideal==="environment"?y=["back","rear"]:(b.exact==="user"||b.ideal==="user")&&(y=["front"]),y)return n.mediaDevices.enumerateDevices().then(C=>{C=C.filter(F=>F.kind==="videoinput");let A=C.find(F=>y.some(Y=>F.label.toLowerCase().includes(Y)));return!A&&C.length&&y.includes("back")&&(A=C[C.length-1]),A&&(h.video.deviceId=b.exact?{exact:A.deviceId}:{ideal:A.deviceId}),h.video=r(h.video),bn("chrome: "+JSON.stringify(h)),k(h)})}h.video=r(h.video)}return bn("chrome: "+JSON.stringify(h)),k(h)},u=function(h){return o.version>=64?h:{name:{PermissionDeniedError:"NotAllowedError",PermissionDismissedError:"NotAllowedError",InvalidStateError:"NotAllowedError",DevicesNotFoundError:"NotFoundError",ConstraintNotSatisfiedError:"OverconstrainedError",TrackStartError:"NotReadableError",MediaDeviceFailedDueToShutdown:"NotAllowedError",MediaDeviceKillSwitchOn:"NotAllowedError",TabCaptureError:"AbortError",ScreenCaptureError:"AbortError",DeviceCaptureError:"AbortError"}[h.name]||h.name,message:h.message,constraint:h.constraint||h.constraintName,toString(){return this.name+(this.message&&": ")+this.message}}},v=function(h,k,b){a(h,g=>{n.webkitGetUserMedia(g,k,y=>{b&&b(u(y))})})};if(n.getUserMedia=v.bind(n),n.mediaDevices.getUserMedia){const h=n.mediaDevices.getUserMedia.bind(n.mediaDevices);n.mediaDevices.getUserMedia=function(k){return a(k,b=>h(b).then(g=>{if(b.audio&&!g.getAudioTracks().length||b.video&&!g.getVideoTracks().length)throw g.getTracks().forEach(y=>{y.stop()}),new DOMException("","NotFoundError");return g},g=>Promise.reject(u(g))))}}}function bs(i,o){if(!(i.navigator.mediaDevices&&"getDisplayMedia"in i.navigator.mediaDevices)&&i.navigator.mediaDevices){if(typeof o!="function"){console.error("shimGetDisplayMedia: getSourceId argument is not a function");return}i.navigator.mediaDevices.getDisplayMedia=function(r){return o(r).then(a=>{const u=r.video&&r.video.width,v=r.video&&r.video.height,h=r.video&&r.video.frameRate;return r.video={mandatory:{chromeMediaSource:"desktop",chromeMediaSourceId:a,maxFrameRate:h||3}},u&&(r.video.mandatory.maxWidth=u),v&&(r.video.mandatory.maxHeight=v),i.navigator.mediaDevices.getUserMedia(r)})}}}function Ln(i){i.MediaStream=i.MediaStream||i.webkitMediaStream}function Dn(i){if(typeof i=="object"&&i.RTCPeerConnection&&!("ontrack"in i.RTCPeerConnection.prototype)){Object.defineProperty(i.RTCPeerConnection.prototype,"ontrack",{get(){return this._ontrack},set(n){this._ontrack&&this.removeEventListener("track",this._ontrack),this.addEventListener("track",this._ontrack=n)},enumerable:!0,configurable:!0});const o=i.RTCPeerConnection.prototype.setRemoteDescription;i.RTCPeerConnection.prototype.setRemoteDescription=function(){return this._ontrackpoly||(this._ontrackpoly=r=>{r.stream.addEventListener("addtrack",a=>{let u;i.RTCPeerConnection.prototype.getReceivers?u=this.getReceivers().find(h=>h.track&&h.track.id===a.track.id):u={track:a.track};const v=new Event("track");v.track=a.track,v.receiver=u,v.transceiver={receiver:u},v.streams=[r.stream],this.dispatchEvent(v)}),r.stream.getTracks().forEach(a=>{let u;i.RTCPeerConnection.prototype.getReceivers?u=this.getReceivers().find(h=>h.track&&h.track.id===a.id):u={track:a};const v=new Event("track");v.track=a,v.receiver=u,v.transceiver={receiver:u},v.streams=[r.stream],this.dispatchEvent(v)})},this.addEventListener("addstream",this._ontrackpoly)),o.apply(this,arguments)}}else qe(i,"track",o=>(o.transceiver||Object.defineProperty(o,"transceiver",{value:{receiver:o.receiver}}),o))}function $n(i){if(typeof i=="object"&&i.RTCPeerConnection&&!("getSenders"in i.RTCPeerConnection.prototype)&&"createDTMFSender"in i.RTCPeerConnection.prototype){const o=function(a,u){return{track:u,get dtmf(){return this._dtmf===void 0&&(u.kind==="audio"?this._dtmf=a.createDTMFSender(u):this._dtmf=null),this._dtmf},_pc:a}};if(!i.RTCPeerConnection.prototype.getSenders){i.RTCPeerConnection.prototype.getSenders=function(){return this._senders=this._senders||[],this._senders.slice()};const a=i.RTCPeerConnection.prototype.addTrack;i.RTCPeerConnection.prototype.addTrack=function(h,k){let b=a.apply(this,arguments);return b||(b=o(this,h),this._senders.push(b)),b};const u=i.RTCPeerConnection.prototype.removeTrack;i.RTCPeerConnection.prototype.removeTrack=function(h){u.apply(this,arguments);const k=this._senders.indexOf(h);k!==-1&&this._senders.splice(k,1)}}const n=i.RTCPeerConnection.prototype.addStream;i.RTCPeerConnection.prototype.addStream=function(u){this._senders=this._senders||[],n.apply(this,[u]),u.getTracks().forEach(v=>{this._senders.push(o(this,v))})};const r=i.RTCPeerConnection.prototype.removeStream;i.RTCPeerConnection.prototype.removeStream=function(u){this._senders=this._senders||[],r.apply(this,[u]),u.getTracks().forEach(v=>{const h=this._senders.find(k=>k.track===v);h&&this._senders.splice(this._senders.indexOf(h),1)})}}else if(typeof i=="object"&&i.RTCPeerConnection&&"getSenders"in i.RTCPeerConnection.prototype&&"createDTMFSender"in i.RTCPeerConnection.prototype&&i.RTCRtpSender&&!("dtmf"in i.RTCRtpSender.prototype)){const o=i.RTCPeerConnection.prototype.getSenders;i.RTCPeerConnection.prototype.getSenders=function(){const r=o.apply(this,[]);return r.forEach(a=>a._pc=this),r},Object.defineProperty(i.RTCRtpSender.prototype,"dtmf",{get(){return this._dtmf===void 0&&(this.track.kind==="audio"?this._dtmf=this._pc.createDTMFSender(this.track):this._dtmf=null),this._dtmf}})}}function wn(i){if(!i.RTCPeerConnection)return;const o=i.RTCPeerConnection.prototype.getStats;i.RTCPeerConnection.prototype.getStats=function(){const[r,a,u]=arguments;if(arguments.length>0&&typeof r=="function")return o.apply(this,arguments);if(o.length===0&&(arguments.length===0||typeof r!="function"))return o.apply(this,[]);const v=function(k){const b={};return k.result().forEach(y=>{const C={id:y.id,timestamp:y.timestamp,type:{localcandidate:"local-candidate",remotecandidate:"remote-candidate"}[y.type]||y.type};y.names().forEach(A=>{C[A]=y.stat(A)}),b[C.id]=C}),b},h=function(k){return new Map(Object.keys(k).map(b=>[b,k[b]]))};if(arguments.length>=2){const k=function(b){a(h(v(b)))};return o.apply(this,[k,r])}return new Promise((k,b)=>{o.apply(this,[function(g){k(h(v(g)))},b])}).then(a,u)}}function Fn(i){if(!(typeof i=="object"&&i.RTCPeerConnection&&i.RTCRtpSender&&i.RTCRtpReceiver))return;if(!("getStats"in i.RTCRtpSender.prototype)){const n=i.RTCPeerConnection.prototype.getSenders;n&&(i.RTCPeerConnection.prototype.getSenders=function(){const u=n.apply(this,[]);return u.forEach(v=>v._pc=this),u});const r=i.RTCPeerConnection.prototype.addTrack;r&&(i.RTCPeerConnection.prototype.addTrack=function(){const u=r.apply(this,arguments);return u._pc=this,u}),i.RTCRtpSender.prototype.getStats=function(){const u=this;return this._pc.getStats().then(v=>yn(v,u.track,!0))}}if(!("getStats"in i.RTCRtpReceiver.prototype)){const n=i.RTCPeerConnection.prototype.getReceivers;n&&(i.RTCPeerConnection.prototype.getReceivers=function(){const a=n.apply(this,[]);return a.forEach(u=>u._pc=this),a}),qe(i,"track",r=>(r.receiver._pc=r.srcElement,r)),i.RTCRtpReceiver.prototype.getStats=function(){const a=this;return this._pc.getStats().then(u=>yn(u,a.track,!1))}}if(!("getStats"in i.RTCRtpSender.prototype&&"getStats"in i.RTCRtpReceiver.prototype))return;const o=i.RTCPeerConnection.prototype.getStats;i.RTCPeerConnection.prototype.getStats=function(){if(arguments.length>0&&arguments[0]instanceof i.MediaStreamTrack){const r=arguments[0];let a,u,v;return this.getSenders().forEach(h=>{h.track===r&&(a?v=!0:a=h)}),this.getReceivers().forEach(h=>(h.track===r&&(u?v=!0:u=h),h.track===r)),v||a&&u?Promise.reject(new DOMException("There are more than one sender or receiver for the track.","InvalidAccessError")):a?a.getStats():u?u.getStats():Promise.reject(new DOMException("There is no sender or receiver for the track.","InvalidAccessError"))}return o.apply(this,arguments)}}function Un(i){i.RTCPeerConnection.prototype.getLocalStreams=function(){return this._shimmedLocalStreams=this._shimmedLocalStreams||{},Object.keys(this._shimmedLocalStreams).map(v=>this._shimmedLocalStreams[v][0])};const o=i.RTCPeerConnection.prototype.addTrack;i.RTCPeerConnection.prototype.addTrack=function(v,h){if(!h)return o.apply(this,arguments);this._shimmedLocalStreams=this._shimmedLocalStreams||{};const k=o.apply(this,arguments);return this._shimmedLocalStreams[h.id]?this._shimmedLocalStreams[h.id].indexOf(k)===-1&&this._shimmedLocalStreams[h.id].push(k):this._shimmedLocalStreams[h.id]=[h,k],k};const n=i.RTCPeerConnection.prototype.addStream;i.RTCPeerConnection.prototype.addStream=function(v){this._shimmedLocalStreams=this._shimmedLocalStreams||{},v.getTracks().forEach(b=>{if(this.getSenders().find(y=>y.track===b))throw new DOMException("Track already exists.","InvalidAccessError")});const h=this.getSenders();n.apply(this,arguments);const k=this.getSenders().filter(b=>h.indexOf(b)===-1);this._shimmedLocalStreams[v.id]=[v].concat(k)};const r=i.RTCPeerConnection.prototype.removeStream;i.RTCPeerConnection.prototype.removeStream=function(v){return this._shimmedLocalStreams=this._shimmedLocalStreams||{},delete this._shimmedLocalStreams[v.id],r.apply(this,arguments)};const a=i.RTCPeerConnection.prototype.removeTrack;i.RTCPeerConnection.prototype.removeTrack=function(v){return this._shimmedLocalStreams=this._shimmedLocalStreams||{},v&&Object.keys(this._shimmedLocalStreams).forEach(h=>{const k=this._shimmedLocalStreams[h].indexOf(v);k!==-1&&this._shimmedLocalStreams[h].splice(k,1),this._shimmedLocalStreams[h].length===1&&delete this._shimmedLocalStreams[h]}),a.apply(this,arguments)}}function Nn(i,o){if(!i.RTCPeerConnection)return;if(i.RTCPeerConnection.prototype.addTrack&&o.version>=65)return Un(i);const n=i.RTCPeerConnection.prototype.getLocalStreams;i.RTCPeerConnection.prototype.getLocalStreams=function(){const g=n.apply(this);return this._reverseStreams=this._reverseStreams||{},g.map(y=>this._reverseStreams[y.id])};const r=i.RTCPeerConnection.prototype.addStream;i.RTCPeerConnection.prototype.addStream=function(g){if(this._streams=this._streams||{},this._reverseStreams=this._reverseStreams||{},g.getTracks().forEach(y=>{if(this.getSenders().find(A=>A.track===y))throw new DOMException("Track already exists.","InvalidAccessError")}),!this._reverseStreams[g.id]){const y=new i.MediaStream(g.getTracks());this._streams[g.id]=y,this._reverseStreams[y.id]=g,g=y}r.apply(this,[g])};const a=i.RTCPeerConnection.prototype.removeStream;i.RTCPeerConnection.prototype.removeStream=function(g){this._streams=this._streams||{},this._reverseStreams=this._reverseStreams||{},a.apply(this,[this._streams[g.id]||g]),delete this._reverseStreams[this._streams[g.id]?this._streams[g.id].id:g.id],delete this._streams[g.id]},i.RTCPeerConnection.prototype.addTrack=function(g,y){if(this.signalingState==="closed")throw new DOMException("The RTCPeerConnection's signalingState is 'closed'.","InvalidStateError");const C=[].slice.call(arguments,1);if(C.length!==1||!C[0].getTracks().find(Y=>Y===g))throw new DOMException("The adapter.js addTrack polyfill only supports a single  stream which is associated with the specified track.","NotSupportedError");if(this.getSenders().find(Y=>Y.track===g))throw new DOMException("Track already exists.","InvalidAccessError");this._streams=this._streams||{},this._reverseStreams=this._reverseStreams||{};const F=this._streams[y.id];if(F)F.addTrack(g),Promise.resolve().then(()=>{this.dispatchEvent(new Event("negotiationneeded"))});else{const Y=new i.MediaStream([g]);this._streams[y.id]=Y,this._reverseStreams[Y.id]=y,this.addStream(Y)}return this.getSenders().find(Y=>Y.track===g)};function u(b,g){let y=g.sdp;return Object.keys(b._reverseStreams||[]).forEach(C=>{const A=b._reverseStreams[C],F=b._streams[A.id];y=y.replace(new RegExp(F.id,"g"),A.id)}),new RTCSessionDescription({type:g.type,sdp:y})}function v(b,g){let y=g.sdp;return Object.keys(b._reverseStreams||[]).forEach(C=>{const A=b._reverseStreams[C],F=b._streams[A.id];y=y.replace(new RegExp(A.id,"g"),F.id)}),new RTCSessionDescription({type:g.type,sdp:y})}["createOffer","createAnswer"].forEach(function(b){const g=i.RTCPeerConnection.prototype[b],y={[b](){const C=arguments;return arguments.length&&typeof arguments[0]=="function"?g.apply(this,[F=>{const Y=u(this,F);C[0].apply(null,[Y])},F=>{C[1]&&C[1].apply(null,F)},arguments[2]]):g.apply(this,arguments).then(F=>u(this,F))}};i.RTCPeerConnection.prototype[b]=y[b]});const h=i.RTCPeerConnection.prototype.setLocalDescription;i.RTCPeerConnection.prototype.setLocalDescription=function(){return!arguments.length||!arguments[0].type?h.apply(this,arguments):(arguments[0]=v(this,arguments[0]),h.apply(this,arguments))};const k=Object.getOwnPropertyDescriptor(i.RTCPeerConnection.prototype,"localDescription");Object.defineProperty(i.RTCPeerConnection.prototype,"localDescription",{get(){const b=k.get.apply(this);return b.type===""?b:u(this,b)}}),i.RTCPeerConnection.prototype.removeTrack=function(g){if(this.signalingState==="closed")throw new DOMException("The RTCPeerConnection's signalingState is 'closed'.","InvalidStateError");if(!g._pc)throw new DOMException("Argument 1 of RTCPeerConnection.removeTrack does not implement interface RTCRtpSender.","TypeError");if(!(g._pc===this))throw new DOMException("Sender was not created by this connection.","InvalidAccessError");this._streams=this._streams||{};let C;Object.keys(this._streams).forEach(A=>{this._streams[A].getTracks().find(Y=>g.track===Y)&&(C=this._streams[A])}),C&&(C.getTracks().length===1?this.removeStream(this._reverseStreams[C.id]):C.removeTrack(g.track),this.dispatchEvent(new Event("negotiationneeded")))}}function _r(i,o){!i.RTCPeerConnection&&i.webkitRTCPeerConnection&&(i.RTCPeerConnection=i.webkitRTCPeerConnection),i.RTCPeerConnection&&o.version<53&&["setLocalDescription","setRemoteDescription","addIceCandidate"].forEach(function(n){const r=i.RTCPeerConnection.prototype[n],a={[n](){return arguments[0]=new(n==="addIceCandidate"?i.RTCIceCandidate:i.RTCSessionDescription)(arguments[0]),r.apply(this,arguments)}};i.RTCPeerConnection.prototype[n]=a[n]})}function Bn(i,o){qe(i,"negotiationneeded",n=>{const r=n.target;if(!((o.version<72||r.getConfiguration&&r.getConfiguration().sdpSemantics==="plan-b")&&r.signalingState!=="stable"))return n})}const xn=Object.freeze(Object.defineProperty({__proto__:null,fixNegotiationNeeded:Bn,shimAddTrackRemoveTrack:Nn,shimAddTrackRemoveTrackWithNative:Un,shimGetDisplayMedia:bs,shimGetSendersWithDtmf:$n,shimGetStats:wn,shimGetUserMedia:On,shimMediaStream:Ln,shimOnTrack:Dn,shimPeerConnection:_r,shimSenderReceiverGetStats:Fn},Symbol.toStringTag,{value:"Module"}));function Vn(i,o){const n=i&&i.navigator,r=i&&i.MediaStreamTrack;if(n.getUserMedia=function(a,u,v){wr("navigator.getUserMedia","navigator.mediaDevices.getUserMedia"),n.mediaDevices.getUserMedia(a).then(u,v)},!(o.version>55&&"autoGainControl"in n.mediaDevices.getSupportedConstraints())){const a=function(v,h,k){h in v&&!(k in v)&&(v[k]=v[h],delete v[h])},u=n.mediaDevices.getUserMedia.bind(n.mediaDevices);if(n.mediaDevices.getUserMedia=function(v){return typeof v=="object"&&typeof v.audio=="object"&&(v=JSON.parse(JSON.stringify(v)),a(v.audio,"autoGainControl","mozAutoGainControl"),a(v.audio,"noiseSuppression","mozNoiseSuppression")),u(v)},r&&r.prototype.getSettings){const v=r.prototype.getSettings;r.prototype.getSettings=function(){const h=v.apply(this,arguments);return a(h,"mozAutoGainControl","autoGainControl"),a(h,"mozNoiseSuppression","noiseSuppression"),h}}if(r&&r.prototype.applyConstraints){const v=r.prototype.applyConstraints;r.prototype.applyConstraints=function(h){return this.kind==="audio"&&typeof h=="object"&&(h=JSON.parse(JSON.stringify(h)),a(h,"autoGainControl","mozAutoGainControl"),a(h,"noiseSuppression","mozNoiseSuppression")),v.apply(this,[h])}}}}function xs(i,o){i.navigator.mediaDevices&&"getDisplayMedia"in i.navigator.mediaDevices||i.navigator.mediaDevices&&(i.navigator.mediaDevices.getDisplayMedia=function(r){if(!(r&&r.video)){const a=new DOMException("getDisplayMedia without video constraints is undefined");return a.name="NotFoundError",a.code=8,Promise.reject(a)}return r.video===!0?r.video={mediaSource:o}:r.video.mediaSource=o,i.navigator.mediaDevices.getUserMedia(r)})}function zn(i){typeof i=="object"&&i.RTCTrackEvent&&"receiver"in i.RTCTrackEvent.prototype&&!("transceiver"in i.RTCTrackEvent.prototype)&&Object.defineProperty(i.RTCTrackEvent.prototype,"transceiver",{get(){return{receiver:this.receiver}}})}function Or(i,o){if(typeof i!="object"||!(i.RTCPeerConnection||i.mozRTCPeerConnection))return;!i.RTCPeerConnection&&i.mozRTCPeerConnection&&(i.RTCPeerConnection=i.mozRTCPeerConnection),o.version<53&&["setLocalDescription","setRemoteDescription","addIceCandidate"].forEach(function(a){const u=i.RTCPeerConnection.prototype[a],v={[a](){return arguments[0]=new(a==="addIceCandidate"?i.RTCIceCandidate:i.RTCSessionDescription)(arguments[0]),u.apply(this,arguments)}};i.RTCPeerConnection.prototype[a]=v[a]});const n={inboundrtp:"inbound-rtp",outboundrtp:"outbound-rtp",candidatepair:"candidate-pair",localcandidate:"local-candidate",remotecandidate:"remote-candidate"},r=i.RTCPeerConnection.prototype.getStats;i.RTCPeerConnection.prototype.getStats=function(){const[u,v,h]=arguments;return r.apply(this,[u||null]).then(k=>{if(o.version<53&&!v)try{k.forEach(b=>{b.type=n[b.type]||b.type})}catch(b){if(b.name!=="TypeError")throw b;k.forEach((g,y)=>{k.set(y,Object.assign({},g,{type:n[g.type]||g.type}))})}return k}).then(v,h)}}function Gn(i){if(!(typeof i=="object"&&i.RTCPeerConnection&&i.RTCRtpSender)||i.RTCRtpSender&&"getStats"in i.RTCRtpSender.prototype)return;const o=i.RTCPeerConnection.prototype.getSenders;o&&(i.RTCPeerConnection.prototype.getSenders=function(){const a=o.apply(this,[]);return a.forEach(u=>u._pc=this),a});const n=i.RTCPeerConnection.prototype.addTrack;n&&(i.RTCPeerConnection.prototype.addTrack=function(){const a=n.apply(this,arguments);return a._pc=this,a}),i.RTCRtpSender.prototype.getStats=function(){return this.track?this._pc.getStats(this.track):Promise.resolve(new Map)}}function Jn(i){if(!(typeof i=="object"&&i.RTCPeerConnection&&i.RTCRtpSender)||i.RTCRtpSender&&"getStats"in i.RTCRtpReceiver.prototype)return;const o=i.RTCPeerConnection.prototype.getReceivers;o&&(i.RTCPeerConnection.prototype.getReceivers=function(){const r=o.apply(this,[]);return r.forEach(a=>a._pc=this),r}),qe(i,"track",n=>(n.receiver._pc=n.srcElement,n)),i.RTCRtpReceiver.prototype.getStats=function(){return this._pc.getStats(this.track)}}function Hn(i){!i.RTCPeerConnection||"removeStream"in i.RTCPeerConnection.prototype||(i.RTCPeerConnection.prototype.removeStream=function(n){wr("removeStream","removeTrack"),this.getSenders().forEach(r=>{r.track&&n.getTracks().includes(r.track)&&this.removeTrack(r)})})}function qn(i){i.DataChannel&&!i.RTCDataChannel&&(i.RTCDataChannel=i.DataChannel)}function Wn(i){if(!(typeof i=="object"&&i.RTCPeerConnection))return;const o=i.RTCPeerConnection.prototype.addTransceiver;o&&(i.RTCPeerConnection.prototype.addTransceiver=function(){this.setParametersPromises=[];let r=arguments[1]&&arguments[1].sendEncodings;r===void 0&&(r=[]),r=[...r];const a=r.length>0;a&&r.forEach(v=>{if("rid"in v&&!/^[a-z0-9]{0,16}$/i.test(v.rid))throw new TypeError("Invalid RID value provided.");if("scaleResolutionDownBy"in v&&!(parseFloat(v.scaleResolutionDownBy)>=1))throw new RangeError("scale_resolution_down_by must be >= 1.0");if("maxFramerate"in v&&!(parseFloat(v.maxFramerate)>=0))throw new RangeError("max_framerate must be >= 0.0")});const u=o.apply(this,arguments);if(a){const{sender:v}=u,h=v.getParameters();(!("encodings"in h)||h.encodings.length===1&&Object.keys(h.encodings[0]).length===0)&&(h.encodings=r,v.sendEncodings=r,this.setParametersPromises.push(v.setParameters(h).then(()=>{delete v.sendEncodings}).catch(()=>{delete v.sendEncodings})))}return u})}function Yn(i){if(!(typeof i=="object"&&i.RTCRtpSender))return;const o=i.RTCRtpSender.prototype.getParameters;o&&(i.RTCRtpSender.prototype.getParameters=function(){const r=o.apply(this,arguments);return"encodings"in r||(r.encodings=[].concat(this.sendEncodings||[{}])),r})}function Kn(i){if(!(typeof i=="object"&&i.RTCPeerConnection))return;const o=i.RTCPeerConnection.prototype.createOffer;i.RTCPeerConnection.prototype.createOffer=function(){return this.setParametersPromises&&this.setParametersPromises.length?Promise.all(this.setParametersPromises).then(()=>o.apply(this,arguments)).finally(()=>{this.setParametersPromises=[]}):o.apply(this,arguments)}}function Xn(i){if(!(typeof i=="object"&&i.RTCPeerConnection))return;const o=i.RTCPeerConnection.prototype.createAnswer;i.RTCPeerConnection.prototype.createAnswer=function(){return this.setParametersPromises&&this.setParametersPromises.length?Promise.all(this.setParametersPromises).then(()=>o.apply(this,arguments)).finally(()=>{this.setParametersPromises=[]}):o.apply(this,arguments)}}const kn=Object.freeze(Object.defineProperty({__proto__:null,shimAddTransceiver:Wn,shimCreateAnswer:Xn,shimCreateOffer:Kn,shimGetDisplayMedia:xs,shimGetParameters:Yn,shimGetUserMedia:Vn,shimOnTrack:zn,shimPeerConnection:Or,shimRTCDataChannel:qn,shimReceiverGetStats:Jn,shimRemoveStream:Hn,shimSenderGetStats:Gn},Symbol.toStringTag,{value:"Module"}));function Zn(i){if(!(typeof i!="object"||!i.RTCPeerConnection)){if("getLocalStreams"in i.RTCPeerConnection.prototype||(i.RTCPeerConnection.prototype.getLocalStreams=function(){return this._localStreams||(this._localStreams=[]),this._localStreams}),!("addStream"in i.RTCPeerConnection.prototype)){const o=i.RTCPeerConnection.prototype.addTrack;i.RTCPeerConnection.prototype.addStream=function(r){this._localStreams||(this._localStreams=[]),this._localStreams.includes(r)||this._localStreams.push(r),r.getAudioTracks().forEach(a=>o.call(this,a,r)),r.getVideoTracks().forEach(a=>o.call(this,a,r))},i.RTCPeerConnection.prototype.addTrack=function(r,...a){return a&&a.forEach(u=>{this._localStreams?this._localStreams.includes(u)||this._localStreams.push(u):this._localStreams=[u]}),o.apply(this,arguments)}}"removeStream"in i.RTCPeerConnection.prototype||(i.RTCPeerConnection.prototype.removeStream=function(n){this._localStreams||(this._localStreams=[]);const r=this._localStreams.indexOf(n);if(r===-1)return;this._localStreams.splice(r,1);const a=n.getTracks();this.getSenders().forEach(u=>{a.includes(u.track)&&this.removeTrack(u)})})}}function Qn(i){if(!(typeof i!="object"||!i.RTCPeerConnection)&&("getRemoteStreams"in i.RTCPeerConnection.prototype||(i.RTCPeerConnection.prototype.getRemoteStreams=function(){return this._remoteStreams?this._remoteStreams:[]}),!("onaddstream"in i.RTCPeerConnection.prototype))){Object.defineProperty(i.RTCPeerConnection.prototype,"onaddstream",{get(){return this._onaddstream},set(n){this._onaddstream&&(this.removeEventListener("addstream",this._onaddstream),this.removeEventListener("track",this._onaddstreampoly)),this.addEventListener("addstream",this._onaddstream=n),this.addEventListener("track",this._onaddstreampoly=r=>{r.streams.forEach(a=>{if(this._remoteStreams||(this._remoteStreams=[]),this._remoteStreams.includes(a))return;this._remoteStreams.push(a);const u=new Event("addstream");u.stream=a,this.dispatchEvent(u)})})}});const o=i.RTCPeerConnection.prototype.setRemoteDescription;i.RTCPeerConnection.prototype.setRemoteDescription=function(){const r=this;return this._onaddstreampoly||this.addEventListener("track",this._onaddstreampoly=function(a){a.streams.forEach(u=>{if(r._remoteStreams||(r._remoteStreams=[]),r._remoteStreams.indexOf(u)>=0)return;r._remoteStreams.push(u);const v=new Event("addstream");v.stream=u,r.dispatchEvent(v)})}),o.apply(r,arguments)}}}function ei(i){if(typeof i!="object"||!i.RTCPeerConnection)return;const o=i.RTCPeerConnection.prototype,n=o.createOffer,r=o.createAnswer,a=o.setLocalDescription,u=o.setRemoteDescription,v=o.addIceCandidate;o.createOffer=function(b,g){const y=arguments.length>=2?arguments[2]:arguments[0],C=n.apply(this,[y]);return g?(C.then(b,g),Promise.resolve()):C},o.createAnswer=function(b,g){const y=arguments.length>=2?arguments[2]:arguments[0],C=r.apply(this,[y]);return g?(C.then(b,g),Promise.resolve()):C};let h=function(k,b,g){const y=a.apply(this,[k]);return g?(y.then(b,g),Promise.resolve()):y};o.setLocalDescription=h,h=function(k,b,g){const y=u.apply(this,[k]);return g?(y.then(b,g),Promise.resolve()):y},o.setRemoteDescription=h,h=function(k,b,g){const y=v.apply(this,[k]);return g?(y.then(b,g),Promise.resolve()):y},o.addIceCandidate=h}function ti(i){const o=i&&i.navigator;if(o.mediaDevices&&o.mediaDevices.getUserMedia){const n=o.mediaDevices,r=n.getUserMedia.bind(n);o.mediaDevices.getUserMedia=a=>r(ri(a))}!o.getUserMedia&&o.mediaDevices&&o.mediaDevices.getUserMedia&&(o.getUserMedia=(function(r,a,u){o.mediaDevices.getUserMedia(r).then(a,u)}).bind(o))}function ri(i){return i&&i.video!==void 0?Object.assign({},i,{video:_n(i.video)}):i}function ni(i){if(!i.RTCPeerConnection)return;const o=i.RTCPeerConnection;i.RTCPeerConnection=function(r,a){if(r&&r.iceServers){const u=[];for(let v=0;v<r.iceServers.length;v++){let h=r.iceServers[v];h.urls===void 0&&h.url?(wr("RTCIceServer.url","RTCIceServer.urls"),h=JSON.parse(JSON.stringify(h)),h.urls=h.url,delete h.url,u.push(h)):u.push(r.iceServers[v])}r.iceServers=u}return new o(r,a)},i.RTCPeerConnection.prototype=o.prototype,"generateCertificate"in o&&Object.defineProperty(i.RTCPeerConnection,"generateCertificate",{get(){return o.generateCertificate}})}function ii(i){typeof i=="object"&&i.RTCTrackEvent&&"receiver"in i.RTCTrackEvent.prototype&&!("transceiver"in i.RTCTrackEvent.prototype)&&Object.defineProperty(i.RTCTrackEvent.prototype,"transceiver",{get(){return{receiver:this.receiver}}})}function si(i){const o=i.RTCPeerConnection.prototype.createOffer;i.RTCPeerConnection.prototype.createOffer=function(r){if(r){typeof r.offerToReceiveAudio<"u"&&(r.offerToReceiveAudio=!!r.offerToReceiveAudio);const a=this.getTransceivers().find(v=>v.receiver.track.kind==="audio");r.offerToReceiveAudio===!1&&a?a.direction==="sendrecv"?a.setDirection?a.setDirection("sendonly"):a.direction="sendonly":a.direction==="recvonly"&&(a.setDirection?a.setDirection("inactive"):a.direction="inactive"):r.offerToReceiveAudio===!0&&!a&&this.addTransceiver("audio",{direction:"recvonly"}),typeof r.offerToReceiveVideo<"u"&&(r.offerToReceiveVideo=!!r.offerToReceiveVideo);const u=this.getTransceivers().find(v=>v.receiver.track.kind==="video");r.offerToReceiveVideo===!1&&u?u.direction==="sendrecv"?u.setDirection?u.setDirection("sendonly"):u.direction="sendonly":u.direction==="recvonly"&&(u.setDirection?u.setDirection("inactive"):u.direction="inactive"):r.offerToReceiveVideo===!0&&!u&&this.addTransceiver("video",{direction:"recvonly"})}return o.apply(this,arguments)}}function oi(i){typeof i!="object"||i.AudioContext||(i.AudioContext=i.webkitAudioContext)}const Sn=Object.freeze(Object.defineProperty({__proto__:null,shimAudioContext:oi,shimCallbacksAPI:ei,shimConstraints:ri,shimCreateOfferLegacy:si,shimGetUserMedia:ti,shimLocalStreamsAPI:Zn,shimRTCIceServerUrls:ni,shimRemoteStreamsAPI:Qn,shimTrackEventTransceiver:ii},Symbol.toStringTag,{value:"Module"}));var Cn=typeof globalThis<"u"?globalThis:typeof window<"u"?window:typeof global<"u"?global:typeof self<"u"?self:{};function ks(i){return i&&i.__esModule&&Object.prototype.hasOwnProperty.call(i,"default")?i.default:i}var jr={exports:{}},Rn;function Ss(){return Rn||(Rn=1,function(i){const o={};o.generateIdentifier=function(){return Math.random().toString(36).substring(2,12)},o.localCName=o.generateIdentifier(),o.splitLines=function(n){return n.trim().split(`
`).map(r=>r.trim())},o.splitSections=function(n){return n.split(`
m=`).map((a,u)=>(u>0?"m="+a:a).trim()+`\r
`)},o.getDescription=function(n){const r=o.splitSections(n);return r&&r[0]},o.getMediaSections=function(n){const r=o.splitSections(n);return r.shift(),r},o.matchPrefix=function(n,r){return o.splitLines(n).filter(a=>a.indexOf(r)===0)},o.parseCandidate=function(n){let r;n.indexOf("a=candidate:")===0?r=n.substring(12).split(" "):r=n.substring(10).split(" ");const a={foundation:r[0],component:{1:"rtp",2:"rtcp"}[r[1]]||r[1],protocol:r[2].toLowerCase(),priority:parseInt(r[3],10),ip:r[4],address:r[4],port:parseInt(r[5],10),type:r[7]};for(let u=8;u<r.length;u+=2)switch(r[u]){case"raddr":a.relatedAddress=r[u+1];break;case"rport":a.relatedPort=parseInt(r[u+1],10);break;case"tcptype":a.tcpType=r[u+1];break;case"ufrag":a.ufrag=r[u+1],a.usernameFragment=r[u+1];break;default:a[r[u]]===void 0&&(a[r[u]]=r[u+1]);break}return a},o.writeCandidate=function(n){const r=[];r.push(n.foundation);const a=n.component;a==="rtp"?r.push(1):a==="rtcp"?r.push(2):r.push(a),r.push(n.protocol.toUpperCase()),r.push(n.priority),r.push(n.address||n.ip),r.push(n.port);const u=n.type;return r.push("typ"),r.push(u),u!=="host"&&n.relatedAddress&&n.relatedPort&&(r.push("raddr"),r.push(n.relatedAddress),r.push("rport"),r.push(n.relatedPort)),n.tcpType&&n.protocol.toLowerCase()==="tcp"&&(r.push("tcptype"),r.push(n.tcpType)),(n.usernameFragment||n.ufrag)&&(r.push("ufrag"),r.push(n.usernameFragment||n.ufrag)),"candidate:"+r.join(" ")},o.parseIceOptions=function(n){return n.substring(14).split(" ")},o.parseRtpMap=function(n){let r=n.substring(9).split(" ");const a={payloadType:parseInt(r.shift(),10)};return r=r[0].split("/"),a.name=r[0],a.clockRate=parseInt(r[1],10),a.channels=r.length===3?parseInt(r[2],10):1,a.numChannels=a.channels,a},o.writeRtpMap=function(n){let r=n.payloadType;n.preferredPayloadType!==void 0&&(r=n.preferredPayloadType);const a=n.channels||n.numChannels||1;return"a=rtpmap:"+r+" "+n.name+"/"+n.clockRate+(a!==1?"/"+a:"")+`\r
`},o.parseExtmap=function(n){const r=n.substring(9).split(" ");return{id:parseInt(r[0],10),direction:r[0].indexOf("/")>0?r[0].split("/")[1]:"sendrecv",uri:r[1],attributes:r.slice(2).join(" ")}},o.writeExtmap=function(n){return"a=extmap:"+(n.id||n.preferredId)+(n.direction&&n.direction!=="sendrecv"?"/"+n.direction:"")+" "+n.uri+(n.attributes?" "+n.attributes:"")+`\r
`},o.parseFmtp=function(n){const r={};let a;const u=n.substring(n.indexOf(" ")+1).split(";");for(let v=0;v<u.length;v++)a=u[v].trim().split("="),r[a[0].trim()]=a[1];return r},o.writeFmtp=function(n){let r="",a=n.payloadType;if(n.preferredPayloadType!==void 0&&(a=n.preferredPayloadType),n.parameters&&Object.keys(n.parameters).length){const u=[];Object.keys(n.parameters).forEach(v=>{n.parameters[v]!==void 0?u.push(v+"="+n.parameters[v]):u.push(v)}),r+="a=fmtp:"+a+" "+u.join(";")+`\r
`}return r},o.parseRtcpFb=function(n){const r=n.substring(n.indexOf(" ")+1).split(" ");return{type:r.shift(),parameter:r.join(" ")}},o.writeRtcpFb=function(n){let r="",a=n.payloadType;return n.preferredPayloadType!==void 0&&(a=n.preferredPayloadType),n.rtcpFeedback&&n.rtcpFeedback.length&&n.rtcpFeedback.forEach(u=>{r+="a=rtcp-fb:"+a+" "+u.type+(u.parameter&&u.parameter.length?" "+u.parameter:"")+`\r
`}),r},o.parseSsrcMedia=function(n){const r=n.indexOf(" "),a={ssrc:parseInt(n.substring(7,r),10)},u=n.indexOf(":",r);return u>-1?(a.attribute=n.substring(r+1,u),a.value=n.substring(u+1)):a.attribute=n.substring(r+1),a},o.parseSsrcGroup=function(n){const r=n.substring(13).split(" ");return{semantics:r.shift(),ssrcs:r.map(a=>parseInt(a,10))}},o.getMid=function(n){const r=o.matchPrefix(n,"a=mid:")[0];if(r)return r.substring(6)},o.parseFingerprint=function(n){const r=n.substring(14).split(" ");return{algorithm:r[0].toLowerCase(),value:r[1].toUpperCase()}},o.getDtlsParameters=function(n,r){return{role:"auto",fingerprints:o.matchPrefix(n+r,"a=fingerprint:").map(o.parseFingerprint)}},o.writeDtlsParameters=function(n,r){let a="a=setup:"+r+`\r
`;return n.fingerprints.forEach(u=>{a+="a=fingerprint:"+u.algorithm+" "+u.value+`\r
`}),a},o.parseCryptoLine=function(n){const r=n.substring(9).split(" ");return{tag:parseInt(r[0],10),cryptoSuite:r[1],keyParams:r[2],sessionParams:r.slice(3)}},o.writeCryptoLine=function(n){return"a=crypto:"+n.tag+" "+n.cryptoSuite+" "+(typeof n.keyParams=="object"?o.writeCryptoKeyParams(n.keyParams):n.keyParams)+(n.sessionParams?" "+n.sessionParams.join(" "):"")+`\r
`},o.parseCryptoKeyParams=function(n){if(n.indexOf("inline:")!==0)return null;const r=n.substring(7).split("|");return{keyMethod:"inline",keySalt:r[0],lifeTime:r[1],mkiValue:r[2]?r[2].split(":")[0]:void 0,mkiLength:r[2]?r[2].split(":")[1]:void 0}},o.writeCryptoKeyParams=function(n){return n.keyMethod+":"+n.keySalt+(n.lifeTime?"|"+n.lifeTime:"")+(n.mkiValue&&n.mkiLength?"|"+n.mkiValue+":"+n.mkiLength:"")},o.getCryptoParameters=function(n,r){return o.matchPrefix(n+r,"a=crypto:").map(o.parseCryptoLine)},o.getIceParameters=function(n,r){const a=o.matchPrefix(n+r,"a=ice-ufrag:")[0],u=o.matchPrefix(n+r,"a=ice-pwd:")[0];return a&&u?{usernameFragment:a.substring(12),password:u.substring(10)}:null},o.writeIceParameters=function(n){let r="a=ice-ufrag:"+n.usernameFragment+`\r
a=ice-pwd:`+n.password+`\r
`;return n.iceLite&&(r+=`a=ice-lite\r
`),r},o.parseRtpParameters=function(n){const r={codecs:[],headerExtensions:[],fecMechanisms:[],rtcp:[]},u=o.splitLines(n)[0].split(" ");r.profile=u[2];for(let h=3;h<u.length;h++){const k=u[h],b=o.matchPrefix(n,"a=rtpmap:"+k+" ")[0];if(b){const g=o.parseRtpMap(b),y=o.matchPrefix(n,"a=fmtp:"+k+" ");switch(g.parameters=y.length?o.parseFmtp(y[0]):{},g.rtcpFeedback=o.matchPrefix(n,"a=rtcp-fb:"+k+" ").map(o.parseRtcpFb),r.codecs.push(g),g.name.toUpperCase()){case"RED":case"ULPFEC":r.fecMechanisms.push(g.name.toUpperCase());break}}}o.matchPrefix(n,"a=extmap:").forEach(h=>{r.headerExtensions.push(o.parseExtmap(h))});const v=o.matchPrefix(n,"a=rtcp-fb:* ").map(o.parseRtcpFb);return r.codecs.forEach(h=>{v.forEach(k=>{h.rtcpFeedback.find(g=>g.type===k.type&&g.parameter===k.parameter)||h.rtcpFeedback.push(k)})}),r},o.writeRtpDescription=function(n,r){let a="";a+="m="+n+" ",a+=r.codecs.length>0?"9":"0",a+=" "+(r.profile||"UDP/TLS/RTP/SAVPF")+" ",a+=r.codecs.map(v=>v.preferredPayloadType!==void 0?v.preferredPayloadType:v.payloadType).join(" ")+`\r
`,a+=`c=IN IP4 0.0.0.0\r
`,a+=`a=rtcp:9 IN IP4 0.0.0.0\r
`,r.codecs.forEach(v=>{a+=o.writeRtpMap(v),a+=o.writeFmtp(v),a+=o.writeRtcpFb(v)});let u=0;return r.codecs.forEach(v=>{v.maxptime>u&&(u=v.maxptime)}),u>0&&(a+="a=maxptime:"+u+`\r
`),r.headerExtensions&&r.headerExtensions.forEach(v=>{a+=o.writeExtmap(v)}),a},o.parseRtpEncodingParameters=function(n){const r=[],a=o.parseRtpParameters(n),u=a.fecMechanisms.indexOf("RED")!==-1,v=a.fecMechanisms.indexOf("ULPFEC")!==-1,h=o.matchPrefix(n,"a=ssrc:").map(C=>o.parseSsrcMedia(C)).filter(C=>C.attribute==="cname"),k=h.length>0&&h[0].ssrc;let b;const g=o.matchPrefix(n,"a=ssrc-group:FID").map(C=>C.substring(17).split(" ").map(F=>parseInt(F,10)));g.length>0&&g[0].length>1&&g[0][0]===k&&(b=g[0][1]),a.codecs.forEach(C=>{if(C.name.toUpperCase()==="RTX"&&C.parameters.apt){let A={ssrc:k,codecPayloadType:parseInt(C.parameters.apt,10)};k&&b&&(A.rtx={ssrc:b}),r.push(A),u&&(A=JSON.parse(JSON.stringify(A)),A.fec={ssrc:k,mechanism:v?"red+ulpfec":"red"},r.push(A))}}),r.length===0&&k&&r.push({ssrc:k});let y=o.matchPrefix(n,"b=");return y.length&&(y[0].indexOf("b=TIAS:")===0?y=parseInt(y[0].substring(7),10):y[0].indexOf("b=AS:")===0?y=parseInt(y[0].substring(5),10)*1e3*.95-50*40*8:y=void 0,r.forEach(C=>{C.maxBitrate=y})),r},o.parseRtcpParameters=function(n){const r={},a=o.matchPrefix(n,"a=ssrc:").map(h=>o.parseSsrcMedia(h)).filter(h=>h.attribute==="cname")[0];a&&(r.cname=a.value,r.ssrc=a.ssrc);const u=o.matchPrefix(n,"a=rtcp-rsize");r.reducedSize=u.length>0,r.compound=u.length===0;const v=o.matchPrefix(n,"a=rtcp-mux");return r.mux=v.length>0,r},o.writeRtcpParameters=function(n){let r="";return n.reducedSize&&(r+=`a=rtcp-rsize\r
`),n.mux&&(r+=`a=rtcp-mux\r
`),n.ssrc!==void 0&&n.cname&&(r+="a=ssrc:"+n.ssrc+" cname:"+n.cname+`\r
`),r},o.parseMsid=function(n){let r;const a=o.matchPrefix(n,"a=msid:");if(a.length===1)return r=a[0].substring(7).split(" "),{stream:r[0],track:r[1]};const u=o.matchPrefix(n,"a=ssrc:").map(v=>o.parseSsrcMedia(v)).filter(v=>v.attribute==="msid");if(u.length>0)return r=u[0].value.split(" "),{stream:r[0],track:r[1]}},o.parseSctpDescription=function(n){const r=o.parseMLine(n),a=o.matchPrefix(n,"a=max-message-size:");let u;a.length>0&&(u=parseInt(a[0].substring(19),10)),isNaN(u)&&(u=65536);const v=o.matchPrefix(n,"a=sctp-port:");if(v.length>0)return{port:parseInt(v[0].substring(12),10),protocol:r.fmt,maxMessageSize:u};const h=o.matchPrefix(n,"a=sctpmap:");if(h.length>0){const k=h[0].substring(10).split(" ");return{port:parseInt(k[0],10),protocol:k[1],maxMessageSize:u}}},o.writeSctpDescription=function(n,r){let a=[];return n.protocol!=="DTLS/SCTP"?a=["m="+n.kind+" 9 "+n.protocol+" "+r.protocol+`\r
`,`c=IN IP4 0.0.0.0\r
`,"a=sctp-port:"+r.port+`\r
`]:a=["m="+n.kind+" 9 "+n.protocol+" "+r.port+`\r
`,`c=IN IP4 0.0.0.0\r
`,"a=sctpmap:"+r.port+" "+r.protocol+` 65535\r
`],r.maxMessageSize!==void 0&&a.push("a=max-message-size:"+r.maxMessageSize+`\r
`),a.join("")},o.generateSessionId=function(){return Math.random().toString().substr(2,22)},o.writeSessionBoilerplate=function(n,r,a){let u;const v=r!==void 0?r:2;return n?u=n:u=o.generateSessionId(),`v=0\r
o=`+(a||"thisisadapterortc")+" "+u+" "+v+` IN IP4 127.0.0.1\r
s=-\r
t=0 0\r
`},o.getDirection=function(n,r){const a=o.splitLines(n);for(let u=0;u<a.length;u++)switch(a[u]){case"a=sendrecv":case"a=sendonly":case"a=recvonly":case"a=inactive":return a[u].substring(2)}return r?o.getDirection(r):"sendrecv"},o.getKind=function(n){return o.splitLines(n)[0].split(" ")[0].substring(2)},o.isRejected=function(n){return n.split(" ",2)[1]==="0"},o.parseMLine=function(n){const a=o.splitLines(n)[0].substring(2).split(" ");return{kind:a[0],port:parseInt(a[1],10),protocol:a[2],fmt:a.slice(3).join(" ")}},o.parseOLine=function(n){const a=o.matchPrefix(n,"o=")[0].substring(2).split(" ");return{username:a[0],sessionId:a[1],sessionVersion:parseInt(a[2],10),netType:a[3],addressType:a[4],address:a[5]}},o.isValidSDP=function(n){if(typeof n!="string"||n.length===0)return!1;const r=o.splitLines(n);for(let a=0;a<r.length;a++)if(r[a].length<2||r[a].charAt(1)!=="=")return!1;return!0},i.exports=o}(jr)),jr.exports}var ai=Ss();const it=ks(ai),Cs=hs({__proto__:null,default:it},[ai]);function rr(i){if(!i.RTCIceCandidate||i.RTCIceCandidate&&"foundation"in i.RTCIceCandidate.prototype)return;const o=i.RTCIceCandidate;i.RTCIceCandidate=function(r){if(typeof r=="object"&&r.candidate&&r.candidate.indexOf("a=")===0&&(r=JSON.parse(JSON.stringify(r)),r.candidate=r.candidate.substring(2)),r.candidate&&r.candidate.length){const a=new o(r),u=it.parseCandidate(r.candidate);for(const v in u)v in a||Object.defineProperty(a,v,{value:u[v]});return a.toJSON=function(){return{candidate:a.candidate,sdpMid:a.sdpMid,sdpMLineIndex:a.sdpMLineIndex,usernameFragment:a.usernameFragment}},a}return new o(r)},i.RTCIceCandidate.prototype=o.prototype,qe(i,"icecandidate",n=>(n.candidate&&Object.defineProperty(n,"candidate",{value:new i.RTCIceCandidate(n.candidate),writable:"false"}),n))}function Lr(i){!i.RTCIceCandidate||i.RTCIceCandidate&&"relayProtocol"in i.RTCIceCandidate.prototype||qe(i,"icecandidate",o=>{if(o.candidate){const n=it.parseCandidate(o.candidate.candidate);n.type==="relay"&&(o.candidate.relayProtocol={0:"tls",1:"tcp",2:"udp"}[n.priority>>24])}return o})}function nr(i,o){if(!i.RTCPeerConnection)return;"sctp"in i.RTCPeerConnection.prototype||Object.defineProperty(i.RTCPeerConnection.prototype,"sctp",{get(){return typeof this._sctp>"u"?null:this._sctp}});const n=function(h){if(!h||!h.sdp)return!1;const k=it.splitSections(h.sdp);return k.shift(),k.some(b=>{const g=it.parseMLine(b);return g&&g.kind==="application"&&g.protocol.indexOf("SCTP")!==-1})},r=function(h){const k=h.sdp.match(/mozilla...THIS_IS_SDPARTA-(\d+)/);if(k===null||k.length<2)return-1;const b=parseInt(k[1],10);return b!==b?-1:b},a=function(h){let k=65536;return o.browser==="firefox"&&(o.version<57?h===-1?k=16384:k=2147483637:o.version<60?k=o.version===57?65535:65536:k=2147483637),k},u=function(h,k){let b=65536;o.browser==="firefox"&&o.version===57&&(b=65535);const g=it.matchPrefix(h.sdp,"a=max-message-size:");return g.length>0?b=parseInt(g[0].substring(19),10):o.browser==="firefox"&&k!==-1&&(b=2147483637),b},v=i.RTCPeerConnection.prototype.setRemoteDescription;i.RTCPeerConnection.prototype.setRemoteDescription=function(){if(this._sctp=null,o.browser==="chrome"&&o.version>=76){const{sdpSemantics:k}=this.getConfiguration();k==="plan-b"&&Object.defineProperty(this,"sctp",{get(){return typeof this._sctp>"u"?null:this._sctp},enumerable:!0,configurable:!0})}if(n(arguments[0])){const k=r(arguments[0]),b=a(k),g=u(arguments[0],k);let y;b===0&&g===0?y=Number.POSITIVE_INFINITY:b===0||g===0?y=Math.max(b,g):y=Math.min(b,g);const C={};Object.defineProperty(C,"maxMessageSize",{get(){return y}}),this._sctp=C}return v.apply(this,arguments)}}function ir(i){if(!(i.RTCPeerConnection&&"createDataChannel"in i.RTCPeerConnection.prototype))return;function o(r,a){const u=r.send;r.send=function(){const h=arguments[0],k=h.length||h.size||h.byteLength;if(r.readyState==="open"&&a.sctp&&k>a.sctp.maxMessageSize)throw new TypeError("Message too large (can send a maximum of "+a.sctp.maxMessageSize+" bytes)");return u.apply(r,arguments)}}const n=i.RTCPeerConnection.prototype.createDataChannel;i.RTCPeerConnection.prototype.createDataChannel=function(){const a=n.apply(this,arguments);return o(a,this),a},qe(i,"datachannel",r=>(o(r.channel,r.target),r))}function Dr(i){if(!i.RTCPeerConnection||"connectionState"in i.RTCPeerConnection.prototype)return;const o=i.RTCPeerConnection.prototype;Object.defineProperty(o,"connectionState",{get(){return{completed:"connected",checking:"connecting"}[this.iceConnectionState]||this.iceConnectionState},enumerable:!0,configurable:!0}),Object.defineProperty(o,"onconnectionstatechange",{get(){return this._onconnectionstatechange||null},set(n){this._onconnectionstatechange&&(this.removeEventListener("connectionstatechange",this._onconnectionstatechange),delete this._onconnectionstatechange),n&&this.addEventListener("connectionstatechange",this._onconnectionstatechange=n)},enumerable:!0,configurable:!0}),["setLocalDescription","setRemoteDescription"].forEach(n=>{const r=o[n];o[n]=function(){return this._connectionstatechangepoly||(this._connectionstatechangepoly=a=>{const u=a.target;if(u._lastConnectionState!==u.connectionState){u._lastConnectionState=u.connectionState;const v=new Event("connectionstatechange",a);u.dispatchEvent(v)}return a},this.addEventListener("iceconnectionstatechange",this._connectionstatechangepoly)),r.apply(this,arguments)}})}function $r(i,o){if(!i.RTCPeerConnection||o.browser==="chrome"&&o.version>=71||o.browser==="safari"&&o._safariVersion>=13.1)return;const n=i.RTCPeerConnection.prototype.setRemoteDescription;i.RTCPeerConnection.prototype.setRemoteDescription=function(a){if(a&&a.sdp&&a.sdp.indexOf(`
a=extmap-allow-mixed`)!==-1){const u=a.sdp.split(`
`).filter(v=>v.trim()!=="a=extmap-allow-mixed").join(`
`);i.RTCSessionDescription&&a instanceof i.RTCSessionDescription?arguments[0]=new i.RTCSessionDescription({type:a.type,sdp:u}):a.sdp=u}return n.apply(this,arguments)}}function sr(i,o){if(!(i.RTCPeerConnection&&i.RTCPeerConnection.prototype))return;const n=i.RTCPeerConnection.prototype.addIceCandidate;!n||n.length===0||(i.RTCPeerConnection.prototype.addIceCandidate=function(){return arguments[0]?(o.browser==="chrome"&&o.version<78||o.browser==="firefox"&&o.version<68||o.browser==="safari")&&arguments[0]&&arguments[0].candidate===""?Promise.resolve():n.apply(this,arguments):(arguments[1]&&arguments[1].apply(null),Promise.resolve())})}function or(i,o){if(!(i.RTCPeerConnection&&i.RTCPeerConnection.prototype))return;const n=i.RTCPeerConnection.prototype.setLocalDescription;!n||n.length===0||(i.RTCPeerConnection.prototype.setLocalDescription=function(){let a=arguments[0]||{};if(typeof a!="object"||a.type&&a.sdp)return n.apply(this,arguments);if(a={type:a.type,sdp:a.sdp},!a.type)switch(this.signalingState){case"stable":case"have-local-offer":case"have-remote-pranswer":a.type="offer";break;default:a.type="answer";break}return a.sdp||a.type!=="offer"&&a.type!=="answer"?n.apply(this,[a]):(a.type==="offer"?this.createOffer:this.createAnswer).apply(this).then(v=>n.apply(this,[v]))})}const Rs=Object.freeze(Object.defineProperty({__proto__:null,removeExtmapAllowMixed:$r,shimAddIceCandidateNullOrEmpty:sr,shimConnectionState:Dr,shimMaxMessageSize:nr,shimParameterlessSetLocalDescription:or,shimRTCIceCandidate:rr,shimRTCIceCandidateRelayProtocol:Lr,shimSendThrowTypeError:ir},Symbol.toStringTag,{value:"Module"}));function Ts({window:i}={},o={shimChrome:!0,shimFirefox:!0,shimSafari:!0}){const n=In,r=ys(i),a={browserDetails:r,commonShim:Rs,extractVersion:Pt,disableLog:ms,disableWarnings:vs,sdp:Cs};switch(r.browser){case"chrome":if(!xn||!_r||!o.shimChrome)return n("Chrome shim is not included in this adapter release."),a;if(r.version===null)return n("Chrome shim can not determine version, not shimming."),a;n("adapter.js shimming chrome."),a.browserShim=xn,sr(i,r),or(i),On(i,r),Ln(i),_r(i,r),Dn(i),Nn(i,r),$n(i),wn(i),Fn(i),Bn(i,r),rr(i),Lr(i),Dr(i),nr(i,r),ir(i),$r(i,r);break;case"firefox":if(!kn||!Or||!o.shimFirefox)return n("Firefox shim is not included in this adapter release."),a;n("adapter.js shimming firefox."),a.browserShim=kn,sr(i,r),or(i),Vn(i,r),Or(i,r),zn(i),Hn(i),Gn(i),Jn(i),qn(i),Wn(i),Yn(i),Kn(i),Xn(i),rr(i),Dr(i),nr(i,r),ir(i);break;case"safari":if(!Sn||!o.shimSafari)return n("Safari shim is not included in this adapter release."),a;n("adapter.js shimming safari."),a.browserShim=Sn,sr(i,r),or(i),ni(i),si(i),ei(i),Zn(i),Qn(i),ii(i),ti(i),oi(i),rr(i),Lr(i),nr(i,r),ir(i),$r(i,r);break;default:n("Unsupported browser!");break}return a}Ts({window:typeof window>"u"?void 0:window});const Ps={iceServers:[{urls:"stun:stun.l.google.com:19302"},{urls:"stun:stun1.l.google.com:19302"},{urls:"stun:stun2.l.google.com:19302"}]};class Es{constructor(o,n,r,a,u,v){this.meetingId=o,this.userId=n,this.peerConnections={},this.localStream=null,this.onRemoteStreamAdded=r,this.onRemoteStreamRemoved=a,this.onParticipantJoined=u,this.onParticipantLeft=v}async initLocalStream(o=!0,n=!0){try{return this.localStream=await navigator.mediaDevices.getUserMedia({audio:o,video:n}),this.localStream}catch(r){throw console.error("Error accessing media devices:",r),r}}async joinMeeting(){try{const o=await Xi(ce(Q,"meetings",this.meetingId));if(!o.exists())throw new Error("Meeting not found");const n=o.data(),r=n.createdBy===this.userId;if(console.log(`Joining meeting. isAdmin: ${r}, requiresApproval: ${n.requiresApproval}`),r)await this.addParticipantToMeeting(),this.setupMeetingListeners();else if(n.requiresApproval===!0){console.log("Meeting requires approval, sending join request");const u=localStorage.getItem("userName")||"Anonymous";alert("Your request to join this meeting has been sent. Please wait for the host to approve."),await this.sendJoinRequest(),this.joinRequestUnsubscribe=er(ce(Q,"meetings",this.meetingId,"participants",this.userId),v=>{v.exists()&&(console.log("Join request approved, joining meeting"),alert("Your request to join has been approved!"),this.joinRequestUnsubscribe&&this.joinRequestUnsubscribe(),this.setupMeetingListeners())});return}else console.log("No approval required, joining directly"),await this.addParticipantToMeeting(),this.setupMeetingListeners()}catch(o){throw console.error("Error joining meeting:",o),o}}async addParticipantToMeeting(){const o=nt(Q,"meetings",this.meetingId,"participants");await Rt(ce(o,this.userId),{joined:new Date().toISOString(),userId:this.userId,audioMuted:!1,videoMuted:!1})}async sendJoinRequest(){try{const o=localStorage.getItem("userName")||"Anonymous";await Rt(ce(Q,"meetings",this.meetingId,"joinRequests",this.userId),{userId:this.userId,userName:o,timestamp:new Date().toISOString(),status:"pending"}),console.log(`Join request sent for user ${o}, waiting for approval`)}catch(o){throw console.error("Error sending join request:",o),o}}setupMeetingListeners(){const o=nt(Q,"meetings",this.meetingId,"participants");this.participantsUnsubscribe=er(o,n=>{n.docChanges().forEach(r=>{const a=r.doc.data(),u=r.doc.id;u!==this.userId&&(r.type==="added"&&(this.onParticipantJoined(a),this.createPeerConnection(u),this.createOffer(u)),r.type==="removed"&&(this.onParticipantLeft(u),this.closePeerConnection(u)),r.type)})}),this.signalingUnsubscribe=er(nt(Q,"meetings",this.meetingId,"signaling"),n=>{n.docChanges().forEach(r=>{const a=r.doc.data();a.from!==this.userId&&(a.type==="offer"&&a.to===this.userId?this.handleOffer(a):a.type==="answer"&&a.to===this.userId?this.handleAnswer(a):a.type==="ice-candidate"&&a.to===this.userId&&this.handleIceCandidate(a))})})}createPeerConnection(o){const n=new RTCPeerConnection(Ps);return this.localStream&&this.localStream.getTracks().forEach(r=>{n.addTrack(r,this.localStream)}),n.onicecandidate=r=>{r.candidate&&this.sendSignal({type:"ice-candidate",from:this.userId,to:o,candidate:r.candidate.toJSON()})},n.onconnectionstatechange=r=>{console.log(`Connection state change: ${n.connectionState}`),(n.connectionState==="failed"||n.connectionState==="disconnected")&&(console.log(`Connection to ${o} failed or disconnected. Attempting to reconnect...`),this.closePeerConnection(o),setTimeout(()=>{this.createPeerConnection(o),this.createOffer(o)},2e3))},n.oniceconnectionstatechange=r=>{console.log(`ICE connection state change: ${n.iceConnectionState}`)},n.onsignalingstatechange=r=>{console.log(`Signaling state change: ${n.signalingState}`)},n.ontrack=r=>{r.streams&&r.streams[0]&&this.onRemoteStreamAdded(o,r.streams[0])},this.peerConnections[o]=n,n}async createOffer(o){try{const n=this.peerConnections[o];if(!n||n.signalingState==="closed"){console.log("Cannot create offer: peer connection is closed or doesn't exist");return}if(n.signalingState!=="stable")if(console.log(`Cannot create offer: signaling state is ${n.signalingState}`),n.signalingState==="have-remote-offer")console.log("Already have a remote offer, waiting for stable state"),await new Promise(a=>{const u=()=>{n.signalingState==="stable"&&(n.removeEventListener("signalingstatechange",u),a())};n.addEventListener("signalingstatechange",u),setTimeout(a,5e3)});else return;if(n.signalingState!=="stable"){console.log(`Still cannot create offer: signaling state is ${n.signalingState}`);return}const r=await n.createOffer({offerToReceiveAudio:!0,offerToReceiveVideo:!0});await n.setLocalDescription(r),await this.waitForIceGathering(n),this.sendSignal({type:"offer",from:this.userId,to:o,sdp:n.localDescription.toJSON()})}catch(n){console.error("Error creating offer:",n)}}async handleOffer(o){try{const n=o.from;this.peerConnections[n]||this.createPeerConnection(n);const r=this.peerConnections[n];if(r.signalingState==="closed"){console.log("Cannot handle offer: peer connection is closed");return}r.signalingState!=="stable"&&(console.log(`Signaling state is ${r.signalingState}, applying rollback`),await r.setLocalDescription({type:"rollback"})),await r.setRemoteDescription(new RTCSessionDescription(o.sdp));const a=await r.createAnswer();await r.setLocalDescription(a),await this.waitForIceGathering(r),this.sendSignal({type:"answer",from:this.userId,to:n,sdp:r.localDescription.toJSON()})}catch(n){console.error("Error handling offer:",n)}}async handleAnswer(o){try{const n=o.from,r=this.peerConnections[n];if(!r){console.log("Cannot handle answer: peer connection doesn't exist");return}if(r.signalingState==="closed"){console.log("Cannot handle answer: peer connection is closed");return}r.signalingState==="have-local-offer"?await r.setRemoteDescription(new RTCSessionDescription(o.sdp)):console.log(`Cannot handle answer: signaling state is ${r.signalingState}`)}catch(n){console.error("Error handling answer:",n)}}async handleIceCandidate(o){try{const n=o.from,r=this.peerConnections[n];if(!r){console.log("Cannot handle ICE candidate: peer connection doesn't exist");return}if(r.signalingState==="closed"){console.log("Cannot handle ICE candidate: peer connection is closed");return}r.remoteDescription?await r.addIceCandidate(new RTCIceCandidate(o.candidate)):console.log("Cannot add ICE candidate: remote description is null")}catch(n){console.error("Error handling ICE candidate:",n)}}async waitForIceGathering(o){if(o.iceGatheringState!=="complete")return new Promise(n=>{const r=()=>{o.iceGatheringState==="complete"&&(o.removeEventListener("icegatheringstatechange",r),n())};o.addEventListener("icegatheringstatechange",r),setTimeout(n,1e3)})}async sendSignal(o){try{const n=nt(Q,"meetings",this.meetingId,"signaling");await Rt(ce(n),{...o,timestamp:new Date().toISOString()})}catch(n){console.error("Error sending signal:",n)}}closePeerConnection(o){const n=this.peerConnections[o];n&&(n.close(),delete this.peerConnections[o],this.onRemoteStreamRemoved(o))}async leaveMeeting(){Object.keys(this.peerConnections).forEach(o=>{this.closePeerConnection(o)}),this.localStream&&this.localStream.getTracks().forEach(o=>o.stop());try{await Et(ce(Q,"meetings",this.meetingId,"participants",this.userId)),this.checkAndEndMeetingIfEmpty()}catch(o){console.error("Error leaving meeting:",o)}this.participantsUnsubscribe&&this.participantsUnsubscribe(),this.signalingUnsubscribe&&this.signalingUnsubscribe(),this.joinRequestUnsubscribe&&this.joinRequestUnsubscribe()}async checkAndEndMeetingIfEmpty(){try{const o=nt(Q,"meetings",this.meetingId,"participants");(await Zi(o)).empty&&(console.log("No participants left in the meeting. Ending the meeting."),await Rt(ce(Q,"meetings",this.meetingId),{active:!1,endedAt:new Date().toISOString()},{merge:!0}))}catch(o){console.error("Error checking if meeting is empty:",o)}}async removeParticipant(o){try{return this.closePeerConnection(o),await Et(ce(Q,"meetings",this.meetingId,"participants",o)),!0}catch(n){return console.error("Error removing participant:",n),!1}}async toggleScreenSharing(o){if(o)try{const n=await navigator.mediaDevices.getDisplayMedia({video:!0}),r=n.getVideoTracks()[0];return Object.values(this.peerConnections).forEach(a=>{const u=a.getSenders().find(v=>v.track&&v.track.kind==="video");u&&u.replaceTrack(r)}),this.screenTrack=r,this.screenTrack.onended=()=>{this.toggleScreenSharing(!1)},n}catch(n){throw console.error("Error starting screen sharing:",n),n}else{if(this.localStream){const n=this.localStream.getVideoTracks()[0];n&&Object.values(this.peerConnections).forEach(r=>{const a=r.getSenders().find(u=>u.track&&u.track.kind==="video");a&&a.replaceTrack(n)})}this.screenTrack&&(this.screenTrack.stop(),this.screenTrack=null)}}async updateVideoTrack(o){try{Object.values(this.peerConnections).forEach(n=>{const r=n.getSenders().find(a=>a.track&&a.track.kind==="video");r&&r.replaceTrack(o)})}catch(n){throw console.error("Error updating video track:",n),n}}}var Mr={},Tn;function js(){return Tn||(Tn=1,(function(){var i;function o(e){var t=0;return function(){return t<e.length?{done:!1,value:e[t++]}:{done:!0}}}var n=typeof Object.defineProperties=="function"?Object.defineProperty:function(e,t,s){return e==Array.prototype||e==Object.prototype||(e[t]=s.value),e};function r(e){e=[typeof globalThis=="object"&&globalThis,e,typeof window=="object"&&window,typeof self=="object"&&self,typeof Cn=="object"&&Cn];for(var t=0;t<e.length;++t){var s=e[t];if(s&&s.Math==Math)return s}throw Error("Cannot find global object")}var a=r(this);function u(e,t){if(t)e:{var s=a;e=e.split(".");for(var l=0;l<e.length-1;l++){var c=e[l];if(!(c in s))break e;s=s[c]}e=e[e.length-1],l=s[e],t=t(l),t!=l&&t!=null&&n(s,e,{configurable:!0,writable:!0,value:t})}}u("Symbol",function(e){function t(p){if(this instanceof t)throw new TypeError("Symbol is not a constructor");return new s(l+(p||"")+"_"+c++,p)}function s(p,f){this.h=p,n(this,"description",{configurable:!0,writable:!0,value:f})}if(e)return e;s.prototype.toString=function(){return this.h};var l="jscomp_symbol_"+(1e9*Math.random()>>>0)+"_",c=0;return t}),u("Symbol.iterator",function(e){if(e)return e;e=Symbol("Symbol.iterator");for(var t="Array Int8Array Uint8Array Uint8ClampedArray Int16Array Uint16Array Int32Array Uint32Array Float32Array Float64Array".split(" "),s=0;s<t.length;s++){var l=a[t[s]];typeof l=="function"&&typeof l.prototype[e]!="function"&&n(l.prototype,e,{configurable:!0,writable:!0,value:function(){return v(o(this))}})}return e});function v(e){return e={next:e},e[Symbol.iterator]=function(){return this},e}function h(e){var t=typeof Symbol<"u"&&Symbol.iterator&&e[Symbol.iterator];return t?t.call(e):{next:o(e)}}function k(e){if(!(e instanceof Array)){e=h(e);for(var t,s=[];!(t=e.next()).done;)s.push(t.value);e=s}return e}var b=typeof Object.assign=="function"?Object.assign:function(e,t){for(var s=1;s<arguments.length;s++){var l=arguments[s];if(l)for(var c in l)Object.prototype.hasOwnProperty.call(l,c)&&(e[c]=l[c])}return e};u("Object.assign",function(e){return e||b});var g=typeof Object.create=="function"?Object.create:function(e){function t(){}return t.prototype=e,new t},y;if(typeof Object.setPrototypeOf=="function")y=Object.setPrototypeOf;else{var C;e:{var A={a:!0},F={};try{F.__proto__=A,C=F.a;break e}catch{}C=!1}y=C?function(e,t){if(e.__proto__=t,e.__proto__!==t)throw new TypeError(e+" is not extensible");return e}:null}var Y=y;function oe(e,t){if(e.prototype=g(t.prototype),e.prototype.constructor=e,Y)Y(e,t);else for(var s in t)if(s!="prototype")if(Object.defineProperties){var l=Object.getOwnPropertyDescriptor(t,s);l&&Object.defineProperty(e,s,l)}else e[s]=t[s];e.za=t.prototype}function Te(){this.m=!1,this.j=null,this.i=void 0,this.h=1,this.v=this.s=0,this.l=null}function st(e){if(e.m)throw new TypeError("Generator is already running");e.m=!0}Te.prototype.u=function(e){this.i=e};function ot(e,t){e.l={ma:t,na:!0},e.h=e.s||e.v}Te.prototype.return=function(e){this.l={return:e},this.h=this.v};function q(e,t,s){return e.h=s,{value:t}}function Mt(e){this.h=new Te,this.i=e}function ee(e,t){st(e.h);var s=e.h.j;return s?te(e,"return"in s?s.return:function(l){return{value:l,done:!0}},t,e.h.return):(e.h.return(t),Ie(e))}function te(e,t,s,l){try{var c=t.call(e.h.j,s);if(!(c instanceof Object))throw new TypeError("Iterator result "+c+" is not an object");if(!c.done)return e.h.m=!1,c;var p=c.value}catch(f){return e.h.j=null,ot(e.h,f),Ie(e)}return e.h.j=null,l.call(e.h,p),Ie(e)}function Ie(e){for(;e.h.h;)try{var t=e.i(e.h);if(t)return e.h.m=!1,{value:t.value,done:!1}}catch(s){e.h.i=void 0,ot(e.h,s)}if(e.h.m=!1,e.h.l){if(t=e.h.l,e.h.l=null,t.na)throw t.ma;return{value:t.return,done:!0}}return{value:void 0,done:!0}}function we(e){this.next=function(t){return st(e.h),e.h.j?t=te(e,e.h.j.next,t,e.h.u):(e.h.u(t),t=Ie(e)),t},this.throw=function(t){return st(e.h),e.h.j?t=te(e,e.h.j.throw,t,e.h.u):(ot(e.h,t),t=Ie(e)),t},this.return=function(t){return ee(e,t)},this[Symbol.iterator]=function(){return this}}function lr(e){function t(l){return e.next(l)}function s(l){return e.throw(l)}return new Promise(function(l,c){function p(f){f.done?l(f.value):Promise.resolve(f.value).then(t,s).then(p,c)}p(e.next())})}function V(e){return lr(new we(new Mt(e)))}u("Promise",function(e){function t(f){this.i=0,this.j=void 0,this.h=[],this.u=!1;var m=this.l();try{f(m.resolve,m.reject)}catch(S){m.reject(S)}}function s(){this.h=null}function l(f){return f instanceof t?f:new t(function(m){m(f)})}if(e)return e;s.prototype.i=function(f){if(this.h==null){this.h=[];var m=this;this.j(function(){m.m()})}this.h.push(f)};var c=a.setTimeout;s.prototype.j=function(f){c(f,0)},s.prototype.m=function(){for(;this.h&&this.h.length;){var f=this.h;this.h=[];for(var m=0;m<f.length;++m){var S=f[m];f[m]=null;try{S()}catch(T){this.l(T)}}}this.h=null},s.prototype.l=function(f){this.j(function(){throw f})},t.prototype.l=function(){function f(T){return function(P){S||(S=!0,T.call(m,P))}}var m=this,S=!1;return{resolve:f(this.I),reject:f(this.m)}},t.prototype.I=function(f){if(f===this)this.m(new TypeError("A Promise cannot resolve to itself"));else if(f instanceof t)this.L(f);else{e:switch(typeof f){case"object":var m=f!=null;break e;case"function":m=!0;break e;default:m=!1}m?this.F(f):this.s(f)}},t.prototype.F=function(f){var m=void 0;try{m=f.then}catch(S){this.m(S);return}typeof m=="function"?this.M(m,f):this.s(f)},t.prototype.m=function(f){this.v(2,f)},t.prototype.s=function(f){this.v(1,f)},t.prototype.v=function(f,m){if(this.i!=0)throw Error("Cannot settle("+f+", "+m+"): Promise already settled in state"+this.i);this.i=f,this.j=m,this.i===2&&this.K(),this.H()},t.prototype.K=function(){var f=this;c(function(){if(f.D()){var m=a.console;typeof m<"u"&&m.error(f.j)}},1)},t.prototype.D=function(){if(this.u)return!1;var f=a.CustomEvent,m=a.Event,S=a.dispatchEvent;return typeof S>"u"?!0:(typeof f=="function"?f=new f("unhandledrejection",{cancelable:!0}):typeof m=="function"?f=new m("unhandledrejection",{cancelable:!0}):(f=a.document.createEvent("CustomEvent"),f.initCustomEvent("unhandledrejection",!1,!0,f)),f.promise=this,f.reason=this.j,S(f))},t.prototype.H=function(){if(this.h!=null){for(var f=0;f<this.h.length;++f)p.i(this.h[f]);this.h=null}};var p=new s;return t.prototype.L=function(f){var m=this.l();f.T(m.resolve,m.reject)},t.prototype.M=function(f,m){var S=this.l();try{f.call(m,S.resolve,S.reject)}catch(T){S.reject(T)}},t.prototype.then=function(f,m){function S(_,M){return typeof _=="function"?function(L){try{T(_(L))}catch(U){P(U)}}:M}var T,P,O=new t(function(_,M){T=_,P=M});return this.T(S(f,T),S(m,P)),O},t.prototype.catch=function(f){return this.then(void 0,f)},t.prototype.T=function(f,m){function S(){switch(T.i){case 1:f(T.j);break;case 2:m(T.j);break;default:throw Error("Unexpected state: "+T.i)}}var T=this;this.h==null?p.i(S):this.h.push(S),this.u=!0},t.resolve=l,t.reject=function(f){return new t(function(m,S){S(f)})},t.race=function(f){return new t(function(m,S){for(var T=h(f),P=T.next();!P.done;P=T.next())l(P.value).T(m,S)})},t.all=function(f){var m=h(f),S=m.next();return S.done?l([]):new t(function(T,P){function O(L){return function(U){_[L]=U,M--,M==0&&T(_)}}var _=[],M=0;do _.push(void 0),M++,l(S.value).T(O(_.length-1),P),S=m.next();while(!S.done)})},t});function Fe(e,t){e instanceof String&&(e+="");var s=0,l=!1,c={next:function(){if(!l&&s<e.length){var p=s++;return{value:t(p,e[p]),done:!1}}return l=!0,{done:!0,value:void 0}}};return c[Symbol.iterator]=function(){return c},c}u("Array.prototype.keys",function(e){return e||function(){return Fe(this,function(t){return t})}}),u("Array.prototype.fill",function(e){return e||function(t,s,l){var c=this.length||0;for(0>s&&(s=Math.max(0,c+s)),(l==null||l>c)&&(l=c),l=Number(l),0>l&&(l=Math.max(0,c+l)),s=Number(s||0);s<l;s++)this[s]=t;return this}});function se(e){return e||Array.prototype.fill}u("Int8Array.prototype.fill",se),u("Uint8Array.prototype.fill",se),u("Uint8ClampedArray.prototype.fill",se),u("Int16Array.prototype.fill",se),u("Uint16Array.prototype.fill",se),u("Int32Array.prototype.fill",se),u("Uint32Array.prototype.fill",se),u("Float32Array.prototype.fill",se),u("Float64Array.prototype.fill",se),u("Object.is",function(e){return e||function(t,s){return t===s?t!==0||1/t===1/s:t!==t&&s!==s}}),u("Array.prototype.includes",function(e){return e||function(t,s){var l=this;l instanceof String&&(l=String(l));var c=l.length;for(s=s||0,0>s&&(s=Math.max(s+c,0));s<c;s++){var p=l[s];if(p===t||Object.is(p,t))return!0}return!1}}),u("String.prototype.includes",function(e){return e||function(t,s){if(this==null)throw new TypeError("The 'this' value for String.prototype.includes must not be null or undefined");if(t instanceof RegExp)throw new TypeError("First argument to String.prototype.includes must not be a regular expression");return this.indexOf(t,s||0)!==-1}});var Ue=this||self;function ge(e,t){e=e.split(".");var s=Ue;e[0]in s||typeof s.execScript>"u"||s.execScript("var "+e[0]);for(var l;e.length&&(l=e.shift());)e.length||t===void 0?s[l]&&s[l]!==Object.prototype[l]?s=s[l]:s=s[l]={}:s[l]=t}function Ne(e){var t;e:{if((t=Ue.navigator)&&(t=t.userAgent))break e;t=""}return t.indexOf(e)!=-1}var Pe=Array.prototype.map?function(e,t){return Array.prototype.map.call(e,t,void 0)}:function(e,t){for(var s=e.length,l=Array(s),c=typeof e=="string"?e.split(""):e,p=0;p<s;p++)p in c&&(l[p]=t.call(void 0,c[p],p,e));return l},Be={},ue=null;function _e(e){var t=e.length,s=3*t/4;s%3?s=Math.floor(s):"=.".indexOf(e[t-1])!=-1&&(s="=.".indexOf(e[t-2])!=-1?s-2:s-1);var l=new Uint8Array(s),c=0;return at(e,function(p){l[c++]=p}),c!==s?l.subarray(0,c):l}function at(e,t){function s(S){for(;l<e.length;){var T=e.charAt(l++),P=ue[T];if(P!=null)return P;if(!/^[\s\xa0]*$/.test(T))throw Error("Unknown base64 encoding at char: "+T)}return S}Oe();for(var l=0;;){var c=s(-1),p=s(0),f=s(64),m=s(64);if(m===64&&c===-1)break;t(c<<2|p>>4),f!=64&&(t(p<<4&240|f>>2),m!=64&&t(f<<6&192|m))}}function Oe(){if(!ue){ue={};for(var e="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789".split(""),t=["+/=","+/","-_=","-_.","-_"],s=0;5>s;s++){var l=e.concat(t[s].split(""));Be[s]=l;for(var c=0;c<l.length;c++){var p=l[c];ue[p]===void 0&&(ue[p]=c)}}}}var Ve=typeof Uint8Array<"u",We=!(Ne("Trident")||Ne("MSIE"))&&typeof Ue.btoa=="function";function Ye(e){if(!We){var t;t===void 0&&(t=0),Oe(),t=Be[t];for(var s=Array(Math.floor(e.length/3)),l=t[64]||"",c=0,p=0;c<e.length-2;c+=3){var f=e[c],m=e[c+1],S=e[c+2],T=t[f>>2];f=t[(f&3)<<4|m>>4],m=t[(m&15)<<2|S>>6],S=t[S&63],s[p++]=T+f+m+S}switch(T=0,S=l,e.length-c){case 2:T=e[c+1],S=t[(T&15)<<2]||l;case 1:e=e[c],s[p]=t[e>>2]+t[(e&3)<<4|T>>4]+S+l}return s.join("")}for(t="";10240<e.length;)t+=String.fromCharCode.apply(null,e.subarray(0,10240)),e=e.subarray(10240);return t+=String.fromCharCode.apply(null,e),btoa(t)}var Ke=RegExp("[-_.]","g");function cr(e){switch(e){case"-":return"+";case"_":return"/";case".":return"=";default:return""}}function At(e){if(!We)return _e(e);Ke.test(e)&&(e=e.replace(Ke,cr)),e=atob(e);for(var t=new Uint8Array(e.length),s=0;s<e.length;s++)t[s]=e.charCodeAt(s);return t}var lt;function ct(){return lt||(lt=new Uint8Array(0))}var ze={},It=typeof Uint8Array.prototype.slice=="function",K=0,re=0;function _t(e){var t=0>e;e=Math.abs(e);var s=e>>>0;e=Math.floor((e-s)/4294967296),t&&(s=h(Ee(s,e)),t=s.next().value,e=s.next().value,s=t),K=s>>>0,re=e>>>0}var Ot=typeof BigInt=="function";function Ee(e,t){return t=~t,e?e=~e+1:t+=1,[e,t]}function Lt(e,t){this.i=e>>>0,this.h=t>>>0}function Dt(e){if(!e)return $t||($t=new Lt(0,0));if(!/^-?\d+$/.test(e))return null;if(16>e.length)_t(Number(e));else if(Ot)e=BigInt(e),K=Number(e&BigInt(4294967295))>>>0,re=Number(e>>BigInt(32)&BigInt(4294967295));else{var t=+(e[0]==="-");re=K=0;for(var s=e.length,l=t,c=(s-t)%6+t;c<=s;l=c,c+=6)l=Number(e.slice(l,c)),re*=1e6,K=1e6*K+l,4294967296<=K&&(re+=K/4294967296|0,K%=4294967296);t&&(t=h(Ee(K,re)),e=t.next().value,t=t.next().value,K=e,re=t)}return new Lt(K,re)}var $t;function wt(e,t){return Error("Invalid wire type: "+e+" (at position "+t+")")}function ut(){return Error("Failed to read varint, encoding is invalid.")}function Ft(e,t){return Error("Tried to read past the end of the data "+t+" > "+e)}function je(){throw Error("Invalid UTF8")}function Ut(e,t){return t=String.fromCharCode.apply(null,t),e==null?t:e+t}var Xe=void 0,dt,ur=typeof TextDecoder<"u",ft,dr=typeof TextEncoder<"u",Ze;function pt(e){if(e!==ze)throw Error("illegal external caller")}function Ge(e,t){if(pt(t),this.V=e,e!=null&&e.length===0)throw Error("ByteString should be constructed with non-empty values")}function ht(){return Ze||(Ze=new Ge(null,ze))}function Nt(e){pt(ze);var t=e.V;return t=t==null||Ve&&t!=null&&t instanceof Uint8Array?t:typeof t=="string"?At(t):null,t==null?t:e.V=t}function Bt(e){if(typeof e=="string")return{buffer:At(e),C:!1};if(Array.isArray(e))return{buffer:new Uint8Array(e),C:!1};if(e.constructor===Uint8Array)return{buffer:e,C:!1};if(e.constructor===ArrayBuffer)return{buffer:new Uint8Array(e),C:!1};if(e.constructor===Ge)return{buffer:Nt(e)||ct(),C:!0};if(e instanceof Uint8Array)return{buffer:new Uint8Array(e.buffer,e.byteOffset,e.byteLength),C:!1};throw Error("Type not convertible to a Uint8Array, expected a Uint8Array, an ArrayBuffer, a base64 encoded string, a ByteString or an Array of numbers")}function Vt(e,t){this.i=null,this.m=!1,this.h=this.j=this.l=0,gt(this,e,t)}function gt(e,t,s){s=s===void 0?{}:s,e.S=s.S===void 0?!1:s.S,t&&(t=Bt(t),e.i=t.buffer,e.m=t.C,e.l=0,e.j=e.i.length,e.h=e.l)}Vt.prototype.reset=function(){this.h=this.l};function be(e,t){if(e.h=t,t>e.j)throw Ft(e.j,t)}function Le(e){var t=e.i,s=e.h,l=t[s++],c=l&127;if(l&128&&(l=t[s++],c|=(l&127)<<7,l&128&&(l=t[s++],c|=(l&127)<<14,l&128&&(l=t[s++],c|=(l&127)<<21,l&128&&(l=t[s++],c|=l<<28,l&128&&t[s++]&128&&t[s++]&128&&t[s++]&128&&t[s++]&128&&t[s++]&128)))))throw ut();return be(e,s),c}function R(e,t){if(0>t)throw Error("Tried to read a negative byte length: "+t);var s=e.h,l=s+t;if(l>e.j)throw Ft(t,e.j-s);return e.h=l,s}var x=[];function D(){this.h=[]}D.prototype.length=function(){return this.h.length},D.prototype.end=function(){var e=this.h;return this.h=[],e};function De(e,t,s){for(;0<s||127<t;)e.h.push(t&127|128),t=(t>>>7|s<<25)>>>0,s>>>=7;e.h.push(t)}function fe(e,t){for(;127<t;)e.h.push(t&127|128),t>>>=7;e.h.push(t)}function mt(e,t){if(x.length){var s=x.pop();gt(s,e,t),e=s}else e=new Vt(e,t);this.h=e,this.j=this.h.h,this.i=this.l=-1,this.setOptions(t)}mt.prototype.setOptions=function(e){e=e===void 0?{}:e,this.ca=e.ca===void 0?!1:e.ca},mt.prototype.reset=function(){this.h.reset(),this.j=this.h.h,this.i=this.l=-1};function zt(e){var t=e.h;if(t.h==t.j)return!1;e.j=e.h.h;var s=Le(e.h)>>>0;if(t=s>>>3,s&=7,!(0<=s&&5>=s))throw wt(s,e.j);if(1>t)throw Error("Invalid field number: "+t+" (at position "+e.j+")");return e.l=t,e.i=s,!0}function Qe(e){switch(e.i){case 0:if(e.i!=0)Qe(e);else e:{e=e.h;for(var t=e.h,s=t+10,l=e.i;t<s;)if((l[t++]&128)===0){be(e,t);break e}throw ut()}break;case 1:e=e.h,be(e,e.h+8);break;case 2:e.i!=2?Qe(e):(t=Le(e.h)>>>0,e=e.h,be(e,e.h+t));break;case 5:e=e.h,be(e,e.h+4);break;case 3:t=e.l;do{if(!zt(e))throw Error("Unmatched start-group tag: stream EOF");if(e.i==4){if(e.l!=t)throw Error("Unmatched end-group tag");break}Qe(e)}while(!0);break;default:throw wt(e.i,e.j)}}var I=[];function de(){this.j=[],this.i=0,this.h=new D}function pe(e,t){t.length!==0&&(e.j.push(t),e.i+=t.length)}function ci(e,t){if(t=t.R){pe(e,e.h.end());for(var s=0;s<t.length;s++)pe(e,Nt(t[s])||ct())}}var Me=typeof Symbol=="function"&&typeof Symbol()=="symbol"?Symbol():void 0;function Je(e,t){return Me?e[Me]|=t:e.A!==void 0?e.A|=t:(Object.defineProperties(e,{A:{value:t,configurable:!0,writable:!0,enumerable:!1}}),t)}function Fr(e,t){Me?e[Me]&&(e[Me]&=~t):e.A!==void 0&&(e.A&=~t)}function X(e){var t;return Me?t=e[Me]:t=e.A,t??0}function xe(e,t){Me?e[Me]=t:e.A!==void 0?e.A=t:Object.defineProperties(e,{A:{value:t,configurable:!0,writable:!0,enumerable:!1}})}function fr(e){return Je(e,1),e}function ui(e,t){xe(t,(e|0)&-51)}function Gt(e,t){xe(t,(e|18)&-41)}var pr={};function Jt(e){return e!==null&&typeof e=="object"&&!Array.isArray(e)&&e.constructor===Object}var vt,Ur=[];xe(Ur,23),vt=Object.freeze(Ur);function hr(e){if(X(e.o)&2)throw Error("Cannot mutate an immutable Message")}function gr(e){var t=e.length;(t=t?e[t-1]:void 0)&&Jt(t)?t.g=1:(t={},e.push((t.g=1,t)))}function Nr(e){var t=e.i+e.G;return e.B||(e.B=e.o[t]={})}function me(e,t){return t===-1?null:t>=e.i?e.B?e.B[t]:void 0:e.o[t+e.G]}function ke(e,t,s,l){hr(e),yt(e,t,s,l)}function yt(e,t,s,l){e.j&&(e.j=void 0),t>=e.i||l?Nr(e)[t]=s:(e.o[t+e.G]=s,(e=e.B)&&t in e&&delete e[t])}function mr(e,t,s,l){var c=me(e,t);Array.isArray(c)||(c=vt);var p=X(c);if(p&1||fr(c),l)p&2||Je(c,2),s&1||Object.freeze(c);else{l=!(s&2);var f=p&2;s&1||!f?l&&p&16&&!f&&Fr(c,16):(c=fr(Array.prototype.slice.call(c)),yt(e,t,c))}return c}function vr(e,t){var s=me(e,t),l=s==null?s:typeof s=="number"||s==="NaN"||s==="Infinity"||s==="-Infinity"?Number(s):void 0;return l!=null&&l!==s&&yt(e,t,l),l}function Br(e,t,s,l,c){e.h||(e.h={});var p=e.h[s],f=mr(e,s,3,c);if(!p){var m=f;p=[];var S=!!(X(e.o)&16);f=!!(X(m)&2);var T=m;!c&&f&&(m=Array.prototype.slice.call(m));for(var P=f,O=0;O<m.length;O++){var _=m[O],M=t,L=!1;if(L=L===void 0?!1:L,_=Array.isArray(_)?new M(_):L?new M:void 0,_!==void 0){M=_.o;var U=L=X(M);f&&(U|=2),S&&(U|=16),U!=L&&xe(M,U),M=U,P=P||!!(2&M),p.push(_)}}return e.h[s]=p,S=X(m),t=S|33,t=P?t&-9:t|8,S!=t&&(P=m,Object.isFrozen(P)&&(P=Array.prototype.slice.call(P)),xe(P,t),m=P),T!==m&&yt(e,s,m),(c||l&&f)&&Je(p,2),l&&Object.freeze(p),p}return c||(c=Object.isFrozen(p),l&&!c?Object.freeze(p):!l&&c&&(p=Array.prototype.slice.call(p),e.h[s]=p)),p}function yr(e,t,s){var l=!!(X(e.o)&2);if(t=Br(e,t,s,l,l),e=mr(e,s,3,l),!(l||X(e)&8)){for(l=0;l<t.length;l++){if(s=t[l],X(s.o)&2){var c=qr(s,!1);c.j=s}else c=s;s!==c&&(t[l]=c,e[l]=c.o)}Je(e,8)}return t}function Se(e,t,s){if(s!=null&&typeof s!="number")throw Error("Value of float/double field must be a number|null|undefined, found "+typeof s+": "+s);ke(e,t,s)}function Vr(e,t,s,l,c){hr(e);var p=Br(e,s,t,!1,!1);return s=l??new s,e=mr(e,t,2,!1),c!=null?(p.splice(c,0,s),e.splice(c,0,s.o)):(p.push(s),e.push(s.o)),s.C()&&Fr(e,8),s}function Ht(e,t){return e??t}function Ce(e,t,s){return s=s===void 0?0:s,Ht(vr(e,t),s)}var qt;function di(e){switch(typeof e){case"number":return isFinite(e)?e:String(e);case"object":if(e)if(Array.isArray(e)){if((X(e)&128)!==0)return e=Array.prototype.slice.call(e),gr(e),e}else{if(Ve&&e!=null&&e instanceof Uint8Array)return Ye(e);if(e instanceof Ge){var t=e.V;return t==null?"":typeof t=="string"?t:e.V=Ye(t)}}}return e}function zr(e,t,s,l){if(e!=null){if(Array.isArray(e))e=br(e,t,s,l!==void 0);else if(Jt(e)){var c={},p;for(p in e)c[p]=zr(e[p],t,s,l);e=c}else e=t(e,l);return e}}function br(e,t,s,l){var c=X(e);l=l?!!(c&16):void 0,e=Array.prototype.slice.call(e);for(var p=0;p<e.length;p++)e[p]=zr(e[p],t,s,l);return s(c,e),e}function fi(e){return e.ja===pr?e.toJSON():di(e)}function pi(e,t){e&128&&gr(t)}function Gr(e,t,s){if(s=s===void 0?Gt:s,e!=null){if(Ve&&e instanceof Uint8Array)return e.length?new Ge(new Uint8Array(e),ze):ht();if(Array.isArray(e)){var l=X(e);return l&2?e:t&&!(l&32)&&(l&16||l===0)?(xe(e,l|2),e):(e=br(e,Gr,l&4?Gt:s,!0),t=X(e),t&4&&t&2&&Object.freeze(e),e)}return e.ja===pr?Hr(e):e}}function Jr(e,t,s,l,c,p,f){if(e=e.h&&e.h[s]){if(l=X(e),l&2?l=e:(p=Pe(e,Hr),Gt(l,p),Object.freeze(p),l=p),hr(t),f=l==null?vt:fr([]),l!=null){for(p=!!l.length,e=0;e<l.length;e++){var m=l[e];p=p&&!(X(m.o)&2),f[e]=m.o}p=(p?8:0)|1,e=X(f),(e&p)!==p&&(Object.isFrozen(f)&&(f=Array.prototype.slice.call(f)),xe(f,e|p)),t.h||(t.h={}),t.h[s]=l}else t.h&&(t.h[s]=void 0);yt(t,s,f,c)}else ke(t,s,Gr(l,p,f),c)}function Hr(e){return X(e.o)&2||(e=qr(e,!0),Je(e.o,2)),e}function qr(e,t){var s=e.o,l=[];Je(l,16);var c=e.constructor.h;if(c&&l.push(c),c=e.B,c){l.length=s.length,l.fill(void 0,l.length,s.length);var p={};l[l.length-1]=p}(X(s)&128)!==0&&gr(l),t=t||e.C()?Gt:ui,p=e.constructor,qt=l,l=new p(l),qt=void 0,e.R&&(l.R=e.R.slice()),p=!!(X(s)&16);for(var f=c?s.length-1:s.length,m=0;m<f;m++)Jr(e,l,m-e.G,s[m],!1,p,t);if(c)for(var S in c)Jr(e,l,+S,c[S],!0,p,t);return l}function ae(e,t,s){e==null&&(e=qt),qt=void 0;var l=this.constructor.i||0,c=0<l,p=this.constructor.h,f=!1;if(e==null){e=p?[p]:[];var m=48,S=!0;c&&(l=0,m|=128),xe(e,m)}else{if(!Array.isArray(e)||p&&p!==e[0])throw Error();var T=m=Je(e,0);if((S=(16&T)!==0)&&((f=(32&T)!==0)||(T|=32)),c){if(128&T)l=0;else if(0<e.length){var P=e[e.length-1];if(Jt(P)&&"g"in P){l=0,T|=128,delete P.g;var O=!0,_;for(_ in P){O=!1;break}O&&e.pop()}}}else if(128&T)throw Error();m!==T&&xe(e,T)}this.G=(p?0:-1)-l,this.h=void 0,this.o=e;e:{if(p=this.o.length,l=p-1,p&&(p=this.o[l],Jt(p))){this.B=p,this.i=l-this.G;break e}t!==void 0&&-1<t?(this.i=Math.max(t,l+1-this.G),this.B=void 0):this.i=Number.MAX_VALUE}if(!c&&this.B&&"g"in this.B)throw Error('Unexpected "g" flag in sparse object of message that is not a group type.');if(s){t=S&&!f&&!0,c=this.i;var M;for(S=0;S<s.length;S++)f=s[S],f<c?(f+=this.G,(l=e[f])?Wr(l,t):e[f]=vt):(M||(M=Nr(this)),(l=M[f])?Wr(l,t):M[f]=vt)}}ae.prototype.toJSON=function(){return br(this.o,fi,pi)},ae.prototype.C=function(){return!!(X(this.o)&2)};function Wr(e,t){if(Array.isArray(e)){var s=X(e),l=1;!t||s&2||(l|=16),(s&l)!==l&&xe(e,s|l)}}ae.prototype.ja=pr,ae.prototype.toString=function(){return this.o.toString()};function Yr(e,t,s){if(s){var l={},c;for(c in s){var p=s[c],f=p.ra;f||(l.J=p.xa||p.oa.W,p.ia?(l.aa=en(p.ia),f=function(m){return function(S,T,P){return m.J(S,T,P,m.aa)}}(l)):p.ka?(l.Z=tn(p.da.P,p.ka),f=function(m){return function(S,T,P){return m.J(S,T,P,m.Z)}}(l)):f=l.J,p.ra=f),f(t,e,p.da),l={J:l.J,aa:l.aa,Z:l.Z}}}ci(t,e)}var Wt=Symbol();function Kr(e,t,s){return e[Wt]||(e[Wt]=function(l,c){return t(l,c,s)})}function Xr(e){var t=e[Wt];if(!t){var s=kr(e);t=function(l,c){return rn(l,c,s)},e[Wt]=t}return t}function hi(e){var t=e.ia;if(t)return Xr(t);if(t=e.wa)return Kr(e.da.P,t,e.ka)}function gi(e){var t=hi(e),s=e.da,l=e.oa.U;return t?function(c,p){return l(c,p,s,t)}:function(c,p){return l(c,p,s)}}function Zr(e,t){var s=e[t];return typeof s=="function"&&s.length===0&&(s=s(),e[t]=s),Array.isArray(s)&&(xt in s||bt in s||0<s.length&&typeof s[0]=="function")?s:void 0}function Qr(e,t,s,l,c,p){t.P=e[0];var f=1;if(e.length>f&&typeof e[f]!="number"){var m=e[f++];s(t,m)}for(;f<e.length;){s=e[f++];for(var S=f+1;S<e.length&&typeof e[S]!="number";)S++;switch(m=e[f++],S-=f,S){case 0:l(t,s,m);break;case 1:(S=Zr(e,f))?(f++,c(t,s,m,S)):l(t,s,m,e[f++]);break;case 2:S=f++,S=Zr(e,S),c(t,s,m,S,e[f++]);break;case 3:p(t,s,m,e[f++],e[f++],e[f++]);break;case 4:p(t,s,m,e[f++],e[f++],e[f++],e[f++]);break;default:throw Error("unexpected number of binary field arguments: "+S)}}return t}var Yt=Symbol();function en(e){var t=e[Yt];if(!t){var s=xr(e);t=function(l,c){return nn(l,c,s)},e[Yt]=t}return t}function tn(e,t){var s=e[Yt];return s||(s=function(l,c){return Yr(l,c,t)},e[Yt]=s),s}var bt=Symbol();function mi(e,t){e.push(t)}function vi(e,t,s){e.push(t,s.W)}function yi(e,t,s,l){var c=en(l),p=xr(l).P,f=s.W;e.push(t,function(m,S,T){return f(m,S,T,p,c)})}function bi(e,t,s,l,c,p){var f=tn(l,p),m=s.W;e.push(t,function(S,T,P){return m(S,T,P,l,f)})}function xr(e){var t=e[bt];return t||(t=Qr(e,e[bt]=[],mi,vi,yi,bi),xt in e&&bt in e&&(e.length=0),t)}var xt=Symbol();function xi(e,t){e[0]=t}function ki(e,t,s,l){var c=s.U;e[t]=l?function(p,f,m){return c(p,f,m,l)}:c}function Si(e,t,s,l,c){var p=s.U,f=Xr(l),m=kr(l).P;e[t]=function(S,T,P){return p(S,T,P,m,f,c)}}function Ci(e,t,s,l,c,p,f){var m=s.U,S=Kr(l,c,p);e[t]=function(T,P,O){return m(T,P,O,l,S,f)}}function kr(e){var t=e[xt];return t||(t=Qr(e,e[xt]={},xi,ki,Si,Ci),xt in e&&bt in e&&(e.length=0),t)}function rn(e,t,s){for(;zt(t)&&t.i!=4;){var l=t.l,c=s[l];if(!c){var p=s[0];p&&(p=p[l])&&(c=s[l]=gi(p))}if(!c||!c(t,e,l)){c=t,l=e,p=c.j,Qe(c);var f=c;if(!f.ca){if(c=f.h.h-p,f.h.h=p,f=f.h,c==0)c=ht();else{if(p=R(f,c),f.S&&f.m)c=f.i.subarray(p,p+c);else{f=f.i;var m=p;c=p+c,c=m===c?ct():It?f.slice(m,c):new Uint8Array(f.subarray(m,c))}c=c.length==0?ht():new Ge(c,ze)}(p=l.R)?p.push(c):l.R=[c]}}}return e}function nn(e,t,s){for(var l=s.length,c=l%2==1,p=c?1:0;p<l;p+=2)(0,s[p+1])(t,e,s[p]);Yr(e,t,c?s[0]:void 0)}function kt(e,t){return{U:e,W:t}}var ve=kt(function(e,t,s){if(e.i!==5)return!1;e=e.h;var l=e.i,c=e.h,p=l[c],f=l[c+1],m=l[c+2];return l=l[c+3],be(e,e.h+4),f=(p<<0|f<<8|m<<16|l<<24)>>>0,e=2*(f>>31)+1,p=f>>>23&255,f&=8388607,ke(t,s,p==255?f?NaN:1/0*e:p==0?e*Math.pow(2,-149)*f:e*Math.pow(2,p-150)*(f+Math.pow(2,23))),!0},function(e,t,s){if(t=vr(t,s),t!=null){fe(e.h,8*s+5),e=e.h;var l=+t;l===0?0<1/l?K=re=0:(re=0,K=2147483648):isNaN(l)?(re=0,K=2147483647):(l=(s=0>l?-2147483648:0)?-l:l,34028234663852886e22<l?(re=0,K=(s|2139095040)>>>0):11754943508222875e-54>l?(l=Math.round(l/Math.pow(2,-149)),re=0,K=(s|l)>>>0):(t=Math.floor(Math.log(l)/Math.LN2),l*=Math.pow(2,-t),l=Math.round(8388608*l),16777216<=l&&++t,re=0,K=(s|t+127<<23|l&8388607)>>>0)),s=K,e.h.push(s>>>0&255),e.h.push(s>>>8&255),e.h.push(s>>>16&255),e.h.push(s>>>24&255)}}),Ri=kt(function(e,t,s){if(e.i!==0)return!1;var l=e.h,c=0,p=e=0,f=l.i,m=l.h;do{var S=f[m++];c|=(S&127)<<p,p+=7}while(32>p&&S&128);for(32<p&&(e|=(S&127)>>4),p=3;32>p&&S&128;p+=7)S=f[m++],e|=(S&127)<<p;if(be(l,m),128>S)l=c>>>0,S=e>>>0,(e=S&2147483648)&&(l=~l+1>>>0,S=~S>>>0,l==0&&(S=S+1>>>0)),l=4294967296*S+(l>>>0);else throw ut();return ke(t,s,e?-l:l),!0},function(e,t,s){t=me(t,s),t!=null&&(typeof t=="string"&&Dt(t),t!=null&&(fe(e.h,8*s),typeof t=="number"?(e=e.h,_t(t),De(e,K,re)):(s=Dt(t),De(e.h,s.i,s.h))))}),Ti=kt(function(e,t,s){return e.i!==0?!1:(ke(t,s,Le(e.h)),!0)},function(e,t,s){if(t=me(t,s),t!=null&&t!=null)if(fe(e.h,8*s),e=e.h,s=t,0<=s)fe(e,s);else{for(t=0;9>t;t++)e.h.push(s&127|128),s>>=7;e.h.push(1)}}),sn=kt(function(e,t,s){if(e.i!==2)return!1;var l=Le(e.h)>>>0;e=e.h;var c=R(e,l);if(e=e.i,ur){var p=e,f;(f=dt)||(f=dt=new TextDecoder("utf-8",{fatal:!0})),e=c+l,p=c===0&&e===p.length?p:p.subarray(c,e);try{var m=f.decode(p)}catch(O){if(Xe===void 0){try{f.decode(new Uint8Array([128]))}catch{}try{f.decode(new Uint8Array([97])),Xe=!0}catch{Xe=!1}}throw!Xe&&(dt=void 0),O}}else{m=c,l=m+l,c=[];for(var S=null,T,P;m<l;)T=e[m++],128>T?c.push(T):224>T?m>=l?je():(P=e[m++],194>T||(P&192)!==128?(m--,je()):c.push((T&31)<<6|P&63)):240>T?m>=l-1?je():(P=e[m++],(P&192)!==128||T===224&&160>P||T===237&&160<=P||((p=e[m++])&192)!==128?(m--,je()):c.push((T&15)<<12|(P&63)<<6|p&63)):244>=T?m>=l-2?je():(P=e[m++],(P&192)!==128||(T<<28)+(P-144)>>30!==0||((p=e[m++])&192)!==128||((f=e[m++])&192)!==128?(m--,je()):(T=(T&7)<<18|(P&63)<<12|(p&63)<<6|f&63,T-=65536,c.push((T>>10&1023)+55296,(T&1023)+56320))):je(),8192<=c.length&&(S=Ut(S,c),c.length=0);m=Ut(S,c)}return ke(t,s,m),!0},function(e,t,s){if(t=me(t,s),t!=null){var l=!1;if(l=l===void 0?!1:l,dr){if(l&&/(?:[^\uD800-\uDBFF]|^)[\uDC00-\uDFFF]|[\uD800-\uDBFF](?![\uDC00-\uDFFF])/.test(t))throw Error("Found an unpaired surrogate");t=(ft||(ft=new TextEncoder)).encode(t)}else{for(var c=0,p=new Uint8Array(3*t.length),f=0;f<t.length;f++){var m=t.charCodeAt(f);if(128>m)p[c++]=m;else{if(2048>m)p[c++]=m>>6|192;else{if(55296<=m&&57343>=m){if(56319>=m&&f<t.length){var S=t.charCodeAt(++f);if(56320<=S&&57343>=S){m=1024*(m-55296)+S-56320+65536,p[c++]=m>>18|240,p[c++]=m>>12&63|128,p[c++]=m>>6&63|128,p[c++]=m&63|128;continue}else f--}if(l)throw Error("Found an unpaired surrogate");m=65533}p[c++]=m>>12|224,p[c++]=m>>6&63|128}p[c++]=m&63|128}}t=c===p.length?p:p.subarray(0,c)}fe(e.h,8*s+2),fe(e.h,t.length),pe(e,e.h.end()),pe(e,t)}}),on=kt(function(e,t,s,l,c){if(e.i!==2)return!1;t=Vr(t,s,l),s=e.h.j,l=Le(e.h)>>>0;var p=e.h.h+l,f=p-s;if(0>=f&&(e.h.j=p,c(t,e,void 0,void 0,void 0),f=p-e.h.h),f)throw Error("Message parsing ended unexpectedly. Expected to read "+(l+" bytes, instead read "+(l-f)+" bytes, either the data ended unexpectedly or the message misreported its own length"));return e.h.h=p,e.h.j=s,!0},function(e,t,s,l,c){if(t=yr(t,l,s),t!=null)for(l=0;l<t.length;l++){var p=e;fe(p.h,8*s+2);var f=p.h.end();pe(p,f),f.push(p.i),p=f,c(t[l],e),f=e;var m=p.pop();for(m=f.i+f.h.length()-m;127<m;)p.push(m&127|128),m>>>=7,f.i++;p.push(m),f.i++}});function Sr(e){return function(t,s){e:{if(I.length){var l=I.pop();l.setOptions(s),gt(l.h,t,s),t=l}else t=new mt(t,s);try{var c=kr(e),p=rn(new c.P,t,c);break e}finally{c=t.h,c.i=null,c.m=!1,c.l=0,c.j=0,c.h=0,c.S=!1,t.l=-1,t.i=-1,100>I.length&&I.push(t)}p=void 0}return p}}function Cr(e){return function(){var t=new de;nn(this,t,xr(e)),pe(t,t.h.end());for(var s=new Uint8Array(t.i),l=t.j,c=l.length,p=0,f=0;f<c;f++){var m=l[f];s.set(m,p),p+=m.length}return t.j=[s],s}}function et(e){ae.call(this,e)}oe(et,ae);var an=[et,1,Ti,2,ve,3,sn,4,sn];et.prototype.l=Cr(an);function Rr(e){ae.call(this,e,-1,Pi)}oe(Rr,ae),Rr.prototype.addClassification=function(e,t){return Vr(this,1,et,e,t),this};var Pi=[1],Ei=Sr([Rr,1,on,an]);function St(e){ae.call(this,e)}oe(St,ae);var ln=[St,1,ve,2,ve,3,ve,4,ve,5,ve];St.prototype.l=Cr(ln);function cn(e){ae.call(this,e,-1,ji)}oe(cn,ae);var ji=[1],Mi=Sr([cn,1,on,ln]);function Kt(e){ae.call(this,e)}oe(Kt,ae);var un=[Kt,1,ve,2,ve,3,ve,4,ve,5,ve,6,Ri],Ai=Sr(un);Kt.prototype.l=Cr(un);function dn(e,t,s){if(s=e.createShader(s===0?e.VERTEX_SHADER:e.FRAGMENT_SHADER),e.shaderSource(s,t),e.compileShader(s),!e.getShaderParameter(s,e.COMPILE_STATUS))throw Error(`Could not compile WebGL shader.

`+e.getShaderInfoLog(s));return s}function Ii(e){return yr(e,et,1).map(function(t){var s=me(t,1);return{index:s??0,qa:Ce(t,2),label:me(t,3)!=null?Ht(me(t,3),""):void 0,displayName:me(t,4)!=null?Ht(me(t,4),""):void 0}})}function _i(e){return{x:Ce(e,1),y:Ce(e,2),z:Ce(e,3),visibility:vr(e,4)!=null?Ce(e,4):void 0}}function Tr(e,t){this.i=e,this.h=t,this.m=0}function fn(e,t,s){return Oi(e,t),typeof e.h.canvas.transferToImageBitmap=="function"?Promise.resolve(e.h.canvas.transferToImageBitmap()):s?Promise.resolve(e.h.canvas):typeof createImageBitmap=="function"?createImageBitmap(e.h.canvas):(e.j===void 0&&(e.j=document.createElement("canvas")),new Promise(function(l){e.j.height=e.h.canvas.height,e.j.width=e.h.canvas.width,e.j.getContext("2d",{}).drawImage(e.h.canvas,0,0,e.h.canvas.width,e.h.canvas.height),l(e.j)}))}function Oi(e,t){var s=e.h;if(e.s===void 0){var l=dn(s,`
  attribute vec2 aVertex;
  attribute vec2 aTex;
  varying vec2 vTex;
  void main(void) {
    gl_Position = vec4(aVertex, 0.0, 1.0);
    vTex = aTex;
  }`,0),c=dn(s,`
  precision mediump float;
  varying vec2 vTex;
  uniform sampler2D sampler0;
  void main(){
    gl_FragColor = texture2D(sampler0, vTex);
  }`,1),p=s.createProgram();if(s.attachShader(p,l),s.attachShader(p,c),s.linkProgram(p),!s.getProgramParameter(p,s.LINK_STATUS))throw Error(`Could not compile WebGL program.

`+s.getProgramInfoLog(p));l=e.s=p,s.useProgram(l),c=s.getUniformLocation(l,"sampler0"),e.l={O:s.getAttribLocation(l,"aVertex"),N:s.getAttribLocation(l,"aTex"),ya:c},e.v=s.createBuffer(),s.bindBuffer(s.ARRAY_BUFFER,e.v),s.enableVertexAttribArray(e.l.O),s.vertexAttribPointer(e.l.O,2,s.FLOAT,!1,0,0),s.bufferData(s.ARRAY_BUFFER,new Float32Array([-1,-1,-1,1,1,1,1,-1]),s.STATIC_DRAW),s.bindBuffer(s.ARRAY_BUFFER,null),e.u=s.createBuffer(),s.bindBuffer(s.ARRAY_BUFFER,e.u),s.enableVertexAttribArray(e.l.N),s.vertexAttribPointer(e.l.N,2,s.FLOAT,!1,0,0),s.bufferData(s.ARRAY_BUFFER,new Float32Array([0,1,0,0,1,0,1,1]),s.STATIC_DRAW),s.bindBuffer(s.ARRAY_BUFFER,null),s.uniform1i(c,0)}l=e.l,s.useProgram(e.s),s.canvas.width=t.width,s.canvas.height=t.height,s.viewport(0,0,t.width,t.height),s.activeTexture(s.TEXTURE0),e.i.bindTexture2d(t.glName),s.enableVertexAttribArray(l.O),s.bindBuffer(s.ARRAY_BUFFER,e.v),s.vertexAttribPointer(l.O,2,s.FLOAT,!1,0,0),s.enableVertexAttribArray(l.N),s.bindBuffer(s.ARRAY_BUFFER,e.u),s.vertexAttribPointer(l.N,2,s.FLOAT,!1,0,0),s.bindFramebuffer(s.DRAW_FRAMEBUFFER?s.DRAW_FRAMEBUFFER:s.FRAMEBUFFER,null),s.clearColor(0,0,0,0),s.clear(s.COLOR_BUFFER_BIT),s.colorMask(!0,!0,!0,!0),s.drawArrays(s.TRIANGLE_FAN,0,4),s.disableVertexAttribArray(l.O),s.disableVertexAttribArray(l.N),s.bindBuffer(s.ARRAY_BUFFER,null),e.i.bindTexture2d(0)}function Li(e){this.h=e}var Di=new Uint8Array([0,97,115,109,1,0,0,0,1,4,1,96,0,0,3,2,1,0,10,9,1,7,0,65,0,253,15,26,11]);function $i(e,t){return t+e}function pn(e,t){window[e]=t}function wi(e){var t=document.createElement("script");return t.setAttribute("src",e),t.setAttribute("crossorigin","anonymous"),new Promise(function(s){t.addEventListener("load",function(){s()},!1),t.addEventListener("error",function(){s()},!1),document.body.appendChild(t)})}function Fi(){return V(function(e){switch(e.h){case 1:return e.s=2,q(e,WebAssembly.instantiate(Di),4);case 4:e.h=3,e.s=0;break;case 2:return e.s=0,e.l=null,e.return(!1);case 3:return e.return(!0)}})}function Pr(e){if(this.h=e,this.listeners={},this.l={},this.L={},this.s={},this.v={},this.M=this.u=this.ga=!0,this.I=Promise.resolve(),this.fa="",this.D={},this.locateFile=e&&e.locateFile||$i,typeof window=="object")var t=window.location.pathname.toString().substring(0,window.location.pathname.toString().lastIndexOf("/"))+"/";else if(typeof location<"u")t=location.pathname.toString().substring(0,location.pathname.toString().lastIndexOf("/"))+"/";else throw Error("solutions can only be loaded on a web page or in a web worker");if(this.ha=t,e.options){t=h(Object.keys(e.options));for(var s=t.next();!s.done;s=t.next()){s=s.value;var l=e.options[s].default;l!==void 0&&(this.l[s]=typeof l=="function"?l():l)}}}i=Pr.prototype,i.close=function(){return this.j&&this.j.delete(),Promise.resolve()};function Ui(e){var t,s,l,c,p,f,m,S,T,P,O;return V(function(_){switch(_.h){case 1:return e.ga?(t=e.h.files===void 0?[]:typeof e.h.files=="function"?e.h.files(e.l):e.h.files,q(_,Fi(),2)):_.return();case 2:if(s=_.i,typeof window=="object")return pn("createMediapipeSolutionsWasm",{locateFile:e.locateFile}),pn("createMediapipeSolutionsPackedAssets",{locateFile:e.locateFile}),f=t.filter(function(M){return M.data!==void 0}),m=t.filter(function(M){return M.data===void 0}),S=Promise.all(f.map(function(M){var L=Xt(e,M.url);if(M.path!==void 0){var U=M.path;L=L.then(function(G){return e.overrideFile(U,G),Promise.resolve(G)})}return L})),T=Promise.all(m.map(function(M){return M.simd===void 0||M.simd&&s||!M.simd&&!s?wi(e.locateFile(M.url,e.ha)):Promise.resolve()})).then(function(){var M,L,U;return V(function(G){if(G.h==1)return M=window.createMediapipeSolutionsWasm,L=window.createMediapipeSolutionsPackedAssets,U=e,q(G,M(L),2);U.i=G.i,G.h=0})}),P=function(){return V(function(M){return e.h.graph&&e.h.graph.url?M=q(M,Xt(e,e.h.graph.url),0):(M.h=0,M=void 0),M})}(),q(_,Promise.all([T,S,P]),7);if(typeof importScripts!="function")throw Error("solutions can only be loaded on a web page or in a web worker");return l=t.filter(function(M){return M.simd===void 0||M.simd&&s||!M.simd&&!s}).map(function(M){return e.locateFile(M.url,e.ha)}),importScripts.apply(null,k(l)),c=e,q(_,createMediapipeSolutionsWasm(Module),6);case 6:c.i=_.i,e.m=new OffscreenCanvas(1,1),e.i.canvas=e.m,p=e.i.GL.createContext(e.m,{antialias:!1,alpha:!1,va:typeof WebGL2RenderingContext<"u"?2:1}),e.i.GL.makeContextCurrent(p),_.h=4;break;case 7:if(e.m=document.createElement("canvas"),O=e.m.getContext("webgl2",{}),!O&&(O=e.m.getContext("webgl",{}),!O))return alert("Failed to create WebGL canvas context when passing video frame."),_.return();e.K=O,e.i.canvas=e.m,e.i.createContext(e.m,!0,!0,{});case 4:e.j=new e.i.SolutionWasm,e.ga=!1,_.h=0}})}function Ni(e){var t,s,l,c,p,f,m,S;return V(function(T){if(T.h==1){if(e.h.graph&&e.h.graph.url&&e.fa===e.h.graph.url)return T.return();if(e.u=!0,!e.h.graph||!e.h.graph.url){T.h=2;return}return e.fa=e.h.graph.url,q(T,Xt(e,e.h.graph.url),3)}for(T.h!=2&&(t=T.i,e.j.loadGraph(t)),s=h(Object.keys(e.D)),l=s.next();!l.done;l=s.next())c=l.value,e.j.overrideFile(c,e.D[c]);if(e.D={},e.h.listeners)for(p=h(e.h.listeners),f=p.next();!f.done;f=p.next())m=f.value,Gi(e,m);S=e.l,e.l={},e.setOptions(S),T.h=0})}i.reset=function(){var e=this;return V(function(t){e.j&&(e.j.reset(),e.s={},e.v={}),t.h=0})},i.setOptions=function(e,t){var s=this;if(t=t||this.h.options){for(var l=[],c=[],p={},f=h(Object.keys(e)),m=f.next();!m.done;p={X:p.X,Y:p.Y},m=f.next())if(m=m.value,!(m in this.l&&this.l[m]===e[m])){this.l[m]=e[m];var S=t[m];S!==void 0&&(S.onChange&&(p.X=S.onChange,p.Y=e[m],l.push(function(T){return function(){var P;return V(function(O){if(O.h==1)return q(O,T.X(T.Y),2);P=O.i,P===!0&&(s.u=!0),O.h=0})}}(p))),S.graphOptionXref&&(m=Object.assign({},{calculatorName:"",calculatorIndex:0},S.graphOptionXref,{valueNumber:S.type===1?e[m]:0,valueBoolean:S.type===0?e[m]:!1,valueString:S.type===2?e[m]:""}),c.push(m)))}(l.length!==0||c.length!==0)&&(this.u=!0,this.H=(this.H===void 0?[]:this.H).concat(c),this.F=(this.F===void 0?[]:this.F).concat(l))}};function Bi(e){var t,s,l,c,p,f,m;return V(function(S){switch(S.h){case 1:if(!e.u)return S.return();if(!e.F){S.h=2;break}t=h(e.F),s=t.next();case 3:if(s.done){S.h=5;break}return l=s.value,q(S,l(),4);case 4:s=t.next(),S.h=3;break;case 5:e.F=void 0;case 2:if(e.H){for(c=new e.i.GraphOptionChangeRequestList,p=h(e.H),f=p.next();!f.done;f=p.next())m=f.value,c.push_back(m);e.j.changeOptions(c),c.delete(),e.H=void 0}e.u=!1,S.h=0}})}i.initialize=function(){var e=this;return V(function(t){return t.h==1?q(t,Ui(e),2):t.h!=3?q(t,Ni(e),3):q(t,Bi(e),0)})};function Xt(e,t){var s,l;return V(function(c){return t in e.L?c.return(e.L[t]):(s=e.locateFile(t,""),l=fetch(s).then(function(p){return p.arrayBuffer()}),e.L[t]=l,c.return(l))})}i.overrideFile=function(e,t){this.j?this.j.overrideFile(e,t):this.D[e]=t},i.clearOverriddenFiles=function(){this.D={},this.j&&this.j.clearOverriddenFiles()},i.send=function(e,t){var s=this,l,c,p,f,m,S,T,P,O;return V(function(_){switch(_.h){case 1:return s.h.inputs?(l=1e3*(t??performance.now()),q(_,s.I,2)):_.return();case 2:return q(_,s.initialize(),3);case 3:for(c=new s.i.PacketDataList,p=h(Object.keys(e)),f=p.next();!f.done;f=p.next())if(m=f.value,S=s.h.inputs[m]){e:{var M=e[m];switch(S.type){case"video":var L=s.s[S.stream];if(L||(L=new Tr(s.i,s.K),s.s[S.stream]=L),L.m===0&&(L.m=L.i.createTexture()),typeof HTMLVideoElement<"u"&&M instanceof HTMLVideoElement)var U=M.videoWidth,G=M.videoHeight;else typeof HTMLImageElement<"u"&&M instanceof HTMLImageElement?(U=M.naturalWidth,G=M.naturalHeight):(U=M.width,G=M.height);G={glName:L.m,width:U,height:G},U=L.h,U.canvas.width=G.width,U.canvas.height=G.height,U.activeTexture(U.TEXTURE0),L.i.bindTexture2d(L.m),U.texImage2D(U.TEXTURE_2D,0,U.RGBA,U.RGBA,U.UNSIGNED_BYTE,M),L.i.bindTexture2d(0),L=G;break e;case"detections":for(L=s.s[S.stream],L||(L=new Li(s.i),s.s[S.stream]=L),L.data||(L.data=new L.h.DetectionListData),L.data.reset(M.length),G=0;G<M.length;++G){U=M[G];var z=L.data,ne=z.setBoundingBox,he=G,le=U.la,B=new Kt;if(Se(B,1,le.sa),Se(B,2,le.ta),Se(B,3,le.height),Se(B,4,le.width),Se(B,5,le.rotation),ke(B,6,le.pa),le=B.l(),ne.call(z,he,le),U.ea)for(z=0;z<U.ea.length;++z){B=U.ea[z],ne=L.data,he=ne.addNormalizedLandmark,le=G,B=Object.assign({},B,{visibility:B.visibility?B.visibility:0});var ie=new St;Se(ie,1,B.x),Se(ie,2,B.y),Se(ie,3,B.z),B.visibility&&Se(ie,4,B.visibility),B=ie.l(),he.call(ne,le,B)}if(U.ba)for(z=0;z<U.ba.length;++z)ne=L.data,he=ne.addClassification,le=G,B=U.ba[z],ie=new et,Se(ie,2,B.qa),B.index&&ke(ie,1,B.index),B.label&&ke(ie,3,B.label),B.displayName&&ke(ie,4,B.displayName),B=ie.l(),he.call(ne,le,B)}L=L.data;break e;default:L={}}}switch(T=L,P=S.stream,S.type){case"video":c.pushTexture2d(Object.assign({},T,{stream:P,timestamp:l}));break;case"detections":O=T,O.stream=P,O.timestamp=l,c.pushDetectionList(O);break;default:throw Error("Unknown input config type: '"+S.type+"'")}}return s.j.send(c),q(_,s.I,4);case 4:c.delete(),_.h=0}})};function Vi(e,t,s){var l,c,p,f,m,S,T,P,O,_,M,L,U,G;return V(function(z){switch(z.h){case 1:if(!s)return z.return(t);for(l={},c=0,p=h(Object.keys(s)),f=p.next();!f.done;f=p.next())m=f.value,S=s[m],typeof S!="string"&&S.type==="texture"&&t[S.stream]!==void 0&&++c;1<c&&(e.M=!1),T=h(Object.keys(s)),f=T.next();case 2:if(f.done){z.h=4;break}if(P=f.value,O=s[P],typeof O=="string")return U=l,G=P,q(z,zi(e,P,t[O]),14);if(_=t[O.stream],O.type==="detection_list"){if(_){for(var ne=_.getRectList(),he=_.getLandmarksList(),le=_.getClassificationsList(),B=[],ie=0;ie<ne.size();++ie){var $e=Ai(ne.get(ie)),Ji=Ce($e,1),Hi=Ce($e,2),qi=Ce($e,3),Wi=Ce($e,4),Yi=Ce($e,5,0),Zt=void 0;Zt=Zt===void 0?0:Zt,$e={la:{sa:Ji,ta:Hi,height:qi,width:Wi,rotation:Yi,pa:Ht(me($e,6),Zt)},ea:yr(Mi(he.get(ie)),St,1).map(_i),ba:Ii(Ei(le.get(ie)))},B.push($e)}ne=B}else ne=[];l[P]=ne,z.h=7;break}if(O.type==="proto_list"){if(_){for(ne=Array(_.size()),he=0;he<_.size();he++)ne[he]=_.get(he);_.delete()}else ne=[];l[P]=ne,z.h=7;break}if(_===void 0){z.h=3;break}if(O.type==="float_list"){l[P]=_,z.h=7;break}if(O.type==="proto"){l[P]=_,z.h=7;break}if(O.type!=="texture")throw Error("Unknown output config type: '"+O.type+"'");return M=e.v[P],M||(M=new Tr(e.i,e.K),e.v[P]=M),q(z,fn(M,_,e.M),13);case 13:L=z.i,l[P]=L;case 7:O.transform&&l[P]&&(l[P]=O.transform(l[P])),z.h=3;break;case 14:U[G]=z.i;case 3:f=T.next(),z.h=2;break;case 4:return z.return(l)}})}function zi(e,t,s){var l;return V(function(c){return typeof s=="number"||s instanceof Uint8Array||s instanceof e.i.Uint8BlobList?c.return(s):s instanceof e.i.Texture2dDataOut?(l=e.v[t],l||(l=new Tr(e.i,e.K),e.v[t]=l),c.return(fn(l,s,e.M))):c.return(void 0)})}function Gi(e,t){for(var s=t.name||"$",l=[].concat(k(t.wants)),c=new e.i.StringList,p=h(t.wants),f=p.next();!f.done;f=p.next())c.push_back(f.value);p=e.i.PacketListener.implement({onResults:function(m){for(var S={},T=0;T<t.wants.length;++T)S[l[T]]=m.get(T);var P=e.listeners[s];P&&(e.I=Vi(e,S,t.outs).then(function(O){O=P(O);for(var _=0;_<t.wants.length;++_){var M=S[l[_]];typeof M=="object"&&M.hasOwnProperty&&M.hasOwnProperty("delete")&&M.delete()}O&&(e.I=O)}))}}),e.j.attachMultiListener(c,p),c.delete()}i.onResults=function(e,t){this.listeners[t||"$"]=e},ge("Solution",Pr),ge("OptionType",{BOOL:0,NUMBER:1,ua:2,0:"BOOL",1:"NUMBER",2:"STRING"});function hn(e){switch(e===void 0&&(e=0),e){case 1:return"selfie_segmentation_landscape.tflite";default:return"selfie_segmentation.tflite"}}function gn(e){var t=this;e=e||{},this.h=new Pr({locateFile:e.locateFile,files:function(s){return[{simd:!0,url:"selfie_segmentation_solution_simd_wasm_bin.js"},{simd:!1,url:"selfie_segmentation_solution_wasm_bin.js"},{data:!0,url:hn(s.modelSelection)}]},graph:{url:"selfie_segmentation.binarypb"},listeners:[{wants:["segmentation_mask","image_transformed"],outs:{image:{type:"texture",stream:"image_transformed"},segmentationMask:{type:"texture",stream:"segmentation_mask"}}}],inputs:{image:{type:"video",stream:"input_frames_gpu"}},options:{useCpuInference:{type:0,graphOptionXref:{calculatorType:"InferenceCalculator",fieldName:"use_cpu_inference"},default:typeof window!="object"||window.navigator===void 0?!1:"iPad Simulator;iPhone Simulator;iPod Simulator;iPad;iPhone;iPod".split(";").includes(navigator.platform)||navigator.userAgent.includes("Mac")&&"ontouchend"in document},selfieMode:{type:0,graphOptionXref:{calculatorType:"GlScalerCalculator",calculatorIndex:1,fieldName:"flip_horizontal"}},modelSelection:{type:1,graphOptionXref:{calculatorType:"ConstantSidePacketCalculator",calculatorName:"ConstantSidePacketCalculatorModelSelection",fieldName:"int_value"},onChange:function(s){var l,c,p;return V(function(f){return f.h==1?(l=hn(s),c="third_party/mediapipe/modules/selfie_segmentation/"+l,q(f,Xt(t.h,l),2)):(p=f.i,t.h.overrideFile(c,p),f.return(!0))})}}}})}i=gn.prototype,i.close=function(){return this.h.close(),Promise.resolve()},i.onResults=function(e){this.h.onResults(e)},i.initialize=function(){var e=this;return V(function(t){return q(t,e.h.initialize(),0)})},i.reset=function(){this.h.reset()},i.send=function(e){var t=this;return V(function(s){return q(s,t.h.send(e),0)})},i.setOptions=function(e){this.h.setOptions(e)},ge("SelfieSegmentation",gn),ge("VERSION","0.1.1675465747")}).call(Mr)),Mr}var Ms=js();class As{constructor(){this.canvas=document.createElement("canvas"),this.ctx=this.canvas.getContext("2d"),this.backgroundImage=null,this.isBlur=!1,this.processingStream=null,this.videoProcessor=null,this.selfieSegmentation=null}async initialize(o){if(!o)return null;const n=document.createElement("video");return n.autoplay=!0,n.muted=!0,n.srcObject=new MediaStream([o]),await new Promise(r=>{n.onloadedmetadata=()=>{n.play(),r()}}),this.canvas.width=n.videoWidth,this.canvas.height=n.videoHeight,this.selfieSegmentation=new Ms.SelfieSegmentation({locateFile:r=>`https://cdn.jsdelivr.net/npm/@mediapipe/selfie_segmentation/${r}`}),this.selfieSegmentation.setOptions({modelSelection:1,selfieMode:!0}),this.processingStream=this.canvas.captureStream(30),this.videoProcessor=setInterval(()=>{this.processFrame(n)},1e3/30),this.processingStream.getVideoTracks()[0]}setBackground(o){if(o==="blur"){this.isBlur=!0,this.backgroundImage=null;return}if(this.isBlur=!1,!o){this.backgroundImage=null;return}const n=new Image;n.crossOrigin="anonymous",n.src=o,n.onload=()=>{this.backgroundImage=n}}async processFrame(o){if(!this.ctx||!o||!this.selfieSegmentation)return;const n=await this.selfieSegmentation.send({image:o});n.segmentationMask&&(this.isBlur?(this.ctx.filter="blur(10px)",this.ctx.drawImage(o,0,0,this.canvas.width,this.canvas.height),this.ctx.filter="none"):this.backgroundImage&&this.ctx.drawImage(this.backgroundImage,0,0,this.canvas.width,this.canvas.height),this.ctx.globalCompositeOperation="source-over",this.ctx.drawImage(n.segmentationMask,0,0,this.canvas.width,this.canvas.height))}stop(){this.videoProcessor&&(clearInterval(this.videoProcessor),this.videoProcessor=null),this.processingStream&&(this.processingStream.getTracks().forEach(o=>o.stop()),this.processingStream=null),this.selfieSegmentation&&(this.selfieSegmentation.close(),this.selfieSegmentation=null)}}const Is={key:0,class:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50"},_s={class:"bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6 max-w-lg w-full mx-4"},Os={class:"flex items-center justify-between mb-4"},Ls={class:"space-y-4"},Ds={class:"flex items-center"},$s=["value"],ws={class:"bg-gray-50 dark:bg-gray-700 rounded-lg p-3"},Fs={class:"space-y-1 text-xs text-gray-600 dark:text-gray-400"},Us={key:0},Ns={class:"bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-3"},Bs={class:"flex items-start"},Vs={class:"text-xs text-blue-700 dark:text-blue-300"},zs={class:"flex justify-between items-center mt-6"},Gs={class:"flex space-x-2"},Js={__name:"MeetingInvitePopup",props:{visible:Boolean,inviteLink:String,meetingId:String,meetingName:String,hostName:String,linkSharingPolicy:{type:String,default:"host"}},emits:["close","linkShared"],setup(i,{emit:o}){const n=i,r=o,a=H(!1),u=Z(()=>`Join me in "${n.meetingName||`Meeting ${n.meetingId}`}" on TheMeet!

Meeting Link: ${n.inviteLink}
Meeting ID: ${n.meetingId}

Hosted by: ${n.hostName}`);function v(){navigator.clipboard.writeText(n.inviteLink).then(()=>{a.value=!0,setTimeout(()=>a.value=!1,2e3),b("copy")}).catch(g=>{console.error("Failed to copy link:",g);const y=document.createElement("textarea");y.value=n.inviteLink,document.body.appendChild(y),y.select(),document.execCommand("copy"),document.body.removeChild(y),a.value=!0,setTimeout(()=>a.value=!1,2e3),b("copy")})}function h(){const g=encodeURIComponent(`Join me in "${n.meetingName||"TheMeet Meeting"}"`),y=encodeURIComponent(u.value);window.open(`mailto:?subject=${g}&body=${y}`),b("email")}function k(){const g=encodeURIComponent(u.value);window.open(`https://wa.me/?text=${g}`),b("whatsapp")}function b(g="manual"){r("linkShared",{method:g,timestamp:new Date().toISOString(),link:n.inviteLink})}return jt(()=>n.visible,g=>{g||(a.value=!1)}),(g,y)=>i.visible?(E(),j("div",Is,[d("div",_s,[d("div",Os,[y[3]||(y[3]=d("h2",{class:"text-xl font-semibold text-gray-900 dark:text-white"},[d("i",{class:"fas fa-share-alt mr-2 text-primary"}),J(" Invite Participants ")],-1)),d("button",{onClick:y[0]||(y[0]=C=>g.$emit("close")),class:"text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"},y[2]||(y[2]=[d("i",{class:"fas fa-times text-lg"},null,-1)]))]),d("div",Ls,[d("div",null,[y[4]||(y[4]=d("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"},[d("i",{class:"fas fa-link mr-1"}),J(" Meeting Link ")],-1)),d("div",Ds,[d("input",{value:i.inviteLink,readonly:"",class:"flex-1 p-3 border rounded-l-lg bg-gray-50 dark:bg-gray-700 text-gray-900 dark:text-white text-sm font-mono",id:"invite-link-input"},null,8,$s),d("button",{onClick:v,class:W(["px-4 py-3 bg-primary text-white rounded-r-lg hover:bg-primary-dark transition-colors flex items-center",{"bg-green-500 hover:bg-green-600":a.value}])},[d("i",{class:W([a.value?"fas fa-check":"fas fa-copy","mr-1"])},null,2),J(" "+w(a.value?"Copied!":"Copy"),1)],2)])]),d("div",null,[y[7]||(y[7]=d("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"},[d("i",{class:"fas fa-share mr-1"}),J(" Quick Share ")],-1)),d("div",{class:"grid grid-cols-2 gap-2"},[d("button",{onClick:h,class:"flex items-center justify-center p-3 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"},y[5]||(y[5]=[d("i",{class:"fas fa-envelope mr-2 text-blue-500"},null,-1),d("span",{class:"text-sm"},"Email",-1)])),d("button",{onClick:k,class:"flex items-center justify-center p-3 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"},y[6]||(y[6]=[d("i",{class:"fab fa-whatsapp mr-2 text-green-500"},null,-1),d("span",{class:"text-sm"},"WhatsApp",-1)]))])]),d("div",ws,[y[11]||(y[11]=d("h3",{class:"text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"},"Meeting Information",-1)),d("div",Fs,[d("div",null,[y[8]||(y[8]=d("strong",null,"Meeting ID:",-1)),J(" "+w(i.meetingId),1)]),d("div",null,[y[9]||(y[9]=d("strong",null,"Host:",-1)),J(" "+w(i.hostName),1)]),i.meetingName?(E(),j("div",Us,[y[10]||(y[10]=d("strong",null,"Title:",-1)),J(" "+w(i.meetingName),1)])):N("",!0)])]),d("div",Ns,[d("div",Bs,[y[13]||(y[13]=d("i",{class:"fas fa-shield-alt text-blue-500 mr-2 mt-0.5"},null,-1)),d("div",Vs,[y[12]||(y[12]=d("strong",null,"Security Notice:",-1)),J(" External users joining via this link will be prompted to enter their name before joining the meeting. "+w(i.linkSharingPolicy==="host"?"Only you can share this link.":"All participants can share this link."),1)])])])]),d("div",zs,[d("button",{onClick:b,class:"text-sm text-primary hover:text-primary-dark flex items-center"},y[14]||(y[14]=[d("i",{class:"fas fa-chart-line mr-1"},null,-1),J(" Mark as Shared ")])),d("div",Gs,[d("button",{onClick:y[1]||(y[1]=C=>g.$emit("close")),class:"px-4 py-2 bg-gray-200 dark:bg-gray-700 text-gray-800 dark:text-gray-200 rounded-lg hover:bg-gray-300 dark:hover:bg-gray-600 transition-colors"}," Close ")])])])])):N("",!0)}},Hs={key:0,class:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50"},qs={class:"bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6 max-w-md w-full mx-4"},Ws={key:0,class:"bg-gray-50 dark:bg-gray-700 rounded-lg p-3 mb-4"},Ys={class:"text-center"},Ks={class:"font-medium text-gray-900 dark:text-white"},Xs={class:"text-xs text-gray-600 dark:text-gray-400 mt-1"},Zs={class:"flex items-center justify-center mt-2 text-xs text-gray-500 dark:text-gray-400"},Qs=["disabled"],eo={class:"flex justify-end space-x-3 pt-2"},to=["disabled"],ro=["disabled"],no={key:0,class:"fas fa-spinner fa-spin mr-2"},io={key:1,class:"fas fa-sign-in-alt mr-2"},so={__name:"UsernamePromptPopup",props:{visible:Boolean,meetingInfo:{type:Object,default:()=>({})},sharedBy:String,joinViaLink:Boolean},emits:["submit","cancel"],setup(i,{emit:o}){const n=i,r=o,a=H(""),u=H(!1);function v(){a.value.trim()&&!u.value&&(u.value=!0,r("submit",{username:a.value.trim(),joinMethod:"shared_link",sharedBy:n.sharedBy,timestamp:new Date().toISOString()}),setTimeout(()=>{a.value="",u.value=!1},1e3))}return jt(()=>n.visible,h=>{h?setTimeout(()=>{const k=document.getElementById("username");k&&k.focus()},100):(a.value="",u.value=!1)}),ar(()=>{const h=localStorage.getItem("userName");h&&!a.value&&(a.value=h)}),(h,k)=>i.visible?(E(),j("div",Hs,[d("div",qs,[k[6]||(k[6]=Ar('<div class="text-center mb-6"><div class="w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-4"><i class="fas fa-user-plus text-2xl text-primary"></i></div><h2 class="text-xl font-semibold text-gray-900 dark:text-white mb-2">Join Meeting</h2><p class="text-sm text-gray-600 dark:text-gray-400"> You&#39;re joining via a shared link. Please enter your name to continue. </p></div>',1)),i.meetingInfo?(E(),j("div",Ws,[d("div",Ys,[d("h3",Ks,w(i.meetingInfo.name||"TheMeet Meeting"),1),d("p",Xs," Hosted by "+w(i.meetingInfo.hostName||"Host"),1),d("div",Zs,[k[2]||(k[2]=d("i",{class:"fas fa-users mr-1"},null,-1)),J(" "+w(i.meetingInfo.participantCount||0)+" participant"+w((i.meetingInfo.participantCount||0)!==1?"s":""),1)])])])):N("",!0),d("form",{onSubmit:Pn(v,["prevent"]),class:"space-y-4"},[d("div",null,[k[3]||(k[3]=d("label",{for:"username",class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"},[J(" Your Name "),d("span",{class:"text-red-500"},"*")],-1)),Tt(d("input",{id:"username","onUpdate:modelValue":k[0]||(k[0]=b=>a.value=b),type:"text",class:"w-full p-3 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:ring-2 focus:ring-primary focus:border-transparent",placeholder:"Enter your full name",maxlength:"50",required:"",autocomplete:"name",disabled:u.value},null,8,Qs),[[tr,a.value]]),k[4]||(k[4]=d("p",{class:"text-xs text-gray-500 dark:text-gray-400 mt-1"}," This name will be visible to other participants ",-1))]),k[5]||(k[5]=d("div",{class:"bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-3"},[d("div",{class:"flex items-start"},[d("i",{class:"fas fa-shield-alt text-blue-500 mr-2 mt-0.5 text-sm"}),d("div",{class:"text-xs text-blue-700 dark:text-blue-300"},[d("strong",null,"Secure Meeting:"),J(" Your join will be tracked for security purposes. The host can see who shared the link that you used to join. ")])])],-1)),d("div",eo,[d("button",{type:"button",onClick:k[1]||(k[1]=b=>h.$emit("cancel")),class:"px-4 py-2 bg-gray-200 dark:bg-gray-700 text-gray-800 dark:text-gray-200 rounded-lg hover:bg-gray-300 dark:hover:bg-gray-600 transition-colors",disabled:u.value}," Cancel ",8,to),d("button",{type:"submit",class:"px-6 py-2 bg-primary text-white rounded-lg hover:bg-primary-dark transition-colors flex items-center",disabled:!a.value.trim()||u.value},[u.value?(E(),j("i",no)):(E(),j("i",io)),J(" "+w(u.value?"Joining...":"Join Meeting"),1)],8,ro)])],32)])])):N("",!0)}},oo={class:"bg-white dark:bg-gray-800 shadow-lg px-4 py-3 flex justify-center items-center space-x-4"},ao=["title","aria-label"],lo=["title","aria-label"],co=["title","aria-label"],uo=["title","aria-label"],fo=["title"],po=["title"],ho={class:"absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center"},go=["title"],mo={class:"absolute -bottom-1 -right-1 text-xs"},vo={key:0,class:"fas fa-users text-green-500"},yo={key:1,class:"fas fa-crown text-orange-500"},bo={__name:"MeetingControls",props:{isMicMuted:{type:Boolean,default:!1},isCameraOff:{type:Boolean,default:!1},isScreenSharing:{type:Boolean,default:!1},isChatOpen:{type:Boolean,default:!1},isAISEnabled:{type:Boolean,default:!1},isReactionsEnabled:{type:Boolean,default:!1},isBreakoutRoomsEnabled:{type:Boolean,default:!1},isVirtualBackgroundEnabled:{type:Boolean,default:!1},isWhiteboardEnabled:{type:Boolean,default:!1},isRecordingEnabled:{type:Boolean,default:!1},isRecording:{type:Boolean,default:!1},hasJoinRequests:{type:Boolean,default:!1},showJoinRequestsPanel:{type:Boolean,default:!1},joinRequestsCount:{type:Number,default:0},isAdmin:{type:Boolean,default:!1},canShareLink:{type:Boolean,default:!1},linkSharingPolicy:{type:String,default:"host"}},emits:["toggle-mic","toggle-camera","toggle-screen","toggle-chat","toggle-ais","toggle-reactions","toggle-breakout-rooms","toggle-virtual-background","toggle-whiteboard","toggle-recording","toggle-join-requests","show-invite","show-link-sharing","leave-meeting"],setup(i){return(o,n)=>(E(),j("div",oo,[d("button",{onClick:n[0]||(n[0]=r=>o.$emit("toggle-mic")),class:W(["p-3 rounded-full transition-colors duration-200",i.isMicMuted?"bg-red-100 text-red-600 dark:bg-red-900 dark:text-red-300":"bg-gray-100 text-gray-700 dark:bg-gray-700 dark:text-gray-300"]),title:i.isMicMuted?"Unmute microphone":"Mute microphone","aria-label":i.isMicMuted?"Unmute microphone":"Mute microphone"},[d("i",{class:W(i.isMicMuted?"fas fa-microphone-slash":"fas fa-microphone")},null,2)],10,ao),d("button",{onClick:n[1]||(n[1]=r=>o.$emit("toggle-camera")),class:W(["p-3 rounded-full transition-colors duration-200",i.isCameraOff?"bg-red-100 text-red-600 dark:bg-red-900 dark:text-red-300":"bg-gray-100 text-gray-700 dark:bg-gray-700 dark:text-gray-300"]),title:i.isCameraOff?"Turn on camera":"Turn off camera","aria-label":i.isCameraOff?"Turn on camera":"Turn off camera"},[d("i",{class:W(i.isCameraOff?"fas fa-video-slash":"fas fa-video")},null,2)],10,lo),d("button",{onClick:n[2]||(n[2]=r=>o.$emit("toggle-screen")),class:W(["p-3 rounded-full transition-colors duration-200",i.isScreenSharing?"bg-green-100 text-green-600 dark:bg-green-900 dark:text-green-300":"bg-gray-100 text-gray-700 dark:bg-gray-700 dark:text-gray-300"]),title:i.isScreenSharing?"Stop sharing screen":"Share screen","aria-label":i.isScreenSharing?"Stop sharing screen":"Share screen"},n[14]||(n[14]=[d("i",{class:"fas fa-desktop"},null,-1)]),10,co),d("button",{onClick:n[3]||(n[3]=r=>o.$emit("toggle-chat")),class:W(["p-3 rounded-full transition-colors duration-200",i.isChatOpen?"bg-primary-light text-white":"bg-gray-100 text-gray-700 dark:bg-gray-700 dark:text-gray-300"]),title:i.isChatOpen?"Close chat":"Open chat","aria-label":i.isChatOpen?"Close chat":"Open chat"},n[15]||(n[15]=[d("i",{class:"fas fa-comments"},null,-1)]),10,uo),d("button",{onClick:n[4]||(n[4]=r=>o.$emit("toggle-ais")),class:W(["p-3 rounded-full",i.isAISEnabled?"bg-purple-100 text-purple-600 dark:bg-purple-900 dark:text-purple-300":"bg-gray-100 text-gray-700 dark:bg-gray-700 dark:text-gray-300"]),title:i.isAISEnabled?"Disable transcription":"Enable transcription"},n[16]||(n[16]=[d("i",{class:"fas fa-closed-captioning"},null,-1)]),10,fo),d("button",{onClick:n[5]||(n[5]=r=>o.$emit("toggle-reactions")),class:W(["p-3 rounded-full",i.isReactionsEnabled?"bg-yellow-100 text-yellow-600 dark:bg-yellow-900 dark:text-yellow-300":"bg-gray-100 text-gray-700 dark:bg-gray-700 dark:text-gray-300"]),title:"Toggle reactions"},n[17]||(n[17]=[d("i",{class:"fas fa-smile"},null,-1)]),2),i.isAdmin?(E(),j("button",{key:0,onClick:n[6]||(n[6]=r=>o.$emit("toggle-breakout-rooms")),class:W(["p-3 rounded-full",i.isBreakoutRoomsEnabled?"bg-blue-100 text-blue-600 dark:bg-blue-900 dark:text-blue-300":"bg-gray-100 text-gray-700 dark:bg-gray-700 dark:text-gray-300"]),title:"Toggle breakout rooms"},n[18]||(n[18]=[d("i",{class:"fas fa-th-large"},null,-1)]),2)):N("",!0),d("button",{onClick:n[7]||(n[7]=r=>o.$emit("toggle-virtual-background")),class:W(["p-3 rounded-full",i.isVirtualBackgroundEnabled?"bg-indigo-100 text-indigo-600 dark:bg-indigo-900 dark:text-indigo-300":"bg-gray-100 text-gray-700 dark:bg-gray-700 dark:text-gray-300"]),title:"Toggle virtual background"},n[19]||(n[19]=[d("i",{class:"fas fa-image"},null,-1)]),2),d("button",{onClick:n[8]||(n[8]=r=>o.$emit("toggle-whiteboard")),class:W(["p-3 rounded-full",i.isWhiteboardEnabled?"bg-pink-100 text-pink-600 dark:bg-pink-900 dark:text-pink-300":"bg-gray-100 text-gray-700 dark:bg-gray-700 dark:text-gray-300"]),title:"Toggle whiteboard"},n[20]||(n[20]=[d("i",{class:"fas fa-edit"},null,-1)]),2),d("button",{onClick:n[9]||(n[9]=r=>o.$emit("toggle-recording")),class:W(["p-3 rounded-full",i.isRecordingEnabled?i.isRecording?"bg-red-100 text-red-600 dark:bg-red-900 dark:text-red-300 animate-pulse":"bg-green-100 text-green-600 dark:bg-green-900 dark:text-green-300":"bg-gray-100 text-gray-700 dark:bg-gray-700 dark:text-gray-300"]),title:i.isRecordingEnabled?i.isRecording?"Recording in progress":"Stop recording":"Start recording"},[d("i",{class:W(i.isRecording?"fas fa-stop-circle":"fas fa-record-vinyl")},null,2)],10,po),i.hasJoinRequests?(E(),j("button",{key:1,onClick:n[10]||(n[10]=r=>o.$emit("toggle-join-requests")),class:W(["p-3 rounded-full relative",i.showJoinRequestsPanel?"bg-primary-light text-white":"bg-gray-100 text-gray-700 dark:bg-gray-700 dark:text-gray-300"]),title:"Manage join requests"},[n[21]||(n[21]=d("i",{class:"fas fa-user-plus"},null,-1)),d("span",ho,w(i.joinRequestsCount),1)],2)):N("",!0),i.canShareLink?(E(),j("button",{key:2,onClick:n[11]||(n[11]=r=>o.$emit("show-invite")),class:"p-3 rounded-full bg-blue-100 text-blue-600 dark:bg-blue-900 dark:text-blue-300 hover:bg-blue-200 dark:hover:bg-blue-800 transition-colors",title:"Invite participants"},n[22]||(n[22]=[d("i",{class:"fas fa-user-plus"},null,-1)]))):N("",!0),i.isAdmin?(E(),j("button",{key:3,onClick:n[12]||(n[12]=r=>o.$emit("show-link-sharing")),class:W(["p-3 rounded-full relative",i.linkSharingPolicy==="all"?"bg-green-100 text-green-600 dark:bg-green-900 dark:text-green-300":"bg-orange-100 text-orange-600 dark:bg-orange-900 dark:text-orange-300"]),title:i.linkSharingPolicy==="all"?"Link sharing: All members":"Link sharing: Host only"},[n[23]||(n[23]=d("i",{class:"fas fa-share-alt"},null,-1)),d("span",mo,[i.linkSharingPolicy==="all"?(E(),j("i",vo)):(E(),j("i",yo))])],10,go)):N("",!0),d("button",{onClick:n[13]||(n[13]=r=>o.$emit("leave-meeting")),class:"p-3 rounded-full bg-red-100 text-red-600 dark:bg-red-900 dark:text-red-300",title:"Leave meeting"},n[24]||(n[24]=[d("i",{class:"fas fa-sign-out-alt"},null,-1)]))]))}},rt=[{id:"none",name:"None",url:null},{id:"blur",name:"Blur",url:"blur"}],xo={class:"virtual-background-selector bg-white dark:bg-gray-800 rounded-lg shadow-lg overflow-hidden"},ko={class:"p-4"},So={class:"grid grid-cols-3 gap-3"},Co=["onClick"],Ro={key:0,class:"w-full h-full flex items-center justify-center bg-gray-100 dark:bg-gray-700"},To={key:1,class:"w-full h-full flex items-center justify-center bg-gray-200 dark:bg-gray-600 backdrop-blur"},Po=["src","alt"],Eo={class:"mt-4"},jo={class:"flex"},Mo={class:"p-4 bg-gray-50 dark:bg-gray-700 flex justify-end"},Ao={__name:"VirtualBackgroundSelector",props:{currentBackground:{type:String,default:"none"},stream:{type:Object,default:null}},emits:["apply","cancel"],setup(i,{emit:o}){const n=i,r=o,a=H(n.currentBackground),u=H(null),v=H(null);ar(()=>{a.value=n.currentBackground});const h=g=>{a.value=g.id},k=g=>{const y=g.target.files[0];if(!y)return;u.value=URL.createObjectURL(y);const C={id:"custom",name:"Custom",url:u.value};if(!rt.find(A=>A.id==="custom"))rt.push(C);else{const A=rt.findIndex(F=>F.id==="custom");rt[A]=C}a.value="custom"},b=()=>{const g=rt.find(y=>y.id===a.value);g&&r("apply",{id:g.id,url:g.url})};return(g,y)=>(E(),j("div",xo,[y[5]||(y[5]=d("div",{class:"p-4 border-b border-gray-200 dark:border-gray-700"},[d("h2",{class:"text-lg font-semibold text-gray-900 dark:text-white"},"Virtual Background")],-1)),d("div",ko,[d("div",So,[(E(!0),j(ye,null,Re($(rt),C=>(E(),j("div",{key:C.id,onClick:A=>h(C),class:W(["aspect-video rounded-lg overflow-hidden cursor-pointer border-2",{"border-primary":a.value===C.id,"border-transparent":a.value!==C.id}])},[C.id==="none"?(E(),j("div",Ro,y[2]||(y[2]=[d("span",{class:"text-gray-700 dark:text-gray-300"},"None",-1)]))):C.id==="blur"?(E(),j("div",To,y[3]||(y[3]=[d("span",{class:"text-gray-700 dark:text-gray-300"},"Blur",-1)]))):(E(),j("img",{key:2,src:C.url,alt:C.name,class:"w-full h-full object-cover"},null,8,Po))],10,Co))),128))]),d("div",Eo,[y[4]||(y[4]=d("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"}," Custom Background ",-1)),d("div",jo,[d("input",{type:"file",accept:"image/*",onChange:k,class:"hidden",ref_key:"fileInput",ref:v},null,544),d("button",{onClick:y[0]||(y[0]=C=>g.$refs.fileInput.click()),class:"btn btn-outline flex-1"}," Upload Image ")])])]),d("div",Mo,[d("button",{onClick:y[1]||(y[1]=C=>g.$emit("cancel")),class:"btn btn-outline mr-2"}," Cancel "),d("button",{onClick:b,class:"btn btn-primary"}," Apply ")])]))}},Io={class:"bg-white dark:bg-gray-800 shadow-lg border-l border-gray-200 dark:border-gray-700 w-80 flex flex-col"},_o={class:"p-4 border-b border-gray-200 dark:border-gray-700 flex justify-between items-center"},Oo={class:"p-4 border-b border-gray-200 dark:border-gray-700 space-y-3"},Lo=["value"],Do={class:"flex-1 overflow-y-auto"},$o={class:"p-4"},wo={key:0,class:"text-center py-8"},Fo={key:1,class:"space-y-3"},Uo={key:0,class:"flex items-start space-x-3"},No={class:"flex-1 min-w-0"},Bo={class:"text-sm text-gray-900 dark:text-white"},Vo={class:"flex items-center mt-1 text-xs text-gray-500 dark:text-gray-400"},zo={key:0,class:"ml-2 px-2 py-0.5 bg-gray-200 dark:bg-gray-600 rounded text-xs"},Go={key:1,class:"flex items-start space-x-3"},Jo={class:"flex-1 min-w-0"},Ho={class:"text-sm text-gray-900 dark:text-white"},qo={class:"flex items-center mt-1 text-xs text-gray-500 dark:text-gray-400"},Wo={key:0,class:"ml-2 text-xs"},Yo={class:"p-4 border-t border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-700"},Ko={class:"grid grid-cols-2 gap-4 text-center"},Xo={class:"text-lg font-semibold text-primary"},Zo={class:"text-lg font-semibold text-green-600"},Qo={__name:"LinkSharingPanel",props:{activities:{type:Array,default:()=>[]},linkSharingPolicy:{type:String,default:"host"},participants:{type:Array,default:()=>[]}},emits:["close","updatePolicy","showInvite"],setup(i,{emit:o}){const n=i,r=Z(()=>[...n.activities].sort((k,b)=>new Date(b.timestamp)-new Date(k.timestamp))),a=Z(()=>n.activities.filter(k=>k.type==="link_shared").length),u=Z(()=>n.activities.filter(k=>k.type==="user_joined").length);function v(k){const b=n.participants.find(g=>g.id===k||g.userId===k);return(b==null?void 0:b.userName)||"Unknown User"}function h(k){return k?(k.toDate?k.toDate():new Date(k)).toLocaleTimeString([],{hour:"2-digit",minute:"2-digit"}):""}return(k,b)=>(E(),j("div",Io,[d("div",_o,[b[4]||(b[4]=d("h2",{class:"font-semibold text-gray-900 dark:text-white flex items-center"},[d("i",{class:"fas fa-share-alt mr-2 text-primary"}),J(" Link Activity ")],-1)),d("button",{onClick:b[0]||(b[0]=g=>k.$emit("close")),class:"text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"},b[3]||(b[3]=[d("i",{class:"fas fa-times"},null,-1)]))]),d("div",Oo,[d("div",null,[b[6]||(b[6]=d("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"}," Who can share the meeting link? ",-1)),d("select",{value:i.linkSharingPolicy,onChange:b[1]||(b[1]=g=>k.$emit("updatePolicy",g.target.value)),class:"w-full p-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white text-sm"},b[5]||(b[5]=[d("option",{value:"host"},"Host Only",-1),d("option",{value:"all"},"All Participants",-1)]),40,Lo)]),d("button",{onClick:b[2]||(b[2]=g=>k.$emit("showInvite")),class:"w-full btn btn-primary flex items-center justify-center"},b[7]||(b[7]=[d("i",{class:"fas fa-share mr-2"},null,-1),J(" Share Meeting Link ")]))]),d("div",Do,[d("div",$o,[b[15]||(b[15]=d("h3",{class:"text-sm font-medium text-gray-700 dark:text-gray-300 mb-3 flex items-center"},[d("i",{class:"fas fa-history mr-2"}),J(" Recent Activity ")],-1)),i.activities.length===0?(E(),j("div",wo,b[8]||(b[8]=[d("i",{class:"fas fa-share-alt text-4xl text-gray-300 dark:text-gray-600 mb-3"},null,-1),d("p",{class:"text-sm text-gray-500 dark:text-gray-400"},"No link sharing activity yet",-1)]))):(E(),j("div",Fo,[(E(!0),j(ye,null,Re(r.value,g=>(E(),j("div",{key:g.id,class:"bg-gray-50 dark:bg-gray-700 rounded-lg p-3 border border-gray-200 dark:border-gray-600"},[g.type==="link_shared"?(E(),j("div",Uo,[b[11]||(b[11]=d("div",{class:"w-8 h-8 bg-blue-100 dark:bg-blue-900 rounded-full flex items-center justify-center flex-shrink-0"},[d("i",{class:"fas fa-share text-blue-600 dark:text-blue-400 text-sm"})],-1)),d("div",No,[d("p",Bo,[d("strong",null,w(v(g.sharedBy)),1),b[9]||(b[9]=J(" shared the meeting link "))]),d("div",Vo,[b[10]||(b[10]=d("i",{class:"fas fa-clock mr-1"},null,-1)),J(" "+w(h(g.timestamp))+" ",1),g.method?(E(),j("span",zo,w(g.method),1)):N("",!0)])])])):g.type==="user_joined"?(E(),j("div",Go,[b[14]||(b[14]=d("div",{class:"w-8 h-8 bg-green-100 dark:bg-green-900 rounded-full flex items-center justify-center flex-shrink-0"},[d("i",{class:"fas fa-user-plus text-green-600 dark:text-green-400 text-sm"})],-1)),d("div",Jo,[d("p",Ho,[d("strong",null,w(g.joinedName),1),b[12]||(b[12]=J(" joined via shared link "))]),d("div",qo,[b[13]||(b[13]=d("i",{class:"fas fa-clock mr-1"},null,-1)),J(" "+w(h(g.timestamp))+" ",1),g.sharedBy?(E(),j("span",Wo," (Link shared by "+w(v(g.sharedBy))+") ",1)):N("",!0)])])])):N("",!0)]))),128))]))])]),d("div",Yo,[b[18]||(b[18]=d("h3",{class:"text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"},"Statistics",-1)),d("div",Ko,[d("div",null,[d("div",Xo,w(a.value),1),b[16]||(b[16]=d("div",{class:"text-xs text-gray-500 dark:text-gray-400"},"Links Shared",-1))]),d("div",null,[d("div",Zo,w(u.value),1),b[17]||(b[17]=d("div",{class:"text-xs text-gray-500 dark:text-gray-400"},"Joined via Link",-1))])])])]))}},Ct=H([]);let ea=0;function li(){const i=y=>{const C=++ea,A={id:C,...y,timestamp:new Date};Ct.value.push(A);const F=y.autoRemoveTime||5e3;return y.autoDismiss!==!1&&setTimeout(()=>{o(C)},F),C},o=y=>{const C=Ct.value.findIndex(A=>A.id===y);C>-1&&Ct.value.splice(C,1)};return{notifications:Ct,addNotification:i,removeNotification:o,clearAllNotifications:()=>{Ct.value=[]},showSuccess:(y,C,A={})=>i({type:"success",title:y,message:C,...A}),showError:(y,C,A={})=>i({type:"error",title:y,message:C,autoDismiss:!1,...A}),showWarning:(y,C,A={})=>i({type:"warning",title:y,message:C,...A}),showInfo:(y,C,A={})=>i({type:"info",title:y,message:C,...A}),showLinkShared:(y,C,A={})=>i({type:"link_shared",title:"Meeting Link Shared",message:`${y} shared the meeting link${C?` via ${C}`:""}`,...A}),showUserJoinedViaLink:(y,C,A={})=>i({type:"user_joined",title:"New Participant Joined",message:`${y} joined via link shared by ${C}`,...A}),showInvitePrompt:(y,C={})=>i({type:"info",title:"Invite Participants",message:y===1?"You're alone in the meeting. Invite others to join!":"Share the meeting link to invite more participants",actions:[{label:"Share Link",primary:!0,action:"show_invite"},{label:"Dismiss",action:"dismiss"}],autoDismiss:!1,...C}),showLinkSharingPolicyChanged:(y,C,A={})=>i({type:"info",title:"Link Sharing Policy Updated",message:`${C} changed link sharing to: ${y==="all"?"All participants":"Host only"}`,...A})}}const ta={key:0,class:"max-w-sm w-full bg-white dark:bg-gray-800 shadow-lg rounded-lg pointer-events-auto ring-1 ring-black ring-opacity-5 overflow-hidden"},ra={class:"p-4"},na={class:"flex items-start"},ia={class:"flex-shrink-0"},sa={class:"ml-3 w-0 flex-1 pt-0.5"},oa={class:"text-sm font-medium text-gray-900 dark:text-white"},aa={class:"mt-1 text-sm text-gray-500 dark:text-gray-400"},la={key:0,class:"mt-3 flex space-x-2"},ca=["onClick"],ua={class:"ml-4 flex-shrink-0 flex"},da={key:0,class:"h-1 bg-gray-200 dark:bg-gray-700"},fa={__name:"NotificationToast",props:{notification:{type:Object,required:!0},visible:{type:Boolean,default:!0},autoDismiss:{type:Boolean,default:!0},autoDismissTime:{type:Number,default:5e3}},emits:["close","action"],setup(i,{emit:o}){const n=i,r=o,a=H(n.autoDismissTime);let u=null,v=null;const h=Z(()=>{const C="text-white";switch(n.notification.type){case"success":return`${C} bg-green-500`;case"error":return`${C} bg-red-500`;case"warning":return`${C} bg-yellow-500`;case"info":return`${C} bg-blue-500`;case"link_shared":return`${C} bg-blue-500`;case"user_joined":return`${C} bg-green-500`;default:return`${C} bg-gray-500`}}),k=Z(()=>{switch(n.notification.type){case"success":return"fas fa-check";case"error":return"fas fa-exclamation-triangle";case"warning":return"fas fa-exclamation";case"info":return"fas fa-info";case"link_shared":return"fas fa-share-alt";case"user_joined":return"fas fa-user-plus";default:return"fas fa-bell"}}),b=C=>{r("action",C),C.closeOnClick!==!1&&r("close")},g=()=>{if(!n.autoDismiss)return;u=setTimeout(()=>{r("close")},n.autoDismissTime);const C=100;v=setInterval(()=>{a.value-=C,a.value<=0&&clearInterval(v)},C)},y=()=>{u&&(clearTimeout(u),u=null),v&&(clearInterval(v),v=null)};return ar(()=>{n.visible&&g()}),Qi(()=>{y()}),(C,A)=>(E(),He(ts,{"enter-active-class":"transform ease-out duration-300 transition","enter-from-class":"translate-y-2 opacity-0 sm:translate-y-0 sm:translate-x-2","enter-to-class":"translate-y-0 opacity-100 sm:translate-x-0","leave-active-class":"transition ease-in duration-100","leave-from-class":"opacity-100","leave-to-class":"opacity-0"},{default:jn(()=>[i.visible?(E(),j("div",ta,[d("div",ra,[d("div",na,[d("div",ia,[d("div",{class:W(["w-8 h-8 rounded-full flex items-center justify-center",h.value])},[d("i",{class:W([k.value,"text-sm"])},null,2)],2)]),d("div",sa,[d("p",oa,w(i.notification.title),1),d("p",aa,w(i.notification.message),1),i.notification.actions?(E(),j("div",la,[(E(!0),j(ye,null,Re(i.notification.actions,F=>(E(),j("button",{key:F.label,onClick:Y=>b(F),class:W(["text-sm font-medium rounded-md px-3 py-1.5 transition-colors",F.primary?"bg-primary text-white hover:bg-primary-dark":"bg-gray-100 text-gray-700 hover:bg-gray-200 dark:bg-gray-700 dark:text-gray-300 dark:hover:bg-gray-600"])},w(F.label),11,ca))),128))])):N("",!0)]),d("div",ua,[d("button",{onClick:A[0]||(A[0]=F=>C.$emit("close")),class:"bg-white dark:bg-gray-800 rounded-md inline-flex text-gray-400 hover:text-gray-500 dark:hover:text-gray-300 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary"},A[1]||(A[1]=[d("span",{class:"sr-only"},"Close",-1),d("i",{class:"fas fa-times text-sm"},null,-1)]))])])]),i.autoDismiss&&a.value>0?(E(),j("div",da,[d("div",{class:"h-full bg-primary transition-all duration-100 ease-linear",style:es({width:`${a.value/i.autoDismissTime*100}%`})},null,4)])):N("",!0)])):N("",!0)]),_:1}))}},pa=En(fa,[["__scopeId","data-v-da0fc769"]]),ha={class:"fixed inset-0 flex items-end justify-center px-4 py-6 pointer-events-none sm:p-6 sm:items-start sm:justify-end z-50"},ga={class:"w-full flex flex-col items-center space-y-4 sm:items-end"},ma={__name:"NotificationContainer",emits:["action"],setup(i,{emit:o}){const{notifications:n,removeNotification:r}=li(),a=o,u=v=>{a("action",v)};return(v,h)=>(E(),j("div",ha,[d("div",ga,[Ae(rs,{"enter-active-class":"transform ease-out duration-300 transition","enter-from-class":"translate-y-2 opacity-0 sm:translate-y-0 sm:translate-x-2","enter-to-class":"translate-y-0 opacity-100 sm:translate-x-0","leave-active-class":"transition ease-in duration-100","leave-from-class":"opacity-100","leave-to-class":"opacity-0","move-class":"transition-all duration-300"},{default:jn(()=>[(E(!0),j(ye,null,Re($(n),k=>(E(),He(pa,{key:k.id,notification:k,visible:!0,onClose:b=>$(r)(k.id),onAction:u},null,8,["notification","onClose"]))),128))]),_:1})])]))}},va=En(ma,[["__scopeId","data-v-da453db0"]]),ya={key:2,class:"h-screen flex items-center justify-center bg-gray-100 dark:bg-gray-900"},ba={class:"text-center"},xa={class:"mt-4 text-lg font-medium text-gray-700 dark:text-gray-300"},ka={key:3,class:"h-screen flex items-center justify-center bg-gray-100 dark:bg-gray-900"},Sa={class:"text-center p-6 bg-white dark:bg-gray-800 rounded-lg shadow-xl"},Ca={class:"text-gray-600 dark:text-gray-300 mb-6"},Ra={key:4,class:"h-screen flex flex-col bg-gray-100 dark:bg-gray-900"},Ta={class:"sticky top-0 z-30 bg-white dark:bg-gray-800 shadow-md px-2 sm:px-4 py-2 flex flex-col sm:flex-row justify-between items-center gap-2 border-b border-gray-200 dark:border-gray-700"},Pa={class:"flex items-center"},Ea={class:"text-xl font-semibold text-gray-900 dark:text-white"},ja={class:"ml-4 px-2 py-1 bg-gray-100 dark:bg-gray-700 rounded text-sm text-gray-600 dark:text-gray-300"},Ma={key:0,class:"ml-4 flex items-center gap-2"},Aa={key:0,class:"w-full mt-2"},Ia={class:"text-xs max-h-24 overflow-y-auto"},_a={class:"font-bold"},Oa={class:"font-bold"},La={class:"flex items-center"},Da={class:"text-sm text-gray-600 dark:text-gray-300 mr-2"},$a={class:"flex-1 flex overflow-hidden"},wa={class:"flex-1 p-4 overflow-auto"},Fa={class:"text-center mb-6"},Ua={class:"text-2xl font-bold text-gray-900 dark:text-white"},Na={class:"flex flex-col lg:flex-row gap-6 lg:gap-8 h-full max-h-[600px]"},Ba={class:"flex-shrink-0 w-full lg:w-2/5"},Va={class:"relative bg-black rounded-2xl overflow-hidden shadow-xl h-full min-h-[300px] lg:min-h-[400px] border-2 border-gray-300 dark:border-gray-600"},za={key:1,class:"w-full h-full flex flex-col items-center justify-center bg-black"},Ga={key:2,class:"absolute top-4 right-4 bg-red-600 rounded-full p-2"},Ja={class:"text-center mt-4"},Ha={class:"text-xl font-bold text-gray-900 dark:text-white uppercase tracking-wide"},qa={class:"flex-1"},Wa={class:"grid grid-cols-2 sm:grid-cols-3 gap-3 lg:gap-4 h-full auto-rows-fr"},Ya={class:"relative bg-black rounded-xl overflow-hidden shadow-lg aspect-square border border-gray-300 dark:border-gray-600"},Ka={key:1,class:"w-full h-full flex items-center justify-center bg-gray-800"},Xa=["aria-label"],Za={key:2,class:"absolute top-2 right-2 bg-red-600 rounded-full p-1.5"},Qa={key:3,class:"absolute top-2 left-2"},el=["onClick"],tl={class:"text-center mt-2"},rl={class:"text-sm font-semibold text-gray-900 dark:text-white truncate"},nl={key:0,class:"mt-4"},il={key:0},sl={class:"font-semibold"},ol={class:"text-sm text-red-500"},al={key:0,class:"w-80 bg-white dark:bg-gray-800 shadow-lg flex flex-col border-l border-gray-200 dark:border-gray-700"},ll={class:"flex-1 p-3 overflow-y-auto"},cl={class:"font-semibold mb-1 text-gray-900 dark:text-white"},ul={class:"text-sm text-gray-600 dark:text-gray-300 mb-2"},dl={class:"flex space-x-2"},fl=["onClick"],pl=["onClick"],hl={key:1,class:"w-80 bg-white dark:bg-gray-800 shadow-lg flex flex-col border-l border-gray-200 dark:border-gray-700"},gl={class:"p-3 border-b border-gray-200 dark:border-gray-700 flex justify-between items-center"},ml={class:"flex-1"},vl={key:2,class:"w-80 bg-white dark:bg-gray-800 shadow-lg flex flex-col border-l border-gray-200 dark:border-gray-700"},yl={class:"p-3 border-b border-gray-200 dark:border-gray-700 flex justify-between items-center"},bl={class:"flex-1"},xl={key:3,class:"w-80 bg-white dark:bg-gray-800 shadow-lg flex flex-col border-l border-gray-200 dark:border-gray-700"},kl={class:"flex-1"},Sl={key:5,class:"w-80 bg-white dark:bg-gray-800 shadow-lg flex flex-col border-l border-gray-200 dark:border-gray-700"},Cl={class:"p-3 border-b border-gray-200 dark:border-gray-700 flex justify-between items-center"},Rl={class:"flex items-start"},Tl={class:"font-semibold mb-1"},Pl={class:"p-3 border-t border-gray-200 dark:border-gray-700"},El=["disabled"],jl={key:1,class:"border-t border-gray-200 dark:border-gray-700"},Ml={key:2,class:"border-t border-gray-200 dark:border-gray-700 p-2"},Al={key:3,class:"fixed bottom-24 left-1/2 transform -translate-x-1/2 z-10"},Il={key:5,class:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50"},_l={class:"bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6 max-w-md w-full"},Ol={class:"flex justify-end space-x-2"},Ll={key:6,class:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50"},Dl={class:"bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6 max-w-md w-full"},$l={class:"mb-4 text-gray-700 dark:text-gray-300"},wl={class:"font-semibold"},Fl={class:"flex justify-end space-x-2"},zl={__name:"Meeting",setup(i){const o=ss(),n=is(),r=gs(),a=us(),u=ns(),{showLinkShared:v,showUserJoinedViaLink:h,showInvitePrompt:k,showLinkSharingPolicyChanged:b}=li(),g=Z(()=>n.params.id),y=Z(()=>{var R;return(R=u.user)!=null&&R.id?u.user.id:localStorage.getItem("userId")||`guest_${Date.now()}`}),C=Z(()=>{var R;return(R=u.user)!=null&&R.name?u.user.name:localStorage.getItem("userName")||"Guest User"}),A=Z(()=>{try{return(a==null?void 0:a.getMeetingName)||`Meeting ${g.value}`}catch(R){return console.warn("Error accessing meeting name:",R),`Meeting ${g.value}`}}),F=Z(()=>{try{return(a==null?void 0:a.linkSharingPolicy)||"host"}catch(R){return console.warn("Error accessing link sharing policy:",R),"host"}}),Y=Z(()=>{var R;try{return ee.value&&((R=r==null?void 0:r.participants)==null?void 0:R.length)<=1}catch(x){return console.warn("Error checking if host is alone:",x),!1}});jt(Y,R=>{R&&!oe.value&&!Te.value&&setTimeout(()=>{Y.value&&!oe.value&&k(1)},3e3)}),jt(()=>{try{return a==null?void 0:a.linkSharingPolicy}catch(R){return console.warn("Error watching link sharing policy:",R),"host"}},(R,x)=>{try{x&&R!==x&&b(R,Ot())}catch(D){console.warn("Error in link sharing policy change handler:",D)}});const oe=H(!1),Te=H(!1),st=(R=null)=>{const x=`${window.location.origin}/meeting/${g.value}`,D=new URLSearchParams;return D.set("joinViaLink","true"),R&&D.set("sharedBy",R),D.set("timestamp",Date.now().toString()),`${x}?${D.toString()}`},ot=Z(()=>{try{return st(y.value)}catch(R){return console.warn("Error generating invite link:",R),""}}),q=Z(()=>{try{return(a==null?void 0:a.getJoinTracking)||[]}catch(R){return console.warn("Error accessing join tracking:",R),[]}});function Mt(R){a.updateLinkSharingPolicy(R)}const ee=Z(()=>{var R;try{return((R=a==null?void 0:a.isHost)==null?void 0:R.call(a,y.value))||!1}catch(x){return console.warn("Error checking admin status:",x),!1}}),te=Z(()=>{try{return(a==null?void 0:a.activeFeatures)||{}}catch(R){return console.warn("Error accessing active features:",R),{}}}),Ie=Z(()=>a.getAllActiveExtensions),we=H(null),lr=H({}),V=H(null),Fe=H(!1),se=H(!1),Ue=H(!1),ge=H(""),Ne=H(null),Pe=H([]),Be=H(!1),ue=H(null),_e=H(""),at=H(!1),Oe=H("");let Ve=null,We=null,Ye=null,Ke=null;const cr=()=>{const R=n.query.joinViaLink,x=n.query.sharedBy,D=localStorage.getItem("userName");return R&&!D?(Te.value=!0,!0):(R&&D&&x&&At(x,D),!1)},At=async(R,x)=>{try{await a.recordJoinViaLink({sharedBy:R,joinedName:x,joinedBy:y.value,timestamp:new Date().toISOString()})}catch(D){console.error("Failed to record join via link:",D)}};ar(async()=>{if(!cr()){if(!y.value){alert("User ID not found. Please ensure you are logged in or have provided a name."),o.push("/");return}localStorage.getItem("userId")||localStorage.setItem("userId",y.value),C.value&&!localStorage.getItem("userName")&&localStorage.setItem("userName",C.value);try{await It()}catch(R){console.error("Error in onMounted:",R)}}});const lt=()=>{V.value&&V.value.leaveMeeting()};os(()=>{window.removeEventListener("beforeunload",lt),V.value&&V.value.leaveMeeting(),r.localStream&&r.localStream.getTracks().forEach(R=>R.stop()),r.reset(),a.resetMeetingConfig(),We&&We(),Ye&&Ye(),Ve&&Ve(),Ke&&Ke()});const ct=R=>{console.log("Participant joined (WebRTC):",R)},ze=R=>{console.log("Participant left (WebRTC):",R)},It=async()=>{try{if(r.setMeetingId(g.value),await a.loadMeetingConfiguration(g.value,y.value),a.error){alert(`Error loading meeting: ${a.error}`),o.push("/");return}V.value=new Es(g.value,y.value,(x,D)=>r.addRemoteStream(x,D),x=>r.removeRemoteStream(x),ct,ze);const R=await V.value.initLocalStream();r.setLocalStream(R),we.value&&r.localStream&&(we.value.srcObject=r.localStream),await V.value.joinMeeting(),r.setConnectionStatus("connected"),We=ds(g.value,x=>{r.messages=x,pt()}),Ye=fs(g.value,x=>{r.participants=x},x=>{if(ee.value){console.log("User joined via shared link:",x);const D=Ee(x.participantData.sharedBy)||"Unknown";h(x.participantData.userName||"Unknown User",D)}}),ee.value&&(Ge(),Ke=ps(g.value,x=>{a.linkSharingActivities=x})),window.addEventListener("beforeunload",lt)}catch(R){console.error("Error initializing meeting:",R),r.setConnectionStatus("disconnected"),alert(`Failed to join the meeting: ${R.message}. Please try again.`),o.push("/")}},K=async R=>{typeof R=="string"?(localStorage.setItem("userName",R),console.log("Username set:",R)):(localStorage.setItem("userName",R.username),console.log("Username set with tracking:",R),R.joinMethod==="shared_link"&&await a.recordJoinViaLink({sharedBy:R.sharedBy||"unknown",joinedName:R.username,joinedBy:y.value,timestamp:R.timestamp})),Te.value=!1,await It()},re=()=>{o.push("/")},_t=async R=>{await a.recordLinkSharingActivity({type:"link_shared",sharedBy:y.value,method:R.method,timestamp:R.timestamp}),ee.value&&v(C.value,R.method),console.log("Link shared via:",R.method)},Ot=()=>{const R=a.meetingSettings.hostUserId,x=r.participants.find(D=>D.userId===R||D.id===R);return(x==null?void 0:x.userName)||"Host"},Ee=R=>{const x=r.participants.find(D=>D.id===R||D.userId===R);return(x==null?void 0:x.userName)||"Unknown User"},Lt=R=>{switch(R.action){case"show_invite":oe.value=!0;break;case"dismiss":break;default:console.log("Unknown notification action:",R)}},Dt=()=>{r.toggleMic()},$t=()=>{r.toggleCamera()},wt=async()=>{try{const R=r.isScreenSharing;await V.value.toggleScreenSharing(!R),r.toggleScreenSharing()}catch(R){console.error("Error toggling screen sharing:",R),r.isScreenSharing&&r.toggleScreenSharing()}},ut=async()=>{if(ge.value.trim())try{await Qt(g.value,y.value,C.value,ge.value),ge.value=""}catch(R){console.error("Error sending message:",R)}},Ft=()=>{navigator.clipboard.writeText(g.value).then(()=>alert("Meeting ID copied to clipboard")).catch(R=>console.error("Failed to copy meeting ID:",R))},je=R=>{const x=r.participants.find(D=>D.id===R||D.userId===R);return(x==null?void 0:x.audioMuted)||!1},Ut=R=>{const x=r.participants.find(D=>D.id===R||D.userId===R);return(x==null?void 0:x.videoMuted)||!1},Xe=async R=>{if(!(!a.isFeatureActive("virtualBackground")||!r.localStream))try{const x=r.localStream.getVideoTracks()[0];if(!x)return;const D=new As,De=await D.initialize(x);if(De){D.setBackground(R.url);const fe=new MediaStream([De,...r.localStream.getAudioTracks()]);r.setLocalStream(fe),we.value&&(we.value.srcObject=fe),V.value&&await V.value.updateVideoTrack(De)}await a.toggleFeature("virtualBackground")}catch(x){console.error("Error applying virtual background:",x),alert("Failed to apply virtual background.")}},dt=R=>{ue.value=R,_e.value="",Be.value=!0},ur=async()=>{if(!(!ee.value||!ue.value))try{if(V.value){await Et(ce(Q,"meetings",g.value,"participants",ue.value));const R=_e.value.trim()?` (Reason: ${_e.value})`:"";await Qt(g.value,"system","System",`${Ee(ue.value)} was removed by the host${R}`),Be.value=!1,ue.value=null,_e.value=""}}catch(R){console.error("Error removing participant:",R),alert("Failed to remove participant.")}},ft=()=>{Oe.value="",at.value=!0},dr=async()=>{try{Oe.value.trim()&&await Qt(g.value,"system","System",`${C.value} left the meeting (Reason: ${Oe.value})`),o.push("/")}catch(R){console.error("Error leaving meeting with reason:",R),o.push("/")}},Ze=R=>R?(R.toDate?R.toDate():new Date(R)).toLocaleTimeString([],{hour:"2-digit",minute:"2-digit"}):"",pt=()=>{as(()=>{Ne.value&&(Ne.value.scrollTop=Ne.value.scrollHeight)})};jt(()=>r.messages,pt,{deep:!0,immediate:!0});const Ge=()=>{const R=nt(Q,"meetings",g.value,"joinRequests");Ve=er(R,x=>{Pe.value=x.docs.map(D=>({id:D.id,...D.data()})),Pe.value.length>0&&se.value})},ht=async R=>{try{const x=Pe.value.find(D=>D.id===R);if(!x)return;await mn(ce(Q,"meetings",g.value,"joinRequests",R),{status:"approved",approvedAt:Er(),approvedBy:y.value}),await Rt(ce(Q,"meetings",g.value,"participants",x.userId),{userId:x.userId,userName:x.userName,joined:Er(),approved:!0,isAdmin:!1}),await Qt(g.value,"system","System",`${x.userName} joined the meeting.`),await Et(ce(Q,"meetings",g.value,"joinRequests",R))}catch(x){console.error("Error approving join request:",x),alert("Failed to approve join request.")}},Nt=async R=>{try{await mn(ce(Q,"meetings",g.value,"joinRequests",R),{status:"denied",deniedAt:Er(),deniedBy:y.value}),await Et(ce(Q,"meetings",g.value,"joinRequests",R))}catch(x){console.error("Error denying join request:",x),alert("Failed to deny join request.")}},Bt=()=>{se.value=!se.value},Vt=Z(()=>a.isFeatureActive("recording")),gt=Z(()=>a.isFeatureActive("whiteboard")),be=async()=>{ee.value&&await a.toggleFeature("whiteboard")},Le=R=>(R&&console.warn(`Dynamic component resolution for entry point "${R}" not yet fully implemented. Attempting placeholder lookup.`),console.warn(`Component for entry point "${R}" could not be resolved.`),null);return(R,x)=>{const D=tt("BreakoutRooms"),De=tt("Whiteboard"),fe=tt("ReactionDisplay"),mt=tt("AudibleImpairedSystem"),zt=tt("RecordingControls"),Qe=tt("ReactionSelector");return E(),j(ye,null,[oe.value?(E(),He(Js,{key:0,visible:oe.value,inviteLink:ot.value,meetingId:g.value,meetingName:A.value,hostName:C.value,linkSharingPolicy:F.value,onClose:x[0]||(x[0]=I=>oe.value=!1),onLinkShared:_t},null,8,["visible","inviteLink","meetingId","meetingName","hostName","linkSharingPolicy"])):N("",!0),Te.value?(E(),He(so,{key:1,visible:Te.value,meetingInfo:{name:A.value,hostName:Ot(),participantCount:$(r).participants.length},sharedBy:$(n).query.sharedBy,joinViaLink:!!$(n).query.joinViaLink,onSubmit:K,onCancel:re},null,8,["visible","meetingInfo","sharedBy","joinViaLink"])):N("",!0),$(a).isLoading||$(r).connectionStatus==="connecting"?(E(),j("div",ya,[d("div",ba,[x[24]||(x[24]=d("svg",{class:"mx-auto h-12 w-12 text-primary animate-spin",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","aria-label":"Loading meeting",role:"img"},[d("circle",{class:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor","stroke-width":"4"}),d("path",{class:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})],-1)),d("p",xa,w($(r).connectionStatus==="connecting"?"Connecting to meeting...":"Loading meeting details..."),1)])])):$(a).error?(E(),j("div",ka,[d("div",Sa,[x[25]||(x[25]=d("i",{class:"fas fa-exclamation-triangle text-4xl text-red-500 mb-4"},null,-1)),x[26]||(x[26]=d("h2",{class:"text-2xl font-semibold text-gray-900 dark:text-white mb-2"},"Error Loading Meeting",-1)),d("p",Ca,w($(a).error),1),d("button",{onClick:x[1]||(x[1]=I=>$(o).push("/")),class:"btn btn-primary"},"Go to Homepage")])])):(E(),j("div",Ra,[d("div",Ta,[d("div",Pa,[d("h1",Ea,w(A.value),1),d("div",ja,[J(w(g.value)+" ",1),d("button",{onClick:Ft,class:"ml-1 text-primary hover:text-primary-dark",title:"Copy meeting ID"},x[27]||(x[27]=[d("i",{class:"fas fa-copy"},null,-1)]))]),ee.value?(E(),j("div",Ma,[x[29]||(x[29]=d("label",{class:"text-xs text-gray-600 dark:text-gray-300"},"Who can share invite?",-1)),Tt(d("select",{"onUpdate:modelValue":x[2]||(x[2]=I=>F.value=I),onChange:Mt,class:"text-xs p-1 rounded border bg-gray-50 dark:bg-gray-700"},x[28]||(x[28]=[d("option",{value:"host"},"Host Only",-1),d("option",{value:"all"},"All Members",-1)]),544),[[ls,F.value]])])):N("",!0)]),ee.value&&q.value.length?(E(),j("div",Aa,[x[31]||(x[31]=d("div",{class:"text-xs text-gray-600 dark:text-gray-300 font-semibold mb-1"},"Join Link Activity",-1)),d("ul",Ia,[(E(!0),j(ye,null,Re(q.value,(I,de)=>(E(),j("li",{key:de,class:"mb-1"},[d("span",_a,w(Ee(I.sharedBy)),1),x[30]||(x[30]=J(" shared link → ")),d("span",Oa,w(I.joinedName),1),J(" joined ("+w(Ze(I.joinedAt))+") ",1)]))),128))])])):N("",!0),d("div",La,[d("span",Da,w($(r).participants.length)+" participant"+w($(r).participants.length!==1?"s":""),1),d("button",{onClick:ft,class:"btn btn-outline text-red-600 hover:bg-red-50 dark:hover:bg-red-900 dark:text-red-400"}," Leave ")])]),d("div",$a,[d("div",wa,[d("div",Fa,[d("h1",Ua,w(A.value||"MVP DEVELOPMENT MEETING"),1)]),d("div",Na,[d("div",Ba,[d("div",Va,[$(r).isCameraOff?(E(),j("div",za,x[32]||(x[32]=[Ar('<div class="relative mb-6"><svg class="w-24 h-24 text-white" fill="currentColor" viewBox="0 0 24 24" aria-label="Host avatar"><circle cx="12" cy="8" r="3"></circle><path d="M12 12c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z"></path></svg><div class="absolute inset-0 flex items-center justify-center"><div class="absolute w-32 h-32 border-2 border-white opacity-30 rounded-full animate-ping"></div><div class="absolute w-40 h-40 border-2 border-white opacity-20 rounded-full animate-ping" style="animation-delay:0.5s;"></div><div class="absolute w-48 h-48 border-2 border-white opacity-10 rounded-full animate-ping" style="animation-delay:1s;"></div></div></div>',1)]))):(E(),j("video",{key:0,ref_key:"localVideo",ref:we,muted:!0,autoplay:"",playsinline:"",class:"w-full h-full object-cover"},null,512)),$(r).isMicMuted?(E(),j("div",Ga,x[33]||(x[33]=[d("i",{class:"fas fa-microphone-slash text-white"},null,-1)]))):N("",!0)]),d("div",Ja,[d("div",Ha,w(C.value||"KATHLEEN JOHNSON"),1),x[34]||(x[34]=d("div",{class:"text-base font-bold text-gray-900 dark:text-white mt-1"}," HOST ",-1))])]),d("div",qa,[d("div",Wa,[(E(!0),j(ye,null,Re($(r).remoteStreams,(I,de)=>(E(),j("div",{key:de,class:"flex flex-col"},[d("div",Ya,[Ut(de)?(E(),j("div",Ka,[(E(),j("svg",{class:"w-16 h-16 text-gray-400",fill:"currentColor",viewBox:"0 0 24 24","aria-label":`${Ee(de)} avatar`},x[35]||(x[35]=[d("circle",{cx:"12",cy:"8",r:"3"},null,-1),d("path",{d:"M12 12c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z"},null,-1)]),8,Xa))])):(E(),j("video",{key:0,ref_for:!0,ref:pe=>{pe&&(lr.value[de]=pe),pe&&I&&(pe.srcObject=I)},autoplay:"",playsinline:"",class:"w-full h-full object-cover"},null,512)),je(de)?(E(),j("div",Za,x[36]||(x[36]=[d("i",{class:"fas fa-microphone-slash text-white text-xs"},null,-1)]))):N("",!0),ee.value?(E(),j("div",Qa,[d("button",{onClick:pe=>dt(de),class:"bg-red-600 text-white p-1.5 rounded-full hover:bg-red-700 text-xs",title:"Remove participant"},x[37]||(x[37]=[d("i",{class:"fas fa-user-times"},null,-1)]),8,el)])):N("",!0)]),d("div",tl,[d("div",rl,w(Ee(de)),1)])]))),128)),(E(!0),j(ye,null,Re(Math.max(0,9-Object.keys($(r).remoteStreams).length),I=>(E(),j("div",{key:`empty-${I}`,class:"flex flex-col"},x[38]||(x[38]=[Ar('<div class="relative bg-gray-800 rounded-xl overflow-hidden shadow-lg aspect-square border border-gray-300 dark:border-gray-600 flex items-center justify-center"><svg class="w-12 h-12 text-gray-500" fill="currentColor" viewBox="0 0 24 24" aria-label="Empty participant slot"><circle cx="12" cy="8" r="3"></circle><path d="M12 12c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z"></path></svg></div><div class="text-center mt-2"><div class="text-sm font-semibold text-gray-900 dark:text-white"> &lt;name&gt; </div></div>',2)])))),128))])])]),Ie.value.length>0?(E(),j("div",nl,[x[39]||(x[39]=d("h3",{class:"text-lg font-semibold text-gray-800 dark:text-white mb-2"},"Active Extensions",-1)),(E(!0),j(ye,null,Re(Ie.value,I=>(E(),j("div",{key:I.id,class:"p-2 border rounded mb-2 bg-gray-50 dark:bg-gray-800"},[(E(),He(cs(Le(I.entryPoint||I.id)),{config:I.config,meetingId:g.value,userId:y.value,isAdmin:ee.value,meetingContext:{meetingId:g.value,userId:y.value,userName:C.value.value,meetingConfigStore:$(a),meetingStore:$(r)}},null,8,["config","meetingId","userId","isAdmin","meetingContext"])),Le(I.entryPoint||I.id)?N("",!0):(E(),j("div",il,[d("p",sl,w(I.name),1),d("p",ol,"Cannot load UI for this extension (entry point: "+w(I.entryPoint||"not defined")+").",1)]))]))),128))])):N("",!0)]),ee.value&&se.value&&Pe.value.length>0?(E(),j("div",al,[d("div",{class:"p-3 border-b border-gray-200 dark:border-gray-700 flex justify-between items-center"},[x[41]||(x[41]=d("h2",{class:"font-semibold text-gray-900 dark:text-white"},"Join Requests",-1)),d("button",{onClick:Bt,class:"text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"},x[40]||(x[40]=[d("i",{class:"fas fa-times"},null,-1)]))]),d("div",ll,[(E(!0),j(ye,null,Re(Pe.value,I=>(E(),j("div",{key:I.id,class:"mb-4 p-3 bg-gray-100 dark:bg-gray-700 rounded-lg"},[d("div",cl,w(I.userName),1),d("div",ul,"Requested at "+w(Ze(I.timestamp)),1),d("div",dl,[d("button",{onClick:de=>ht(I.id),class:"btn btn-primary text-sm py-1 px-3"},"Approve",8,fl),d("button",{onClick:de=>Nt(I.id),class:"btn btn-outline text-sm py-1 px-3"},"Deny",8,pl)])]))),128))])])):N("",!0),te.value.breakoutRooms?(E(),j("div",hl,[d("div",gl,[x[43]||(x[43]=d("h2",{class:"font-semibold text-gray-900 dark:text-white"},"Breakout Rooms",-1)),d("button",{onClick:x[3]||(x[3]=I=>$(a).toggleFeature("breakoutRooms")),class:"text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"},x[42]||(x[42]=[d("i",{class:"fas fa-times"},null,-1)]))]),d("div",ml,[Ae(D,{meetingId:g.value,userId:y.value,isHost:ee.value,participants:$(r).participants},null,8,["meetingId","userId","isHost","participants"])])])):N("",!0),te.value.virtualBackground?(E(),j("div",vl,[d("div",yl,[x[45]||(x[45]=d("h2",{class:"font-semibold text-gray-900 dark:text-white"},"Virtual Background",-1)),d("button",{onClick:x[4]||(x[4]=I=>$(a).toggleFeature("virtualBackground")),class:"text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"},x[44]||(x[44]=[d("i",{class:"fas fa-times"},null,-1)]))]),d("div",bl,[Ae(Ao,{stream:$(r).localStream,currentBackground:"none",onApply:Xe,onCancel:x[5]||(x[5]=I=>$(a).toggleFeature("virtualBackground"))},null,8,["stream"])])])):N("",!0),te.value.whiteboard?(E(),j("div",xl,[d("div",{class:"p-3 border-b border-gray-200 dark:border-gray-700 flex justify-between items-center"},[x[47]||(x[47]=d("h2",{class:"font-semibold text-gray-900 dark:text-white"},"Whiteboard",-1)),d("button",{onClick:be,class:"text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"},x[46]||(x[46]=[d("i",{class:"fas fa-times"},null,-1)]))]),d("div",kl,[Ae(De,{meetingId:g.value,userId:y.value,isHost:ee.value},null,8,["meetingId","userId","isHost"])])])):N("",!0),Ue.value?(E(),He(Qo,{key:4,activities:$(a).getLinkSharingActivities,linkSharingPolicy:F.value,participants:$(r).participants,onClose:x[6]||(x[6]=I=>Ue.value=!1),onUpdatePolicy:Mt,onShowInvite:x[7]||(x[7]=I=>oe.value=!0)},null,8,["activities","linkSharingPolicy","participants"])):N("",!0),Fe.value&&te.value.chat?(E(),j("div",Sl,[d("div",Cl,[x[49]||(x[49]=d("h2",{class:"font-semibold text-gray-900 dark:text-white"},"Chat",-1)),d("button",{onClick:x[8]||(x[8]=I=>Fe.value=!1),class:"text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"},x[48]||(x[48]=[d("i",{class:"fas fa-times"},null,-1)]))]),d("div",{class:"flex-1 p-3 overflow-y-auto",ref_key:"chatMessagesContainer",ref:Ne},[(E(!0),j(ye,null,Re($(r).messages,I=>(E(),j("div",{key:I.id,class:"mb-3"},[d("div",Rl,[d("div",{class:W(["flex-1 bg-gray-100 dark:bg-gray-700 rounded-lg px-3 py-2 text-sm",{"bg-primary-light text-white":I.userId===y.value,"ml-auto":I.userId===y.value}])},[d("div",Tl,w(I.userId===y.value?"You":I.userName),1),d("div",null,w(I.message),1)],2)]),d("div",{class:W(["text-xs text-gray-500 dark:text-gray-400 mt-1",{"text-right":I.userId===y.value}])},w(Ze(I.timestamp)),3)]))),128))],512),d("div",Pl,[d("form",{onSubmit:Pn(ut,["prevent"]),class:"flex"},[Tt(d("input",{"onUpdate:modelValue":x[9]||(x[9]=I=>ge.value=I),type:"text",placeholder:"Type a message...",class:"input flex-1 mr-2"},null,512),[[tr,ge.value]]),d("button",{type:"submit",class:"btn btn-primary",disabled:!ge.value.trim()},"Send",8,El)],32)])])):N("",!0)]),te.value.reactions?(E(),He(fe,{key:0,meetingId:g.value},null,8,["meetingId"])):N("",!0),te.value.audibleImpairedSystem?(E(),j("div",jl,[Ae(mt,{meetingId:g.value,currentUserId:y.value,currentUserName:C.value.value,localStream:$(r).localStream},null,8,["meetingId","currentUserId","currentUserName","localStream"])])):N("",!0),te.value.recording?(E(),j("div",Ml,[Ae(zt,{meetingId:g.value,userId:y.value,stream:$(r).localStream,onRecordingStarted:x[10]||(x[10]=I=>$(r).isRecording=!0),onRecordingStopped:x[11]||(x[11]=I=>$(r).isRecording=!1)},null,8,["meetingId","userId","stream"])])):N("",!0),te.value.reactions?(E(),j("div",Al,[Ae(Qe,{meetingId:g.value,userId:y.value,userName:C.value.value},null,8,["meetingId","userId","userName"])])):N("",!0),Ae(bo,{isMicMuted:$(r).isMicMuted,isCameraOff:$(r).isCameraOff,isScreenSharing:$(r).isScreenSharing,isChatOpen:Fe.value,isChatEnabled:te.value.chat,isAISEnabled:te.value.audibleImpairedSystem,isReactionsEnabled:te.value.reactions,isBreakoutRoomsEnabled:te.value.breakoutRooms,isVirtualBackgroundEnabled:te.value.virtualBackground,isWhiteboardEnabled:gt.value,isRecordingEnabled:Vt.value,isRecordingActive:$(r).isRecording,hasJoinRequests:ee.value&&Pe.value.length>0,showJoinRequestsPanel:se.value,joinRequestsCount:Pe.value.length,isAdmin:ee.value,canShareLink:$(a).canUserShareLink(y.value),linkSharingPolicy:F.value,onToggleMic:Dt,onToggleCamera:$t,onToggleScreen:wt,onToggleChat:x[12]||(x[12]=I=>Fe.value=!Fe.value),onToggleAis:x[13]||(x[13]=I=>$(a).toggleFeature("audibleImpairedSystem")),onToggleReactions:x[14]||(x[14]=I=>$(a).toggleFeature("reactions")),onToggleBreakoutRooms:x[15]||(x[15]=I=>$(a).toggleFeature("breakoutRooms")),onToggleVirtualBackground:x[16]||(x[16]=I=>$(a).toggleFeature("virtualBackground")),onToggleWhiteboard:be,onToggleRecording:x[17]||(x[17]=I=>$(a).toggleFeature("recording")),onToggleJoinRequests:Bt,onShowInvite:x[18]||(x[18]=I=>oe.value=!0),onShowLinkSharing:x[19]||(x[19]=I=>Ue.value=!0),onLeaveMeeting:ft},null,8,["isMicMuted","isCameraOff","isScreenSharing","isChatOpen","isChatEnabled","isAISEnabled","isReactionsEnabled","isBreakoutRoomsEnabled","isVirtualBackgroundEnabled","isWhiteboardEnabled","isRecordingEnabled","isRecordingActive","hasJoinRequests","showJoinRequestsPanel","joinRequestsCount","isAdmin","canShareLink","linkSharingPolicy"])])),at.value?(E(),j("div",Il,[d("div",_l,[x[50]||(x[50]=d("h2",{class:"text-xl font-semibold mb-4 text-gray-900 dark:text-white"},"Leave Meeting",-1)),x[51]||(x[51]=d("p",{class:"mb-4 text-gray-700 dark:text-gray-300"},"Please provide a reason for leaving the meeting:",-1)),Tt(d("textarea",{"onUpdate:modelValue":x[20]||(x[20]=I=>Oe.value=I),class:"w-full p-2 border border-gray-300 dark:border-gray-600 rounded mb-4 bg-white dark:bg-gray-700 text-gray-900 dark:text-white",placeholder:"Reason for leaving (optional)",rows:"3"},null,512),[[tr,Oe.value]]),d("div",Ol,[d("button",{onClick:x[21]||(x[21]=I=>at.value=!1),class:"px-4 py-2 bg-gray-200 dark:bg-gray-700 text-gray-800 dark:text-gray-200 rounded hover:bg-gray-300 dark:hover:bg-gray-600"},"Cancel"),d("button",{onClick:dr,class:"px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700"},"Leave Meeting")])])])):N("",!0),Be.value?(E(),j("div",Ll,[d("div",Dl,[x[54]||(x[54]=d("h2",{class:"text-xl font-semibold mb-4 text-gray-900 dark:text-white"},"Remove Participant",-1)),d("p",$l,[x[52]||(x[52]=J(" You are about to remove ")),d("span",wl,w(ue.value?Ee(ue.value):""),1),x[53]||(x[53]=J(" from the meeting. "))]),x[55]||(x[55]=d("p",{class:"mb-4 text-gray-700 dark:text-gray-300"},"Please provide a reason (visible only to the host):",-1)),Tt(d("textarea",{"onUpdate:modelValue":x[22]||(x[22]=I=>_e.value=I),class:"w-full p-2 border border-gray-300 dark:border-gray-600 rounded mb-4 bg-white dark:bg-gray-700 text-gray-900 dark:text-white",placeholder:"Reason for removal (optional)",rows:"3"},null,512),[[tr,_e.value]]),d("div",Fl,[d("button",{onClick:x[23]||(x[23]=I=>Be.value=!1),class:"px-4 py-2 bg-gray-200 dark:bg-gray-700 text-gray-800 dark:text-gray-200 rounded hover:bg-gray-300 dark:hover:bg-gray-600"},"Cancel"),d("button",{onClick:ur,class:"px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700"},"Remove")])])])):N("",!0),Ae(va,{onAction:Lt})],64)}}};export{zl as default};
