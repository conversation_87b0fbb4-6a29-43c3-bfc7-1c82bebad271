{"version": 3, "file": "index.esm5.js", "sources": ["../src/core/version.ts", "../src/core/AppCheckTokenProvider.ts", "../src/core/error.ts", "../src/logger.ts", "../src/core/FirebaseAuthProvider.ts", "../src/api/Reference.ts", "../src/util/encoder.ts", "../src/util/map.ts", "../src/core/QueryManager.ts", "../src/util/url.ts", "../src/network/fetch.ts", "../src/network/transport/rest.ts", "../src/api/Mutation.ts", "../src/api/DataConnect.ts", "../src/register.ts", "../src/api/query.ts", "../src/util/validateArgs.ts", "../src/api.browser.ts", "../src/index.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2024 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n/** The semver (www.semver.org) version of the SDK. */\nexport let SDK_VERSION = '';\n\n/**\n * SDK_VERSION should be set before any database instance is created\n * @internal\n */\nexport function setSDKVersion(version: string): void {\n  SDK_VERSION = version;\n}\n", "/**\n * @license\n * Copyright 2024 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {\n  AppCheckInternalComponentName,\n  AppCheckTokenListener,\n  AppCheckTokenResult,\n  FirebaseAppCheckInternal\n} from '@firebase/app-check-interop-types';\nimport { Provider } from '@firebase/component';\n\n/**\n * @internal\n * Abstraction around AppChe<PERSON>'s token fetching capabilities.\n */\nexport class AppCheckTokenProvider {\n  private appCheck?: FirebaseAppCheckInternal;\n  constructor(\n    private appName_: string,\n    private appCheckProvider?: Provider<AppCheckInternalComponentName>\n  ) {\n    this.appCheck = appCheckProvider?.getImmediate({ optional: true });\n    if (!this.appCheck) {\n      void appCheckProvider\n        ?.get()\n        .then(appCheck => (this.appCheck = appCheck))\n        .catch();\n    }\n  }\n\n  getToken(forceRefresh?: boolean): Promise<AppCheckTokenResult> {\n    if (!this.appCheck) {\n      return new Promise<AppCheckTokenResult>((resolve, reject) => {\n        // Support delayed initialization of FirebaseAppCheck. This allows our\n        // customers to initialize the RTDB SDK before initializing Firebase\n        // AppCheck and ensures that all requests are authenticated if a token\n        // becomes available before the timoeout below expires.\n        setTimeout(() => {\n          if (this.appCheck) {\n            this.getToken(forceRefresh).then(resolve, reject);\n          } else {\n            resolve(null);\n          }\n        }, 0);\n      });\n    }\n    return this.appCheck.getToken(forceRefresh);\n  }\n\n  addTokenChangeListener(listener: AppCheckTokenListener): void {\n    void this.appCheckProvider\n      ?.get()\n      .then(appCheck => appCheck.addTokenListener(listener));\n  }\n}\n", "/**\n * @license\n * Copyright 2024 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { FirebaseError } from '@firebase/util';\n\nexport type DataConnectErrorCode =\n  | 'other'\n  | 'already-initialized'\n  | 'not-initialized'\n  | 'not-supported'\n  | 'invalid-argument'\n  | 'partial-error'\n  | 'unauthorized';\n\nexport type Code = DataConnectErrorCode;\n\nexport const Code = {\n  OTHER: 'other' as DataConnectErrorCode,\n  ALREADY_INITIALIZED: 'already-initialized' as DataConnectErrorCode,\n  NOT_INITIALIZED: 'not-initialized' as DataConnect<PERSON><PERSON>r<PERSON>ode,\n  NOT_SUPPORTED: 'not-supported' as DataConnect<PERSON>rrorCode,\n  INVALID_ARGUMENT: 'invalid-argument' as DataConnectErrorCode,\n  PARTIAL_ERROR: 'partial-error' as DataConnectErrorCode,\n  UNAUTHORIZED: 'unauthorized' as DataConnectErrorCode\n};\n\n/** An error returned by a DataConnect operation. */\nexport class DataConnectError extends FirebaseError {\n  /** The stack of the error. */\n  readonly stack?: string;\n\n  /** @hideconstructor */\n  constructor(\n    /**\n     * The backend error code associated with this error.\n     */\n    readonly code: DataConnectErrorCode,\n    /**\n     * A custom error description.\n     */\n    readonly message: string\n  ) {\n    super(code, message);\n\n    // HACK: We write a toString property directly because Error is not a real\n    // class and so inheritance does not work correctly. We could alternatively\n    // do the same \"back-door inheritance\" trick that FirebaseError does.\n    this.toString = () => `${this.name}: [code=${this.code}]: ${this.message}`;\n  }\n}\n", "/**\n * @license\n * Copyright 2024 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nimport { Logger, LogLevelString } from '@firebase/logger';\n\nimport { SDK_VERSION } from './core/version';\n\nconst logger = new Logger('@firebase/data-connect');\nexport function setLogLevel(logLevel: LogLevelString): void {\n  logger.setLogLevel(logLevel);\n}\nexport function logDebug(msg: string): void {\n  logger.debug(`DataConnect (${SDK_VERSION}): ${msg}`);\n}\n\nexport function logError(msg: string): void {\n  logger.error(`DataConnect (${SDK_VERSION}): ${msg}`);\n}\n", "/**\n * @license\n * Copyright 2024 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { FirebaseOptions } from '@firebase/app-types';\nimport {\n  FirebaseAuthInternal,\n  FirebaseAuthInternalName,\n  FirebaseAuthTokenData\n} from '@firebase/auth-interop-types';\nimport { Provider } from '@firebase/component';\n\nimport { logDebug, logError } from '../logger';\n\n// @internal\nexport interface AuthTokenProvider {\n  getToken(forceRefresh: boolean): Promise<FirebaseAuthTokenData | null>;\n  addTokenChangeListener(listener: AuthTokenListener): void;\n}\nexport type AuthTokenListener = (token: string | null) => void;\n\n// @internal\nexport class FirebaseAuthProvider implements AuthTokenProvider {\n  private _auth: FirebaseAuthInternal;\n  constructor(\n    private _appName: string,\n    private _options: FirebaseOptions,\n    private _authProvider: Provider<FirebaseAuthInternalName>\n  ) {\n    this._auth = _authProvider.getImmediate({ optional: true })!;\n    if (!this._auth) {\n      _authProvider.onInit(auth => (this._auth = auth));\n    }\n  }\n  getToken(forceRefresh: boolean): Promise<FirebaseAuthTokenData | null> {\n    if (!this._auth) {\n      return new Promise((resolve, reject) => {\n        setTimeout(() => {\n          if (this._auth) {\n            this.getToken(forceRefresh).then(resolve, reject);\n          } else {\n            resolve(null);\n          }\n        }, 0);\n      });\n    }\n    return this._auth.getToken(forceRefresh).catch(error => {\n      if (error && error.code === 'auth/token-not-initialized') {\n        logDebug(\n          'Got auth/token-not-initialized error.  Treating as null token.'\n        );\n        return null;\n      } else {\n        logError(\n          'Error received when attempting to retrieve token: ' +\n            JSON.stringify(error)\n        );\n        return Promise.reject(error);\n      }\n    });\n  }\n  addTokenChangeListener(listener: AuthTokenListener): void {\n    this._auth?.addAuthTokenListener(listener);\n  }\n  removeTokenChangeListener(listener: (token: string | null) => void): void {\n    this._authProvider\n      .get()\n      .then(auth => auth.removeAuthTokenListener(listener))\n      .catch(err => logError(err));\n  }\n}\n", "/**\n * @license\n * Copyright 2024 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { DataConnect, DataConnectOptions } from './DataConnect';\nexport const QUERY_STR = 'query';\nexport const MUTATION_STR = 'mutation';\nexport type ReferenceType = typeof QUERY_STR | typeof MUTATION_STR;\n\nexport const SOURCE_SERVER = 'SERVER';\nexport const SOURCE_CACHE = 'CACHE';\nexport type DataSource = typeof SOURCE_CACHE | typeof SOURCE_SERVER;\n\nexport interface OpResult<Data> {\n  data: Data;\n  source: DataSource;\n  fetchTime: string;\n}\n\nexport interface OperationRef<_Data, Variables> {\n  name: string;\n  variables: Variables;\n  refType: ReferenceType;\n  dataConnect: DataConnect;\n}\n\nexport interface DataConnectResult<Data, Variables> extends OpResult<Data> {\n  ref: OperationRef<Data, Variables>;\n  // future metadata\n}\n\n/**\n * Serialized RefInfo as a result of `QueryResult.toJSON().refInfo`\n */\nexport interface RefInfo<Variables> {\n  name: string;\n  variables: Variables;\n  connectorConfig: DataConnectOptions;\n}\n/**\n * Serialized Ref as a result of `QueryResult.toJSON()`\n */\nexport interface SerializedRef<Data, Variables> extends OpResult<Data> {\n  refInfo: RefInfo<Variables>;\n}\n", "/**\n * @license\n * Copyright 2024 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nexport type HmacImpl = (obj: unknown) => string;\nexport let encoderImpl: HmacImpl;\nexport function setEncoder(encoder: HmacImpl): void {\n  encoderImpl = encoder;\n}\nsetEncoder(o => JSON.stringify(o));\n", "/**\n * @license\n * Copyright 2024 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nexport function setIfNotExists<T>(\n  map: Map<string, T>,\n  key: string,\n  val: T\n): void {\n  if (!map.has(key)) {\n    map.set(key, val);\n  }\n}\n", "/**\n * @license\n * Copyright 2024 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {\n  DataConnectSubscription,\n  OnErrorSubscription,\n  OnResultSubscription,\n  QueryPromise,\n  QueryRef,\n  QueryResult\n} from '../api/query';\nimport {\n  OperationRef,\n  QUERY_STR,\n  OpResult,\n  SerializedRef,\n  SOURCE_SERVER,\n  DataSource,\n  SOURCE_CACHE\n} from '../api/Reference';\nimport { logDebug } from '../logger';\nimport { DataConnectTransport } from '../network';\nimport { encoderImpl } from '../util/encoder';\nimport { setIfNotExists } from '../util/map';\n\nimport { DataConnectError } from './error';\n\ninterface TrackedQuery<Data, Variables> {\n  ref: Omit<OperationRef<Data, Variables>, 'dataConnect'>;\n  subscriptions: Array<DataConnectSubscription<Data, Variables>>;\n  currentCache: OpResult<Data> | null;\n  lastError: DataConnectError | null;\n}\n\nfunction getRefSerializer<Data, Variables>(\n  queryRef: QueryRef<Data, Variables>,\n  data: Data,\n  source: DataSource\n) {\n  return function toJSON(): SerializedRef<Data, Variables> {\n    return {\n      data,\n      refInfo: {\n        name: queryRef.name,\n        variables: queryRef.variables,\n        connectorConfig: {\n          projectId: queryRef.dataConnect.app.options.projectId!,\n          ...queryRef.dataConnect.getSettings()\n        }\n      },\n      fetchTime: Date.now().toLocaleString(),\n      source\n    };\n  };\n}\n\nexport class QueryManager {\n  _queries: Map<string, TrackedQuery<unknown, unknown>>;\n  constructor(private transport: DataConnectTransport) {\n    this._queries = new Map();\n  }\n  track<Data, Variables>(\n    queryName: string,\n    variables: Variables,\n    initialCache?: OpResult<Data>\n  ): TrackedQuery<Data, Variables> {\n    const ref: TrackedQuery<Data, Variables>['ref'] = {\n      name: queryName,\n      variables,\n      refType: QUERY_STR\n    };\n    const key = encoderImpl(ref);\n    const newTrackedQuery: TrackedQuery<Data, Variables> = {\n      ref,\n      subscriptions: [],\n      currentCache: initialCache || null,\n      lastError: null\n    };\n    // @ts-ignore\n    setIfNotExists(this._queries, key, newTrackedQuery);\n    return this._queries.get(key) as TrackedQuery<Data, Variables>;\n  }\n  addSubscription<Data, Variables>(\n    queryRef: OperationRef<Data, Variables>,\n    onResultCallback: OnResultSubscription<Data, Variables>,\n    onErrorCallback?: OnErrorSubscription,\n    initialCache?: OpResult<Data>\n  ): () => void {\n    const key = encoderImpl({\n      name: queryRef.name,\n      variables: queryRef.variables,\n      refType: QUERY_STR\n    });\n    const trackedQuery = this._queries.get(key) as TrackedQuery<\n      Data,\n      Variables\n    >;\n    const subscription = {\n      userCallback: onResultCallback,\n      errCallback: onErrorCallback\n    };\n    const unsubscribe = (): void => {\n      const trackedQuery = this._queries.get(key)!;\n      trackedQuery.subscriptions = trackedQuery.subscriptions.filter(\n        sub => sub !== subscription\n      );\n    };\n    if (initialCache && trackedQuery.currentCache !== initialCache) {\n      logDebug('Initial cache found. Comparing dates.');\n      if (\n        !trackedQuery.currentCache ||\n        (trackedQuery.currentCache &&\n          compareDates(\n            trackedQuery.currentCache.fetchTime,\n            initialCache.fetchTime\n          ))\n      ) {\n        trackedQuery.currentCache = initialCache;\n      }\n    }\n    if (trackedQuery.currentCache !== null) {\n      const cachedData = trackedQuery.currentCache.data;\n      onResultCallback({\n        data: cachedData,\n        source: SOURCE_CACHE,\n        ref: queryRef as QueryRef<Data, Variables>,\n        toJSON: getRefSerializer(\n          queryRef as QueryRef<Data, Variables>,\n          trackedQuery.currentCache.data,\n          SOURCE_CACHE\n        ),\n        fetchTime: trackedQuery.currentCache.fetchTime\n      });\n      if (trackedQuery.lastError !== null && onErrorCallback) {\n        onErrorCallback(undefined);\n      }\n    }\n\n    trackedQuery.subscriptions.push({\n      userCallback: onResultCallback,\n      errCallback: onErrorCallback,\n      unsubscribe\n    });\n    if (!trackedQuery.currentCache) {\n      logDebug(\n        `No cache available for query ${\n          queryRef.name\n        } with variables ${JSON.stringify(\n          queryRef.variables\n        )}. Calling executeQuery.`\n      );\n      const promise = this.executeQuery(queryRef as QueryRef<Data, Variables>);\n      // We want to ignore the error and let subscriptions handle it\n      promise.then(undefined, err => {});\n    }\n    return unsubscribe;\n  }\n  executeQuery<Data, Variables>(\n    queryRef: QueryRef<Data, Variables>\n  ): QueryPromise<Data, Variables> {\n    const key = encoderImpl({\n      name: queryRef.name,\n      variables: queryRef.variables,\n      refType: QUERY_STR\n    });\n    const trackedQuery = this._queries.get(key)!;\n    const result = this.transport.invokeQuery<Data, Variables>(\n      queryRef.name,\n      queryRef.variables\n    );\n    const newR = result.then(\n      res => {\n        const fetchTime = new Date().toString();\n        const result: QueryResult<Data, Variables> = {\n          ...res,\n          source: SOURCE_SERVER,\n          ref: queryRef,\n          toJSON: getRefSerializer(queryRef, res.data, SOURCE_SERVER),\n          fetchTime\n        };\n        trackedQuery.subscriptions.forEach(subscription => {\n          subscription.userCallback(result);\n        });\n        trackedQuery.currentCache = {\n          data: res.data,\n          source: SOURCE_CACHE,\n          fetchTime\n        };\n        return result;\n      },\n      err => {\n        trackedQuery.lastError = err;\n        trackedQuery.subscriptions.forEach(subscription => {\n          if (subscription.errCallback) {\n            subscription.errCallback(err);\n          }\n        });\n        throw err;\n      }\n    );\n\n    return newR;\n  }\n  enableEmulator(host: string, port: number): void {\n    this.transport.useEmulator(host, port);\n  }\n}\nfunction compareDates(str1: string, str2: string): boolean {\n  const date1 = new Date(str1);\n  const date2 = new Date(str2);\n  return date1.getTime() < date2.getTime();\n}\n", "/**\n * @license\n * Copyright 2024 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { DataConnectOptions, TransportOptions } from '../api/DataConnect';\nimport { Code, DataConnectError } from '../core/error';\nimport { logError } from '../logger';\n\nexport function urlBuilder(\n  projectConfig: DataConnectOptions,\n  transportOptions: TransportOptions\n): string {\n  const { connector, location, projectId: project, service } = projectConfig;\n  const { host, sslEnabled, port } = transportOptions;\n  const protocol = sslEnabled ? 'https' : 'http';\n  const realHost = host || `firebasedataconnect.googleapis.com`;\n  let baseUrl = `${protocol}://${realHost}`;\n  if (typeof port === 'number') {\n    baseUrl += `:${port}`;\n  } else if (typeof port !== 'undefined') {\n    logError('Port type is of an invalid type');\n    throw new DataConnectError(\n      Code.INVALID_ARGUMENT,\n      'Incorrect type for port passed in!'\n    );\n  }\n  return `${baseUrl}/v1beta/projects/${project}/locations/${location}/services/${service}/connectors/${connector}`;\n}\nexport function addToken(url: string, apiKey?: string): string {\n  if (!apiKey) {\n    return url;\n  }\n  const newUrl = new URL(url);\n  newUrl.searchParams.append('key', apiKey);\n  return newUrl.toString();\n}\n", "/**\n * @license\n * Copyright 2024 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { Code, DataConnectError } from '../core/error';\nimport { SDK_VERSION } from '../core/version';\nimport { logDebug, logError } from '../logger';\n\nlet connectFetch: typeof fetch | null = globalThis.fetch;\nexport function initializeFetch(fetchImpl: typeof fetch): void {\n  connectFetch = fetchImpl;\n}\nfunction getGoogApiClientValue(_isUsingGen: boolean): string {\n  let str = 'gl-js/ fire/' + SDK_VERSION;\n  if (_isUsingGen) {\n    str += ' web/gen';\n  }\n  return str;\n}\nexport function dcFetch<T, U>(\n  url: string,\n  body: U,\n  { signal }: AbortController,\n  appId: string | null,\n  accessToken: string | null,\n  appCheckToken: string | null,\n  _isUsingGen: boolean\n): Promise<{ data: T; errors: Error[] }> {\n  if (!connectFetch) {\n    throw new DataConnectError(Code.OTHER, 'No Fetch Implementation detected!');\n  }\n  const headers: HeadersInit = {\n    'Content-Type': 'application/json',\n    'X-Goog-Api-Client': getGoogApiClientValue(_isUsingGen)\n  };\n  if (accessToken) {\n    headers['X-Firebase-Auth-Token'] = accessToken;\n  }\n  if (appId) {\n    headers['x-firebase-gmpid'] = appId;\n  }\n  if (appCheckToken) {\n    headers['X-Firebase-AppCheck'] = appCheckToken;\n  }\n  const bodyStr = JSON.stringify(body);\n  logDebug(`Making request out to ${url} with body: ${bodyStr}`);\n\n  return connectFetch(url, {\n    body: bodyStr,\n    method: 'POST',\n    headers,\n    signal\n  })\n    .catch(err => {\n      throw new DataConnectError(\n        Code.OTHER,\n        'Failed to fetch: ' + JSON.stringify(err)\n      );\n    })\n    .then(async response => {\n      let jsonResponse = null;\n      try {\n        jsonResponse = await response.json();\n      } catch (e) {\n        throw new DataConnectError(Code.OTHER, JSON.stringify(e));\n      }\n      const message = getMessage(jsonResponse);\n      if (response.status >= 400) {\n        logError(\n          'Error while performing request: ' + JSON.stringify(jsonResponse)\n        );\n        if (response.status === 401) {\n          throw new DataConnectError(Code.UNAUTHORIZED, message);\n        }\n        throw new DataConnectError(Code.OTHER, message);\n      }\n      return jsonResponse;\n    })\n    .then(res => {\n      if (res.errors && res.errors.length) {\n        const stringified = JSON.stringify(res.errors);\n        logError('DataConnect error while performing request: ' + stringified);\n        throw new DataConnectError(Code.OTHER, stringified);\n      }\n      return res as { data: T; errors: Error[] };\n    });\n}\ninterface MessageObject {\n  message?: string;\n}\nfunction getMessage(obj: MessageObject): string {\n  if ('message' in obj) {\n    return obj.message;\n  }\n  return JSON.stringify(obj);\n}\n", "/**\n * @license\n * Copyright 2024 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { DataConnectOptions, TransportOptions } from '../../api/DataConnect';\nimport { AppCheckTokenProvider } from '../../core/AppCheckTokenProvider';\nimport { DataConnectError, Code } from '../../core/error';\nimport { AuthTokenProvider } from '../../core/FirebaseAuthProvider';\nimport { logDebug } from '../../logger';\nimport { addToken, urlBuilder } from '../../util/url';\nimport { dcFetch } from '../fetch';\n\nimport { DataConnectTransport } from '.';\n\nexport class RESTTransport implements DataConnectTransport {\n  private _host = '';\n  private _port: number | undefined;\n  private _location = 'l';\n  private _connectorName = '';\n  private _secure = true;\n  private _project = 'p';\n  private _serviceName: string;\n  private _accessToken: string | null = null;\n  private _appCheckToken: string | null = null;\n  private _lastToken: string | null = null;\n  constructor(\n    options: DataConnectOptions,\n    private apiKey?: string | undefined,\n    private appId?: string,\n    private authProvider?: AuthTokenProvider | undefined,\n    private appCheckProvider?: AppCheckTokenProvider | undefined,\n    transportOptions?: TransportOptions | undefined,\n    private _isUsingGen = false\n  ) {\n    if (transportOptions) {\n      if (typeof transportOptions.port === 'number') {\n        this._port = transportOptions.port;\n      }\n      if (typeof transportOptions.sslEnabled !== 'undefined') {\n        this._secure = transportOptions.sslEnabled;\n      }\n      this._host = transportOptions.host;\n    }\n    const { location, projectId: project, connector, service } = options;\n    if (location) {\n      this._location = location;\n    }\n    if (project) {\n      this._project = project;\n    }\n    this._serviceName = service;\n    if (!connector) {\n      throw new DataConnectError(\n        Code.INVALID_ARGUMENT,\n        'Connector Name required!'\n      );\n    }\n    this._connectorName = connector;\n    this.authProvider?.addTokenChangeListener(token => {\n      logDebug(`New Token Available: ${token}`);\n      this._accessToken = token;\n    });\n    this.appCheckProvider?.addTokenChangeListener(result => {\n      const { token } = result;\n      logDebug(`New App Check Token Available: ${token}`);\n      this._appCheckToken = token;\n    });\n  }\n  get endpointUrl(): string {\n    return urlBuilder(\n      {\n        connector: this._connectorName,\n        location: this._location,\n        projectId: this._project,\n        service: this._serviceName\n      },\n      { host: this._host, sslEnabled: this._secure, port: this._port }\n    );\n  }\n  useEmulator(host: string, port?: number, isSecure?: boolean): void {\n    this._host = host;\n    if (typeof port === 'number') {\n      this._port = port;\n    }\n    if (typeof isSecure !== 'undefined') {\n      this._secure = isSecure;\n    }\n  }\n  onTokenChanged(newToken: string | null): void {\n    this._accessToken = newToken;\n  }\n\n  async getWithAuth(forceToken = false): Promise<string> {\n    let starterPromise: Promise<string | null> = new Promise(resolve =>\n      resolve(this._accessToken)\n    );\n    if (this.appCheckProvider) {\n      this._appCheckToken = (await this.appCheckProvider.getToken())?.token;\n    }\n    if (this.authProvider) {\n      starterPromise = this.authProvider\n        .getToken(/*forceToken=*/ forceToken)\n        .then(data => {\n          if (!data) {\n            return null;\n          }\n          this._accessToken = data.accessToken;\n          return this._accessToken;\n        });\n    } else {\n      starterPromise = new Promise(resolve => resolve(''));\n    }\n    return starterPromise;\n  }\n\n  _setLastToken(lastToken: string | null): void {\n    this._lastToken = lastToken;\n  }\n\n  withRetry<T>(\n    promiseFactory: () => Promise<{ data: T; errors: Error[] }>,\n    retry = false\n  ): Promise<{ data: T; errors: Error[] }> {\n    let isNewToken = false;\n    return this.getWithAuth(retry)\n      .then(res => {\n        isNewToken = this._lastToken !== res;\n        this._lastToken = res;\n        return res;\n      })\n      .then(promiseFactory)\n      .catch(err => {\n        // Only retry if the result is unauthorized and the last token isn't the same as the new one.\n        if (\n          'code' in err &&\n          err.code === Code.UNAUTHORIZED &&\n          !retry &&\n          isNewToken\n        ) {\n          logDebug('Retrying due to unauthorized');\n          return this.withRetry(promiseFactory, true);\n        }\n        throw err;\n      });\n  }\n\n  // TODO(mtewani): Update U to include shape of body defined in line 13.\n  invokeQuery: <T, U>(\n    queryName: string,\n    body?: U\n  ) => PromiseLike<{ data: T; errors: Error[] }> = <T, U = unknown>(\n    queryName: string,\n    body: U\n  ) => {\n    const abortController = new AbortController();\n    // TODO(mtewani): Update to proper value\n    const withAuth = this.withRetry(() =>\n      dcFetch<T, U>(\n        addToken(`${this.endpointUrl}:executeQuery`, this.apiKey),\n        {\n          name: `projects/${this._project}/locations/${this._location}/services/${this._serviceName}/connectors/${this._connectorName}`,\n          operationName: queryName,\n          variables: body\n        } as unknown as U, // TODO(mtewani): This is a patch, fix this.\n        abortController,\n        this.appId,\n        this._accessToken,\n        this._appCheckToken,\n        this._isUsingGen\n      )\n    );\n\n    return {\n      then: withAuth.then.bind(withAuth),\n      catch: withAuth.catch.bind(withAuth)\n    };\n  };\n  invokeMutation: <T, U>(\n    queryName: string,\n    body?: U\n  ) => PromiseLike<{ data: T; errors: Error[] }> = <T, U = unknown>(\n    mutationName: string,\n    body: U\n  ) => {\n    const abortController = new AbortController();\n    const taskResult = this.withRetry(() => {\n      return dcFetch<T, U>(\n        addToken(`${this.endpointUrl}:executeMutation`, this.apiKey),\n        {\n          name: `projects/${this._project}/locations/${this._location}/services/${this._serviceName}/connectors/${this._connectorName}`,\n          operationName: mutationName,\n          variables: body\n        } as unknown as U,\n        abortController,\n        this.appId,\n        this._accessToken,\n        this._appCheckToken,\n        this._isUsingGen\n      );\n    });\n\n    return {\n      then: taskResult.then.bind(taskResult),\n      // catch: taskResult.catch.bind(taskResult),\n      // finally: taskResult.finally.bind(taskResult),\n      cancel: () => abortController.abort()\n    };\n  };\n}\n", "/**\n * @license\n * Copyright 2024 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { DataConnectTransport } from '../network/transport';\n\nimport { DataConnect } from './DataConnect';\nimport {\n  DataConnectResult,\n  MUTATION_STR,\n  OperationRef,\n  SOURCE_SERVER\n} from './Reference';\n\nexport interface MutationRef<Data, Variables>\n  extends OperationRef<Data, Variables> {\n  refType: typeof MUTATION_STR;\n}\n\n/**\n * Creates a `MutationRef`\n * @param dcInstance Data Connect instance\n * @param mutationName name of mutation\n */\nexport function mutationRef<Data>(\n  dcInstance: DataConnect,\n  mutationName: string\n): MutationRef<Data, undefined>;\n/**\n *\n * @param dcInstance Data Connect instance\n * @param mutationName name of mutation\n * @param variables variables to send with mutation\n */\nexport function mutationRef<Data, Variables>(\n  dcInstance: DataConnect,\n  mutationName: string,\n  variables: Variables\n): MutationRef<Data, Variables>;\n/**\n *\n * @param dcInstance Data Connect instance\n * @param mutationName name of mutation\n * @param variables variables to send with mutation\n * @returns `MutationRef`\n */\nexport function mutationRef<Data, Variables>(\n  dcInstance: DataConnect,\n  mutationName: string,\n  variables?: Variables\n): MutationRef<Data, Variables> {\n  dcInstance.setInitialized();\n  const ref: MutationRef<Data, Variables> = {\n    dataConnect: dcInstance,\n    name: mutationName,\n    refType: MUTATION_STR,\n    variables: variables as Variables\n  };\n  return ref;\n}\n\n/**\n * @internal\n */\nexport class MutationManager {\n  private _inflight: Array<PromiseLike<unknown>> = [];\n  constructor(private _transport: DataConnectTransport) {}\n  executeMutation<Data, Variables>(\n    mutationRef: MutationRef<Data, Variables>\n  ): MutationPromise<Data, Variables> {\n    const result = this._transport.invokeMutation<Data, Variables>(\n      mutationRef.name,\n      mutationRef.variables\n    );\n    const withRefPromise = result.then(res => {\n      const obj: MutationResult<Data, Variables> = {\n        ...res, // Double check that the result is result.data, not just result\n        source: SOURCE_SERVER,\n        ref: mutationRef,\n        fetchTime: Date.now().toLocaleString()\n      };\n      return obj;\n    });\n    this._inflight.push(result);\n    const removePromise = (): Array<PromiseLike<unknown>> =>\n      (this._inflight = this._inflight.filter(promise => promise !== result));\n    result.then(removePromise, removePromise);\n    return withRefPromise;\n  }\n}\n\n/**\n * Mutation Result from `executeMutation`\n */\nexport interface MutationResult<Data, Variables>\n  extends DataConnectResult<Data, Variables> {\n  ref: MutationRef<Data, Variables>;\n}\n/**\n * Mutation return value from `executeMutation`\n */\nexport interface MutationPromise<Data, Variables>\n  extends PromiseLike<MutationResult<Data, Variables>> {\n  // reserved for special actions like cancellation\n}\n\n/**\n * Execute Mutation\n * @param mutationRef mutation to execute\n * @returns `MutationRef`\n */\nexport function executeMutation<Data, Variables>(\n  mutationRef: MutationRef<Data, Variables>\n): MutationPromise<Data, Variables> {\n  return mutationRef.dataConnect._mutationManager.executeMutation(mutationRef);\n}\n", "/**\n * @license\n * Copyright 2024 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {\n  FirebaseApp,\n  _getProvider,\n  _removeServiceInstance,\n  getApp\n} from '@firebase/app';\nimport { AppCheckInternalComponentName } from '@firebase/app-check-interop-types';\nimport { FirebaseAuthInternalName } from '@firebase/auth-interop-types';\nimport { Provider } from '@firebase/component';\n\nimport { AppCheckTokenProvider } from '../core/AppCheckTokenProvider';\nimport { Code, DataConnectError } from '../core/error';\nimport {\n  AuthTokenProvider,\n  FirebaseAuthProvider\n} from '../core/FirebaseAuthProvider';\nimport { QueryManager } from '../core/QueryManager';\nimport { logDebug, logError } from '../logger';\nimport { DataConnectTransport, TransportClass } from '../network';\nimport { RESTTransport } from '../network/transport/rest';\n\nimport { MutationManager } from './Mutation';\n\n/**\n * Connector Config for calling Data Connect backend.\n */\nexport interface ConnectorConfig {\n  location: string;\n  connector: string;\n  service: string;\n}\n\n/**\n * Options to connect to emulator\n */\nexport interface TransportOptions {\n  host: string;\n  sslEnabled?: boolean;\n  port?: number;\n}\n\nconst FIREBASE_DATA_CONNECT_EMULATOR_HOST_VAR =\n  'FIREBASE_DATA_CONNECT_EMULATOR_HOST';\n\n/**\n *\n * @param fullHost\n * @returns TransportOptions\n * @internal\n */\nexport function parseOptions(fullHost: string): TransportOptions {\n  const [protocol, hostName] = fullHost.split('://');\n  const isSecure = protocol === 'https';\n  const [host, portAsString] = hostName.split(':');\n  const port = Number(portAsString);\n  return { host, port, sslEnabled: isSecure };\n}\n/**\n * DataConnectOptions including project id\n */\nexport interface DataConnectOptions extends ConnectorConfig {\n  projectId: string;\n}\n\n/**\n * Class representing Firebase Data Connect\n */\nexport class DataConnect {\n  _queryManager!: QueryManager;\n  _mutationManager!: MutationManager;\n  isEmulator = false;\n  _initialized = false;\n  private _transport!: DataConnectTransport;\n  private _transportClass: TransportClass | undefined;\n  private _transportOptions?: TransportOptions;\n  private _authTokenProvider?: AuthTokenProvider;\n  _isUsingGeneratedSdk: boolean = false;\n  private _appCheckTokenProvider?: AppCheckTokenProvider;\n  // @internal\n  constructor(\n    public readonly app: FirebaseApp,\n    // TODO(mtewani): Replace with _dataConnectOptions in the future\n    private readonly dataConnectOptions: DataConnectOptions,\n    private readonly _authProvider: Provider<FirebaseAuthInternalName>,\n    private readonly _appCheckProvider: Provider<AppCheckInternalComponentName>\n  ) {\n    if (typeof process !== 'undefined' && process.env) {\n      const host = process.env[FIREBASE_DATA_CONNECT_EMULATOR_HOST_VAR];\n      if (host) {\n        logDebug('Found custom host. Using emulator');\n        this.isEmulator = true;\n        this._transportOptions = parseOptions(host);\n      }\n    }\n  }\n  // @internal\n  _useGeneratedSdk(): void {\n    if (!this._isUsingGeneratedSdk) {\n      this._isUsingGeneratedSdk = true;\n    }\n  }\n  _delete(): Promise<void> {\n    _removeServiceInstance(\n      this.app,\n      'data-connect',\n      JSON.stringify(this.getSettings())\n    );\n    return Promise.resolve();\n  }\n\n  // @internal\n  getSettings(): ConnectorConfig {\n    const copy = JSON.parse(JSON.stringify(this.dataConnectOptions));\n    delete copy.projectId;\n    return copy;\n  }\n\n  // @internal\n  setInitialized(): void {\n    if (this._initialized) {\n      return;\n    }\n    if (this._transportClass === undefined) {\n      logDebug('transportClass not provided. Defaulting to RESTTransport.');\n      this._transportClass = RESTTransport;\n    }\n\n    if (this._authProvider) {\n      this._authTokenProvider = new FirebaseAuthProvider(\n        this.app.name,\n        this.app.options,\n        this._authProvider\n      );\n    }\n    if (this._appCheckProvider) {\n      this._appCheckTokenProvider = new AppCheckTokenProvider(\n        this.app.name,\n        this._appCheckProvider\n      );\n    }\n\n    this._initialized = true;\n    this._transport = new this._transportClass(\n      this.dataConnectOptions,\n      this.app.options.apiKey,\n      this.app.options.appId,\n      this._authTokenProvider,\n      this._appCheckTokenProvider,\n      undefined,\n      this._isUsingGeneratedSdk\n    );\n    if (this._transportOptions) {\n      this._transport.useEmulator(\n        this._transportOptions.host,\n        this._transportOptions.port,\n        this._transportOptions.sslEnabled\n      );\n    }\n    this._queryManager = new QueryManager(this._transport);\n    this._mutationManager = new MutationManager(this._transport);\n  }\n\n  // @internal\n  enableEmulator(transportOptions: TransportOptions): void {\n    if (this._initialized) {\n      logError('enableEmulator called after initialization');\n      throw new DataConnectError(\n        Code.ALREADY_INITIALIZED,\n        'DataConnect instance already initialized!'\n      );\n    }\n    this._transportOptions = transportOptions;\n    this.isEmulator = true;\n  }\n}\n\n/**\n * Connect to the DataConnect Emulator\n * @param dc Data Connect instance\n * @param host host of emulator server\n * @param port port of emulator server\n * @param sslEnabled use https\n */\nexport function connectDataConnectEmulator(\n  dc: DataConnect,\n  host: string,\n  port?: number,\n  sslEnabled = false\n): void {\n  dc.enableEmulator({ host, port, sslEnabled });\n}\n\n/**\n * Initialize DataConnect instance\n * @param options ConnectorConfig\n */\nexport function getDataConnect(options: ConnectorConfig): DataConnect;\n/**\n * Initialize DataConnect instance\n * @param app FirebaseApp to initialize to.\n * @param options ConnectorConfig\n */\nexport function getDataConnect(\n  app: FirebaseApp,\n  options: ConnectorConfig\n): DataConnect;\nexport function getDataConnect(\n  appOrOptions: FirebaseApp | ConnectorConfig,\n  optionalOptions?: ConnectorConfig\n): DataConnect {\n  let app: FirebaseApp;\n  let dcOptions: ConnectorConfig;\n  if ('location' in appOrOptions) {\n    dcOptions = appOrOptions;\n    app = getApp();\n  } else {\n    dcOptions = optionalOptions!;\n    app = appOrOptions;\n  }\n\n  if (!app || Object.keys(app).length === 0) {\n    app = getApp();\n  }\n  const provider = _getProvider(app, 'data-connect');\n  const identifier = JSON.stringify(dcOptions);\n  if (provider.isInitialized(identifier)) {\n    const dcInstance = provider.getImmediate({ identifier });\n    const options = provider.getOptions(identifier);\n    const optionsValid = Object.keys(options).length > 0;\n    if (optionsValid) {\n      logDebug('Re-using cached instance');\n      return dcInstance;\n    }\n  }\n  validateDCOptions(dcOptions);\n\n  logDebug('Creating new DataConnect instance');\n  // Initialize with options.\n  return provider.initialize({\n    instanceIdentifier: identifier,\n    options: dcOptions\n  });\n}\n\n/**\n *\n * @param dcOptions\n * @returns {void}\n * @internal\n */\nexport function validateDCOptions(dcOptions: ConnectorConfig): boolean {\n  const fields = ['connector', 'location', 'service'];\n  if (!dcOptions) {\n    throw new DataConnectError(Code.INVALID_ARGUMENT, 'DC Option Required');\n  }\n  fields.forEach(field => {\n    if (dcOptions[field] === null || dcOptions[field] === undefined) {\n      throw new DataConnectError(Code.INVALID_ARGUMENT, `${field} Required`);\n    }\n  });\n  return true;\n}\n\n/**\n * Delete DataConnect instance\n * @param dataConnect DataConnect instance\n * @returns\n */\nexport function terminate(dataConnect: DataConnect): Promise<void> {\n  return dataConnect._delete();\n  // TODO(mtewani): Stop pending tasks\n}\n", "/**\n * @license\n * Copyright 2024 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n// eslint-disable-next-line import/no-extraneous-dependencies\nimport {\n  _registerComponent,\n  registerVersion,\n  SDK_VERSION\n} from '@firebase/app';\nimport { Component, ComponentType } from '@firebase/component';\n\nimport { name, version } from '../package.json';\nimport { setSDKVersion } from '../src/core/version';\n\nimport { DataConnect, ConnectorConfig } from './api/DataConnect';\nimport { Code, DataConnectError } from './core/error';\n\nexport function registerDataConnect(variant?: string): void {\n  setSDKVersion(SDK_VERSION);\n  _registerComponent(\n    new Component(\n      'data-connect',\n      (container, { instanceIdentifier: settings, options }) => {\n        const app = container.getProvider('app').getImmediate()!;\n        const authProvider = container.getProvider('auth-internal');\n        const appCheckProvider = container.getProvider('app-check-internal');\n        let newOpts = options as ConnectorConfig;\n        if (settings) {\n          newOpts = JSON.parse(settings);\n        }\n        if (!app.options.projectId) {\n          throw new DataConnectError(\n            Code.INVALID_ARGUMENT,\n            'Project ID must be provided. Did you pass in a proper projectId to initializeApp?'\n          );\n        }\n        return new DataConnect(\n          app,\n          { ...newOpts, projectId: app.options.projectId! },\n          authProvider,\n          appCheckProvider\n        );\n      },\n      ComponentType.PUBLIC\n    ).setMultipleInstances(true)\n  );\n  registerVersion(name, version, variant);\n  // BUILD_TARGET will be replaced by values like esm5, esm2017, cjs5, etc during the compilation\n  registerVersion(name, version, '__BUILD_TARGET__');\n}\n", "/**\n * @license\n * Copyright 2024 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { DataConnectError } from '../core/error';\n\nimport { DataConnect, getDataConnect } from './DataConnect';\nimport {\n  OperationRef,\n  QUERY_STR,\n  DataConnectResult,\n  SerializedRef\n} from './Reference';\n\n/**\n * Signature for `OnResultSubscription` for `subscribe`\n */\nexport type OnResultSubscription<Data, Variables> = (\n  res: QueryResult<Data, Variables>\n) => void;\n/**\n * Signature for `OnErrorSubscription` for `subscribe`\n */\nexport type OnErrorSubscription = (err?: DataConnectError) => void;\n/**\n * Signature for unsubscribe from `subscribe`\n */\nexport type QueryUnsubscribe = () => void;\n/**\n * Representation of user provided subscription options.\n */\nexport interface DataConnectSubscription<Data, Variables> {\n  userCallback: OnResultSubscription<Data, Variables>;\n  errCallback?: (e?: DataConnectError) => void;\n  unsubscribe: () => void;\n}\n\n/**\n * QueryRef object\n */\nexport interface QueryRef<Data, Variables>\n  extends OperationRef<Data, Variables> {\n  refType: typeof QUERY_STR;\n}\n/**\n * Result of `executeQuery`\n */\nexport interface QueryResult<Data, Variables>\n  extends DataConnectResult<Data, Variables> {\n  ref: QueryRef<Data, Variables>;\n  toJSON: () => SerializedRef<Data, Variables>;\n}\n/**\n * Promise returned from `executeQuery`\n */\nexport interface QueryPromise<Data, Variables>\n  extends PromiseLike<QueryResult<Data, Variables>> {\n  // reserved for special actions like cancellation\n}\n\n/**\n * Execute Query\n * @param queryRef query to execute.\n * @returns `QueryPromise`\n */\nexport function executeQuery<Data, Variables>(\n  queryRef: QueryRef<Data, Variables>\n): QueryPromise<Data, Variables> {\n  return queryRef.dataConnect._queryManager.executeQuery(queryRef);\n}\n\n/**\n * Execute Query\n * @param dcInstance Data Connect instance to use.\n * @param queryName Query to execute\n * @returns `QueryRef`\n */\nexport function queryRef<Data>(\n  dcInstance: DataConnect,\n  queryName: string\n): QueryRef<Data, undefined>;\n/**\n * Execute Query\n * @param dcInstance Data Connect instance to use.\n * @param queryName Query to execute\n * @param variables Variables to execute with\n * @returns `QueryRef`\n */\nexport function queryRef<Data, Variables>(\n  dcInstance: DataConnect,\n  queryName: string,\n  variables: Variables\n): QueryRef<Data, Variables>;\n/**\n * Execute Query\n * @param dcInstance Data Connect instance to use.\n * @param queryName Query to execute\n * @param variables Variables to execute with\n * @param initialCache initial cache to use for client hydration\n * @returns `QueryRef`\n */\nexport function queryRef<Data, Variables>(\n  dcInstance: DataConnect,\n  queryName: string,\n  variables?: Variables,\n  initialCache?: QueryResult<Data, Variables>\n): QueryRef<Data, Variables> {\n  dcInstance.setInitialized();\n  dcInstance._queryManager.track(queryName, variables, initialCache);\n  return {\n    dataConnect: dcInstance,\n    refType: QUERY_STR,\n    name: queryName,\n    variables: variables as Variables\n  };\n}\n/**\n * Converts serialized ref to query ref\n * @param serializedRef ref to convert to `QueryRef`\n * @returns `QueryRef`\n */\nexport function toQueryRef<Data, Variables>(\n  serializedRef: SerializedRef<Data, Variables>\n): QueryRef<Data, Variables> {\n  const {\n    refInfo: { name, variables, connectorConfig }\n  } = serializedRef;\n  return queryRef(getDataConnect(connectorConfig), name, variables);\n}\n/**\n * `OnCompleteSubscription`\n */\nexport type OnCompleteSubscription = () => void;\n/**\n * Representation of full observer options in `subscribe`\n */\nexport interface SubscriptionOptions<Data, Variables> {\n  onNext?: OnResultSubscription<Data, Variables>;\n  onErr?: OnErrorSubscription;\n  onComplete?: OnCompleteSubscription;\n}\n", "/**\n * @license\n * Copyright 2024 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {\n  ConnectorConfig,\n  DataConnect,\n  getDataConnect\n} from '../api/DataConnect';\nimport { Code, DataConnectError } from '../core/error';\ninterface ParsedArgs<Variables> {\n  dc: DataConnect;\n  vars: Variables;\n}\n\n/**\n * The generated SDK will allow the user to pass in either the variable or the data connect instance with the variable,\n * and this function validates the variables and returns back the DataConnect instance and variables based on the arguments passed in.\n * @param connectorConfig\n * @param dcOrVars\n * @param vars\n * @param validateVars\n * @returns {DataConnect} and {Variables} instance\n * @internal\n */\nexport function validateArgs<Variables extends object>(\n  connectorConfig: ConnectorConfig,\n  dcOrVars?: DataConnect | Variables,\n  vars?: Variables,\n  validateVars?: boolean\n): ParsedArgs<Variables> {\n  let dcInstance: DataConnect;\n  let realVars: Variables;\n  if (dcOrVars && 'enableEmulator' in dcOrVars) {\n    dcInstance = dcOrVars as DataConnect;\n    realVars = vars;\n  } else {\n    dcInstance = getDataConnect(connectorConfig);\n    realVars = dcOrVars as Variables;\n  }\n  if (!dcInstance || (!realVars && validateVars)) {\n    throw new DataConnectError(Code.INVALID_ARGUMENT, 'Variables required.');\n  }\n  return { dc: dcInstance, vars: realVars };\n}\n", "/**\n * @license\n * Copyright 2024 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {\n  OnCompleteSubscription,\n  OnErrorSubscription,\n  OnResultSubscription,\n  QueryRef,\n  QueryUnsubscribe,\n  SubscriptionOptions,\n  toQueryRef\n} from './api/query';\nimport { OpResult, SerializedRef } from './api/Reference';\nimport { DataConnectError, Code } from './core/error';\n\n/**\n * Subscribe to a `QueryRef`\n * @param queryRefOrSerializedResult query ref or serialized result.\n * @param observer observer object to use for subscribing.\n * @returns `SubscriptionOptions`\n */\nexport function subscribe<Data, Variables>(\n  queryRefOrSerializedResult:\n    | QueryRef<Data, Variables>\n    | SerializedRef<Data, Variables>,\n  observer: SubscriptionOptions<Data, Variables>\n): QueryUnsubscribe;\n/**\n * Subscribe to a `QueryRef`\n * @param queryRefOrSerializedResult query ref or serialized result.\n * @param onNext Callback to call when result comes back.\n * @param onError Callback to call when error gets thrown.\n * @param onComplete Called when subscription completes.\n * @returns `SubscriptionOptions`\n */\nexport function subscribe<Data, Variables>(\n  queryRefOrSerializedResult:\n    | QueryRef<Data, Variables>\n    | SerializedRef<Data, Variables>,\n  onNext: OnResultSubscription<Data, Variables>,\n  onError?: OnErrorSubscription,\n  onComplete?: OnCompleteSubscription\n): QueryUnsubscribe;\n/**\n * Subscribe to a `QueryRef`\n * @param queryRefOrSerializedResult query ref or serialized result.\n * @param observerOrOnNext observer object or next function.\n * @param onError Callback to call when error gets thrown.\n * @param onComplete Called when subscription completes.\n * @returns `SubscriptionOptions`\n */\nexport function subscribe<Data, Variables>(\n  queryRefOrSerializedResult:\n    | QueryRef<Data, Variables>\n    | SerializedRef<Data, Variables>,\n  observerOrOnNext:\n    | SubscriptionOptions<Data, Variables>\n    | OnResultSubscription<Data, Variables>,\n  onError?: OnErrorSubscription,\n  onComplete?: OnCompleteSubscription\n): QueryUnsubscribe {\n  let ref: QueryRef<Data, Variables>;\n  let initialCache: OpResult<Data> | undefined;\n  if ('refInfo' in queryRefOrSerializedResult) {\n    const serializedRef: SerializedRef<Data, Variables> =\n      queryRefOrSerializedResult;\n    const { data, source, fetchTime } = serializedRef;\n    initialCache = {\n      data,\n      source,\n      fetchTime\n    };\n    ref = toQueryRef(serializedRef);\n  } else {\n    ref = queryRefOrSerializedResult;\n  }\n  let onResult: OnResultSubscription<Data, Variables> | undefined = undefined;\n  if (typeof observerOrOnNext === 'function') {\n    onResult = observerOrOnNext;\n  } else {\n    onResult = observerOrOnNext.onNext;\n    onError = observerOrOnNext.onErr;\n    onComplete = observerOrOnNext.onComplete;\n  }\n  if (!onResult) {\n    throw new DataConnectError(Code.INVALID_ARGUMENT, 'Must provide onNext');\n  }\n  return ref.dataConnect._queryManager.addSubscription(\n    ref,\n    onResult,\n    onError,\n    initialCache\n  );\n}\n", "/**\n * Firebase Data Connect\n *\n * @packageDocumentation\n */\n\n/**\n * @license\n * Copyright 2024 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nimport { DataConnect } from './api/DataConnect';\nimport { registerDataConnect } from './register';\n\nexport * from './api';\nexport * from './api.browser';\n\nregisterDataConnect();\n\ndeclare module '@firebase/component' {\n  interface NameServiceMapping {\n    'data-connect': DataConnect;\n  }\n}\n"], "names": ["SDK_VERSION"], "mappings": ";;;;;;;;;AAAA;;;;;;;;;;;;;;;AAeG;AAEH;AACO,IAAI,WAAW,GAAG,EAAE,CAAC;AAE5B;;;AAGG;AACG,SAAU,aAAa,CAAC,OAAe,EAAA;IAC3C,WAAW,GAAG,OAAO,CAAC;AACxB;;AC1BA;;;;;;;;;;;;;;;AAeG;AAUH;;;AAGG;AACH,IAAA,qBAAA,kBAAA,YAAA;IAEE,SACU,qBAAA,CAAA,QAAgB,EAChB,gBAA0D,EAAA;QAFpE,IAWC,KAAA,GAAA,IAAA,CAAA;QAVS,IAAQ,CAAA,QAAA,GAAR,QAAQ,CAAQ;QAChB,IAAgB,CAAA,gBAAA,GAAhB,gBAAgB,CAA0C;AAElE,QAAA,IAAI,CAAC,QAAQ,GAAG,gBAAgB,KAAA,IAAA,IAAhB,gBAAgB,KAAhB,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,gBAAgB,CAAE,YAAY,CAAC,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC,CAAC;AACnE,QAAA,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;AAClB,YAAA,MAAK,gBAAgB,KAAhB,IAAA,IAAA,gBAAgB,KAAhB,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,gBAAgB,CACjB,GAAG,EACJ,CAAA,IAAI,CAAC,UAAA,QAAQ,EAAA,EAAI,QAAC,KAAI,CAAC,QAAQ,GAAG,QAAQ,EAAzB,EAA0B,CAC3C,CAAA,KAAK,EAAE,CAAA,CAAC;AACZ,SAAA;KACF;IAED,qBAAQ,CAAA,SAAA,CAAA,QAAA,GAAR,UAAS,YAAsB,EAAA;QAA/B,IAiBC,KAAA,GAAA,IAAA,CAAA;AAhBC,QAAA,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;AAClB,YAAA,OAAO,IAAI,OAAO,CAAsB,UAAC,OAAO,EAAE,MAAM,EAAA;;;;;AAKtD,gBAAA,UAAU,CAAC,YAAA;oBACT,IAAI,KAAI,CAAC,QAAQ,EAAE;AACjB,wBAAA,KAAI,CAAC,QAAQ,CAAC,YAAY,CAAC,CAAC,IAAI,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC;AACnD,qBAAA;AAAM,yBAAA;wBACL,OAAO,CAAC,IAAI,CAAC,CAAC;AACf,qBAAA;iBACF,EAAE,CAAC,CAAC,CAAC;AACR,aAAC,CAAC,CAAC;AACJ,SAAA;QACD,OAAO,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,YAAY,CAAC,CAAC;KAC7C,CAAA;IAED,qBAAsB,CAAA,SAAA,CAAA,sBAAA,GAAtB,UAAuB,QAA+B,EAAA;;QACpD,MAAK,CAAA,EAAA,GAAA,IAAI,CAAC,gBAAgB,0CACtB,GAAG,EAAA,CACJ,IAAI,CAAC,UAAA,QAAQ,IAAI,OAAA,QAAQ,CAAC,gBAAgB,CAAC,QAAQ,CAAC,CAAnC,EAAmC,CAAC,CAAA,CAAC;KAC1D,CAAA;IACH,OAAC,qBAAA,CAAA;AAAD,CAAC,EAAA,CAAA;;ACpED;;;;;;;;;;;;;;;AAeG;AAeI,IAAM,IAAI,GAAG;AAClB,IAAA,KAAK,EAAE,OAA+B;AACtC,IAAA,mBAAmB,EAAE,qBAA6C;AAClE,IAAA,eAAe,EAAE,iBAAyC;AAC1D,IAAA,aAAa,EAAE,eAAuC;AACtD,IAAA,gBAAgB,EAAE,kBAA0C;AAC5D,IAAA,aAAa,EAAE,eAAuC;AACtD,IAAA,YAAY,EAAE,cAAsC;CACrD,CAAC;AAEF;AACA,IAAA,gBAAA,kBAAA,UAAA,MAAA,EAAA;IAAsC,SAAa,CAAA,gBAAA,EAAA,MAAA,CAAA,CAAA;;AAKjD,IAAA,SAAA,gBAAA;AACE;;AAEG;IACM,IAA0B;AACnC;;AAEG;IACM,OAAe,EAAA;AAR1B,QAAA,IAAA,KAAA,GAUE,MAAM,CAAA,IAAA,CAAA,IAAA,EAAA,IAAI,EAAE,OAAO,CAAC,IAMrB,IAAA,CAAA;QAZU,KAAI,CAAA,IAAA,GAAJ,IAAI,CAAsB;QAI1B,KAAO,CAAA,OAAA,GAAP,OAAO,CAAQ;;;;QAOxB,KAAI,CAAC,QAAQ,GAAG,YAAA,EAAM,OAAA,EAAG,CAAA,MAAA,CAAA,KAAI,CAAC,IAAI,EAAA,UAAA,CAAA,CAAA,MAAA,CAAW,KAAI,CAAC,IAAI,gBAAM,KAAI,CAAC,OAAO,CAAE,CAAA,EAAA,CAAC;;KAC5E;IACH,OAAC,gBAAA,CAAA;AAAD,CAtBA,CAAsC,aAAa,CAsBlD,CAAA;;AC/DD;;;;;;;;;;;;;;;AAeG;AAKH,IAAM,MAAM,GAAG,IAAI,MAAM,CAAC,wBAAwB,CAAC,CAAC;AAC9C,SAAU,WAAW,CAAC,QAAwB,EAAA;AAClD,IAAA,MAAM,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;AAC/B,CAAC;AACK,SAAU,QAAQ,CAAC,GAAW,EAAA;IAClC,MAAM,CAAC,KAAK,CAAC,eAAA,CAAA,MAAA,CAAgB,WAAW,EAAM,KAAA,CAAA,CAAA,MAAA,CAAA,GAAG,CAAE,CAAC,CAAC;AACvD,CAAC;AAEK,SAAU,QAAQ,CAAC,GAAW,EAAA;IAClC,MAAM,CAAC,KAAK,CAAC,eAAA,CAAA,MAAA,CAAgB,WAAW,EAAM,KAAA,CAAA,CAAA,MAAA,CAAA,GAAG,CAAE,CAAC,CAAC;AACvD;;AC9BA;;;;;;;;;;;;;;;AAeG;AAmBH;AACA,IAAA,oBAAA,kBAAA,YAAA;AAEE,IAAA,SAAA,oBAAA,CACU,QAAgB,EAChB,QAAyB,EACzB,aAAiD,EAAA;QAH3D,IASC,KAAA,GAAA,IAAA,CAAA;QARS,IAAQ,CAAA,QAAA,GAAR,QAAQ,CAAQ;QAChB,IAAQ,CAAA,QAAA,GAAR,QAAQ,CAAiB;QACzB,IAAa,CAAA,aAAA,GAAb,aAAa,CAAoC;AAEzD,QAAA,IAAI,CAAC,KAAK,GAAG,aAAa,CAAC,YAAY,CAAC,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAE,CAAC;AAC7D,QAAA,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE;AACf,YAAA,aAAa,CAAC,MAAM,CAAC,UAAA,IAAI,IAAI,QAAC,KAAI,CAAC,KAAK,GAAG,IAAI,EAAlB,EAAmB,CAAC,CAAC;AACnD,SAAA;KACF;IACD,oBAAQ,CAAA,SAAA,CAAA,QAAA,GAAR,UAAS,YAAqB,EAAA;QAA9B,IA0BC,KAAA,GAAA,IAAA,CAAA;AAzBC,QAAA,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE;AACf,YAAA,OAAO,IAAI,OAAO,CAAC,UAAC,OAAO,EAAE,MAAM,EAAA;AACjC,gBAAA,UAAU,CAAC,YAAA;oBACT,IAAI,KAAI,CAAC,KAAK,EAAE;AACd,wBAAA,KAAI,CAAC,QAAQ,CAAC,YAAY,CAAC,CAAC,IAAI,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC;AACnD,qBAAA;AAAM,yBAAA;wBACL,OAAO,CAAC,IAAI,CAAC,CAAC;AACf,qBAAA;iBACF,EAAE,CAAC,CAAC,CAAC;AACR,aAAC,CAAC,CAAC;AACJ,SAAA;AACD,QAAA,OAAO,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,YAAY,CAAC,CAAC,KAAK,CAAC,UAAA,KAAK,EAAA;AAClD,YAAA,IAAI,KAAK,IAAI,KAAK,CAAC,IAAI,KAAK,4BAA4B,EAAE;gBACxD,QAAQ,CACN,gEAAgE,CACjE,CAAC;AACF,gBAAA,OAAO,IAAI,CAAC;AACb,aAAA;AAAM,iBAAA;AACL,gBAAA,QAAQ,CACN,oDAAoD;AAClD,oBAAA,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,CACxB,CAAC;AACF,gBAAA,OAAO,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;AAC9B,aAAA;AACH,SAAC,CAAC,CAAC;KACJ,CAAA;IACD,oBAAsB,CAAA,SAAA,CAAA,sBAAA,GAAtB,UAAuB,QAA2B,EAAA;;QAChD,CAAA,EAAA,GAAA,IAAI,CAAC,KAAK,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAE,oBAAoB,CAAC,QAAQ,CAAC,CAAC;KAC5C,CAAA;IACD,oBAAyB,CAAA,SAAA,CAAA,yBAAA,GAAzB,UAA0B,QAAwC,EAAA;AAChE,QAAA,IAAI,CAAC,aAAa;AACf,aAAA,GAAG,EAAE;AACL,aAAA,IAAI,CAAC,UAAA,IAAI,EAAA,EAAI,OAAA,IAAI,CAAC,uBAAuB,CAAC,QAAQ,CAAC,CAAA,EAAA,CAAC;AACpD,aAAA,KAAK,CAAC,UAAA,GAAG,EAAA,EAAI,OAAA,QAAQ,CAAC,GAAG,CAAC,CAAA,EAAA,CAAC,CAAC;KAChC,CAAA;IACH,OAAC,oBAAA,CAAA;AAAD,CAAC,EAAA,CAAA;;ACnFD;;;;;;;;;;;;;;;AAeG;AAGI,IAAM,SAAS,GAAG,QAAQ;AAC1B,IAAM,YAAY,GAAG,WAAW;AAGhC,IAAM,aAAa,GAAG,SAAS;AAC/B,IAAM,YAAY,GAAG;;ACvB5B;;;;;;;;;;;;;;;AAeG;AAGI,IAAI,WAAqB,CAAC;AAC3B,SAAU,UAAU,CAAC,OAAiB,EAAA;IAC1C,WAAW,GAAG,OAAO,CAAC;AACxB,CAAC;AACD,UAAU,CAAC,UAAA,CAAC,EAAA,EAAI,OAAA,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAjB,EAAiB,CAAC;;ACtBlC;;;;;;;;;;;;;;;AAeG;SAEa,cAAc,CAC5B,GAAmB,EACnB,GAAW,EACX,GAAM,EAAA;AAEN,IAAA,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE;AACjB,QAAA,GAAG,CAAC,GAAG,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC;AACnB,KAAA;AACH;;ACzBA;;;;;;;;;;;;;;;AAeG;AAiCH,SAAS,gBAAgB,CACvB,QAAmC,EACnC,IAAU,EACV,MAAkB,EAAA;AAElB,IAAA,OAAO,SAAS,MAAM,GAAA;QACpB,OAAO;AACL,YAAA,IAAI,EAAA,IAAA;AACJ,YAAA,OAAO,EAAE;gBACP,IAAI,EAAE,QAAQ,CAAC,IAAI;gBACnB,SAAS,EAAE,QAAQ,CAAC,SAAS;AAC7B,gBAAA,eAAe,aACb,SAAS,EAAE,QAAQ,CAAC,WAAW,CAAC,GAAG,CAAC,OAAO,CAAC,SAAU,IACnD,QAAQ,CAAC,WAAW,CAAC,WAAW,EAAE,CACtC;AACF,aAAA;AACD,YAAA,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE,CAAC,cAAc,EAAE;AACtC,YAAA,MAAM,EAAA,MAAA;SACP,CAAC;AACJ,KAAC,CAAC;AACJ,CAAC;AAED,IAAA,YAAA,kBAAA,YAAA;AAEE,IAAA,SAAA,YAAA,CAAoB,SAA+B,EAAA;QAA/B,IAAS,CAAA,SAAA,GAAT,SAAS,CAAsB;AACjD,QAAA,IAAI,CAAC,QAAQ,GAAG,IAAI,GAAG,EAAE,CAAC;KAC3B;AACD,IAAA,YAAA,CAAA,SAAA,CAAA,KAAK,GAAL,UACE,SAAiB,EACjB,SAAoB,EACpB,YAA6B,EAAA;AAE7B,QAAA,IAAM,GAAG,GAAyC;AAChD,YAAA,IAAI,EAAE,SAAS;AACf,YAAA,SAAS,EAAA,SAAA;AACT,YAAA,OAAO,EAAE,SAAS;SACnB,CAAC;AACF,QAAA,IAAM,GAAG,GAAG,WAAW,CAAC,GAAG,CAAC,CAAC;AAC7B,QAAA,IAAM,eAAe,GAAkC;AACrD,YAAA,GAAG,EAAA,GAAA;AACH,YAAA,aAAa,EAAE,EAAE;YACjB,YAAY,EAAE,YAAY,IAAI,IAAI;AAClC,YAAA,SAAS,EAAE,IAAI;SAChB,CAAC;;QAEF,cAAc,CAAC,IAAI,CAAC,QAAQ,EAAE,GAAG,EAAE,eAAe,CAAC,CAAC;QACpD,OAAO,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,GAAG,CAAkC,CAAC;KAChE,CAAA;IACD,YAAe,CAAA,SAAA,CAAA,eAAA,GAAf,UACE,QAAuC,EACvC,gBAAuD,EACvD,eAAqC,EACrC,YAA6B,EAAA;QAJ/B,IA0EC,KAAA,GAAA,IAAA,CAAA;QApEC,IAAM,GAAG,GAAG,WAAW,CAAC;YACtB,IAAI,EAAE,QAAQ,CAAC,IAAI;YACnB,SAAS,EAAE,QAAQ,CAAC,SAAS;AAC7B,YAAA,OAAO,EAAE,SAAS;AACnB,SAAA,CAAC,CAAC;QACH,IAAM,YAAY,GAAG,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,GAAG,CAGzC,CAAC;AACF,QAAA,IAAM,YAAY,GAAG;AACnB,YAAA,YAAY,EAAE,gBAAgB;AAC9B,YAAA,WAAW,EAAE,eAAe;SAC7B,CAAC;AACF,QAAA,IAAM,WAAW,GAAG,YAAA;YAClB,IAAM,YAAY,GAAG,KAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,GAAG,CAAE,CAAC;AAC7C,YAAA,YAAY,CAAC,aAAa,GAAG,YAAY,CAAC,aAAa,CAAC,MAAM,CAC5D,UAAA,GAAG,EAAA,EAAI,OAAA,GAAG,KAAK,YAAY,CAApB,EAAoB,CAC5B,CAAC;AACJ,SAAC,CAAC;AACF,QAAA,IAAI,YAAY,IAAI,YAAY,CAAC,YAAY,KAAK,YAAY,EAAE;YAC9D,QAAQ,CAAC,uCAAuC,CAAC,CAAC;YAClD,IACE,CAAC,YAAY,CAAC,YAAY;iBACzB,YAAY,CAAC,YAAY;AACxB,oBAAA,YAAY,CACV,YAAY,CAAC,YAAY,CAAC,SAAS,EACnC,YAAY,CAAC,SAAS,CACvB,CAAC,EACJ;AACA,gBAAA,YAAY,CAAC,YAAY,GAAG,YAAY,CAAC;AAC1C,aAAA;AACF,SAAA;AACD,QAAA,IAAI,YAAY,CAAC,YAAY,KAAK,IAAI,EAAE;AACtC,YAAA,IAAM,UAAU,GAAG,YAAY,CAAC,YAAY,CAAC,IAAI,CAAC;AAClD,YAAA,gBAAgB,CAAC;AACf,gBAAA,IAAI,EAAE,UAAU;AAChB,gBAAA,MAAM,EAAE,YAAY;AACpB,gBAAA,GAAG,EAAE,QAAqC;AAC1C,gBAAA,MAAM,EAAE,gBAAgB,CACtB,QAAqC,EACrC,YAAY,CAAC,YAAY,CAAC,IAAI,EAC9B,YAAY,CACb;AACD,gBAAA,SAAS,EAAE,YAAY,CAAC,YAAY,CAAC,SAAS;AAC/C,aAAA,CAAC,CAAC;AACH,YAAA,IAAI,YAAY,CAAC,SAAS,KAAK,IAAI,IAAI,eAAe,EAAE;gBACtD,eAAe,CAAC,SAAS,CAAC,CAAC;AAC5B,aAAA;AACF,SAAA;AAED,QAAA,YAAY,CAAC,aAAa,CAAC,IAAI,CAAC;AAC9B,YAAA,YAAY,EAAE,gBAAgB;AAC9B,YAAA,WAAW,EAAE,eAAe;AAC5B,YAAA,WAAW,EAAA,WAAA;AACZ,SAAA,CAAC,CAAC;AACH,QAAA,IAAI,CAAC,YAAY,CAAC,YAAY,EAAE;AAC9B,YAAA,QAAQ,CACN,+BACE,CAAA,MAAA,CAAA,QAAQ,CAAC,IAAI,6BACI,IAAI,CAAC,SAAS,CAC/B,QAAQ,CAAC,SAAS,CACnB,EAAA,yBAAA,CAAyB,CAC3B,CAAC;YACF,IAAM,OAAO,GAAG,IAAI,CAAC,YAAY,CAAC,QAAqC,CAAC,CAAC;;YAEzE,OAAO,CAAC,IAAI,CAAC,SAAS,EAAE,UAAA,GAAG,EAAA,GAAM,CAAC,CAAC;AACpC,SAAA;AACD,QAAA,OAAO,WAAW,CAAC;KACpB,CAAA;IACD,YAAY,CAAA,SAAA,CAAA,YAAA,GAAZ,UACE,QAAmC,EAAA;QAEnC,IAAM,GAAG,GAAG,WAAW,CAAC;YACtB,IAAI,EAAE,QAAQ,CAAC,IAAI;YACnB,SAAS,EAAE,QAAQ,CAAC,SAAS;AAC7B,YAAA,OAAO,EAAE,SAAS;AACnB,SAAA,CAAC,CAAC;QACH,IAAM,YAAY,GAAG,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,GAAG,CAAE,CAAC;AAC7C,QAAA,IAAM,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC,WAAW,CACvC,QAAQ,CAAC,IAAI,EACb,QAAQ,CAAC,SAAS,CACnB,CAAC;AACF,QAAA,IAAM,IAAI,GAAG,MAAM,CAAC,IAAI,CACtB,UAAA,GAAG,EAAA;YACD,IAAM,SAAS,GAAG,IAAI,IAAI,EAAE,CAAC,QAAQ,EAAE,CAAC;AACxC,YAAA,IAAM,MAAM,GAAA,QAAA,CAAA,QAAA,CAAA,EAAA,EACP,GAAG,CAAA,EAAA,EACN,MAAM,EAAE,aAAa,EACrB,GAAG,EAAE,QAAQ,EACb,MAAM,EAAE,gBAAgB,CAAC,QAAQ,EAAE,GAAG,CAAC,IAAI,EAAE,aAAa,CAAC,EAC3D,SAAS,EAAA,SAAA,EAAA,CACV,CAAC;AACF,YAAA,YAAY,CAAC,aAAa,CAAC,OAAO,CAAC,UAAA,YAAY,EAAA;AAC7C,gBAAA,YAAY,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC;AACpC,aAAC,CAAC,CAAC;YACH,YAAY,CAAC,YAAY,GAAG;gBAC1B,IAAI,EAAE,GAAG,CAAC,IAAI;AACd,gBAAA,MAAM,EAAE,YAAY;AACpB,gBAAA,SAAS,EAAA,SAAA;aACV,CAAC;AACF,YAAA,OAAO,MAAM,CAAC;SACf,EACD,UAAA,GAAG,EAAA;AACD,YAAA,YAAY,CAAC,SAAS,GAAG,GAAG,CAAC;AAC7B,YAAA,YAAY,CAAC,aAAa,CAAC,OAAO,CAAC,UAAA,YAAY,EAAA;gBAC7C,IAAI,YAAY,CAAC,WAAW,EAAE;AAC5B,oBAAA,YAAY,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC;AAC/B,iBAAA;AACH,aAAC,CAAC,CAAC;AACH,YAAA,MAAM,GAAG,CAAC;AACZ,SAAC,CACF,CAAC;AAEF,QAAA,OAAO,IAAI,CAAC;KACb,CAAA;AACD,IAAA,YAAA,CAAA,SAAA,CAAA,cAAc,GAAd,UAAe,IAAY,EAAE,IAAY,EAAA;QACvC,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;KACxC,CAAA;IACH,OAAC,YAAA,CAAA;AAAD,CAAC,EAAA,CAAA,CAAA;AACD,SAAS,YAAY,CAAC,IAAY,EAAE,IAAY,EAAA;AAC9C,IAAA,IAAM,KAAK,GAAG,IAAI,IAAI,CAAC,IAAI,CAAC,CAAC;AAC7B,IAAA,IAAM,KAAK,GAAG,IAAI,IAAI,CAAC,IAAI,CAAC,CAAC;IAC7B,OAAO,KAAK,CAAC,OAAO,EAAE,GAAG,KAAK,CAAC,OAAO,EAAE,CAAC;AAC3C;;ACjOA;;;;;;;;;;;;;;;AAeG;AAMa,SAAA,UAAU,CACxB,aAAiC,EACjC,gBAAkC,EAAA;AAE1B,IAAA,IAAA,SAAS,GAA4C,aAAa,UAAzD,EAAE,QAAQ,GAAkC,aAAa,CAAA,QAA/C,EAAa,OAAO,GAAc,aAAa,CAA3B,SAAA,EAAE,OAAO,GAAK,aAAa,QAAlB,CAAmB;AACnE,IAAA,IAAA,IAAI,GAAuB,gBAAgB,CAAA,IAAvC,EAAE,UAAU,GAAW,gBAAgB,CAAA,UAA3B,EAAE,IAAI,GAAK,gBAAgB,KAArB,CAAsB;IACpD,IAAM,QAAQ,GAAG,UAAU,GAAG,OAAO,GAAG,MAAM,CAAC;AAC/C,IAAA,IAAM,QAAQ,GAAG,IAAI,IAAI,oCAAoC,CAAC;AAC9D,IAAA,IAAI,OAAO,GAAG,EAAA,CAAA,MAAA,CAAG,QAAQ,EAAM,KAAA,CAAA,CAAA,MAAA,CAAA,QAAQ,CAAE,CAAC;AAC1C,IAAA,IAAI,OAAO,IAAI,KAAK,QAAQ,EAAE;AAC5B,QAAA,OAAO,IAAI,GAAA,CAAA,MAAA,CAAI,IAAI,CAAE,CAAC;AACvB,KAAA;AAAM,SAAA,IAAI,OAAO,IAAI,KAAK,WAAW,EAAE;QACtC,QAAQ,CAAC,iCAAiC,CAAC,CAAC;QAC5C,MAAM,IAAI,gBAAgB,CACxB,IAAI,CAAC,gBAAgB,EACrB,oCAAoC,CACrC,CAAC;AACH,KAAA;IACD,OAAO,EAAA,CAAA,MAAA,CAAG,OAAO,EAAA,mBAAA,CAAA,CAAA,MAAA,CAAoB,OAAO,EAAA,aAAA,CAAA,CAAA,MAAA,CAAc,QAAQ,EAAA,YAAA,CAAA,CAAA,MAAA,CAAa,OAAO,EAAA,cAAA,CAAA,CAAA,MAAA,CAAe,SAAS,CAAE,CAAC;AACnH,CAAC;AACe,SAAA,QAAQ,CAAC,GAAW,EAAE,MAAe,EAAA;IACnD,IAAI,CAAC,MAAM,EAAE;AACX,QAAA,OAAO,GAAG,CAAC;AACZ,KAAA;AACD,IAAA,IAAM,MAAM,GAAG,IAAI,GAAG,CAAC,GAAG,CAAC,CAAC;IAC5B,MAAM,CAAC,YAAY,CAAC,MAAM,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC;AAC1C,IAAA,OAAO,MAAM,CAAC,QAAQ,EAAE,CAAC;AAC3B;;AChDA;;;;;;;;;;;;;;;AAeG;AAMH,IAAI,YAAY,GAAwB,UAAU,CAAC,KAAK,CAAC;AAIzD,SAAS,qBAAqB,CAAC,WAAoB,EAAA;AACjD,IAAA,IAAI,GAAG,GAAG,cAAc,GAAG,WAAW,CAAC;AACvC,IAAA,IAAI,WAAW,EAAE;QACf,GAAG,IAAI,UAAU,CAAC;AACnB,KAAA;AACD,IAAA,OAAO,GAAG,CAAC;AACb,CAAC;AACe,SAAA,OAAO,CACrB,GAAW,EACX,IAAO,EACP,EAA2B,EAC3B,KAAoB,EACpB,WAA0B,EAC1B,aAA4B,EAC5B,WAAoB,EAAA;IAPtB,IAmEC,KAAA,GAAA,IAAA,CAAA;AAhEG,IAAA,IAAA,MAAM,GAAA,EAAA,CAAA,MAAA,CAAA;IAMR,IAAI,CAAC,YAAY,EAAE;QACjB,MAAM,IAAI,gBAAgB,CAAC,IAAI,CAAC,KAAK,EAAE,mCAAmC,CAAC,CAAC;AAC7E,KAAA;AACD,IAAA,IAAM,OAAO,GAAgB;AAC3B,QAAA,cAAc,EAAE,kBAAkB;AAClC,QAAA,mBAAmB,EAAE,qBAAqB,CAAC,WAAW,CAAC;KACxD,CAAC;AACF,IAAA,IAAI,WAAW,EAAE;AACf,QAAA,OAAO,CAAC,uBAAuB,CAAC,GAAG,WAAW,CAAC;AAChD,KAAA;AACD,IAAA,IAAI,KAAK,EAAE;AACT,QAAA,OAAO,CAAC,kBAAkB,CAAC,GAAG,KAAK,CAAC;AACrC,KAAA;AACD,IAAA,IAAI,aAAa,EAAE;AACjB,QAAA,OAAO,CAAC,qBAAqB,CAAC,GAAG,aAAa,CAAC;AAChD,KAAA;IACD,IAAM,OAAO,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;AACrC,IAAA,QAAQ,CAAC,wBAAyB,CAAA,MAAA,CAAA,GAAG,yBAAe,OAAO,CAAE,CAAC,CAAC;IAE/D,OAAO,YAAY,CAAC,GAAG,EAAE;AACvB,QAAA,IAAI,EAAE,OAAO;AACb,QAAA,MAAM,EAAE,MAAM;AACd,QAAA,OAAO,EAAA,OAAA;AACP,QAAA,MAAM,EAAA,MAAA;KACP,CAAC;SACC,KAAK,CAAC,UAAA,GAAG,EAAA;AACR,QAAA,MAAM,IAAI,gBAAgB,CACxB,IAAI,CAAC,KAAK,EACV,mBAAmB,GAAG,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,CAC1C,CAAC;AACJ,KAAC,CAAC;SACD,IAAI,CAAC,UAAM,QAAQ,EAAA,EAAA,OAAA,SAAA,CAAA,KAAA,EAAA,KAAA,CAAA,EAAA,KAAA,CAAA,EAAA,YAAA;;;;;oBACd,YAAY,GAAG,IAAI,CAAC;;;;AAEP,oBAAA,OAAA,CAAA,CAAA,YAAM,QAAQ,CAAC,IAAI,EAAE,CAAA,CAAA;;oBAApC,YAAY,GAAG,SAAqB,CAAC;;;;AAErC,oBAAA,MAAM,IAAI,gBAAgB,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,SAAS,CAAC,GAAC,CAAC,CAAC,CAAC;;AAEtD,oBAAA,OAAO,GAAG,UAAU,CAAC,YAAY,CAAC,CAAC;AACzC,oBAAA,IAAI,QAAQ,CAAC,MAAM,IAAI,GAAG,EAAE;wBAC1B,QAAQ,CACN,kCAAkC,GAAG,IAAI,CAAC,SAAS,CAAC,YAAY,CAAC,CAClE,CAAC;AACF,wBAAA,IAAI,QAAQ,CAAC,MAAM,KAAK,GAAG,EAAE;4BAC3B,MAAM,IAAI,gBAAgB,CAAC,IAAI,CAAC,YAAY,EAAE,OAAO,CAAC,CAAC;AACxD,yBAAA;wBACD,MAAM,IAAI,gBAAgB,CAAC,IAAI,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;AACjD,qBAAA;AACD,oBAAA,OAAA,CAAA,CAAA,aAAO,YAAY,CAAC,CAAA;;;SACrB,CAAC;SACD,IAAI,CAAC,UAAA,GAAG,EAAA;QACP,IAAI,GAAG,CAAC,MAAM,IAAI,GAAG,CAAC,MAAM,CAAC,MAAM,EAAE;YACnC,IAAM,WAAW,GAAG,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;AAC/C,YAAA,QAAQ,CAAC,8CAA8C,GAAG,WAAW,CAAC,CAAC;YACvE,MAAM,IAAI,gBAAgB,CAAC,IAAI,CAAC,KAAK,EAAE,WAAW,CAAC,CAAC;AACrD,SAAA;AACD,QAAA,OAAO,GAAmC,CAAC;AAC7C,KAAC,CAAC,CAAC;AACP,CAAC;AAID,SAAS,UAAU,CAAC,GAAkB,EAAA;IACpC,IAAI,SAAS,IAAI,GAAG,EAAE;QACpB,OAAO,GAAG,CAAC,OAAO,CAAC;AACpB,KAAA;AACD,IAAA,OAAO,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC;AAC7B;;AC5GA;;;;;;;;;;;;;;;AAeG;AAYH,IAAA,aAAA,kBAAA,YAAA;AAWE,IAAA,SAAA,aAAA,CACE,OAA2B,EACnB,MAA2B,EAC3B,KAAc,EACd,YAA4C,EAC5C,gBAAoD,EAC5D,gBAA+C,EACvC,WAAmB,EAAA;AAAnB,QAAA,IAAA,WAAA,KAAA,KAAA,CAAA,EAAA,EAAA,WAAmB,GAAA,KAAA,CAAA,EAAA;QAP7B,IA0CC,KAAA,GAAA,IAAA,CAAA;;QAxCS,IAAM,CAAA,MAAA,GAAN,MAAM,CAAqB;QAC3B,IAAK,CAAA,KAAA,GAAL,KAAK,CAAS;QACd,IAAY,CAAA,YAAA,GAAZ,YAAY,CAAgC;QAC5C,IAAgB,CAAA,gBAAA,GAAhB,gBAAgB,CAAoC;QAEpD,IAAW,CAAA,WAAA,GAAX,WAAW,CAAQ;QAjBrB,IAAK,CAAA,KAAA,GAAG,EAAE,CAAC;QAEX,IAAS,CAAA,SAAA,GAAG,GAAG,CAAC;QAChB,IAAc,CAAA,cAAA,GAAG,EAAE,CAAC;QACpB,IAAO,CAAA,OAAA,GAAG,IAAI,CAAC;QACf,IAAQ,CAAA,QAAA,GAAG,GAAG,CAAC;QAEf,IAAY,CAAA,YAAA,GAAkB,IAAI,CAAC;QACnC,IAAc,CAAA,cAAA,GAAkB,IAAI,CAAC;QACrC,IAAU,CAAA,UAAA,GAAkB,IAAI,CAAC;;AA2HzC,QAAA,IAAA,CAAA,WAAW,GAGsC,UAC/C,SAAiB,EACjB,IAAO,EAAA;AAEP,YAAA,IAAM,eAAe,GAAG,IAAI,eAAe,EAAE,CAAC;;AAE9C,YAAA,IAAM,QAAQ,GAAG,KAAI,CAAC,SAAS,CAAC,YAAA;AAC9B,gBAAA,OAAA,OAAO,CACL,QAAQ,CAAC,UAAG,KAAI,CAAC,WAAW,EAAA,eAAA,CAAe,EAAE,KAAI,CAAC,MAAM,CAAC,EACzD;AACE,oBAAA,IAAI,EAAE,WAAY,CAAA,MAAA,CAAA,KAAI,CAAC,QAAQ,wBAAc,KAAI,CAAC,SAAS,EAAA,YAAA,CAAA,CAAA,MAAA,CAAa,KAAI,CAAC,YAAY,yBAAe,KAAI,CAAC,cAAc,CAAE;AAC7H,oBAAA,aAAa,EAAE,SAAS;AACxB,oBAAA,SAAS,EAAE,IAAI;AACA,iBAAA;AACjB,gBAAA,eAAe,EACf,KAAI,CAAC,KAAK,EACV,KAAI,CAAC,YAAY,EACjB,KAAI,CAAC,cAAc,EACnB,KAAI,CAAC,WAAW,CACjB,CAAA;AAZD,aAYC,CACF,CAAC;YAEF,OAAO;gBACL,IAAI,EAAE,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC;gBAClC,KAAK,EAAE,QAAQ,CAAC,KAAK,CAAC,IAAI,CAAC,QAAQ,CAAC;aACrC,CAAC;AACJ,SAAC,CAAC;AACF,QAAA,IAAA,CAAA,cAAc,GAGmC,UAC/C,YAAoB,EACpB,IAAO,EAAA;AAEP,YAAA,IAAM,eAAe,GAAG,IAAI,eAAe,EAAE,CAAC;AAC9C,YAAA,IAAM,UAAU,GAAG,KAAI,CAAC,SAAS,CAAC,YAAA;AAChC,gBAAA,OAAO,OAAO,CACZ,QAAQ,CAAC,UAAG,KAAI,CAAC,WAAW,EAAA,kBAAA,CAAkB,EAAE,KAAI,CAAC,MAAM,CAAC,EAC5D;AACE,oBAAA,IAAI,EAAE,WAAY,CAAA,MAAA,CAAA,KAAI,CAAC,QAAQ,wBAAc,KAAI,CAAC,SAAS,EAAA,YAAA,CAAA,CAAA,MAAA,CAAa,KAAI,CAAC,YAAY,yBAAe,KAAI,CAAC,cAAc,CAAE;AAC7H,oBAAA,aAAa,EAAE,YAAY;AAC3B,oBAAA,SAAS,EAAE,IAAI;AACA,iBAAA,EACjB,eAAe,EACf,KAAI,CAAC,KAAK,EACV,KAAI,CAAC,YAAY,EACjB,KAAI,CAAC,cAAc,EACnB,KAAI,CAAC,WAAW,CACjB,CAAC;AACJ,aAAC,CAAC,CAAC;YAEH,OAAO;gBACL,IAAI,EAAE,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC;;;gBAGtC,MAAM,EAAE,cAAM,OAAA,eAAe,CAAC,KAAK,EAAE,GAAA;aACtC,CAAC;AACJ,SAAC,CAAC;AA7KA,QAAA,IAAI,gBAAgB,EAAE;AACpB,YAAA,IAAI,OAAO,gBAAgB,CAAC,IAAI,KAAK,QAAQ,EAAE;AAC7C,gBAAA,IAAI,CAAC,KAAK,GAAG,gBAAgB,CAAC,IAAI,CAAC;AACpC,aAAA;AACD,YAAA,IAAI,OAAO,gBAAgB,CAAC,UAAU,KAAK,WAAW,EAAE;AACtD,gBAAA,IAAI,CAAC,OAAO,GAAG,gBAAgB,CAAC,UAAU,CAAC;AAC5C,aAAA;AACD,YAAA,IAAI,CAAC,KAAK,GAAG,gBAAgB,CAAC,IAAI,CAAC;AACpC,SAAA;AACO,QAAA,IAAA,QAAQ,GAA6C,OAAO,SAApD,EAAa,OAAO,GAAyB,OAAO,CAAA,SAAhC,EAAE,SAAS,GAAc,OAAO,CAArB,SAAA,EAAE,OAAO,GAAK,OAAO,QAAZ,CAAa;AACrE,QAAA,IAAI,QAAQ,EAAE;AACZ,YAAA,IAAI,CAAC,SAAS,GAAG,QAAQ,CAAC;AAC3B,SAAA;AACD,QAAA,IAAI,OAAO,EAAE;AACX,YAAA,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC;AACzB,SAAA;AACD,QAAA,IAAI,CAAC,YAAY,GAAG,OAAO,CAAC;QAC5B,IAAI,CAAC,SAAS,EAAE;YACd,MAAM,IAAI,gBAAgB,CACxB,IAAI,CAAC,gBAAgB,EACrB,0BAA0B,CAC3B,CAAC;AACH,SAAA;AACD,QAAA,IAAI,CAAC,cAAc,GAAG,SAAS,CAAC;AAChC,QAAA,CAAA,EAAA,GAAA,IAAI,CAAC,YAAY,0CAAE,sBAAsB,CAAC,UAAA,KAAK,EAAA;AAC7C,YAAA,QAAQ,CAAC,uBAAA,CAAA,MAAA,CAAwB,KAAK,CAAE,CAAC,CAAC;AAC1C,YAAA,KAAI,CAAC,YAAY,GAAG,KAAK,CAAC;AAC5B,SAAC,CAAC,CAAC;AACH,QAAA,CAAA,EAAA,GAAA,IAAI,CAAC,gBAAgB,0CAAE,sBAAsB,CAAC,UAAA,MAAM,EAAA;AAC1C,YAAA,IAAA,KAAK,GAAK,MAAM,CAAA,KAAX,CAAY;AACzB,YAAA,QAAQ,CAAC,iCAAA,CAAA,MAAA,CAAkC,KAAK,CAAE,CAAC,CAAC;AACpD,YAAA,KAAI,CAAC,cAAc,GAAG,KAAK,CAAC;AAC9B,SAAC,CAAC,CAAC;KACJ;AACD,IAAA,MAAA,CAAA,cAAA,CAAI,aAAW,CAAA,SAAA,EAAA,aAAA,EAAA;AAAf,QAAA,GAAA,EAAA,YAAA;AACE,YAAA,OAAO,UAAU,CACf;gBACE,SAAS,EAAE,IAAI,CAAC,cAAc;gBAC9B,QAAQ,EAAE,IAAI,CAAC,SAAS;gBACxB,SAAS,EAAE,IAAI,CAAC,QAAQ;gBACxB,OAAO,EAAE,IAAI,CAAC,YAAY;aAC3B,EACD,EAAE,IAAI,EAAE,IAAI,CAAC,KAAK,EAAE,UAAU,EAAE,IAAI,CAAC,OAAO,EAAE,IAAI,EAAE,IAAI,CAAC,KAAK,EAAE,CACjE,CAAC;SACH;;;AAAA,KAAA,CAAA,CAAA;AACD,IAAA,aAAA,CAAA,SAAA,CAAA,WAAW,GAAX,UAAY,IAAY,EAAE,IAAa,EAAE,QAAkB,EAAA;AACzD,QAAA,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC;AAClB,QAAA,IAAI,OAAO,IAAI,KAAK,QAAQ,EAAE;AAC5B,YAAA,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC;AACnB,SAAA;AACD,QAAA,IAAI,OAAO,QAAQ,KAAK,WAAW,EAAE;AACnC,YAAA,IAAI,CAAC,OAAO,GAAG,QAAQ,CAAC;AACzB,SAAA;KACF,CAAA;IACD,aAAc,CAAA,SAAA,CAAA,cAAA,GAAd,UAAe,QAAuB,EAAA;AACpC,QAAA,IAAI,CAAC,YAAY,GAAG,QAAQ,CAAC;KAC9B,CAAA;IAEK,aAAW,CAAA,SAAA,CAAA,WAAA,GAAjB,UAAkB,UAAkB,EAAA;;AAAlB,QAAA,IAAA,UAAA,KAAA,KAAA,CAAA,EAAA,EAAA,UAAkB,GAAA,KAAA,CAAA,EAAA;;;;;;;AAC9B,wBAAA,cAAc,GAA2B,IAAI,OAAO,CAAC,UAAA,OAAO,EAAA;AAC9D,4BAAA,OAAA,OAAO,CAAC,KAAI,CAAC,YAAY,CAAC,CAAA;AAA1B,yBAA0B,CAC3B,CAAC;6BACE,IAAI,CAAC,gBAAgB,EAArB,OAAqB,CAAA,CAAA,YAAA,CAAA,CAAA,CAAA;AACvB,wBAAA,EAAA,GAAA,IAAI,CAAA;AAAmB,wBAAA,OAAA,CAAA,CAAA,YAAM,IAAI,CAAC,gBAAgB,CAAC,QAAQ,EAAE,CAAA,CAAA;;wBAA7D,EAAK,CAAA,cAAc,GAAG,CAAA,EAAA,IAAC,SAAsC,CAAC,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAE,KAAK,CAAC;;;wBAExE,IAAI,IAAI,CAAC,YAAY,EAAE;4BACrB,cAAc,GAAG,IAAI,CAAC,YAAY;AAC/B,iCAAA,QAAQ,iBAAiB,UAAU,CAAC;iCACpC,IAAI,CAAC,UAAA,IAAI,EAAA;gCACR,IAAI,CAAC,IAAI,EAAE;AACT,oCAAA,OAAO,IAAI,CAAC;AACb,iCAAA;AACD,gCAAA,KAAI,CAAC,YAAY,GAAG,IAAI,CAAC,WAAW,CAAC;gCACrC,OAAO,KAAI,CAAC,YAAY,CAAC;AAC3B,6BAAC,CAAC,CAAC;AACN,yBAAA;AAAM,6BAAA;AACL,4BAAA,cAAc,GAAG,IAAI,OAAO,CAAC,UAAA,OAAO,EAAA,EAAI,OAAA,OAAO,CAAC,EAAE,CAAC,CAAX,EAAW,CAAC,CAAC;AACtD,yBAAA;AACD,wBAAA,OAAA,CAAA,CAAA,aAAO,cAAc,CAAC,CAAA;;;;AACvB,KAAA,CAAA;IAED,aAAa,CAAA,SAAA,CAAA,aAAA,GAAb,UAAc,SAAwB,EAAA;AACpC,QAAA,IAAI,CAAC,UAAU,GAAG,SAAS,CAAC;KAC7B,CAAA;AAED,IAAA,aAAA,CAAA,SAAA,CAAA,SAAS,GAAT,UACE,cAA2D,EAC3D,KAAa,EAAA;QAFf,IAyBC,KAAA,GAAA,IAAA,CAAA;AAvBC,QAAA,IAAA,KAAA,KAAA,KAAA,CAAA,EAAA,EAAA,KAAa,GAAA,KAAA,CAAA,EAAA;QAEb,IAAI,UAAU,GAAG,KAAK,CAAC;AACvB,QAAA,OAAO,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC;aAC3B,IAAI,CAAC,UAAA,GAAG,EAAA;AACP,YAAA,UAAU,GAAG,KAAI,CAAC,UAAU,KAAK,GAAG,CAAC;AACrC,YAAA,KAAI,CAAC,UAAU,GAAG,GAAG,CAAC;AACtB,YAAA,OAAO,GAAG,CAAC;AACb,SAAC,CAAC;aACD,IAAI,CAAC,cAAc,CAAC;aACpB,KAAK,CAAC,UAAA,GAAG,EAAA;;YAER,IACE,MAAM,IAAI,GAAG;AACb,gBAAA,GAAG,CAAC,IAAI,KAAK,IAAI,CAAC,YAAY;AAC9B,gBAAA,CAAC,KAAK;AACN,gBAAA,UAAU,EACV;gBACA,QAAQ,CAAC,8BAA8B,CAAC,CAAC;gBACzC,OAAO,KAAI,CAAC,SAAS,CAAC,cAAc,EAAE,IAAI,CAAC,CAAC;AAC7C,aAAA;AACD,YAAA,MAAM,GAAG,CAAC;AACZ,SAAC,CAAC,CAAC;KACN,CAAA;IAgEH,OAAC,aAAA,CAAA;AAAD,CAAC,EAAA,CAAA;;AC7ND;;;;;;;;;;;;;;;AAeG;AAqCH;;;;;;AAMG;SACa,WAAW,CACzB,UAAuB,EACvB,YAAoB,EACpB,SAAqB,EAAA;IAErB,UAAU,CAAC,cAAc,EAAE,CAAC;AAC5B,IAAA,IAAM,GAAG,GAAiC;AACxC,QAAA,WAAW,EAAE,UAAU;AACvB,QAAA,IAAI,EAAE,YAAY;AAClB,QAAA,OAAO,EAAE,YAAY;AACrB,QAAA,SAAS,EAAE,SAAsB;KAClC,CAAC;AACF,IAAA,OAAO,GAAG,CAAC;AACb,CAAC;AAED;;AAEG;AACH,IAAA,eAAA,kBAAA,YAAA;AAEE,IAAA,SAAA,eAAA,CAAoB,UAAgC,EAAA;QAAhC,IAAU,CAAA,UAAA,GAAV,UAAU,CAAsB;QAD5C,IAAS,CAAA,SAAA,GAAgC,EAAE,CAAC;KACI;IACxD,eAAe,CAAA,SAAA,CAAA,eAAA,GAAf,UACE,WAAyC,EAAA;QAD3C,IAqBC,KAAA,GAAA,IAAA,CAAA;AAlBC,QAAA,IAAM,MAAM,GAAG,IAAI,CAAC,UAAU,CAAC,cAAc,CAC3C,WAAW,CAAC,IAAI,EAChB,WAAW,CAAC,SAAS,CACtB,CAAC;AACF,QAAA,IAAM,cAAc,GAAG,MAAM,CAAC,IAAI,CAAC,UAAA,GAAG,EAAA;YACpC,IAAM,GAAG,yBACJ,GAAG,CAAA,EAAA,EACN,MAAM,EAAE,aAAa,EACrB,GAAG,EAAE,WAAW,EAChB,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE,CAAC,cAAc,EAAE,EAAA,CACvC,CAAC;AACF,YAAA,OAAO,GAAG,CAAC;AACb,SAAC,CAAC,CAAC;AACH,QAAA,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;AAC5B,QAAA,IAAM,aAAa,GAAG,YAAA;YACpB,QAAC,KAAI,CAAC,SAAS,GAAG,KAAI,CAAC,SAAS,CAAC,MAAM,CAAC,UAAA,OAAO,IAAI,OAAA,OAAO,KAAK,MAAM,CAAA,EAAA,CAAC,EAAC;AAAvE,SAAuE,CAAC;AAC1E,QAAA,MAAM,CAAC,IAAI,CAAC,aAAa,EAAE,aAAa,CAAC,CAAC;AAC1C,QAAA,OAAO,cAAc,CAAC;KACvB,CAAA;IACH,OAAC,eAAA,CAAA;AAAD,CAAC,EAAA,EAAA;AAiBD;;;;AAIG;AACG,SAAU,eAAe,CAC7B,WAAyC,EAAA;IAEzC,OAAO,WAAW,CAAC,WAAW,CAAC,gBAAgB,CAAC,eAAe,CAAC,WAAW,CAAC,CAAC;AAC/E;;AChIA;;;;;;;;;;;;;;;AAeG;AA2CH,IAAM,uCAAuC,GAC3C,qCAAqC,CAAC;AAExC;;;;;AAKG;AACG,SAAU,YAAY,CAAC,QAAgB,EAAA;AACrC,IAAA,IAAA,EAAuB,GAAA,QAAQ,CAAC,KAAK,CAAC,KAAK,CAAC,EAA3C,QAAQ,GAAA,EAAA,CAAA,CAAA,CAAA,EAAE,QAAQ,QAAyB,CAAC;AACnD,IAAA,IAAM,QAAQ,GAAG,QAAQ,KAAK,OAAO,CAAC;AAChC,IAAA,IAAA,EAAuB,GAAA,QAAQ,CAAC,KAAK,CAAC,GAAG,CAAC,EAAzC,IAAI,GAAA,EAAA,CAAA,CAAA,CAAA,EAAE,YAAY,QAAuB,CAAC;AACjD,IAAA,IAAM,IAAI,GAAG,MAAM,CAAC,YAAY,CAAC,CAAC;IAClC,OAAO,EAAE,IAAI,EAAA,IAAA,EAAE,IAAI,EAAA,IAAA,EAAE,UAAU,EAAE,QAAQ,EAAE,CAAC;AAC9C,CAAC;AAQD;;AAEG;AACH,IAAA,WAAA,kBAAA,YAAA;;AAYE,IAAA,SAAA,WAAA,CACkB,GAAgB;;IAEf,kBAAsC,EACtC,aAAiD,EACjD,iBAA0D,EAAA;QAJ3D,IAAG,CAAA,GAAA,GAAH,GAAG,CAAa;QAEf,IAAkB,CAAA,kBAAA,GAAlB,kBAAkB,CAAoB;QACtC,IAAa,CAAA,aAAA,GAAb,aAAa,CAAoC;QACjD,IAAiB,CAAA,iBAAA,GAAjB,iBAAiB,CAAyC;QAd7E,IAAU,CAAA,UAAA,GAAG,KAAK,CAAC;QACnB,IAAY,CAAA,YAAA,GAAG,KAAK,CAAC;QAKrB,IAAoB,CAAA,oBAAA,GAAY,KAAK,CAAC;QAUpC,IAAI,OAAO,OAAO,KAAK,WAAW,IAAI,OAAO,CAAC,GAAG,EAAE;YACjD,IAAM,IAAI,GAAG,OAAO,CAAC,GAAG,CAAC,uCAAuC,CAAC,CAAC;AAClE,YAAA,IAAI,IAAI,EAAE;gBACR,QAAQ,CAAC,mCAAmC,CAAC,CAAC;AAC9C,gBAAA,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;AACvB,gBAAA,IAAI,CAAC,iBAAiB,GAAG,YAAY,CAAC,IAAI,CAAC,CAAC;AAC7C,aAAA;AACF,SAAA;KACF;;AAED,IAAA,WAAA,CAAA,SAAA,CAAA,gBAAgB,GAAhB,YAAA;AACE,QAAA,IAAI,CAAC,IAAI,CAAC,oBAAoB,EAAE;AAC9B,YAAA,IAAI,CAAC,oBAAoB,GAAG,IAAI,CAAC;AAClC,SAAA;KACF,CAAA;AACD,IAAA,WAAA,CAAA,SAAA,CAAA,OAAO,GAAP,YAAA;AACE,QAAA,sBAAsB,CACpB,IAAI,CAAC,GAAG,EACR,cAAc,EACd,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC,CACnC,CAAC;AACF,QAAA,OAAO,OAAO,CAAC,OAAO,EAAE,CAAC;KAC1B,CAAA;;AAGD,IAAA,WAAA,CAAA,SAAA,CAAA,WAAW,GAAX,YAAA;AACE,QAAA,IAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC,CAAC;QACjE,OAAO,IAAI,CAAC,SAAS,CAAC;AACtB,QAAA,OAAO,IAAI,CAAC;KACb,CAAA;;AAGD,IAAA,WAAA,CAAA,SAAA,CAAA,cAAc,GAAd,YAAA;QACE,IAAI,IAAI,CAAC,YAAY,EAAE;YACrB,OAAO;AACR,SAAA;AACD,QAAA,IAAI,IAAI,CAAC,eAAe,KAAK,SAAS,EAAE;YACtC,QAAQ,CAAC,2DAA2D,CAAC,CAAC;AACtE,YAAA,IAAI,CAAC,eAAe,GAAG,aAAa,CAAC;AACtC,SAAA;QAED,IAAI,IAAI,CAAC,aAAa,EAAE;YACtB,IAAI,CAAC,kBAAkB,GAAG,IAAI,oBAAoB,CAChD,IAAI,CAAC,GAAG,CAAC,IAAI,EACb,IAAI,CAAC,GAAG,CAAC,OAAO,EAChB,IAAI,CAAC,aAAa,CACnB,CAAC;AACH,SAAA;QACD,IAAI,IAAI,CAAC,iBAAiB,EAAE;AAC1B,YAAA,IAAI,CAAC,sBAAsB,GAAG,IAAI,qBAAqB,CACrD,IAAI,CAAC,GAAG,CAAC,IAAI,EACb,IAAI,CAAC,iBAAiB,CACvB,CAAC;AACH,SAAA;AAED,QAAA,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC;AACzB,QAAA,IAAI,CAAC,UAAU,GAAG,IAAI,IAAI,CAAC,eAAe,CACxC,IAAI,CAAC,kBAAkB,EACvB,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,MAAM,EACvB,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,KAAK,EACtB,IAAI,CAAC,kBAAkB,EACvB,IAAI,CAAC,sBAAsB,EAC3B,SAAS,EACT,IAAI,CAAC,oBAAoB,CAC1B,CAAC;QACF,IAAI,IAAI,CAAC,iBAAiB,EAAE;YAC1B,IAAI,CAAC,UAAU,CAAC,WAAW,CACzB,IAAI,CAAC,iBAAiB,CAAC,IAAI,EAC3B,IAAI,CAAC,iBAAiB,CAAC,IAAI,EAC3B,IAAI,CAAC,iBAAiB,CAAC,UAAU,CAClC,CAAC;AACH,SAAA;QACD,IAAI,CAAC,aAAa,GAAG,IAAI,YAAY,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;QACvD,IAAI,CAAC,gBAAgB,GAAG,IAAI,eAAe,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;KAC9D,CAAA;;IAGD,WAAc,CAAA,SAAA,CAAA,cAAA,GAAd,UAAe,gBAAkC,EAAA;QAC/C,IAAI,IAAI,CAAC,YAAY,EAAE;YACrB,QAAQ,CAAC,4CAA4C,CAAC,CAAC;YACvD,MAAM,IAAI,gBAAgB,CACxB,IAAI,CAAC,mBAAmB,EACxB,2CAA2C,CAC5C,CAAC;AACH,SAAA;AACD,QAAA,IAAI,CAAC,iBAAiB,GAAG,gBAAgB,CAAC;AAC1C,QAAA,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;KACxB,CAAA;IACH,OAAC,WAAA,CAAA;AAAD,CAAC,EAAA,EAAA;AAED;;;;;;AAMG;AACG,SAAU,0BAA0B,CACxC,EAAe,EACf,IAAY,EACZ,IAAa,EACb,UAAkB,EAAA;AAAlB,IAAA,IAAA,UAAA,KAAA,KAAA,CAAA,EAAA,EAAA,UAAkB,GAAA,KAAA,CAAA,EAAA;AAElB,IAAA,EAAE,CAAC,cAAc,CAAC,EAAE,IAAI,EAAA,IAAA,EAAE,IAAI,EAAA,IAAA,EAAE,UAAU,EAAA,UAAA,EAAE,CAAC,CAAC;AAChD,CAAC;AAgBe,SAAA,cAAc,CAC5B,YAA2C,EAC3C,eAAiC,EAAA;AAEjC,IAAA,IAAI,GAAgB,CAAC;AACrB,IAAA,IAAI,SAA0B,CAAC;IAC/B,IAAI,UAAU,IAAI,YAAY,EAAE;QAC9B,SAAS,GAAG,YAAY,CAAC;QACzB,GAAG,GAAG,MAAM,EAAE,CAAC;AAChB,KAAA;AAAM,SAAA;QACL,SAAS,GAAG,eAAgB,CAAC;QAC7B,GAAG,GAAG,YAAY,CAAC;AACpB,KAAA;AAED,IAAA,IAAI,CAAC,GAAG,IAAI,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,MAAM,KAAK,CAAC,EAAE;QACzC,GAAG,GAAG,MAAM,EAAE,CAAC;AAChB,KAAA;IACD,IAAM,QAAQ,GAAG,YAAY,CAAC,GAAG,EAAE,cAAc,CAAC,CAAC;IACnD,IAAM,UAAU,GAAG,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC;AAC7C,IAAA,IAAI,QAAQ,CAAC,aAAa,CAAC,UAAU,CAAC,EAAE;QACtC,IAAM,UAAU,GAAG,QAAQ,CAAC,YAAY,CAAC,EAAE,UAAU,EAAA,UAAA,EAAE,CAAC,CAAC;QACzD,IAAM,OAAO,GAAG,QAAQ,CAAC,UAAU,CAAC,UAAU,CAAC,CAAC;AAChD,QAAA,IAAM,YAAY,GAAG,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC;AACrD,QAAA,IAAI,YAAY,EAAE;YAChB,QAAQ,CAAC,0BAA0B,CAAC,CAAC;AACrC,YAAA,OAAO,UAAU,CAAC;AACnB,SAAA;AACF,KAAA;IACD,iBAAiB,CAAC,SAAS,CAAC,CAAC;IAE7B,QAAQ,CAAC,mCAAmC,CAAC,CAAC;;IAE9C,OAAO,QAAQ,CAAC,UAAU,CAAC;AACzB,QAAA,kBAAkB,EAAE,UAAU;AAC9B,QAAA,OAAO,EAAE,SAAS;AACnB,KAAA,CAAC,CAAC;AACL,CAAC;AAED;;;;;AAKG;AACG,SAAU,iBAAiB,CAAC,SAA0B,EAAA;IAC1D,IAAM,MAAM,GAAG,CAAC,WAAW,EAAE,UAAU,EAAE,SAAS,CAAC,CAAC;IACpD,IAAI,CAAC,SAAS,EAAE;QACd,MAAM,IAAI,gBAAgB,CAAC,IAAI,CAAC,gBAAgB,EAAE,oBAAoB,CAAC,CAAC;AACzE,KAAA;AACD,IAAA,MAAM,CAAC,OAAO,CAAC,UAAA,KAAK,EAAA;AAClB,QAAA,IAAI,SAAS,CAAC,KAAK,CAAC,KAAK,IAAI,IAAI,SAAS,CAAC,KAAK,CAAC,KAAK,SAAS,EAAE;YAC/D,MAAM,IAAI,gBAAgB,CAAC,IAAI,CAAC,gBAAgB,EAAE,EAAG,CAAA,MAAA,CAAA,KAAK,EAAW,WAAA,CAAA,CAAC,CAAC;AACxE,SAAA;AACH,KAAC,CAAC,CAAC;AACH,IAAA,OAAO,IAAI,CAAC;AACd,CAAC;AAED;;;;AAIG;AACG,SAAU,SAAS,CAAC,WAAwB,EAAA;AAChD,IAAA,OAAO,WAAW,CAAC,OAAO,EAAE,CAAC;;AAE/B;;AClQM,SAAU,mBAAmB,CAAC,OAAgB,EAAA;IAClD,aAAa,CAACA,aAAW,CAAC,CAAC;IAC3B,kBAAkB,CAChB,IAAI,SAAS,CACX,cAAc,EACd,UAAC,SAAS,EAAE,EAAyC,EAAA;YAAnB,QAAQ,GAAA,EAAA,CAAA,kBAAA,EAAE,OAAO,GAAA,EAAA,CAAA,OAAA,CAAA;QACjD,IAAM,GAAG,GAAG,SAAS,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC,YAAY,EAAG,CAAC;QACzD,IAAM,YAAY,GAAG,SAAS,CAAC,WAAW,CAAC,eAAe,CAAC,CAAC;QAC5D,IAAM,gBAAgB,GAAG,SAAS,CAAC,WAAW,CAAC,oBAAoB,CAAC,CAAC;QACrE,IAAI,OAAO,GAAG,OAA0B,CAAC;AACzC,QAAA,IAAI,QAAQ,EAAE;AACZ,YAAA,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC;AAChC,SAAA;AACD,QAAA,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,SAAS,EAAE;YAC1B,MAAM,IAAI,gBAAgB,CACxB,IAAI,CAAC,gBAAgB,EACrB,mFAAmF,CACpF,CAAC;AACH,SAAA;AACD,QAAA,OAAO,IAAI,WAAW,CACpB,GAAG,EACE,QAAA,CAAA,QAAA,CAAA,EAAA,EAAA,OAAO,KAAE,SAAS,EAAE,GAAG,CAAC,OAAO,CAAC,SAAU,EAAA,CAAA,EAC/C,YAAY,EACZ,gBAAgB,CACjB,CAAC;AACJ,KAAC,sCAEF,CAAC,oBAAoB,CAAC,IAAI,CAAC,CAC7B,CAAC;AACF,IAAA,eAAe,CAAC,IAAI,EAAE,OAAO,EAAE,OAAO,CAAC,CAAC;;AAExC,IAAA,eAAe,CAAC,IAAI,EAAE,OAAO,EAAE,MAAkB,CAAC,CAAC;AACrD;;AC9DA;;;;;;;;;;;;;;;AAeG;AA0DH;;;;AAIG;AACG,SAAU,YAAY,CAC1B,QAAmC,EAAA;IAEnC,OAAO,QAAQ,CAAC,WAAW,CAAC,aAAa,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC;AACnE,CAAC;AAwBD;;;;;;;AAOG;AACG,SAAU,QAAQ,CACtB,UAAuB,EACvB,SAAiB,EACjB,SAAqB,EACrB,YAA2C,EAAA;IAE3C,UAAU,CAAC,cAAc,EAAE,CAAC;IAC5B,UAAU,CAAC,aAAa,CAAC,KAAK,CAAC,SAAS,EAAE,SAAS,EAAE,YAAY,CAAC,CAAC;IACnE,OAAO;AACL,QAAA,WAAW,EAAE,UAAU;AACvB,QAAA,OAAO,EAAE,SAAS;AAClB,QAAA,IAAI,EAAE,SAAS;AACf,QAAA,SAAS,EAAE,SAAsB;KAClC,CAAC;AACJ,CAAC;AACD;;;;AAIG;AACG,SAAU,UAAU,CACxB,aAA6C,EAAA;AAG3C,IAAA,IAAA,EACE,GAAA,aAAa,CAD8B,OAAA,EAAlC,IAAI,GAAA,EAAA,CAAA,IAAA,EAAE,SAAS,GAAA,EAAA,CAAA,SAAA,EAAE,eAAe,GAAA,EAAA,CAAA,eAAE,CAC7B;IAClB,OAAO,QAAQ,CAAC,cAAc,CAAC,eAAe,CAAC,EAAE,IAAI,EAAE,SAAS,CAAC,CAAC;AACpE;;AC7IA;;;;;;;;;;;;;;;AAeG;AAaH;;;;;;;;;AASG;AACG,SAAU,YAAY,CAC1B,eAAgC,EAChC,QAAkC,EAClC,IAAgB,EAChB,YAAsB,EAAA;AAEtB,IAAA,IAAI,UAAuB,CAAC;AAC5B,IAAA,IAAI,QAAmB,CAAC;AACxB,IAAA,IAAI,QAAQ,IAAI,gBAAgB,IAAI,QAAQ,EAAE;QAC5C,UAAU,GAAG,QAAuB,CAAC;QACrC,QAAQ,GAAG,IAAI,CAAC;AACjB,KAAA;AAAM,SAAA;AACL,QAAA,UAAU,GAAG,cAAc,CAAC,eAAe,CAAC,CAAC;QAC7C,QAAQ,GAAG,QAAqB,CAAC;AAClC,KAAA;IACD,IAAI,CAAC,UAAU,KAAK,CAAC,QAAQ,IAAI,YAAY,CAAC,EAAE;QAC9C,MAAM,IAAI,gBAAgB,CAAC,IAAI,CAAC,gBAAgB,EAAE,qBAAqB,CAAC,CAAC;AAC1E,KAAA;IACD,OAAO,EAAE,EAAE,EAAE,UAAU,EAAE,IAAI,EAAE,QAAQ,EAAE,CAAC;AAC5C;;ACzDA;;;;;;;;;;;;;;;AAeG;AA0CH;;;;;;;AAOG;AACG,SAAU,SAAS,CACvB,0BAEkC,EAClC,gBAEyC,EACzC,OAA6B,EAC7B,UAAmC,EAAA;AAEnC,IAAA,IAAI,GAA8B,CAAC;AACnC,IAAA,IAAI,YAAwC,CAAC;IAC7C,IAAI,SAAS,IAAI,0BAA0B,EAAE;QAC3C,IAAM,aAAa,GACjB,0BAA0B,CAAC;AACrB,QAAA,IAAA,IAAI,GAAwB,aAAa,CAAA,IAArC,EAAE,MAAM,GAAgB,aAAa,CAAA,MAA7B,EAAE,SAAS,GAAK,aAAa,UAAlB,CAAmB;AAClD,QAAA,YAAY,GAAG;AACb,YAAA,IAAI,EAAA,IAAA;AACJ,YAAA,MAAM,EAAA,MAAA;AACN,YAAA,SAAS,EAAA,SAAA;SACV,CAAC;AACF,QAAA,GAAG,GAAG,UAAU,CAAC,aAAa,CAAC,CAAC;AACjC,KAAA;AAAM,SAAA;QACL,GAAG,GAAG,0BAA0B,CAAC;AAClC,KAAA;IACD,IAAI,QAAQ,GAAsD,SAAS,CAAC;AAC5E,IAAA,IAAI,OAAO,gBAAgB,KAAK,UAAU,EAAE;QAC1C,QAAQ,GAAG,gBAAgB,CAAC;AAC7B,KAAA;AAAM,SAAA;AACL,QAAA,QAAQ,GAAG,gBAAgB,CAAC,MAAM,CAAC;AACnC,QAAA,OAAO,GAAG,gBAAgB,CAAC,KAAK,CAAC;AACjC,QAAa,gBAAgB,CAAC,UAAU,CAAC;AAC1C,KAAA;IACD,IAAI,CAAC,QAAQ,EAAE;QACb,MAAM,IAAI,gBAAgB,CAAC,IAAI,CAAC,gBAAgB,EAAE,qBAAqB,CAAC,CAAC;AAC1E,KAAA;AACD,IAAA,OAAO,GAAG,CAAC,WAAW,CAAC,aAAa,CAAC,eAAe,CAClD,GAAG,EACH,QAAQ,EACR,OAAO,EACP,YAAY,CACb,CAAC;AACJ;;AC3GA;;;;AAIG;AAwBH,mBAAmB,EAAE;;;;"}