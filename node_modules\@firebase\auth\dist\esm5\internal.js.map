{"version": 3, "file": "internal.js", "sources": ["../../src/platform_cordova/plugins.ts", "../../src/platform_cordova/popup_redirect/utils.ts", "../../src/platform_cordova/popup_redirect/events.ts", "../../src/platform_cordova/popup_redirect/popup_redirect.ts", "../../internal/index.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2021 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nexport interface CordovaWindow extends Window {\n  cordova: {\n    plugins: {\n      browsertab: {\n        isAvailable(cb: (available: boolean) => void): void;\n        openUrl(url: string): void;\n        close(): void;\n      };\n    };\n\n    InAppBrowser: {\n      open(url: string, target: string, options: string): InAppBrowserRef;\n    };\n  };\n\n  universalLinks: {\n    subscribe(\n      n: null,\n      cb: (event: Record<string, string> | null) => void\n    ): void;\n  };\n\n  BuildInfo: {\n    readonly packageName: string;\n    readonly displayName: string;\n  };\n\n  handleOpenURL(url: string): void;\n}\n\nexport interface InAppBrowserRef {\n  close?: () => void;\n}\n\nexport function _cordovaWindow(): CordovaWindow {\n  return window as unknown as CordovaWindow;\n}\n", "/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { AuthProvider } from '../../model/public_types';\nimport { AuthErrorCode } from '../../core/errors';\nimport {\n  debugAssert,\n  _assert,\n  _createError,\n  _fail\n} from '../../core/util/assert';\nimport { _isAndroid, _isIOS, _isIOS7Or8 } from '../../core/util/browser';\nimport { _getRedirectUrl } from '../../core/util/handler';\nimport { AuthInternal } from '../../model/auth';\nimport { AuthEvent } from '../../model/popup_redirect';\nimport { InAppBrowserRef, _cordovaWindow } from '../plugins';\nimport {\n  GetProjectConfigRequest,\n  _getProjectConfig\n} from '../../api/project_config/get_project_config';\n\n/**\n * How long to wait after the app comes back into focus before concluding that\n * the user closed the sign in tab.\n */\nconst REDIRECT_TIMEOUT_MS = 2000;\n\n/**\n * Generates the URL for the OAuth handler.\n */\nexport async function _generateHandlerUrl(\n  auth: AuthInternal,\n  event: AuthEvent,\n  provider: AuthProvider\n): Promise<string> {\n  // Get the cordova plugins\n  const { BuildInfo } = _cordovaWindow();\n  debugAssert(event.sessionId, 'AuthEvent did not contain a session ID');\n  const sessionDigest = await computeSha256(event.sessionId);\n\n  const additionalParams: Record<string, string> = {};\n  if (_isIOS()) {\n    // iOS app identifier\n    additionalParams['ibi'] = BuildInfo.packageName;\n  } else if (_isAndroid()) {\n    // Android app identifier\n    additionalParams['apn'] = BuildInfo.packageName;\n  } else {\n    _fail(auth, AuthErrorCode.OPERATION_NOT_SUPPORTED);\n  }\n\n  // Add the display name if available\n  if (BuildInfo.displayName) {\n    additionalParams['appDisplayName'] = BuildInfo.displayName;\n  }\n\n  // Attached the hashed session ID\n  additionalParams['sessionId'] = sessionDigest;\n  return _getRedirectUrl(\n    auth,\n    provider,\n    event.type,\n    undefined,\n    event.eventId ?? undefined,\n    additionalParams\n  );\n}\n\n/**\n * Validates that this app is valid for this project configuration\n */\nexport async function _validateOrigin(auth: AuthInternal): Promise<void> {\n  const { BuildInfo } = _cordovaWindow();\n  const request: GetProjectConfigRequest = {};\n  if (_isIOS()) {\n    request.iosBundleId = BuildInfo.packageName;\n  } else if (_isAndroid()) {\n    request.androidPackageName = BuildInfo.packageName;\n  } else {\n    _fail(auth, AuthErrorCode.OPERATION_NOT_SUPPORTED);\n  }\n\n  // Will fail automatically if package name is not authorized\n  await _getProjectConfig(auth, request);\n}\n\nexport function _performRedirect(\n  handlerUrl: string\n): Promise<InAppBrowserRef | null> {\n  // Get the cordova plugins\n  const { cordova } = _cordovaWindow();\n\n  return new Promise(resolve => {\n    cordova.plugins.browsertab.isAvailable(browserTabIsAvailable => {\n      let iabRef: InAppBrowserRef | null = null;\n      if (browserTabIsAvailable) {\n        cordova.plugins.browsertab.openUrl(handlerUrl);\n      } else {\n        // TODO: Return the inappbrowser ref that's returned from the open call\n        iabRef = cordova.InAppBrowser.open(\n          handlerUrl,\n          _isIOS7Or8() ? '_blank' : '_system',\n          'location=yes'\n        );\n      }\n      resolve(iabRef);\n    });\n  });\n}\n\n// Thin interface wrapper to avoid circular dependency with ./events module\ninterface PassiveAuthEventListener {\n  addPassiveListener(cb: () => void): void;\n  removePassiveListener(cb: () => void): void;\n}\n\n/**\n * This function waits for app activity to be seen before resolving. It does\n * this by attaching listeners to various dom events. Once the app is determined\n * to be visible, this promise resolves. AFTER that resolution, the listeners\n * are detached and any browser tabs left open will be closed.\n */\nexport async function _waitForAppResume(\n  auth: AuthInternal,\n  eventListener: PassiveAuthEventListener,\n  iabRef: InAppBrowserRef | null\n): Promise<void> {\n  // Get the cordova plugins\n  const { cordova } = _cordovaWindow();\n\n  let cleanup = (): void => {};\n  try {\n    await new Promise<void>((resolve, reject) => {\n      let onCloseTimer: number | null = null;\n\n      // DEFINE ALL THE CALLBACKS =====\n      function authEventSeen(): void {\n        // Auth event was detected. Resolve this promise and close the extra\n        // window if it's still open.\n        resolve();\n        const closeBrowserTab = cordova.plugins.browsertab?.close;\n        if (typeof closeBrowserTab === 'function') {\n          closeBrowserTab();\n        }\n        // Close inappbrowser embedded webview in iOS7 and 8 case if still\n        // open.\n        if (typeof iabRef?.close === 'function') {\n          iabRef.close();\n        }\n      }\n\n      function resumed(): void {\n        if (onCloseTimer) {\n          // This code already ran; do not rerun.\n          return;\n        }\n\n        onCloseTimer = window.setTimeout(() => {\n          // Wait two seconds after resume then reject.\n          reject(_createError(auth, AuthErrorCode.REDIRECT_CANCELLED_BY_USER));\n        }, REDIRECT_TIMEOUT_MS);\n      }\n\n      function visibilityChanged(): void {\n        if (document?.visibilityState === 'visible') {\n          resumed();\n        }\n      }\n\n      // ATTACH ALL THE LISTENERS =====\n      // Listen for the auth event\n      eventListener.addPassiveListener(authEventSeen);\n\n      // Listen for resume and visibility events\n      document.addEventListener('resume', resumed, false);\n      if (_isAndroid()) {\n        document.addEventListener('visibilitychange', visibilityChanged, false);\n      }\n\n      // SETUP THE CLEANUP FUNCTION =====\n      cleanup = () => {\n        eventListener.removePassiveListener(authEventSeen);\n        document.removeEventListener('resume', resumed, false);\n        document.removeEventListener(\n          'visibilitychange',\n          visibilityChanged,\n          false\n        );\n        if (onCloseTimer) {\n          window.clearTimeout(onCloseTimer);\n        }\n      };\n    });\n  } finally {\n    cleanup();\n  }\n}\n\n/**\n * Checks the configuration of the Cordova environment. This has no side effect\n * if the configuration is correct; otherwise it throws an error with the\n * missing plugin.\n */\nexport function _checkCordovaConfiguration(auth: AuthInternal): void {\n  const win = _cordovaWindow();\n  // Check all dependencies installed.\n  // https://github.com/nordnet/cordova-universal-links-plugin\n  // Note that cordova-universal-links-plugin has been abandoned.\n  // A fork with latest fixes is available at:\n  // https://www.npmjs.com/package/cordova-universal-links-plugin-fix\n  _assert(\n    typeof win?.universalLinks?.subscribe === 'function',\n    auth,\n    AuthErrorCode.INVALID_CORDOVA_CONFIGURATION,\n    {\n      missingPlugin: 'cordova-universal-links-plugin-fix'\n    }\n  );\n\n  // https://www.npmjs.com/package/cordova-plugin-buildinfo\n  _assert(\n    typeof win?.BuildInfo?.packageName !== 'undefined',\n    auth,\n    AuthErrorCode.INVALID_CORDOVA_CONFIGURATION,\n    {\n      missingPlugin: 'cordova-plugin-buildInfo'\n    }\n  );\n\n  // https://github.com/google/cordova-plugin-browsertab\n  _assert(\n    typeof win?.cordova?.plugins?.browsertab?.openUrl === 'function',\n    auth,\n    AuthErrorCode.INVALID_CORDOVA_CONFIGURATION,\n    {\n      missingPlugin: 'cordova-plugin-browsertab'\n    }\n  );\n  _assert(\n    typeof win?.cordova?.plugins?.browsertab?.isAvailable === 'function',\n    auth,\n    AuthErrorCode.INVALID_CORDOVA_CONFIGURATION,\n    {\n      missingPlugin: 'cordova-plugin-browsertab'\n    }\n  );\n\n  // https://cordova.apache.org/docs/en/latest/reference/cordova-plugin-inappbrowser/\n  _assert(\n    typeof win?.cordova?.InAppBrowser?.open === 'function',\n    auth,\n    AuthErrorCode.INVALID_CORDOVA_CONFIGURATION,\n    {\n      missingPlugin: 'cordova-plugin-inappbrowser'\n    }\n  );\n}\n\n/**\n * Computes the SHA-256 of a session ID. The SubtleCrypto interface is only\n * available in \"secure\" contexts, which covers Cordova (which is served on a file\n * protocol).\n */\nasync function computeSha256(sessionId: string): Promise<string> {\n  const bytes = stringToArrayBuffer(sessionId);\n\n  // TODO: For IE11 crypto has a different name and this operation comes back\n  //       as an object, not a promise. This is the old proposed standard that\n  //       is used by IE11:\n  // https://www.w3.org/TR/2013/WD-WebCryptoAPI-20130108/#cryptooperation-interface\n  const buf = await crypto.subtle.digest('SHA-256', bytes);\n  const arr = Array.from(new Uint8Array(buf));\n  return arr.map(num => num.toString(16).padStart(2, '0')).join('');\n}\n\nfunction stringToArrayBuffer(str: string): Uint8Array {\n  // This function is only meant to deal with an ASCII charset and makes\n  // certain simplifying assumptions.\n  debugAssert(\n    /[0-9a-zA-Z]+/.test(str),\n    'Can only convert alpha-numeric strings'\n  );\n  if (typeof TextEncoder !== 'undefined') {\n    return new TextEncoder().encode(str);\n  }\n\n  const buff = new ArrayBuffer(str.length);\n  const view = new Uint8Array(buff);\n  for (let i = 0; i < str.length; i++) {\n    view[i] = str.charCodeAt(i);\n  }\n  return view;\n}\n", "/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { querystringDecode } from '@firebase/util';\nimport { AuthEventManager } from '../../core/auth/auth_event_manager';\nimport { AuthErrorCode } from '../../core/errors';\nimport { PersistedBlob, PersistenceInternal } from '../../core/persistence';\nimport {\n  KeyName,\n  _persistenceKeyName\n} from '../../core/persistence/persistence_user_manager';\nimport { _createError } from '../../core/util/assert';\nimport { _getInstance } from '../../core/util/instantiator';\nimport { AuthInternal } from '../../model/auth';\nimport { AuthEvent, AuthEventType } from '../../model/popup_redirect';\nimport { browserLocalPersistence } from '../../platform_browser/persistence/local_storage';\n\nconst SESSION_ID_LENGTH = 20;\n\n/** Custom AuthEventManager that adds passive listeners to events */\nexport class CordovaAuthEventManager extends AuthEventManager {\n  private readonly passiveListeners = new Set<(e: AuthEvent) => void>();\n  private resolveInitialized!: () => void;\n  private initPromise = new Promise<void>(resolve => {\n    this.resolveInitialized = resolve;\n  });\n\n  addPassiveListener(cb: (e: AuthEvent) => void): void {\n    this.passiveListeners.add(cb);\n  }\n\n  removePassiveListener(cb: (e: AuthEvent) => void): void {\n    this.passiveListeners.delete(cb);\n  }\n\n  // In a Cordova environment, this manager can live through multiple redirect\n  // operations\n  resetRedirect(): void {\n    this.queuedRedirectEvent = null;\n    this.hasHandledPotentialRedirect = false;\n  }\n\n  /** Override the onEvent method */\n  onEvent(event: AuthEvent): boolean {\n    this.resolveInitialized();\n    this.passiveListeners.forEach(cb => cb(event));\n    return super.onEvent(event);\n  }\n\n  async initialized(): Promise<void> {\n    await this.initPromise;\n  }\n}\n\n/**\n * Generates a (partial) {@link AuthEvent}.\n */\nexport function _generateNewEvent(\n  auth: AuthInternal,\n  type: AuthEventType,\n  eventId: string | null = null\n): AuthEvent {\n  return {\n    type,\n    eventId,\n    urlResponse: null,\n    sessionId: generateSessionId(),\n    postBody: null,\n    tenantId: auth.tenantId,\n    error: _createError(auth, AuthErrorCode.NO_AUTH_EVENT)\n  };\n}\n\nexport function _savePartialEvent(\n  auth: AuthInternal,\n  event: AuthEvent\n): Promise<void> {\n  return storage()._set(persistenceKey(auth), event as object as PersistedBlob);\n}\n\nexport async function _getAndRemoveEvent(\n  auth: AuthInternal\n): Promise<AuthEvent | null> {\n  const event = (await storage()._get(\n    persistenceKey(auth)\n  )) as AuthEvent | null;\n  if (event) {\n    await storage()._remove(persistenceKey(auth));\n  }\n  return event;\n}\n\nexport function _eventFromPartialAndUrl(\n  partialEvent: AuthEvent,\n  url: string\n): AuthEvent | null {\n  // Parse the deep link within the dynamic link URL.\n  const callbackUrl = _getDeepLinkFromCallback(url);\n  // Confirm it is actually a callback URL.\n  // Currently the universal link will be of this format:\n  // https://<AUTH_DOMAIN>/__/auth/callback<OAUTH_RESPONSE>\n  // This is a fake URL but is not intended to take the user anywhere\n  // and just redirect to the app.\n  if (callbackUrl.includes('/__/auth/callback')) {\n    // Check if there is an error in the URL.\n    // This mechanism is also used to pass errors back to the app:\n    // https://<AUTH_DOMAIN>/__/auth/callback?firebaseError=<STRINGIFIED_ERROR>\n    const params = searchParamsOrEmpty(callbackUrl);\n    // Get the error object corresponding to the stringified error if found.\n    const errorObject = params['firebaseError']\n      ? parseJsonOrNull(decodeURIComponent(params['firebaseError']))\n      : null;\n    const code = errorObject?.['code']?.split('auth/')?.[1];\n    const error = code ? _createError(code) : null;\n    if (error) {\n      return {\n        type: partialEvent.type,\n        eventId: partialEvent.eventId,\n        tenantId: partialEvent.tenantId,\n        error,\n        urlResponse: null,\n        sessionId: null,\n        postBody: null\n      };\n    } else {\n      return {\n        type: partialEvent.type,\n        eventId: partialEvent.eventId,\n        tenantId: partialEvent.tenantId,\n        sessionId: partialEvent.sessionId,\n        urlResponse: callbackUrl,\n        postBody: null\n      };\n    }\n  }\n\n  return null;\n}\n\nfunction generateSessionId(): string {\n  const chars = [];\n  const allowedChars =\n    '1234567890abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ';\n  for (let i = 0; i < SESSION_ID_LENGTH; i++) {\n    const idx = Math.floor(Math.random() * allowedChars.length);\n    chars.push(allowedChars.charAt(idx));\n  }\n  return chars.join('');\n}\n\nfunction storage(): PersistenceInternal {\n  return _getInstance(browserLocalPersistence);\n}\n\nfunction persistenceKey(auth: AuthInternal): string {\n  return _persistenceKeyName(KeyName.AUTH_EVENT, auth.config.apiKey, auth.name);\n}\n\nfunction parseJsonOrNull(json: string): ReturnType<typeof JSON.parse> | null {\n  try {\n    return JSON.parse(json);\n  } catch (e) {\n    return null;\n  }\n}\n\n// Exported for testing\nexport function _getDeepLinkFromCallback(url: string): string {\n  const params = searchParamsOrEmpty(url);\n  const link = params['link'] ? decodeURIComponent(params['link']) : undefined;\n  // Double link case (automatic redirect)\n  const doubleDeepLink = searchParamsOrEmpty(link)['link'];\n  // iOS custom scheme links.\n  const iOSDeepLink = params['deep_link_id']\n    ? decodeURIComponent(params['deep_link_id'])\n    : undefined;\n  const iOSDoubleDeepLink = searchParamsOrEmpty(iOSDeepLink)['link'];\n  return iOSDoubleDeepLink || iOSDeepLink || doubleDeepLink || link || url;\n}\n\n/**\n * Optimistically tries to get search params from a string, or else returns an\n * empty search params object.\n */\nfunction searchParamsOrEmpty(url: string | undefined): Record<string, string> {\n  if (!url?.includes('?')) {\n    return {};\n  }\n\n  const [_, ...rest] = url.split('?');\n  return querystringDecode(rest.join('?')) as Record<string, string>;\n}\n", "/**\n * @license\n * Copyright 2021 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { AuthProvider, PopupRedirectResolver } from '../../model/public_types';\nimport { browserSessionPersistence } from '../../platform_browser/persistence/session_storage';\nimport { AuthInternal } from '../../model/auth';\nimport {\n  AuthEvent,\n  AuthEventType,\n  PopupRedirectResolverInternal\n} from '../../model/popup_redirect';\nimport { AuthPopup } from '../../platform_browser/util/popup';\nimport { _createError, _fail } from '../../core/util/assert';\nimport { AuthErrorCode } from '../../core/errors';\nimport {\n  _checkCordovaConfiguration,\n  _generateHandlerUrl,\n  _performRedirect,\n  _validateOrigin,\n  _waitForAppResume\n} from './utils';\nimport {\n  CordovaAuthEventManager,\n  _eventFromPartialAndUrl,\n  _generateNewEvent,\n  _getAndRemoveEvent,\n  _savePartialEvent\n} from './events';\nimport { AuthEventManager } from '../../core/auth/auth_event_manager';\nimport { _getRedirectResult } from '../../platform_browser/strategies/redirect';\nimport {\n  _clearRedirectOutcomes,\n  _overrideRedirectResult\n} from '../../core/strategies/redirect';\nimport { _cordovaWindow } from '../plugins';\n\n/**\n * How long to wait for the initial auth event before concluding no\n * redirect pending\n */\nconst INITIAL_EVENT_TIMEOUT_MS = 500;\n\nclass CordovaPopupRedirectResolver implements PopupRedirectResolverInternal {\n  readonly _redirectPersistence = browserSessionPersistence;\n  readonly _shouldInitProactively = true; // This is lightweight for Cordova\n  private readonly eventManagers = new Map<string, CordovaAuthEventManager>();\n  private readonly originValidationPromises: Record<string, Promise<void>> = {};\n\n  _completeRedirectFn = _getRedirectResult;\n  _overrideRedirectResult = _overrideRedirectResult;\n\n  async _initialize(auth: AuthInternal): Promise<CordovaAuthEventManager> {\n    const key = auth._key();\n    let manager = this.eventManagers.get(key);\n    if (!manager) {\n      manager = new CordovaAuthEventManager(auth);\n      this.eventManagers.set(key, manager);\n      this.attachCallbackListeners(auth, manager);\n    }\n    return manager;\n  }\n\n  _openPopup(auth: AuthInternal): Promise<AuthPopup> {\n    _fail(auth, AuthErrorCode.OPERATION_NOT_SUPPORTED);\n  }\n\n  async _openRedirect(\n    auth: AuthInternal,\n    provider: AuthProvider,\n    authType: AuthEventType,\n    eventId?: string\n  ): Promise<void> {\n    _checkCordovaConfiguration(auth);\n    const manager = await this._initialize(auth);\n    await manager.initialized();\n\n    // Reset the persisted redirect states. This does not matter on Web where\n    // the redirect always blows away application state entirely. On Cordova,\n    // the app maintains control flow through the redirect.\n    manager.resetRedirect();\n    _clearRedirectOutcomes();\n\n    await this._originValidation(auth);\n\n    const event = _generateNewEvent(auth, authType, eventId);\n    await _savePartialEvent(auth, event);\n    const url = await _generateHandlerUrl(auth, event, provider);\n    const iabRef = await _performRedirect(url);\n    return _waitForAppResume(auth, manager, iabRef);\n  }\n\n  _isIframeWebStorageSupported(\n    _auth: AuthInternal,\n    _cb: (support: boolean) => unknown\n  ): void {\n    throw new Error('Method not implemented.');\n  }\n\n  _originValidation(auth: AuthInternal): Promise<void> {\n    const key = auth._key();\n    if (!this.originValidationPromises[key]) {\n      this.originValidationPromises[key] = _validateOrigin(auth);\n    }\n\n    return this.originValidationPromises[key];\n  }\n\n  private attachCallbackListeners(\n    auth: AuthInternal,\n    manager: AuthEventManager\n  ): void {\n    // Get the global plugins\n    const { universalLinks, handleOpenURL, BuildInfo } = _cordovaWindow();\n\n    const noEventTimeout = setTimeout(async () => {\n      // We didn't see that initial event. Clear any pending object and\n      // dispatch no event\n      await _getAndRemoveEvent(auth);\n      manager.onEvent(generateNoEvent());\n    }, INITIAL_EVENT_TIMEOUT_MS);\n\n    const universalLinksCb = async (\n      eventData: Record<string, string> | null\n    ): Promise<void> => {\n      // We have an event so we can clear the no event timeout\n      clearTimeout(noEventTimeout);\n\n      const partialEvent = await _getAndRemoveEvent(auth);\n      let finalEvent: AuthEvent | null = null;\n      if (partialEvent && eventData?.['url']) {\n        finalEvent = _eventFromPartialAndUrl(partialEvent, eventData['url']);\n      }\n\n      // If finalEvent is never filled, trigger with no event\n      manager.onEvent(finalEvent || generateNoEvent());\n    };\n\n    // Universal links subscriber doesn't exist for iOS, so we need to check\n    if (\n      typeof universalLinks !== 'undefined' &&\n      typeof universalLinks.subscribe === 'function'\n    ) {\n      universalLinks.subscribe(null, universalLinksCb);\n    }\n\n    // iOS 7 or 8 custom URL schemes.\n    // This is also the current default behavior for iOS 9+.\n    // For this to work, cordova-plugin-customurlscheme needs to be installed.\n    // https://github.com/EddyVerbruggen/Custom-URL-scheme\n    // Do not overwrite the existing developer's URL handler.\n    const existingHandleOpenURL = handleOpenURL;\n    const packagePrefix = `${BuildInfo.packageName.toLowerCase()}://`;\n    _cordovaWindow().handleOpenURL = async url => {\n      if (url.toLowerCase().startsWith(packagePrefix)) {\n        // We want this intentionally to float\n        // eslint-disable-next-line @typescript-eslint/no-floating-promises\n        universalLinksCb({ url });\n      }\n      // Call the developer's handler if it is present.\n      if (typeof existingHandleOpenURL === 'function') {\n        try {\n          existingHandleOpenURL(url);\n        } catch (e) {\n          // This is a developer error. Don't stop the flow of the SDK.\n          console.error(e);\n        }\n      }\n    };\n  }\n}\n\n/**\n * An implementation of {@link PopupRedirectResolver} suitable for Cordova\n * based applications.\n *\n * @public\n */\nexport const cordovaPopupRedirectResolver: PopupRedirectResolver =\n  CordovaPopupRedirectResolver;\n\nfunction generateNoEvent(): AuthEvent {\n  return {\n    type: AuthEventType.UNKNOWN,\n    eventId: null,\n    sessionId: null,\n    urlResponse: null,\n    postBody: null,\n    tenantId: null,\n    error: _createError(AuthErrorCode.NO_AUTH_EVENT)\n  };\n}\n", "/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { _castAuth } from '../src/core/auth/auth_impl';\nimport { Auth } from '../src/model/public_types';\n\n/**\n * This interface is intended only for use by @firebase/auth-compat, do not use directly\n */\nexport * from '../index';\n\nexport { SignInWithIdpResponse } from '../src/api/authentication/idp';\nexport { AuthErrorCode } from '../src/core/errors';\nexport { PersistenceInternal } from '../src/core/persistence';\nexport { _persistenceKeyName } from '../src/core/persistence/persistence_user_manager';\nexport { UserImpl } from '../src/core/user/user_impl';\nexport { _getInstance } from '../src/core/util/instantiator';\nexport {\n  PopupRedirectResolverInternal,\n  EventManager,\n  AuthEventType\n} from '../src/model/popup_redirect';\nexport { UserCredentialInternal, UserParameters } from '../src/model/user';\nexport { AuthInternal, ConfigInternal } from '../src/model/auth';\nexport { DefaultConfig, AuthImpl, _castAuth } from '../src/core/auth/auth_impl';\n\nexport { ClientPlatform, _getClientVersion } from '../src/core/util/version';\n\nexport { _generateEventId } from '../src/core/util/event_id';\nexport { TaggedWithTokenResponse } from '../src/model/id_token';\nexport { _fail, _assert } from '../src/core/util/assert';\nexport { AuthPopup } from '../src/platform_browser/util/popup';\nexport { _getRedirectResult } from '../src/platform_browser/strategies/redirect';\nexport { _overrideRedirectResult } from '../src/core/strategies/redirect';\nexport { cordovaPopupRedirectResolver } from '../src/platform_cordova/popup_redirect/popup_redirect';\nexport { FetchProvider } from '../src/core/util/fetch_provider';\nexport { SAMLAuthCredential } from '../src/core/credentials/saml';\n\n// This function should only be called by frameworks (e.g. FirebaseUI-web) to log their usage.\n// It is not intended for direct use by developer apps. NO jsdoc here to intentionally leave it out\n// of autogenerated documentation pages to reduce accidental misuse.\nexport function addFrameworkForLogging(auth: Auth, framework: string): void {\n  _castAuth(auth)._logFramework(framework);\n}\n"], "names": [], "mappings": ";;;;;;;;AAAA;;;;;;;;;;;;;;;AAeG;SAoCa,cAAc,GAAA;AAC5B,IAAA,OAAO,MAAkC,CAAC;AAC5C;;ACrDA;;;;;;;;;;;;;;;AAeG;AAoBH;;;AAGG;AACH,IAAM,mBAAmB,GAAG,IAAI,CAAC;AAEjC;;AAEG;SACmB,mBAAmB,CACvC,IAAkB,EAClB,KAAgB,EAChB,QAAsB,EAAA;;;;;;;AAGd,oBAAA,SAAS,GAAK,cAAc,EAAE,CAAA,SAArB,CAAsB;AACvC,oBAAA,WAAW,CAAC,KAAK,CAAC,SAAS,EAAE,wCAAwC,CAAC,CAAC;AACjD,oBAAA,OAAA,CAAA,CAAA,YAAM,aAAa,CAAC,KAAK,CAAC,SAAS,CAAC,CAAA,CAAA;;AAApD,oBAAA,aAAa,GAAG,EAAoC,CAAA,IAAA,EAAA,CAAA;oBAEpD,gBAAgB,GAA2B,EAAE,CAAC;oBACpD,IAAI,MAAM,EAAE,EAAE;;AAEZ,wBAAA,gBAAgB,CAAC,KAAK,CAAC,GAAG,SAAS,CAAC,WAAW,CAAC;AACjD,qBAAA;yBAAM,IAAI,UAAU,EAAE,EAAE;;AAEvB,wBAAA,gBAAgB,CAAC,KAAK,CAAC,GAAG,SAAS,CAAC,WAAW,CAAC;AACjD,qBAAA;AAAM,yBAAA;wBACL,KAAK,CAAC,IAAI,EAAA,6CAAA,6CAAwC,CAAC;AACpD,qBAAA;;oBAGD,IAAI,SAAS,CAAC,WAAW,EAAE;AACzB,wBAAA,gBAAgB,CAAC,gBAAgB,CAAC,GAAG,SAAS,CAAC,WAAW,CAAC;AAC5D,qBAAA;;AAGD,oBAAA,gBAAgB,CAAC,WAAW,CAAC,GAAG,aAAa,CAAC;oBAC9C,OAAO,CAAA,CAAA,aAAA,eAAe,CACpB,IAAI,EACJ,QAAQ,EACR,KAAK,CAAC,IAAI,EACV,SAAS,EACT,CAAA,EAAA,GAAA,KAAK,CAAC,OAAO,mCAAI,SAAS,EAC1B,gBAAgB,CACjB,CAAC,CAAA;;;;AACH,CAAA;AAED;;AAEG;AACG,SAAgB,eAAe,CAAC,IAAkB,EAAA;;;;;;AAC9C,oBAAA,SAAS,GAAK,cAAc,EAAE,CAAA,SAArB,CAAsB;oBACjC,OAAO,GAA4B,EAAE,CAAC;oBAC5C,IAAI,MAAM,EAAE,EAAE;AACZ,wBAAA,OAAO,CAAC,WAAW,GAAG,SAAS,CAAC,WAAW,CAAC;AAC7C,qBAAA;yBAAM,IAAI,UAAU,EAAE,EAAE;AACvB,wBAAA,OAAO,CAAC,kBAAkB,GAAG,SAAS,CAAC,WAAW,CAAC;AACpD,qBAAA;AAAM,yBAAA;wBACL,KAAK,CAAC,IAAI,EAAA,6CAAA,6CAAwC,CAAC;AACpD,qBAAA;;AAGD,oBAAA,OAAA,CAAA,CAAA,YAAM,iBAAiB,CAAC,IAAI,EAAE,OAAO,CAAC,CAAA,CAAA;;;AAAtC,oBAAA,EAAA,CAAA,IAAA,EAAsC,CAAC;;;;;AACxC,CAAA;AAEK,SAAU,gBAAgB,CAC9B,UAAkB,EAAA;;AAGV,IAAA,IAAA,OAAO,GAAK,cAAc,EAAE,QAArB,CAAsB;AAErC,IAAA,OAAO,IAAI,OAAO,CAAC,UAAA,OAAO,EAAA;QACxB,OAAO,CAAC,OAAO,CAAC,UAAU,CAAC,WAAW,CAAC,UAAA,qBAAqB,EAAA;YAC1D,IAAI,MAAM,GAA2B,IAAI,CAAC;AAC1C,YAAA,IAAI,qBAAqB,EAAE;gBACzB,OAAO,CAAC,OAAO,CAAC,UAAU,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC;AAChD,aAAA;AAAM,iBAAA;;gBAEL,MAAM,GAAG,OAAO,CAAC,YAAY,CAAC,IAAI,CAChC,UAAU,EACV,UAAU,EAAE,GAAG,QAAQ,GAAG,SAAS,EACnC,cAAc,CACf,CAAC;AACH,aAAA;YACD,OAAO,CAAC,MAAM,CAAC,CAAC;AAClB,SAAC,CAAC,CAAC;AACL,KAAC,CAAC,CAAC;AACL,CAAC;AAQD;;;;;AAKG;SACmB,iBAAiB,CACrC,IAAkB,EAClB,aAAuC,EACvC,MAA8B,EAAA;;;;;;AAGtB,oBAAA,OAAO,GAAK,cAAc,EAAE,CAAA,OAArB,CAAsB;oBAEjC,OAAO,GAAG,YAAa,GAAC,CAAC;;;;AAE3B,oBAAA,OAAA,CAAA,CAAA,YAAM,IAAI,OAAO,CAAO,UAAC,OAAO,EAAE,MAAM,EAAA;4BACtC,IAAI,YAAY,GAAkB,IAAI,CAAC;;AAGvC,4BAAA,SAAS,aAAa,GAAA;;;;AAGpB,gCAAA,OAAO,EAAE,CAAC;gCACV,IAAM,eAAe,GAAG,CAAA,EAAA,GAAA,OAAO,CAAC,OAAO,CAAC,UAAU,MAAE,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,KAAK,CAAC;AAC1D,gCAAA,IAAI,OAAO,eAAe,KAAK,UAAU,EAAE;AACzC,oCAAA,eAAe,EAAE,CAAC;AACnB,iCAAA;;;AAGD,gCAAA,IAAI,QAAO,MAAM,KAAA,IAAA,IAAN,MAAM,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAN,MAAM,CAAE,KAAK,CAAA,KAAK,UAAU,EAAE;oCACvC,MAAM,CAAC,KAAK,EAAE,CAAC;AAChB,iCAAA;6BACF;AAED,4BAAA,SAAS,OAAO,GAAA;AACd,gCAAA,IAAI,YAAY,EAAE;;oCAEhB,OAAO;AACR,iCAAA;AAED,gCAAA,YAAY,GAAG,MAAM,CAAC,UAAU,CAAC,YAAA;;AAE/B,oCAAA,MAAM,CAAC,YAAY,CAAC,IAAI,EAAA,4BAAA,gDAA2C,CAAC,CAAC;iCACtE,EAAE,mBAAmB,CAAC,CAAC;6BACzB;AAED,4BAAA,SAAS,iBAAiB,GAAA;gCACxB,IAAI,CAAA,QAAQ,KAAA,IAAA,IAAR,QAAQ,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAR,QAAQ,CAAE,eAAe,MAAK,SAAS,EAAE;AAC3C,oCAAA,OAAO,EAAE,CAAC;AACX,iCAAA;6BACF;;;AAID,4BAAA,aAAa,CAAC,kBAAkB,CAAC,aAAa,CAAC,CAAC;;4BAGhD,QAAQ,CAAC,gBAAgB,CAAC,QAAQ,EAAE,OAAO,EAAE,KAAK,CAAC,CAAC;4BACpD,IAAI,UAAU,EAAE,EAAE;gCAChB,QAAQ,CAAC,gBAAgB,CAAC,kBAAkB,EAAE,iBAAiB,EAAE,KAAK,CAAC,CAAC;AACzE,6BAAA;;AAGD,4BAAA,OAAO,GAAG,YAAA;AACR,gCAAA,aAAa,CAAC,qBAAqB,CAAC,aAAa,CAAC,CAAC;gCACnD,QAAQ,CAAC,mBAAmB,CAAC,QAAQ,EAAE,OAAO,EAAE,KAAK,CAAC,CAAC;gCACvD,QAAQ,CAAC,mBAAmB,CAC1B,kBAAkB,EAClB,iBAAiB,EACjB,KAAK,CACN,CAAC;AACF,gCAAA,IAAI,YAAY,EAAE;AAChB,oCAAA,MAAM,CAAC,YAAY,CAAC,YAAY,CAAC,CAAC;AACnC,iCAAA;AACH,6BAAC,CAAC;AACJ,yBAAC,CAAC,CAAA,CAAA;;AA5DF,oBAAA,EAAA,CAAA,IAAA,EA4DE,CAAC;;;AAEH,oBAAA,OAAO,EAAE,CAAC;;;;;;AAEb,CAAA;AAED;;;;AAIG;AACG,SAAU,0BAA0B,CAAC,IAAkB,EAAA;;AAC3D,IAAA,IAAM,GAAG,GAAG,cAAc,EAAE,CAAC;;;;;;AAM7B,IAAA,OAAO,CACL,QAAO,MAAA,GAAG,KAAA,IAAA,IAAH,GAAG,KAAH,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,GAAG,CAAE,cAAc,0CAAE,SAAS,CAAA,KAAK,UAAU,EACpD,IAAI,EAEJ,+BAAA,oDAAA;AACE,QAAA,aAAa,EAAE,oCAAoC;AACpD,KAAA,CACF,CAAC;;AAGF,IAAA,OAAO,CACL,QAAO,MAAA,GAAG,KAAA,IAAA,IAAH,GAAG,KAAH,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,GAAG,CAAE,SAAS,0CAAE,WAAW,CAAA,KAAK,WAAW,EAClD,IAAI,EAEJ,+BAAA,oDAAA;AACE,QAAA,aAAa,EAAE,0BAA0B;AAC1C,KAAA,CACF,CAAC;;IAGF,OAAO,CACL,QAAO,CAAA,EAAA,GAAA,CAAA,EAAA,GAAA,MAAA,GAAG,KAAA,IAAA,IAAH,GAAG,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAH,GAAG,CAAE,OAAO,MAAE,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,OAAO,MAAE,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,UAAU,MAAE,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,OAAO,CAAA,KAAK,UAAU,EAChE,IAAI,EAEJ,+BAAA,oDAAA;AACE,QAAA,aAAa,EAAE,2BAA2B;AAC3C,KAAA,CACF,CAAC;IACF,OAAO,CACL,QAAO,CAAA,EAAA,GAAA,CAAA,EAAA,GAAA,MAAA,GAAG,KAAA,IAAA,IAAH,GAAG,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAH,GAAG,CAAE,OAAO,MAAE,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,OAAO,MAAE,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,UAAU,MAAE,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,WAAW,CAAA,KAAK,UAAU,EACpE,IAAI,EAEJ,+BAAA,oDAAA;AACE,QAAA,aAAa,EAAE,2BAA2B;AAC3C,KAAA,CACF,CAAC;;IAGF,OAAO,CACL,QAAO,CAAA,EAAA,GAAA,MAAA,GAAG,KAAA,IAAA,IAAH,GAAG,KAAH,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,GAAG,CAAE,OAAO,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAE,YAAY,MAAE,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,IAAI,CAAA,KAAK,UAAU,EACtD,IAAI,EAEJ,+BAAA,oDAAA;AACE,QAAA,aAAa,EAAE,6BAA6B;AAC7C,KAAA,CACF,CAAC;AACJ,CAAC;AAED;;;;AAIG;AACH,SAAe,aAAa,CAAC,SAAiB,EAAA;;;;;;AACtC,oBAAA,KAAK,GAAG,mBAAmB,CAAC,SAAS,CAAC,CAAC;oBAMjC,OAAM,CAAA,CAAA,YAAA,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,SAAS,EAAE,KAAK,CAAC,CAAA,CAAA;;AAAlD,oBAAA,GAAG,GAAG,EAA4C,CAAA,IAAA,EAAA,CAAA;oBAClD,GAAG,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,UAAU,CAAC,GAAG,CAAC,CAAC,CAAC;AAC5C,oBAAA,OAAA,CAAA,CAAA,aAAO,GAAG,CAAC,GAAG,CAAC,UAAA,GAAG,EAAI,EAAA,OAAA,GAAG,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,CAAA,EAAA,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,CAAA;;;;AACnE,CAAA;AAED,SAAS,mBAAmB,CAAC,GAAW,EAAA;;;IAGtC,WAAW,CACT,cAAc,CAAC,IAAI,CAAC,GAAG,CAAC,EACxB,wCAAwC,CACzC,CAAC;AACF,IAAA,IAAI,OAAO,WAAW,KAAK,WAAW,EAAE;QACtC,OAAO,IAAI,WAAW,EAAE,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;AACtC,KAAA;IAED,IAAM,IAAI,GAAG,IAAI,WAAW,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;AACzC,IAAA,IAAM,IAAI,GAAG,IAAI,UAAU,CAAC,IAAI,CAAC,CAAC;AAClC,IAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;QACnC,IAAI,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;AAC7B,KAAA;AACD,IAAA,OAAO,IAAI,CAAC;AACd;;AClTA;;;;;;;;;;;;;;;AAeG;AAgBH,IAAM,iBAAiB,GAAG,EAAE,CAAC;AAE7B;AACA,IAAA,uBAAA,kBAAA,UAAA,MAAA,EAAA;IAA6C,SAAgB,CAAA,uBAAA,EAAA,MAAA,CAAA,CAAA;AAA7D,IAAA,SAAA,uBAAA,GAAA;QAAA,IAgCC,KAAA,GAAA,MAAA,KAAA,IAAA,IAAA,MAAA,CAAA,KAAA,CAAA,IAAA,EAAA,SAAA,CAAA,IAAA,IAAA,CAAA;AA/BkB,QAAA,KAAA,CAAA,gBAAgB,GAAG,IAAI,GAAG,EAA0B,CAAC;AAE9D,QAAA,KAAA,CAAA,WAAW,GAAG,IAAI,OAAO,CAAO,UAAA,OAAO,EAAA;AAC7C,YAAA,KAAI,CAAC,kBAAkB,GAAG,OAAO,CAAC;AACpC,SAAC,CAAC,CAAC;;KA2BJ;IAzBC,uBAAkB,CAAA,SAAA,CAAA,kBAAA,GAAlB,UAAmB,EAA0B,EAAA;AAC3C,QAAA,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;KAC/B,CAAA;IAED,uBAAqB,CAAA,SAAA,CAAA,qBAAA,GAArB,UAAsB,EAA0B,EAAA;AAC9C,QAAA,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC;KAClC,CAAA;;;AAID,IAAA,uBAAA,CAAA,SAAA,CAAA,aAAa,GAAb,YAAA;AACE,QAAA,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC;AAChC,QAAA,IAAI,CAAC,2BAA2B,GAAG,KAAK,CAAC;KAC1C,CAAA;;IAGD,uBAAO,CAAA,SAAA,CAAA,OAAA,GAAP,UAAQ,KAAgB,EAAA;QACtB,IAAI,CAAC,kBAAkB,EAAE,CAAC;AAC1B,QAAA,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC,UAAA,EAAE,EAAA,EAAI,OAAA,EAAE,CAAC,KAAK,CAAC,CAAT,EAAS,CAAC,CAAC;AAC/C,QAAA,OAAO,MAAM,CAAA,SAAA,CAAA,OAAO,CAAC,IAAA,CAAA,IAAA,EAAA,KAAK,CAAC,CAAC;KAC7B,CAAA;AAEK,IAAA,uBAAA,CAAA,SAAA,CAAA,WAAW,GAAjB,YAAA;;;;4BACE,OAAM,CAAA,CAAA,YAAA,IAAI,CAAC,WAAW,CAAA,CAAA;;AAAtB,wBAAA,EAAA,CAAA,IAAA,EAAsB,CAAC;;;;;AACxB,KAAA,CAAA;IACH,OAAC,uBAAA,CAAA;AAAD,CAhCA,CAA6C,gBAAgB,CAgC5D,CAAA,CAAA;AAED;;AAEG;SACa,iBAAiB,CAC/B,IAAkB,EAClB,IAAmB,EACnB,OAA6B,EAAA;AAA7B,IAAA,IAAA,OAAA,KAAA,KAAA,CAAA,EAAA,EAAA,OAA6B,GAAA,IAAA,CAAA,EAAA;IAE7B,OAAO;AACL,QAAA,IAAI,EAAA,IAAA;AACJ,QAAA,OAAO,EAAA,OAAA;AACP,QAAA,WAAW,EAAE,IAAI;QACjB,SAAS,EAAE,iBAAiB,EAAE;AAC9B,QAAA,QAAQ,EAAE,IAAI;QACd,QAAQ,EAAE,IAAI,CAAC,QAAQ;AACvB,QAAA,KAAK,EAAE,YAAY,CAAC,IAAI,EAA8B,eAAA,mCAAA;KACvD,CAAC;AACJ,CAAC;AAEe,SAAA,iBAAiB,CAC/B,IAAkB,EAClB,KAAgB,EAAA;AAEhB,IAAA,OAAO,OAAO,EAAE,CAAC,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,EAAE,KAAgC,CAAC,CAAC;AAChF,CAAC;AAEK,SAAgB,kBAAkB,CACtC,IAAkB,EAAA;;;;;wBAEH,OAAM,CAAA,CAAA,YAAA,OAAO,EAAE,CAAC,IAAI,CACjC,cAAc,CAAC,IAAI,CAAC,CACrB,CAAA,CAAA;;oBAFK,KAAK,IAAI,EAAA,CAAA,IAAA,EAEd,CAAqB,CAAA;AAClB,oBAAA,IAAA,CAAA,KAAK,EAAL,OAAK,CAAA,CAAA,YAAA,CAAA,CAAA,CAAA;oBACP,OAAM,CAAA,CAAA,YAAA,OAAO,EAAE,CAAC,OAAO,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC,CAAA,CAAA;;AAA7C,oBAAA,EAAA,CAAA,IAAA,EAA6C,CAAC;;AAEhD,gBAAA,KAAA,CAAA,EAAA,OAAA,CAAA,CAAA,aAAO,KAAK,CAAC,CAAA;;;;AACd,CAAA;AAEe,SAAA,uBAAuB,CACrC,YAAuB,EACvB,GAAW,EAAA;;;AAGX,IAAA,IAAM,WAAW,GAAG,wBAAwB,CAAC,GAAG,CAAC,CAAC;;;;;;AAMlD,IAAA,IAAI,WAAW,CAAC,QAAQ,CAAC,mBAAmB,CAAC,EAAE;;;;AAI7C,QAAA,IAAM,MAAM,GAAG,mBAAmB,CAAC,WAAW,CAAC,CAAC;;AAEhD,QAAA,IAAM,WAAW,GAAG,MAAM,CAAC,eAAe,CAAC;cACvC,eAAe,CAAC,kBAAkB,CAAC,MAAM,CAAC,eAAe,CAAC,CAAC,CAAC;cAC5D,IAAI,CAAC;QACT,IAAM,IAAI,GAAG,CAAA,EAAA,GAAA,CAAA,EAAA,GAAA,WAAW,KAAX,IAAA,IAAA,WAAW,uBAAX,WAAW,CAAG,MAAM,CAAC,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAE,KAAK,CAAC,OAAO,CAAC,MAAG,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,CAAC,CAAC,CAAC;AACxD,QAAA,IAAM,KAAK,GAAG,IAAI,GAAG,YAAY,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC;AAC/C,QAAA,IAAI,KAAK,EAAE;YACT,OAAO;gBACL,IAAI,EAAE,YAAY,CAAC,IAAI;gBACvB,OAAO,EAAE,YAAY,CAAC,OAAO;gBAC7B,QAAQ,EAAE,YAAY,CAAC,QAAQ;AAC/B,gBAAA,KAAK,EAAA,KAAA;AACL,gBAAA,WAAW,EAAE,IAAI;AACjB,gBAAA,SAAS,EAAE,IAAI;AACf,gBAAA,QAAQ,EAAE,IAAI;aACf,CAAC;AACH,SAAA;AAAM,aAAA;YACL,OAAO;gBACL,IAAI,EAAE,YAAY,CAAC,IAAI;gBACvB,OAAO,EAAE,YAAY,CAAC,OAAO;gBAC7B,QAAQ,EAAE,YAAY,CAAC,QAAQ;gBAC/B,SAAS,EAAE,YAAY,CAAC,SAAS;AACjC,gBAAA,WAAW,EAAE,WAAW;AACxB,gBAAA,QAAQ,EAAE,IAAI;aACf,CAAC;AACH,SAAA;AACF,KAAA;AAED,IAAA,OAAO,IAAI,CAAC;AACd,CAAC;AAED,SAAS,iBAAiB,GAAA;IACxB,IAAM,KAAK,GAAG,EAAE,CAAC;IACjB,IAAM,YAAY,GAChB,gEAAgE,CAAC;IACnE,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,iBAAiB,EAAE,CAAC,EAAE,EAAE;AAC1C,QAAA,IAAM,GAAG,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,YAAY,CAAC,MAAM,CAAC,CAAC;QAC5D,KAAK,CAAC,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC;AACtC,KAAA;AACD,IAAA,OAAO,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;AACxB,CAAC;AAED,SAAS,OAAO,GAAA;AACd,IAAA,OAAO,YAAY,CAAC,uBAAuB,CAAC,CAAC;AAC/C,CAAC;AAED,SAAS,cAAc,CAAC,IAAkB,EAAA;AACxC,IAAA,OAAO,mBAAmB,CAAA,WAAA,2BAAqB,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC;AAChF,CAAC;AAED,SAAS,eAAe,CAAC,IAAY,EAAA;IACnC,IAAI;AACF,QAAA,OAAO,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;AACzB,KAAA;AAAC,IAAA,OAAO,CAAC,EAAE;AACV,QAAA,OAAO,IAAI,CAAC;AACb,KAAA;AACH,CAAC;AAED;AACM,SAAU,wBAAwB,CAAC,GAAW,EAAA;AAClD,IAAA,IAAM,MAAM,GAAG,mBAAmB,CAAC,GAAG,CAAC,CAAC;IACxC,IAAM,IAAI,GAAG,MAAM,CAAC,MAAM,CAAC,GAAG,kBAAkB,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,GAAG,SAAS,CAAC;;IAE7E,IAAM,cAAc,GAAG,mBAAmB,CAAC,IAAI,CAAC,CAAC,MAAM,CAAC,CAAC;;AAEzD,IAAA,IAAM,WAAW,GAAG,MAAM,CAAC,cAAc,CAAC;AACxC,UAAE,kBAAkB,CAAC,MAAM,CAAC,cAAc,CAAC,CAAC;UAC1C,SAAS,CAAC;IACd,IAAM,iBAAiB,GAAG,mBAAmB,CAAC,WAAW,CAAC,CAAC,MAAM,CAAC,CAAC;IACnE,OAAO,iBAAiB,IAAI,WAAW,IAAI,cAAc,IAAI,IAAI,IAAI,GAAG,CAAC;AAC3E,CAAC;AAED;;;AAGG;AACH,SAAS,mBAAmB,CAAC,GAAuB,EAAA;AAClD,IAAA,IAAI,EAAC,GAAG,KAAA,IAAA,IAAH,GAAG,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAH,GAAG,CAAE,QAAQ,CAAC,GAAG,CAAC,CAAA,EAAE;AACvB,QAAA,OAAO,EAAE,CAAC;AACX,KAAA;AAEK,IAAA,IAAA,EAAe,GAAA,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,CAAA,CAA3B,EAAA,CAAA,CAAA,CAAA,CAAK,KAAA,IAAI,eAAmB;IACpC,OAAO,iBAAiB,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,CAA2B,CAAC;AACrE;;AC7MA;;;;;;;;;;;;;;;AAeG;AAmCH;;;AAGG;AACH,IAAM,wBAAwB,GAAG,GAAG,CAAC;AAErC,IAAA,4BAAA,kBAAA,YAAA;AAAA,IAAA,SAAA,4BAAA,GAAA;QACW,IAAoB,CAAA,oBAAA,GAAG,yBAAyB,CAAC;AACjD,QAAA,IAAA,CAAA,sBAAsB,GAAG,IAAI,CAAC;AACtB,QAAA,IAAA,CAAA,aAAa,GAAG,IAAI,GAAG,EAAmC,CAAC;QAC3D,IAAwB,CAAA,wBAAA,GAAkC,EAAE,CAAC;QAE9E,IAAmB,CAAA,mBAAA,GAAG,kBAAkB,CAAC;QACzC,IAAuB,CAAA,uBAAA,GAAG,uBAAuB,CAAC;KAwHnD;IAtHO,4BAAW,CAAA,SAAA,CAAA,WAAA,GAAjB,UAAkB,IAAkB,EAAA;;;;AAC5B,gBAAA,GAAG,GAAG,IAAI,CAAC,IAAI,EAAE,CAAC;gBACpB,OAAO,GAAG,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;gBAC1C,IAAI,CAAC,OAAO,EAAE;AACZ,oBAAA,OAAO,GAAG,IAAI,uBAAuB,CAAC,IAAI,CAAC,CAAC;oBAC5C,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,GAAG,EAAE,OAAO,CAAC,CAAC;AACrC,oBAAA,IAAI,CAAC,uBAAuB,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;AAC7C,iBAAA;AACD,gBAAA,OAAA,CAAA,CAAA,aAAO,OAAO,CAAC,CAAA;;;AAChB,KAAA,CAAA;IAED,4BAAU,CAAA,SAAA,CAAA,UAAA,GAAV,UAAW,IAAkB,EAAA;QAC3B,KAAK,CAAC,IAAI,EAAA,6CAAA,6CAAwC,CAAC;KACpD,CAAA;IAEK,4BAAa,CAAA,SAAA,CAAA,aAAA,GAAnB,UACE,IAAkB,EAClB,QAAsB,EACtB,QAAuB,EACvB,OAAgB,EAAA;;;;;;wBAEhB,0BAA0B,CAAC,IAAI,CAAC,CAAC;AACjB,wBAAA,OAAA,CAAA,CAAA,YAAM,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,CAAA,CAAA;;AAAtC,wBAAA,OAAO,GAAG,EAA4B,CAAA,IAAA,EAAA,CAAA;AAC5C,wBAAA,OAAA,CAAA,CAAA,YAAM,OAAO,CAAC,WAAW,EAAE,CAAA,CAAA;;AAA3B,wBAAA,EAAA,CAAA,IAAA,EAA2B,CAAC;;;;wBAK5B,OAAO,CAAC,aAAa,EAAE,CAAC;AACxB,wBAAA,sBAAsB,EAAE,CAAC;AAEzB,wBAAA,OAAA,CAAA,CAAA,YAAM,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAA,CAAA;;AAAlC,wBAAA,EAAA,CAAA,IAAA,EAAkC,CAAC;wBAE7B,KAAK,GAAG,iBAAiB,CAAC,IAAI,EAAE,QAAQ,EAAE,OAAO,CAAC,CAAC;AACzD,wBAAA,OAAA,CAAA,CAAA,YAAM,iBAAiB,CAAC,IAAI,EAAE,KAAK,CAAC,CAAA,CAAA;;AAApC,wBAAA,EAAA,CAAA,IAAA,EAAoC,CAAC;wBACzB,OAAM,CAAA,CAAA,YAAA,mBAAmB,CAAC,IAAI,EAAE,KAAK,EAAE,QAAQ,CAAC,CAAA,CAAA;;AAAtD,wBAAA,GAAG,GAAG,EAAgD,CAAA,IAAA,EAAA,CAAA;AAC7C,wBAAA,OAAA,CAAA,CAAA,YAAM,gBAAgB,CAAC,GAAG,CAAC,CAAA,CAAA;;AAApC,wBAAA,MAAM,GAAG,EAA2B,CAAA,IAAA,EAAA,CAAA;wBAC1C,OAAO,CAAA,CAAA,aAAA,iBAAiB,CAAC,IAAI,EAAE,OAAO,EAAE,MAAM,CAAC,CAAC,CAAA;;;;AACjD,KAAA,CAAA;AAED,IAAA,4BAAA,CAAA,SAAA,CAAA,4BAA4B,GAA5B,UACE,KAAmB,EACnB,GAAkC,EAAA;AAElC,QAAA,MAAM,IAAI,KAAK,CAAC,yBAAyB,CAAC,CAAC;KAC5C,CAAA;IAED,4BAAiB,CAAA,SAAA,CAAA,iBAAA,GAAjB,UAAkB,IAAkB,EAAA;AAClC,QAAA,IAAM,GAAG,GAAG,IAAI,CAAC,IAAI,EAAE,CAAC;AACxB,QAAA,IAAI,CAAC,IAAI,CAAC,wBAAwB,CAAC,GAAG,CAAC,EAAE;YACvC,IAAI,CAAC,wBAAwB,CAAC,GAAG,CAAC,GAAG,eAAe,CAAC,IAAI,CAAC,CAAC;AAC5D,SAAA;AAED,QAAA,OAAO,IAAI,CAAC,wBAAwB,CAAC,GAAG,CAAC,CAAC;KAC3C,CAAA;AAEO,IAAA,4BAAA,CAAA,SAAA,CAAA,uBAAuB,GAA/B,UACE,IAAkB,EAClB,OAAyB,EAAA;QAF3B,IA6DC,KAAA,GAAA,IAAA,CAAA;;AAxDO,QAAA,IAAA,EAA+C,GAAA,cAAc,EAAE,EAA7D,cAAc,GAAA,EAAA,CAAA,cAAA,EAAE,aAAa,GAAA,EAAA,CAAA,aAAA,EAAE,SAAS,GAAA,EAAA,CAAA,SAAqB,CAAC;QAEtE,IAAM,cAAc,GAAG,UAAU,CAAC,YAAA,EAAA,OAAA,SAAA,CAAA,KAAA,EAAA,KAAA,CAAA,EAAA,KAAA,CAAA,EAAA,YAAA;;;;;;AAGhC,oBAAA,OAAA,CAAA,CAAA,YAAM,kBAAkB,CAAC,IAAI,CAAC,CAAA,CAAA;;;;AAA9B,wBAAA,EAAA,CAAA,IAAA,EAA8B,CAAC;AAC/B,wBAAA,OAAO,CAAC,OAAO,CAAC,eAAe,EAAE,CAAC,CAAC;;;;aACpC,EAAE,wBAAwB,CAAC,CAAC;QAE7B,IAAM,gBAAgB,GAAG,UACvB,SAAwC,EAAA,EAAA,OAAA,SAAA,CAAA,KAAA,EAAA,KAAA,CAAA,EAAA,KAAA,CAAA,EAAA,YAAA;;;;;;wBAGxC,YAAY,CAAC,cAAc,CAAC,CAAC;AAER,wBAAA,OAAA,CAAA,CAAA,YAAM,kBAAkB,CAAC,IAAI,CAAC,CAAA,CAAA;;AAA7C,wBAAA,YAAY,GAAG,EAA8B,CAAA,IAAA,EAAA,CAAA;wBAC/C,UAAU,GAAqB,IAAI,CAAC;wBACxC,IAAI,YAAY,KAAI,SAAS,KAAT,IAAA,IAAA,SAAS,KAAT,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,SAAS,CAAG,KAAK,CAAC,CAAA,EAAE;4BACtC,UAAU,GAAG,uBAAuB,CAAC,YAAY,EAAE,SAAS,CAAC,KAAK,CAAC,CAAC,CAAC;AACtE,yBAAA;;wBAGD,OAAO,CAAC,OAAO,CAAC,UAAU,IAAI,eAAe,EAAE,CAAC,CAAC;;;;aAClD,CAAC;;QAGF,IACE,OAAO,cAAc,KAAK,WAAW;AACrC,YAAA,OAAO,cAAc,CAAC,SAAS,KAAK,UAAU,EAC9C;AACA,YAAA,cAAc,CAAC,SAAS,CAAC,IAAI,EAAE,gBAAgB,CAAC,CAAC;AAClD,SAAA;;;;;;QAOD,IAAM,qBAAqB,GAAG,aAAa,CAAC;QAC5C,IAAM,aAAa,GAAG,EAAA,CAAA,MAAA,CAAG,SAAS,CAAC,WAAW,CAAC,WAAW,EAAE,EAAA,KAAA,CAAK,CAAC;AAClE,QAAA,cAAc,EAAE,CAAC,aAAa,GAAG,UAAM,GAAG,EAAA,EAAA,OAAA,SAAA,CAAA,KAAA,EAAA,KAAA,CAAA,EAAA,KAAA,CAAA,EAAA,YAAA;;gBACxC,IAAI,GAAG,CAAC,WAAW,EAAE,CAAC,UAAU,CAAC,aAAa,CAAC,EAAE;;;AAG/C,oBAAA,gBAAgB,CAAC,EAAE,GAAG,EAAA,GAAA,EAAE,CAAC,CAAC;AAC3B,iBAAA;;AAED,gBAAA,IAAI,OAAO,qBAAqB,KAAK,UAAU,EAAE;oBAC/C,IAAI;wBACF,qBAAqB,CAAC,GAAG,CAAC,CAAC;AAC5B,qBAAA;AAAC,oBAAA,OAAO,CAAC,EAAE;;AAEV,wBAAA,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;AAClB,qBAAA;AACF,iBAAA;;;aACF,CAAC;KACH,CAAA;IACH,OAAC,4BAAA,CAAA;AAAD,CAAC,EAAA,CAAA,CAAA;AAED;;;;;AAKG;AACI,IAAM,4BAA4B,GACvC,6BAA6B;AAE/B,SAAS,eAAe,GAAA;IACtB,OAAO;AACL,QAAA,IAAI,EAAuB,SAAA;AAC3B,QAAA,OAAO,EAAE,IAAI;AACb,QAAA,SAAS,EAAE,IAAI;AACf,QAAA,WAAW,EAAE,IAAI;AACjB,QAAA,QAAQ,EAAE,IAAI;AACd,QAAA,QAAQ,EAAE,IAAI;QACd,KAAK,EAAE,YAAY,CAA6B,eAAA,mCAAA;KACjD,CAAC;AACJ;;AC5MA;;;;;;;;;;;;;;;AAeG;AAqCH;AACA;AACA;AACgB,SAAA,sBAAsB,CAAC,IAAU,EAAE,SAAiB,EAAA;IAClE,SAAS,CAAC,IAAI,CAAC,CAAC,aAAa,CAAC,SAAS,CAAC,CAAC;AAC3C;;;;"}