import{U as f,V as u,A as d,z as l,x as g,B as p,y as o,S as m,s as h,W as y}from"./index-2cGdSAEs.js";let w=(s=21)=>crypto.getRandomValues(new Uint8Array(s)).reduce((e,t)=>(t&=63,t<36?e+=t.toString(36):t<62?e+=(t-26).toString(36).toUpperCase():t>62?e+="-":e+="_",e),"");const v=async(s,e="",t=!1,n={},a=[])=>{try{const r=w(10);return console.log(`Creating meeting with requireApproval: ${t}`),console.log("Initial features:",n),console.log("Initial extensions:",a),await p(g(o,"meetings",r),{createdBy:s,createdAt:m(),meetingName:e||`Meeting ${r}`,active:!0,requiresApproval:t,features:n,extensions:a.map(i=>({id:i.id,name:i.name,config:i.config||{}}))}),await p(g(o,"meetings",r,"participants",s),{userId:s,userName:localStorage.getItem("userName")||"Host",joined:m(),isAdmin:!0}),r}catch(r){throw console.error("Error creating meeting:",r),r}},S=async s=>{try{const e=await h(g(o,"meetings",s));return e.exists()&&e.data().active}catch(e){return console.error("Error checking meeting:",e),!1}},D=async(s,e,t,n)=>{try{await y(d(o,"meetings",s,"messages"),{userId:e,userName:t,message:n,timestamp:m()})}catch(a){throw console.error("Error sending message:",a),a}},M=(s,e)=>{const t=f(d(o,"meetings",s,"messages"),u("timestamp","asc"));return l(t,n=>{const a=n.docs.map(r=>({id:r.id,...r.data()}));e(a)})},k=(s,e,t=null)=>{const n=d(o,"meetings",s,"participants");return l(n,a=>{const r=a.docs.map(i=>({id:i.id,...i.data()}));t&&a.docChanges().forEach(i=>{if(i.type==="added"){const c=i.doc.data();c.joinMethod==="shared_link"&&c.sharedBy&&t({participantId:i.doc.id,participantData:c,timestamp:c.joinedAt||new Date})}}),e(r)})},B=(s,e)=>{const t=g(o,"meetings",s);return l(t,n=>{const a=n.data(),r=(a==null?void 0:a.linkSharingActivities)||[];e(r)})};export{S as a,k as b,v as c,B as d,M as l,w as n,D as s};
