{"version": 3, "file": "firebase-installations.js", "sources": ["../util/src/errors.ts", "../component/src/component.ts", "../../node_modules/idb/build/wrap-idb-value.js", "../../node_modules/idb/build/index.js", "../installations/src/util/errors.ts", "../installations/src/util/constants.ts", "../installations/src/functions/common.ts", "../installations/src/util/sleep.ts", "../installations/src/helpers/generate-fid.ts", "../installations/src/helpers/buffer-to-base64-url-safe.ts", "../installations/src/util/get-key.ts", "../installations/src/helpers/fid-changed.ts", "../installations/src/helpers/idb-manager.ts", "../installations/src/helpers/get-installation-entry.ts", "../installations/src/functions/create-installation-request.ts", "../installations/src/functions/generate-auth-token-request.ts", "../installations/src/helpers/refresh-auth-token.ts", "../installations/src/api/get-id.ts", "../installations/src/api/get-token.ts", "../installations/src/functions/delete-installation-request.ts", "../installations/src/api/delete-installations.ts", "../installations/src/api/on-id-change.ts", "../installations/src/api/get-installations.ts", "../installations/src/helpers/extract-app-config.ts", "../installations/src/functions/config.ts", "../installations/src/index.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n * @fileoverview Standardized Firebase Error.\n *\n * Usage:\n *\n *   // TypeScript string literals for type-safe codes\n *   type Err =\n *     'unknown' |\n *     'object-not-found'\n *     ;\n *\n *   // Closure enum for type-safe error codes\n *   // at-enum {string}\n *   var Err = {\n *     UNKNOWN: 'unknown',\n *     OBJECT_NOT_FOUND: 'object-not-found',\n *   }\n *\n *   let errors: Map<Err, string> = {\n *     'generic-error': \"Unknown error\",\n *     'file-not-found': \"Could not find file: {$file}\",\n *   };\n *\n *   // Type-safe function - must pass a valid error code as param.\n *   let error = new ErrorFactory<Err>('service', 'Service', errors);\n *\n *   ...\n *   throw error.create(Err.GENERIC);\n *   ...\n *   throw error.create(Err.FILE_NOT_FOUND, {'file': fileName});\n *   ...\n *   // Service: Could not file file: foo.txt (service/file-not-found).\n *\n *   catch (e) {\n *     assert(e.message === \"Could not find file: foo.txt.\");\n *     if ((e as FirebaseError)?.code === 'service/file-not-found') {\n *       console.log(\"Could not read file: \" + e['file']);\n *     }\n *   }\n */\n\nexport type ErrorMap<ErrorCode extends string> = {\n  readonly [K in ErrorCode]: string;\n};\n\nconst ERROR_NAME = 'FirebaseError';\n\nexport interface StringLike {\n  toString(): string;\n}\n\nexport interface ErrorData {\n  [key: string]: unknown;\n}\n\n// Based on code from:\n// https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Error#Custom_Error_Types\nexport class FirebaseError extends Error {\n  /** The custom name for all FirebaseErrors. */\n  readonly name: string = ERROR_NAME;\n\n  constructor(\n    /** The error code for this error. */\n    readonly code: string,\n    message: string,\n    /** Custom data for this error. */\n    public customData?: Record<string, unknown>\n  ) {\n    super(message);\n\n    // Fix For ES5\n    // https://github.com/Microsoft/TypeScript-wiki/blob/master/Breaking-Changes.md#extending-built-ins-like-error-array-and-map-may-no-longer-work\n    Object.setPrototypeOf(this, FirebaseError.prototype);\n\n    // Maintains proper stack trace for where our error was thrown.\n    // Only available on V8.\n    if (Error.captureStackTrace) {\n      Error.captureStackTrace(this, ErrorFactory.prototype.create);\n    }\n  }\n}\n\nexport class ErrorFactory<\n  ErrorCode extends string,\n  ErrorParams extends { readonly [K in ErrorCode]?: ErrorData } = {}\n> {\n  constructor(\n    private readonly service: string,\n    private readonly serviceName: string,\n    private readonly errors: ErrorMap<ErrorCode>\n  ) {}\n\n  create<K extends ErrorCode>(\n    code: K,\n    ...data: K extends keyof ErrorParams ? [ErrorParams[K]] : []\n  ): FirebaseError {\n    const customData = (data[0] as ErrorData) || {};\n    const fullCode = `${this.service}/${code}`;\n    const template = this.errors[code];\n\n    const message = template ? replaceTemplate(template, customData) : 'Error';\n    // Service Name: Error message (service/code).\n    const fullMessage = `${this.serviceName}: ${message} (${fullCode}).`;\n\n    const error = new FirebaseError(fullCode, fullMessage, customData);\n\n    return error;\n  }\n}\n\nfunction replaceTemplate(template: string, data: ErrorData): string {\n  return template.replace(PATTERN, (_, key) => {\n    const value = data[key];\n    return value != null ? String(value) : `<${key}?>`;\n  });\n}\n\nconst PATTERN = /\\{\\$([^}]+)}/g;\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nimport {\n  InstantiationMode,\n  InstanceFactory,\n  ComponentType,\n  Dictionary,\n  Name,\n  onInstanceCreatedCallback\n} from './types';\n\n/**\n * Component for service name T, e.g. `auth`, `auth-internal`\n */\nexport class Component<T extends Name = Name> {\n  multipleInstances = false;\n  /**\n   * Properties to be added to the service namespace\n   */\n  serviceProps: Dictionary = {};\n\n  instantiationMode = InstantiationMode.LAZY;\n\n  onInstanceCreated: onInstanceCreatedCallback<T> | null = null;\n\n  /**\n   *\n   * @param name The public service name, e.g. app, auth, firestore, database\n   * @param instanceFactory Service factory responsible for creating the public interface\n   * @param type whether the service provided by the component is public or private\n   */\n  constructor(\n    readonly name: T,\n    readonly instanceFactory: InstanceFactory<T>,\n    readonly type: ComponentType\n  ) {}\n\n  setInstantiationMode(mode: InstantiationMode): this {\n    this.instantiationMode = mode;\n    return this;\n  }\n\n  setMultipleInstances(multipleInstances: boolean): this {\n    this.multipleInstances = multipleInstances;\n    return this;\n  }\n\n  setServiceProps(props: Dictionary): this {\n    this.serviceProps = props;\n    return this;\n  }\n\n  setInstanceCreatedCallback(callback: onInstanceCreatedCallback<T>): this {\n    this.onInstanceCreated = callback;\n    return this;\n  }\n}\n", "const instanceOfAny = (object, constructors) => constructors.some((c) => object instanceof c);\n\nlet idbProxyableTypes;\nlet cursorAdvanceMethods;\n// This is a function to prevent it throwing up in node environments.\nfunction getIdbProxyableTypes() {\n    return (idbProxyableTypes ||\n        (idbProxyableTypes = [\n            IDBDatabase,\n            IDBObjectStore,\n            IDBIndex,\n            IDBCursor,\n            IDBTransaction,\n        ]));\n}\n// This is a function to prevent it throwing up in node environments.\nfunction getCursorAdvanceMethods() {\n    return (cursorAdvanceMethods ||\n        (cursorAdvanceMethods = [\n            IDBCursor.prototype.advance,\n            IDBCursor.prototype.continue,\n            IDBCursor.prototype.continuePrimaryKey,\n        ]));\n}\nconst cursorRequestMap = new WeakMap();\nconst transactionDoneMap = new WeakMap();\nconst transactionStoreNamesMap = new WeakMap();\nconst transformCache = new WeakMap();\nconst reverseTransformCache = new WeakMap();\nfunction promisifyRequest(request) {\n    const promise = new Promise((resolve, reject) => {\n        const unlisten = () => {\n            request.removeEventListener('success', success);\n            request.removeEventListener('error', error);\n        };\n        const success = () => {\n            resolve(wrap(request.result));\n            unlisten();\n        };\n        const error = () => {\n            reject(request.error);\n            unlisten();\n        };\n        request.addEventListener('success', success);\n        request.addEventListener('error', error);\n    });\n    promise\n        .then((value) => {\n        // Since cursoring reuses the IDBRequest (*sigh*), we cache it for later retrieval\n        // (see wrapFunction).\n        if (value instanceof IDBCursor) {\n            cursorRequestMap.set(value, request);\n        }\n        // Catching to avoid \"Uncaught Promise exceptions\"\n    })\n        .catch(() => { });\n    // This mapping exists in reverseTransformCache but doesn't doesn't exist in transformCache. This\n    // is because we create many promises from a single IDBRequest.\n    reverseTransformCache.set(promise, request);\n    return promise;\n}\nfunction cacheDonePromiseForTransaction(tx) {\n    // Early bail if we've already created a done promise for this transaction.\n    if (transactionDoneMap.has(tx))\n        return;\n    const done = new Promise((resolve, reject) => {\n        const unlisten = () => {\n            tx.removeEventListener('complete', complete);\n            tx.removeEventListener('error', error);\n            tx.removeEventListener('abort', error);\n        };\n        const complete = () => {\n            resolve();\n            unlisten();\n        };\n        const error = () => {\n            reject(tx.error || new DOMException('AbortError', 'AbortError'));\n            unlisten();\n        };\n        tx.addEventListener('complete', complete);\n        tx.addEventListener('error', error);\n        tx.addEventListener('abort', error);\n    });\n    // Cache it for later retrieval.\n    transactionDoneMap.set(tx, done);\n}\nlet idbProxyTraps = {\n    get(target, prop, receiver) {\n        if (target instanceof IDBTransaction) {\n            // Special handling for transaction.done.\n            if (prop === 'done')\n                return transactionDoneMap.get(target);\n            // Polyfill for objectStoreNames because of Edge.\n            if (prop === 'objectStoreNames') {\n                return target.objectStoreNames || transactionStoreNamesMap.get(target);\n            }\n            // Make tx.store return the only store in the transaction, or undefined if there are many.\n            if (prop === 'store') {\n                return receiver.objectStoreNames[1]\n                    ? undefined\n                    : receiver.objectStore(receiver.objectStoreNames[0]);\n            }\n        }\n        // Else transform whatever we get back.\n        return wrap(target[prop]);\n    },\n    set(target, prop, value) {\n        target[prop] = value;\n        return true;\n    },\n    has(target, prop) {\n        if (target instanceof IDBTransaction &&\n            (prop === 'done' || prop === 'store')) {\n            return true;\n        }\n        return prop in target;\n    },\n};\nfunction replaceTraps(callback) {\n    idbProxyTraps = callback(idbProxyTraps);\n}\nfunction wrapFunction(func) {\n    // Due to expected object equality (which is enforced by the caching in `wrap`), we\n    // only create one new func per func.\n    // Edge doesn't support objectStoreNames (booo), so we polyfill it here.\n    if (func === IDBDatabase.prototype.transaction &&\n        !('objectStoreNames' in IDBTransaction.prototype)) {\n        return function (storeNames, ...args) {\n            const tx = func.call(unwrap(this), storeNames, ...args);\n            transactionStoreNamesMap.set(tx, storeNames.sort ? storeNames.sort() : [storeNames]);\n            return wrap(tx);\n        };\n    }\n    // Cursor methods are special, as the behaviour is a little more different to standard IDB. In\n    // IDB, you advance the cursor and wait for a new 'success' on the IDBRequest that gave you the\n    // cursor. It's kinda like a promise that can resolve with many values. That doesn't make sense\n    // with real promises, so each advance methods returns a new promise for the cursor object, or\n    // undefined if the end of the cursor has been reached.\n    if (getCursorAdvanceMethods().includes(func)) {\n        return function (...args) {\n            // Calling the original function with the proxy as 'this' causes ILLEGAL INVOCATION, so we use\n            // the original object.\n            func.apply(unwrap(this), args);\n            return wrap(cursorRequestMap.get(this));\n        };\n    }\n    return function (...args) {\n        // Calling the original function with the proxy as 'this' causes ILLEGAL INVOCATION, so we use\n        // the original object.\n        return wrap(func.apply(unwrap(this), args));\n    };\n}\nfunction transformCachableValue(value) {\n    if (typeof value === 'function')\n        return wrapFunction(value);\n    // This doesn't return, it just creates a 'done' promise for the transaction,\n    // which is later returned for transaction.done (see idbObjectHandler).\n    if (value instanceof IDBTransaction)\n        cacheDonePromiseForTransaction(value);\n    if (instanceOfAny(value, getIdbProxyableTypes()))\n        return new Proxy(value, idbProxyTraps);\n    // Return the same value back if we're not going to transform it.\n    return value;\n}\nfunction wrap(value) {\n    // We sometimes generate multiple promises from a single IDBRequest (eg when cursoring), because\n    // IDB is weird and a single IDBRequest can yield many responses, so these can't be cached.\n    if (value instanceof IDBRequest)\n        return promisifyRequest(value);\n    // If we've already transformed this value before, reuse the transformed value.\n    // This is faster, but it also provides object equality.\n    if (transformCache.has(value))\n        return transformCache.get(value);\n    const newValue = transformCachableValue(value);\n    // Not all types are transformed.\n    // These may be primitive types, so they can't be WeakMap keys.\n    if (newValue !== value) {\n        transformCache.set(value, newValue);\n        reverseTransformCache.set(newValue, value);\n    }\n    return newValue;\n}\nconst unwrap = (value) => reverseTransformCache.get(value);\n\nexport { reverseTransformCache as a, instanceOfAny as i, replaceTraps as r, unwrap as u, wrap as w };\n", "import { w as wrap, r as replaceTraps } from './wrap-idb-value.js';\nexport { u as unwrap, w as wrap } from './wrap-idb-value.js';\n\n/**\n * Open a database.\n *\n * @param name Name of the database.\n * @param version Schema version.\n * @param callbacks Additional callbacks.\n */\nfunction openDB(name, version, { blocked, upgrade, blocking, terminated } = {}) {\n    const request = indexedDB.open(name, version);\n    const openPromise = wrap(request);\n    if (upgrade) {\n        request.addEventListener('upgradeneeded', (event) => {\n            upgrade(wrap(request.result), event.oldVersion, event.newVersion, wrap(request.transaction), event);\n        });\n    }\n    if (blocked) {\n        request.addEventListener('blocked', (event) => blocked(\n        // Casting due to https://github.com/microsoft/TypeScript-DOM-lib-generator/pull/1405\n        event.oldVersion, event.newVersion, event));\n    }\n    openPromise\n        .then((db) => {\n        if (terminated)\n            db.addEventListener('close', () => terminated());\n        if (blocking) {\n            db.addEventListener('versionchange', (event) => blocking(event.oldVersion, event.newVersion, event));\n        }\n    })\n        .catch(() => { });\n    return openPromise;\n}\n/**\n * Delete a database.\n *\n * @param name Name of the database.\n */\nfunction deleteDB(name, { blocked } = {}) {\n    const request = indexedDB.deleteDatabase(name);\n    if (blocked) {\n        request.addEventListener('blocked', (event) => blocked(\n        // Casting due to https://github.com/microsoft/TypeScript-DOM-lib-generator/pull/1405\n        event.oldVersion, event));\n    }\n    return wrap(request).then(() => undefined);\n}\n\nconst readMethods = ['get', 'getKey', 'getAll', 'getAllKeys', 'count'];\nconst writeMethods = ['put', 'add', 'delete', 'clear'];\nconst cachedMethods = new Map();\nfunction getMethod(target, prop) {\n    if (!(target instanceof IDBDatabase &&\n        !(prop in target) &&\n        typeof prop === 'string')) {\n        return;\n    }\n    if (cachedMethods.get(prop))\n        return cachedMethods.get(prop);\n    const targetFuncName = prop.replace(/FromIndex$/, '');\n    const useIndex = prop !== targetFuncName;\n    const isWrite = writeMethods.includes(targetFuncName);\n    if (\n    // Bail if the target doesn't exist on the target. Eg, getAll isn't in Edge.\n    !(targetFuncName in (useIndex ? IDBIndex : IDBObjectStore).prototype) ||\n        !(isWrite || readMethods.includes(targetFuncName))) {\n        return;\n    }\n    const method = async function (storeName, ...args) {\n        // isWrite ? 'readwrite' : undefined gzipps better, but fails in Edge :(\n        const tx = this.transaction(storeName, isWrite ? 'readwrite' : 'readonly');\n        let target = tx.store;\n        if (useIndex)\n            target = target.index(args.shift());\n        // Must reject if op rejects.\n        // If it's a write operation, must reject if tx.done rejects.\n        // Must reject with op rejection first.\n        // Must resolve with op value.\n        // Must handle both promises (no unhandled rejections)\n        return (await Promise.all([\n            target[targetFuncName](...args),\n            isWrite && tx.done,\n        ]))[0];\n    };\n    cachedMethods.set(prop, method);\n    return method;\n}\nreplaceTraps((oldTraps) => ({\n    ...oldTraps,\n    get: (target, prop, receiver) => getMethod(target, prop) || oldTraps.get(target, prop, receiver),\n    has: (target, prop) => !!getMethod(target, prop) || oldTraps.has(target, prop),\n}));\n\nexport { deleteDB, openDB };\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { ErrorFactory, FirebaseError } from '@firebase/util';\nimport { SERVICE, SERVICE_NAME } from './constants';\n\nexport const enum ErrorCode {\n  MISSING_APP_CONFIG_VALUES = 'missing-app-config-values',\n  NOT_REGISTERED = 'not-registered',\n  INSTALLATION_NOT_FOUND = 'installation-not-found',\n  REQUEST_FAILED = 'request-failed',\n  APP_OFFLINE = 'app-offline',\n  DELETE_PENDING_REGISTRATION = 'delete-pending-registration'\n}\n\nconst ERROR_DESCRIPTION_MAP: { readonly [key in ErrorCode]: string } = {\n  [ErrorCode.MISSING_APP_CONFIG_VALUES]:\n    'Missing App configuration value: \"{$valueName}\"',\n  [ErrorCode.NOT_REGISTERED]: 'Firebase Installation is not registered.',\n  [ErrorCode.INSTALLATION_NOT_FOUND]: 'Firebase Installation not found.',\n  [ErrorCode.REQUEST_FAILED]:\n    '{$requestName} request failed with error \"{$serverCode} {$serverStatus}: {$serverMessage}\"',\n  [ErrorCode.APP_OFFLINE]: 'Could not process request. Application offline.',\n  [ErrorCode.DELETE_PENDING_REGISTRATION]:\n    \"Can't delete installation while there is a pending registration request.\"\n};\n\ninterface ErrorParams {\n  [ErrorCode.MISSING_APP_CONFIG_VALUES]: {\n    valueName: string;\n  };\n  [ErrorCode.REQUEST_FAILED]: {\n    requestName: string;\n    [index: string]: string | number; // to make TypeScript 3.8 happy\n  } & ServerErrorData;\n}\n\nexport const ERROR_FACTORY = new ErrorFactory<ErrorCode, ErrorParams>(\n  SERVICE,\n  SERVICE_NAME,\n  ERROR_DESCRIPTION_MAP\n);\n\nexport interface ServerErrorData {\n  serverCode: number;\n  serverMessage: string;\n  serverStatus: string;\n}\n\nexport type ServerError = FirebaseError & { customData: ServerErrorData };\n\n/** Returns true if error is a FirebaseError that is based on an error from the server. */\nexport function isServerError(error: unknown): error is ServerError {\n  return (\n    error instanceof FirebaseError &&\n    error.code.includes(ErrorCode.REQUEST_FAILED)\n  );\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { version } from '../../package.json';\n\nexport const PENDING_TIMEOUT_MS = 10000;\n\nexport const PACKAGE_VERSION = `w:${version}`;\nexport const INTERNAL_AUTH_VERSION = 'FIS_v2';\n\nexport const INSTALLATIONS_API_URL =\n  'https://firebaseinstallations.googleapis.com/v1';\n\nexport const TOKEN_EXPIRATION_BUFFER = 60 * 60 * 1000; // One hour\n\nexport const SERVICE = 'installations';\nexport const SERVICE_NAME = 'Installations';\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { FirebaseError } from '@firebase/util';\nimport { GenerateAuthTokenResponse } from '../interfaces/api-response';\nimport {\n  CompletedAuthToken,\n  RegisteredInstallationEntry,\n  RequestStatus\n} from '../interfaces/installation-entry';\nimport {\n  INSTALLATIONS_API_URL,\n  INTERNAL_AUTH_VERSION\n} from '../util/constants';\nimport { ERROR_FACTORY, ErrorCode } from '../util/errors';\nimport { AppConfig } from '../interfaces/installation-impl';\n\nexport function getInstallationsEndpoint({ projectId }: AppConfig): string {\n  return `${INSTALLATIONS_API_URL}/projects/${projectId}/installations`;\n}\n\nexport function extractAuthTokenInfoFromResponse(\n  response: GenerateAuthTokenResponse\n): CompletedAuthToken {\n  return {\n    token: response.token,\n    requestStatus: RequestStatus.COMPLETED,\n    expiresIn: getExpiresInFromResponseExpiresIn(response.expiresIn),\n    creationTime: Date.now()\n  };\n}\n\nexport async function getErrorFromResponse(\n  requestName: string,\n  response: Response\n): Promise<FirebaseError> {\n  const responseJson: ErrorResponse = await response.json();\n  const errorData = responseJson.error;\n  return ERROR_FACTORY.create(ErrorCode.REQUEST_FAILED, {\n    requestName,\n    serverCode: errorData.code,\n    serverMessage: errorData.message,\n    serverStatus: errorData.status\n  });\n}\n\nexport function getHeaders({ apiKey }: AppConfig): Headers {\n  return new Headers({\n    'Content-Type': 'application/json',\n    Accept: 'application/json',\n    'x-goog-api-key': apiKey\n  });\n}\n\nexport function getHeadersWithAuth(\n  appConfig: AppConfig,\n  { refreshToken }: RegisteredInstallationEntry\n): Headers {\n  const headers = getHeaders(appConfig);\n  headers.append('Authorization', getAuthorizationHeader(refreshToken));\n  return headers;\n}\n\nexport interface ErrorResponse {\n  error: {\n    code: number;\n    message: string;\n    status: string;\n  };\n}\n\n/**\n * Calls the passed in fetch wrapper and returns the response.\n * If the returned response has a status of 5xx, re-runs the function once and\n * returns the response.\n */\nexport async function retryIfServerError(\n  fn: () => Promise<Response>\n): Promise<Response> {\n  const result = await fn();\n\n  if (result.status >= 500 && result.status < 600) {\n    // Internal Server Error. Retry request.\n    return fn();\n  }\n\n  return result;\n}\n\nfunction getExpiresInFromResponseExpiresIn(responseExpiresIn: string): number {\n  // This works because the server will never respond with fractions of a second.\n  return Number(responseExpiresIn.replace('s', '000'));\n}\n\nfunction getAuthorizationHeader(refreshToken: string): string {\n  return `${INTERNAL_AUTH_VERSION} ${refreshToken}`;\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n/** Returns a promise that resolves after given time passes. */\nexport function sleep(ms: number): Promise<void> {\n  return new Promise<void>(resolve => {\n    setTimeout(resolve, ms);\n  });\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { bufferToBase64UrlSafe } from './buffer-to-base64-url-safe';\n\nexport const VALID_FID_PATTERN = /^[cdef][\\w-]{21}$/;\nexport const INVALID_FID = '';\n\n/**\n * Generates a new FID using random values from Web Crypto API.\n * Returns an empty string if FID generation fails for any reason.\n */\nexport function generateFid(): string {\n  try {\n    // A valid FID has exactly 22 base64 characters, which is 132 bits, or 16.5\n    // bytes. our implementation generates a 17 byte array instead.\n    const fidByteArray = new Uint8Array(17);\n    const crypto =\n      self.crypto || (self as unknown as { msCrypto: Crypto }).msCrypto;\n    crypto.getRandomValues(fidByteArray);\n\n    // Replace the first 4 random bits with the constant FID header of 0b0111.\n    fidByteArray[0] = 0b01110000 + (fidByteArray[0] % 0b00010000);\n\n    const fid = encode(fidByteArray);\n\n    return VALID_FID_PATTERN.test(fid) ? fid : INVALID_FID;\n  } catch {\n    // FID generation errored\n    return INVALID_FID;\n  }\n}\n\n/** Converts a FID Uint8Array to a base64 string representation. */\nfunction encode(fidByteArray: Uint8Array): string {\n  const b64String = bufferToBase64UrlSafe(fidByteArray);\n\n  // Remove the 23rd character that was added because of the extra 4 bits at the\n  // end of our 17 byte array, and the '=' padding.\n  return b64String.substr(0, 22);\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nexport function bufferToBase64UrlSafe(array: Uint8Array): string {\n  const b64 = btoa(String.fromCharCode(...array));\n  return b64.replace(/\\+/g, '-').replace(/\\//g, '_');\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { AppConfig } from '../interfaces/installation-impl';\n\n/** Returns a string key that can be used to identify the app. */\nexport function getKey(appConfig: AppConfig): string {\n  return `${appConfig.appName}!${appConfig.appId}`;\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { getKey } from '../util/get-key';\nimport { AppConfig } from '../interfaces/installation-impl';\nimport { IdChangeCallbackFn } from '../api';\n\nconst fidChangeCallbacks: Map<string, Set<IdChangeCallbackFn>> = new Map();\n\n/**\n * Calls the onIdChange callbacks with the new FID value, and broadcasts the\n * change to other tabs.\n */\nexport function fidChanged(appConfig: AppConfig, fid: string): void {\n  const key = getKey(appConfig);\n\n  callFidChangeCallbacks(key, fid);\n  broadcastFidChange(key, fid);\n}\n\nexport function addCallback(\n  appConfig: AppConfig,\n  callback: IdChangeCallbackFn\n): void {\n  // Open the broadcast channel if it's not already open,\n  // to be able to listen to change events from other tabs.\n  getBroadcastChannel();\n\n  const key = getKey(appConfig);\n\n  let callbackSet = fidChangeCallbacks.get(key);\n  if (!callbackSet) {\n    callbackSet = new Set();\n    fidChangeCallbacks.set(key, callbackSet);\n  }\n  callbackSet.add(callback);\n}\n\nexport function removeCallback(\n  appConfig: AppConfig,\n  callback: IdChangeCallbackFn\n): void {\n  const key = getKey(appConfig);\n\n  const callbackSet = fidChangeCallbacks.get(key);\n\n  if (!callbackSet) {\n    return;\n  }\n\n  callbackSet.delete(callback);\n  if (callbackSet.size === 0) {\n    fidChangeCallbacks.delete(key);\n  }\n\n  // Close broadcast channel if there are no more callbacks.\n  closeBroadcastChannel();\n}\n\nfunction callFidChangeCallbacks(key: string, fid: string): void {\n  const callbacks = fidChangeCallbacks.get(key);\n  if (!callbacks) {\n    return;\n  }\n\n  for (const callback of callbacks) {\n    callback(fid);\n  }\n}\n\nfunction broadcastFidChange(key: string, fid: string): void {\n  const channel = getBroadcastChannel();\n  if (channel) {\n    channel.postMessage({ key, fid });\n  }\n  closeBroadcastChannel();\n}\n\nlet broadcastChannel: BroadcastChannel | null = null;\n/** Opens and returns a BroadcastChannel if it is supported by the browser. */\nfunction getBroadcastChannel(): BroadcastChannel | null {\n  if (!broadcastChannel && 'BroadcastChannel' in self) {\n    broadcastChannel = new BroadcastChannel('[Firebase] FID Change');\n    broadcastChannel.onmessage = e => {\n      callFidChangeCallbacks(e.data.key, e.data.fid);\n    };\n  }\n  return broadcastChannel;\n}\n\nfunction closeBroadcastChannel(): void {\n  if (fidChangeCallbacks.size === 0 && broadcastChannel) {\n    broadcastChannel.close();\n    broadcastChannel = null;\n  }\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { DBSchema, IDBPDatabase, openDB } from 'idb';\nimport { AppConfig } from '../interfaces/installation-impl';\nimport { InstallationEntry } from '../interfaces/installation-entry';\nimport { getKey } from '../util/get-key';\nimport { fidChanged } from './fid-changed';\n\nconst DATABASE_NAME = 'firebase-installations-database';\nconst DATABASE_VERSION = 1;\nconst OBJECT_STORE_NAME = 'firebase-installations-store';\n\ninterface InstallationsDB extends DBSchema {\n  'firebase-installations-store': {\n    key: string;\n    value: InstallationEntry | undefined;\n  };\n}\n\nlet dbPromise: Promise<IDBPDatabase<InstallationsDB>> | null = null;\nfunction getDbPromise(): Promise<IDBPDatabase<InstallationsDB>> {\n  if (!dbPromise) {\n    dbPromise = openDB(DATABASE_NAME, DATABASE_VERSION, {\n      upgrade: (db, oldVersion) => {\n        // We don't use 'break' in this switch statement, the fall-through\n        // behavior is what we want, because if there are multiple versions between\n        // the old version and the current version, we want ALL the migrations\n        // that correspond to those versions to run, not only the last one.\n        // eslint-disable-next-line default-case\n        switch (oldVersion) {\n          case 0:\n            db.createObjectStore(OBJECT_STORE_NAME);\n        }\n      }\n    });\n  }\n  return dbPromise;\n}\n\n/** Gets record(s) from the objectStore that match the given key. */\nexport async function get(\n  appConfig: AppConfig\n): Promise<InstallationEntry | undefined> {\n  const key = getKey(appConfig);\n  const db = await getDbPromise();\n  return db\n    .transaction(OBJECT_STORE_NAME)\n    .objectStore(OBJECT_STORE_NAME)\n    .get(key) as Promise<InstallationEntry>;\n}\n\n/** Assigns or overwrites the record for the given key with the given value. */\nexport async function set<ValueType extends InstallationEntry>(\n  appConfig: AppConfig,\n  value: ValueType\n): Promise<ValueType> {\n  const key = getKey(appConfig);\n  const db = await getDbPromise();\n  const tx = db.transaction(OBJECT_STORE_NAME, 'readwrite');\n  const objectStore = tx.objectStore(OBJECT_STORE_NAME);\n  const oldValue = (await objectStore.get(key)) as InstallationEntry;\n  await objectStore.put(value, key);\n  await tx.done;\n\n  if (!oldValue || oldValue.fid !== value.fid) {\n    fidChanged(appConfig, value.fid);\n  }\n\n  return value;\n}\n\n/** Removes record(s) from the objectStore that match the given key. */\nexport async function remove(appConfig: AppConfig): Promise<void> {\n  const key = getKey(appConfig);\n  const db = await getDbPromise();\n  const tx = db.transaction(OBJECT_STORE_NAME, 'readwrite');\n  await tx.objectStore(OBJECT_STORE_NAME).delete(key);\n  await tx.done;\n}\n\n/**\n * Atomically updates a record with the result of updateFn, which gets\n * called with the current value. If newValue is undefined, the record is\n * deleted instead.\n * @return Updated value\n */\nexport async function update<ValueType extends InstallationEntry | undefined>(\n  appConfig: AppConfig,\n  updateFn: (previousValue: InstallationEntry | undefined) => ValueType\n): Promise<ValueType> {\n  const key = getKey(appConfig);\n  const db = await getDbPromise();\n  const tx = db.transaction(OBJECT_STORE_NAME, 'readwrite');\n  const store = tx.objectStore(OBJECT_STORE_NAME);\n  const oldValue: InstallationEntry | undefined = (await store.get(\n    key\n  )) as InstallationEntry;\n  const newValue = updateFn(oldValue);\n\n  if (newValue === undefined) {\n    await store.delete(key);\n  } else {\n    await store.put(newValue, key);\n  }\n  await tx.done;\n\n  if (newValue && (!oldValue || oldValue.fid !== newValue.fid)) {\n    fidChanged(appConfig, newValue.fid);\n  }\n\n  return newValue;\n}\n\nexport async function clear(): Promise<void> {\n  const db = await getDbPromise();\n  const tx = db.transaction(OBJECT_STORE_NAME, 'readwrite');\n  await tx.objectStore(OBJECT_STORE_NAME).clear();\n  await tx.done;\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { createInstallationRequest } from '../functions/create-installation-request';\nimport {\n  AppConfig,\n  FirebaseInstallationsImpl\n} from '../interfaces/installation-impl';\nimport {\n  InProgressInstallationEntry,\n  InstallationEntry,\n  RegisteredInstallationEntry,\n  RequestStatus\n} from '../interfaces/installation-entry';\nimport { PENDING_TIMEOUT_MS } from '../util/constants';\nimport { ERROR_FACTORY, ErrorCode, isServerError } from '../util/errors';\nimport { sleep } from '../util/sleep';\nimport { generateFid, INVALID_FID } from './generate-fid';\nimport { remove, set, update } from './idb-manager';\n\nexport interface InstallationEntryWithRegistrationPromise {\n  installationEntry: InstallationEntry;\n  /** Exist iff the installationEntry is not registered. */\n  registrationPromise?: Promise<RegisteredInstallationEntry>;\n}\n\n/**\n * Updates and returns the InstallationEntry from the database.\n * Also triggers a registration request if it is necessary and possible.\n */\nexport async function getInstallationEntry(\n  installations: FirebaseInstallationsImpl\n): Promise<InstallationEntryWithRegistrationPromise> {\n  let registrationPromise: Promise<RegisteredInstallationEntry> | undefined;\n\n  const installationEntry = await update(installations.appConfig, oldEntry => {\n    const installationEntry = updateOrCreateInstallationEntry(oldEntry);\n    const entryWithPromise = triggerRegistrationIfNecessary(\n      installations,\n      installationEntry\n    );\n    registrationPromise = entryWithPromise.registrationPromise;\n    return entryWithPromise.installationEntry;\n  });\n\n  if (installationEntry.fid === INVALID_FID) {\n    // FID generation failed. Waiting for the FID from the server.\n    return { installationEntry: await registrationPromise! };\n  }\n\n  return {\n    installationEntry,\n    registrationPromise\n  };\n}\n\n/**\n * Creates a new Installation Entry if one does not exist.\n * Also clears timed out pending requests.\n */\nfunction updateOrCreateInstallationEntry(\n  oldEntry: InstallationEntry | undefined\n): InstallationEntry {\n  const entry: InstallationEntry = oldEntry || {\n    fid: generateFid(),\n    registrationStatus: RequestStatus.NOT_STARTED\n  };\n\n  return clearTimedOutRequest(entry);\n}\n\n/**\n * If the Firebase Installation is not registered yet, this will trigger the\n * registration and return an InProgressInstallationEntry.\n *\n * If registrationPromise does not exist, the installationEntry is guaranteed\n * to be registered.\n */\nfunction triggerRegistrationIfNecessary(\n  installations: FirebaseInstallationsImpl,\n  installationEntry: InstallationEntry\n): InstallationEntryWithRegistrationPromise {\n  if (installationEntry.registrationStatus === RequestStatus.NOT_STARTED) {\n    if (!navigator.onLine) {\n      // Registration required but app is offline.\n      const registrationPromiseWithError = Promise.reject(\n        ERROR_FACTORY.create(ErrorCode.APP_OFFLINE)\n      );\n      return {\n        installationEntry,\n        registrationPromise: registrationPromiseWithError\n      };\n    }\n\n    // Try registering. Change status to IN_PROGRESS.\n    const inProgressEntry: InProgressInstallationEntry = {\n      fid: installationEntry.fid,\n      registrationStatus: RequestStatus.IN_PROGRESS,\n      registrationTime: Date.now()\n    };\n    const registrationPromise = registerInstallation(\n      installations,\n      inProgressEntry\n    );\n    return { installationEntry: inProgressEntry, registrationPromise };\n  } else if (\n    installationEntry.registrationStatus === RequestStatus.IN_PROGRESS\n  ) {\n    return {\n      installationEntry,\n      registrationPromise: waitUntilFidRegistration(installations)\n    };\n  } else {\n    return { installationEntry };\n  }\n}\n\n/** This will be executed only once for each new Firebase Installation. */\nasync function registerInstallation(\n  installations: FirebaseInstallationsImpl,\n  installationEntry: InProgressInstallationEntry\n): Promise<RegisteredInstallationEntry> {\n  try {\n    const registeredInstallationEntry = await createInstallationRequest(\n      installations,\n      installationEntry\n    );\n    return set(installations.appConfig, registeredInstallationEntry);\n  } catch (e) {\n    if (isServerError(e) && e.customData.serverCode === 409) {\n      // Server returned a \"FID cannot be used\" error.\n      // Generate a new ID next time.\n      await remove(installations.appConfig);\n    } else {\n      // Registration failed. Set FID as not registered.\n      await set(installations.appConfig, {\n        fid: installationEntry.fid,\n        registrationStatus: RequestStatus.NOT_STARTED\n      });\n    }\n    throw e;\n  }\n}\n\n/** Call if FID registration is pending in another request. */\nasync function waitUntilFidRegistration(\n  installations: FirebaseInstallationsImpl\n): Promise<RegisteredInstallationEntry> {\n  // Unfortunately, there is no way of reliably observing when a value in\n  // IndexedDB changes (yet, see https://github.com/WICG/indexed-db-observers),\n  // so we need to poll.\n\n  let entry: InstallationEntry = await updateInstallationRequest(\n    installations.appConfig\n  );\n  while (entry.registrationStatus === RequestStatus.IN_PROGRESS) {\n    // createInstallation request still in progress.\n    await sleep(100);\n\n    entry = await updateInstallationRequest(installations.appConfig);\n  }\n\n  if (entry.registrationStatus === RequestStatus.NOT_STARTED) {\n    // The request timed out or failed in a different call. Try again.\n    const { installationEntry, registrationPromise } =\n      await getInstallationEntry(installations);\n\n    if (registrationPromise) {\n      return registrationPromise;\n    } else {\n      // if there is no registrationPromise, entry is registered.\n      return installationEntry as RegisteredInstallationEntry;\n    }\n  }\n\n  return entry;\n}\n\n/**\n * Called only if there is a CreateInstallation request in progress.\n *\n * Updates the InstallationEntry in the DB based on the status of the\n * CreateInstallation request.\n *\n * Returns the updated InstallationEntry.\n */\nfunction updateInstallationRequest(\n  appConfig: AppConfig\n): Promise<InstallationEntry> {\n  return update(appConfig, oldEntry => {\n    if (!oldEntry) {\n      throw ERROR_FACTORY.create(ErrorCode.INSTALLATION_NOT_FOUND);\n    }\n    return clearTimedOutRequest(oldEntry);\n  });\n}\n\nfunction clearTimedOutRequest(entry: InstallationEntry): InstallationEntry {\n  if (hasInstallationRequestTimedOut(entry)) {\n    return {\n      fid: entry.fid,\n      registrationStatus: RequestStatus.NOT_STARTED\n    };\n  }\n\n  return entry;\n}\n\nfunction hasInstallationRequestTimedOut(\n  installationEntry: InstallationEntry\n): boolean {\n  return (\n    installationEntry.registrationStatus === RequestStatus.IN_PROGRESS &&\n    installationEntry.registrationTime + PENDING_TIMEOUT_MS < Date.now()\n  );\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { CreateInstallationResponse } from '../interfaces/api-response';\nimport {\n  InProgressInstallationEntry,\n  RegisteredInstallationEntry,\n  RequestStatus\n} from '../interfaces/installation-entry';\nimport { INTERNAL_AUTH_VERSION, PACKAGE_VERSION } from '../util/constants';\nimport {\n  extractAuthTokenInfoFromResponse,\n  getErrorFromResponse,\n  getHeaders,\n  getInstallationsEndpoint,\n  retryIfServerError\n} from './common';\nimport { FirebaseInstallationsImpl } from '../interfaces/installation-impl';\n\nexport async function createInstallationRequest(\n  { appConfig, heartbeatServiceProvider }: FirebaseInstallationsImpl,\n  { fid }: InProgressInstallationEntry\n): Promise<RegisteredInstallationEntry> {\n  const endpoint = getInstallationsEndpoint(appConfig);\n\n  const headers = getHeaders(appConfig);\n\n  // If heartbeat service exists, add the heartbeat string to the header.\n  const heartbeatService = heartbeatServiceProvider.getImmediate({\n    optional: true\n  });\n  if (heartbeatService) {\n    const heartbeatsHeader = await heartbeatService.getHeartbeatsHeader();\n    if (heartbeatsHeader) {\n      headers.append('x-firebase-client', heartbeatsHeader);\n    }\n  }\n\n  const body = {\n    fid,\n    authVersion: INTERNAL_AUTH_VERSION,\n    appId: appConfig.appId,\n    sdkVersion: PACKAGE_VERSION\n  };\n\n  const request: RequestInit = {\n    method: 'POST',\n    headers,\n    body: JSON.stringify(body)\n  };\n\n  const response = await retryIfServerError(() => fetch(endpoint, request));\n  if (response.ok) {\n    const responseValue: CreateInstallationResponse = await response.json();\n    const registeredInstallationEntry: RegisteredInstallationEntry = {\n      fid: responseValue.fid || fid,\n      registrationStatus: RequestStatus.COMPLETED,\n      refreshToken: responseValue.refreshToken,\n      authToken: extractAuthTokenInfoFromResponse(responseValue.authToken)\n    };\n    return registeredInstallationEntry;\n  } else {\n    throw await getErrorFromResponse('Create Installation', response);\n  }\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { GenerateAuthTokenResponse } from '../interfaces/api-response';\nimport {\n  CompletedAuthToken,\n  RegisteredInstallationEntry\n} from '../interfaces/installation-entry';\nimport { PACKAGE_VERSION } from '../util/constants';\nimport {\n  extractAuthTokenInfoFromResponse,\n  getErrorFromResponse,\n  getHeadersWithAuth,\n  getInstallationsEndpoint,\n  retryIfServerError\n} from './common';\nimport {\n  FirebaseInstallationsImpl,\n  AppConfig\n} from '../interfaces/installation-impl';\n\nexport async function generateAuthTokenRequest(\n  { appConfig, heartbeatServiceProvider }: FirebaseInstallationsImpl,\n  installationEntry: RegisteredInstallationEntry\n): Promise<CompletedAuthToken> {\n  const endpoint = getGenerateAuthTokenEndpoint(appConfig, installationEntry);\n\n  const headers = getHeadersWithAuth(appConfig, installationEntry);\n\n  // If heartbeat service exists, add the heartbeat string to the header.\n  const heartbeatService = heartbeatServiceProvider.getImmediate({\n    optional: true\n  });\n  if (heartbeatService) {\n    const heartbeatsHeader = await heartbeatService.getHeartbeatsHeader();\n    if (heartbeatsHeader) {\n      headers.append('x-firebase-client', heartbeatsHeader);\n    }\n  }\n\n  const body = {\n    installation: {\n      sdkVersion: PACKAGE_VERSION,\n      appId: appConfig.appId\n    }\n  };\n\n  const request: RequestInit = {\n    method: 'POST',\n    headers,\n    body: JSON.stringify(body)\n  };\n\n  const response = await retryIfServerError(() => fetch(endpoint, request));\n  if (response.ok) {\n    const responseValue: GenerateAuthTokenResponse = await response.json();\n    const completedAuthToken: CompletedAuthToken =\n      extractAuthTokenInfoFromResponse(responseValue);\n    return completedAuthToken;\n  } else {\n    throw await getErrorFromResponse('Generate Auth Token', response);\n  }\n}\n\nfunction getGenerateAuthTokenEndpoint(\n  appConfig: AppConfig,\n  { fid }: RegisteredInstallationEntry\n): string {\n  return `${getInstallationsEndpoint(appConfig)}/${fid}/authTokens:generate`;\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { generateAuthTokenRequest } from '../functions/generate-auth-token-request';\nimport {\n  AppConfig,\n  FirebaseInstallationsImpl\n} from '../interfaces/installation-impl';\nimport {\n  AuthToken,\n  CompletedAuthToken,\n  InProgressAuthToken,\n  InstallationEntry,\n  RegisteredInstallationEntry,\n  RequestStatus\n} from '../interfaces/installation-entry';\nimport { PENDING_TIMEOUT_MS, TOKEN_EXPIRATION_BUFFER } from '../util/constants';\nimport { ERROR_FACTORY, ErrorCode, isServerError } from '../util/errors';\nimport { sleep } from '../util/sleep';\nimport { remove, set, update } from './idb-manager';\n\n/**\n * Returns a valid authentication token for the installation. Generates a new\n * token if one doesn't exist, is expired or about to expire.\n *\n * Should only be called if the Firebase Installation is registered.\n */\nexport async function refreshAuthToken(\n  installations: FirebaseInstallationsImpl,\n  forceRefresh = false\n): Promise<CompletedAuthToken> {\n  let tokenPromise: Promise<CompletedAuthToken> | undefined;\n  const entry = await update(installations.appConfig, oldEntry => {\n    if (!isEntryRegistered(oldEntry)) {\n      throw ERROR_FACTORY.create(ErrorCode.NOT_REGISTERED);\n    }\n\n    const oldAuthToken = oldEntry.authToken;\n    if (!forceRefresh && isAuthTokenValid(oldAuthToken)) {\n      // There is a valid token in the DB.\n      return oldEntry;\n    } else if (oldAuthToken.requestStatus === RequestStatus.IN_PROGRESS) {\n      // There already is a token request in progress.\n      tokenPromise = waitUntilAuthTokenRequest(installations, forceRefresh);\n      return oldEntry;\n    } else {\n      // No token or token expired.\n      if (!navigator.onLine) {\n        throw ERROR_FACTORY.create(ErrorCode.APP_OFFLINE);\n      }\n\n      const inProgressEntry = makeAuthTokenRequestInProgressEntry(oldEntry);\n      tokenPromise = fetchAuthTokenFromServer(installations, inProgressEntry);\n      return inProgressEntry;\n    }\n  });\n\n  const authToken = tokenPromise\n    ? await tokenPromise\n    : (entry.authToken as CompletedAuthToken);\n  return authToken;\n}\n\n/**\n * Call only if FID is registered and Auth Token request is in progress.\n *\n * Waits until the current pending request finishes. If the request times out,\n * tries once in this thread as well.\n */\nasync function waitUntilAuthTokenRequest(\n  installations: FirebaseInstallationsImpl,\n  forceRefresh: boolean\n): Promise<CompletedAuthToken> {\n  // Unfortunately, there is no way of reliably observing when a value in\n  // IndexedDB changes (yet, see https://github.com/WICG/indexed-db-observers),\n  // so we need to poll.\n\n  let entry = await updateAuthTokenRequest(installations.appConfig);\n  while (entry.authToken.requestStatus === RequestStatus.IN_PROGRESS) {\n    // generateAuthToken still in progress.\n    await sleep(100);\n\n    entry = await updateAuthTokenRequest(installations.appConfig);\n  }\n\n  const authToken = entry.authToken;\n  if (authToken.requestStatus === RequestStatus.NOT_STARTED) {\n    // The request timed out or failed in a different call. Try again.\n    return refreshAuthToken(installations, forceRefresh);\n  } else {\n    return authToken;\n  }\n}\n\n/**\n * Called only if there is a GenerateAuthToken request in progress.\n *\n * Updates the InstallationEntry in the DB based on the status of the\n * GenerateAuthToken request.\n *\n * Returns the updated InstallationEntry.\n */\nfunction updateAuthTokenRequest(\n  appConfig: AppConfig\n): Promise<RegisteredInstallationEntry> {\n  return update(appConfig, oldEntry => {\n    if (!isEntryRegistered(oldEntry)) {\n      throw ERROR_FACTORY.create(ErrorCode.NOT_REGISTERED);\n    }\n\n    const oldAuthToken = oldEntry.authToken;\n    if (hasAuthTokenRequestTimedOut(oldAuthToken)) {\n      return {\n        ...oldEntry,\n        authToken: { requestStatus: RequestStatus.NOT_STARTED }\n      };\n    }\n\n    return oldEntry;\n  });\n}\n\nasync function fetchAuthTokenFromServer(\n  installations: FirebaseInstallationsImpl,\n  installationEntry: RegisteredInstallationEntry\n): Promise<CompletedAuthToken> {\n  try {\n    const authToken = await generateAuthTokenRequest(\n      installations,\n      installationEntry\n    );\n    const updatedInstallationEntry: RegisteredInstallationEntry = {\n      ...installationEntry,\n      authToken\n    };\n    await set(installations.appConfig, updatedInstallationEntry);\n    return authToken;\n  } catch (e) {\n    if (\n      isServerError(e) &&\n      (e.customData.serverCode === 401 || e.customData.serverCode === 404)\n    ) {\n      // Server returned a \"FID not found\" or a \"Invalid authentication\" error.\n      // Generate a new ID next time.\n      await remove(installations.appConfig);\n    } else {\n      const updatedInstallationEntry: RegisteredInstallationEntry = {\n        ...installationEntry,\n        authToken: { requestStatus: RequestStatus.NOT_STARTED }\n      };\n      await set(installations.appConfig, updatedInstallationEntry);\n    }\n    throw e;\n  }\n}\n\nfunction isEntryRegistered(\n  installationEntry: InstallationEntry | undefined\n): installationEntry is RegisteredInstallationEntry {\n  return (\n    installationEntry !== undefined &&\n    installationEntry.registrationStatus === RequestStatus.COMPLETED\n  );\n}\n\nfunction isAuthTokenValid(authToken: AuthToken): boolean {\n  return (\n    authToken.requestStatus === RequestStatus.COMPLETED &&\n    !isAuthTokenExpired(authToken)\n  );\n}\n\nfunction isAuthTokenExpired(authToken: CompletedAuthToken): boolean {\n  const now = Date.now();\n  return (\n    now < authToken.creationTime ||\n    authToken.creationTime + authToken.expiresIn < now + TOKEN_EXPIRATION_BUFFER\n  );\n}\n\n/** Returns an updated InstallationEntry with an InProgressAuthToken. */\nfunction makeAuthTokenRequestInProgressEntry(\n  oldEntry: RegisteredInstallationEntry\n): RegisteredInstallationEntry {\n  const inProgressAuthToken: InProgressAuthToken = {\n    requestStatus: RequestStatus.IN_PROGRESS,\n    requestTime: Date.now()\n  };\n  return {\n    ...oldEntry,\n    authToken: inProgressAuthToken\n  };\n}\n\nfunction hasAuthTokenRequestTimedOut(authToken: AuthToken): boolean {\n  return (\n    authToken.requestStatus === RequestStatus.IN_PROGRESS &&\n    authToken.requestTime + PENDING_TIMEOUT_MS < Date.now()\n  );\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { getInstallationEntry } from '../helpers/get-installation-entry';\nimport { refreshAuthToken } from '../helpers/refresh-auth-token';\nimport { FirebaseInstallationsImpl } from '../interfaces/installation-impl';\nimport { Installations } from '../interfaces/public-types';\n\n/**\n * Creates a Firebase Installation if there isn't one for the app and\n * returns the Installation ID.\n * @param installations - The `Installations` instance.\n *\n * @public\n */\nexport async function getId(installations: Installations): Promise<string> {\n  const installationsImpl = installations as FirebaseInstallationsImpl;\n  const { installationEntry, registrationPromise } = await getInstallationEntry(\n    installationsImpl\n  );\n\n  if (registrationPromise) {\n    registrationPromise.catch(console.error);\n  } else {\n    // If the installation is already registered, update the authentication\n    // token if needed.\n    refreshAuthToken(installationsImpl).catch(console.error);\n  }\n\n  return installationEntry.fid;\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { getInstallationEntry } from '../helpers/get-installation-entry';\nimport { refreshAuthToken } from '../helpers/refresh-auth-token';\nimport { FirebaseInstallationsImpl } from '../interfaces/installation-impl';\nimport { Installations } from '../interfaces/public-types';\n\n/**\n * Returns a Firebase Installations auth token, identifying the current\n * Firebase Installation.\n * @param installations - The `Installations` instance.\n * @param forceRefresh - Force refresh regardless of token expiration.\n *\n * @public\n */\nexport async function getToken(\n  installations: Installations,\n  forceRefresh = false\n): Promise<string> {\n  const installationsImpl = installations as FirebaseInstallationsImpl;\n  await completeInstallationRegistration(installationsImpl);\n\n  // At this point we either have a Registered Installation in the DB, or we've\n  // already thrown an error.\n  const authToken = await refreshAuthToken(installationsImpl, forceRefresh);\n  return authToken.token;\n}\n\nasync function completeInstallationRegistration(\n  installations: FirebaseInstallationsImpl\n): Promise<void> {\n  const { registrationPromise } = await getInstallationEntry(installations);\n\n  if (registrationPromise) {\n    // A createInstallation request is in progress. Wait until it finishes.\n    await registrationPromise;\n  }\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { AppConfig } from '../interfaces/installation-impl';\nimport { RegisteredInstallationEntry } from '../interfaces/installation-entry';\nimport {\n  getErrorFromResponse,\n  getHeadersWithAuth,\n  getInstallationsEndpoint,\n  retryIfServerError\n} from './common';\n\nexport async function deleteInstallationRequest(\n  appConfig: AppConfig,\n  installationEntry: RegisteredInstallationEntry\n): Promise<void> {\n  const endpoint = getDeleteEndpoint(appConfig, installationEntry);\n\n  const headers = getHeadersWithAuth(appConfig, installationEntry);\n  const request: RequestInit = {\n    method: 'DELETE',\n    headers\n  };\n\n  const response = await retryIfServerError(() => fetch(endpoint, request));\n  if (!response.ok) {\n    throw await getErrorFromResponse('Delete Installation', response);\n  }\n}\n\nfunction getDeleteEndpoint(\n  appConfig: AppConfig,\n  { fid }: RegisteredInstallationEntry\n): string {\n  return `${getInstallationsEndpoint(appConfig)}/${fid}`;\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { deleteInstallationRequest } from '../functions/delete-installation-request';\nimport { remove, update } from '../helpers/idb-manager';\nimport { RequestStatus } from '../interfaces/installation-entry';\nimport { ERROR_FACTORY, ErrorCode } from '../util/errors';\nimport { FirebaseInstallationsImpl } from '../interfaces/installation-impl';\nimport { Installations } from '../interfaces/public-types';\n\n/**\n * Deletes the Firebase Installation and all associated data.\n * @param installations - The `Installations` instance.\n *\n * @public\n */\nexport async function deleteInstallations(\n  installations: Installations\n): Promise<void> {\n  const { appConfig } = installations as FirebaseInstallationsImpl;\n\n  const entry = await update(appConfig, oldEntry => {\n    if (oldEntry && oldEntry.registrationStatus === RequestStatus.NOT_STARTED) {\n      // Delete the unregistered entry without sending a deleteInstallation request.\n      return undefined;\n    }\n    return oldEntry;\n  });\n\n  if (entry) {\n    if (entry.registrationStatus === RequestStatus.IN_PROGRESS) {\n      // Can't delete while trying to register.\n      throw ERROR_FACTORY.create(ErrorCode.DELETE_PENDING_REGISTRATION);\n    } else if (entry.registrationStatus === RequestStatus.COMPLETED) {\n      if (!navigator.onLine) {\n        throw ERROR_FACTORY.create(ErrorCode.APP_OFFLINE);\n      } else {\n        await deleteInstallationRequest(appConfig, entry);\n        await remove(appConfig);\n      }\n    }\n  }\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { addCallback, removeCallback } from '../helpers/fid-changed';\nimport { FirebaseInstallationsImpl } from '../interfaces/installation-impl';\nimport { Installations } from '../interfaces/public-types';\n\n/**\n * An user defined callback function that gets called when Installations ID changes.\n *\n * @public\n */\nexport type IdChangeCallbackFn = (installationId: string) => void;\n/**\n * Unsubscribe a callback function previously added via {@link IdChangeCallbackFn}.\n *\n * @public\n */\nexport type IdChangeUnsubscribeFn = () => void;\n\n/**\n * Sets a new callback that will get called when Installation ID changes.\n * Returns an unsubscribe function that will remove the callback when called.\n * @param installations - The `Installations` instance.\n * @param callback - The callback function that is invoked when FID changes.\n * @returns A function that can be called to unsubscribe.\n *\n * @public\n */\nexport function onIdChange(\n  installations: Installations,\n  callback: IdChangeCallbackFn\n): IdChangeUnsubscribeFn {\n  const { appConfig } = installations as FirebaseInstallationsImpl;\n\n  addCallback(appConfig, callback);\n  return () => {\n    removeCallback(appConfig, callback);\n  };\n}\n", "/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { FirebaseApp, getApp, _getProvider } from '@firebase/app';\nimport { Installations } from '../interfaces/public-types';\n\n/**\n * Returns an instance of {@link Installations} associated with the given\n * {@link @firebase/app#FirebaseApp} instance.\n * @param app - The {@link @firebase/app#FirebaseApp} instance.\n *\n * @public\n */\nexport function getInstallations(app: FirebaseApp = getApp()): Installations {\n  const installationsImpl = _getProvider(app, 'installations').getImmediate();\n  return installationsImpl;\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { FirebaseApp, FirebaseOptions } from '@firebase/app';\nimport { FirebaseError } from '@firebase/util';\nimport { AppConfig } from '../interfaces/installation-impl';\nimport { ERROR_FACTORY, ErrorCode } from '../util/errors';\n\nexport function extractAppConfig(app: FirebaseApp): AppConfig {\n  if (!app || !app.options) {\n    throw getMissingValueError('App Configuration');\n  }\n\n  if (!app.name) {\n    throw getMissingValueError('App Name');\n  }\n\n  // Required app config keys\n  const configKeys: Array<keyof FirebaseOptions> = [\n    'projectId',\n    'apiKey',\n    'appId'\n  ];\n\n  for (const keyName of configKeys) {\n    if (!app.options[keyName]) {\n      throw getMissingValueError(keyName);\n    }\n  }\n\n  return {\n    appName: app.name,\n    projectId: app.options.projectId!,\n    apiKey: app.options.apiKey!,\n    appId: app.options.appId!\n  };\n}\n\nfunction getMissingValueError(valueName: string): FirebaseError {\n  return ERROR_FACTORY.create(ErrorCode.MISSING_APP_CONFIG_VALUES, {\n    valueName\n  });\n}\n", "/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { _registerComponent, _getProvider } from '@firebase/app';\nimport {\n  Component,\n  ComponentType,\n  InstanceFactory,\n  ComponentContainer\n} from '@firebase/component';\nimport { getId, getToken } from '../api/index';\nimport { _FirebaseInstallationsInternal } from '../interfaces/public-types';\nimport { FirebaseInstallationsImpl } from '../interfaces/installation-impl';\nimport { extractAppConfig } from '../helpers/extract-app-config';\n\nconst INSTALLATIONS_NAME = 'installations';\nconst INSTALLATIONS_NAME_INTERNAL = 'installations-internal';\n\nconst publicFactory: InstanceFactory<'installations'> = (\n  container: ComponentContainer\n) => {\n  const app = container.getProvider('app').getImmediate();\n  // Throws if app isn't configured properly.\n  const appConfig = extractAppConfig(app);\n  const heartbeatServiceProvider = _getProvider(app, 'heartbeat');\n\n  const installationsImpl: FirebaseInstallationsImpl = {\n    app,\n    appConfig,\n    heartbeatServiceProvider,\n    _delete: () => Promise.resolve()\n  };\n  return installationsImpl;\n};\n\nconst internalFactory: InstanceFactory<'installations-internal'> = (\n  container: ComponentContainer\n) => {\n  const app = container.getProvider('app').getImmediate();\n  // Internal FIS instance relies on public FIS instance.\n  const installations = _getProvider(app, INSTALLATIONS_NAME).getImmediate();\n\n  const installationsInternal: _FirebaseInstallationsInternal = {\n    getId: () => getId(installations),\n    getToken: (forceRefresh?: boolean) => getToken(installations, forceRefresh)\n  };\n  return installationsInternal;\n};\n\nexport function registerInstallations(): void {\n  _registerComponent(\n    new Component(INSTALLATIONS_NAME, publicFactory, ComponentType.PUBLIC)\n  );\n  _registerComponent(\n    new Component(\n      INSTALLATIONS_NAME_INTERNAL,\n      internalFactory,\n      ComponentType.PRIVATE\n    )\n  );\n}\n", "/**\n * The Firebase Installations Web SDK.\n * This SDK does not work in a Node.js environment.\n *\n * @packageDocumentation\n */\n\n/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { registerInstallations } from './functions/config';\nimport { registerVersion } from '@firebase/app';\nimport { name, version } from '../package.json';\n\nexport * from './api';\nexport * from './interfaces/public-types';\n\nregisterInstallations();\nregisterVersion(name, version);\n// BUILD_TARGET will be replaced by values like esm5, esm2017, cjs5, etc during the compilation\nregisterVersion(name, version, '__BUILD_TARGET__');\n"], "names": ["FirebaseError", "Error", "constructor", "code", "message", "customData", "super", "this", "name", "Object", "setPrototypeOf", "prototype", "captureStackTrace", "ErrorFactory", "create", "service", "serviceName", "errors", "data", "fullCode", "template", "replaceTemplate", "replace", "PATTERN", "_", "key", "value", "String", "fullMessage", "Component", "instanceFactory", "type", "multipleInstances", "serviceProps", "instantiationMode", "onInstanceCreated", "setInstantiationMode", "mode", "setMultipleInstances", "setServiceProps", "props", "setInstanceCreatedCallback", "callback", "idbProxyableTypes", "cursorAdvanceMethods", "cursorRequestMap", "WeakMap", "transactionDoneMap", "transactionStoreNamesMap", "transformCache", "reverseTransformCache", "idbProxyTraps", "get", "target", "prop", "receiver", "IDBTransaction", "objectStoreNames", "undefined", "objectStore", "wrap", "set", "has", "wrapFunction", "func", "IDBDatabase", "transaction", "getCursorAdvanceMethods", "IDBCursor", "advance", "continue", "continuePrimaryKey", "includes", "args", "apply", "unwrap", "storeNames", "tx", "call", "sort", "transformCachableValue", "cacheDonePromiseForTransaction", "done", "Promise", "resolve", "reject", "unlisten", "removeEventListener", "complete", "error", "DOMException", "addEventListener", "object", "getIdbProxyableTypes", "IDBObjectStore", "IDBIndex", "some", "c", "Proxy", "IDBRequest", "promisifyRequest", "request", "promise", "success", "result", "then", "catch", "newValue", "readMethods", "writeMethods", "cachedMethods", "Map", "getMethod", "targetFuncName", "useIndex", "isWrite", "method", "async", "storeName", "store", "index", "shift", "all", "replaceTraps", "oldTraps", "ERROR_FACTORY", "isServerError", "getInstallationsEndpoint", "projectId", "extractAuthTokenInfoFromResponse", "response", "token", "requestStatus", "expiresIn", "responseExpiresIn", "Number", "creationTime", "Date", "now", "getErrorFromResponse", "requestName", "errorData", "json", "serverCode", "serverMessage", "serverStatus", "status", "getHeaders", "<PERSON><PERSON><PERSON><PERSON>", "Headers", "Accept", "getHeadersWithAuth", "appConfig", "refreshToken", "headers", "append", "getAuthorizationHeader", "retryIfServerError", "fn", "sleep", "ms", "setTimeout", "VALID_FID_PATTERN", "generateFid", "fidByteArray", "Uint8Array", "self", "crypto", "msCrypto", "getRandomValues", "fid", "encode", "bufferToBase64UrlSafe", "array", "btoa", "fromCharCode", "substr", "test", "_a", "<PERSON><PERSON><PERSON>", "appName", "appId", "fidChangeCallbacks", "fidChanged", "callFidChangeCallbacks", "broadcastFidChange", "channel", "getBroadcastChannel", "postMessage", "closeBroadcastChannel", "callbacks", "broadcastChannel", "BroadcastChannel", "onmessage", "e", "size", "close", "OBJECT_STORE_NAME", "db<PERSON><PERSON><PERSON>", "getDbPromise", "openDB", "version", "blocked", "upgrade", "blocking", "terminated", "indexedDB", "open", "openPromise", "event", "oldVersion", "newVersion", "db", "createObjectStore", "oldValue", "put", "remove", "delete", "update", "updateFn", "getInstallationEntry", "installations", "registrationPromise", "installationEntry", "oldEntry", "updateOrCreateInstallationEntry", "clearTimedOutRequest", "registrationStatus", "entryWithPromise", "triggerRegistrationIfNecessary", "navigator", "onLine", "inProgressEntry", "registrationTime", "registerInstallation", "registeredInstallationEntry", "createInstallationRequest", "heartbeatServiceProvider", "endpoint", "heartbeatService", "getImmediate", "optional", "heartbeatsHeader", "getHeartbeatsHeader", "body", "authVersion", "sdkVersion", "JSON", "stringify", "fetch", "ok", "responseValue", "authToken", "waitUntilFidRegistration", "entry", "updateInstallationRequest", "hasInstallationRequestTimedOut", "generateAuthTokenRequest", "getGenerateAuthTokenEndpoint", "installation", "refreshAuthToken", "forceRefresh", "tokenPromise", "isEntryRegistered", "oldAuthToken", "isAuthTokenValid", "isAuthTokenExpired", "waitUntilAuthTokenRequest", "updateAuthTokenRequest", "makeAuthTokenRequestInProgressEntry", "inProgressAuthToken", "requestTime", "assign", "fetchAuthTokenFromServer", "updatedInstallationEntry", "hasAuthTokenRequestTimedOut", "getId", "installationsImpl", "console", "getToken", "completeInstallationRegistration", "deleteInstallationRequest", "getDeleteEndpoint", "deleteInstallations", "onIdChange", "addCallback", "callbackSet", "Set", "add", "removeCallback", "getInstallations", "app", "getApp", "_get<PERSON><PERSON><PERSON>", "getMissingValueError", "valueName", "publicFactory", "container", "get<PERSON><PERSON><PERSON>", "extractAppConfig", "options", "config<PERSON><PERSON><PERSON>", "keyName", "_delete", "internalFactory", "registerInstallations", "_registerComponent", "registerVersion"], "mappings": "iGAyEM,MAAOA,sBAAsBC,MAIjCC,YAEWC,EACTC,EAEOC,GAEPC,MAAMF,GALGG,KAAIJ,KAAJA,EAGFI,KAAUF,WAAVA,EAPAE,KAAIC,KAdI,gBA2BfC,OAAOC,eAAeH,KAAMP,cAAcW,WAItCV,MAAMW,mBACRX,MAAMW,kBAAkBL,KAAMM,aAAaF,UAAUG,SAK9C,MAAAD,aAIXX,YACmBa,EACAC,EACAC,GAFAV,KAAOQ,QAAPA,EACAR,KAAWS,YAAXA,EACAT,KAAMU,OAANA,EAGnBH,OACEX,KACGe,GAEH,MAAMb,EAAca,EAAK,IAAoB,GACvCC,EAAW,GAAGZ,KAAKQ,WAAWZ,IAC9BiB,EAAWb,KAAKU,OAAOd,GAEvBC,EAAUgB,EAUpB,SAASC,gBAAgBD,EAAkBF,GACzC,OAAOE,EAASE,QAAQC,GAAS,CAACC,EAAGC,KACnC,MAAMC,EAAQR,EAAKO,GACnB,OAAgB,MAATC,EAAgBC,OAAOD,GAAS,IAAID,SAbhBJ,CAAgBD,EAAUf,GAAc,QAE7DuB,EAAc,GAAGrB,KAAKS,gBAAgBZ,MAAYe,MAIxD,OAFc,IAAInB,cAAcmB,EAAUS,EAAavB,IAa3D,MAAMkB,EAAU,gBCzGH,MAAAM,UAiBX3B,YACWM,EACAsB,EACAC,GAFAxB,KAAIC,KAAJA,EACAD,KAAeuB,gBAAfA,EACAvB,KAAIwB,KAAJA,EAnBXxB,KAAiByB,mBAAG,EAIpBzB,KAAY0B,aAAe,GAE3B1B,KAAA2B,kBAA2C,OAE3C3B,KAAiB4B,kBAAwC,KAczDC,qBAAqBC,GAEnB,OADA9B,KAAK2B,kBAAoBG,EAClB9B,KAGT+B,qBAAqBN,GAEnB,OADAzB,KAAKyB,kBAAoBA,EAClBzB,KAGTgC,gBAAgBC,GAEd,OADAjC,KAAK0B,aAAeO,EACbjC,KAGTkC,2BAA2BC,GAEzB,OADAnC,KAAK4B,kBAAoBO,EAClBnC,MClEX,IAAIoC,EACAC,EAqBJ,MAAMC,EAAmB,IAAIC,QACvBC,EAAqB,IAAID,QACzBE,EAA2B,IAAIF,QAC/BG,EAAiB,IAAIH,QACrBI,EAAwB,IAAIJ,QA0DlC,IAAIK,EAAgB,CAChBC,IAAIC,EAAQC,EAAMC,GACd,GAAIF,aAAkBG,eAAgB,CAElC,GAAa,SAATF,EACA,OAAOP,EAAmBK,IAAIC,GAElC,GAAa,qBAATC,EACA,OAAOD,EAAOI,kBAAoBT,EAAyBI,IAAIC,GAGnE,GAAa,UAATC,EACA,OAAOC,EAASE,iBAAiB,QAC3BC,EACAH,EAASI,YAAYJ,EAASE,iBAAiB,IAI7D,OAAOG,KAAKP,EAAOC,KAEvBO,IAAG,CAACR,EAAQC,EAAM5B,KACd2B,EAAOC,GAAQ5B,GACR,GAEXoC,IAAG,CAACT,EAAQC,IACJD,aAAkBG,iBACR,SAATF,GAA4B,UAATA,IAGjBA,KAAQD,GAMvB,SAASU,aAAaC,GAIlB,OAAIA,IAASC,YAAYtD,UAAUuD,aAC7B,qBAAsBV,eAAe7C,UA9G/C,SAASwD,0BACL,OAAQvB,IACHA,EAAuB,CACpBwB,UAAUzD,UAAU0D,QACpBD,UAAUzD,UAAU2D,SACpBF,UAAUzD,UAAU4D,qBAqHxBJ,GAA0BK,SAASR,GAC5B,YAAaS,GAIhB,OADAT,EAAKU,MAAMC,OAAOpE,MAAOkE,GAClBb,KAAKf,EAAiBO,IAAI7C,QAGlC,YAAakE,GAGhB,OAAOb,KAAKI,EAAKU,MAAMC,OAAOpE,MAAOkE,KAtB9B,SAAUG,KAAeH,GAC5B,MAAMI,EAAKb,EAAKc,KAAKH,OAAOpE,MAAOqE,KAAeH,GAElD,OADAzB,EAAyBa,IAAIgB,EAAID,EAAWG,KAAOH,EAAWG,OAAS,CAACH,IACjEhB,KAAKiB,IAsBxB,SAASG,uBAAuBtD,GAC5B,MAAqB,mBAAVA,EACAqC,aAAarC,IAGpBA,aAAiB8B,gBAhGzB,SAASyB,+BAA+BJ,GAEpC,GAAI9B,EAAmBe,IAAIe,GACvB,OACJ,MAAMK,EAAO,IAAIC,SAAQ,CAACC,EAASC,KAC/B,MAAMC,SAAW,KACbT,EAAGU,oBAAoB,WAAYC,UACnCX,EAAGU,oBAAoB,QAASE,OAChCZ,EAAGU,oBAAoB,QAASE,QAE9BD,SAAW,KACbJ,IACAE,YAEEG,MAAQ,KACVJ,EAAOR,EAAGY,OAAS,IAAIC,aAAa,aAAc,eAClDJ,YAEJT,EAAGc,iBAAiB,WAAYH,UAChCX,EAAGc,iBAAiB,QAASF,OAC7BZ,EAAGc,iBAAiB,QAASF,UAGjC1C,EAAmBc,IAAIgB,EAAIK,GA0EvBD,CAA+BvD,GA9JhBkE,EA+JDlE,EA1JtB,SAASmE,uBACL,OAAQlD,IACHA,EAAoB,CACjBsB,YACA6B,eACAC,SACA3B,UACAZ,iBAmJiBqC,GA/JgCG,MAAMC,GAAML,aAAkBK,IAgK5E,IAAIC,MAAMxE,EAAOyB,GAErBzB,GAlKW,IAACkE,EAoKvB,SAAShC,KAAKlC,GAGV,GAAIA,aAAiByE,WACjB,OA3IR,SAASC,iBAAiBC,GACtB,MAAMC,EAAU,IAAInB,SAAQ,CAACC,EAASC,KAClC,MAAMC,SAAW,KACbe,EAAQd,oBAAoB,UAAWgB,SACvCF,EAAQd,oBAAoB,QAASE,QAEnCc,QAAU,KACZnB,EAAQxB,KAAKyC,EAAQG,SACrBlB,YAEEG,MAAQ,KACVJ,EAAOgB,EAAQZ,OACfH,YAEJe,EAAQV,iBAAiB,UAAWY,SACpCF,EAAQV,iBAAiB,QAASF,UAetC,OAbAa,EACKG,MAAM/E,IAGHA,aAAiB0C,WACjBvB,EAAiBgB,IAAInC,EAAO2E,MAI/BK,OAAM,SAGXxD,EAAsBW,IAAIyC,EAASD,GAC5BC,EA6GIF,CAAiB1E,GAG5B,GAAIuB,EAAea,IAAIpC,GACnB,OAAOuB,EAAeG,IAAI1B,GAC9B,MAAMiF,EAAW3B,uBAAuBtD,GAOxC,OAJIiF,IAAajF,IACbuB,EAAeY,IAAInC,EAAOiF,GAC1BzD,EAAsBW,IAAI8C,EAAUjF,IAEjCiF,EAEX,MAAMhC,OAAUjD,GAAUwB,EAAsBE,IAAI1B,GCrIpD,MAAMkF,EAAc,CAAC,MAAO,SAAU,SAAU,aAAc,SACxDC,EAAe,CAAC,MAAO,MAAO,SAAU,SACxCC,EAAgB,IAAIC,IAC1B,SAASC,UAAU3D,EAAQC,GACvB,KAAMD,aAAkBY,cAClBX,KAAQD,GACM,iBAATC,EACP,OAEJ,GAAIwD,EAAc1D,IAAIE,GAClB,OAAOwD,EAAc1D,IAAIE,GAC7B,MAAM2D,EAAiB3D,EAAKhC,QAAQ,aAAc,IAC5C4F,EAAW5D,IAAS2D,EACpBE,EAAUN,EAAarC,SAASyC,GACtC,KAEEA,KAAmBC,EAAWnB,SAAWD,gBAAgBnF,aACrDwG,IAAWP,EAAYpC,SAASyC,GAClC,OAEJ,MAAMG,OAASC,eAAgBC,KAAc7C,GAEzC,MAAMI,EAAKtE,KAAK2D,YAAYoD,EAAWH,EAAU,YAAc,YAC/D,IAAI9D,EAASwB,EAAG0C,MAQhB,OAPIL,IACA7D,EAASA,EAAOmE,MAAM/C,EAAKgD,iBAMjBtC,QAAQuC,IAAI,CACtBrE,EAAO4D,MAAmBxC,GAC1B0C,GAAWtC,EAAGK,QACd,IAGR,OADA4B,EAAcjD,IAAIP,EAAM8D,QACjBA,QDgCX,SAASO,aAAajF,GAClBS,EAAgBT,EAASS,GC/B7BwE,EAAcC,IAAc,IACrBA,EACHxE,IAAK,CAACC,EAAQC,EAAMC,IAAayD,UAAU3D,EAAQC,IAASsE,EAASxE,IAAIC,EAAQC,EAAMC,GACvFO,IAAK,CAACT,EAAQC,MAAW0D,UAAU3D,EAAQC,IAASsE,EAAS9D,IAAIT,EAAQC,yCCxChEuE,EAAgB,IAAIhH,aCtBV,gBACK,gBDD2C,CACrE,4BACE,kDACF,iBAA4B,2CAC5B,yBAAoC,mCACpC,iBACE,6FACF,cAAyB,kDACzB,8BACE,6EA4BE,SAAUiH,cAAcrC,GAC5B,OACEA,aAAiBzF,eACjByF,EAAMtF,KAAKqE,SAAQ,kBEtCP,SAAAuD,0BAAyBC,UAAEA,IACzC,MAAO,4DAAqCA,kBAGxC,SAAUC,iCACdC,GAEA,MAAO,CACLC,MAAOD,EAASC,MAChBC,cAAsC,EACtCC,WA8DuCC,EA9DMJ,EAASG,UAgEjDE,OAAOD,EAAkBhH,QAAQ,IAAK,SA/D3CkH,aAAcC,KAAKC,OA6DvB,IAA2CJ,EAzDpCjB,eAAesB,qBACpBC,EACAV,GAEA,MACMW,SADoCX,EAASY,QACpBrD,MAC/B,OAAOoC,EAAc/G,OAAiC,iBAAA,CACpD8H,YAAAA,EACAG,WAAYF,EAAU1I,KACtB6I,cAAeH,EAAUzI,QACzB6I,aAAcJ,EAAUK,SAIZ,SAAAC,YAAWC,OAAEA,IAC3B,OAAO,IAAIC,QAAQ,CACjB,eAAgB,mBAChBC,OAAQ,mBACR,iBAAkBF,IAIN,SAAAG,mBACdC,GACAC,aAAEA,IAEF,MAAMC,EAAUP,WAAWK,GAE3B,OADAE,EAAQC,OAAO,gBAmCjB,SAASC,uBAAuBH,GAC9B,MAAO,UAA4BA,IApCHG,CAAuBH,IAChDC,EAgBFrC,eAAewC,mBACpBC,GAEA,MAAMtD,QAAesD,IAErB,OAAItD,EAAO0C,QAAU,KAAO1C,EAAO0C,OAAS,IAEnCY,IAGFtD,EClFH,SAAUuD,MAAMC,GACpB,OAAO,IAAI7E,SAAcC,IACvB6E,WAAW7E,EAAS4E,MCDjB,MAAME,EAAoB,oBAOjB,SAAAC,cACd,IAGE,MAAMC,EAAe,IAAIC,WAAW,KAElCC,KAAKC,QAAWD,KAAyCE,UACpDC,gBAAgBL,GAGvBA,EAAa,GAAK,IAAcA,EAAa,GAAK,GAElD,MAAMM,EAUV,SAASC,OAAOP,GAKd,OCpCI,SAAUQ,sBAAsBC,GAEpC,OADYC,KAAKnJ,OAAOoJ,gBAAgBF,IAC7BvJ,QAAQ,MAAO,KAAKA,QAAQ,MAAO,KD8B5BsJ,CAAsBR,GAIvBY,OAAO,EAAG,IAfbL,CAAOP,GAEnB,OAAOF,EAAkBe,KAAKP,GAAOA,EApBd,GAqBvB,MAAMQ,GAEN,MAvBuB,IEArB,SAAUC,OAAO3B,GACrB,MAAO,GAAGA,EAAU4B,WAAW5B,EAAU6B,QCA3C,MAAMC,EAA2D,IAAIvE,IAMrD,SAAAwE,WAAW/B,EAAsBkB,GAC/C,MAAMjJ,EAAM0J,OAAO3B,GAEnBgC,uBAAuB/J,EAAKiJ,GAsD9B,SAASe,mBAAmBhK,EAAaiJ,GACvC,MAAMgB,EAAUC,sBACZD,GACFA,EAAQE,YAAY,CAAEnK,IAAAA,EAAKiJ,IAAAA,IAE7BmB,wBA1DAJ,CAAmBhK,EAAKiJ,GA0C1B,SAASc,uBAAuB/J,EAAaiJ,GAC3C,MAAMoB,EAAYR,EAAmBlI,IAAI3B,GACzC,GAAKqK,EAIL,IAAK,MAAMpJ,KAAYoJ,EACrBpJ,EAASgI,GAYb,IAAIqB,EAA4C,KAEhD,SAASJ,sBAOP,OANKI,GAAoB,qBAAsBzB,OAC7CyB,EAAmB,IAAIC,iBAAiB,yBACxCD,EAAiBE,UAAYC,IAC3BV,uBAAuBU,EAAEhL,KAAKO,IAAKyK,EAAEhL,KAAKwJ,OAGvCqB,EAGT,SAASF,wBACyB,IAA5BP,EAAmBa,MAAcJ,IACnCA,EAAiBK,QACjBL,EAAmB,MCpFvB,MAEMM,EAAoB,+BAS1B,IAAIC,EAA2D,KAC/D,SAASC,eAgBP,OAfKD,IACHA,ET3BJ,SAASE,OAAOhM,EAAMiM,GAASC,QAAEA,EAAOC,QAAEA,EAAOC,SAAEA,EAAQC,WAAEA,GAAe,IACxE,MAAMxG,EAAUyG,UAAUC,KAAKvM,EAAMiM,GAC/BO,EAAcpJ,KAAKyC,GAoBzB,OAnBIsG,GACAtG,EAAQV,iBAAiB,iBAAkBsH,IACvCN,EAAQ/I,KAAKyC,EAAQG,QAASyG,EAAMC,WAAYD,EAAME,WAAYvJ,KAAKyC,EAAQnC,aAAc+I,MAGjGP,GACArG,EAAQV,iBAAiB,WAAYsH,GAAUP,EAE/CO,EAAMC,WAAYD,EAAME,WAAYF,KAExCD,EACKvG,MAAM2G,IACHP,GACAO,EAAGzH,iBAAiB,SAAS,IAAMkH,MACnCD,GACAQ,EAAGzH,iBAAiB,iBAAkBsH,GAAUL,EAASK,EAAMC,WAAYD,EAAME,WAAYF,QAGhGvG,OAAM,SACJsG,ESKKR,CAdM,kCACG,EAa+B,CAClDG,QAAS,CAACS,EAAIF,KAMZ,GACO,IADCA,EAEJE,EAAGC,kBAAkBhB,OAKxBC,EAgBFjF,eAAexD,IACpB2F,EACA9H,GAEA,MAAMD,EAAM0J,OAAO3B,GAEb3E,SADW0H,gBACHrI,YAAYmI,EAAmB,aACvC1I,EAAckB,EAAGlB,YAAY0I,GAC7BiB,QAAkB3J,EAAYP,IAAI3B,GAQxC,aAPMkC,EAAY4J,IAAI7L,EAAOD,SACvBoD,EAAGK,KAEJoI,GAAYA,EAAS5C,MAAQhJ,EAAMgJ,KACtCa,WAAW/B,EAAW9H,EAAMgJ,KAGvBhJ,EAIF2F,eAAemG,OAAOhE,GAC3B,MAAM/H,EAAM0J,OAAO3B,GAEb3E,SADW0H,gBACHrI,YAAYmI,EAAmB,mBACvCxH,EAAGlB,YAAY0I,GAAmBoB,OAAOhM,SACzCoD,EAAGK,KASJmC,eAAeqG,OACpBlE,EACAmE,GAEA,MAAMlM,EAAM0J,OAAO3B,GAEb3E,SADW0H,gBACHrI,YAAYmI,EAAmB,aACvC9E,EAAQ1C,EAAGlB,YAAY0I,GACvBiB,QAAiD/F,EAAMnE,IAC3D3B,GAEIkF,EAAWgH,EAASL,GAa1B,YAXiB5J,IAAbiD,QACIY,EAAMkG,OAAOhM,SAEb8F,EAAMgG,IAAI5G,EAAUlF,SAEtBoD,EAAGK,MAELyB,GAAc2G,GAAYA,EAAS5C,MAAQ/D,EAAS+D,KACtDa,WAAW/B,EAAW7C,EAAS+D,KAG1B/D,ECjFFU,eAAeuG,qBACpBC,GAEA,IAAIC,EAEJ,MAAMC,QAA0BL,OAAOG,EAAcrE,WAAWwE,IAC9D,MAAMD,EAwBV,SAASE,gCACPD,GAOA,OAAOE,qBAL0BF,GAAY,CAC3CtD,IAAKP,cACLgE,mBAA6C,IA7BnBF,CAAgCD,GACpDI,EAyCV,SAASC,+BACPR,EACAE,GAEA,GAAwC,IAApCA,EAAkBI,mBAAkD,CACtE,IAAKG,UAAUC,OAAQ,CAKrB,MAAO,CACLR,kBAAAA,EACAD,oBALmC3I,QAAQE,OAC3CwC,EAAc/G,OAA6B,iBAS/C,MAAM0N,EAA+C,CACnD9D,IAAKqD,EAAkBrD,IACvByD,mBAA6C,EAC7CM,iBAAkBhG,KAAKC,OAEnBoF,EAkBVzG,eAAeqH,qBACbb,EACAE,GAEA,IACE,MAAMY,QCxGHtH,eAAeuH,2BACpBpF,UAAEA,EAASqF,yBAAEA,IACbnE,IAAEA,IAEF,MAAMoE,EAAW/G,yBAAyByB,GAEpCE,EAAUP,WAAWK,GAGrBuF,EAAmBF,EAAyBG,aAAa,CAC7DC,UAAU,IAEZ,GAAIF,EAAkB,CACpB,MAAMG,QAAyBH,EAAiBI,sBAC5CD,GACFxF,EAAQC,OAAO,oBAAqBuF,GAIxC,MAAME,EAAO,CACX1E,IAAAA,EACA2E,YThCiC,SSiCjChE,MAAO7B,EAAU6B,MACjBiE,WTnC2B,WSsCvBjJ,EAAuB,CAC3Be,OAAQ,OACRsC,QAAAA,EACA0F,KAAMG,KAAKC,UAAUJ,IAGjBlH,QAAiB2B,oBAAmB,IAAM4F,MAAMX,EAAUzI,KAChE,GAAI6B,EAASwH,GAAI,CACf,MAAMC,QAAkDzH,EAASY,OAOjE,MANiE,CAC/D4B,IAAKiF,EAAcjF,KAAOA,EAC1ByD,mBAA2C,EAC3C1E,aAAckG,EAAclG,aAC5BmG,UAAW3H,iCAAiC0H,EAAcC,YAI5D,YAAYjH,qBAAqB,sBAAuBT,GD6Dd0G,CACxCf,EACAE,GAEF,OAAOlK,IAAIgK,EAAcrE,UAAWmF,GACpC,MAAOzC,GAYP,MAXIpE,cAAcoE,IAAkC,MAA5BA,EAAE7L,WAAW0I,iBAG7ByE,OAAOK,EAAcrE,iBAGrB3F,IAAIgK,EAAcrE,UAAW,CACjCkB,IAAKqD,EAAkBrD,IACvByD,mBAA6C,IAG3CjC,GAxCsBwC,CAC1Bb,EACAW,GAEF,MAAO,CAAET,kBAAmBS,EAAiBV,oBAAAA,GACxC,OAC+B,IAApCC,EAAkBI,mBAEX,CACLJ,kBAAAA,EACAD,oBAAqB+B,yBAAyBhC,IAGzC,CAAEE,kBAAAA,GA5EgBM,CACvBR,EACAE,GAGF,OADAD,EAAsBM,EAAiBN,oBAChCM,EAAiBL,qBAG1B,MLvCyB,KKuCrBA,EAAkBrD,IAEb,CAAEqD,wBAAyBD,GAG7B,CACLC,kBAAAA,EACAD,oBAAAA,GA6FJzG,eAAewI,yBACbhC,GAMA,IAAIiC,QAAiCC,0BACnClC,EAAcrE,WAEhB,KAA+B,IAAxBsG,EAAM3B,0BAELpE,MAAM,KAEZ+F,QAAcC,0BAA0BlC,EAAcrE,WAGxD,GAA4B,IAAxBsG,EAAM3B,mBAAkD,CAE1D,MAAMJ,kBAAEA,EAAiBD,oBAAEA,SACnBF,qBAAqBC,GAE7B,OAAIC,GAIKC,EAIX,OAAO+B,EAWT,SAASC,0BACPvG,GAEA,OAAOkE,OAAOlE,GAAWwE,IACvB,IAAKA,EACH,MAAMnG,EAAc/G,OAAM,0BAE5B,OAAOoN,qBAAqBF,MAIhC,SAASE,qBAAqB4B,GAC5B,OAUF,SAASE,+BACPjC,GAEA,OACoE,IAAlEA,EAAkBI,oBAClBJ,EAAkBU,iBRhNY,IQgN4BhG,KAAKC,MAf7DsH,CAA+BF,GAC1B,CACLpF,IAAKoF,EAAMpF,IACXyD,mBAA6C,GAI1C2B,EExLFzI,eAAe4I,0BACpBzG,UAAEA,EAASqF,yBAAEA,GACbd,GAEA,MAAMe,EAuCR,SAASoB,6BACP1G,GACAkB,IAAEA,IAEF,MAAO,GAAG3C,yBAAyByB,MAAckB,wBA3ChCwF,CAA6B1G,EAAWuE,GAEnDrE,EAAUH,mBAAmBC,EAAWuE,GAGxCgB,EAAmBF,EAAyBG,aAAa,CAC7DC,UAAU,IAEZ,GAAIF,EAAkB,CACpB,MAAMG,QAAyBH,EAAiBI,sBAC5CD,GACFxF,EAAQC,OAAO,oBAAqBuF,GAIxC,MAAME,EAAO,CACXe,aAAc,CACZb,WVnCyB,UUoCzBjE,MAAO7B,EAAU6B,QAIfhF,EAAuB,CAC3Be,OAAQ,OACRsC,QAAAA,EACA0F,KAAMG,KAAKC,UAAUJ,IAGjBlH,QAAiB2B,oBAAmB,IAAM4F,MAAMX,EAAUzI,KAChE,GAAI6B,EAASwH,GAAI,CAIf,OADEzH,uCAFqDC,EAASY,QAKhE,YAAYH,qBAAqB,sBAAuBT,GCjCrDb,eAAe+I,iBACpBvC,EACAwC,GAAe,GAEf,IAAIC,EACJ,MAAMR,QAAcpC,OAAOG,EAAcrE,WAAWwE,IAClD,IAAKuC,kBAAkBvC,GACrB,MAAMnG,EAAc/G,OAAM,kBAG5B,MAAM0P,EAAexC,EAAS4B,UAC9B,IAAKS,GA+HT,SAASI,iBAAiBb,GACxB,OACqD,IAAnDA,EAAUxH,gBAKd,SAASsI,mBAAmBd,GAC1B,MAAMlH,EAAMD,KAAKC,MACjB,OACEA,EAAMkH,EAAUpH,cAChBoH,EAAUpH,aAAeoH,EAAUvH,UAAYK,EXnKZ,KW2JlCgI,CAAmBd,GAlICa,CAAiBD,GAEpC,OAAOxC,EACF,GAA8B,IAA1BwC,EAAapI,cAGtB,OADAkI,EA0BNjJ,eAAesJ,0BACb9C,EACAwC,GAMA,IAAIP,QAAcc,uBAAuB/C,EAAcrE,WACvD,KAAoE,IAA7DsG,EAAMF,UAAUxH,qBAEf2B,MAAM,KAEZ+F,QAAcc,uBAAuB/C,EAAcrE,WAGrD,MAAMoG,EAAYE,EAAMF,UACxB,OAA2B,IAAvBA,EAAUxH,cAELgI,iBAAiBvC,EAAewC,GAEhCT,EA/CUe,CAA0B9C,EAAewC,GACjDrC,EACF,CAEL,IAAKM,UAAUC,OACb,MAAM1G,EAAc/G,OAAM,eAG5B,MAAM0N,EAkIZ,SAASqC,oCACP7C,GAEA,MAAM8C,EAA2C,CAC/C1I,cAAwC,EACxC2I,YAAatI,KAAKC,OAEpB,OAAAjI,OAAAuQ,OAAAvQ,OAAAuQ,OAAA,GACKhD,GAAQ,CACX4B,UAAWkB,IA3IeD,CAAoC7C,GAE5D,OADAsC,EAsENjJ,eAAe4J,yBACbpD,EACAE,GAEA,IACE,MAAM6B,QAAkBK,yBACtBpC,EACAE,GAEImD,EACDzQ,OAAAuQ,OAAAvQ,OAAAuQ,OAAA,GAAAjD,GACH,CAAA6B,UAAAA,IAGF,aADM/L,IAAIgK,EAAcrE,UAAW0H,GAC5BtB,EACP,MAAO1D,GACP,IACEpE,cAAcoE,IACe,MAA5BA,EAAE7L,WAAW0I,YAAkD,MAA5BmD,EAAE7L,WAAW0I,WAK5C,CACL,MAAMmI,EACDzQ,OAAAuQ,OAAAvQ,OAAAuQ,OAAA,GAAAjD,GACH,CAAA6B,UAAW,CAAExH,cAAa,WAEtBvE,IAAIgK,EAAcrE,UAAW0H,cAN7B1D,OAAOK,EAAcrE,WAQ7B,MAAM0C,GApGW+E,CAAyBpD,EAAeW,GAChDA,MAOX,OAHkB8B,QACRA,EACLR,EAAMF,UA2Cb,SAASgB,uBACPpH,GAEA,OAAOkE,OAAOlE,GAAWwE,IACvB,IAAKuC,kBAAkBvC,GACrB,MAAMnG,EAAc/G,OAAM,kBAI5B,OAmFJ,SAASqQ,4BAA4BvB,GACnC,OACuD,IAArDA,EAAUxH,eACVwH,EAAUmB,YXhMoB,IWgMetI,KAAKC,MAtF9CyI,CADiBnD,EAAS4B,WAGvBnP,OAAAuQ,OAAAvQ,OAAAuQ,OAAA,GAAAhD,GACH,CAAA4B,UAAW,CAAExH,cAAa,KAIvB4F,KAsCX,SAASuC,kBACPxC,GAEA,YACwBrK,IAAtBqK,GACgE,IAAhEA,EAAkBI,mBClJf9G,eAAe+J,MAAMvD,GAC1B,MAAMwD,EAAoBxD,GACpBE,kBAAEA,EAAiBD,oBAAEA,SAA8BF,qBACvDyD,GAWF,OARIvD,EACFA,EAAoBpH,MAAM4K,QAAQ7L,OAIlC2K,iBAAiBiB,GAAmB3K,MAAM4K,QAAQ7L,OAG7CsI,EAAkBrD,ICbpBrD,eAAekK,SACpB1D,EACAwC,GAAe,GAEf,MAAMgB,EAAoBxD,QAS5BxG,eAAemK,iCACb3D,GAEA,MAAMC,oBAAEA,SAA8BF,qBAAqBC,GAEvDC,SAEIA,EAfF0D,CAAiCH,GAKvC,aADwBjB,iBAAiBiB,EAAmBhB,IAC3ClI,MCdZd,eAAeoK,0BACpBjI,EACAuE,GAEA,MAAMe,EAcR,SAAS4C,kBACPlI,GACAkB,IAAEA,IAEF,MAAO,GAAG3C,yBAAyByB,MAAckB,IAlBhCgH,CAAkBlI,EAAWuE,GAGxC1H,EAAuB,CAC3Be,OAAQ,SACRsC,QAHcH,mBAAmBC,EAAWuE,IAMxC7F,QAAiB2B,oBAAmB,IAAM4F,MAAMX,EAAUzI,KAChE,IAAK6B,EAASwH,GACZ,YAAY/G,qBAAqB,sBAAuBT,GCVrDb,eAAesK,oBACpB9D,GAEA,MAAMrE,UAAEA,GAAcqE,EAEhBiC,QAAcpC,OAAOlE,GAAWwE,IACpC,IAAIA,GAAuE,IAA3DA,EAASG,mBAIzB,OAAOH,KAGT,GAAI8B,EAAO,CACT,GAA4B,IAAxBA,EAAM3B,mBAER,MAAMtG,EAAc/G,OAAM,+BACrB,GAA4B,IAAxBgP,EAAM3B,mBAAgD,CAC/D,IAAKG,UAAUC,OACb,MAAM1G,EAAc/G,OAAM,qBAEpB2Q,0BAA0BjI,EAAWsG,SACrCtC,OAAOhE,KCTL,SAAAoI,WACd/D,EACAnL,GAEA,MAAM8G,UAAEA,GAAcqE,EAGtB,OVhBc,SAAAgE,YACdrI,EACA9G,GAIAiJ,sBAEA,MAAMlK,EAAM0J,OAAO3B,GAEnB,IAAIsI,EAAcxG,EAAmBlI,IAAI3B,GACpCqQ,IACHA,EAAc,IAAIC,IAClBzG,EAAmBzH,IAAIpC,EAAKqQ,IAE9BA,EAAYE,IAAItP,GUAhBmP,CAAYrI,EAAW9G,GAChB,MVEO,SAAAuP,eACdzI,EACA9G,GAEA,MAAMjB,EAAM0J,OAAO3B,GAEbsI,EAAcxG,EAAmBlI,IAAI3B,GAEtCqQ,IAILA,EAAYrE,OAAO/K,GACM,IAArBoP,EAAY3F,MACdb,EAAmBmC,OAAOhM,GAI5BoK,yBUnBEoG,CAAezI,EAAW9G,ICxBd,SAAAwP,iBAAiBC,EAAmBC,KAElD,OAD0BC,aAAaF,EAAK,iBAAiBnD,eCwB/D,SAASsD,qBAAqBC,GAC5B,OAAO1K,EAAc/G,OAA4C,4BAAA,CAC/DyR,UAAAA,ICzBJ,MAGMC,cACJC,IAEA,MAAMN,EAAMM,EAAUC,YAAY,OAAO1D,eAEnCxF,EDfF,SAAUmJ,iBAAiBR,GAC/B,IAAKA,IAAQA,EAAIS,QACf,MAAMN,qBAAqB,qBAG7B,IAAKH,EAAI3R,KACP,MAAM8R,qBAAqB,YAI7B,MAAMO,EAA2C,CAC/C,YACA,SACA,SAGF,IAAK,MAAMC,KAAWD,EACpB,IAAKV,EAAIS,QAAQE,GACf,MAAMR,qBAAqBQ,GAI/B,MAAO,CACL1H,QAAS+G,EAAI3R,KACbwH,UAAWmK,EAAIS,QAAQ5K,UACvBoB,OAAQ+I,EAAIS,QAAQxJ,OACpBiC,MAAO8G,EAAIS,QAAQvH,OCXHsH,CAAiBR,GASnC,MANqD,CACnDA,IAAAA,EACA3I,UAAAA,EACAqF,yBAL+BwD,aAAaF,EAAK,aAMjDY,QAAS,IAAM5N,QAAQC,YAKrB4N,gBACJP,IAEA,MAAMN,EAAMM,EAAUC,YAAY,OAAO1D,eAEnCnB,EAAgBwE,aAAaF,EAzBV,iBAyBmCnD,eAM5D,MAJ8D,CAC5DoC,MAAO,IAAMA,MAAMvD,GACnB0D,SAAWlB,GAA2BkB,SAAS1D,EAAewC,MAKlD,SAAA4C,wBACdC,EACE,IAAIrR,UApCmB,gBAoCW2Q,cAAoC,WAExEU,EACE,IAAIrR,UAtC4B,yBAwC9BmR,gBAED,YCzCLC,GACAE,EAAgB3S,WAEhB2S,EAAgB3S,UAAe", "preExistingComment": "firebase-installations.js.map"}